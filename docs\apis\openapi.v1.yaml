openapi: 3.0.3
info:
  title: JCS Endpoint Management System API
  description: JCS 端点资产与任务管理系统 (JCS Endpoint Management System) 的API接口规范。本文档详细定义了系统提供的所有HTTP API端点、请求参数、响应结构以及认证机制。
  version: v1.0.0
  contact:
    name: 项目开发团队
    # email: <EMAIL> # 可选，项目联系邮箱

servers:
  - url: /api # 假设所有API的根路径为 /api
    description: 开发环境服务器

components:
  schemas:
    # --- 通用基础 Schema 定义 ---
    ErrorResponse:
      type: object
      description: API请求发生错误时的标准响应结构。
      properties:
        error:
          type: string
          description: 关于错误的具体描述信息，通常面向开发者。
        # errorCode: # 如果项目中定义并使用了统一的错误代码，可以取消注释并定义
        #   type: string
        #   description: 唯一标识错误的内部代码。
        # message: # 如果错误时也可能包含面向用户的消息，可以添加
        #   type: string
        #   description: 面向用户的错误提示信息。
      required:
        - error

    StatusResponse:
      type: object
      description: 用于表示操作状态的通用响应结构，通常用于不返回特定数据的操作结果。
      properties:
        status:
          type: integer
          format: int32
          description: 反映操作结果的HTTP状态码。
        message:
          type: string
          nullable: true
          description: (可选) 与状态相关的补充消息。
        url:
          type: string
          format: url
          nullable: true
          description: (可选) 操作成功后建议跳转的URL，例如登录成功后的重定向地址。

    # --- 用户及会话相关 Schema 定义 ---
    SessionUser:
      type: object
      description: 存储在会话中的用户信息结构。
      properties:
        id:
          type: string
          description: 用户的唯一标识符 (通常对应Keycloak中的subject ID)。
        userId:
          type: string
          description: 用户登录名 (例如，Keycloak中的preferred_username)。
        licenseId:
          type: string
          nullable: true
          description: 用户关联的许可证ID。如果用户没有特定许可证，可能为空。
        tz:
          type: string
          description: 用户选择的时区信息。
        refreshToken:
          type: string
          description: 用于刷新访问令牌的Keycloak刷新令牌 (此信息通常仅在服务器端会话中处理，不直接暴露给前端)。
      required:
        - id
        - userId
        - tz
        # refreshToken 仅在服务器内部会话中必需

    IronSessionData: # /api/ironSession 端点的响应结构
      type: object
      description: 通过 /api/ironSession 端点获取的当前会话数据。
      properties:
        user:
          $ref: '#/components/schemas/SessionUser'
        isLoggedIn: # 假设会话中会包含此状态
          type: boolean
          description: 指示用户当前是否已登录。
        # ... 此处可以根据 SessionData 的实际结构补充其他会话字段
      required:
        - isLoggedIn

    # --- Keycloak回调请求 Schema ---
    CallbackRequest:
      type: object
      description: Keycloak认证成功后回调到后端的请求体结构。
      properties:
        code:
          type: string
          description: Keycloak颁发的授权码 (Authorization Code)。
        tz:
          type: string
          description: 用户在客户端选择或检测到的时区信息。
      required:
        - code
        - tz

    # --- 审计日志 Schema ---
    AuditLoginLogRequest:
      type: object
      description: 创建登录/登出审计日志的请求体结构。
      properties:
        auditType:
          type: string
          enum: [LOGIN, LOGIN_FAILURE, LOGOUT]
          description: 审计事件的类型。
        loginMessage:
          type: string
          nullable: true
          description: (可选) 与审计事件相关的补充消息，例如登录失败的原因。
      required:
        - auditType

    AuditLoginLogResponse:
      type: object
      description: 创建审计日志成功后的响应。
      properties:
        message:
          type: string
          description: 操作成功的确认消息。
          example: "監査ログが正常に記録されました。"

    # --- 许可证信息 Schema ---
    LovItem: # 通用的LOV (List of Values) 项结构
      type: object
      description: 值列表(LOV)中的一个条目，通常用于提供代码的友好名称或值。
      properties:
        code:
          type: string
          description: LOV条目的代码。
        name:
          type: string
          description: LOV条目的显示名称/标签。
        value:
          type: string
          description: LOV条目的实际值。
        # ... 根据实际LOV表结构可补充 parentCode, isEnabled 等字段
      required:
        - code
        - name
        - value

    LicenseInfo:
      type: object
      description: 用户当前关联的许可证详细信息。
      properties:
        licenseId:
          type: string
          description: 许可证的唯一ID。
        type:
          type: string
          description: 许可证的类型代码 (例如 "BASIC", "PREMIUM")。
        # 以下字段根据实际 `prisma.license` 模型补充
        # name:
        #   type: string
        #   description: 许可证名称
        # startDate:
        #   type: string
        #   format: date-time
        #   description: 许可证生效日期
        # endDate:
        #   type: string
        #   format: date-time
        #   description: 许可证失效日期
        typeLov:
          $ref: '#/components/schemas/LovItem' # 使用通用LOV项结构
          description: 许可证类型的本地化描述信息 (来自LOV缓存)。
      required:
        - licenseId
        - type
        - typeLov

    # --- 通知信息 Schema ---
    Notification:
      type: object
      description: 系统或用户相关的通知消息结构。
      properties:
        id:
          type: string
          format: uuid # 假设是UUID
          description: 通知的唯一ID。
        type:
          type: string
          enum: [SYSTEM, USER, LICENSE, PLAN] # 根据 NotificationType 定义
          description: 通知的类型。
        title:
          type: string
          nullable: true # 假设标题可以为空
          description: 通知的标题。
        content:
          type: string
          description: 通知的主要内容。
        publishedAt:
          type: string
          format: date-time
          description: 通知的发布时间。
        userId:
          type: string
          nullable: true
          description: 如果是用户特定通知 (type=USER)，则为目标用户的ID。
        licenseId:
          type: string
          nullable: true
          description: 如果是许可证特定通知 (type=LICENSE)，则为目标许可证的ID。
        # planId: # 如果 PLAN 类型的通知直接关联 planId
        #   type: string
        #   nullable: true
        #   description: 如果是计划特定通知 (type=PLAN)，则为目标计划的ID。
      required:
        - id
        - type
        - content
        - publishedAt

    # --- 密码修改 Schema ---
    ChangePasswordRequest:
      type: object
      description: 用户修改密码的请求体结构。
      properties:
        password:
          type: string
          format: password # OpenAPI中format: password通常用于提示UI隐藏输入
          description:用户的当前密码。
        newPassword:
          type: string
          format: password
          description: 用户希望设置的新密码。
        oneTimePassword:
          type: string
          nullable: true # 根据代码逻辑，OTP似乎并非总是必需的，确认这一点
          description: (可选) 如果用户启用了两步验证 (2FA/MFA)，则为一次性密码。
      required:
        - password
        - newPassword
        # - oneTimePassword # 仅当OTP是强制的时才加入required

  securitySchemes:
    cookieAuth:
      type: apiKey
      in: cookie
      # **重要**: 请将 'your-session-cookie-name' 替换为您的 iron-session 实际使用的 cookie 名称。
      # 这个名称在您的 `sessionOptions` 中定义（例如，`app/lib/session.ts`）。
      # 常见的 iron-session cookie 名称可能是自定义的（如 'app-session'）或库的默认（需查证）。
      name: your-session-cookie-name
      description: >
        基于Cookie的会话认证。用户登录成功后，服务器会在响应中设置一个包含会话信息的加密Cookie。
        后续需要认证的API请求，客户端浏览器会自动携带此Cookie。

  responses:
    # --- 标准错误响应定义 ---
    UnauthorizedError:
      description: 未授权。请求的操作需要有效的用户身份认证，但认证信息缺失或无效。
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "用户未登录或会话已失效。"
    ForbiddenError:
      description: 禁止访问。已通过身份认证的用户没有足够的权限来执行所请求的操作。
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "用户权限不足。"
    BadRequestError:
      description: 错误的请求。服务器无法理解请求，通常因为请求参数无效、格式错误或缺失。
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "请求参数 'type' 无效。"
    NotFoundError:
      description: 未找到资源。请求的目标资源在服务器上不存在。
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "ID为 'xyz' 的通知未找到。"
    InternalServerError:
      description: 服务器内部错误。服务器在处理请求时遇到了意外的、无法处理的错误。
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "服务器内部发生未知错误，请联系管理员。"

paths:
  /audit-login-logs:
    post:
      summary: 创建登录/登出相关的审计日志
      description: 记录用户的登录成功、登录失败或登出事件到审计日志中。需要有效的用户会话来识别操作用户。
      tags:
        - Audit Logs (审计日志)
      requestBody:
        required: true
        description: 包含审计事件类型和可选消息的请求体。
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuditLoginLogRequest'
            examples:
              login_success:
                summary: 登录成功日志
                value:
                  auditType: "LOGIN"
              login_failure:
                summary: 登录失败日志
                value:
                  auditType: "LOGIN_FAILURE"
                  loginMessage: "密码错误次数过多"
              logout:
                summary: 登出日志
                value:
                  auditType: "LOGOUT"
      responses:
        '200':
          description: 审计日志记录成功。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuditLoginLogResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
          description: 请求参数错误，例如 `auditType` 无效。
        '401':
          $ref: '#/components/responses/UnauthorizedError'
          description: 需要用户会话才能记录与用户相关的审计日志。
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - cookieAuth: [] # 假设需要会话来获取 userId

  /callback:
    post:
      summary: 处理Keycloak认证回调
      description: >
        此端点用于接收来自Keycloak认证服务器的授权码回调。
        后端将使用此授权码向Keycloak请求访问令牌和刷新令牌，
        验证用户许可证，并在成功后创建用户会话。
      tags:
        - Authentication (认证授权)
      requestBody:
        required: true
        description: 包含从Keycloak获取的授权码和用户时区信息。
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CallbackRequest'
      responses:
        '200':
          description: Keycloak回调处理成功，用户会话已建立。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StatusResponse' # 通常返回 { status: 200 }
              example:
                status: 200
        '400':
          description: 回调请求无效，或与Keycloak交换令牌失败，或必要的环境变量缺失。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StatusResponse' # 通常返回 { status: 400 }
              example:
                status: 400
        '401':
          description: 用户身份已验证，但许可证无效或校验失败，禁止访问。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StatusResponse' # 通常返回 { status: 401 }
              example:
                status: 401
        '500':
          $ref: '#/components/responses/InternalServerError'

  /ironSession:
    post:
      # 代码中是POST方法。通常获取会话信息使用GET更符合RESTful语义。
      # 如果此POST请求确实有副作用（例如刷新会话的某些状态），则POST是合理的。
      # 否则，建议考虑是否应为GET。当前按代码实现为POST。
      summary: 获取当前用户的会话信息
      description: >
        检索当前已认证用户的会话数据。注意：此端点在提供的代码中实现为POST方法。
        如果其仅用于读取会话数据，通常应为GET方法。请确认设计意图。
      tags:
        - Session (会话管理)
      responses:
        '200':
          description: 成功获取当前用户的会话数据。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IronSessionData'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
          description: 用户未登录或会话无效。
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - cookieAuth: [] # 此操作显然需要有效的会话

  /licenses/current:
    get:
      summary: 获取当前用户的许可证信息
      description: 查询并返回当前已登录用户所关联的有效许可证的详细信息，包括许可证类型及其本地化描述。
      tags:
        - Licenses (许可证管理)
      responses:
        '200':
          description: 成功获取用户的许可证信息。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LicenseInfo'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
          description: 未找到与当前用户关联的许可证信息。
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - cookieAuth: []

  /login:
    post:
      summary: 获取Keycloak登录URL
      description: >
        此端点不执行实际登录，而是返回一个Keycloak认证服务器的登录URL。
        客户端（前端）应将用户重定向到此URL以开始OIDC登录流程。
      tags:
        - Authentication (认证授权)
      responses:
        '200':
          description: 成功生成Keycloak登录URL。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StatusResponse' # url 字段将包含登录URL
              example:
                status: 200
                url: "https://keycloak.example.com/realms/your-realm/protocol/openid-connect/auth?response_type=code&client_id=your-client&redirect_uri=your-callback"
        '400':
          description: 无法生成登录URL，通常因为必要的环境变量配置错误或Keycloak服务暂时不可达。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StatusResponse' # 仅返回 { status: 400 }
              example:
                status: 400
        '500':
          $ref: '#/components/responses/InternalServerError'

  /logout:
    post:
      summary: 用户登出
      description: >
        执行用户登出操作。后端会首先尝试通过Keycloak API使当前用户的访问令牌和刷新令牌失效（如果配置了相关功能），
        然后销毁本地服务器会话 (iron-session)。
      tags:
        - Authentication (认证授权)
      responses:
        '200':
          description: 用户登出成功，本地会话已销毁。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StatusResponse' # { status: 200 }
              example:
                status: 200
        '400':
          description: 登出过程中发生错误，例如与Keycloak通信失败或必要的环境变量缺失。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StatusResponse' # { status: 400 }
              example:
                status: 400
        '401':
          $ref: '#/components/responses/UnauthorizedError'
          description: 执行登出操作需要有效的用户会话（例如，获取刷新令牌）。
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - cookieAuth: [] # 需要会话来获取 refresh_token 和 user.id

  /notifications:
    get:
      summary: 获取当前用户的通知列表
      description: >
        查询并返回与当前登录用户相关的各类通知，包括系统级通知、用户特定通知、
        与用户许可证相关的通知以及与用户订阅计划相关的通知。通知按发布时间降序排列。
      tags:
        - Notifications (通知管理)
      responses:
        '200':
          description: 成功获取通知列表。
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Notification'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - cookieAuth: []

  /notifications/system:
    get:
      summary: 获取所有系统级通知
      description: >
        查询并返回所有类型为“SYSTEM”的通知。这些通知通常面向所有用户。
        此端点在Next.js中配置为动态渲染 (`revalidate = 0`)，确保每次请求都获取最新数据。
      tags:
        - Notifications (通知管理)
      responses:
        '200':
          description: 成功获取系统级通知列表。
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Notification'
        '500':
          $ref: '#/components/responses/InternalServerError'
      # 此端点代码未显式检查会话，假设对系统公告的获取是公开的或由页面级权限控制。
      # 如果也需要认证，请添加 security 节。

  /passwords/{id}:
    put:
      summary: 修改当前用户的密码
      description: >
        允许当前已登录用户修改其密码。用户需要提供当前密码、新密码，以及一次性密码（如果启用了2FA/MFA）。
        后端将首先验证当前凭据，然后通过Keycloak Admin API更新密码。
        注意：路径参数 `id` 在提供的代码逻辑中似乎并未使用，密码修改是针对当前会话用户。
        如果此 `id` 用于管理员修改其他用户密码，则API定义和安全需求将完全不同。
      tags:
        - Users (用户管理)
        - Authentication (认证授权)
      parameters:
        - name: id
          in: path
          required: true
          description: >
            用户ID。注意：根据当前代码实现，此路径参数可能未被实际用于定位修改密码的用户
            （操作是针对当前会话用户）。如果此参数有实际用途（例如管理员操作），请更新描述。
          schema:
            type: string
            format: uuid # 假设用户ID是UUID格式
          example: "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
      requestBody:
        required: true
        description: 包含当前密码、新密码和可选的一次性密码。
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
      responses:
        '200': # 成功时代码返回 { message: PORTAL_ERROR_MESSAGES.EMEC0008 }
          description: 密码修改成功。
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    description: 操作成功的提示消息。
                example:
                  message: "ユーザーのパスワードが変更されました。" # EMEC0008 的日文含义
        '400':
          description: >
            请求参数不符合要求（如新密码长度、复杂度不满足策略，新旧密码相同），
            或Keycloak更新密码操作失败（非认证失败）。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                password_policy_length:
                  value: { error: "新しいパスワードは8文字以上128文字以内で入力してください。" } # EMEC0009
                password_policy_complexity:
                  value: { error: "新しいパスワードには、英字（大文字または小文字）、数字、記号のうち少なくとも2種類を使用してください。" } # EMEC0010
                password_same_as_userid:
                  value: { error: "新しいパスワードにユーザーIDと同じ文字列は使用できません。" } # EMEC0011
                password_new_equals_old:
                  value: { error: "新しいパスワードが現在のパスワードと同じです。" } # EMEC0012
                keycloak_update_failed:
                  value: { error: "パスワードの変更に失敗しました。しばらくしてから再度お試しください。" } # EMEC0013
        '401':
          $ref: '#/components/responses/UnauthorizedError'
          description: 当前用户会话无效。
        '403':
          description: 用户身份验证失败（当前密码或一次性密码不正确）。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "現在のパスワードまたはワンタイムコードが正しくありません。" # EMEC0015
        '404': # 代码中针对环境变量缺失返回404并附带EMEC0007
          description: 服务配置错误，例如Keycloak相关的环境变量缺失。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "サービスの実行に必要な設定が不足しています。" # EMEC0007
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - cookieAuth: []

  /refreshToken:
    post:
      summary: 刷新Keycloak访问令牌
      description: >
        使用存储在当前用户会话中的刷新令牌，从Keycloak获取新的访问令牌和刷新令牌，
        并更新本地服务器会话中的这些令牌及会话有效期。
      tags:
        - Authentication (认证授权)
        - Session (会话管理)
      responses:
        '200':
          description: 成功刷新访问令牌并更新会话。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StatusResponse' # { status: 200 }
              example:
                status: 200
        '400':
          description: 无法刷新令牌，例如刷新令牌无效/过期、与Keycloak通信失败或必要的环境变量缺失。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StatusResponse' # { status: 400 }
              example:
                status: 400
        '401':
          $ref: '#/components/responses/UnauthorizedError'
          description: 当前用户会话无效或会话中缺少有效的刷新令牌。
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - cookieAuth: []