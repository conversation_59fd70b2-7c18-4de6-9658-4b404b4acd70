/**
 * @fileoverview タスク一覧の各行に表示される「タスク詳細」列の動的コンテンツを生成するクライアントコンポーネント
 * @description
 * タスク一覧画面の各タスク行における「タスク詳細」列の表示内容を、タスクの現在のステータスと種別に基づいて動的に決定する。
 * 以下の表示パターンを実装する：
 * - 実行待ち（QUEUED）：中止ボタンを表示
 * - 実行中（RUNBOOK_SUBMITTED/RUNBOOK_PROCESSING）・中止待ち（PENDING_CANCELLATION）：空欄表示
 * - 中止（CANCELLED）：中止完了メッセージを表示（task.resultMessageから取得）
 * - 正常終了（COMPLETED_SUCCESS）：タスク種別に応じてダウンロードリンクまたはメッセージを表示
 * - エラー（COMPLETED_ERROR）：エラー詳細表示ボタンを表示
 *
 * タスク中止要求処理では、ユーザー確認後にrequestTaskCancellationサーバアクションを呼び出し、
 * 楽観ロック制御による安全な状態更新とService Bus経由でのバックエンド通知を実行する。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

"use client";

import Link from "next/link";
import { useState, useTransition } from "react";
import { useRouter } from "next/navigation";

import ConfirmModal from "@/app/ui/ConfirmModal";
import ErrorCommonModal from "@/app/ui/ErrorCommonModal";
import { TASK_STATUS, TASK_TYPE, TaskForList } from "@/app/lib/definitions";

import { cancelTask, refreshTaskList } from "@/app/lib/actions/tasks";

/**
 * タスクごとのアクションアイテムをレンダリングするコンポーネント
 *
 * 表示対象の単一タスクオブジェクトに基づいて、タスクのステータスに応じたアクションUIを生成する
 * @param props - コンポーネントのプロパティ
 * @param props.task - 表示対象のタスクオブジェクト（id、status、taskType、taskName、resultMessageを含む）
 * @returns タスクのステータスに応じた動的なアクションUI要素
 */
export default function TaskActions({ task }: { task: TaskForList }) {
  // Hooksの初期化
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  // 状態管理
  const [modalState, setModalState] = useState({
    showConfirm: false,
    showError: false,
    showInfo: false,
    message: "",
    title: "",
  });

  // イベント処理関数
  const handleCancelClick = () => {
    setModalState((prev) => ({ ...prev, showConfirm: true }));
  };

  const handleConfirm = () => {
    setModalState((prev) => ({ ...prev, showConfirm: false }));

    startTransition(() => {
      cancelTask(task.id).then((result: any) => {
        if (result.success) {
          setModalState((prev) => ({
            ...prev,
            showInfo: true,
            message: result.message,
            title: "情報",
          }));
        } else {
          setModalState((prev) => ({
            ...prev,
            showError: true,
            message: result.message,
            title: "エラー",
          }));
        }
      });
    });
  };

  const handleCancel = () => {
    setModalState((prev) => ({ ...prev, showConfirm: false }));
  };

  const handleErrorClick = () => {
    setModalState((prev) => ({
      ...prev,
      showError: true,
      title: "エラー詳細",
    }));
  };

  const closeModal = (type: "info" | "error") => {
    setModalState({
      showConfirm: false,
      showError: false,
      showInfo: false,
      message: "",
      title: "",
    });

    if (type === "info") {
      startTransition(() => {
        refreshTaskList().then(() => {
          router.refresh();
        });
      });
    }
  };

  // JSX内の複雑な条件式を事前に計算
  const isModalOpen = modalState.showInfo || modalState.showError;
  const modalMessage = modalState.showInfo ? modalState.message : undefined;
  const modalError = modalState.showError
    ? modalState.message || task.resultMessage || "不明なエラーが発生しました。"
    : undefined;
  const modalCloseType = modalState.showInfo ? "info" : "error";

  // タスクステータスに応じた動的UI、確認モーダル、エラーモーダルをレンダリング
  // isPending状態では半透明表示とポインターイベント無効化を適用
  return (
    <div className={isPending ? "opacity-50 pointer-events-none" : ""}>
      {(() => {
        switch (task.status) {
          case TASK_STATUS.QUEUED:
            return (
              <button
                type="button"
                onClick={handleCancelClick}
                disabled={isPending}
                className="mr-2 rounded bg-gradient-dark drop-shadow-dark shadow-dark px-3 py-2 text-center text-xs font-medium text-white hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
              >
                中止する
              </button>
            );

          case TASK_STATUS.RUNBOOK_SUBMITTED:
          case TASK_STATUS.RUNBOOK_PROCESSING:
          case TASK_STATUS.PENDING_CANCELLATION:
            return null;

          case TASK_STATUS.CANCELLED:
            return <span className="text-gray-600">{task.resultMessage}</span>;

          case TASK_STATUS.COMPLETED_SUCCESS:
            switch (task.taskType) {
              case TASK_TYPE.OPLOG_EXPORT:
                return (
                  <span className="text-gray-600">{task.resultMessage}</span>
                );
              case TASK_TYPE.MGMT_ITEM_IMPORT:
                return null;
              case TASK_TYPE.MGMT_ITEM_EXPORT:
                return (
                  <Link
                    href={`/dashboard/tasks/${task.id}/download`}
                    className="font-medium text-blue-600 hover:underline"
                    prefetch={false}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    ダウンロード
                  </Link>
                );
              default:
                return null;
            }

          case TASK_STATUS.COMPLETED_ERROR:
            return (
              <button
                type="button"
                onClick={handleErrorClick}
                disabled={isPending}
                className="font-medium text-blue-600 hover:underline"
              >
                エラー詳細を表示
              </button>
            );

          default:
            return null;
        }
      })()}

      <ConfirmModal
        isOpen={modalState.showConfirm}
        title="確認"
        message={`タスクを中止してもよろしいですか？\nタスク名：${task.taskName}`}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        confirmText="OK"
        cancelText="キャンセル"
      />

      <ErrorCommonModal
        isOpen={isModalOpen}
        title={modalState.title}
        message={modalMessage}
        error={modalError}
        onClose={() => closeModal(modalCloseType)}
      />
    </div>
  );
}
