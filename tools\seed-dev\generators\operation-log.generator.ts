/**
 * @fileoverview 操作ログデータ生成器
 * @description 開発環境用の大量の操作ログデータを生成する
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { PrismaClient } from '@prisma/client';
import { BaseGenerator } from './base.generator';

/**
 * 操作ログデータ生成器クラス
 * 様々なライセンスとタスクに関連する操作ログを大量に生成する
 */
export class OperationLogGenerator extends BaseGenerator {
  private readonly LOG_COUNT = 1000; // 生成するログ数
  private readonly BATCH_SIZE = 50;

  constructor(prisma: PrismaClient) {
    super(prisma, 'OperationLogGenerator');
  }

  /**
   * 生成器名を取得する
   * @returns 生成器名
   */
  getGeneratorName(): string {
    return '操作ログデータ';
  }

  /**
   * 生成予定のデータ数を取得する
   * @returns 生成予定数
   */
  getExpectedCount(): number {
    return this.LOG_COUNT;
  }

  /**
   * 既存の操作ログデータをクリーンアップする
   */
  async cleanup(): Promise<void> {
    try {
      const deleteResult = await this.prisma.operationLog.deleteMany({});
      console.log(`既存操作ログデータを削除しました (${deleteResult.count}件)`);
    } catch (error) {
      console.error('操作ログデータのクリーンアップに失敗しました', error);
      throw error;
    }
  }

  /**
   * 操作ログデータを生成する
   * 様々なライセンスとタスクに関連する操作ログを大量に生成する
   * @returns 生成されたデータの数
   */
  async generate(): Promise<number> {
    try {
      // 必要なマスターデータを取得
      const [licenses, tasks] = await Promise.all([
        this.getAvailableLicenses(),
        this.getAvailableTasks(),
      ]);

      if (licenses.length === 0) {
        throw new Error('利用可能なライセンスが見つかりません。先にライセンスデータを生成してください。');
      }

      console.log(
        `${licenses.length}個のライセンス、${tasks.length}個のタスクを使用します`
      );

      // バッチで操作ログデータを生成
      return await this.generateInBatches(
        this.LOG_COUNT,
        this.BATCH_SIZE,
        async (startIndex: number, count: number) => {
          return await this.generateLogBatch(startIndex, count, licenses, tasks);
        }
      );
    } catch (error) {
      console.error('操作ログデータ生成中にエラーが発生しました', error);
      throw error;
    }
  }

  /**
   * 操作ログデータのバッチを生成する
   * @param startIndex 開始インデックス
   * @param count 生成数
   * @param licenses 利用可能なライセンス
   * @param tasks 利用可能なタスク
   * @returns 生成された操作ログデータ
   */
  private async generateLogBatch(
    startIndex: number,
    count: number,
    licenses: any[],
    tasks: any[]
  ): Promise<any[]> {
    const logs = [];

    for (let i = 0; i < count; i++) {
      const logIndex = startIndex + i + 1;
      const license = licenses[Math.floor(Math.random() * licenses.length)];
      const task = tasks.length > 0 ? tasks[Math.floor(Math.random() * tasks.length)] : null;
      
      const createdAt = this.faker.pastDate(90); // 過去90日以内
      const log = {
        name: this.generateLogName(logIndex),
        size: this.generateLogSize(),
        createdAt,
        retentionAt: this.generateRetentionDate(createdAt),
        licenseId: license.licenseId,
        generatedByTaskId: task ? task.id : null,
        fileName: this.generateFileName(logIndex, createdAt),
      };

      logs.push(log);
    }

    // バッチでデータベースに挿入
    await this.prisma.operationLog.createMany({
      data: logs,
    });

    return logs;
  }

  /**
   * ログ名を生成する
   * @param index ログインデックス
   * @returns ログ名
   */
  private generateLogName(index: number): string {
    const logTypes = [
      '操作ログエクスポート',
      'システム監査ログ',
      'ユーザーアクセスログ',
      'セキュリティイベントログ',
      'システム変更ログ',
      'エラーログ',
      'パフォーマンスログ',
      'バックアップログ',
      'メンテナンスログ',
      'アップデートログ',
      'ネットワークアクセスログ',
      'データベースアクセスログ',
      'ファイルアクセスログ',
      'プロセス実行ログ',
      'サービス起動ログ'
    ];

    const logType = this.faker.randomFromArray(logTypes);
    const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    
    return `${logType}_${timestamp}_${index.toString().padStart(4, '0')}`;
  }

  /**
   * ログサイズを生成する（バイト単位）
   * @returns ログサイズ
   */
  private generateLogSize(): number {
    // 1KB から 100MB の範囲でランダム生成
    const minSize = 1024; // 1KB
    const maxSize = 100 * 1024 * 1024; // 100MB
    
    // 重み付き分布（小さいファイルが多い）
    const random = Math.random();
    if (random < 0.6) {
      // 60%: 1KB - 1MB
      return this.faker.randomInt(minSize, 1024 * 1024);
    } else if (random < 0.9) {
      // 30%: 1MB - 10MB
      return this.faker.randomInt(1024 * 1024, 10 * 1024 * 1024);
    } else {
      // 10%: 10MB - 100MB
      return this.faker.randomInt(10 * 1024 * 1024, maxSize);
    }
  }

  /**
   * 保持期限を生成する
   * @param createdAt 作成日時
   * @returns 保持期限またはnull
   */
  private generateRetentionDate(createdAt: Date): Date | null {
    if (this.faker.randomBoolean(0.8)) { // 80%の確率で保持期限を設定
      // 作成日から30日〜365日後
      const retentionDays = this.faker.randomInt(30, 365);
      const retentionDate = new Date(createdAt);
      retentionDate.setDate(retentionDate.getDate() + retentionDays);
      return retentionDate;
    }
    return null;
  }

  /**
   * ファイル名を生成する
   * @param index ログインデックス
   * @param createdAt 作成日時
   * @returns ファイル名
   */
  private generateFileName(index: number, createdAt: Date): string {
    const dateStr = createdAt.toISOString().slice(0, 10).replace(/-/g, '');
    const timeStr = createdAt.toISOString().slice(11, 19).replace(/:/g, '');
    const extensions = ['log', 'txt', 'csv', 'json', 'xml'];
    const extension = this.faker.randomFromArray(extensions);
    
    const fileTypes = [
      'oplog',
      'audit',
      'access',
      'security',
      'system',
      'error',
      'performance',
      'backup',
      'maintenance',
      'update'
    ];
    
    const fileType = this.faker.randomFromArray(fileTypes);
    
    return `${fileType}_${dateStr}_${timeStr}_${index.toString().padStart(4, '0')}.${extension}`;
  }

  /**
   * 利用可能なライセンスを取得する
   * @returns ライセンスの配列
   */
  private async getAvailableLicenses(): Promise<any[]> {
    return await this.prisma.license.findMany({
      where: {
        isDisabled: false,
      },
      select: {
        licenseId: true,
        type: true,
      },
    });
  }

  /**
   * 利用可能なタスクを取得する
   * @returns タスクの配列
   */
  private async getAvailableTasks(): Promise<any[]> {
    return await this.prisma.task.findMany({
      where: {
        status: {
          in: ['TASK_STATUS.COMPLETED_SUCCESS', 'TASK_STATUS.COMPLETED_ERROR'],
        },
      },
      select: {
        id: true,
        taskName: true,
        taskType: true,
      },
      take: 100, // 最大100件のタスクを参照
    });
  }

  /**
   * 操作ログの統計情報を取得する
   * @returns 統計情報
   */
  async getStatistics(): Promise<{
    totalCount: number;
    totalSize: number;
    averageSize: number;
    byLicense: Record<string, number>;
    withRetention: number;
    withoutRetention: number;
    generatedByTask: number;
    byExtension: Record<string, number>;
  }> {
    const [
      totalCount,
      logs,
      withRetention,
      withoutRetention,
      generatedByTask,
    ] = await Promise.all([
      this.prisma.operationLog.count(),
      this.prisma.operationLog.findMany({
        select: {
          size: true,
          licenseId: true,
          fileName: true,
        },
      }),
      this.prisma.operationLog.count({ where: { retentionAt: { not: null } } }),
      this.prisma.operationLog.count({ where: { retentionAt: null } }),
      this.prisma.operationLog.count({ where: { generatedByTaskId: { not: null } } }),
    ]);

    // 各種集計
    const byLicense: Record<string, number> = {};
    const byExtension: Record<string, number> = {};
    let totalSize = 0;

    logs.forEach(log => {
      totalSize += log.size;
      byLicense[log.licenseId] = (byLicense[log.licenseId] || 0) + 1;
      
      const extension = log.fileName.split('.').pop()?.toLowerCase() || 'unknown';
      byExtension[extension] = (byExtension[extension] || 0) + 1;
    });

    const averageSize = totalCount > 0 ? Math.round(totalSize / totalCount) : 0;

    return {
      totalCount,
      totalSize,
      averageSize,
      byLicense,
      withRetention,
      withoutRetention,
      generatedByTask,
      byExtension,
    };
  }

  /**
   * 配列からランダムな要素を選択する
   * @param array 選択元の配列
   * @returns ランダムに選択された要素
   */
  private randomFromArray<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }
}
