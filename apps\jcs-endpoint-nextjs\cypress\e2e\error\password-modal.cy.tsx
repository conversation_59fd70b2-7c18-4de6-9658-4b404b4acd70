describe("エラーハンドリングの一般的なテスト", () => {
  describe("パスワード変更ダイアログ", () => {
    it("未入力のままフォームを送信", () => {
      cy.visit("/login");
      cy.get("#userId").type("hitachi.hanako.ab");
      cy.get("#password").type("changeit!@#");
      cy.get("button").contains("ログイン").click();
      cy.wait(3000);
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password-modal button").contains("OK").click();
      cy.get("input.border-red-500").should("have.length", 1);
      cy.contains("現在のパスワードが正しくありません。");
      cy.get("#password").type("changeit!@#");
      cy.get("#password-modal button").contains("OK").click();
      cy.get("input.border-red-500").should("have.length", 1);
      cy.contains(
        "新しいパスワードは8文字以上、128文字以下のパスワードを入力してください。",
      );
      cy.get("#newPassword").type("changeit!@#");
      cy.get("#password-modal button").contains("OK").click();
      cy.get("input.border-red-500").should("have.length", 1);
      cy.contains("確認用パスワードが正しくありません。");
    });

    it("文字数が不足しているパスワードでのパスワード変更", () => {
      cy.visit("/login");
      cy.get("#userId").type("hitachi.hanako.ab");
      cy.get("#password").type("changeit!@#");
      cy.get("button").contains("ログイン").click();
      cy.wait(3000);
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password").type("change");
      cy.get("#newPassword").type("pass");
      cy.get("#confirmPassword").type("pass");
      cy.get("#password-modal button").contains("OK").click();
      cy.get("input.border-red-500").should("have.length", 1);
      cy.contains("現在のパスワードが正しくありません。");
    });

    it("文字数が不足している新しいパスワードでのパスワード変更", () => {
      cy.visit("/login");
      cy.get("#userId").type("hitachi.hanako.ab");
      cy.get("#password").type("changeit!@#");
      cy.get("button").contains("ログイン").click();
      cy.wait(3000);
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password").type("changeit!@#");
      cy.get("#newPassword").type("pass");
      cy.get("#confirmPassword").type("pass");
      cy.get("#password-modal button").contains("OK").click();
      cy.get("input.border-red-500").should("have.length", 1);
      cy.contains(
        "新しいパスワードは8文字以上、128文字以下のパスワードを入力してください。",
      );
    });

    it("文字数が超過している新しいパスワードでのパスワード変更", () => {
      cy.visit("/login");
      cy.get("#userId").type("hitachi.hanako.ab");
      cy.get("#password").type("changeit!@#");
      cy.get("button").contains("ログイン").click();
      cy.wait(3000);
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("パスワード変更").click();
      cy.get("#newPassword")
        .type(
          "123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789",
        )
        .should(
          "have.value",
          "12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678",
        );
    });

    it("英文字だけの新しいパスワードでのパスワード変更", () => {
      cy.visit("/login");
      cy.get("#userId").type("hitachi.hanako.ab");
      cy.get("#password").type("changeit!@#");
      cy.get("button").contains("ログイン").click();
      cy.wait(3000);
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password").type("changeit!@#");
      cy.get("#newPassword").type("password");
      cy.get("#confirmPassword").type("password");
      cy.get("#password-modal button").contains("OK").click();
      cy.get("input.border-red-500").should("have.length", 1);
      cy.contains(
        "新しいパスワードには2種類以上の文字の組み合わせを入力してください。",
      );
    });
    it("数字だけの新しいパスワードでのパスワード変更", () => {
      cy.visit("/login");
      cy.get("#userId").type("hitachi.hanako.ab");
      cy.get("#password").type("changeit!@#");
      cy.get("button").contains("ログイン").click();
      cy.wait(3000);
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password").type("changeit!@#");
      cy.get("#newPassword").type("12345678");
      cy.get("#confirmPassword").type("12345678");
      cy.get("#password-modal button").contains("OK").click();
      cy.get("input.border-red-500").should("have.length", 1);
      cy.contains(
        "新しいパスワードには2種類以上の文字の組み合わせを入力してください。",
      );
    });
    it("記号だけの新しいパスワードでのパスワード変更", () => {
      cy.visit("/login");
      cy.get("#userId").type("hitachi.hanako.ab");
      cy.get("#password").type("changeit!@#");
      cy.get("button").contains("ログイン").click();
      cy.wait(3000);
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password").type("changeit!@#");
      cy.get("#newPassword").type("!@#$%^&*");
      cy.get("#confirmPassword").type("!@#$%^&*");
      cy.get("#password-modal button").contains("OK").click();
      cy.get("input.border-red-500").should("have.length", 1);
      cy.contains(
        "新しいパスワードには2種類以上の文字の組み合わせを入力してください。",
      );
    });
    it("ユーザーIDと同じ新しいパスワードでのパスワード変更", () => {
      cy.visit("/login");
      cy.get("#userId").type("hitachi.hanako.ab");
      cy.get("#password").type("changeit!@#");
      cy.get("button").contains("ログイン").click();
      cy.wait(3000);
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password").type("changeit!@#");
      cy.get("#newPassword").type("hitachi.hanako.ab");
      cy.get("#confirmPassword").type("hitachi.hanako.ab");
      cy.get("#password-modal button").contains("OK").click();
      cy.get("input.border-red-500").should("have.length", 1);
      cy.contains(
        "新しいパスワードにはユーザーIDと異なる文字列を入力してください。",
      );
    });
    it("現在のパスワードと同じ新しいパスワードでのパスワード変更", () => {
      cy.visit("/login");
      cy.get("#userId").type("hitachi.hanako.ab");
      cy.get("#password").type("changeit!@#");
      cy.get("button").contains("ログイン").click();
      cy.wait(3000);
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password").type("changeit!@#");
      cy.get("#newPassword").type("changeit!@#");
      cy.get("#confirmPassword").type("changeit!@#");
      cy.get("#password-modal button").contains("OK").click();
      cy.get("input.border-red-500").should("have.length", 1);
      cy.contains(
        "新しいパスワードには現在のパスワードと異なる文字列を入力してください。",
      );
    });
    it("新しパスワードと異なる確認用パスワードでのパスワード変更", () => {
      cy.visit("/login");
      cy.get("#userId").type("hitachi.hanako.ab");
      cy.get("#password").type("changeit!@#");
      cy.get("button").contains("ログイン").click();
      cy.wait(3000);
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password").type("changeit!@#");
      cy.get("#newPassword").type("password123");
      cy.get("#confirmPassword").type("password1234");
      cy.get("#password-modal button").contains("OK").click();
      cy.get("input.border-red-500").should("have.length", 1);
      cy.contains("確認用パスワードが正しくありません。");
    });
    it("誤るパスワードでのパスワード変更", () => {
      cy.visit("/login");
      cy.get("#userId").type("hitachi.hanako.ab");
      cy.get("#password").type("changeit!@#");
      cy.get("button").contains("ログイン").click();
      cy.wait(3000);
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password").type("invalidpassword");
      cy.get("#newPassword").type("password123");
      cy.get("#confirmPassword").type("password123");
      cy.get("#password-modal button").contains("OK").click();
      cy.get("input.border-red-500").should("have.length", 1);
      cy.contains("現在のパスワードが正しくありません。");
    });
  });
});
