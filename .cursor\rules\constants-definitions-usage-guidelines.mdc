---
description: 
globs: 
alwaysApply: true
---
# Constants and Definitions Usage Guidelines (for apps/jcs-endpoint-nextjs)

This document mandates how constants, type definitions, environment variable mappings, navigation links, error messages, and Lists of Values (LOVs) defined within the `apps/jcs-endpoint-nextjs` application and the Monorepo's `docs/` directory **MUST** be utilized by AI when generating or modifying code. Strict adherence to these guidelines is crucial for code consistency, maintainability, readability, and ease of future updates.

**Primary Sources of Truth (SSoT) for Definitions:**

*   **`app/lib/definitions.ts` (for `apps/jcs-endpoint-nextjs`):**
    *   **`ENV` object:** The **ONLY** way to access environment variables within the application code. AI **MUST NOT** use `process.env.VARIABLE_NAME` directly elsewhere.
    *   **`PORTAL_ERROR_MESSAGES` object:** The **ONLY** source for user-facing error message strings. AI **MUST** use the keys of this object (e.g., `PORTAL_ERROR_MESSAGES.EMEC0007`) when constructing error responses or displaying errors in the UI.
    *   **`navLinks` array:** Defines the structure and paths for sidebar navigation.
    *   **Type Definitions:** Contains core TypeScript interfaces and enums (e.g., `LicenseType`, `NotificationType`, `ServerType`, `LicenseAPI`, `ModalProps`).
    *   **LOV Code Constants:** Contains string constants representing LOV codes (e.g., `LICENSE_TYPE`, `SERVER_TYPE`, `OS_TYPE`, `SUPPORT_IMPORTANCE`, `LOV_CODE_AZURE_STORAGE_CONTAINER_OPLOGS`, `LOV_CODE_AZURE_STORAGE_SAS_TTL_SECONDS`). These **MUST** be used when referring to or querying LOV entries.
    *   **Cache Key Constants:** (e.g., `PORTAL_CACHE_KEY_LOV_LIST`, `PORTAL_CACHE_KEY_SERVERS`).
    *   **Regex Patterns:** Predefined regular expressions for validation (e.g., `atLeastTwoKindsRegex`, `LoginFormUserIdPattern`).
*   **Monorepo `docs/definitions/` Directory:**
    *   **`docs/definitions/error-messages.md`:** The detailed SSoT for all messages in `PORTAL_ERROR_MESSAGES`, including their intended meaning and context. AI should understand this is the master list.
    *   **`docs/definitions/lov-definitions.md`:** The detailed SSoT for all LOV entries, explaining the business meaning of each `code`, `name`, `value`, and `parentCode`. This is the master reference for understanding what values like `LOV_CODE_AZURE_STORAGE_SAS_TTL_SECONDS` actually configure.
    *   **`docs/definitions/glossary.md`:** Project-specific terminology.

## Core Mandates for AI

1.  **No Hardcoding of Strings/Numbers for Defined Constants:**
    *   AI **MUST NOT** hardcode strings for error messages, navigation paths, LOV codes, cache keys, environment variable names (outside `definitions.ts`), or any other value that is (or should be) defined as a constant in `app/lib/definitions.ts`.
    *   **Incorrect:** `return NextResponse.json({ error: "サーバーに一時的に接続できません。" }, { status: 500 });`
    *   **Correct:** `return NextResponse.json({ error: PORTAL_ERROR_MESSAGES.EMEC0007 }, { status: 500 });`
    *   **Incorrect:** `const containerName = "oplogs-container";`
    *   **Correct (Conceptual, actual value fetched via LOV):**
        ```typescript
        // Assuming lovValue is fetched using LOV_CODE_AZURE_STORAGE_CONTAINER_OPLOGS
        // const containerLov = await ServerData.fetchCachedLov(LOV_CODE_AZURE_STORAGE_CONTAINER_OPLOGS);
        // const containerName = containerLov.value;
        ```
2.  **Use `ENV` Object for Environment Variables:** All access to environment variables **MUST** go through the `ENV` object imported from `app/lib/definitions.ts`.
    *   **Incorrect:** `const apiKey = process.env.MY_API_KEY;`
    *   **Correct:** `const apiKey = ENV.MY_API_KEY;` (Assuming `MY_API_KEY` is defined in the `ENV` object structure in `definitions.ts`).
3.  **Use TypeScript Enums and Types from `definitions.ts`:** When dealing with predefined sets of values (like notification types, license types, server types), **MUST** use the Enums (e.g., `NotificationType.SYSTEM`) or string literal types defined in `app/lib/definitions.ts`.
    *   **Incorrect:** `if (notification.type === "SYSTEM") { ... }`
    *   **Correct:** `if (notification.type === NotificationType.SYSTEM) { ... }`
4.  **Reference LOV Codes via Constants:** When fetching or referring to specific LOV entries (e.g., for Azure Storage container names, SAS TTL values), **MUST** use the exported string constants from `app/lib/definitions.ts` (e.g., `LOV_CODE_AZURE_STORAGE_CONTAINER_PRODUCT_MANUALS`). The actual values are then fetched from the database using these codes via `ServerData.fetchCachedLov()`.
5.  **Refer to `docs/definitions/` for Understanding:** While `app/lib/definitions.ts` provides the code-level constants, AI should understand that the Monorepo's `docs/definitions/` directory (specifically `error-messages.md` and `lov-definitions.md`) contains the detailed explanations and business context for these definitions. When asked about the *meaning* or *purpose* of an error message key or an LOV code, AI should be guided to (or act as if it has consulted) these documents.

## Examples of Correct Usage

1.  **Accessing an Environment Variable:**
    ```typescript
    import { ENV } from "@/app/lib/definitions";

    const logLevel = ENV.LOG_LEVEL; // Correct
    const sasTTL = ENV.APP_CACHE_TTL_SECONDS; // Correct (example, might be from LOV instead)
    ```

2.  **Using an Error Message Key:**
    ```typescript
    import { PORTAL_ERROR_MESSAGES } from "@/app/lib/definitions";
    import { NextResponse } from "next/server";

    // In an API route or Server Action
    // return NextResponse.json({ error: PORTAL_ERROR_MESSAGES.EMEC0001 }, { status: 401 }); // Correct
    ```

3.  **Using a Notification Type Enum:**
    ```typescript
    import { NotificationType } from "@/app/lib/definitions";
    import prisma from "@/app/lib/prisma";

    // const systemNotifications = await prisma.notification.findMany({
    //   where: { type: NotificationType.SYSTEM } // Correct
    // });
    ```

4.  **Using an LOV Code Constant to Fetch an LOV Value:**
    ```typescript
    import ServerData from "@/app/lib/data";
    import { LOV_CODE_AZURE_STORAGE_CONTAINER_OPLOGS } from "@/app/lib/definitions";

    // async function getOplogsContainerName() {
    //   const containerLov = await ServerData.fetchCachedLov(LOV_CODE_AZURE_STORAGE_CONTAINER_OPLOGS); // Correct
    //   if (!containerLov) throw new Error("Oplogs container LOV not found");
    //   return containerLov.value;
    // }
    ```

---

**NOTE TO CURSOR:**
1.  Before using any string literal for error messages, configuration keys, type discriminators, navigation paths, etc., **FIRST CHECK** if a corresponding constant, enum, or type already exists in `app/lib/definitions.ts`. If it does, you **MUST** use the defined entity.
2.  When working with LOV codes, always use the constants from `app/lib/definitions.ts` (e.g., `LOV_CODE_...`) as arguments to functions like `ServerData.fetchCachedLov()`. Do not hardcode the LOV code strings directly.
3.  Understand that `app/lib/definitions.ts` is the code-level SSoT for these values, while the Monorepo's `docs/definitions/` directory provides the detailed business meaning and documentation for them.
4.  When asked to generate new constants or definitions, they should primarily be added to `app/lib/definitions.ts` and, if user-facing or requiring detailed explanation, also documented in the appropriate file within `docs/definitions/`.


---