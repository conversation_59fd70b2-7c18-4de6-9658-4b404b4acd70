/**
 * @file route.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2024, 2025, Hitachi Solutions, Ltd.
 */

import { ServerDataLov } from "@/app/lib/data/lov";
import { LICENSE_TYPE } from "@/app/lib/definitions";
import { handleApiError } from "@/app/lib/portal-error";
import prisma from "@/app/lib/prisma";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

// ライセンス情報のGETリクエストの処理
export async function GET(req: Request) {
  try {
    const session = await getIronSession<SessionData>(cookies(), sessionOptions);
    const license = await prisma.license.findUnique({
      where: {
        licenseId: session!.user.licenseId!,
      },
    });
    const lov = await ServerDataLov.fetchLov(
      `${LICENSE_TYPE}.${license?.type}`,
    );

    // 取得した通知をJSONレスポンスで返す
    return NextResponse.json({ ...license, typeLov: lov });
  } catch (error) {
    return handleApiError(error);
  }
}
