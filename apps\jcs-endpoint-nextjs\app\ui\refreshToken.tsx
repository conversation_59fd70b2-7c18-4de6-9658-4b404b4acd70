/**
 * @file refreshToken.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2024, 2025, Hitachi Solutions, Ltd.
 */

"use client";

import { useRouter } from "next/navigation"; // Next.jsのルーター
import { useEffect } from "react";

// セッションリフレッシュコンポーネント
export default function RefreshToken() {
  const router = useRouter(); // ルーターのインスタンス取得
  try {
    useEffect(() => {
      const fetchSession = async () => {
        const response = await fetch(`/api/refreshToken`, {
          method: "POST", // POSTリクエスト
          headers: {
            "Content-Type": "application/json", // JSON形式のヘッダー
          }
        });
        if (response.status === 401) {
          router.push("/login");
        } else {
          // レスポンスのデータを処理
          const res = await response.json();
          // セッションリフレッシュに失敗しました
          if (!response.ok || res.status !== 200) {
            // ログアウト処理
            await fetch("/api/logout", {
              method: "POST", // POSTリクエスト
              headers: {
                "Content-Type": "application/json", // JSON形式のヘッダー
              }
            });
            router.push("/login?error07");
          } 
        }
      };
      fetchSession();
    }, []);
  } catch (error) {
    fetch("/api/logout", {
      method: "POST", // POSTリクエスト
      headers: {
        "Content-Type": "application/json", // JSON形式のヘッダー
      }
    });
    router.push("/login?error07");
  }

  return null;
}
