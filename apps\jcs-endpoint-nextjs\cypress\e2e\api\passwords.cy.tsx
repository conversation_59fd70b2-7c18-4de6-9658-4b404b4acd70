describe("API エンドポイントの可用性のテスト", () => {
  describe("API - パスワード変更", () => {
    const validCredentials = {
      userId: "hitachi.taro.aa",
      password: "changeit!@#",
    };

    describe("セッションが存在しない場合", () => {
      it("セッションが存在しない場合、エラーが返される", () => {
        cy.request({
          method: "PUT",
          url: "/api/passwords/useridunique",
          failOnStatusCode: false,
        }).then((response) => {
          expect(response.status).to.eq(401);
        });
      });
    });

    describe("セッションが存在する場合", () => {
      // @ts-ignore
      let cookies;

      before(() => {
        Cypress.Cookies.debug(true);
        cy.visit("/login");
        cy.get("#userId").type(validCredentials.userId);
        cy.get("#password").type(validCredentials.password);
        cy.get("button").click();

        cy.wait(3000);
        cy.getCookies()
          .should("have.length.gt", 0)
          .then((cookiesArray) => {
            cookies = cookiesArray;
          });
      });

      it("お知らせ一覧が正常に取得できる", () => {
        cy.request({
          method: "PUT",
          url: "/api/passwords/useridunique",
          headers: {
            // @ts-ignore
            Cookie: `${cookies[0].name}=${cookies[0].value}`,
          },
          body: {
            password: "changeit!@#",
            newPassword: "password123",
          },
        }).then((response) => {
          expect(response.status).to.eq(200);
          expect(response.body.message).to.be.a("string");
        });
        cy.wait(3000);
        cy.request({
          method: "PUT",
          url: "/api/passwords/useridunique",
          headers: {
            // @ts-ignore
            Cookie: `${cookies[0].name}=${cookies[0].value}`,
          },
          body: {
            newPassword: "changeit!@#",
            password: "password123",
          },
        }).then((response) => {
          expect(response.status).to.eq(200);
          expect(response.body.message).to.be.a("string");
        });
      });
    });
  });
});
