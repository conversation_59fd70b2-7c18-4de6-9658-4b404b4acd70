describe("画面操作のテスト", () => {
  describe("ログイン画面", () => {
    const validCredentials = {
      userId: "hitachi.taro.aa",
      password: "changeit!@#",
    };

    it("ログインボタンをクリックすると、サーバ一覧画面へ遷移する", () => {
      cy.visit("/login");
      cy.get("#userId").type(validCredentials.userId);
      cy.get("#password").type(validCredentials.password);
      cy.get("button").contains("ログイン").click();
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("#license-modal").should("have.class", "hidden");
    });
  });
});
