## Runbookジョブ監視及び結果ポーリング関数 (RunbookMonitorFunc) 詳細設計

### 概要

#### 責務
RunbookMonitorFuncは、Azure Functionのタイマートリガーによって定期的に実行され、Taskテーブル内でRUNBOOK_SUBMITTEDステータスにあるタスクに対応するAzure Automation Runbookジョブの実行状況を監視する。主な責務は以下の通り。

1.  データベースのTaskテーブルから、statusがRUNBOOK_SUBMITTEDであるTaskレコードをすべて取得する。
2.  各タスクについて、タスクの実行時間（タスクの開始日時から現在の日時までの経過時間、両方UTC協定世界時で計算）が設定されたRunbookジョブのタイムアウト時間（環境変数 RUNBOOK_TIMEOUT_SECONDSで設定、デフォルトは5時間）を超えているか判定する。
3.  実行時間がタイムアウト時間を超えていないタスクについて、taskId=ジョブ名を使用してAzure Automation APIを呼び出して、ジョブの現在の実行状況（ジョブステータス）を問い合わせる。
4.  タスクがタイムアウト、或いはジョブステータスが特定の終了状態/異常状態（Completed, Failed, Removing, Resuming, Stopped, Stopping, Suspended, Suspending）である場合、当該タスクに対してRunbookジョブ処理関数で処理する必要があるので、次の5.-7.のステップを実行する。上記以外の場合は処理対象外のため次のタスクレコードの判定に入る。
5.  ステップ4.で処理対象と判定されたタスクのステータスをRUNBOOK_PROCESSINGに更新する。DB更新失敗の場合、ログを記録して、次のタスクレコードの判定に入る（当該タスクに対する処理は次回で起動されるRunbookジョブ監視関数に持ち越す）。
6.  処理対象タスクのID、ジョブステータス、例外情報でメッセージを構築して、Azure Service BusのRunbookStatusQueueへ送信する。
7.  送信成功の場合、次のタスクレコードの判定に入る。送信失敗の場合、処理対象タスクのステータスをRUNBOOK_SUBMITTEDに更新する（戻す）。ログを記録して、次のタスクレコードの判定に入る（当該タスクに対する処理は次回で起動されるRunbookジョブ監視関数に持ち越す）。
8.  RUNBOOK_SUBMITTEDのタスクレコードの判定が全部終わったら、処理成功のログを記録して、Runbookジョブ監視関数を終了する。

#### トリガー
Azure Functions タイマートリガー。

#### 主要入力
*   データベース (Taskテーブル): status = RUNBOOK_SUBMITTED のすべてのタスクレコード。
*   Azure Automation API: APIを呼び出して各taskId=ジョブ名に対応するジョブのステータスと例外情報を取得する。

#### 主要出力
*   Azure Service Bus (RunbookStatusQueue) へのメッセージ送信。メッセージにはtaskId, automationJobStatus (APIで取得した実際のジョブステータス、または本関数が判断した"Timeout"), exception（APIで取得した例外情報）が含まれる。
*   Azure SQL Database (Taskテーブル) のレコード更新：statusをRUNBOOK_PROCESSINGへ更新。

※タイマートリガーの時間間隔は環境変数 RUNBOOK_MONITOR_INTERVAL_SECONDS で設定される（デフォルトは30秒）。
※データベース接続には環境変数 MSSQL_PRISMA_URL で指定される接続文字列が使用される。
※Azure Automationアカウントの名称は環境変数 AZURE_AUTOMATION_ACCOUNT_NAME により指定される。
※サブスクリプションIDは環境変数 SUBSCRIPTION_ID により指定される。
※リソースグループ名は環境変数 RESOURCE_GROUP_NAME により指定される。
※RunbookStatusQueueの名称は、環境変数 SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME によって定義される。

### 主要処理フロー

```mermaid
graph TD
    A["開始: タイマートリガー起動"] --> B["DB (Taskテーブル) より<br/>RUNBOOK_SUBMITTED ステータスの<br/>Taskレコードを全て取得"];
    B -- "レコードなし/取得失敗" --> Z["ログ記録、処理終了"];
    B -- "レコードあり" --> C{"各Taskレコードをループ処理"};

    C --> D{"Task.startedAtから現在時刻<br/>までの時間を<br/>RUNBOOK_TIMEOUT_SECONDS<br/>と比較"};
        D -- "超過している" --> G_Timeout["Task.statusを<br/>RUNBOOK_PROCESSINGへ更新"];
            G_Timeout -- "DB更新失敗" --> GA_Timeout_Tx["エラーログ記録、<br/>ループ継続（当該タスクは次回のRunbookジョブ監視関数に持ち越し）"];
                GA_Timeout_Tx --> C;
            G_Timeout -- "DB更新成功" --> F_Timeout["RunbookStatusQueueへメッセージ送信<br/>(automationJobStatus: Timeout)"]; 
                F_Timeout -- "送信失敗" --> E_Timeout["Task.statusを<br/>RUNBOOK_SUBMITTEDに更新（戻す）"];
                E_Timeout --> GA_Timeout_Tx;
                F_Timeout -- "送信成功" --> C;

        D -- "超過していない" --> H["Azure Automation API呼出<br/>ジョブステータス・例外情報取得"];
            H -- "取得成功" --> I{"ジョブステータス判定"};
                I -- "準備中/実行中/一時状態<br/>(New, Activating, Running,<br/>Blocked, Disconnected)のいずれかの場合" --> C;
                
                I -- "特定の終了状態/異常状態<br/>(Completed, Failed, Removing, Resuming,<br/>Stopped, Stopping, Suspended, Suspending)<br/>のいずれかの場合" --> K_Regular["Task.statusを<br/>RUNBOOK_PROCESSINGへ更新"];
                    K_Regular -- "DB更新失敗" --> KA_Regular_Tx["エラーログ記録、<br/>ループ継続（当該タスクは次回のRunbookジョブ監視関数に持ち越し）"];
                        KA_Regular_Tx --> C;
                    K_Regular -- "DB更新成功" --> L_Regular["RunbookStatusQueueへメッセージ送信<br/>(automationJobStatus: 取得したステータス)"];
                        L_Regular -- "送信失敗" --> J["Task.statusを<br/>RUNBOOK_SUBMITTEDに更新（戻す）"];
                        J --> KA_Regular_Tx;
                        L_Regular -- "送信成功" --> C;
            
            H -- "取得失敗" --> HA["エラーログ記録、<br/>ループ継続（当該タスクは次回のRunbookジョブ監視関数に持ち越し）"];
            HA --> C;
    C -- "全タスクレコード処理完了" --> Z;
```

**■ 共通処理フロー詳細**

1.  Runbookジョブ監視関数がタイマートリガーにより起動される。処理開始のログを記録する。
2.  データベースの Task テーブルから、status が RUNBOOK_SUBMITTED である全てのタスクレコードを取得する。
    *   取得失敗または対象タスクが存在しない場合、ログに記録し処理を終了する。
3.  取得した各タスクレコードに対して、4.-9.の処理をループで行う。各タスクの処理は独立しており、あるタスクの処理失敗が他のタスクの処理を妨げない。
4.  タイムアウトチェック:
    *   タスクの開始日時startedAtから現在の日時までの時間を計算（両方UTC協定世界時で計算）し、環境変数 RUNBOOK_TIMEOUT_SECONDS で定義されたRunbookジョブのタイムアウト時間と比較して、超過しているか（タイムアウトしたか）判断する。
5.  (タイムアウトしていない場合)ジョブステータスポーリング :
    *   taskId=jobName を使用して、Azure Automationのジョブ取得APIを呼び出してジョブの現在のステータスと（例外が発生した場合）例外情報を問い合わせる。下記のリクエストを構築してAPIを呼び出す。
    *   API の呼び出しに失敗した場合（レスポンスのHTTPステータスコードが200（OK）でない場合）：エラー詳細をログに記録し、ステップ3.に戻って、次のタスクレコードの判定に入る。当該タスクに対する処理は次回のRunbookジョブ監視関数に持ち越す。
    
    リクエスト
    URI：GET https://management.azure.com/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Automation/automationAccounts/{automationAccountName}/jobs/{jobName}?api-version=2023-11-01
    ※{subscriptionId}には環境変数SUBSCRIPTION_IDで指定されたサブスクリプションID、{resourceGroupName}には環境変数RESOURCE_GROUP_NAMEで指定されたリソースグループ名、{automationAccountName}には環境変数AZURE_AUTOMATION_ACCOUNT_NAMEで指定されたAutomationのアカウント名、{jobName}にはtaskIdを設定する（タスク実行関数ではtaskId=jobNameでジョブを作成した）。
    ※Request Bodyは必要なし。

    サンプルレスポンス
    Status code: 200
    ```json
    {
      "id": "/subscriptions/********-3ed7-4a72-a187-0c8ab644ddab/resourceGroups/mygroup/providers/Microsoft.Automation/automationAccounts/ContoseAutomationAccount/jobs/jobName",
      "name": "foo",
      "type": "Microsoft.Automation/AutomationAccounts/Jobs",
      "properties": {
        "jobId": "5b8a3960-e8ab-45f6-bec6-567df8467d1a",
        "runbook": {
          "name": "TestRunbook"
        },
        "provisioningState": "Processing",
        "creationTime": "2018-02-01T05:53:30.243+00:00",
        "endTime": null,
        "exception": null,
        "lastModifiedTime": "2018-02-01T05:53:30.243+00:00",
        "lastStatusModifiedTime": "2018-02-01T05:53:30.243+00:00",
        "startTime": null,
        "status": "New",
        "statusDetails": "None",
        "parameters": {
          "tag01": "value01",
          "tag02": "value02"
        },
        "runOn": ""
      }
    }
    ```
6.  (タイムアウトしていない場合)5.で取得したジョブステータスproperties.statusを判定する :
    *   準備中/実行中/一時状態（New, Activating, Running, Blocked, Disconnectedのいずれか）の場合：当該タスクが処理対象外のため、ステップ3.に戻って、次のタスクレコードの判定に入る。
    *   特定の終了状態/異常状態（Completed, Failed, Removing, Resuming, Stopped, Stopping, Suspended, Suspendingのいずれか）の場合：ステップ7.の処理に進める。
7.  Taskテーブルから当該タスクのステータスをRUNBOOK_PROCESSINGに更新する。条件：IDがステップ2.で取得した各タスクレコードのtaskIdと一致し、最終更新日時がステップ2.で取得した最終更新日時と一致する。
    *   DB更新失敗、または更新した件数が0件の場合：エラー詳細をログに記録し、ステップ3.に戻って、次のタスクレコードの判定に入る。当該タスクに対する処理は次回のRunbookジョブ監視関数に持ち越す。
8.  Runbookジョブのステータスを通知するメッセージを構築し、Service Bus (RunbookStatusQueue) へ送信:
    メッセージにはtaskId（当該タスクのID）、automationJobStatus（タイムアウトした場合は"Timeout"/タイムアウトしていない場合はステップ5.で取得した実際のジョブステータスproperties.status）、exception（ステップ5.で取得した例外情報properties.exception）が含まれる。
    *   送信失敗の場合：
        ・処理対象タスクのステータスをRUNBOOK_SUBMITTEDに更新する（戻す）。条件：IDが処理対象タスクのタスクIDと一致し、ステータスがRUNBOOK_PROCESSING。
        ・エラー詳細をログに記録し、ステップ3.に戻って、次のタスクレコードの判定に入る。当該タスクに対する処理は次回のRunbookジョブ監視関数に持ち越す。
9.  ステップ3.に戻って、次のタスクレコードの判定に入る。
10. RUNBOOK_SUBMITTEDのタスクレコードの判定が全部終わったら、処理成功のログを記録して、Runbookジョブ監視関数を終了する。

### 主要なデータ及び外部サービスとの対話詳細

#### データベース (Taskテーブル) との対話
*   status = RUNBOOK_SUBMITTED の条件でタスクを読み取る。
*   対象タスクの status を RUNBOOK_PROCESSING へ更新する。

#### Azure Automation API との対話
*   Job - GetのREST API: taskId=jobName を使用してジョブの現在の実行状態（ジョブステータス）と例外情報を取得する。

#### Azure Service Bus (RunbookStatusQueue) との対話
*   タイムアウトしたタスク、またはジョブステータスが特定の終了状態/異常状態であるタスクについて、Runbookジョブのステータスを通知するメッセージを RunbookStatusQueue へ送信する。
*   メッセージボディの構造：
```json
			パラメータ名					データ型				必須		説明
			taskId					文字列				○		タスクID
			automationJobStatus					文字列				○		タイムアウトした場合は"Timeout"/タイムアウトしていない場合はAPIで取得した実際のジョブステータスproperties.status
			exception					文字列						Runbookジョブに例外が発生した場合、APIで取得した例外情報properties.exception
```

### エラー処理メカニズム

| エラーシナリオ | 関連エラーコード | 対処・補償ロジック |
|:---|:---|:---|
| Taskテーブルからのデータ取得失敗 | - | エラーログ記録。処理終了。 |
| Azure Automation API呼び出し失敗 | - | エラーログ記録。ループ継続。当該タスクは次回のRunbookジョブ監視関数に持ち越す。 |
| Taskテーブルのstatus更新失敗/更新件数が0件 | - | エラーログ記録。ループ継続。当該タスクは次回のRunbookジョブ監視関数に持ち越す。 |
| RunbookStatusQueueへのメッセージ送信失敗 | - | エラーログ記録。ループ継続。当該タスクは次回のRunbookジョブ監視関数に持ち越す。 |
| 予期せぬ内部エラー | - | エラーログ記録。ループ内ならループ継続し、当該タスクは次回のRunbookジョブ監視関数に持ち越す。 |

*   **タイムアウトについて**
    Azure FunctionsがRunbookジョブ監視関数の実行時間を監視し、host.jsonのfunctionTimeoutプロパティで設定されたタイムアウト時間（5分）になっても関数がまだ終了していない場合、Azure Functionsは関数の実行を強制的に中止する。
*   **リトライについて**
    Runbookジョブ監視関数はタイマーによって定期的に起動されるため、一回のRunbookジョブ監視関数で例外やタイムアウトが発生し、処理失敗の場合は別途リトライを行わず、次回のRunbookジョブ監視関数に持ち越すことで、実質リトライとなる。