# 组件：管理项目定义导入功能 (Import Management Item Definition Feature)

## 1. 概要 (Overview)

### 1.1 目的 (Purpose)
本功能允许用户针对特定的JP1/ITDM2服务器，通过上传一个包含管理项目定义的CSV文件，发起一个后台任务，以将这些定义导入到目标服务器中。此功能主要用于快速应用标准配置、从备份恢复配置或在多个服务器间同步配置。

### 1.2 用户故事/需求 (User Stories/Requirements)
*   作为一名顾客系统管理员，我希望能方便地为选定的JP1/ITDM2服务器上传一个CSV格式的管理项目定义文件，并执行导入操作。
*   作为一名顾客系统管理员，我希望在上传文件时，系统能对文件类型（CSV扩展名）进行客户端初步校验。
*   作为一名顾客系统管理员，我希望导入任务的执行状态和结果能在门户中被跟踪。

### 1.3 高层依赖与交互 (High-Level Dependencies & Interactions)
*   **依赖于**:
    *   [服务器列表主功能组件设计](../server-list.md)：用户通过此界面的任务操作菜单发起本导入任务。本组件 (`ManagementDefinitionImportModal`) 被 `ServerListPage` 调用和管理。
    *   用户认证模块：确保用户已登录并具有相应权限。
    *   Server Action [`createTaskAction` 组件设计文档](../../actions/create-task-action.md)：作为所有后台任务创建请求的统一入口，负责接收上传的文件并协调后续处理。
    *   数据服务模块 (`apps/jcs-endpoint-nextjs/app/lib/data.ts`)：间接通过 `createTaskAction` 使用。
    *   常量定义模块 (`apps/jcs-endpoint-nextjs/app/lib/definitions.ts`)：用于可能的内部常量定义（但用户可见消息文本通过消息ID从[`错误消息定义`](../../../definitions/error-messages.md)获取）。
    *   环境变量：`AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` (由 `createTaskAction` 内部使用，其值应为 "assetsfield-def")。详细定义参见 [`环境变量指南`](../../../guides/environment-variables.md)。
    *   后端核心服务 (Azure Functions `TaskExecuteFunc`, Azure Automation Runbook)：负责实际的任务编排与执行。
    *   Azure SQL Database：存储 `Task`, `ContainerConcurrencyStatus`, `Server`, `License`, `Lov` 表。
    *   Azure Blob Storage：由 `createTaskAction` 用于**临时存储**用户上传的CSV文件，具体存储在由环境变量 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` 指定的容器内的 `{licenseId}/imports/{taskId}/assetsfield_def.csv` 路径。
    *   Azure Files：Runbook将从其Azure Files工作区的 `imports/` 目录读取由 `TaskExecuteFunc` 准备好的CSV文件（文件名固定为 `assetsfield_def.csv`）。
*   **交互**:
    *   用户在[服务器列表主功能组件设计](../server-list.md)的界面选择“管理项目定义のインポート”操作后，`ServerListPage` 组件将显示本功能对应的参数输入对话框 (`ManagementDefinitionImportModal`)。
    *   用户在 `ManagementDefinitionImportModal` 中选择CSV文件并点击“インポート”按钮后，该模态框会将选定的 `File` 对象和原始文件名回调给 `ServerListPage`。
    *   `ServerListPage` 随后会弹出一个通用的二次确认模态框。其确认消息为：“{サーバ名}の管理項目定義をインポートします。よろしいですか？”。
    *   用户在通用二次确认模态框中确认后，`ServerListPage` 才调用 [`createTaskAction` Server Action](../../actions/create-task-action.md)，并将任务类型 (`TASK_TYPE.MGMT_ITEM_IMPORT`)、服务器ID以及从 `ManagementDefinitionImportModal` 获取的 `File` 对象和 `originalFileName` 作为参数提交。
    *   [`createTaskAction` Server Action](../../actions/create-task-action.md) 负责将接收到的文件上传到Azure Blob Storage的临时位置（路径为 `{licenseId}/imports/{taskId}/assetsfield_def.csv`，文件名被固定为 `assetsfield_def.csv`，容器由环境变量 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` 指定），并在 `Task` 记录的 `parametersJson` 中保存此临时文件的相对路径引用 (`importedFileBlobPath`) 及原始文件名 (`originalFileName`)。**`createTaskAction` 成功后会将任务消息发送到队列，后续由一系列后端Azure Functions进行异步处理，主要包括：[`TaskExecuteFunc`](../../backend-services-functions/function-task-execute.md) 负责任务的启动（包括从Blob临时存储下载导入文件到工作区并删除临时Blob文件）和Runbook提交，[`RunbookMonitorFunc`](../../backend-services-functions/function-runbook-monitor.md) 负责监控Runbook执行状态，[`RunbookProcessorFunc`](../../backend-services-functions/function-runbook-processor.md) 负责处理最终结果和资源清理。前端在 `createTaskAction` 调用成功后，不直接获取新任务ID，而是通过后续的任务列表刷新来查看新创建的任务及其状态。**
    *   任务的执行状态和结果可在[任务列表组件设计](../../13-task-list.md)中跟踪。此任务类型通常不直接产生用户可下载的“最终产出文件”。

## 2. 功能规格 (Functional Specifications)

### 2.1 主要流程 (High-Level Flow)
```mermaid
graph TD
    A["用户在服务器列表选择目标服务器<br/>并从任务菜单选择“管理项目定义のインポート”"] --> B["服务器列表页面 (ServerListPage):<br/>打开“管理项目定义导入参数对话框”<br/>(ManagementDefinitionImportModal)"];
    B --> BX["用户: 在 ManagementDefinitionImportModal 中<br/>通过文件选择对话框选择一个本地CSV文件。<br/>(文件输入框不可编辑，初始路径为桌面)"];
    BX --> C["ManagementDefinitionImportModal:<br/>显示选定文件的路径，<br/>并进行客户端文件扩展名校验"];
    C -- "文件扩展名校验通过<br/>(用户点击 ManagementDefinitionImportModal 的“インポート”按钮)" --> E_ParamsSubmitted["ManagementDefinitionImportModal:<br/>将选定的文件对象和原始文件名<br/>回调给 ServerListPage"];
    E_ParamsSubmitted --> E_ConfirmOpen["ServerListPage:<br/>打开通用二次确认模态框<br/>(消息：“{サーバ名}の管理項目定義をインポートします。よろしいですか？”)"];
    E_ConfirmOpen -- "用户在通用确认框中点击“OK/はい”" --> F_CallServerAction["ServerListPage (Frontend):<br/>调用 createTaskAction Server Action<br/>(taskType: 'TASK_TYPE.MGMT_ITEM_IMPORT',<br/> serverId, importFile, originalFileName)"];
    F_CallServerAction -- "Server Action成功接收并处理<br/>(包括文件临时上传至 'assetsfield-def' 容器)" --> G["服务器列表页面 (Frontend):<br/>根据消息ID EMEC0025 (参考 '错误消息定义')<br/>显示任务提交成功日文提示。<br/>(关闭所有相关模态框)"];
    G --> H["用户可在“任务列表”<br/>查看任务状态和结果。<br/>(前端不直接从createTaskAction获取taskId)"];
    C -- "文件扩展名校验失败<br/>(返回消息ID EMEC0017 或 EMEC0016)" --> H_Error["ManagementDefinitionImportModal:<br/>在对话框内显示错误提示<br/>(基于相应的消息ID)"];
    B -- "用户点击“キャンセル”或关闭参数对话框" --> I[操作取消, 参数对话框关闭];
    E_ConfirmOpen -- "用户在通用确认对话框中点击“キャンセル”或关闭" --> K_BackToParams["操作取消, 返回参数输入模态框<br/>(ManagementDefinitionImportModal)，<br/>并保留之前已选择的文件信息。"];
    F_CallServerAction -- "Server Action处理失败<br/>(例如参数服务端校验失败 EMEC0017,<br/>文件上传失败 EMEC0018,<br/>容器繁忙 EMEC0022 等)" --> J["服务器列表页面 (Frontend):<br/>根据返回的消息ID (参考 '错误消息定义')<br/>显示相应的日文错误提示。<br/>(关闭所有相关模态框)"];
```

### 2.2 业务规则 (Business Rules)
*   本操作仅对类型为JP1/ITDM2的服务器（其`Server.type`值为`'SERVER_TYPE.GENERAL_MANAGER'`或`'SERVER_TYPE.RELAY_MANAGER'`，由[服务器列表主功能组件设计](../server-list.md)控制入口的可见性）可用。
*   用户必须在参数输入对话框 (`ManagementDefinitionImportModal`) 中选择一个文件进行上传。文件输入框不可编辑，只能通过点击文件夹图标（或类似按钮）打开操作系统的文件选择对话框。文件选择对话框应配置为仅允许选择单个扩展名为 `.csv` 的文件，其初始打开路径建议为用户的桌面。
*   **客户端文件校验 (`ManagementDefinitionImportModal`)**:
    *   上传的文件必须具有 `.csv` 扩展名。此校验在 `ManagementDefinitionImportModal` 组件中进行。
    *   若未选择文件，提示 `EMEC0016` (占位符 `{0}` 替换为 “管理項目定義のCSVファイル” 或 “ファイル”)。
    *   若扩展名错误，提示 `EMEC0017`。
*   **服务器端文件校验 (在 [`createTaskAction` Server Action](../../actions/create-task-action.md) 中)**:
    *   [`createTaskAction` Server Action](../../actions/create-task-action.md) 在接收到 `File` 对象后，会对其MIME类型进行校验，期望为 `text/csv`。若校验失败，服务器端将返回 `messageId: 'EMEC0017'`。
    *   当前项目**未通过 `LOV` 表配置全局性的服务器端文件大小限制**。实际的文件大小限制将依赖于Web服务器（如Azure App Service）本身可能存在的配置上限。
    *   文件内容的详细业务逻辑校验（例如，CSV列的顺序、数据格式、业务规则符合性等）**不由** [`createTaskAction` Server Action](../../actions/create-task-action.md)直接负责，而是由后续的后端处理流程（如Azure Automation Runbook）在实际执行导入操作时进行。
*   后台任务的并发控制由通用的 [`createTaskAction` Server Action](../../actions/create-task-action.md) (并发检查，若记录不存在则创建) 及其调用的后端服务 (`TaskExecuteFunc` 负责写锁)处理。
*   **文件临时存储与传递**:
    1.  当用户提交导入请求并通过通用确认后，`ServerListPage` 调用 [`createTaskAction` Server Action](../../actions/create-task-action.md)，传递 `File` 对象。
    2.  `createTaskAction` Server Action 负责将接收到的 `File` 对象上传到Azure Blob Storage。此容器的名称通过环境变量 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` (其值应为 "assetsfield-def") 进行配置。上传时，文件名被固定为 `assetsfield_def.csv`，并存储在路径 `{licenseId}/imports/{taskId}/assetsfield_def.csv` (其中 `{taskId}` 是为该任务新生成的ID)。
    3.  成功上传后，此临时Blob的相对路径 (即 `{licenseId}/imports/{taskId}/assetsfield_def.csv`) 将作为 `importedFileBlobPath`，连同用户上传的原始文件名 `originalFileName`，一起记录在 `Task` 表对应记录的 `parametersJson` 字段中。其表结构定义参见[`Task数据模型定义`](../../../data-models/task.md)。
*   **后端处理流程**: 后端的 `TaskExecuteFunc` 在接收到此类型的任务消息后，会根据 `Task.parametersJson` 中的 `importedFileBlobPath`，从Azure Blob Storage的临时位置下载该CSV文件（其在Blob中的名称为 `assetsfield_def.csv`）到Azure Files工作区的 `imports/assetsfield_def.csv` 目录。**`TaskExecuteFunc` 在成功下载文件到工作区后，应立即删除Azure Blob Storage临时存储中的该文件 (`{licenseId}/imports/{taskId}/assetsfield_def.csv`)。**
*   每个发起的导入任务都会在 `Task` 数据库表中创建一条记录。
*   **任务记录保留策略**: `Task` 表中的任务记录将遵循系统中定义的保留策略（每个服务器的任务记录上限由 `LOV` 表 `TASK_CONFIG.MAX_RETENTION_COUNT` 的值设定，定义于[`LOV值列表定义`](../../../definitions/lov-definitions.md)）。当为某个服务器创建新任务导致超出此限制时，最早的旧 `Task` 记录将被删除。在删除与管理项目定义导入任务相关的旧 `Task` 记录时，如果其 `parametersJson` 中包含 `importedFileBlobPath`，则其指向的Azure Blob Storage中的临时上传文件也应被联动删除（此删除逻辑由 `createTaskAction` 中的保留策略执行）。

### 2.3 用户界面概述 (User Interface Overview)
*   **发起入口**: [服务器列表主功能组件设计](../server-list.md)界面的每行JP1/ITDM2服务器记录的任务操作下拉菜单中的“管理項目定義のインポート”选项。
*   **主要界面**: 管理项目定义导入参数输入对话框 (`ManagementDefinitionImportModal`)。
    *   **组件路径**: `apps/jcs-endpoint-nextjs/app/ui/servers/modals/management-definition-import-modal.tsx`
    *   **核心功能**: 提供一个文件选择控件 (HTML `<input type="file" accept=".csv">`)，允许用户选择本地的CSV文件。文件输入框不可编辑，通过点击文件夹图标（或类似按钮）打开文件选择对话框。选择文件后，对话框应能显示选中文件的路径，并进行客户端文件扩展名校验。
*   **最终确认**: 在 `ManagementDefinitionImportModal` 提交参数并回调给 `ServerListPage` 后，由 `ServerListPage` 负责弹出通用的二次确认对话框 (使用 `app/ui/message-modal.tsx`)，用户在此确认后才真正调用 `createTaskAction`。确认消息为：“{サーバ名}の管理項目定義をインポートします。よろしいですか？”。

### 2.4 前提条件 (Preconditions)
*   用户已通过身份验证并成功登录到门户系统。
*   目标服务器记录在系统中存在，并且其类型允许执行管理项目定义导入任务。
*   后端任务处理相关的Azure服务及数据库表已正确配置并运行。
*   Server Action [`createTaskAction` 组件设计文档](../../actions/create-task-action.md) 已在 `apps/jcs-endpoint-nextjs/app/lib/actions.ts` 中定义并可用。
*   `LOV` 表中与管理项目定义导入相关的配置项（如 `TASK_TYPE.MGMT_ITEM_IMPORT`, `TASK_CONFIG.MAX_RETENTION_COUNT`）已正确配置。其权威定义参见[`LOV值列表定义`](../../../definitions/lov-definitions.md)。
*   相关的**环境变量**（特别是 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF`，其值应为 "assetsfield-def"）已正确设置。其权威定义参见[`环境变量指南`](../../../guides/environment-variables.md)。
*   Azure Blob Storage 中由 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` 指定的容器必须已创建，并且 `createTaskAction` Server Action 运行的身份必须具有向此容器写入的权限。
*   用户提示信息的消息ID定义在[`错误消息定义`](../../../definitions/error-messages.md)中。

### 2.5 制约事项 (Constraints/Limitations)
*   不支持一次性向多个服务器批量导入管理项目定义。
*   导入操作是异步的后台任务，用户需要通过[任务列表组件设计](../../13-task-list.md)来查看任务的完成状态和结果（例如，成功导入或失败原因）。
*   客户端仅进行文件扩展名校验。文件内容和结构的有效性将在服务器端由后端Runbook在实际导入时进行校验。

### 2.6 注意事项 (Notes/Considerations)
*   导入的CSV文件内容和结构必须严格符合目标JP1/ITDM2管理工具的要求，否则后端Runbook在执行导入时很可能会失败，或导致目标服务器配置错误。
*   对于非常大的CSV文件，用户上传到前端以及Server Action将其上传到Blob临时存储的过程可能需要一些时间。前端界面在用户选择文件并点击“导入”按钮后，`ServerListPage` 在调用 `createTaskAction` 期间应有明确的等待或进度指示。
*   **临时文件的清理**:
    *   由 `createTaskAction` 上传到Blob的临时文件 (`{licenseId}/imports/{taskId}/assetsfield_def.csv`)，其主要清理责任方是后端的 `TaskExecuteFunc`。`TaskExecuteFunc` 在成功将此文件从Blob下载到其Azure Files工作区之后，**必须立即负责删除**该临时Blob文件。
    *   此外，在 `createTaskAction` 执行任务记录保留策略时，如果删除的是一个旧的管理项目定义导入任务记录，且该记录的 `parametersJson` 中仍包含有效的 `importedFileBlobPath` (理论上此时应已被 `TaskExecuteFunc` 清理，但作为容错机制)，则也应尝试删除此Blob文件。

### 2.7 错误处理概述 (Error Handling Overview)
*   **客户端文件校验错误**: 在参数输入对话框 (`ManagementDefinitionImportModal`) 中，如果用户选择了非 `.csv` 扩展名的文件（提示 `EMEC0017`），或未选择任何文件就尝试提交（提示 `EMEC0016`，占位符 `{0}` 替换为 “管理項目定義のCSVファイル” 或 “ファイル”），将在对话框内显示明确的日文错误提示信息（消息文本从[`错误消息定义`](../../../definitions/error-messages.md)获取），并阻止提交。
*   **文件上传或处理失败 (由 `createTaskAction` 返回)**: 如果 [`createTaskAction` Server Action](../../actions/create-task-action.md) 在处理文件时发生错误（例如上传到Blob失败，返回 `EMEC0018`；或服务器端MIME类型校验失败，返回 `EMEC0017`），它将返回包含错误信息的 `CreateTaskActionResult` 对象。调用方（[服务器列表主功能组件设计](../server-list.md)）将根据返回的消息ID (其日文文本在[`错误消息定义`](../../../definitions/error-messages.md)) 向用户显示相应的日文错误提示。
*   **其他通用任务提交失败**: 同其他任务类型（例如，容器繁忙 `EMEC0022`，数据库错误 `EMEC0006`，队列错误 `EMEC0019`）。
*   **后台任务执行失败**: 若任务在后端执行过程中失败，`Task` 表中对应任务的状态将被更新为错误状态。用户可在[任务列表组件设计](../../13-task-list.md)中查看到此失败状态及原因（例如，Runbook执行错误，可能返回 `EMET0012` - 有详细错误）。

### 2.8 相关功能参考 (Related Functional References)
*   [服务器列表主功能组件设计](../server-list.md)
*   Server Action: [`createTaskAction` 组件设计文档](../../actions/create-task-action.md)
*   [任务列表组件设计](../../13-task-list.md)
*   数据模型:
    *   [`Task` 数据模型定义](../../../data-models/task.md)
    *   [`Server` 数据模型定义](../../../data-models/server.md)
    *   [`License` 数据模型定义](../../../data-models/license.md)
    *   [`ContainerConcurrencyStatus` 数据模型定义](../../../data-models/container-concurrency-status.md)
*   系统级定义:
    *   [`LOV值列表定义`](../../../definitions/lov-definitions.md)
    *   [`错误消息定义`](../../../definitions/error-messages.md)
    *   [`项目术语表`](../../../definitions/glossary.md)
*   [`环境变量指南`](../../../guides/environment-variables.md)
*   常量定义: `apps/jcs-endpoint-nextjs/app/lib/definitions.ts` (用于内部常量，用户消息通过消息ID获取)

---

## 3. 技术设计与实现细节 (Technical Design & Implementation)

### 3.1 技术栈与依赖 (Technology Stack & Dependencies)
*   **前端 (管理项目定义导入参数模态框组件 - `ManagementDefinitionImportModal`)**:
    *   React (Next.js App Router 架构下的Client Component)
    *   HTML5 `<input type="file">` 元素用于文件选择。
    *   客户端状态管理: 使用React Hooks (`useState`) 管理模态框内部的表单数据（选定的文件对象）、校验错误信息、以及提交状态。
*   **后端 (主要通过 [`createTaskAction` Server Action](../../actions/create-task-action.md))**:
    *   Next.js Server Action (`createTaskAction` 位于 `apps/jcs-endpoint-nextjs/app/lib/actions.ts`)。
    *   Azure Blob Storage SDK (或其封装的服务): 用于在 `createTaskAction` 内部执行文件上传操作到由环境变量 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` 指定的容器。
    *   Prisma ORM (通过 `app/lib/data.ts` 间接使用)。
    *   Azure Service Bus SDK (通过 `createTaskAction` 间接使用)。
*   **共享类型定义与常量**: `apps/jcs-endpoint-nextjs/app/lib/definitions.ts`。
*   **配置数据来源**:
    *   `LOV` 表 (业务参数)。其权威定义参见[`LOV值列表定义`](../../../definitions/lov-definitions.md)。
    *   环境变量 (基础设施配置如容器名 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF`，其值应为 "assetsfield-def")。其权威定义参见[`环境变量指南`](../../../guides/environment-variables.md)。

### 3.2 详细界面元素定义 (Detailed UI Element Definitions)

#### 3.2.1 管理项目定义导入参数对话框 (`apps/jcs-endpoint-nextjs/app/ui/servers/modals/management-definition-import-modal.tsx`)
*   **用途**: 允许用户为“管理项目定义导入”任务选择一个本地的CSV文件进行上传。
*   **实现风格**: 自定义React客户端组件，使用原生的 `<input type="file">` 控件，能够显示选定文件的基本信息，并执行客户端文件扩展名校验。

| # | 控件内部逻辑名(中文) | 界面显示文本(日文) | 控件类型 (Type) | 建议英文ID (ID) | 数据来源/状态 (Data Source/State) (中文描述) | 主要行为/事件 (Behavior/Event) (中文描述) | 校验规则/显示逻辑 (Validation/Display Logic) (中文描述) | 格式/备注 (Format/Notes) (中文描述) |
|---|-------------|------------|-------------|-------------|------------------------------------|---------------------------------|---------------------------------------------|-----------------------------|
| 1 | 对话框容器 | - | `Dialog` (或自定义模态框容器) | `importDefModal` | `props.isOpen: boolean` (从父组件 `ServerListPage` 传入) | - | 当 `props.isOpen` 为 `true` 时显示。模态，带遮罩层。 | - |
| 2 | 对话框标题 | 管理項目定義のインポート | `Dialog.Title` (或 `h2`) | `importDefModalTitle` | `props.title?: string` (或固定文本“管理項目定義のインポート”) | - | - | - |
| 3 | 关闭按钮 | × (或SVG关闭图标) | `button` (或 `Dialog.Close`) | `importDefModalCloseBtn` | - | 点击时，调用 `props.onClose`。 | - | - |
| 4 | 服务器名称提示 | 対象サーバ: {サーバ名} | `p` | `importDefServerNameHint` | `props.serverName: string` (从父组件传入) | - | 固定显示，用于上下文。 | - |
| 5 | 文件选择提示 | 管理項目定義のCSVファイルを指定してください。 | `p` (class: `text-sm text-gray-600`) | `importDefFilePrompt` | (静态日文文本) | - | 指导用户操作。 | - |
| 6 | "文件"表单组 | - | `div` | `importDefFileGroup` | - | - | 包含标签和文件输入。 | - |
| 7 | "文件"标签 | ファイル | `label` | `importDefFileLabel` | (静态日文文本) | - | `htmlFor="importDefFileInput"` | - |
| 8 | 文件选择输入 (必填) | (按钮样式: "ファイルを選択..." 或 文件夹图标) | `<input type="file" accept=".csv" style={{ display: 'none' }}` (实际由一个button或label触发其click事件) | `importDefFileInput` | `internalState.selectedFile: File | null` (组件内部状态)。**键盘输入禁用，通过点击文件夹图标触发文件选择对话框**。 | `onChange` 事件调用 `handleFileChange(event)` (更新状态、显示信息、触发校验)。 | **必填项**。客户端通过`accept=".csv"`属性提示浏览器筛选CSV文件，并通过`validateFile()`函数校验文件扩展名是否为 `.csv` (不区分大小写)。若未选择文件，提示 `EMEC0016` (占位符 `{0}` 替换为 “管理項目定義のCSVファイル” 或 “ファイル”)。若扩展名错误，提示 `EMEC0017`。文件选择对话框应仅允许选择单个CSV文件，初始路径为用户桌面。 | 原生文件输入控件，但其本身可能被隐藏，通过一个更美观的按钮或图标来触发。 |
| 9 | 已选文件信息区 | (空白或: {ファイルのパス}) | `div` (class: `mt-2 text-xs text-gray-500`) | `importDefFileInfo` | `internalState.selectedFile` (用于动态显示文件路径)。 | - | 当`internalState.selectedFile`有效时显示。**根据日文DDS，此处显示用户选择的文件路径。** | - |
| 10 | 参数校验错误信息区 | (空白或具体的日文错误信息) | `div` (class: `text-red-500 text-sm min-h-[1.25rem]`) | `importDefValidationError` | `internalState.validationErrorMsgId: string | null` (组件内部状态，存储错误消息ID)。日文错误信息文本通过此ID从[`错误消息定义`](../../../definitions/error-messages.md)中获取，并替换占位符（若有）。 | - | 当`internalState.validationErrorMsgId`有值时显示。例如，显示与 `EMEC0016` 或 `EMEC0017` 对应的日文消息。 | - |
| 11 | 对话框操作按钮区域 | - | `div` (class: `flex justify-end space-x-2 mt-6`) | `importDefModalActions` | - | - | 包含“インポート”和“キャンセル”按钮。 | - |
| 12 | "导入"按钮 | インポート | `button` (主要操作按钮样式) | `importDefSubmitBtn` | `internalState.isSubmitting: boolean` (组件内部状态) | 点击时调用 `handleSubmit`。 | 当`internalState.selectedFile`已选择且`internalState.validationErrorMsgId`为空，且`internalState.isSubmitting`为`false`时激活。提交中禁用并**应显示**加载指示。 | - |
| 13 | "取消"按钮 | キャンセル | `button` (次要操作按钮样式) | `importDefCancelBtn` | `internalState.isSubmitting: boolean` (组件内部状态) | 点击时调用 `props.onClose`。 | 在`internalState.isSubmitting`为`true`时禁用。 | - |

### 3.3 详细事件处理逻辑 (Detailed Event Handling)

本节描述 `ManagementDefinitionImportModal` 组件内部以及其与父组件 `ServerListPage` 之间的关键事件处理流程。

#### 3.3.1 从服务器列表选择“管理項目定義のインポート” (父组件 `ServerListPage` 的行为)
*   **触发位置**: [服务器列表主功能组件设计](../server-list.md) (`page.tsx`) 中的 `handleOpenTaskInitiation` 函数 (或类似逻辑)。
*   **处理流程**: 当用户选择此任务时，`ServerListPage` 会：
    1.  设置其内部状态 (例如 `activeTaskContext.paramsModal`)，将 `isOpen` 设为 `true`，`type` 设为 `'TASK_TYPE.MGMT_ITEM_IMPORT'`，并传入 `server.name`，以及包含 `allowedFileExtensions` (值为 `['.csv']`)、`onSubmit` (指向 `ServerListPage` 的 `handleTaskParamsSubmitted` 回调，该函数内部会触发打开通用确认模态框)、`onClose` (指向 `ServerListPage` 的 `handleCloseTaskModal` 回调) 等的 `props`。
*   **结果**: `ManagementDefinitionImportModal` 组件因 `props.isOpen` 变为 `true` 而显示。

#### 3.3.2 在参数对话框中选择文件 (`handleFileChange` - `ManagementDefinitionImportModal` 内部函数)
*   **触发位置**: 文件选择输入控件 (`importDefFileInput`) 的 `onChange` 事件。
*   **处理流程**:
    1.  从 `event.target.files` 获取用户选择的 `File` 对象 (若有)。
    2.  更新组件内部状态 `internalState.selectedFile` 为此 `File` 对象。
    3.  如果选择了文件，则读取并更新UI以显示该文件的路径。
    4.  调用组件内部的 `validateFile()` 函数 (参见3.3.3节) 进行客户端文件校验。
*   **界面显示更新**: 已选文件信息区和参数校验错误信息区根据操作结果更新。

#### 3.3.3 在参数对话框中进行文件校验 (`validateFile()` - `ManagementDefinitionImportModal` 内部函数)
*   **触发时机**: `handleFileChange` 后，或 `handleSubmit` 前。
*   **输入**: 组件内部状态 `internalState.selectedFile`，以及从 `props` 接收的 `props.allowedFileExtensions` (值为 `['.csv']`)。
*   **核心处理逻辑**:
    1.  初始化 `currentValidationErrorMsgId = null`。
    2.  **文件选择校验**: 若 `internalState.selectedFile` 为 `null`，则设置 `currentValidationErrorMsgId` 为 `'EMEC0016'` (其日文模板为 "{0}を指定してください。" 前端UI逻辑需将 `{0}` 替换为 “管理項目定義のCSVファイル” 或 “ファイル”)。
    3.  **文件扩展名校验**: 若 `selectedFile` 存在，获取其文件名并提取扩展名。检查该扩展名（不区分大小写）是否存在于 `props.allowedFileExtensions` 列表中。若不符合，则设置 `currentValidationErrorMsgId` 为 `'EMEC0017'`。
    4.  更新组件内部状态 `internalState.validationErrorMsgId = currentValidationErrorMsgId`。
*   **效果**: 更新UI上的错误信息显示（通过消息ID从[`错误消息定义`](../../../definitions/error-messages.md)获取日文文本）。

#### 3.3.4 在参数对话框中点击“インポート”按钮 (`handleSubmit` - `ManagementDefinitionImportModal` 内部函数)
*   **触发位置**: “导入”按钮 (`importDefSubmitBtn`) 的 `onClick` 事件。
*   **处理流程**:
    1.  调用 `validateFile()` 进行最终校验。
    2.  若 `internalState.validationErrorMsgId` 不为空 (校验失败)，则不执行操作。
    3.  若校验通过：
        a.  设置 `internalState.isSubmitting = true`。
        b.  调用 `props.onSubmit({ file: internalState.selectedFile, originalFileName: internalState.selectedFile.name })` 将选定的文件对象和原始文件名回调给父组件 `ServerListPage`。`ServerListPage` 的 `onSubmit` 回调 (即 `handleTaskParamsSubmitted`) 会接着打开通用确认模态框。
        c.  等待 `props.onSubmit` Promise 完成后（即通用确认模态框被处理或关闭后），在 `finally` 块中设置 `internalState.isSubmitting = false`。
*   **界面显示更新**: “导入”按钮在提交期间应被禁用并显示加载指示。参数模态框通常在回调给父组件后由父组件控制关闭。

#### 3.3.5 (通用二次确认对话框由父组件 `ServerListPage` 处理)
*   当 `ManagementDefinitionImportModal` 调用其 `props.onSubmit` 并传递参数后，`ServerListPage` 中的对应处理函数 (例如 `handleTaskParamsSubmitted`) 会被触发。
*   此函数会打开一个通用的消息模态框 (`app/ui/message-modal.tsx`)。
*   **传递给通用模态框的Props**: 包含确认标题“管理項目定義のインポート確認”、确认消息“{サーバ名}の管理項目定義をインポートします。よろしいですか？”、以及 `onConfirm` 回调 (该回调将真正调用 `createTaskAction` 并传递所有必需参数，包括 `File` 对象和 `originalFileName`)、`onCancel` 回调等。
*   只有当用户在此通用确认模态框中点击“确认”后，才会实际调用 `createTaskAction`。

#### 3.3.6 在参数对话框或通用确认对话框中点击“キャンセル”或关闭
*   **参数对话框 (`ManagementDefinitionImportModal`)**: 点击“キャンセル”或关闭图标时，调用其 `props.onClose`，由 `ServerListPage` 的 `handleCloseTaskModal('params')` 关闭。
*   **通用确认对话框**: 点击“キャンセル”或关闭时，调用其 `props.onCancel`。`ServerListPage` 的 `handleCloseTaskModal('confirm', ...)` 逻辑应负责关闭通用确认模态框，并重新打开 `ManagementDefinitionImportModal` (参数模态框)，**并恢复之前已选择的文件信息**。

### 3.4 数据结构与API/Server Action交互 (Data Structures and API/Server Action Interaction)

#### 3.4.1 前端状态
*   **`ManagementDefinitionImportModal` 内部状态 (`internalState`)**:
    *   `selectedFile: File | null`
    *   `validationErrorMsgId: string | null` (错误消息ID，日文文本从[`错误消息定义`](../../../definitions/error-messages.md)获取并替换占位符)
    *   `isSubmitting: boolean`
*   **`ServerListPage` 组件状态**: (已在[服务器列表主功能组件设计](../server-list.md#341-前端核心状态管理)中定义，特别是 `activeTaskContext`，用于管理所有任务模态框的显隐及参数传递，以及通用二次确认模态框的状态和参数)。

#### 3.4.2 Server Action: `createTaskAction` (针对 `TASK_TYPE.MGMT_ITEM_IMPORT`)

`ServerListPage` (在通用二次确认模态框确认后) 调用 [`createTaskAction` Server Action](../../actions/create-task-action.md) 时，传递的 `FormData` 对象包含：

*   `taskType: string`: `'TASK_TYPE.MGMT_ITEM_IMPORT'`。
*   `serverId: string`: 目标服务器ID。
*   `importFile: File`: 用户上传的CSV文件对象 (从 `ManagementDefinitionImportModal` 回调的参数中获取)。
*   `originalFileName: string`: 上传文件的原始名称 (从 `ManagementDefinitionImportModal` 回调的参数中获取)。

返回值结构为 [`CreateTaskActionResult`](../../actions/create-task-action.md#13-返回值-createtaskactionresult)。

#### 3.4.3 主要交互序列图 (Mermaid Sequence Diagram for Import Management Definition)

```mermaid
sequenceDiagram
    actor User as 用户
    participant FrontendApp as 前端应用 (门户)
    participant NextJsAppServer as Next.js 应用服务器 (后端)
    participant Database as 数据库 (Task表等)
    participant MessageQueue as 消息队列 (TaskInputQueue)

    User->>FrontendApp: 1. 在服务器列表选择“管理项目定义导入”
    FrontendApp->>User: 2. (前端内部逻辑) 显示管理项目定义导入参数模态框
    User->>FrontendApp: 3. 在参数模态框中选择本地CSV文件并提交
    FrontendApp->>FrontendApp: 4. (前端内部逻辑) 客户端文件扩展名校验
    alt 文件扩展名无效 (客户端校验)
        FrontendApp->>User: 5a. 在参数模态框内显示错误提示
    else 文件扩展名有效 (客户端校验)
        FrontendApp->>User: 5b. (前端内部逻辑) 显示通用二次确认信息<br/>(例如：“确定要导入选定的定义文件吗？”)
        User->>FrontendApp: 6. 点击“OK/はい” (最终确认)
        FrontendApp->>NextJsAppServer: 7. 发起后台任务创建请求 (HTTP POST)<br/>(调用 createTaskAction Server Action,<br/>携带taskType, serverId, importFile, originalFileName)
        activate NextJsAppServer
        NextJsAppServer->>NextJsAppServer: 8. (在createTaskAction内) 执行通用前置校验<br/>(用户会话,权限,服务器配置,并发检查, <br/> 若并发记录不存在则创建并初始化为IDLE)<br/>及特定任务逻辑(生成taskId, 服务器端文件类型校验)
        alt 服务器端参数校验失败 (例如文件类型无效)
            NextJsAppServer-->>FrontendApp: 9a. 返回参数错误响应 (含messageId)
        else 服务器端参数校验通过
            NextJsAppServer->>NextJsAppServer: 9b. (在createTaskAction内) 将 importFile 上传到Azure Blob Storage临时区<br/>(路径: {licenseId}/imports/{taskId}/assetsfield_def.csv)
            alt 文件上传到Blob失败
                NextJsAppServer-->>FrontendApp: 10a. 返回文件上传错误响应 (含messageId)
            else 文件上传到Blob成功 (获取到 importedFileBlobPath)
                NextJsAppServer->>Database: 10b. (在createTaskAction内) 执行核心数据库事务<br/>(含任务保留策略, 创建Task记录 - parametersJson含importedFileBlobPath等)
                activate Database
                Database-->>NextJsAppServer: 11b. 数据库事务结果
                deactivate Database
                alt 数据库事务失败 (或并发锁获取失败)
                    NextJsAppServer-->>FrontendApp: 12a. 返回DB错误响应 (含messageId)。<br/>(createTaskAction应负责删除已上传的临时Blob文件)
                else 数据库事务成功
                    NextJsAppServer->>MessageQueue: 12b. (在createTaskAction内) 发送任务消息 (仅含taskId)
                    activate MessageQueue
                    MessageQueue-->>NextJsAppServer: 13b. 消息发送结果
                    deactivate MessageQueue
                    alt 消息发送失败
                        NextJsAppServer-->>FrontendApp: 14a. 返回队列错误响应 (含messageId)。<br/>(createTaskAction应负责删除已上传的临时Blob文件)
                    else 消息发送成功
                        NextJsAppServer->>NextJsAppServer: 14b. (在createTaskAction内) 调用 revalidatePath
                        NextJsAppServer-->>FrontendApp: 15b. 返回成功响应 (含messageId)
                    end
                end
            end
        end
        deactivate NextJsAppServer
        
        FrontendApp->>User: 16. 根据Server Action响应结果<br/>显示成功或失败提示 (基于messageId)。<br/>(关闭所有相关模态框)
    end
```

### 3.5 数据库设计与访问详情 (Database Design and Access Details - 主要通过Server Action间接访问)
本功能的前端部分不直接与数据库交互。数据库操作由 [`createTaskAction` Server Action](../../actions/create-task-action.md) 内部通过数据服务模块执行。

*   **`Task` 表**: (参见[`Task数据模型定义`](../../../data-models/task.md))
    *   当任务类型为 `'TASK_TYPE.MGMT_ITEM_IMPORT'` 时，`parametersJson` 字段将存储包含 `importedFileBlobPath` (指向临时上传到 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` 容器下 `{licenseId}/imports/{taskId}/assetsfield_def.csv` 的相对路径) 和 `originalFileName` (用户上传的原始文件名) 的JSON字符串。
    *   `targetVmName`, `dockerContainerName`, `hrwGroupName` 等执行上下文信息直接存入 `Task` 表的对应专用字段。
*   其他相关表如 `ContainerConcurrencyStatus`, `Server`, `License`, `Lov` 的交互逻辑与导出任务类似，详见 [`createTaskAction` Server Action文档](../../actions/create-task-action.md)。

### 3.6 关键后端逻辑/算法 (Key Backend Logic/Algorithms - 主要在Server Action `createTaskAction` 中)

管理项目定义导入功能的特定后端逻辑主要体现在 [`createTaskAction` Server Action](../../actions/create-task-action.md) 内部处理 `taskType` 为 `'TASK_TYPE.MGMT_ITEM_IMPORT'` 的分支中。

#### 3.6.1 [`createTaskAction`](../../actions/create-task-action.md) 中处理 `TASK_TYPE.MGMT_ITEM_IMPORT` 的特定逻辑分支

当 [`createTaskAction` Server Action](../../actions/create-task-action.md) 接收到 `taskType` 为 `'TASK_TYPE.MGMT_ITEM_IMPORT'` 的请求时，在执行通用的任务创建前置步骤（包括在步骤5.0生成`taskId`）之后，将执行以下特定逻辑（详细定义见 `createTaskAction.md` 的3节）：

1.  **提取并校验特定参数**: 从 `FormData` 中提取 `importFile` (`File` 对象) 和 `originalFileName` (`string`)。服务器端校验 `importFile` 的存在性和MIME类型 (期望 `text/csv`，若不符则返回 `messageId: 'EMEC0017'`)。
2.  **文件上传至Azure Blob Storage临时存储**:
    *   使用步骤5.0中已生成的 `taskId`。
    *   目标容器名称由环境变量 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` (其值应为 "assetsfield-def") 确定。
    *   目标Blob的相对路径为 `{licenseId}/imports/{generatedTaskId}/assetsfield_def.csv` (文件名固定为 `assetsfield_def.csv`，其中 `{generatedTaskId}` 即为步骤5.0中生成的 `taskId`)。
    *   上传成功后，此相对路径作为 `importedFileBlobPath`。若上传失败，返回 `messageId: 'EMEC0018'`。
3.  **构造存入 `Task.parametersJson` 的特定参数部分 (`taskSpecificParamsForDb`)**: 包含 `importedFileBlobPath` 和 `originalFileName`。
    *   示例 `parametersJson` 内容: `{"importedFileBlobPath": "{licenseId}/imports/{taskId}/assetsfield_def.csv", "originalFileName": "user_uploaded_file.csv"}`
4.  **构造发送到Service Bus消息体中（供 `TaskExecuteFunc` 使用）的参数**: **当前设计为仅在消息中传递 `taskId`。** `TaskExecuteFunc` 将根据 `taskId` 从数据库的 `Task` 记录中（特别是其 `parametersJson` 字段）获取 `importedFileBlobPath` 等信息。
5.  后续的通用处理流程（如并发检查、任务记录保留策略、数据库事务、消息发送）遵循 `createTaskAction.md` 的定义。**特别注意：如果在数据库事务或消息发送步骤失败，`createTaskAction` 必须负责删除步骤2中已上传到Blob的临时文件。**

### 3.7 错误处理详情 (Detailed Error Handling)

| 错误场景描述 (中文) | 触发位置 (前端/ServerAction) | 返回的 `CreateTaskActionResult` / 客户端处理方式 | 用户提示信息 (日文界面文本) (基于消息ID，其日文文本定义在[`错误消息定义`](../../../definitions/error-messages.md) 中，并替换占位符若有) | 系统内部处理及日志记录建议 (中文) |
|-------------|------------------------|----------------------------------------|------------------------------------------------------------------------------------------------|--------------------|
| **客户端校验**: 未选择任何文件 | 前端 (`ManagementDefinitionImportModal`) | `internalState.validationErrorMsgId` 更新为 `'EMEC0016'` 并在UI显示。提交被阻止。 | 消息ID: `EMEC0016` (占位符 `{0}` 替换为 “管理項目定義のCSVファイル” 或 “ファイル”) | `ManagementDefinitionImportModal` 组件：阻止提交，在UI上显示错误。 |
| **客户端校验**: 文件扩展名非 `.csv` | 前端 (`ManagementDefinitionImportModal`) | `internalState.validationErrorMsgId` 更新为 `'EMEC0017'` 并在UI显示。提交被阻止。 | 消息ID: `EMEC0017` | `ManagementDefinitionImportModal` 组件：阻止提交，在UI上显示错误。 |
| **服务器端校验**: 未接收到文件对象 (`importFile` 为空) | `createTaskAction` Server Action (特定逻辑分支) | `{ success: false, messageId: 'EMEC0016' }` (占位符由服务器端填充为“ファイル”) | 消息ID: `EMEC0016` | `createTaskAction`: 记录错误日志。返回错误给客户端。 |
| **服务器端校验**: 文件MIME类型不符合预期 (非 `text/csv`) | `createTaskAction` Server Action (特定逻辑分支) | `{ success: false, messageId: 'EMEC0017' }` | 消息ID: `EMEC0017` (“無効なファイル形式です。CSVファイルを指定してください。”) | `createTaskAction`: 记录错误日志，包含实际的文件类型。返回错误给客户端。 |
| 文件上传到Azure Blob Storage临时存储失败 | `createTaskAction` Server Action (特定逻辑分支) | `{ success: false, messageId: 'EMEC0018' }` | 消息ID: `EMEC0018` | `createTaskAction`: 捕获Blob上传过程中抛出的异常。记录详细错误日志（包括Blob服务的错误信息）。返回错误给客户端。 |
| (其他通用错误场景参考 [`createTaskAction` Server Action文档](../../actions/create-task-action.md) 和 [服务器列表主功能组件设计](../server-list.md)) | `createTaskAction` Server Action / `ServerListPage` | (参考对应文档) | (参考对应文档) | (参考对应文档) |

### 3.8 配置项 (Configuration)
*   **`LOV` 表** (通过 `apps/jcs-endpoint-nextjs/app/lib/data.ts` 中的服务访问，其权威定义参见 [`LOV值列表定义`](../../../definitions/lov-definitions.md)):
    *   `TASK_TYPE.MGMT_ITEM_IMPORT` (`code`: "TASK_TYPE.MGMT_ITEM_IMPORT", `name` (日文): "管理項目定義のインポート")
    *   `TASK_CONFIG.MAX_RETENTION_COUNT` (`code`: "TASK_CONFIG.MAX_RETENTION_COUNT", `value`: "10" (示例值))
*   **环境变量** (其权威定义参见 [`环境变量指南`](../../../guides/environment-variables.md)):
    *   `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF`: (值应为 "assetsfield-def") 指定用于临时存储用户上传的CSV文件的Azure Blob Storage容器名称。此配置由 `createTaskAction` Server Action 使用。
    *   间接依赖于 [`createTaskAction` Server Action](../../actions/create-task-action.md) 所需的其他环境变量（如数据库连接、Service Bus连接、Blob Storage连接字符串等）。
*   常量定义 (`apps/jcs-endpoint-nextjs/app/lib/definitions.ts`) (不直接用于用户消息，但可能用于内部逻辑或类型)。

### 3.9 注意事项与其他 (Notes/Miscellaneous)
*   **文件上传安全性**: [`createTaskAction` Server Action](../../actions/create-task-action.md) 在接收到文件后，除了MIME类型校验外，还应对原始文件名 (`originalFileName`) 进行清理（例如，移除或替换不安全字符），以防止其在日志或工作区路径构造中引入安全风险。最终存储到Blob的临时文件名是固定的 `assetsfield_def.csv`，这降低了Blob路径本身被操纵的风险。
*   **临时文件的清理**:
    *   **主要责任方**: 后端的 `TaskExecuteFunc` 在成功将文件从Azure Blob Storage的临时存储位置（路径为 `{licenseId}/imports/{taskId}/assetsfield_def.csv`，容器由 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` 指定）下载到其Azure Files工作区之后，**必须立即负责删除**该临时Blob文件。
    *   **补偿清理责任方**: `createTaskAction` Server Action，如果在临时文件已上传到Blob之后，后续的数据库事务或Service Bus消息发送失败，**必须负责删除**这个已上传的Blob文件。
    *   **机制**: 清理机制应确保即使任务处理中途失败，如果文件已成功下载到工作区，临时Blob也应被清理。详细的清理逻辑应在 `TaskExecuteFunc` 的组件设计文档中描述。
    *   此外，`createTaskAction` 中的任务记录保留策略也会在删除旧的导入任务记录时，尝试删除其关联的临时Blob文件（作为一种补充清理机制）。
*   **Runbook脚本 (`Import-ManagementDefinition.ps1` - 假设名称)**:
    *   需能正确接收并解析从Service Bus消息中传递过来的参数，特别是 `targetContainerName` 和 `sourceFileInWorkspace` (应为 `"imports/assetsfield_def.csv"`，因为 `TaskExecuteFunc` 下载并固定了此名称和路径)。
    *   负责调用目标Docker容器内JP1/ITDM2的管理项目定义导入命令。
    *   执行完毕后上报任务状态及结果信息。
*   **`TaskExecuteFunc` 的职责 (针对导入任务)**:
    *   在处理此类型的任务时，其核心职责之一是根据 `Task.parametersJson` 中记录的 `importedFileBlobPath`，从Azure Blob Storage的临时存储位置安全地下载CSV文件（其Blob名为 `assetsfield_def.csv`），并将其放置到为当前任务分配的Azure Files工作区的预定 `imports/assetsfield_def.csv` 目录下。**成功下载后，立即删除临时Blob文件。**
