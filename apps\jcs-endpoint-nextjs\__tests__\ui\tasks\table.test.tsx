/**
 * @jest-environment jsdom
 */

// Web API polyfills
Object.defineProperty(global, 'Response', {
  value: class Response {
    constructor(_body?: any, _init?: any) {}
    static json(data: any) { return new Response(JSON.stringify(data)); }
  }
});
Object.defineProperty(global, 'Request', {
  value: class Request {
    constructor(_input: any, _init?: any) {}
  }
});

// Azure関連の依存を完全にモックし、ESM構文エラーを防止する
jest.mock("@azure/storage-blob", () => ({}));
jest.mock("@azure/service-bus", () => ({}));
jest.mock("@azure/msal-node", () => ({}));
jest.mock("@azure/identity", () => ({}));
jest.mock("next/server", () => ({}));

// Next.js関連のモック
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    replace: jest.fn(),
    push: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    refresh: jest.fn(),
  }),
  usePathname: () => "/dashboard/tasks",
  useSearchParams: () => ({
    get: jest.fn(),
    set: jest.fn(),
    entries: () => [],
    keys: () => [],
    values: () => [],
    toString: () => "",
  }),
}));

// iron-sessionのモック
jest.mock("iron-session", () => ({
  getIronSession: jest.fn().mockResolvedValue({
    user: {
      tz: "Asia/Tokyo",
      licenseId: "test-license",
    },
  }),
}));

// next/headersのモック
jest.mock("next/headers", () => ({
  cookies: jest.fn(),
}));

// ServerDataTasksのモック
jest.mock("@/app/lib/data/tasks", () => ({
  ServerDataTasks: {
    fetchFilteredTasks: jest.fn(),
  },
}));

// utilsのモック
jest.mock("@/app/lib/utils", () => ({
  formatDate: jest.fn((date, timezone) => {
    if (!date) return "";
    return "2024/01/01 10:00:00";
  }),
}));

// タスクアクションのモック
jest.mock("@/app/ui/tasks/actions-modals", () => {
  return function MockTaskActions({ task }: { task: any }) {
    return (
      <div data-testid={`task-actions-${task.id}`}>
        {task.status === "実行待ち" && (
          <button data-testid={`cancel-button-${task.id}`}>中止する</button>
        )}
        {task.status === "正常終了" && task.taskType === "管理項目定義のエクスポート" && (
          <a data-testid={`download-link-${task.id}`} href={`/dashboard/tasks/${task.id}/download`}>
            ダウンロード
          </a>
        )}
        {task.status === "エラー" && (
          <button data-testid={`error-detail-button-${task.id}`}>エラー詳細を表示</button>
        )}
        {task.status === "中止" && (
          <span data-testid={`cancelled-message-${task.id}`}>
            ユーザーによってタスクが中止されました。
          </span>
        )}
      </div>
    );
  };
});

import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import TasksTable from "@/app/ui/tasks/table";
import { ServerDataTasks } from "@/app/lib/data/tasks";

/**
 * @fileoverview タスク一覧テーブルのUIコンポーネントテスト
 * @description
 * 設計文書「02-画面項目定義.md」のタスク一覧項目1-25に基づく
 * タスクテーブルの全UI要素と動的表示制御を検証する。
 * 
 * テスト対象UI要素：
 * 1. タスク一覧エリア（table）
 * 2-4. タスク名列・ソート・データ表示
 * 5-7. ステータス列・ソート・データ表示
 * 8-10. 開始日時列・ソート・データ表示
 * 11-13. 終了日時列・ソート・データ表示
 * 14-16. サーバ名列・ソート・データ表示
 * 17-19. タスク種別列・ソート・データ表示
 * 20-22. 実行ユーザー列・ソート・データ表示
 * 23-24. タスク詳細列・動的表示制御
 * 25. スクロールバー表示制御
 * 
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

describe("タスク一覧テーブル", () => {
  const mockProps = {
    filter: "",
    page: 1,
    size: 10,
    sort: "startedAt" as const,
    order: "desc" as const,
  };

  const mockTasks = [
    {
      id: "task-1",
      taskName: "TestServer-操作ログのエクスポート-20240101100000",
      status: "実行待ち",
      startedAt: "2024-01-01T10:00:00Z",
      endedAt: null,
      targetServerName: "TestServer",
      taskType: "操作ログのエクスポート",
      submittedByUserId: "<EMAIL>",
      resultMessage: null,
    },
    {
      id: "task-2",
      taskName: "TestServer2-管理項目定義のエクスポート-20240101110000",
      status: "正常終了",
      startedAt: "2024-01-01T11:00:00Z",
      endedAt: "2024-01-01T11:05:00Z",
      targetServerName: "TestServer2",
      taskType: "管理項目定義のエクスポート",
      submittedByUserId: "<EMAIL>",
      resultMessage: null,
    },
    {
      id: "task-3",
      taskName: "TestServer3-管理項目定義のインポート-20240101120000",
      status: "エラー",
      startedAt: "2024-01-01T12:00:00Z",
      endedAt: "2024-01-01T12:02:00Z",
      targetServerName: "TestServer3",
      taskType: "管理項目定義のインポート",
      submittedByUserId: "<EMAIL>",
      resultMessage: "インポート処理中にエラーが発生しました。",
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (ServerDataTasks.fetchFilteredTasks as jest.Mock).mockResolvedValue(mockTasks);
  });

  /**
   * 試験観点：設計文書項目1-22のテーブル基本構造確認
   * 試験対象：設計文書「02-画面項目定義.md」タスク一覧項目1-22
   * 試験手順：
   * 1. TasksTableをレンダリング
   * 2. テーブル構造と列ヘッダーを確認
   * 確認項目：
   * - 項目1: タスク一覧エリア（table）が表示されること
   * - 項目2,5,8,11,14,17,20,23: 各列ヘッダーが正しく表示されること
   * - 項目4,7,10,13,16,19,22: 各データが正しく表示されること
   */
  it("正常系: テーブル基本構造と列ヘッダーが正しく表示される", async () => {
    render(await TasksTable(mockProps));

    // 項目1: タスク一覧エリア（table）
    const table = screen.getByRole("table");
    expect(table).toBeInTheDocument();

    // 項目2: タスク名列
    expect(screen.getByText("タスク名")).toBeInTheDocument();
    
    // 項目5: ステータス列
    expect(screen.getByText("ステータス")).toBeInTheDocument();
    
    // 項目8: 開始日時列
    expect(screen.getByText("開始日時")).toBeInTheDocument();
    
    // 項目11: 終了日時列
    expect(screen.getByText("終了日時")).toBeInTheDocument();
    
    // 項目14: サーバ名列
    expect(screen.getByText("サーバ名")).toBeInTheDocument();
    
    // 項目17: タスク種別列
    expect(screen.getByText("タスク種別")).toBeInTheDocument();
    
    // 項目20: 実行ユーザー列
    expect(screen.getByText("実行ユーザー")).toBeInTheDocument();
    
    // 項目23: タスク詳細列
    expect(screen.getByText("タスク詳細")).toBeInTheDocument();
  });

  /**
   * 試験観点：設計文書項目4のタスク名フォーマット確認
   * 試験対象：設計文書「02-画面項目定義.md」項目4のタスク名フォーマット
   * 試験手順：
   * 1. TasksTableをレンダリング
   * 2. タスク名が正しいフォーマットで表示されることを確認
   * 確認項目：
   * - フォーマット：{ServerName}-{タスク種別}-{YYYYMMDDHHmmss}
   * - 各タスクのタスク名が正しく表示されること
   */
  it("正常系: タスク名が正しいフォーマットで表示される", async () => {
    render(await TasksTable(mockProps));

    // 項目4: タスク名の表示確認
    expect(screen.getByText("TestServer-操作ログのエクスポート-20240101100000")).toBeInTheDocument();
    expect(screen.getByText("TestServer2-管理項目定義のエクスポート-20240101110000")).toBeInTheDocument();
    expect(screen.getByText("TestServer3-管理項目定義のインポート-20240101120000")).toBeInTheDocument();
  });

  /**
   * 試験観点：設計文書項目10,13の日時フォーマット確認
   * 試験対象：設計文書「02-画面項目定義.md」項目10,13の日時表示
   * 試験手順：
   * 1. TasksTableをレンダリング
   * 2. 開始日時・終了日時が正しいフォーマットで表示されることを確認
   * 確認項目：
   * - フォーマット：YYYY/MM/DD hh:mm:ss
   * - ユーザーのタイムゾーンで表示されること
   * - 終了日時がnullの場合は空文字で表示されること
   */
  it("正常系: 日時が正しいフォーマットで表示される", async () => {
    const { formatDate } = require("@/app/lib/utils");
    
    render(await TasksTable(mockProps));

    // formatDateが正しいパラメータで呼ばれることを確認
    expect(formatDate).toHaveBeenCalledWith("2024-01-01T10:00:00Z", "Asia/Tokyo");
    expect(formatDate).toHaveBeenCalledWith("2024-01-01T11:00:00Z", "Asia/Tokyo");
    expect(formatDate).toHaveBeenCalledWith("2024-01-01T11:05:00Z", "Asia/Tokyo");
    expect(formatDate).toHaveBeenCalledWith("2024-01-01T12:00:00Z", "Asia/Tokyo");
    expect(formatDate).toHaveBeenCalledWith("2024-01-01T12:02:00Z", "Asia/Tokyo");

    // 日時が表示されることを確認
    const dateElements = screen.getAllByText("2024/01/01 10:00:00");
    expect(dateElements.length).toBeGreaterThan(0);
  });

  /**
   * 試験観点：設計文書項目24のタスク詳細動的表示確認
   * 試験対象：設計文書「02-画面項目定義.md」項目24のステータス別表示制御
   * 試験手順：
   * 1. 各ステータスのタスクでTasksTableをレンダリング
   * 2. ステータスに応じた表示内容を確認
   * 確認項目：
   * - 実行待ち（QUEUED）：中止ボタンが表示されること
   * - 正常終了（COMPLETED_SUCCESS）かつ管理項目定義のエクスポート：ダウンロードリンクが表示されること
   * - エラー（COMPLETED_ERROR）：エラー詳細表示リンクが表示されること
   */
  it("正常系: タスク詳細がステータスに応じて動的に表示される", async () => {
    render(await TasksTable(mockProps));

    // 実行待ち：中止ボタンが表示される
    expect(screen.getByTestId("cancel-button-task-1")).toBeInTheDocument();

    // 正常終了かつ管理項目定義のエクスポート：ダウンロードリンクが表示される
    expect(screen.getByTestId("download-link-task-2")).toBeInTheDocument();
    expect(screen.getByTestId("download-link-task-2")).toHaveAttribute(
      "href",
      "/dashboard/tasks/task-2/download"
    );

    // エラー：エラー詳細表示ボタンが表示される
    expect(screen.getByTestId("error-detail-button-task-3")).toBeInTheDocument();
  });

  /**
   * 試験観点：データなし時の表示確認
   * 試験対象：タスクデータが空の場合の表示制御
   * 試験手順：
   * 1. 空のタスクデータでTasksTableをレンダリング
   * 2. 適切なメッセージが表示されることを確認
   * 確認項目：
   * - 「該当するタスクがありません」メッセージが表示されること
   * - テーブル構造は維持されること
   */
  it("境界条件: データなし時の表示確認", async () => {
    (ServerDataTasks.fetchFilteredTasks as jest.Mock).mockResolvedValue([]);

    render(await TasksTable(mockProps));

    // テーブル構造は維持される
    const table = screen.getByRole("table");
    expect(table).toBeInTheDocument();

    // 「該当するタスクがありません」メッセージが表示される
    expect(screen.getByText("該当するタスクがありません")).toBeInTheDocument();
  });

  /**
   * 試験観点：設計文書項目9,12のソートアイコン表示確認
   * 試験対象：設計文書「02-画面項目定義.md」項目9,12のソート表示制御
   * 試験手順：
   * 1. デフォルトソート（開始日時降順）でTasksTableをレンダリング
   * 2. ソートアイコンの表示を確認
   * 確認項目：
   * - 項目9: 開始日時ソート（降順のアイコン）が表示されること
   * - 項目12: 終了日時ソート（非表示）であること
   * - その他のソートアイコンは非表示であること
   */
  it("正常系: デフォルトソート（開始日時降順）のアイコン表示", async () => {
    render(await TasksTable(mockProps));

    // Theadコンポーネントが正しいデフォルト設定で呼ばれることを確認
    // 実際の実装では、defaultOrder="startedAt", defaultSort="desc"が設定されている
    const table = screen.getByRole("table");
    expect(table).toBeInTheDocument();

    // 開始日時列ヘッダーが存在することを確認
    const startedAtHeader = screen.getByText("開始日時");
    expect(startedAtHeader).toBeInTheDocument();
  });

  /**
   * 試験観点：中止ステータスのタスク表示確認
   * 試験対象：設計文書「02-画面項目定義.md」項目24の中止タスク表示
   * 試験手順：
   * 1. 中止ステータスのタスクを含むデータでレンダリング
   * 2. 中止メッセージが表示されることを確認
   * 確認項目：
   * - 中止（CANCELLED）：EMET0004メッセージが表示されること
   */
  it("正常系: 中止ステータスのタスク表示確認", async () => {
    const cancelledTask = {
      id: "task-cancelled",
      taskName: "TestServer-操作ログのエクスポート-20240101130000",
      status: "中止",
      startedAt: "2024-01-01T13:00:00Z",
      endedAt: null,
      targetServerName: "TestServer",
      taskType: "操作ログのエクスポート",
      submittedByUserId: "<EMAIL>",
      resultMessage: "ユーザーによってタスクが中止されました。",
    };

    (ServerDataTasks.fetchFilteredTasks as jest.Mock).mockResolvedValue([cancelledTask]);

    render(await TasksTable(mockProps));

    // 中止メッセージが表示されることを確認
    expect(screen.getByTestId("cancelled-message-task-cancelled")).toBeInTheDocument();
    expect(screen.getByText("ユーザーによってタスクが中止されました。")).toBeInTheDocument();
  });

  /**
   * 試験観点：必須テキストの表示確認
   * 試験対象：TasksTable コンポーネントの必須テキスト表示
   * 試験手順：
   * 1. コンポーネントをレンダリング
   * 2. 必須テキストがすべて表示されていることを確認
   * 確認項目：
   * - 必須テキストがすべて表示されていること
   */
  it("必須テキストがすべて表示されること", async () => {
    render(await TasksTable(mockProps));

    // 必須テキスト
    const requiredTexts = [
      "タスク名",
      "ステータス",
      "開始日時",
      "終了日時",
      "サーバ名",
      "タスク種別",
      "実行ユーザー",
      "タスク詳細",
    ];

    const bodyText = document.body.textContent || "";

    // 必須テキストがすべて存在することを確認
    requiredTexts.forEach(requiredText => {
      expect(bodyText).toContain(requiredText);
    });
  });

  /**
   * 試験観点：許可されたテキストのみ表示確認
   * 試験対象：TasksTable コンポーネントのテキスト表示制御
   * 試験手順：
   * 1. コンポーネントをレンダリング
   * 2. 許可されたテキストのみが表示されていることを確認
   * 確認項目：
   * - 許可されていないテキストが表示されていないこと
   */
  it("許可されたテキストのみ表示されること", async () => {
    render(await TasksTable(mockProps));

    // 許可されたテキストの完全リスト
    const allowedTexts = [
      "タスク名",
      "ステータス",
      "開始日時",
      "終了日時",
      "サーバ名",
      "タスク種別",
      "実行ユーザー",
      "タスク詳細",
      "TestServer-操作ログのエクスポート-20240101100000",
      "TestServer2-管理項目定義のエクスポート-20240101110000",
      "TestServer3-管理項目定義のインポート-20240101120000",
      "実行待ち",
      "正常終了",
      "エラー",
      "TestServer",
      "TestServer2",
      "TestServer3",
      "操作ログのエクスポート",
      "管理項目定義のエクスポート",
      "管理項目定義のインポート",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "2024/01/01 10:00:00",
      "該当するタスクがありません",
      // タスク詳細関連（イベント定義より）
      "中止する",
      "ダウンロード",
      "詳細を表示",
      // 数字（ボタンの番号など）
      "1",
      "2",
      "3",
    ];

    const bodyText = document.body.textContent || "";

    // 許可されたテキストをすべて除去
    let remainingText = bodyText;
    allowedTexts.forEach(allowedText => {
      remainingText = remainingText.replace(new RegExp(allowedText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), '');
    });

    // 残ったテキストから空白文字を除去
    const unauthorizedText = remainingText.replace(/[\s\n\r\t]+/g, '').trim();

    // 許可されていないテキストがないことを確認
    expect(unauthorizedText).toBe('');
  });

  /**
   * 試験観点：actions列のソート機能無効化確認
   * 試験対象：タスク詳細列（actions）のソート機能
   * 試験手順：
   * 1. TasksTableをレンダリング
   * 2. タスク詳細列ヘッダーの要素を取得
   * 3. ソート関連のクラスやイベントが設定されていないことを確認
   * 確認項目：
   * - タスク詳細列ヘッダーにcursor-pointerクラスが設定されていないこと
   * - タスク詳細列ヘッダーにhover:opacity-80クラスが設定されていないこと
   * - タスク詳細列ヘッダーにソートアイコンが表示されないこと
   */
  it("機能テスト: actions列はソート機能が無効化されていること", async () => {
    render(await TasksTable(mockProps));

    // タスク詳細列ヘッダーを取得
    const taskDetailHeader = screen.getByText("タスク詳細");
    expect(taskDetailHeader).toBeInTheDocument();

    // タスク詳細列ヘッダーの親要素（div）を取得
    const taskDetailHeaderDiv = taskDetailHeader.parentElement;
    expect(taskDetailHeaderDiv).toBeInTheDocument();

    // ソート機能が無効化されていることを確認
    // cursor-pointerクラスが設定されていないことを確認
    expect(taskDetailHeaderDiv).not.toHaveClass("cursor-pointer");

    // hover:opacity-80クラスが設定されていないことを確認
    expect(taskDetailHeaderDiv).not.toHaveClass("hover:opacity-80");

    // ソートアイコン（img要素）が表示されていないことを確認
    const sortIcon = taskDetailHeaderDiv?.querySelector("img");
    expect(sortIcon).toBeNull();
  });

  /**
   * 試験観点：ソート可能列とソート不可列の区別確認
   * 試験対象：各列のソート機能の有効/無効状態
   * 試験手順：
   * 1. TasksTableをレンダリング
   * 2. 各列ヘッダーのソート機能状態を確認
   * 確認項目：
   * - タスク名、ステータス、開始日時、終了日時、サーバ名、タスク種別、実行ユーザー列はソート機能が有効であること
   * - タスク詳細列はソート機能が無効であること
   */
  it("機能テスト: ソート可能列とソート不可列が正しく区別されること", async () => {
    render(await TasksTable(mockProps));

    // ソート可能な列のヘッダーを確認
    const sortableHeaders = [
      "タスク名", "ステータス", "開始日時", "終了日時",
      "サーバ名", "タスク種別", "実行ユーザー"
    ];
    sortableHeaders.forEach(headerText => {
      const header = screen.getByText(headerText);
      const headerDiv = header.parentElement;

      // ソート機能が有効であることを確認
      expect(headerDiv).toHaveClass("cursor-pointer");
      expect(headerDiv).toHaveClass("hover:opacity-80");
    });

    // ソート不可な列のヘッダーを確認
    const nonSortableHeaders = ["タスク詳細"];
    nonSortableHeaders.forEach(headerText => {
      const header = screen.getByText(headerText);
      const headerDiv = header.parentElement;

      // ソート機能が無効であることを確認
      expect(headerDiv).not.toHaveClass("cursor-pointer");
      expect(headerDiv).not.toHaveClass("hover:opacity-80");
    });
  });
});
