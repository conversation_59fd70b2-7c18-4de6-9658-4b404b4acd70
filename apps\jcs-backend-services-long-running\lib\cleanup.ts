/**
 * @fileoverview タスク保持ポリシーに基づく古いタスクおよび関連ファイルの自動クリーンアップ処理モジュール
 * @description
 * タスク保持ポリシーに基づき、古いタスクおよび関連ファイルの自動クリーンアップ処理を提供する。
 * タスク保持件数制御・古い成果物の自動削除により、ストレージコスト・データベース肥大化を防止し、運用効率・コスト最適化を実現する。
 * サーバ単位でのクリーンアップを提供し、各Functionから共通利用できる設計である。
 * 主要な分岐・例外・catch時のログも日本語で詳細に記録する。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { InvocationContext } from "@azure/functions";
import { prisma } from "./prisma";
import { createBlobServiceClient } from "./azureClients";
import { AppConstants } from "./constants";

const ASSETS_CONTAINER = process.env.AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF!;
const OPLOGS_CONTAINER = process.env.AZURE_STORAGE_CONTAINER_OPLOGS!;

/**
 * タスク保持ポリシーに基づき、指定されたサーバーの古いタスク記録と関連ファイルをクリーンアップする（非ブロッキング）。
 * @param targetServerId クリーンアップ対象のサーバーID。
 * @param context Functionの呼び出しコンテキスト。
 */
export async function cleanupOldTasks(
  targetServerId: string,
  context: InvocationContext
) {
  try {
    // クリーンアップ処理開始をログ出力
    context.log(`[cleanup] クリーンアップ処理を開始する（サーバID: ${targetServerId}）`);

    // DBからタスクの最大保持件数を取得
    const retentionLov = await prisma.lov.findFirst({
      where: { code: AppConstants.TaskConfig.maxRetentionCount },
    });
    const maxRetentionCount = retentionLov
      ? parseInt(retentionLov.value, 10)
      : 10;
    context.log(`[cleanup] 最大保持件数は${maxRetentionCount}件である。`);

    // 現在のタスク総数を取得
    const totalTaskCount = await prisma.task.count({
      where: { targetServerId },
    });

    // 保持件数が上限内の場合、処理を終了
    if (totalTaskCount <= maxRetentionCount) {
      context.log(
        `[cleanup] タスク件数（${totalTaskCount}件）は上限内であるため、クリーンアップ不要。`
      );
      return;
    }

    context.log(
      `[cleanup] タスク件数（${totalTaskCount}件）が上限を超過しているため、削除対象タスクを特定する。`
    );

    // 削除対象となる古い完了済みタスクを特定
    const tasksToDeleteCount = totalTaskCount - maxRetentionCount;
    const tasksToDelete = await prisma.task.findMany({
      where: {
        targetServerId,
        status: {
          in: [
            AppConstants.TaskStatus.CompletedSuccess,
            AppConstants.TaskStatus.CompletedError,
            AppConstants.TaskStatus.Cancelled,
          ],
        },
      },
      orderBy: { submittedAt: "asc" },
      take: tasksToDeleteCount,
    });

    if (tasksToDelete.length === 0) {
      context.log("削除対象となる完了済みタスクは存在しない。");
      return;
    }

    context.log(`[cleanup] 削除対象タスクは${tasksToDelete.length}件である。`);

    // 各タスクをループして、関連データを削除
    for (const task of tasksToDelete) {
      /**
       * 個別タスクの削除処理（エラーが発生してもループは継続）
       * catch時も日本語で詳細ログを記録する。
       */
      try {
        context.log(`[cleanup] タスクID: ${task.id} の関連データ削除を開始する。`);

        // 1. 関連するDB操作ログを削除
        await prisma.operationLog.deleteMany({
          where: { generatedByTaskId: task.id },
        });

        // 2. タスクタイプに応じて、削除するBlobストレージのフォルダ（プレフィックス）を特定
        let folderPrefix: string | null = null;
        let containerNameToClean: string | null = null;

        if (task.licenseId) {
          if (task.taskType === AppConstants.TaskType.MgmtItemImport) {
            folderPrefix = `${task.licenseId}/${AppConstants.AZURE_BLOB_PATHS.IMPORTS_PREFIX}/${task.id}/`;
            containerNameToClean = ASSETS_CONTAINER;
          } else if (task.taskType === AppConstants.TaskType.MgmtItemExport) {
            folderPrefix = `${task.licenseId}/${AppConstants.AZURE_BLOB_PATHS.EXPORTS_PREFIX}/${task.id}/`;
            containerNameToClean = ASSETS_CONTAINER;
          } else if (task.taskType === AppConstants.TaskType.OpLogExport) {
            folderPrefix = `${task.licenseId}/${task.id}/`;
            containerNameToClean = OPLOGS_CONTAINER;
          }
        }

        // 3. Blobストレージ内の関連ファイルを全て削除
        if (folderPrefix && containerNameToClean) {
          context.log(
            `[cleanup] Blobストレージ（${containerNameToClean}）内の${folderPrefix}配下のファイルを削除する。`
          );

          const containerClient =
            createBlobServiceClient().getContainerClient(containerNameToClean);
          const deletePromises: Promise<any>[] = [];

          for await (const blob of containerClient.listBlobsFlat({
            prefix: folderPrefix,
          })) {
            context.log(
              `[cleanup]   - 削除対象Blob: ${blob.name} を削除キューに追加する。`
            );
            deletePromises.push(containerClient.deleteBlob(blob.name));
          }

          if (deletePromises.length > 0) {
            await Promise.all(deletePromises);
            context.log(
              `[cleanup] ${folderPrefix}配下のファイル削除が完了した。`
            );
          } else {
            context.log(
              `[cleanup] ${folderPrefix}配下に削除対象ファイルは存在しない。`
            );
          }
        } else {
          context.warn(
            `[cleanup] タスク種別（${task.taskType}）はBlobフォルダ削除不要、またはlicenseId未設定のためスキップする。`
          );
        }

        // 4. 最後にタスクレコード自体を削除
        await prisma.task.delete({ where: { id: task.id } });
        context.log(`[cleanup] タスクレコード（ID: ${task.id}）の削除が完了した。`);
      } catch (innerError) {
        // 個別タスクの削除失敗を詳細にログ出力し、処理を継続
        let errorMessage = "単一タスクのクリーンアップ中に予期せぬエラーが発生した。";
        if (innerError instanceof Error) {
          errorMessage = innerError.message;
        }
        context.error(
          `[cleanup] タスクID: ${task.id} の削除処理に失敗した。エラー内容: ${errorMessage}。処理を継続する。`
        );
      }
    }
  } catch (error) {
    // クリーンアップ処理全体で発生した致命的なエラーを詳細にログ出力
    let errorMessage = "";
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    context.error(
      `[cleanup] サーバID: ${targetServerId} のクリーンアップ処理中に致命的なエラーが発生した。エラー内容: ${errorMessage}`
    );
  }
}
