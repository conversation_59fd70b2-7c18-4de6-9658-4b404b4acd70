## 画面項目定義

### ヘッダ部情報

| # | 項目名 | 種別 | 桁数/バイト数 | 必須 | 文字種 | ソート順 | 論理DBからの入力<br/>論理テーブル名 | 初期表示 | フォーマット/備考 |
|:---|:---|:---|:---|:---:|:---:|:---:|:---|:---|:---|
| 1 | タスク一覧 | h1 | - | - | - | - | - | 「管理>タスク一覧」 | - |
| 2 | 更新 | button | - | - | - | - | - | 活性 | - |
| 3 | フィルターの入力 | input | - | - | - | - | - | ブランク | 最大は全角100文字を入力できる |
| 4 | フィルターの検索 | button | - | - | - | - | - | 活性 | - |
| 5 | フィルターのクリア | button | - | - | - | - | - | 活性 | - |
| 6 | 改ページのエリア | div | - | - | - | - | - | - | - |
| 7 | 前のページ | button | - | - | - | - | - | 活性 | - |
| 8 | 次のページ | button | - | - | - | - | - | 活性 | - |
| 9 | 指定のページ | button | - | - | - | - | - | 活性 | - |
| 10 | 行数/ページ: | label | - | - | - | - | - | 「行数/ページ:」 | - |
| 11 | 行数/ページの選択項目 | dropdown menu | - | - | - | - | - | 活性 | 選択肢は「10」「30」「50」固定 |

### タスク一覧

| # | 項目名 | 種別 | 桁数/バイト数 | 必須 | 文字種 | ソート順 | 論理DBからの入力<br/>論理テーブル名 | 初期表示 | フォーマット/備考 |
|:---|:---|:---|:---|:---:|:---:|:---:|:---|:---|:---|
| 1 | タスク一覧エリア | table | - | - | - | - | - | - | - |
| 2 | タスク名列 | label | - | - | - | - | - | 「タスク名」 | - |
| 3 | タスク名ソート | botton | - | - | - | - | - | 非表示 | - |
| 4 | タスク名 | label | - | - | - | - | タスク<br/>タスク名 | DBに従う | フォーマット：<br/>{ServerName}-{タスク種別}-{YYYYMMDDHHmmss}<br/>ServerName：対象サーバ名<br/>タスク種別：タスク種別の日本語名<br/>YYYYMMDDHHmmss：タスクが作成された日時（作成したユーザーのタイムゾーンによる日時） |
| 5 | ステータス列 | label | - | - | - | - | - | 「ステータス」 | - |
| 6 | ステータスソート | botton | - | - | - | - | - | 非表示 | - |
| 7 | ステータス | label | - | - | - | - | タスク<br/>ステータス | DBに従う | 値の一覧lovテーブルを参照して、表示する日本語名を取得する。詳細は「参照値一覧」シートの＜タスクステータス＞を参照。 |
| 8 | 開始日時列 | label | - | - | - | - | - | 「開始日時」 | - |
| 9 | 開始日時ソート | botton | - | - | - | - | - | 表示（降順のアイコン） | - |
| 10 | 開始日時 | label | - | - | - | - | タスク<br/>開始日時 | DBに従う | 開始日時を「YYYY/MM/DD hh:mm:ss」形式で表示する。<br/>ブラウザのタイムゾーンで表示する。 |
| 11 | 終了日時列 | label | - | - | - | - | - | 「終了日時」 | - |
| 12 | 終了日時ソート | botton | - | - | - | - | - | 非表示 | - |
| 13 | 終了日時 | label | - | - | - | - | タスク<br/>終了日時 | DBに従う | 終了日時を「YYYY/MM/DD hh:mm:ss」形式で表示する。<br/>ブラウザのタイムゾーンで表示する。 |
| 14 | サーバ名列 | label | - | - | - | - | - | 「サーバ名」 | - |
| 15 | サーバ名ソート | botton | - | - | - | - | - | 非表示 | - |
| 16 | サーバ名 | label | - | - | - | - | タスク<br/>サーバ名 | DBに従う | - |
| 17 | タスク種別列 | label | - | - | - | - | - | 「タスク種別」 | - |
| 18 | タスク種別ソート | botton | - | - | - | - | - | 非表示 | - |
| 19 | タスク種別 | label | - | - | - | - | タスク<br/>タスク種別 | DBに従う | 値の一覧lovテーブルを参照して、表示する日本語名を取得する。詳細は「参照値一覧」シートの＜タスク種別＞を参照。 |
| 20 | 実行ユーザー列 | label | - | - | - | - | - | 「実行ユーザー」 | - |
| 21 | 実行ユーザーソート | botton | - | - | - | - | - | 非表示 | - |
| 22 | 実行ユーザー | label | - | - | - | - | タスク<br/>実行ユーザー | DBに従う | - |
| 23 | タスク詳細列 | label | - | - | - | - | - | 「タスク詳細」 | - |
| 24 | タスク詳細 | label | - | - | - | - | タスク<br/>タスク詳細 | DBに従う | タスクのステータスによって以下を表示する。<br/>①実行待ち（QUEUED）の場合、中止ボタンを表示する。<br/>②実行中（RUNBOOK_SUBMITTED/RUNBOOK_PROCESSING）、中止待ち（PENDING_CANCELLATION）の場合、空欄と表示する。<br/>③中止（CANCELLED）の場合、EMET0004「ユーザーによってタスクが中止されました。」を表示する。<br/>④正常終了（COMPLETED_SUCCESS）かつ操作ログのエクスポートの場合、EMET0014「操作ログ一覧画面で操作ログファイルをダウロードしてください。ログ名は{タスク名}_{連番}です。」を表示する。<br/>⑤正常終了（COMPLETED_SUCCESS）かつ管理項目定義のインポートの場合、空欄と表示する。<br/>⑥正常終了（COMPLETED_SUCCESS）かつ管理項目定義のエクスポートの場合、リンクとなる「ダウンロード」を表示する。<br/>⑦エラー（COMPLETED_ERROR）の場合、リンクとなる「エラー詳細を表示」を表示する。 |
| 25 | スクロールバー | scroll | - | - | - | - | - | 表示条件に従う | ブラウザの表示範囲内でタスク一覧画面を全部表示できる場合はスクロールバーを非表示、全部表示できない場合はスクロールバーを表示する。 |

### 確認画面

| # | 項目名 | 種別 | 桁数/バイト数 | 必須 | 文字種 | ソート順 | 論理DBからの入力<br/>論理テーブル名 | 初期表示 | フォーマット/備考 |
|:---|:---|:---|:---|:---:|:---:|:---:|:---|:---|:---|
| 1 | タイトル | h1 | - | - | - | - | - | 「確認」 | - |
| 2 | × | button | - | - | - | - | - | 活性 | 中止要求メッセージを送信しないで、確認画面を閉じる |
| 3 | 画面記述 | label | - | - | - | - | - | 「タスクを中止してもよろしいですか？<br/>タスク名：{0}」 | {0}：対象タスク名 |
| 4 | OK | button | - | - | - | - | - | 活性 | 中止要求メッセージを送信して、確認画面を閉じる |
| 5 | キャンセル | button | - | - | - | - | - | 活性 | 中止要求メッセージを送信しないで、確認画面を閉じる |

### 受付完了画面

| # | 項目名 | 種別 | 桁数/バイト数 | 必須 | 文字種 | ソート順 | 論理DBからの入力<br/>論理テーブル名 | 初期表示 | フォーマット/備考 |
|:---|:---|:---|:---|:---:|:---:|:---:|:---|:---|:---|
| 1 | タイトル | h1 | - | - | - | - | - | 「情報」 | - |
| 2 | × | button | - | - | - | - | - | 活性 | 画面を閉じる |
| 3 | 画面記述 | label | - | - | - | - | - | 「タスクの中止を受け付けました。タスクのステータスはタスク一覧画面で確認してください。<br/>タスク名：{0}」 | {0}：対象タスク名 |
| 4 | 閉じる | button | - | - | - | - | - | 活性 | 画面を閉じる |

### エラー画面

| # | 項目名 | 種別 | 桁数/バイト数 | 必須 | 文字種 | ソート順 | 論理DBからの入力<br/>論理テーブル名 | 初期表示 | フォーマット/備考 |
|:---|:---|:---|:---|:---:|:---:|:---:|:---|:---|:---|
| 1 | タイトル | h1 | - | - | - | - | - | 「エラー」または「エラー詳細」 | - |
| 2 | × | button | - | - | - | - | - | 活性 | 画面を閉じる |
| 3 | 画面記述 | label | - | - | - | - | - | タスク中止要求受付処理からの応答 (TaskCancellationActionResult) の message フィールド | - |
| 4 | 閉じる | button | - | - | - | - | - | 活性 | 画面を閉じる |