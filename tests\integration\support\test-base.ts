import { test as base, expect, Page, BrowserContext } from '@playwright/test';
import { AuthHelper } from './auth.helper';
import { DatabaseHelper } from './database.helper';
import { ApiHelper } from './api.helper';
import { PerformanceHelper } from './performance.helper';

/**
 * 🚀 现代化测试基础设施 - 业界最佳实践
 * 
 * 特性：
 * - 统一的测试 Fixtures
 * - 自动化认证管理
 * - 数据库状态管理
 * - API 测试辅助
 * - 性能监控集成
 * - 错误处理和重试
 */

// 扩展测试上下文类型
type TestFixtures = {
  // 🔐 认证相关
  authHelper: AuthHelper;
  authenticatedPage: Page;
  adminPage: Page;
  
  // 🗄️ 数据库相关
  dbHelper: DatabaseHelper;
  cleanDatabase: void;
  
  // 🌐 API 相关
  apiHelper: ApiHelper;
  
  // 📊 性能监控
  performanceHelper: PerformanceHelper;
  
  // 🧪 测试工具
  testContext: TestContext;
};

type WorkerFixtures = {
  // Worker 级别的 fixtures（在所有测试间共享）
  workerStorageState: string;
};

interface TestContext {
  testName: string;
  startTime: number;
  metadata: Record<string, any>;
}

// 创建扩展的测试对象
export const test = base.extend<TestFixtures, WorkerFixtures>({
  // 🔐 认证辅助器
  authHelper: async ({ page }, use) => {
    const authHelper = new AuthHelper(page);
    await use(authHelper);
  },

  // 🔐 已认证的页面（普通用户）
  authenticatedPage: async ({ browser, workerStorageState }, use) => {
    const context = await browser.newContext({ 
      storageState: workerStorageState 
    });
    const page = await context.newPage();
    
    // 添加性能监控
    await page.addInitScript(() => {
      window.performance.mark('test-start');
    });
    
    await use(page);
    await context.close();
  },

  // 🔐 管理员页面
  adminPage: async ({ browser }, use) => {
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // 执行管理员登录
    const authHelper = new AuthHelper(page);
    await authHelper.loginAsAdmin();
    
    await use(page);
    await context.close();
  },

  // 🗄️ 数据库辅助器
  dbHelper: async ({}, use) => {
    const dbHelper = new DatabaseHelper();
    await dbHelper.connect();
    await use(dbHelper);
    await dbHelper.disconnect();
  },

  // 🗄️ 数据库清理
  cleanDatabase: async ({ dbHelper }, use) => {
    // 测试前清理
    await dbHelper.cleanTestData();
    await use();
    // 测试后清理
    await dbHelper.cleanTestData();
  },

  // 🌐 API 辅助器
  apiHelper: async ({ request }, use) => {
    const apiHelper = new ApiHelper(request);
    await use(apiHelper);
  },

  // 📊 性能监控辅助器
  performanceHelper: async ({ page }, use) => {
    const performanceHelper = new PerformanceHelper(page);
    await performanceHelper.startMonitoring();
    await use(performanceHelper);
    await performanceHelper.stopMonitoring();
  },

  // 🧪 测试上下文
  testContext: async ({}, use, testInfo) => {
    const context: TestContext = {
      testName: testInfo.title,
      startTime: Date.now(),
      metadata: {},
    };
    
    await use(context);
    
    // 测试结束后记录性能数据
    const duration = Date.now() - context.startTime;
    console.log(`📊 测试 "${context.testName}" 执行时间: ${duration}ms`);
  },

  // Worker 级别的存储状态
  workerStorageState: [async ({ browser }, use) => {
    // 在 worker 启动时执行一次登录
    const context = await browser.newContext();
    const page = await context.newPage();
    
    const authHelper = new AuthHelper(page);
    await authHelper.loginAsUser();
    
    // 保存认证状态
    const storageState = await context.storageState();
    await context.close();
    
    await use(JSON.stringify(storageState));
  }, { scope: 'worker' }],
});

// 导出 expect 以便统一使用
export { expect };

/**
 * 🏷️ 测试标签和分类
 */
export const TestTags = {
  // 功能分类
  AUTH: '@auth',
  DATABASE: '@database', 
  API: '@api',
  UI: '@ui',
  INTEGRATION: '@integration',
  
  // 优先级
  CRITICAL: '@critical',
  HIGH: '@high',
  MEDIUM: '@medium',
  LOW: '@low',
  
  // 环境
  SMOKE: '@smoke',
  REGRESSION: '@regression',
  PERFORMANCE: '@performance',
  
  // 浏览器
  DESKTOP: '@desktop',
  MOBILE: '@mobile',
  
  // 服务
  NEXTJS: '@nextjs',
  AZURE_FUNCTIONS: '@azure-functions',
} as const;

/**
 * 🎯 测试工具函数
 */
export class TestUtils {
  /**
   * 等待元素稳定（停止变化）
   */
  static async waitForStable(page: Page, selector: string, timeout = 5000) {
    const element = page.locator(selector);
    await element.waitFor({ state: 'visible', timeout });
    
    // 等待元素位置稳定
    let previousBox = await element.boundingBox();
    let stableCount = 0;
    
    while (stableCount < 3) {
      await page.waitForTimeout(100);
      const currentBox = await element.boundingBox();
      
      if (JSON.stringify(previousBox) === JSON.stringify(currentBox)) {
        stableCount++;
      } else {
        stableCount = 0;
        previousBox = currentBox;
      }
    }
  }

  /**
   * 安全点击（避免元素被遮挡）
   */
  static async safeClick(page: Page, selector: string) {
    const element = page.locator(selector);
    await element.waitFor({ state: 'visible' });
    await element.scrollIntoViewIfNeeded();
    await this.waitForStable(page, selector);
    await element.click();
  }

  /**
   * 等待网络空闲
   */
  static async waitForNetworkIdle(page: Page, timeout = 10000) {
    await page.waitForLoadState('networkidle', { timeout });
  }

  /**
   * 截图并附加到测试报告
   */
  static async attachScreenshot(page: Page, name: string) {
    const screenshot = await page.screenshot({ fullPage: true });
    await test.info().attach(name, {
      body: screenshot,
      contentType: 'image/png',
    });
  }
}
