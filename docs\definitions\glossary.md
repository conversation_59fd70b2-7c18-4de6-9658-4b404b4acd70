# 项目术语表 (Glossary)

本文档定义了“JCS 端点资产与任务管理系统”项目中使用的关键业务术语、技术缩写、特定组件名称及其简要说明。其目的是确保项目团队成员及AI编程助手对这些术语有统一的理解。

## 1. 业务术语 (Business Terms)

| 中文名称 | 英文名称 (若适用) | 日文参考 (若适用) | 说明 |
|------|------------|------------|----|
| 资产分发管理服务 | Asset Distribution Management Service | 資産配布管理サービス | 本项目所属的整体服务名称，旨在为用户提供IT资产（特别是端点VM）的管理和相关任务自动化能力。 |
| 用户 | User | ユーザー / 利用者 | 在本项目中特指“顾客系统管理者”，是门户系统的主要使用者。 |
| 系统管理者 | System Administrator | システム管理者 | 指“资产分发管理服务提供方”的管理员，负责部署、维护和管理整个门户系统及其后端基础设施。 |
| 端点 | Endpoint | エンドポイント | 指客户环境中被本系统管理的虚拟机 (VM) 或设备。 |
| 资产 | Asset | 資産 | 在本项目上下文中，通常指端点VM及其上运行的受管软件（如JP1/ITDM2实例、秘文控制台代理等）。 |
| 后台任务 | Background Task | バックグラウンドタスク / タスク | 由用户通过门户发起，在后端Azure PaaS服务（Functions, Automation等）上异步执行的操作，例如操作日志导出、管理项目定义导入/导出等。 |
| 服务器条目 | Server Entry | サーバ / サーバーエントリ | 在门户“服务器列表”中显示的代表一个可管理单元的记录，对应一个具体的受管应用实例（如运行在特定VM上的JP1/ITDM2 Manager Docker容器）。 |
| 操作日志 | Operation Log | 操作ログ | 受管应用（如JP1/ITDM2）自身记录的操作行为日志。本系统提供按需导出此类日志的功能。 |
| 管理项目定义 | Management Item Definition | 管理項目定義 | JP1/ITDM2等管理工具中用于配置管理策略、对象、分发包等的定义集合。本系统支持对此类定义的导入和导出。其文件名通常为 `assetsfield_def.csv`。 |
| 许可证/契约 | License / Contract | ライセンス / 契約 | 客户使用本服务的凭证，可能关联到特定的可管理资源范围、服务级别或环境启用状态。 |
| 顾客系统管理者 | Customer System Administrator | 顧客システム管理者 | 门户系统的主要用户，负责管理其组织内的端点资产和执行相关任务。 |
| 服务提供方 | Service Provider | サービス提供者 | 指提供并运维“JCS 端点资产与任务管理系统”的组织或团队。 |
| 任务名 | Task Name | タスク名 | 系统为每个后台任务自动生成的唯一标识名称，通常格式为 `{ServerName}-{タスク種別}-{YYYYMMDDHHmmss}`。 |
| 基本契约计划代码 | Basic Plan Code | 基本契約プランコード | 存储在 `License.basicPlan` 字段的字符串代码，如 `'STANDARD'`, `'LIGHT_A'`, `'LIGHT_B'`，用于确定许可证的基础服务级别和特定功能的可用性（例如“操作日志导出”权限）。其具体的合法值和业务含义在应用程序代码中定义和管理。 |

## 2. 项目核心技术与Azure服务术语 (Core Technologies and Azure Services)

| 英文名称 (官方全称优先) | 中文参考 | 说明 |
|---------------|------|----|
| Azure App Service | Azure 应用服务 | 用于托管Web应用程序（如本项目Next.js门户）的PaaS服务。 |
| Azure Functions | Azure Functions | Azure的无服务器计算服务，用于执行事件驱动的后端逻辑（如 `TaskExecuteFunc`, `RunbookProcessorFunc`, `RunbookMonitorFunc`等）。 |
| Azure Automation | Azure Automation | Azure的云自动化服务，用于创建、部署、编排和监视Runbook。 |
| Runbook | Runbook | 在Azure Automation中定义的一系列自动化操作脚本（本项目中主要为PowerShell），在Hybrid Runbook Worker上执行，负责具体的VM端业务操作。其执行结果由`RunbookMonitorFunc`轮询获取。 |
| Hybrid Runbook Worker (HRW) | 混合Runbook辅助角色 | 部署在端点VM上的代理，使其能够执行Azure Automation Runbook，访问本地资源（如Docker容器）。 |
| Hybrid Runbook Worker Group (HRW Group) | HRW组 | Azure Automation中HRW的逻辑分组。本项目中，每个目标VM配置为一个独立的HRW Group（通常建议命名与VM相关，例如`hrwg-<azureVmName>`），以便精确调度作业到特定VM。其名称记录在`Server`表的`hrwGroupName`字段。 |
| Azure Service Bus | Azure 服务总线 | 企业级消息代理服务，用于组件间的异步通信（例如，用于传递任务输入、任务状态、任务控制等消息的队列）。 |
| TaskInputQueue | 任务输入队列 | (Azure Service Bus Queue) 接收来自门户的新后台任务执行请求的消息队列，由`TaskExecuteFunc`消费。 |
| TaskControlQueue | 任务控制队列 | (Azure Service Bus Queue) 接收来自门户的任务取消请求的消息队列，由`TaskCancellationFunc`消费。 |
| RunbookStatusQueue | Runbook作业状态队列 | (Azure Service Bus Queue) 接收来自`RunbookMonitorFunc`的、关于Azure Automation Runbook原始执行结果的消息队列（消息中包含`Task.id`作为作业标识），由`RunbookProcessorFunc`消费。 |
| Dead-Letter Queue (DLQ) | 死信队列 | Azure Service Bus中用于存放因处理失败（如Function超时）而无法被正常消费的消息的队列。 |
| Azure SQL Database | Azure SQL 数据库 | Azure的关系型数据库即服务 (DBaaS)，用于存储项目核心业务数据（如任务信息、服务器配置、许可证数据、值列表等）。 |
| Azure Blob Storage | Azure Blob 存储 | Azure的对象存储服务，用于存储任务导出的最终文件（如日志压缩包、定义CSV）、产品媒体、手册，以及导入任务的临时上传文件等。 |
| Azure Files | Azure 文件存储 | Azure提供的完全托管的文件共享服务，本项目中用作Azure Automation Runbook执行时的主要临时文件共享工作区 (`TaskWorkspaces/{taskId}/`)，用于Runbook与云端Functions之间中继文件。 |
| Azure Monitor | Azure Monitor | Azure的统一监控服务，用于收集、分析和处理来自Azure及本地环境的遥测数据（日志、指标），支持告警和诊断。 |
| Azure Key Vault | Azure Key Vault | Azure的安全存储服务，用于管理和保护密钥、证书和连接字符串等敏感信息。应用程序通常通过托管身份访问。 |
| Managed Identity | 托管标识 | Microsoft Entra ID (原Azure AD) 中的一种功能，为Azure资源提供自动管理的身份，用于向支持Microsoft Entra ID身份验证的服务进行身份验证，无需管理凭据。 |
| Keycloak | Keycloak | 开源的身份和访问管理解决方案，本项目用作外部身份提供商 (IdP) 进行用户认证（支持MFA）。 |
| Microsoft Entra ID | Microsoft Entra ID (原Azure AD) | 微软的云身份和访问管理服务。在本项目的用户认证流程中，Keycloak是主要的IdP。 |
| Next.js | Next.js | 基于React的开源Web开发框架，本项目用于构建门户前端和部分后端API逻辑 (采用App Router架构)。 |
| React Server Components (RSC) | React 服务器组件 | Next.js App Router中的一种组件类型，在服务器端渲染，可以直接执行异步操作如数据获取。 |
| Server Actions | 服务器操作 (Next.js) | Next.js App Router中用于处理表单提交和数据变更的函数，在服务器端执行，可以被客户端组件直接调用，是门户后端与更深层次服务（如Service Bus）交互的主要入口之一。 |
| Prisma ORM | Prisma ORM | Node.js 和 TypeScript 的类型安全的对象关系映射器 (ORM)，用于与Azure SQL Database交互。数据库结构的权威定义通常在项目代码库的Prisma Schema文件中（例如，`prisma/schema.prisma`）。 |
| Monorepo | Monorepo | 单一代码仓库，一种软件开发策略，将多个逻辑上独立但相关的项目（如前端应用、后端服务、共享库、文档等）组织在同一个版本控制仓库中。 |

## 3. 文档与设计规范术语 (Documentation and Design Standards)

| 英文名称 (若适用) | 中文名称 | 说明 |
|------------|------|----|
| Single Source of Truth (SSoT) | 单一事实来源 | 指项目信息（特别是设计和规格）应有唯一的、权威的出处。在本项目的技术文档体系中，存储于本文档库（通常是`docs/`目录）下的核心Markdown文档是其主要构成部分。 |
| Functional Specification (FS) | 功能规格书 | 通常指《機能仕様書》的对应概念，是描述系统“应该做什么”（即功能性需求、业务规则、用户交互流程等）的文档。在本项目的文档体系中，这些内容主要体现在各组件详细设计文档的概要和功能规格部分。 |
| System Architecture Document (SAD) | 系统架构设计文档 | 描述系统整体高层架构、主要组件及其职责、组件间的关键交互、以及核心技术选型和设计原则的文档。 |
| Detailed Design Specification (DDS) | 详细设计书 | 通常指《詳細設計書》的对应概念，是详细描述系统或组件“如何实现”其功能规格的文档。在本项目的文档体系中，这部分内容主要体现在各组件详细设计文档的技术设计与实现细节部分，力求提供足够信息以指导代码实现。 |
| Markdown | Markdown | 轻量级标记语言，项目核心技术文档的主要编写格式。 |
| Mermaid | Mermaid | 基于文本的图表工具，用于在Markdown中嵌入流程图、序列图、状态图、架构图等，以可视化系统设计和流程。 |
| Architecture Decision Record (ADR) | 架构决策记录 | 用于记录项目过程中做出的重要架构决策及其背景、理由、权衡和后果的简短文档。通常存储于架构相关文档目录下。 |

## 4. 项目特定组件逻辑名 (Project Specific Component Logical Names - Azure Functions)
*(以下为项目中核心的、作为独立部署单元的Azure Function的逻辑名称及其核心职责。这些名称与项目的功能规格和架构设计保持一致。详细技术设计请参考各自的组件设计文档。)*

| 逻辑名 (与功能规格及架构设计一致) | 中文参考 | 说明 | 组件设计文档 |
|--------------------|------|----|--------|
| `TaskExecuteFunc` | 任务执行函数 | 监听任务输入队列，负责接收新任务请求，进行参数校验、并发控制、准备工作区，下载导入文件（若适用），并使用`Task.id`作为作业ID将Runbook作业提交到Azure Automation，更新任务状态为`RUNBOOK_SUBMITTED`。此外，还执行任务记录保留策略。 | [`设计文档`](../components/backend-services-functions/function-task-execute.md) |
| `RunbookMonitorFunc` | Runbook作业监控函数 | 定时触发，负责监控运行中（状态为`RUNBOOK_SUBMITTED`）的Azure Automation作业是否超时或出现异常，并轮询已结束作业的结果。将所有作业结束事件（包含`Task.id`及原始结果/错误）作为消息发送到`RunbookStatusQueue`，并更新任务的内部状态为`RUNBOOK_PROCESSING`。 | [`设计文档`](../components/backend-services-functions/function-runbook-monitor.md) |
| `RunbookProcessorFunc` | Runbook作业处理函数 | 监听`RunbookStatusQueue`，负责处理Runbook的原始执行结果，进行文件归档、工作区清理、并发锁释放、最终任务状态更新（`COMPLETED_SUCCESS`/`COMPLETED_ERROR`），并在需要时调用API停止作业。 | [`设计文档`](../components/backend-services-functions/function-runbook-processor.md) |
| `TaskCancellationFunc` | 任务取消函数 | 监听任务控制队列，处理用户发起的任务取消请求，主要对处于`PENDING_CANCELLATION`状态的任务更新为`CANCELLED`，或对已在执行的任务更新备注。 | [`设计文档`](../components/backend-services-functions/function-task-cancellation.md) |
| `TaskExecuteTimeoutFunc` | 任务执行超时处理函数 | 监听任务输入队列的死信队列 (DLQ)，处理因`TaskExecuteFunc`执行超时导致的消息，尝试进行补偿操作（如停止Runbook、清理资源、释放并发锁）并将任务标记为错误。 | [`设计文档`](../components/backend-services-functions/function-task-execute-timeout.md) |
| `TaskCancellationTimeoutFunc` | 任务取消超时处理函数 | 监听任务控制队列的DLQ，处理因`TaskCancellationFunc`执行超时导致的消息，主要记录错误并更新任务备注（或特定情况下更新状态为`COMPLETED_ERROR`）。 | [`设计文档`](../components/backend-services-functions/function-task-cancellation-timeout.md) |
| `RunbookProcessorTimeoutFunc` | Runbook作业处理超时函数 | 监听`RunbookStatusQueue`的DLQ，处理因`RunbookProcessorFunc`执行超时导致的消息，尝试进行资源回收（如清理工作区和释放并发锁）并将任务标记为错误。 | [`设计文档`](../components/backend-services-functions/function-runbook-processor-timeout.md) |

## 5. 核心 `LOV` 表父级代码 (Parent Codes in `Lov` Table) 说明
*(以下列出`Lov`表中主要的`parentCode`及其用途。这些`parentCode`用于组织和分类值列表。各`parentCode`下具体的`code` (通常为程序内部使用的标识符), 日文名称 (`name`, 通常用于UI显示), 和值 (`value`, 存储具体配置或与`name`相同) 的详细定义，请参考权威的 [`LOV值列表定义`](../definitions/lov-definitions.md)。)*

| `parentCode` 值 (来自 `Lov` 表) | 中文含义 | 说明 |
|-----------------------------|------|----|
| `SERVER_TYPE` | 服务器类型 | 定义服务器的种类（如 JP1/ITDM2统括マネージャ, JP1/ITDM2中継マネージャ, 秘文管理コンソール）及其日文显示名称。 |
| `LICENSE_TYPE` | 许可证类型 | 定义许可证的种类（如 製品版, 評価版）及其日文显示名称。 |
| `OS_TYPE` | 操作系统类型 | 定义操作系统类型（如 Windows, Linux）及其日文显示名称，主要用于产品媒体等信息的分类。 |
| `LOCKOUT` | 账户锁定配置 | 定义用户账户登录失败时的锁定策略参数，如最大尝试次数和锁定时间。 |
| `SUPPORT_IMPORTANCE` | 支持信息重要度 | 定义支持信息的重要级别（如 AAA, AA, A, B, C, -）及其日文描述。 |
| `AZURE_STORAGE` | Azure存储相关应用配置 | (此`parentCode`下主要存放与Azure Blob Storage和Azure Files交互相关的、非连接字符串或密钥本身的应用层面可配置参数) 例如，SAS Token的默认有效时长。具体的容器名称通过环境变量配置。 |
| `OPERATION_LOG_CONFIG` | 操作日志相关配置 | 定义操作日志导出功能的特定参数，例如最大可导出天数跨度，**并通过其子配置项定义允许执行此功能的许可证基本契约套餐代码列表**。 |
| `TASK_CONFIG` | 后台任务相关配置 | 定义后台任务的通用配置，如每个服务器的任务记录最大保留数量。 |
| `TASK_TYPE` | 后台任务类型 | 定义系统中支持的各种后台任务的唯一代码及其日文名称（例如，操作日志导出、管理项目定义导入等）。 |
| `TASK_STATUS` | 后台任务状态 | 定义后台任务在其生命周期中所处的各种**内部状态码**（程序使用）及其对应的用户界面**外部显示日文名称**。 |