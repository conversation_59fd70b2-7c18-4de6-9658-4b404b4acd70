[{"code": "SERVER_TYPE", "name": "サーバータイプ", "value": ""}, {"code": "SERVER_TYPE.GENERAL_MANAGER", "name": "統括マネージャ", "value": "JP1/ITDM2(統括マネージャ)", "parentCode": "SERVER_TYPE"}, {"code": "SERVER_TYPE.RELAY_MANAGER", "name": "中継マネージャ", "value": "JP1/ITDM2(中継マネージャ)", "parentCode": "SERVER_TYPE"}, {"code": "SERVER_TYPE.HIBUN_CONSOLE", "name": "管理コンソー", "value": "秘文(管理コンソール)", "parentCode": "SERVER_TYPE"}, {"code": "LICENSE_TYPE", "name": "ライセンスタイプ", "value": ""}, {"code": "LICENSE_TYPE.PROD", "name": "製品版表示名", "value": "製品版", "parentCode": "LICENSE_TYPE"}, {"code": "LICENSE_TYPE.TRIAL", "name": "評価版表示名", "value": "評価版", "parentCode": "LICENSE_TYPE"}, {"code": "OS_TYPE", "name": "OSタイプ", "value": ""}, {"code": "OS_TYPE.WIN", "name": "Windows表示名", "value": "Windows", "parentCode": "OS_TYPE"}, {"code": "OS_TYPE.LINUX", "name": "Linux表示名", "value": "Linux", "parentCode": "OS_TYPE"}, {"code": "OS_TYPE.AIX", "name": "AIX表示名", "value": "AIX", "parentCode": "OS_TYPE"}, {"code": "OS_TYPE.SOLARIS", "name": "Solaris表示名", "value": "Solar<PERSON>", "parentCode": "OS_TYPE"}, {"code": "OS_TYPE.HPUX", "name": "HP-UX表示名", "value": "HP-UX", "parentCode": "OS_TYPE"}, {"code": "OS_TYPE.MACOS", "name": "Mac表示名", "value": "<PERSON>", "parentCode": "OS_TYPE"}, {"code": "SUPPORT_IMPORTANCE", "name": "サポート情報の重要度", "value": ""}, {"code": "SUPPORT_IMPORTANCE.NONE", "name": "重要度なし(予防保守情報や使用時の注意事項など)", "value": "-", "parentCode": "SUPPORT_IMPORTANCE"}, {"code": "SUPPORT_IMPORTANCE.AAA", "name": "業務システムの運用が停止し、発生頻度が高い", "value": "AAA", "parentCode": "SUPPORT_IMPORTANCE"}, {"code": "SUPPORT_IMPORTANCE.AA", "name": "業務システムの運用が停止する可能性がある", "value": "AA", "parentCode": "SUPPORT_IMPORTANCE"}, {"code": "SUPPORT_IMPORTANCE.A", "name": "業務システムの運用が停止する可能性は低い", "value": "A", "parentCode": "SUPPORT_IMPORTANCE"}, {"code": "SUPPORT_IMPORTANCE.B", "name": "業務システムの運用に与える影響が少ない", "value": "B", "parentCode": "SUPPORT_IMPORTANCE"}, {"code": "SUPPORT_IMPORTANCE.C", "name": "業務システムの運用に与える影響は殆ど無い", "value": "C", "parentCode": "SUPPORT_IMPORTANCE"}, {"code": "LOCKOUT", "name": "ロックアウト設定", "value": ""}, {"code": "LOCKOUT.MAX_FAILED_TIMES", "name": "ロックアウト失敗最大回数", "value": "10", "parentCode": "LOCKOUT"}, {"code": "LOCKOUT.TIME_SECONDS", "name": "ロックアウト時間", "value": "1800", "parentCode": "LOCKOUT"}, {"code": "AZURE_STORAGE", "name": "Azure Blob 設定", "value": ""}, {"code": "AZURE_STORAGE.CONTAINER_OPLOGS", "name": "操作ログコンテナ名", "value": "oplogs", "parentCode": "AZURE_STORAGE"}, {"code": "AZURE_STORAGE.CONTAINER_PRODUCT_MEDIAS", "name": "製品媒体コンテナ名", "value": "product-medias", "parentCode": "AZURE_STORAGE"}, {"code": "AZURE_STORAGE.CONTAINER_PRODUCT_MANUALS", "name": "マニュアルコンテナ名", "value": "product-manuals", "parentCode": "AZURE_STORAGE"}, {"code": "AZURE_STORAGE.CONTAINER_PROVIDED_FILES", "name": "提供ファイルコンテナ名", "value": "provided-files", "parentCode": "AZURE_STORAGE"}, {"code": "AZURE_STORAGE.CONTAINER_SUPPORT_FILES", "name": "サポートファイルコンテナ名", "value": "support-files", "parentCode": "AZURE_STORAGE"}, {"code": "AZURE_STORAGE.SAS_TTL_SECONDS", "name": "SAS有効期限", "value": "7200", "parentCode": "AZURE_STORAGE"}, {"code": "AZURE_STORAGE.CONTAINER_ASSETSFIELD_DEF", "name": "管理項目定義コンテナ名", "value": "assetsfield-def", "parentCode": "AZURE_STORAGE"}, {"code": "OPERATION_LOG_CONFIG", "name": "操作ログ関連設定", "value": ""}, {"code": "OPERATION_LOG_CONFIG.ALLOWED_PLANS", "name": "操作ログ出力許可プランリスト", "value": "", "parentCode": "OPERATION_LOG_CONFIG"}, {"code": "OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN", "name": "操作ログのエクスポート期間最大日数", "value": "30", "parentCode": "OPERATION_LOG_CONFIG"}, {"code": "OPERATION_LOG_CONFIG.ALLOWED_PLANS.OPLOG_ALLOW_LIGHT_B_PLAN", "name": "操作ログ出力許可: LIGHT_Bプラン", "value": "LIGHT_B", "parentCode": "OPERATION_LOG_CONFIG.ALLOWED_PLANS"}, {"code": "OPERATION_LOG_CONFIG.ALLOWED_PLANS.OPLOG_ALLOW_STANDARD_PLAN", "name": "操作ログ出力許可: STANDARDプラン", "value": "STANDARD", "parentCode": "OPERATION_LOG_CONFIG.ALLOWED_PLANS"}, {"code": "TASK_CONFIG", "name": "タスク関連設定", "value": ""}, {"code": "TASK_CONFIG.MAX_RETENTION_COUNT", "name": "サーバ単位のタスク保有上限件数", "value": "10", "parentCode": "TASK_CONFIG"}, {"code": "TASK_STATUS", "name": "タスクステータス", "value": ""}, {"code": "TASK_STATUS.CANCELLED", "name": "中止", "value": "中止", "parentCode": "TASK_STATUS"}, {"code": "TASK_STATUS.COMPLETED_ERROR", "name": "エラー", "value": "エラー", "parentCode": "TASK_STATUS"}, {"code": "TASK_STATUS.COMPLETED_SUCCESS", "name": "正常終了", "value": "正常終了", "parentCode": "TASK_STATUS"}, {"code": "TASK_STATUS.PENDING_CANCELLATION", "name": "中止待ち", "value": "中止待ち", "parentCode": "TASK_STATUS"}, {"code": "TASK_STATUS.QUEUED", "name": "実行待ち", "value": "実行待ち", "parentCode": "TASK_STATUS"}, {"code": "TASK_STATUS.RUNBOOK_PROCESSING", "name": "Runbookジョブ処理中", "value": "実行中", "parentCode": "TASK_STATUS"}, {"code": "TASK_STATUS.RUNBOOK_SUBMITTED", "name": "Runbookジョブ作成完了", "value": "実行中", "parentCode": "TASK_STATUS"}, {"code": "TASK_TYPE", "name": "タスク種別", "value": ""}, {"code": "TASK_TYPE.MGMT_ITEM_EXPORT", "name": "管理項目定義のエクスポート", "value": "管理項目定義のエクスポート", "parentCode": "TASK_TYPE"}, {"code": "TASK_TYPE.MGMT_ITEM_IMPORT", "name": "管理項目定義のインポート", "value": "管理項目定義のインポート", "parentCode": "TASK_TYPE"}, {"code": "TASK_TYPE.OPLOG_EXPORT", "name": "操作ログのエクスポート", "value": "操作ログのエクスポート", "parentCode": "TASK_TYPE"}]