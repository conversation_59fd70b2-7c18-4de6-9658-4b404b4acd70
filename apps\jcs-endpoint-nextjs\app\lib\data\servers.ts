/**
 * @fileoverview サーバー関連データ操作モジュールである。
 * @description サーバー一覧のキャッシュ取得、ページ数計算、フィルタ・ソート・ページング、タスク作成用サーバー詳細取得、操作ログエクスポート権限判定等、サーバー管理に必要なデータアクセスロジックを集約する。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import prisma from "@/app/lib/prisma";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { revalidateTag, unstable_cache } from "next/cache";
import { cookies } from "next/headers";
import {
  ENV,
  SERVER_TYPE,
  PORTAL_CACHE_KEY_SERVERS
} from "../definitions";
import { LogFunctionSignature } from "../logger";
import { handleServerError } from "../portal-error";
import { castValueToLabel } from "../utils";
import { ServerDataLov } from "./lov";

/**
 * サーバー関連のデータ操作を行う静的クラスである。
 * 本クラスはサーバー一覧のキャッシュ取得、ページ数計算、フィルタ・ソート・ページング処理等を担当する。
 */
export class ServerDataServers {
  /**
   * 指定されたライセンスIDに紐づくサーバー一覧をキャッシュ付きで取得する。
   * サーバータイプはLOVからラベル変換され、typeCode（元のtypeコード値）も返す。
   * キャッシュキーとタグにライセンスIDを含めることで、正確なキャッシュ無効化を実現する。
   *
   * @param {string} licenseId - ライセンスID。
   * @returns {Promise<any[]>} サーバー情報配列（typeはラベル、typeCodeはコード）。
   */
  static async fetchCachedServers(licenseId: string) {
    const cachedFn = unstable_cache(
      async () => {
        const servers = await prisma.server.findMany({
          where: { licenseId },
        });
        const serverTypes = await prisma.lov.findMany({
          where: { parentCode: SERVER_TYPE },
        });
        return servers.map((server) => ({
          ...server,
          type: castValueToLabel(server.type, serverTypes) || "",
          typeCode: server.type,
        }));
      },
      [`${PORTAL_CACHE_KEY_SERVERS}-${licenseId}`],
      {
        revalidate: ENV.APP_CACHE_TTL_SECONDS,
        tags: [`${PORTAL_CACHE_KEY_SERVERS}-${licenseId}`],
      },
    );
    return await cachedFn();
  }

  /**
   * サーバー一覧のページ数を取得する。
   *
   * @param {string} filter - フィルタ文字列。
   * @param {number} size - 1ページあたりの件数。
   * @param {boolean} refresh - キャッシュ強制リフレッシュ有無。
   * @returns {Promise<number>} ページ数。
   * @throws DBアクセス・セッション取得等で失敗した場合はhandleServerErrorに委譲される。
   */
  @LogFunctionSignature()
  static async fetchServersPages(filter: string, size: number, refresh: boolean) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);
      if (refresh) {
        revalidateTag(`${PORTAL_CACHE_KEY_SERVERS}-${session!.user.licenseId}`);
      }
      const cachedServers = await this.fetchCachedServers(session!.user.licenseId);
      if (cachedServers) {
        if (filter) {
          const filteredServers = cachedServers.filter(
            (server) =>
              server.name.toLowerCase().includes(filter.toLowerCase()) ||
              server.url.toLowerCase().includes(filter.toLowerCase()) ||
              server.type.toLowerCase().includes(filter.toLowerCase()),
          );
          return Math.ceil(Number(filteredServers.length) / size);
        } else {
          return Math.ceil(Number(cachedServers.length) / size);
        }
      } else {
        return 0;
      }
    } catch (error) {
      handleServerError(error);
    }
  }

  /**
   * フィルタ・ソート・ページング済みのサーバー一覧を取得する。
   *
   * @param {string} filter - フィルタ文字列。
   * @param {number} size - 1ページあたりの件数。
   * @param {number} page - ページ番号。
   * @param {"name"|"type"|"url"} sort - ソートキー。
   * @param {"asc"|"desc"} order - ソート順。
   * @returns {Promise<any[]>} サーバー情報配列。
   * @throws DBアクセス・セッション取得等で失敗した場合はhandleServerErrorに委譲される。
   */
  @LogFunctionSignature()
  static async fetchFilteredServers(
    filter: string,
    size: number,
    page: number,
    sort: "name" | "type" | "url",
    order: "asc" | "desc",
  ) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);
      const cachedServers = await this.fetchCachedServers(session!.user.licenseId);
      if (cachedServers) {
        let filteredServers = cachedServers;
        if (filter) {
          filteredServers = cachedServers.filter(
            (server) =>
              server.name.toLowerCase().includes(filter.toLowerCase()) ||
              server.url.toLowerCase().includes(filter.toLowerCase()) ||
              server.type.toLowerCase().includes(filter.toLowerCase()),
          );
        }
        if (sort) {
          filteredServers.sort((a, b) => {
            const aValue = a[sort].toLowerCase();
            const bValue = b[sort].toLowerCase();
            if (order === "asc") {
              return aValue.localeCompare(bValue);
            } else {
              return bValue.localeCompare(aValue);
            }
          });
        }
        const startIndex = (page - 1) * size;
        const endIndex = startIndex + size;
        const paginatedServers = filteredServers.slice(startIndex, endIndex);
        return paginatedServers;
      } else {
        return [];
      }
    } catch (error) {
      handleServerError(error);
    }
  }

  /**
   * タスク作成に必要なサーバー詳細情報を取得する。
   *
   * @param {string} serverId - サーバーID。
   * @returns {Promise<Object|null>} サーバー詳細情報、見つからない場合はnull。
   * @throws DBアクセスで失敗した場合はhandleServerErrorに委譲される。
   */
  @LogFunctionSignature()
  static async getServerDetailsForTask(serverId: string) {
    try {
      const serverDetails = await prisma.server.findUnique({
        where: { id: serverId },
        select: {
          name: true,
          licenseId: true,
          azureVmName: true,
          dockerContainerName: true,
          hrwGroupName: true,
        },
      });
      return serverDetails;
    } catch (error) {
      handleServerError(error);
    }
  }
}

/**
 * 現在のユーザーが操作ログエクスポート機能を利用できるかどうかを判定する。
 * セッションから取得したライセンスIDに紐づくbasicPlanコードが、
 * LOVテーブルのOPERATION_LOG_CONFIG.ALLOWED_PLANSに登録されているかを確認する。
 * これにより、ユーザーの契約内容に基づく機能制御を実現する。
 *
 * @returns {Promise<boolean>} エクスポートが許可されていればtrue、そうでなければfalse。
 * @throws データベースアクセスやセッション取得に失敗した場合は例外をthrowする。
 */
export async function fetchUserCanExportOplog(): Promise<boolean> {
  const session = await getIronSession<SessionData>(cookies(), sessionOptions);
  const license = await prisma.license.findUnique({
    where: { licenseId: session.user.licenseId },
    select: { basicPlan: true },
  });
  const userPlan = license?.basicPlan || "";
  if (!userPlan) return false;
  const entry = await prisma.lov.findFirst({
    where: {
      parentCode: "OPERATION_LOG_CONFIG.ALLOWED_PLANS",
      value: userPlan,
      isEnabled: true,
    },
  });
  return !!entry;
} 