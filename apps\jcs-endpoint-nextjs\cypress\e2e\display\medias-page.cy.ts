import planFixture from "../../fixtures/PlanProduct.json";
import mediasFixture from "../../fixtures/ProductMedia.json";

describe("初期化表示のテスト", () => {
  describe("製品媒体一覧画面", () => {
    const validCredentials = {
      userId: "hitachi.taro.aa",
      password: "changeit!@#",
    };
    const scrollBarWidth = 15;

    beforeEach(() => {
      cy.visit("/login");
      cy.get("#userId").type(validCredentials.userId);
      cy.get("#password").type(validCredentials.password);
      cy.get("button").click();
      cy.wait(2000);
      cy.visit("/dashboard/medias");
    });

    it("タイトルが正しく表示される", () => {
      cy.title().should("eq", "製品媒体一覧");
      cy.get("nav").should("contain", "ファイル");
      cy.get("nav").should("contain", "製品媒体一覧");
      cy.get("aside .from-blue-600").should("contain", "製品媒体一覧");
    });

    it("アクションバーが正しく表示される", () => {
      cy.get(".bg-white.h-full input").should(
        "have.attr",
        "placeholder",
        "フィルター",
      );
      cy.get(".bg-white.h-full button span").should("contain", "search");
      cy.get(".bg-white.h-full button span").should("contain", "clear");
      cy.get("#page-left").should("have.prop", "tagName", "DIV");
      cy.get("#page-right").should("have.prop", "tagName", "A");
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .first()
        .should("have.prop", "tagName", "DIV");
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .first()
        .nextAll()
        .should("have.prop", "tagName", "A");
      cy.get(".bg-white.h-full label").should("contain", "行数/ページ:");
      cy.get(".bg-white.h-full select option:selected").then(
        (selectedOption) => {
          cy.wrap(selectedOption).invoke("text").should("include", "10");
          cy.wrap(selectedOption).invoke("val").should("eq", "10");
        },
      );
    });

    it("ページ数が正しく表示される", () => {
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .first()
        .should("have.prop", "tagName", "DIV")
        .invoke("text")
        .should("eq", "1");
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .first()
        .next()
        .should("have.prop", "tagName", "A")
        .invoke("text")
        .should("eq", "2");
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .first()
        .next()
        .next()
        .should("have.prop", "tagName", "A")
        .invoke("text")
        .should("eq", "3");
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .first()
        .next()
        .next()
        .next()
        .should("have.prop", "tagName", "A")
        .invoke("text")
        .should("eq", "4");
      const maxPage = Math.ceil(
        planFixture.PlanProduct.filter((m) =>
          ["standard", "lighta"].includes(m.planId),
        ).length / 10,
      );
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .last()
        .should("have.prop", "tagName", "A")
        .invoke("text")
        .should("eq", `${maxPage}`);
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .last()
        .prev()
        .should("have.prop", "tagName", "A")
        .invoke("text")
        .should("eq", `${maxPage - 1}`);
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .last()
        .prev()
        .prev()
        .should("have.prop", "tagName", "A")
        .invoke("text")
        .should("eq", "4");
    });

    it("テーブルヘーダが正しく表示される", () => {
      cy.get("thead th span").should("contain", "製品名");
      cy.get("thead th span").should("contain", "製品形名");
      cy.get("thead th span").should("contain", "バージョン");
      cy.get("thead th span").should("contain", "OS");
      cy.get("thead th span")
        .should("contain", "リリース日")
        .next("img")
        .should("exist");
      cy.get("thead th span")
        .should("contain", "リリース日")
        .next("img")
        .should("not.have.class", "rotate-180");
      cy.get("thead th span").should("contain", "製品媒体");
      cy.get("thead th span").should("contain", "製品媒体のサイズ");
      cy.get("thead th span").should("contain", "ドキュメント");
      cy.get("thead th span").should("contain", "ドキュメントのサイズ");
    });

    it("テーブルボディが正しく表示される", () => {
      cy.get("table")
        .find(
          "tbody th, tbody td:nth-child(2), tbody td:nth-child(3), tbody td:nth-child(4), tbody td:nth-child(5), tbody td:nth-child(6), tbody td:nth-child(8)",
        )
        .each((element) => {
          cy.wrap(element).should("have.css", "textAlign", "left");
        });
      cy.get("table")
        .find("tbody td:nth-child(7), tbody td:nth-child(9)")
        .each((element) => {
          cy.wrap(element).should("have.css", "textAlign", "right");
        });
      cy.get("table")
        .find("tbody td:nth-child(6) ,tbody td:nth-child(8)")
        .find("a")
        .should("exist")
        .should("have.attr", "target", "_blank");
      cy.get("table tbody tr").each(($row) => {
        const keyColumn = $row.find("td:nth-child(2)").text();
        const rowData = mediasFixture.ProductMedia.find(
          (s) => s.productCode === keyColumn,
        );

        // @ts-ignore
        const hrefValue = $row.find("td:nth-child(6) a").attr("href");
        expect(hrefValue).to.eq(
          `medias/${rowData?.productCode}/${rowData?.version}/${rowData?.mediaName}`,
        );
        const href2Value = $row.find("td:nth-child(8) a").attr("href");
        expect(href2Value).to.eq(
          `medias/${rowData?.productCode}/${rowData?.version}/${rowData?.documentName}`,
        );
      });
    });

    it("リリース日列が降順で、製品名列が昇順で並んでいる", () => {
      cy.get("table tbody tr").then(($rows) => {
        const columnData = $rows
          .map((_, row) => {
            // @ts-ignore
            const releaseDate = row.querySelector("td:nth-child(5)").innerText;
            // @ts-ignore
            const name = row.querySelector("th").innerText;
            return { releaseDate, name };
          })
          .get();

        const sortedColumnData = [...columnData].sort((a, b) => {
          const releaseDateComparison = b.releaseDate.localeCompare(
            a.releaseDate,
          );
          if (releaseDateComparison !== 0) {
            return releaseDateComparison;
          }
          return a.name.localeCompare(b.name);
        });

        expect(columnData).to.deep.equal(sortedColumnData);
      });
    });

    it("テーブルには横方向のスクロールバーがある", () => {
      cy.get(".rounded-b-lg").within(() => {
        cy.get("table").then(($child) => {
          const parentClientWidth = $child.parent().width() || 0;
          const parentClientHeight = $child.parent().height() || 0;

          const childScrollWidth = $child[0].scrollWidth;
          const childScrollHeight = $child[0].scrollHeight;

          expect(childScrollWidth).to.be.greaterThan(parentClientWidth);
          expect(childScrollHeight + scrollBarWidth).to.be.equal(
            parentClientHeight,
          );
        });
      });
    });
  });
});
