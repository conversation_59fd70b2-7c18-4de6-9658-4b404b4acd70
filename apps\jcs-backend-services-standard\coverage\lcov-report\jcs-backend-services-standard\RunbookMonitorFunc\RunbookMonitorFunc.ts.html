
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for jcs-backend-services-standard/RunbookMonitorFunc/RunbookMonitorFunc.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">jcs-backend-services-standard/RunbookMonitorFunc</a> RunbookMonitorFunc.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/88</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/26</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/87</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * @fileoverview Runbookジョブ監視関数 (RunbookMonitorFunc)
 * @description
 * Azure Functionのタイマートリガーによって定期的に実行され、RUNBOOK_SUBMITTEDステータスの
 * タスクに対応するAzure Automation Runbookジョブの実行状況を監視する関数。
 * ジョブの完了・失敗・タイムアウトを検出し、後続処理のためにRunbookStatusQueueへメッセージを送信する。
 *
 * @trigger Azure Functions タイマートリガー（環境変数 RUNBOOK_MONITOR_INTERVAL_SECONDS で設定）
 * @input データベース (Taskテーブル): status = RUNBOOK_SUBMITTED のすべてのタスクレコード
 * @output Azure Service Bus (RunbookStatusQueue) へのメッセージ送信、Taskテーブルのレコード更新
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */
&nbsp;
<span class="cstat-no" title="statement not covered" >import { app, InvocationContext, Timer } from "@azure/functions";</span>
<span class="cstat-no" title="statement not covered" >import { prisma } from "../lib/prisma";</span>
<span class="cstat-no" title="statement not covered" >import { AppConstants } from "../lib/constants";</span>
<span class="cstat-no" title="statement not covered" >import { createServiceBusClient, getAutomationJobStatus } from "../lib/azureClients";</span>
&nbsp;
// 環境変数の取得と初期化
const monitorInterval = <span class="cstat-no" title="statement not covered" >parseInt(process.env.RUNBOOK_MONITOR_INTERVAL_SECONDS || "30", 10);</span>
const runbookTimeout = <span class="cstat-no" title="statement not covered" >parseInt(process.env.RUNBOOK_TIMEOUT_SECONDS || String(5 * 3600), 10);</span> // デフォルト5時間
const automationAccount = <span class="cstat-no" title="statement not covered" >process.env.AZURE_AUTOMATION_ACCOUNT_NAME!;</span>
const subscriptionId = <span class="cstat-no" title="statement not covered" >process.env.SUBSCRIPTION_ID!;</span>
const resourceGroupName = <span class="cstat-no" title="statement not covered" >process.env.RESOURCE_GROUP_NAME!;</span>
const runbookStatusQueueName = <span class="cstat-no" title="statement not covered" >process.env.SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME!;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" ><span class="missing-if-branch" title="if path not taken" >I</span>if (!automationAccount || !subscriptionId || !resourceGroupName || !runbookStatusQueueName) {</span>
<span class="cstat-no" title="statement not covered" >  throw new Error("RunbookMonitorFuncに必要な環境変数が不足しています");</span>
}
&nbsp;
// Service Bus クライアント（関数実行時に作成）
let serviceBusClient: any = <span class="cstat-no" title="statement not covered" >null;</span>
let sender: any = <span class="cstat-no" title="statement not covered" >null;</span>
&nbsp;
// 終了・実行中ステータス定義
const END_STATUSES = <span class="cstat-no" title="statement not covered" >[</span>
  "Completed", "Failed", "Removing", "Resuming", "Stopped", "Stopping", "Suspended", "Suspending"
];
const ACTIVE_STATUSES = <span class="cstat-no" title="statement not covered" >[</span>
  "New", "Activating", "Running", "Blocked", "Disconnected"
];
&nbsp;
/**
 * RunbookMonitorFunc - Runbookジョブ監視及び結果ポーリング関数
 *
 * 処理ステップ:
 * 1. Runbookジョブ監視関数がタイマートリガーにより起動される。処理開始のログを記録する。
 * 2. データベースの Task テーブルから、status が RUNBOOK_SUBMITTED である全てのタスクレコードを取得する。
 * 3. 取得した各タスクレコードに対して、4.-9.の処理をループで行う。各タスクの処理は独立している。
 * 4. タイムアウトチェック: startedAtから現在の日時までの時間を計算し、RUNBOOK_TIMEOUT_SECONDSと比較
 * 5. (タイムアウトしていない場合)ジョブステータスポーリング: Azure Automationのジョブ取得APIを呼び出し
 * 6. (タイムアウトしていない場合)取得したジョブステータスproperties.statusを判定
 * 7. Taskテーブルから当該タスクのステータスをRUNBOOK_PROCESSINGに更新（楽観ロック制御）
 * 8. Runbookジョブのステータスを通知するメッセージを構築し、Service Bus (RunbookStatusQueue) へ送信
 * 9. 次のタスクレコードの判定に入る
 * 10. RUNBOOK_SUBMITTEDのタスクレコードの判定が全部終わったら、処理成功のログを記録して終了
 *
 * エラー処理:
 * 各エラーシナリオでエラーログ記録し、当該タスクは次回のRunbookジョブ監視関数に持ち越す。
 */
<span class="cstat-no" title="statement not covered" >export a</span>sync function <span class="fstat-no" title="function not covered" >RunbookMonitorFunc(</span>_timer: Timer, context: InvocationContext) {
<span class="cstat-no" title="statement not covered" >  try {</span>
    // Service Busクライアントを初期化（並行実行時の安全性確保）
<span class="cstat-no" title="statement not covered" >    serviceBusClient = createServiceBusClient();</span>
<span class="cstat-no" title="statement not covered" >    sender = serviceBusClient.createSender(runbookStatusQueueName);</span>
&nbsp;
    // 1. Runbookジョブ監視関数がタイマートリガーにより起動される。処理開始のログを記録する。
<span class="cstat-no" title="statement not covered" >    context.log("[RunbookMonitorFunc] Runbookジョブ監視関数開始");</span>
&nbsp;
    // 2. データベースの Task テーブルから、status が RUNBOOK_SUBMITTED である全てのタスクレコードを取得する。
    const tasks = <span class="cstat-no" title="statement not covered" >await prisma.task.findMany({</span>
      where: { status: AppConstants.TaskStatus.RunbookSubmitted },
    });
&nbsp;
    // 取得失敗または対象タスクが存在しない場合、ログに記録し処理を終了する。
<span class="cstat-no" title="statement not covered" >    <span class="missing-if-branch" title="if path not taken" >I</span>if (!tasks.length) {</span>
<span class="cstat-no" title="statement not covered" >      context.log("[RunbookMonitorFunc] RUNBOOK_SUBMITTEDステータスのタスクが存在しません。処理を終了します。");</span>
<span class="cstat-no" title="statement not covered" >      return;</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    context.log(`[RunbookMonitorFunc] RUNBOOK_SUBMITTEDステータスのタスク数: ${tasks.length}`);</span>
&nbsp;
    // 3. 取得した各タスクレコードに対して、4.-9.の処理をループで行う。各タスクの処理は独立しており、
    // あるタスクの処理失敗が他のタスクの処理を妨げない。
<span class="cstat-no" title="statement not covered" >    for (const task of tasks) {</span>
<span class="cstat-no" title="statement not covered" >      try {</span>
        // startedAt未設定タスクはスキップ（安全性チェック）
<span class="cstat-no" title="statement not covered" >        <span class="missing-if-branch" title="if path not taken" >I</span>if (!task.startedAt) {</span>
<span class="cstat-no" title="statement not covered" >          context.warn(`[RunbookMonitorFunc] タスク${task.id}はstartedAt未設定のためスキップ`);</span>
<span class="cstat-no" title="statement not covered" >          continue;</span>
        }
&nbsp;
        // 4. タイムアウトチェック:
        // タスクの開始日時startedAtから現在の日時までの時間を計算（両方UTC協定世界時で計算）し、
        // 環境変数 RUNBOOK_TIMEOUT_SECONDS で定義されたRunbookジョブのタイムアウト時間と比較して、
        // 超過しているか（タイムアウトしたか）判断する。
        const now = <span class="cstat-no" title="statement not covered" >new Date();</span>
        const startedAt = <span class="cstat-no" title="statement not covered" >new Date(task.startedAt!);</span>
        const elapsed = <span class="cstat-no" title="statement not covered" >(now.getTime() - startedAt.getTime()) / 1000;</span>
&nbsp;
        let automationJobStatus = <span class="cstat-no" title="statement not covered" >"";</span>
        let exception = <span class="cstat-no" title="statement not covered" >"";</span>
        let shouldProcess = <span class="cstat-no" title="statement not covered" >false;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (elapsed &gt; runbookTimeout) {</span>
          // タイムアウトした場合
<span class="cstat-no" title="statement not covered" >          automationJobStatus = "Timeout";</span>
<span class="cstat-no" title="statement not covered" >          shouldProcess = true;</span>
<span class="cstat-no" title="statement not covered" >          context.log(`[RunbookMonitorFunc] タスク${task.id}はタイムアウトしました (${elapsed}s &gt; ${runbookTimeout}s)`);</span>
        } else {
          // 5. (タイムアウトしていない場合)ジョブステータスポーリング :
          // taskId=jobName を使用して、Azure Automationのジョブ取得APIを呼び出してジョブの現在のステータスと
          // （例外が発生した場合）例外情報を問い合わせる。
          const jobName = <span class="cstat-no" title="statement not covered" >task.id;</span> // taskId=jobNameの前提
&nbsp;
<span class="cstat-no" title="statement not covered" >          try {</span>
            const jobResult = <span class="cstat-no" title="statement not covered" >await getAutomationJobStatus(jobName);</span>
<span class="cstat-no" title="statement not covered" >            <span class="missing-if-branch" title="if path not taken" >I</span>if (!jobResult.exists) {</span>
              // ジョブが存在しない場合（404）- 通常は発生しないが、ログ記録してスキップ
<span class="cstat-no" title="statement not covered" >              context.warn(`[RunbookMonitorFunc] タスク${task.id}のジョブが存在しません（404）`);</span>
<span class="cstat-no" title="statement not covered" >              continue;</span>
            }
            const jobStatus = <span class="cstat-no" title="statement not covered" >jobResult.jobData?.properties?.status || "";</span>
<span class="cstat-no" title="statement not covered" >            exception = jobResult.jobData?.properties?.exception || "";</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            context.log(`[RunbookMonitorFunc] タスク${task.id}のジョブステータス取得成功: ${jobStatus}`);</span>
&nbsp;
            // 6. (タイムアウトしていない場合)5.で取得したジョブステータスproperties.statusを判定する :
<span class="cstat-no" title="statement not covered" >            if (ACTIVE_STATUSES.includes(jobStatus)) {</span>
              // 準備中/実行中/一時状態（New, Activating, Running, Blocked, Disconnectedのいずれか）の場合：
              // 当該タスクが処理対象外のため、ステップ3.に戻って、次のタスクレコードの判定に入る。
<span class="cstat-no" title="statement not covered" >              context.log(`[RunbookMonitorFunc] タスク${task.id}は実行中状態のため処理対象外: ${jobStatus}`);</span>
<span class="cstat-no" title="statement not covered" >              continue;</span>
            } else <span class="cstat-no" title="statement not covered" >if (END_STATUSES.includes(jobStatus)) {</span>
              // 特定の終了状態/異常状態（Completed, Failed, Removing, Resuming, Stopped, Stopping, Suspended, Suspendingのいずれか）の場合：
              // ステップ7.の処理に進める。
<span class="cstat-no" title="statement not covered" >              automationJobStatus = jobStatus;</span>
<span class="cstat-no" title="statement not covered" >              shouldProcess = true;</span>
<span class="cstat-no" title="statement not covered" >              context.log(`[RunbookMonitorFunc] タスク${task.id}は終了状態のため処理対象: ${jobStatus}`);</span>
            } else {
              // 未知のステータス
<span class="cstat-no" title="statement not covered" >              context.warn(`[RunbookMonitorFunc] タスク${task.id}の未知ジョブステータス: ${jobStatus}`);</span>
<span class="cstat-no" title="statement not covered" >              continue;</span>
            }
          } catch (err: any) {
            // API の呼び出しに失敗した場合（レスポンスのHTTPステータスコードが200でない場合）：
            // エラー詳細をログに記録し、ステップ3.に戻って、次のタスクレコードの判定に入る。
            // 当該タスクに対する処理は次回のRunbookジョブ監視関数に持ち越す。
<span class="cstat-no" title="statement not covered" >            context.error(`[RunbookMonitorFunc] タスク${task.id}のAzure Automation API呼び出し失敗:`, err);</span>
<span class="cstat-no" title="statement not covered" >            continue;</span>
          }
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        <span class="missing-if-branch" title="if path not taken" >I</span>if (!shouldProcess) <span class="cstat-no" title="statement not covered" >continue;</span></span>
&nbsp;
        // 7. Taskテーブルから当該タスクのステータスをRUNBOOK_PROCESSINGに更新する。
        // 条件：IDがステップ2.で取得した各タスクレコードのtaskIdと一致し、
        // 最終更新日時がステップ2.で取得した最終更新日時と一致する。
<span class="cstat-no" title="statement not covered" >        try {</span>
          const updateResult = <span class="cstat-no" title="statement not covered" >await prisma.task.updateMany({</span>
            where: {
              id: task.id,
              updatedAt: task.updatedAt, // 楽観ロック制御
            },
            data: { status: AppConstants.TaskStatus.RunbookProcessing },
          });
&nbsp;
          // DB更新失敗、または更新した件数が0件の場合：
          // エラー詳細をログに記録し、ステップ3.に戻って、次のタスクレコードの判定に入る。
          // 当該タスクに対する処理は次回のRunbookジョブ監視関数に持ち越す。
<span class="cstat-no" title="statement not covered" >          <span class="missing-if-branch" title="if path not taken" >I</span>if (updateResult.count === 0) {</span>
<span class="cstat-no" title="statement not covered" >            context.error(`[RunbookMonitorFunc] タスク${task.id}の楽観ロック失敗：他プロセスによりタスクが変更されました`);</span>
<span class="cstat-no" title="statement not covered" >            continue;</span>
          }
&nbsp;
<span class="cstat-no" title="statement not covered" >          context.log(`[RunbookMonitorFunc] タスク${task.id}のステータス更新完了: RUNBOOK_PROCESSING`);</span>
        } catch (err: any) {
          // DB更新失敗の場合：エラー詳細をログに記録し、ステップ3.に戻って、次のタスクレコードの判定に入る。
          // 当該タスクに対する処理は次回のRunbookジョブ監視関数に持ち越す。
<span class="cstat-no" title="statement not covered" >          context.error(`[RunbookMonitorFunc] タスク${task.id}のDB更新失敗:`, err);</span>
<span class="cstat-no" title="statement not covered" >          continue;</span>
        }
&nbsp;
        // 8. Runbookジョブのステータスを通知するメッセージを構築し、Service Bus (RunbookStatusQueue) へ送信:
        // メッセージにはtaskId（当該タスクのID）、automationJobStatus（タイムアウトした場合は"Timeout"/
        // タイムアウトしていない場合はステップ5.で取得した実際のジョブステータスproperties.status）、
        // exception（ステップ5.で取得した例外情報properties.exception）が含まれる。
        // 送信失敗の場合は処理対象タスクのステータスをRUNBOOK_SUBMITTEDに更新（戻す）する。
<span class="cstat-no" title="statement not covered" >        try {</span>
          const message = <span class="cstat-no" title="statement not covered" >{</span>
            body: {
              taskId: task.id,
              automationJobStatus,
              exception,
            },
          };
&nbsp;
<span class="cstat-no" title="statement not covered" >          await sender.sendMessages(message);</span>
<span class="cstat-no" title="statement not covered" >          context.log(`[RunbookMonitorFunc] タスク${task.id}のRunbookStatusQueueへメッセージ送信完了: ${automationJobStatus}`);</span>
        } catch (err: any) {
          // 送信失敗の場合：
          // ・処理対象タスクのステータスをRUNBOOK_SUBMITTEDに更新する（戻す）。
          //   条件：IDが処理対象タスクのタスクIDと一致し、ステータスがRUNBOOK_PROCESSINGと一致する。
          // ・エラー詳細をログに記録し、ステップ3.に戻って、次のタスクレコードの判定に入る。
          //   当該タスクに対する処理は次回のRunbookジョブ監視関数に持ち越す。
<span class="cstat-no" title="statement not covered" >          try {</span>
            const rollbackResult = <span class="cstat-no" title="statement not covered" >await prisma.task.updateMany({</span>
              where: {
                id: task.id,
                status: AppConstants.TaskStatus.RunbookProcessing, // ステータス条件による制御
              },
              data: { status: AppConstants.TaskStatus.RunbookSubmitted },
            });
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (rollbackResult.count === 0) {</span>
<span class="cstat-no" title="statement not covered" >              context.error(`[RunbookMonitorFunc] タスク${task.id}のステータス戻し失敗：タスクが既に他の状態に変更されています`);</span>
            } else {
<span class="cstat-no" title="statement not covered" >              context.log(`[RunbookMonitorFunc] タスク${task.id}のステータスをRUNBOOK_SUBMITTEDに戻しました`);</span>
            }
          } catch (rollbackErr: any) {
<span class="cstat-no" title="statement not covered" >            context.error(`[RunbookMonitorFunc] タスク${task.id}のステータス戻し失敗:`, rollbackErr);</span>
          }
&nbsp;
<span class="cstat-no" title="statement not covered" >          context.error(`[RunbookMonitorFunc] タスク${task.id}のRunbookStatusQueue送信失敗:`, err);</span>
<span class="cstat-no" title="statement not covered" >          continue;</span>
        }
      } catch (err: any) {
        // 予期せぬ内部エラー: エラーログ記録。ループ内ならループ継続し、
        // 当該タスクは次回のRunbookジョブ監視関数に持ち越す。
<span class="cstat-no" title="statement not covered" >        context.error(`[RunbookMonitorFunc] タスク${task.id}処理中の予期せぬ内部エラー:`, err);</span>
<span class="cstat-no" title="statement not covered" >        continue;</span>
      }
      // 9. ステップ3.に戻って、次のタスクレコードの判定に入る。
    }
&nbsp;
    // 10. RUNBOOK_SUBMITTEDのタスクレコードの判定が全部終わったら、処理成功のログを記録して、
    // Runbookジョブ監視関数を終了する。
<span class="cstat-no" title="statement not covered" >    context.log("[RunbookMonitorFunc] RUNBOOK_SUBMITTEDタスクの監視処理が正常に完了しました");</span>
  } catch (err: any) {
    // Taskテーブルからのデータ取得失敗: エラーログ記録。処理終了。
<span class="cstat-no" title="statement not covered" >    context.error(`[RunbookMonitorFunc] Taskテーブルからのデータ取得に失敗しました。`, err);</span>
  }
}
&nbsp;
// Azure Functionsタイマートリガー登録
<span class="cstat-no" title="statement not covered" >app.timer("RunbookMonitorFunc", {</span>
  schedule: `*/${monitorInterval} * * * * *`, // 秒単位の定期実行
  handler: RunbookMonitorFunc,
}); </pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-30T07:06:17.797Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    