/**
 * @fileoverview 简化的数据库连接管理ユーティリティ
 * @description 开发环境种子数据生成用的简化Prismaクライアント管理
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { PrismaClient } from '@prisma/client';

let prismaInstance: PrismaClient | null = null;

/**
 * Prismaクライアントのシングルトンインスタンスを取得する
 * @returns Prismaクライアント
 */
export function getPrismaClient(): PrismaClient {
  if (!prismaInstance) {
    prismaInstance = new PrismaClient({
      datasources: {
        db: {
          url: process.env.MSSQL_PRISMA_URL,
        },
      },
      log: ['error', 'warn'],
    });
    console.log('✅ Prismaクライアントを初期化しました');
  }
  return prismaInstance;
}

/**
 * データベースに接続する
 */
export async function connectDatabase(): Promise<void> {
  const prisma = getPrismaClient();
  await prisma.$connect();
  console.log('🔌 データベースに接続しました');
}

/**
 * データベースから切断する
 */
export async function disconnectDatabase(): Promise<void> {
  if (prismaInstance) {
    await prismaInstance.$disconnect();
    console.log('🔌 データベースから切断しました');
  }
}

/**
 * データベース接続をテストする
 * @returns テスト結果
 */
export async function testDatabaseConnection(): Promise<boolean> {
  try {
    const prisma = getPrismaClient();
    await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ データベース接続テストが成功しました');
    return true;
  } catch (error) {
    console.error('❌ データベース接続テストが失敗しました:', error);
    return false;
  }
}

/**
 * データベースの統計情報を取得する
 * @returns 統計情報
 */
export async function getDatabaseStats() {
  const prisma = getPrismaClient();

  try {
    const stats = {
      licenseCount: await prisma.license.count(),
      serverCount: await prisma.server.count(),
      taskCount: await prisma.task.count(),
      lovCount: await prisma.lov.count(),
      planCount: await prisma.plan.count(),
      productManualCount: await prisma.productManual.count(),
      productMediaCount: await prisma.productMedia.count(),
      providedFileCount: await prisma.providedFile.count(),
      supportFileCount: await prisma.supportFile.count(),
      operationLogCount: await prisma.operationLog.count(),
      // Plan関連データの統計
      planProductCount: await prisma.planProduct.count(),
      planManualCount: await prisma.planManual.count(),
      planProvidedFileCount: await prisma.planProvidedFile.count(),
      planSupportCount: await prisma.planSupport.count(),
      licensePlanCount: await prisma.licensePlan.count(),
      timestamp: new Date().toISOString(),
    };

    console.log('📊 データベース統計情報を取得しました');
    return stats;
  } catch (error) {
    console.error('❌ データベース統計情報の取得に失敗しました:', error);
    throw error;
  }
}
