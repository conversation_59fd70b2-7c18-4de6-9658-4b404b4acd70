/**
 * @file ConfirmModal.tsx
 * @description
 * 通用二次確認モーダルコンポーネント。任意のタイトル・内容・ボタン文言・ローディング・危険操作色に対応。
 * すべてのコメントはJSDoc3日本語形式。
 * <AUTHOR>
 * @copyright Copyright (C) 2025 Hitachi Solutions, Ltd.
 */

"use client";

import React, { useEffect, useRef } from "react";
import clsx from "clsx";
import Spinner from "./spinner";

/**
 * ConfirmModalのプロパティ定義
 * @typedef {Object} ConfirmModalProps
 * @property {boolean} isOpen - モーダル表示状態
 * @property {string} title - タイトル
 * @property {string | React.ReactNode} message - 本文（複数行可）
 * @property {() => void | Promise<void>} onConfirm - OKボタン押下時コールバック
 * @property {() => void} onCancel - キャンセル/クローズ時コールバック
 * @property {string} [confirmText] - OKボタン文言（デフォルト: "OK"）
 * @property {string} [cancelText] - キャンセルボタン文言（デフォルト: "キャンセル"）
 * @property {boolean} [danger] - 危険操作時、OKボタンを赤色に
 * @property {boolean} [loading] - OKボタンローディング状態
 */
export interface ConfirmModalProps {
  isOpen: boolean;
  title: string;
  message: string | React.ReactNode;
  onConfirm: () => void | Promise<void>;
  onCancel: () => void;
  confirmText?: string;
  cancelText?: string;
  danger?: boolean;
  loading?: boolean;
}

/**
 * 通用二次確認モーダルコンポーネント
 * @param {ConfirmModalProps} props
 * @returns {JSX.Element | null}
 */
const ConfirmModal: React.FC<ConfirmModalProps> = ({
  isOpen,
  title,
  message,
  onConfirm,
  onCancel,
  confirmText = "OK",
  cancelText = "キャンセル",
  danger = false,
  loading = false,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  // ESCキーで閉じる
  useEffect(() => {
    if (!isOpen) return;
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onCancel();
      }
    };
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [isOpen, onCancel]);
  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮蔽 */}
      <div className="fixed inset-0 bg-black bg-opacity-50" />
      {/* モーダル本体 */}
      <div
        ref={modalRef}
        className="relative w-full max-w-md max-h-full"
        role="dialog"
        aria-modal="true"
        aria-labelledby="confirm-modal-title"
      >
        <div className="relative rounded shadow bg-gray-600">
          {/* ヘッダー */}
          <div className="flex items-center justify-between p-4 border-b rounded-t bg-gradient-header">
            <h3 id="confirm-modal-title" className="text-lg font-semibold text-white">
              {title}
            </h3>
            <button
              type="button"
              className="text-gray-400 bg-transparent rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center hover:bg-gray-600 hover:text-white"
              aria-label="モーダルを閉じる"
              onClick={onCancel}
              disabled={loading}
            >
              <svg className="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
              </svg>
            </button>
          </div>
          {/* 内容 */}
          <div className="h-32 px-8 py-4 bg-white text-base font-medium flex items-center">
            <img src="/dialoginfo_32.png" className="w-8 h-8 me-2 inline-block" alt="info" />
            <div className="whitespace-pre-line break-words">{message}</div>
          </div>
          {/* ボタンエリア */}
          <div className="flex flex-row-reverse items-center p-4 border-t rounded-b bg-gradient-header">
            <button
              type="button"
              className="w-28 ms-3 rounded bg-gradient-blue px-3 py-2 text-center text-xs font-medium text-white drop-shadow-blue shadow-inner hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
              onClick={onCancel}
              disabled={loading}
            >
              {cancelText}
            </button>
            <button
              type="button"
              className={clsx(
                "w-28 rounded px-3 py-2 text-center text-xs font-medium text-white shadow-inner hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300",
                danger
                  ? "bg-gradient-red drop-shadow-red shadow-red"
                  : "bg-gradient-dark drop-shadow-dark shadow-dark",
                loading && "opacity-60 cursor-not-allowed",
              )}
              disabled={loading}
              onClick={async () => {
                await onConfirm();
              }}
            >
              <Spinner
                className={clsx("inline-block mr-2", { hidden: !loading })}
              />
              {confirmText}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmModal; 