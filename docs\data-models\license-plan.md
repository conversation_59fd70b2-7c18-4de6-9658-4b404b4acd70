# 数据模型: 许可证与计划关联 (LicensePlan)

*   **表名 (逻辑名)**: `LicensePlan`
*   **对应UI界面**: N/A (主要由系统内部用于建立`License`和`Plan`之间的多对多关系)
*   **主要用途**: 作为 `License` (许可证/契约) 表和 `Plan` (契约计划信息) 表之间的多对多关联（中间）表。它记录了哪个许可证订阅了哪个契约计划。

## 1. 字段定义

| 字段名 (Prisma/英文) | 数据类型 (SQL参考) | PK | FK | UQ | Nullable | Default  | 描述 (中文)                                                     |
| :------------------- | :----------------- | :--- | :--- | :--- | :------- | :------- | :-------------------------------------------------------------- |
| `id`                 | VARCHAR(36)        | ●    |      |      |          | `cuid()` | 主键。CUID格式，由系统自动生成。                                    |
| `licenseId`          | VARCHAR(XX)        |      | ●    |      |          |          | **外键**。关联到 `License` 表的 `licenseId` (业务唯一键)。          |
| `planId`             | VARCHAR(XX)        |      | ●    |      |          |          | **外键**。关联到 `Plan` 表的 `planId` (业务唯一键)。                |

*PK: Primary Key, FK: Foreign Key, UQ: Unique Key*
*XX表示具体长度，取决于实际的数据库Schema定义。*

## 2. 关系

*   **对 `Plan` (`plan`)**: 多对一关系。通过 `planId` 字段关联到 `Plan` 表的 `planId` 唯一键。
*   **对 `License` (`license`)**: 多对一关系。通过 `licenseId` 字段关联到 `License` 表的 `licenseId` 唯一键。

## 3. 索引

*   `PRIMARY KEY (id)`
*   `INDEX idx_license_plan (licenseId, planId)` (Prisma Schema已定义，用于优化基于这两个外键的查询，并可支持这两个字段组合的唯一性约束，如果业务需要)