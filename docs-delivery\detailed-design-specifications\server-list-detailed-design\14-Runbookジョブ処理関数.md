## Runbookジョブ処理関数 (RunbookProcessorFunc) 詳細設計

### 概要

#### 責務
RunbookProcessorFuncは、Azure Service BusのRunbookStatusQueueからRunbookジョブの実行結果に関するメッセージ（RunbookMonitorFuncによって送信されたもの）を受信し、
タスクの最終的な後処理を行うAzure Functionである。主な責務は以下の通り。

1.  受信したメッセージを解析し、taskId、automationJobStatus（Azure Automationジョブの実際のステータスまたはRunbookMonitorFuncが判断した"Timeout"）、ジョブに例外が発生した場合はexception（例外情報）を取得する。
2.  Taskテーブルを参照し、該当タスクのタスク名、現在のステータス、タスク種別、ライセンスID、対象VM名、対象コンテナ名などの情報を取得する。
3.  該当タスクのステータスがRUNBOOK_PROCESSINGか判断し、RUNBOOK_PROCESSING以外の場合は処理を終了する。
4.  automationJobStatusを判断し、値によって5.-11.から該当の処理を行った後、12.に進める。
5.  automationJobStatusがCompletedかつタスク種別が操作ログのエクスポートの場合、Azure Filesワークスペースのエクスポートディレクトリ TaskWorkspaces/<TaskID>/exports/ から、Runbook（基盤スクリプト）が出力した成果物（exportoplog_<連番>.zip）をAzure Blob Storageの最終保存場所（環境変数AZURE_STORAGE_CONTAINER_OPLOGSで指定されたコンテナの{Task.licenseId}/{taskId}/）へ、<Task.taskName>_<連番>.zipにリネームしてコピーする。その後操作ログOperationLogテーブルに今回のタスクと関連する新規レコードを作成し、タスクのステータスをCOMPLETED_SUCCESSに更新する。
6.  automationJobStatusがCompletedかつタスク種別が管理項目定義のエクスポートの場合、Azure Filesワークスペースのエクスポートディレクトリ TaskWorkspaces/<TaskID>/exports/ から、Runbook（基盤スクリプト）が出力した成果物（assetsfield_def.csv）をAzure Blob Storageの最終保存場所（環境変数AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEFで指定されたコンテナの{Task.licenseId}/exports/{taskId}/）へ、ファイル名そのままでコピーする。その後タスクのステータスをCOMPLETED_SUCCESSに更新する。
7.  automationJobStatusがCompletedかつタスク種別が管理項目定義のインポートの場合、タスクのステータスをCOMPLETED_SUCCESSに更新する。
8.  automationJobStatusがFailedの場合、Azure Files上のエラーメッセージファイル(errordetail.txt)の存在を確認する。errordetail.txtが存在している場合はタスクのステータスをCOMPLETED_ERRORに更新し、タスク詳細をerrordetail.txtの内容が反映された基盤スクリプト実行エラーのメッセージに更新する。errordetail.txtが存在していない場合はタスクのステータスをCOMPLETED_ERRORに更新し、タスク詳細を基盤スクリプト起動エラーのメッセージに更新する。
9.  automationJobStatusがRemoving / Stopped / Stoppingのいずれかの場合、タスクのステータスをCOMPLETED_ERRORに更新し、タスク詳細をメンテナンスのエラーメッセージに更新する。
10. automationJobStatusがResuming / Suspended / Suspendingのいずれかの場合、Azure Automation APIを呼び出して、該当Runbookジョブをストップする。その後タスクのステータスをCOMPLETED_ERRORに更新し、タスク詳細をメンテナンスのエラーメッセージに更新する。
11. automationJobStatusがTimeoutの場合、Azure Automation APIを呼び出して、該当Runbookジョブをストップする。その後タスクのステータスをCOMPLETED_ERRORに更新し、タスク詳細をタイムアウトのエラーメッセージに更新する。
12. コンテナ実行状態ContainerConcurrencyStatusテーブルから対象コンテナのステータスをIDLEに更新する。
13. Azure Files上のタスクIDごとの作業ディレクトリをクリーンアップ（削除）する。

#### トリガー
Azure Service Bus - RunbookStatusQueue キューメッセージ。

#### 主要入力
*   RunbookStatusQueue から受信するJSON形式のメッセージ。主な構造は本設計書「Runbookジョブ監視関数 (RunbookMonitorFunc) 詳細設計 - 主要なデータ及び外部サービスとの対話詳細 - Azure Service Bus (RunbookStatusQueue) との対話」セクションのメッセージボディ例を参照。
*   Azure Filesワークスペースのエクスポートディレクトリ TaskWorkspaces/<TaskID>/exports/にRunbook（基盤スクリプト）が出力したエクスポートファイル及びエラーメッセージファイル(errordetail.txt)。
*   Azure SQL Database のTaskテーブルからタスクの関連情報を取得する。

#### 主要出力
*   Azure SQL Database のTaskテーブル、ContainerConcurrencyStatusテーブルのレコード更新。操作ログのエクスポートタスクが正常終了した場合はOperationLogテーブルのレコード作成。
*   （エクスポートタスク成功時）Azure Blob Storageへの最終成果物ファイルのコピー。
*   Azure Files上のタスク作業ディレクトリ (`TaskWorkspaces/<TaskID>/`) の削除。
*   automationJobStatusがResuming / Suspended / Suspending / Timeout のいずれかの場合、Azure AutomationへのRunbookジョブストップ要求。

※RunbookStatusQueueの名称は、環境変数 SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME によって定義される。
※Azure Filesへのアクセスには環境変数 AZURE_STORAGE_CONNECTION_STRING で指定される接続文字列が使用される。
※データベース接続には環境変数 MSSQL_PRISMA_URL で指定される接続文字列が使用される。
※Azure Blob Storageへのアクセスには環境変数 AZURE_STORAGE_ACCOUNT_NAME が、対象コンテナの指定には環境変数 AZURE_STORAGE_CONTAINER_OPLOGS (操作ログ用)とAZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF (管理項目定義用) が使用される。
※Azure Automationアカウントの名称は環境変数 AZURE_AUTOMATION_ACCOUNT_NAME により指定される。
※サブスクリプションIDは環境変数 SUBSCRIPTION_ID により指定される。
※リソースグループ名は環境変数 RESOURCE_GROUP_NAME により指定される。

### 主要処理フロー

```mermaid
graph TD
    A["開始: RunbookStatusQueue<br/>メッセージ受信"] --> B["メッセージ解析・<br/>必須パラメータ検証<br/>(taskId, automationJobStatus,<br/>(任意)exception)"];
    B -- "不足/不正" --> BA["エラーログ記録、<br/>例外throwでリトライ"];
    B -- "正常" --> C["Taskテーブル参照<br/>(taskIdで現在のstatus,<br/>taskType, licenseId, targetVmName,<br/>targetContainerName など取得)"];
    C -- "Task存在なし/取得失敗" --> BA;
    C -- "取得成功" --> C_taskStatusCheck{"Task.status判定"};
    C_taskStatusCheck -- "RUNBOOK_PROCESSING以外" --> CA["ログ記録、処理終了"];
    C_taskStatusCheck -- "RUNBOOK_PROCESSING" --> D{"automationJobStatus判定"};

    D -- "Completed" --> E{"Task.taskType判定"};
        E -- "TASK_TYPE.OPLOG_EXPORT<br/>(操作ログエクスポート)" --> LogFileExistCheck{"エクスポートされたログファイルが存在しているか"};
            LogFileExistCheck -- "存在している" --> F_Oplog["Azure FilesからBlobへ<br/>exportoplog_*.zipを<br/>{TaskName}_{連番}.zipにリネームしコピー"];
                F_Oplog -- "コピー失敗" --> F_Fail_Oplog["Task.status = COMPLETED_ERROR<br/>Task.resultMessage = EMET0002(Filesエラー)/0003(Blobエラー)<br/>Task.endedAt = Now(UTC)"];
                F_Oplog -- "コピー成功" --> G_Oplog["OperationLogテーブルへ作成するレコードを設定"];
                G_Oplog --> H_Oplog_Success["Task.status = COMPLETED_SUCCESS<br/>Task.resultMessage = EMET0014 (操作ログエクスポート成功)<br/>Task.endedAt = Now(UTC)"];
            LogFileExistCheck -- "存在しない" --> LogFileNotExist["Task.status = COMPLETED_ERROR<br/>Task.resultMessage = EMET0015 (エクスポートファイルなし)<br/>Task.endedAt = Now(UTC)"];
        
        E -- "TASK_TYPE.MGMT_ITEM_EXPORT<br/>(管理項目定義エクスポート)" -->  MgmtFileExistCheck{"エクスポートされた管理項目定義ファイルが存在しているか"};
            MgmtFileExistCheck -- "存在している" --> I_MgmtExport["Azure FilesからBlobへ<br/>assetsfield_def.csvを<br/>[Task.licenseId]/exports/[taskId]/へコピー"];
                I_MgmtExport -- "コピー失敗" --> F_Fail_MgmtExport["Task.status = COMPLETED_ERROR<br/>Task.resultMessage = EMET0002(Filesエラー)/0003(Blobエラー)<br/>Task.endedAt = Now(UTC)"];
                I_MgmtExport -- "コピー成功" --> H_MgmtExport_Success["Task.status = COMPLETED_SUCCESS<br/>Task.endedAt = Now(UTC)"];
            MgmtFileExistCheck -- "存在しない" --> MgmtFileNotExist["Task.status = COMPLETED_ERROR<br/>Task.resultMessage = EMET0015 (エクスポートファイルなし)<br/>Task.endedAt = Now(UTC)"];

        E -- "TASK_TYPE.MGMT_ITEM_IMPORT<br/>(管理項目定義インポート)" --> H_MgmtImport_Success["Task.status = COMPLETED_SUCCESS<br/>Task.endedAt = Now(UTC)"];

    D -- "Failed" --> K_Failed["Azure Filesからerrordetail.txt<br/>内容取得試行"];
        K_Failed -- "errordetail.txtあり" --> L_Failed_Detail["Task.status = COMPLETED_ERROR<br/>Task.errorCode = EMET0011 (基盤スクリプト実行エラー)<br/>Task.resultMessage = errordetail.txtの内容<br/>Task.endedAt = Now(UTC)"];
        K_Failed -- "errordetail.txtなし" --> M_Failed_NoDetail["Task.status = COMPLETED_ERROR<br/>Task.errorCode = EMET0012 (基盤スクリプト起動エラー)<br/>Task.errorMessage = APIからのexception情報<br/>Task.endedAt = Now(UTC)"];

    D -- "Removing / Stopped / Stopping" --> N_Maintenance["Task.status = COMPLETED_ERROR<br/>Task.resultMessage = EMET0010 (メンテナンス)<br/>Task.endedAt = Now(UTC)"];
    
    D -- "Resuming / Suspended / Suspending" --> O_StopJob1["AutomationジョブストップAPI呼出"];
        O_StopJob1 -- "API呼出成功" --> P_Maintenance1["Task.status = COMPLETED_ERROR<br/>Task.resultMessage = EMET0010 (メンテナンス)<br/>Task.endedAt = Now(UTC)"];
        O_StopJob1 -- "API呼出失敗" --> O_Fail_StopJob1["エラーログ記録、<br/>例外throwでリトライ"];

    D -- "Timeout (RunbookMonitorFunc判断)" --> Q_StopJob2["AutomationジョブストップAPI呼出"];
        Q_StopJob2 -- "API呼出成功" --> R_Timeout["Task.status = COMPLETED_ERROR<br/>Task.resultMessage = EMET0005 (タイムアウト)<br/>Task.endedAt = Now(UTC)"];
        Q_StopJob2 -- "API呼出失敗" --> O_Fail_StopJob2["エラーログ記録、<br/>例外throwでリトライ"];

    F_Fail_Oplog --> Y_FilesCleanup;
    LogFileNotExist --> Y_FilesCleanup;
    H_Oplog_Success --> Y_FilesCleanup;
    F_Fail_MgmtExport --> Y_FilesCleanup;
    MgmtFileNotExist --> Y_FilesCleanup;
    H_MgmtExport_Success --> Y_FilesCleanup;
    H_MgmtImport_Success --> Y_FilesCleanup;
    L_Failed_Detail --> Y_FilesCleanup;
    M_Failed_NoDetail --> Y_FilesCleanup;
    N_Maintenance --> Y_FilesCleanup;
    P_Maintenance1 --> Y_FilesCleanup;
    R_Timeout --> Y_FilesCleanup;
    
    Y_FilesCleanup["Azure Files上の作業ディレクトリ削除<br/>(TaskWorkspaces/{taskId}/)"] -- "削除成功" --> TxExecute;
    Y_FilesCleanup["Azure Files上の作業ディレクトリ削除<br/>(TaskWorkspaces/{taskId}/)"] -- "削除失敗" --> Y_FilesCleanup_Fail;
    Y_FilesCleanup_Fail["ログ記録し、処理継続"] --> TxExecute;
    TxExecute["DBトランザクション開始"] --> TxTask;
    TxTask["Taskテーブル更新"] --> TxOplog;
    TxOplog["（操作ログのエクスポートの場合）OperationLogテーブルへレコード作成"] --> ContainerUpdate;
    ContainerUpdate["コンテナ実行状態テーブルから対象コンテナのstatusをIDLEに更新、<br/>currentTaskIdをNULLに更新"] --> TxCommit;
    TxCommit["トランザクションコミット"] --> TxResult{"ここまでの処理結果（tryブロック内で例外が発生したか）"};
    TxResult -- "処理失敗" --> TxFail["エラーログ記録、<br/>例外throwでリトライ"];
    TxResult -- "処理成功" --> Z["正常終了ログを記録、処理終了<br/>(メッセージACK)"];
```

**■ 共通処理フロー詳細**

1.  Azure Service Bus の RunbookStatusQueue からメッセージを受信し、JSON形式から解析する。taskId, automationJobStatus, （任意）exceptionを抽出する。
2.  必須情報（taskId, automationJobStatus）が不足/不正の場合、エラー詳細をログに記録した後、例外をthrowすることでキューメッセージが再配信され、Runbookジョブ処理関数がリトライする。
3.  taskId を使用して Task テーブルを検索し、タスクの関連情報（status, taskName, taskType, licenseId, targetVmName, targetContainerName 等）を取得する。また、statusが RUNBOOK_PROCESSINGであるか判定する。
    *   タスクが存在しない、または取得失敗の場合は、エラー詳細をログに記録した後、例外をthrowすることでキューメッセージが再配信され、Runbookジョブ処理関数がリトライする。
    *   タスクのstatusが RUNBOOK_PROCESSING でない場合、ログに記録して処理を終了する。
4.  メッセージ内の automationJobStatus に基づき、処理が分岐する。（ステップ4.で記載されたDB書き込みは即時に行われるのではなく、書き込みのデータを記録して、ステップ6.-9.で実行されるDBトランザクション内でコンテナのステータスの更新と一緒に行われる）
    *   **automationJobStatus が "Completed" の場合**:
        a.  Task.taskTypeが操作ログのエクスポート (TASK_TYPE.OPLOG_EXPORT) の場合:
            i.  Azure Filesワークスペースのエクスポートディレクトリ `TaskWorkspaces/<taskId>/exports/` から、パターン `exportoplog_*.zip` に一致する全てのファイルを取得する。
            ii. 一致するファイルが存在しない場合、status = COMPLETED_ERROR, endedAt = 現在の日時（UTC協定世界時）, resultMessage = EMET0015 (エクスポートファイルなし)のエラーメッセージ, errorCode = EMET0015 でタスクレコードを更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。その後ステップ5.の処理に進める。
            iii. 各ファイルに対し、Task.taskName (形式: `{ServerName}-{タスク種別}-{YYYYMMDDHHmmss}`) および元のファイル名の連番部分を使用し、新しいBlobファイル名 `{Task.taskName}_{連番}.zip` を構築する。
            iv. 元のファイルを新しいBlobファイル名で Azure Blob Storage の最終保存場所（環境変数AZURE_STORAGE_CONTAINER_OPLOGSで指定されたコンテナの{Task.licenseId}/{taskId}/）へコピーする。
            v. コピー失敗時は、status = COMPLETED_ERROR, endedAt = 現在の日時（UTC協定世界時）, resultMessage = EMET0003 (Blob操作失敗)のエラーメッセージ, errorMessage = システムが出力したエラー情報, errorCode = EMET0003 でタスクレコードを更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。その後ステップ5.の処理に進める。
            vi. コピー成功後、name = コピーしたログファイル名（新しいBlobファイル名）, size = ログファイルのサイズ, createdAt = 現在の日時（UTC協定世界時）, retentionAt = NULL, licenseId = Task.licenseId, fileName = コピーしたログファイル名（新しいBlobファイル名）, generatedByTaskId = taskId で新規レコードを操作ログOperationLog テーブルに作成する。
            vii. 操作ログレコード作成失敗時は、status = COMPLETED_ERROR, endedAt = 現在の日時（UTC協定世界時）, resultMessage = EMET0007 (DB操作失敗)のエラーメッセージ, errorMessage = システムが出力したエラー情報, errorCode = EMET0007 でタスクレコードを更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。その後ステップ5.の処理に進める。
            viii. 操作ログレコード作成成功時は、status = COMPLETED_SUCCESS, endedAt = 現在の日時（UTC協定世界時）, resultMessage = EMET0014「操作ログ一覧画面で操作ログファイルをダウロードしてください。ログ名は{タスク名}_{連番}です。」 でタスクレコードを更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。その後ステップ5.の処理に進める。
        b.  Task.taskTypeが管理項目定義のエクスポート (TASK_TYPE.MGMT_ITEM_EXPORT) の場合:
            i.  Azure Filesワークスペースのエクスポートディレクトリ `TaskWorkspaces/<taskId>/exports/` から、固定ファイル名 `assetsfield_def.csv`のファイル を取得する。
            ii. 目標のファイルが存在しない場合、status = COMPLETED_ERROR, endedAt = 現在の日時（UTC協定世界時）, resultMessage = EMET0015 (エクスポートファイルなし)のエラーメッセージ, errorCode = EMET0015 でタスクレコードを更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。その後ステップ5.の処理に進める。
            iii. `assetsfield_def.csv` を Azure Blob Storage の最終保存場所（環境変数AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEFで指定されたコンテナの{Task.licenseId}/exports/{taskId}/）へ、ファイル名はassetsfield_def.csvのままでコピーする。
            iv. コピー失敗時は、status = COMPLETED_ERROR, endedAt = 現在の日時（UTC協定世界時）, resultMessage = EMET0003 (Blob操作失敗)のエラーメッセージ, errorMessage = システムが出力したエラー情報, errorCode = EMET0003 でタスクレコードを更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。その後ステップ5.の処理に進める。
            v. コピー成功時は、status = COMPLETED_SUCCESS, endedAt = 現在の日時（UTC協定世界時） でタスクレコードを更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。その後ステップ5.の処理に進める。
        c.  Task.taskTypeが管理項目定義のインポート (TASK_TYPE.MGMT_ITEM_IMPORT) の場合:
            status = COMPLETED_SUCCESS, endedAt = 現在の日時（UTC協定世界時） でタスクレコードを更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。その後ステップ5.の処理に進める。
    *   **automationJobStatus が "Failed" の場合**:
        i. Azure Files上のタスク用一時ワークスペースのエクスポートディレクトリ `TaskWorkspaces/<taskId>/exports/` からエラーメッセージファイル(errordetail.txt)の存在を確認する。
        ii. errordetail.txt が存在している場合：status = COMPLETED_ERROR, endedAt = 現在の日時（UTC協定世界時）, resultMessage = 「タスクの実行に失敗しました。エラー詳細：{0}」（{0}はerrordetail.txtの内容で置き換え）, errorMessage = 入力パラメータのexception, errorCode = EMET0011 でタスクレコードを更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。その後ステップ5.の処理に進める。
        iii. errordetail.txt が存在していない場合：status = COMPLETED_ERROR, endedAt = 現在の日時（UTC協定世界時）, resultMessage = 「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0012)」, errorMessage = 入力パラメータのexception, errorCode = EMET0012 でタスクレコードを更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。その後ステップ5.の処理に進める。
    *   **automationJobStatus が Removing / Stopped / Stopping のいずれかの場合**:
        status = COMPLETED_ERROR, endedAt = 現在の日時（UTC協定世界時）, resultMessage = EMET0010（メンテナンス）のエラーメッセージ, errorCode = EMET0010 でタスクレコードを更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。その後ステップ5.の処理に進める。
    *   **automationJobStatus が Resuming / Suspended / Suspending のいずれかの場合**:
        i. Azure AutomationのジョブストップAPIを呼び出して、該当Runbookジョブをストップする。APIを呼び出すためのHTTPリクエストは以下の通り。
        リクエスト
        URI：POST https://management.azure.com/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Automation/automationAccounts/{automationAccountName}/jobs/{jobName}/stop?api-version=2023-11-01
        ※{subscriptionId}にはサブスクリプションID、{resourceGroupName}にはリソースグループ名、{automationAccountName}には環境変数AZURE_AUTOMATION_ACCOUNT_NAMEで指定されたAutomationのアカウント名、{jobName}にはtaskIdを設定する（タスク実行関数ではtaskId=jobNameでジョブを作成した）。
        ※Request Bodyは必要なし。
        ii. API呼び出し失敗の場合（レスポンスのHTTPステータスコードが200（OK）でない）：エラー詳細をログに記録した後、例外をthrowすることでキューメッセージが再配信され、Runbookジョブ処理関数がリトライする。
        iii. API呼び出し成功の場合（レスポンスのHTTPステータスコードが200（OK））：status = COMPLETED_ERROR, endedAt = 現在の日時（UTC協定世界時）, resultMessage = EMET0010 (メンテナンス)のエラーメッセージ, errorCode = EMET0010 でタスクレコードを更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。その後ステップ5.の処理に進める。
    *   **automationJobStatus が Timeout (RunbookMonitorFuncによる判断) の場合**:
        i. Azure AutomationのジョブストップAPIを呼び出して、該当Runbookジョブをストップする。APIを呼び出すためのHTTPリクエストは以下の通り。
        リクエスト
        URI：POST https://management.azure.com/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Automation/automationAccounts/{automationAccountName}/jobs/{jobName}/stop?api-version=2023-11-01
        ※{subscriptionId}にはサブスクリプションID、{resourceGroupName}にはリソースグループ名、{automationAccountName}には環境変数AZURE_AUTOMATION_ACCOUNT_NAMEで指定されたAutomationのアカウント名、{jobName}にはtaskIdを設定する（タスク実行関数ではtaskId=jobNameでジョブを作成した）。
        ※Request Bodyは必要なし。
        ii. API呼び出し失敗の場合（レスポンスのHTTPステータスコードが200（OK）でない）：エラー詳細をログに記録した後、例外をthrowすることでキューメッセージが再配信され、Runbookジョブ処理関数がリトライする。
        iii. API呼び出し成功の場合（レスポンスのHTTPステータスコードが200（OK））：status = COMPLETED_ERROR, endedAt = 現在の日時（UTC協定世界時）, resultMessage = EMET0005 (タイムアウト)のエラーメッセージ, errorCode = EMET0005 でタスクレコードを更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。その後ステップ5.の処理に進める。
5.  Azure Files上のタスクIDごとのタスク作業ディレクトリ (`TaskWorkspaces/<taskId>/`) をクリーンアップ（ディレクトリ全体を削除）する。
6.  DBトランザクションを開始する。
7.  ステップ4.で記載されたDB書き込み（タスクレコードの更新、操作ログレコードの作成）を行う。
8.  コンテナ実行状態ContainerConcurrencyStatus テーブルから対象コンテナを status = IDLE, currentTaskId = NULL で更新する。条件：対象VM名 = ステップ3.で取得した対象VM名、対象コンテナ名 = ステップ3.で取得した対象コンテナ名、ステータス（status） = BUSY、使用中のタスクID（currentTaskId） = 入力パラメータのtaskId
9.  トランザクションをコミットする。
10. ここまでの処理中にエラーが発生した場合：
    *   トランザクション中のエラー（DB書き込み失敗、または更新件数が0件）ならロールバックする。エラー詳細をログに記録した後、例外をthrowすることでキューメッセージが再配信され、Runbookジョブ処理関数がリトライする。
11. 正常終了ログを記録し、処理を終了する（メッセージACK）。

### 主要なデータ及び外部サービスとの対話詳細

#### Azure Service Bus (RunbookStatusQueue) からのメッセージ受信
*   本Functionは RunbookStatusQueue のメッセージをトリガーとする。
*   メッセージ構造は RunbookMonitorFunc の出力メッセージ仕様を参照。
*   Runbookジョブ処理関数が処理成功した場合メッセージは完了確認（ACK）が取れて、キューから削除される。 Runbookジョブ処理関数が例外やタイムアウトによって処理失敗した場合はメッセージが破棄され、Azure Service Busによって再配信されて、Runbookジョブ処理関数が再度起動される。RunbookStatusQueueに設定されている最大配信数の3回になってもRunbookジョブ処理関数が処理失敗の場合メッセージはRunbookStatusQueueのDLQへ送られる。

#### データベース (Task, ContainerConcurrencyStatus, OperationLog) との対話
*   Task テーブル:
    *   taskId で読み取り、現在のstatusなどの関連情報を取得。
    *   タスクレコードを最終的に更新する（status, endedAt, errorCode, resultMessage等）。
*   ContainerConcurrencyStatus テーブル:
    *   対象コンテナの status を IDLE に、currentTaskId を NULL に更新する。
*   OperationLog テーブル:
    *   操作ログエクスポートタスク成功時、成果物ファイルの情報を新規レコードとして作成。

#### Azure Blob Storage との対話
*   成果物コピー：エクスポートタスク成功時、Azure Files作業ディレクトリから成果物（ZIPやCSV）を対応するAzure Blob Storageのコンテナ（環境変数 AZURE_STORAGE_CONTAINER_OPLOGS または AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF）へコピー。

#### Azure Files との対話
*   タスク作業ディレクトリ（`TaskWorkspaces/<taskId>/exports/`） から成果物を読み取る。
*   タスク作業ディレクトリ（`TaskWorkspaces/<taskId>/`） 全体を削除する。

#### Azure Automation API との対話
*   automationJobStatus が Resuming, Suspended, Suspending, Timeout の場合、ジョブストップAPIを呼び出してジョブをストップする。

### エラー処理メカニズム

| エラーシナリオ | 関連エラーコード | 対処・補償ロジック |
|:---|:---|:---|
| 受信メッセージ解析失敗/必須パラメータ不足/Taskレコード存在なし・取得失敗 | - | エラー詳細をログに記録した後、例外をthrowすることでキューメッセージが再配信され、Runbookジョブ処理関数がリトライする。 |
| Task.statusがRUNBOOK_PROCESSINGでない | - | ログ記録。処理終了。（メッセージACK） |
| 成果物のBlob Storageへのコピー失敗 (エクスポートタスク) | EMET0003 | Task.statusをCOMPLETED_ERRORに、コンテナのstatusをIDLEに更新。作業ディレクトリ削除。処理終了。（メッセージACK） |
| OperationLogテーブルへのレコード作成失敗（操作ログのエクスポート） | EMET0007 | Task.statusをCOMPLETED_ERRORに、コンテナのstatusをIDLEに更新。作業ディレクトリ削除。処理終了。（メッセージACK） |
| エクスポートファイルが存在しない（操作ログのエクスポート、管理項目定義のエクスポート） | EMET0015 | Task.statusをCOMPLETED_ERRORに、コンテナのstatusをIDLEに更新。作業ディレクトリ削除。処理終了。（メッセージACK） |
| タスクステータスとコンテナステータスの更新失敗/更新件数が0件 | - | トランザクションロールバック。エラー詳細をログに記録した後、例外をthrowすることでキューメッセージが再配信され、Runbookジョブ処理関数がリトライする。 |
| Azure AutomationジョブストップAPI呼び出し失敗 | - | エラー詳細をログに記録した後、例外をthrowすることでキューメッセージが再配信され、Runbookジョブ処理関数がリトライする。 |
| Azure Files作業ディレクトリ削除失敗 | - | エラー詳細をログに記録した後、例外をthrowすることでキューメッセージが再配信され、Runbookジョブ処理関数がリトライする。 |
| 予期せぬ内部エラー | - | トランザクション内ならロールバック。エラー詳細をログに記録した後、例外をthrowすることでキューメッセージが再配信され、Runbookジョブ処理関数がリトライする。 |

*   **タイムアウトについて**
    Azure FunctionsがRunbookジョブ処理関数の実行時間を監視し、host.jsonのfunctionTimeoutプロパティで設定されたタイムアウト時間（30分）になっても関数がまだ終了していない場合、Azure Functionsは関数の実行を強制的に中止する。
*   **リトライについて**
    Azure FunctionsがRunbookジョブ処理関数に例外が発生したこと（catchされていない例外）或いはRunbookジョブ処理関数がタイムアウトしたことを検知した場合はトリガーとなったメッセージを破棄する。その後メッセージはAzure Service Busによって再配信され、Runbookジョブ処理関数が再度起動される。RunbookStatusQueueに設定されている最大配信数の3回（2回までリトライ）になってもRunbookジョブ処理関数が処理失敗の場合メッセージはRunbookStatusQueueのDLQへ送られる。