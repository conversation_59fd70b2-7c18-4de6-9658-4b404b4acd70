#!/usr/bin/env node

/**
 * 测试用例注释规则检查脚本
 *
 * 检查三个项目中的测试文件是否符合 dev-guide.md 中的单元测试注释规则：
 * 1. it 或 test 块之前的注释必须使用 JSDoc 块
 * 2. 必须包含以下日文关键词：試験観点：、試験対象：、試験手順：、確認項目：
 * 3. 关键词顺序应该正确
 */

const fs = require('fs');
const path = require('path');

// 项目路径配置
const PROJECTS = [
  'apps/jcs-endpoint-nextjs/__tests__',
  'apps/jcs-backend-services-standard/__tests__',
  'apps/jcs-backend-services-long-running/__tests__'
];

// 必需的日文关键词（按正确顺序）
const REQUIRED_KEYWORDS = [
  '試験観点：',
  '試験対象：', 
  '試験手順：',
  '確認項目：'
];

// 违规记录
const violations = [];

/**
 * 递归获取目录下所有测试文件
 */
function getTestFiles(dir) {
  const files = [];
  
  if (!fs.existsSync(dir)) {
    console.warn(`⚠️  目录不存在: ${dir}`);
    return files;
  }
  
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      files.push(...getTestFiles(fullPath));
    } else if (item.endsWith('.test.ts') || item.endsWith('.test.tsx')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * 解析文件内容，提取测试用例及其注释
 */
function parseTestFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const testCases = [];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // 查找 it( 或 test( 开头的行
    if (line.match(/^\s*(it|test)\s*\(/)) {
      // 向上查找注释块
      let commentStart = -1;
      let commentEnd = -1;
      
      // 从当前行向上查找注释
      for (let j = i - 1; j >= 0; j--) {
        const prevLine = lines[j].trim();
        
        if (prevLine === '*/') {
          commentEnd = j;
        } else if (prevLine.startsWith('/**')) {
          commentStart = j;
          break;
        } else if (prevLine === '' || prevLine.startsWith('*')) {
          continue;
        } else {
          // 遇到非注释行，停止查找
          break;
        }
      }
      
      // 提取测试名称
      const testNameMatch = line.match(/^\s*(it|test)\s*\(\s*["'`]([^"'`]+)["'`]/);
      const testName = testNameMatch ? testNameMatch[2] : '未知测试名称';
      
      testCases.push({
        testName,
        lineNumber: i + 1,
        commentStart: commentStart >= 0 ? commentStart + 1 : -1,
        commentEnd: commentEnd >= 0 ? commentEnd + 1 : -1,
        commentLines: commentStart >= 0 && commentEnd >= 0 
          ? lines.slice(commentStart, commentEnd + 1)
          : []
      });
    }
  }
  
  return testCases;
}

/**
 * 检查单个测试用例的注释
 */
function checkTestCase(filePath, testCase) {
  const issues = [];
  
  // 检查是否有注释
  if (testCase.commentLines.length === 0) {
    issues.push('缺少JSDoc注释块');
    return issues;
  }
  
  // 检查是否是JSDoc格式
  const firstLine = testCase.commentLines[0].trim();
  const lastLine = testCase.commentLines[testCase.commentLines.length - 1].trim();
  
  if (!firstLine.startsWith('/**') || !lastLine.endsWith('*/')) {
    issues.push('注释不是JSDoc格式 (/** ... */)');
  }
  
  // 合并所有注释内容
  const commentText = testCase.commentLines.join('\n');
  
  // 检查必需的关键词
  const missingKeywords = [];
  const foundKeywords = [];
  
  for (const keyword of REQUIRED_KEYWORDS) {
    if (commentText.includes(keyword)) {
      foundKeywords.push(keyword);
    } else {
      missingKeywords.push(keyword);
    }
  }
  
  if (missingKeywords.length > 0) {
    issues.push(`缺少必需的日文关键词: ${missingKeywords.join(', ')}`);
  }
  
  // 检查关键词顺序（如果都存在的话）
  if (foundKeywords.length === REQUIRED_KEYWORDS.length) {
    let lastIndex = -1;
    let orderCorrect = true;
    
    for (const keyword of REQUIRED_KEYWORDS) {
      const index = commentText.indexOf(keyword);
      if (index <= lastIndex) {
        orderCorrect = false;
        break;
      }
      lastIndex = index;
    }
    
    if (!orderCorrect) {
      issues.push(`关键词顺序不正确，应该按照: ${REQUIRED_KEYWORDS.join(' → ')}`);
    }
  }
  
  return issues;
}

/**
 * 检查单个文件
 */
function checkFile(filePath) {
  console.log(`\n📄 检查文件: ${filePath}`);
  
  try {
    const testCases = parseTestFile(filePath);
    console.log(`   找到 ${testCases.length} 个测试用例`);
    
    let fileViolations = 0;
    
    for (const testCase of testCases) {
      const issues = checkTestCase(filePath, testCase);
      
      if (issues.length > 0) {
        fileViolations++;
        violations.push({
          file: filePath,
          testName: testCase.testName,
          lineNumber: testCase.lineNumber,
          issues
        });
        
        console.log(`   ❌ 第${testCase.lineNumber}行 "${testCase.testName}"`);
        for (const issue of issues) {
          console.log(`      - ${issue}`);
        }
      }
    }
    
    if (fileViolations === 0) {
      console.log(`   ✅ 所有测试用例注释格式正确`);
    } else {
      console.log(`   ⚠️  发现 ${fileViolations} 个违规测试用例`);
    }
    
  } catch (error) {
    console.error(`   ❌ 解析文件失败: ${error.message}`);
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔍 开始检查测试用例注释规则...\n');
  console.log('📋 检查规则:');
  console.log('   1. it/test 块之前必须有 JSDoc 注释 (/** ... */)');
  console.log('   2. 必须包含日文关键词: ' + REQUIRED_KEYWORDS.join(', '));
  console.log('   3. 关键词顺序必须正确');
  console.log('=' .repeat(80));
  
  let totalFiles = 0;
  let totalTestCases = 0;
  
  // 检查每个项目
  for (const projectPath of PROJECTS) {
    console.log(`\n🗂️  检查项目: ${projectPath}`);
    console.log('-'.repeat(60));
    
    const testFiles = getTestFiles(projectPath);
    console.log(`找到 ${testFiles.length} 个测试文件`);
    
    totalFiles += testFiles.length;
    
    for (const filePath of testFiles) {
      checkFile(filePath);
      
      // 统计测试用例数量
      try {
        const testCases = parseTestFile(filePath);
        totalTestCases += testCases.length;
      } catch (error) {
        // 忽略解析错误
      }
    }
  }
  
  // 输出总结报告
  console.log('\n' + '='.repeat(80));
  console.log('📊 检查结果总结');
  console.log('='.repeat(80));
  console.log(`📁 检查文件总数: ${totalFiles}`);
  console.log(`🧪 检查测试用例总数: ${totalTestCases}`);
  console.log(`❌ 违规测试用例总数: ${violations.length}`);
  
  if (violations.length === 0) {
    console.log('\n🎉 恭喜！所有测试用例注释都符合规范！');
    process.exit(0);
  } else {
    console.log(`\n⚠️  发现 ${violations.length} 个违规项，需要修复：`);
    console.log('\n📝 详细违规列表:');
    
    // 按文件分组显示违规项
    const violationsByFile = {};
    for (const violation of violations) {
      if (!violationsByFile[violation.file]) {
        violationsByFile[violation.file] = [];
      }
      violationsByFile[violation.file].push(violation);
    }
    
    for (const [file, fileViolations] of Object.entries(violationsByFile)) {
      console.log(`\n📄 ${file} (${fileViolations.length} 个违规)`);
      for (const violation of fileViolations) {
        console.log(`   第${violation.lineNumber}行: "${violation.testName}"`);
        for (const issue of violation.issues) {
          console.log(`     - ${issue}`);
        }
      }
    }
    
    console.log('\n💡 修复建议:');
    console.log('   1. 确保每个 it/test 块之前都有 JSDoc 注释');
    console.log('   2. 注释格式: /** ... */');
    console.log('   3. 按顺序包含: ' + REQUIRED_KEYWORDS.join(' → '));
    
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}
