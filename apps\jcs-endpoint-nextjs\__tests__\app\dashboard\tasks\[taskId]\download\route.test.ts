/**
 * @fileoverview タスクファイルダウンロードAPIルートのテスト
 * @description
 * /dashboard/tasks/[taskId]/download/route.ts のGETハンドラのテストケース。
 * セッション認証、LOV設定取得、SAS URL生成、エラーハンドリングの各観点を検証する。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { NextRequest } from "next/server";
import { redirect } from "next/navigation";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";

import { GET } from "@/app/dashboard/tasks/[taskId]/download/route";
import { BlobActions } from "@/app/lib/integrations/azure-blob";
import { ServerDataLov } from "@/app/lib/data/lov";
import Logger from "@/app/lib/logger";
import {
  LOV_CODE_AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF,
  PORTAL_ERROR_MESSAGES,
} from "@/app/lib/definitions";

// モック設定
jest.mock("next/navigation", () => ({
  redirect: jest.fn(),
}));

jest.mock("iron-session", () => ({
  getIronSession: jest.fn(),
}));

jest.mock("next/headers", () => ({
  cookies: jest.fn(),
}));

jest.mock("@/app/lib/integrations/azure-blob", () => ({
  BlobActions: {
    generateBlobUrlWithSAS: jest.fn(),
  },
}));

jest.mock("@/app/lib/data/lov", () => ({
  ServerDataLov: {
    fetchLov: jest.fn(),
  },
}));

jest.mock("@/app/lib/logger", () => ({
  error: jest.fn(),
  info: jest.fn(),
}));

describe("GET /dashboard/tasks/[taskId]/download", () => {
  const mockTaskId = "test-task-123";
  const mockLicenseId = "test-license-456";
  const mockContainerName = "test-container";
  const mockSasUrl = "https://storage.blob.core.windows.net/container/file?sas=token";

  let mockRequest: NextRequest;
  let mockParams: { params: { taskId: string } };

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockRequest = {} as NextRequest;
    mockParams = { params: { taskId: mockTaskId } };

    // デフォルトのモック設定
    (getIronSession as jest.Mock).mockResolvedValue({
      user: { licenseId: mockLicenseId },
    });

    (ServerDataLov.fetchLov as jest.Mock).mockResolvedValue({
      value: mockContainerName,
    });

    (BlobActions.generateBlobUrlWithSAS as jest.Mock).mockResolvedValue(mockSasUrl);
  });

  describe("正常系", () => {
    /**
     * 試験観点：正常なダウンロード処理
     * 試験対象：設計文書に従った基本的なダウンロードフロー
     * 試験手順：
     *   1. 有効なセッションとタスクIDでリクエストを送信
     *   2. LOV設定が正常に取得される
     *   3. SAS URLが生成される
     *   4. リダイレクトが実行される
     * 確認項目：
     *   - ServerDataLov.fetchLovが正しいパラメータで呼ばれること
     *   - BlobActions.generateBlobUrlWithSASが正しいパラメータで呼ばれること
     *   - redirectが正しいSAS URLで呼ばれること
     *   - 適切なログが出力されること
     */
    it("正常系：有効なセッションとタスクIDでダウンロードが成功する", async () => {
      await GET(mockRequest, mockParams);

      // LOV設定取得の確認
      expect(ServerDataLov.fetchLov).toHaveBeenCalledWith(
        LOV_CODE_AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF
      );

      // SAS URL生成の確認
      expect(BlobActions.generateBlobUrlWithSAS).toHaveBeenCalledWith(
        mockContainerName,
        [mockLicenseId, "exports", mockTaskId, "assetsfield_def.csv"]
      );

      // リダイレクトの確認
      expect(redirect).toHaveBeenCalledWith(mockSasUrl);

      // 成功ログの確認
      expect(Logger.info).toHaveBeenCalledWith({
        message: "タスクファイルダウンロード: SAS URLリダイレクト実行",
        taskId: mockTaskId,
        licenseId: mockLicenseId,
        containerName: mockContainerName,
        blobPath: `${mockLicenseId}/exports/${mockTaskId}/assetsfield_def.csv`,
        source: "GET /dashboard/tasks/[taskId]/download",
      });
    });
  });

  describe("異常系：セッション認証エラー", () => {
    /**
     * 試験観点：セッションが存在しない場合のエラー処理
     * 試験対象：認証失敗時の500エラーレスポンス（oplogs下載路由と同様）
     * 試験手順：
     *   1. セッションが存在しない状態でリクエストを送信
     *   2. 500エラーが返されることを確認
     * 確認項目：
     *   - 500ステータスコードが返されること
     *   - EMEC0007エラーメッセージが返されること
     *   - エラーログが出力されること
     */
    it("異常系：セッションが存在しない場合は500エラーを返す", async () => {
      (getIronSession as jest.Mock).mockResolvedValue({});

      const response = await GET(mockRequest, mockParams);

      expect(response.status).toBe(500);
      expect(await response.text()).toBe(PORTAL_ERROR_MESSAGES.EMEC0007);
      
      expect(Logger.error).toHaveBeenCalledWith({
        message: "タスクファイルダウンロード: セッションが無効または未認証",
        taskId: mockTaskId,
        sessionExists: true,
        userExists: false,
        source: "GET /dashboard/tasks/[taskId]/download",
      });
    });

    /**
     * 試験観点：ライセンスIDが存在しない場合のエラー処理
     * 試験対象：不完全なセッション情報での認証失敗
     * 試験手順：
     *   1. ライセンスIDが含まれていないセッションでリクエストを送信
     *   2. 500エラーが返されることを確認
     * 確認項目：
     *   - 500ステータスコードが返されること
     *   - 適切なエラーログが出力されること
     */
    it("異常系：ライセンスIDが存在しない場合は500エラーを返す", async () => {
      (getIronSession as jest.Mock).mockResolvedValue({
        user: {},
      });

      const response = await GET(mockRequest, mockParams);

      expect(response.status).toBe(500);
      expect(Logger.error).toHaveBeenCalledWith({
        message: "タスクファイルダウンロード: セッションが無効または未認証",
        taskId: mockTaskId,
        sessionExists: true,
        userExists: true,
        source: "GET /dashboard/tasks/[taskId]/download",
      });
    });
  });

  describe("異常系：LOV設定エラー", () => {
    /**
     * 試験観点：LOV設定が存在しない場合のエラー処理
     * 試験対象：Azure Blob Storageコンテナ設定取得失敗時の500エラー
     * 試験手順：
     *   1. LOV設定が存在しない状態でリクエストを送信
     *   2. 500エラーが返されることを確認
     * 確認項目：
     *   - 500ステータスコードが返されること
     *   - EMEC0007エラーメッセージが返されること
     *   - 適切なエラーログが出力されること
     */
    it("異常系：LOV設定が存在しない場合は500エラーを返す", async () => {
      (ServerDataLov.fetchLov as jest.Mock).mockResolvedValue(null);

      const response = await GET(mockRequest, mockParams);

      expect(response.status).toBe(500);
      expect(await response.text()).toBe(PORTAL_ERROR_MESSAGES.EMEC0007);
      
      expect(Logger.error).toHaveBeenCalledWith({
        message: "タスクファイルダウンロード: Azure Blob Storageコンテナ設定が見つからない",
        lovCode: LOV_CODE_AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF,
        taskId: mockTaskId,
        licenseId: mockLicenseId,
        containerLovExists: false,
        source: "GET /dashboard/tasks/[taskId]/download",
      });
    });

    /**
     * 試験観点：LOV設定のvalue値が空の場合のエラー処理
     * 試験対象：不正なLOV設定での500エラー
     * 試験手順：
     *   1. LOV設定のvalue値が空の状態でリクエストを送信
     *   2. 500エラーが返されることを確認
     * 確認項目：
     *   - 500ステータスコードが返されること
     *   - 適切なエラーログが出力されること
     */
    it("異常系：LOV設定のvalue値が空の場合は500エラーを返す", async () => {
      (ServerDataLov.fetchLov as jest.Mock).mockResolvedValue({
        value: "",
      });

      const response = await GET(mockRequest, mockParams);

      expect(response.status).toBe(500);
      expect(Logger.error).toHaveBeenCalledWith({
        message: "タスクファイルダウンロード: Azure Blob Storageコンテナ設定が見つからない",
        lovCode: LOV_CODE_AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF,
        taskId: mockTaskId,
        licenseId: mockLicenseId,
        containerLovExists: true,
        source: "GET /dashboard/tasks/[taskId]/download",
      });
    });
  });

  describe("異常系：SAS URL生成エラー", () => {
    /**
     * 試験観点：SAS URL生成失敗時のエラー処理
     * 試験対象：Azure Blob Storage操作失敗時の500エラー
     * 試験手順：
     *   1. SAS URL生成が失敗する状態でリクエストを送信
     *   2. 500エラーが返されることを確認
     * 確認項目：
     *   - 500ステータスコードが返されること
     *   - EMEC0007エラーメッセージが返されること
     *   - 適切なエラーログが出力されること
     */
    it("異常系：SAS URL生成が失敗した場合は500エラーを返す", async () => {
      const mockError = new Error("Azure Blob Storage connection failed");
      (BlobActions.generateBlobUrlWithSAS as jest.Mock).mockRejectedValue(mockError);

      const response = await GET(mockRequest, mockParams);

      expect(response.status).toBe(500);
      expect(await response.text()).toBe(PORTAL_ERROR_MESSAGES.EMEC0007);

      expect(Logger.error).toHaveBeenCalledWith({
        message: "タスクファイルダウンロード: 予期せぬエラーが発生",
        taskId: mockTaskId,
        errorMessage: mockError.message,
        errorStack: mockError.stack,
        errorName: mockError.name,
        source: "GET /dashboard/tasks/[taskId]/download",
      });
    });
  });

  describe("異常系：一般的な例外処理", () => {
    /**
     * 試験観点：予期しない例外発生時のエラー処理
     * 試験対象：システム例外での500エラー
     * 試験手順：
     *   1. セッション取得時に予期しない例外が発生する状態でリクエストを送信
     *   2. 500エラーが返されることを確認
     * 確認項目：
     *   - 500ステータスコードが返されること
     *   - EMEC0007エラーメッセージが返されること
     *   - 詳細なエラーログが出力されること
     */
    it("異常系：予期しない例外が発生した場合は500エラーを返す", async () => {
      const mockError = new TypeError("Unexpected system error");
      (getIronSession as jest.Mock).mockRejectedValue(mockError);

      const response = await GET(mockRequest, mockParams);

      expect(response.status).toBe(500);
      expect(await response.text()).toBe(PORTAL_ERROR_MESSAGES.EMEC0007);

      expect(Logger.error).toHaveBeenCalledWith({
        message: "タスクファイルダウンロード: 予期せぬエラーが発生",
        taskId: mockTaskId,
        errorMessage: mockError.message,
        errorStack: mockError.stack,
        errorName: mockError.name,
        source: "GET /dashboard/tasks/[taskId]/download",
      });
    });

    /**
     * 試験観点：LOV取得時の例外処理
     * 試験対象：データベース接続失敗時の500エラー
     * 試験手順：
     *   1. LOV取得時にデータベース例外が発生する状態でリクエストを送信
     *   2. 500エラーが返されることを確認
     * 確認項目：
     *   - 500ステータスコードが返されること
     *   - 適切なエラーログが出力されること
     */
    it("異常系：LOV取得時に例外が発生した場合は500エラーを返す", async () => {
      const mockError = new Error("Database connection timeout");
      (ServerDataLov.fetchLov as jest.Mock).mockRejectedValue(mockError);

      const response = await GET(mockRequest, mockParams);

      expect(response.status).toBe(500);
      expect(Logger.error).toHaveBeenCalledWith({
        message: "タスクファイルダウンロード: 予期せぬエラーが発生",
        taskId: mockTaskId,
        errorMessage: mockError.message,
        errorStack: mockError.stack,
        errorName: mockError.name,
        source: "GET /dashboard/tasks/[taskId]/download",
      });
    });
  });

  describe("境界値テスト", () => {
    /**
     * 試験観点：特殊文字を含むタスクIDの処理
     * 試験対象：URLエンコードされたタスクIDでの正常処理
     * 試験手順：
     *   1. 特殊文字を含むタスクIDでリクエストを送信
     *   2. 正常にSAS URLが生成されることを確認
     * 確認項目：
     *   - 特殊文字がそのまま処理されること
     *   - 正常にリダイレクトが実行されること
     */
    it("境界値：特殊文字を含むタスクIDでも正常に処理される", async () => {
      const specialTaskId = "<EMAIL>";
      const specialParams = { params: { taskId: specialTaskId } };

      await GET(mockRequest, specialParams);

      expect(BlobActions.generateBlobUrlWithSAS).toHaveBeenCalledWith(
        mockContainerName,
        [mockLicenseId, "exports", specialTaskId, "assetsfield_def.csv"]
      );

      expect(redirect).toHaveBeenCalledWith(mockSasUrl);
    });

    /**
     * 試験観点：長いタスクIDの処理
     * 試験対象：最大長に近いタスクIDでの正常処理
     * 試験手順：
     *   1. 長いタスクIDでリクエストを送信
     *   2. 正常に処理されることを確認
     * 確認項目：
     *   - 長いタスクIDが正常に処理されること
     *   - ファイルパスが正しく構築されること
     */
    it("境界値：長いタスクIDでも正常に処理される", async () => {
      const longTaskId = "a".repeat(100);
      const longParams = { params: { taskId: longTaskId } };

      await GET(mockRequest, longParams);

      expect(BlobActions.generateBlobUrlWithSAS).toHaveBeenCalledWith(
        mockContainerName,
        [mockLicenseId, "exports", longTaskId, "assetsfield_def.csv"]
      );

      expect(redirect).toHaveBeenCalledWith(mockSasUrl);
    });
  });
});
