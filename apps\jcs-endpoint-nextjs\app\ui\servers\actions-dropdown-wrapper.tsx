/**
 * @fileoverview サーバ操作ドロップダウンのラッパーコンポーネント
 * @description
 * 初期読み込み状態の管理：
 * - 初期読み込み中はSpinnerを表示し、テーブルの高さ変形を防ぐ
 * - 読み込み完了後にServerActionsDropdownを表示する
 * - ドロップダウンが表示されない場合は透明なプレースホルダーを表示し、テーブル行高の一致性を保つ
 *
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */
"use client";

import { useState, useEffect } from "react";
import ServerActionsDropdown from "./actions-dropdown";
import Spinner from "@/app/ui/spinner";
import { ServerType } from "@/app/lib/definitions";

interface ServerActionsDropdownWrapperProps {
  serverId: string;
  serverName: string;
  serverTypeCode: string;
  canExportOplog: boolean;
  maxExportDaysSpan: number;
}

/**
 * サーバ操作ドロップダウンのラッパーコンポーネント
 * 初期読み込み中はSpinnerを表示し、読み込み完了後にドロップダウンを表示する
 *
 * @param {object} props - コンポーネントのプロパティ
 * @param {string} props.serverId - サーバID
 * @param {string} props.serverName - サーバ名
 * @param {string} props.serverTypeCode - サーバタイプのコード
 * @param {boolean} props.canExportOplog - 現在ユーザーが操作ログエクスポート可能かどうか
 * @param {number} props.maxExportDaysSpan - 操作ログエクスポートの最大日数
 * @returns {JSX.Element} ラッパーコンポーネント
 */
export default function ServerActionsDropdownWrapper({
  serverId,
  serverName,
  serverTypeCode,
  canExportOplog,
  maxExportDaysSpan,
}: ServerActionsDropdownWrapperProps) {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // 短い遅延後に読み込み完了状態にする
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // ドロップダウンボタンの固定サイズクラス、プレースホルダーと実際のボタンサイズの一致を保証する
  const buttonSizeClasses = "w-56 min-w-[180px] max-w-[240px] px-3 py-2 rounded h-8";

  if (!isLoaded) {
    return (
      <div className={`${buttonSizeClasses} flex items-center justify-center`}>
        <Spinner />
      </div>
    );
  }

  // サーバタイプによる表示制御をチェック
  const showExportOplog = canExportOplog &&
    (serverTypeCode === ServerType.GENERAL_MANAGER || serverTypeCode === ServerType.RELAY_MANAGER);
  const canManageDefinition =
    serverTypeCode === ServerType.GENERAL_MANAGER || serverTypeCode === ServerType.RELAY_MANAGER;

  // いずれの操作も利用不可の場合は透明なプレースホルダーを表示
  if (!showExportOplog && !canManageDefinition) {
    return (
      <div className={`${buttonSizeClasses} invisible`}>
        {/* 透明なプレースホルダー、同じ高さを保つ */}
      </div>
    );
  }

  return (
    <ServerActionsDropdown
      serverId={serverId}
      serverName={serverName}
      serverTypeCode={serverTypeCode}
      canExportOplog={canExportOplog}
      maxExportDaysSpan={maxExportDaysSpan}
    />
  );
}
