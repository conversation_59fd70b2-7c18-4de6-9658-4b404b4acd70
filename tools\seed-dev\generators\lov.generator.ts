/**
 * @fileoverview LOV（List of Values）データ生成器
 * @description システムで使用される全てのLOVデータを生成し、データベースに投入する
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { PrismaClient } from '@prisma/client';
import { BaseGenerator } from './base.generator';
import lovData from '../data/lovs.json';

/**
 * LOVデータ項目の型定義
 */
interface LovDataItem {
  code: string;
  name: string;
  value: string;
  parentCode?: string;
}

/**
 * LOVデータ生成器クラス
 * システムで使用される全てのLOV（List of Values）データを生成する
 */
export class LovGenerator extends BaseGenerator {
  private readonly BATCH_SIZE = 50;

  constructor(prisma: PrismaClient) {
    super(prisma, 'LovGenerator');
  }

  /**
   * 生成器名を取得する
   * @returns 生成器名
   */
  getGeneratorName(): string {
    return 'LOVデータ';
  }

  /**
   * 生成予定のデータ数を取得する
   * @returns 生成予定数
   */
  getExpectedCount(): number {
    return lovData.length;
  }

  /**
   * 既存のLOVデータをクリーンアップする
   * 全てのLOVレコードを削除する
   */
  async cleanup(): Promise<void> {
    try {
      const deleteResult = await this.prisma.lov.deleteMany({});
      console.log(`既存LOVデータを削除しました (${deleteResult.count}件)`);
    } catch (error) {
      console.error('LOVデータのクリーンアップに失敗しました', error);
      throw error;
    }
  }

  /**
   * LOVデータを生成する
   * 階層構造を考慮して親→子の順序で生成する
   * @returns 生成されたデータの数
   */
  async generate(): Promise<number> {
    try {
      // LOVデータを親子関係で分類
      const { parentLovs, childLovs } = this.classifyLovData();
      
      console.log(`親LOV: ${parentLovs.length}件, 子LOV: ${childLovs.length}件`);

      // 親LOVを先に生成
      const parentCount = await this.generateParentLovs(parentLovs);
      
      // 子LOVを生成
      const childCount = await this.generateChildLovs(childLovs);
      
      const totalCount = parentCount + childCount;
      
      // データ整合性を検証
      await this.validateLovData();
      
      return totalCount;
    } catch (error) {
      console.error('LOVデータ生成中にエラーが発生しました', error);
      throw error;
    }
  }

  /**
   * LOVデータを親子関係で分類する
   * @returns 分類されたLOVデータ
   */
  private classifyLovData(): { parentLovs: LovDataItem[]; childLovs: LovDataItem[] } {
    const parentLovs = lovData.filter((item: LovDataItem) => !item.parentCode);
    const childLovs = lovData.filter((item: LovDataItem) => item.parentCode);
    
    return { parentLovs, childLovs };
  }

  /**
   * 親LOVデータを生成する
   * @param parentLovs 親LOVデータの配列
   * @returns 生成されたデータの数
   */
  private async generateParentLovs(parentLovs: LovDataItem[]): Promise<number> {
    console.log('親LOVデータの生成を開始します');
    
    return await this.generateInBatches(
      parentLovs.length,
      this.BATCH_SIZE,
      async (startIndex: number, count: number) => {
        const batch = parentLovs.slice(startIndex, startIndex + count);
        const createData = batch.map(lov => ({
          code: lov.code,
          name: lov.name,
          value: lov.value,
          parentCode: null,
          isEnabled: true,
        }));

        await this.prisma.lov.createMany({
          data: createData,
        });

        return createData;
      }
    );
  }

  /**
   * 子LOVデータを生成する
   * @param childLovs 子LOVデータの配列
   * @returns 生成されたデータの数
   */
  private async generateChildLovs(childLovs: LovDataItem[]): Promise<number> {
    console.log('子LOVデータの生成を開始します');
    
    return await this.generateInBatches(
      childLovs.length,
      this.BATCH_SIZE,
      async (startIndex: number, count: number) => {
        const batch = childLovs.slice(startIndex, startIndex + count);
        const createData = batch.map(lov => ({
          code: lov.code,
          name: lov.name,
          value: lov.value,
          parentCode: lov.parentCode!,
          isEnabled: true,
        }));

        await this.prisma.lov.createMany({
          data: createData,
        });

        return createData;
      }
    );
  }

  /**
   * LOVデータの整合性を検証する
   * 必要なLOVデータが正しく生成されているかチェックする
   */
  private async validateLovData(): Promise<void> {
    console.log('LOVデータの整合性検証を開始します');
    
    try {
      // 総数の確認
      const totalCount = await this.prisma.lov.count();
      if (totalCount !== lovData.length) {
        throw new Error(
          `LOVデータ数が一致しません。期待値: ${lovData.length}, 実際: ${totalCount}`
        );
      }

      // 重要なLOVデータの存在確認
      const criticalLovs = [
        'SERVER_TYPE',
        'TASK_STATUS',
        'TASK_TYPE',
        'LICENSE_TYPE',
        'TASK_STATUS.QUEUED',
        'TASK_STATUS.COMPLETED_SUCCESS',
        'TASK_TYPE.OPLOG_EXPORT',
        'SERVER_TYPE.GENERAL_MANAGER',
      ];

      for (const code of criticalLovs) {
        const lov = await this.prisma.lov.findUnique({
          where: { code },
        });

        if (!lov) {
          throw new Error(`重要なLOVデータが見つかりません: ${code}`);
        }
      }

      // 親子関係の整合性確認
      const childLovs = await this.prisma.lov.findMany({
        where: {
          parentCode: { not: null },
        },
        include: {
          parent: true,
        },
      });

      for (const childLov of childLovs) {
        if (!childLov.parent) {
          throw new Error(
            `親LOVが見つかりません: ${childLov.code} -> ${childLov.parentCode}`
          );
        }
      }

      console.log('LOVデータの整合性検証が完了しました');
    } catch (error) {
      console.error('LOVデータの整合性検証に失敗しました', error);
      throw error;
    }
  }

  /**
   * 指定された親コードの子LOVを取得する
   * @param parentCode 親コード
   * @returns 子LOVの配列
   */
  async getChildLovs(parentCode: string): Promise<any[]> {
    return await this.prisma.lov.findMany({
      where: {
        parentCode,
        isEnabled: true,
      },
      orderBy: {
        code: 'asc',
      },
    });
  }

  /**
   * 指定されたコードのLOVを取得する
   * @param code LOVコード
   * @returns LOVデータまたはnull
   */
  async getLovByCode(code: string): Promise<any | null> {
    return await this.prisma.lov.findUnique({
      where: {
        code,
        isEnabled: true,
      },
    });
  }

  /**
   * LOVデータの統計情報を取得する
   * @returns 統計情報
   */
  async getStatistics(): Promise<{
    totalCount: number;
    parentCount: number;
    childCount: number;
    enabledCount: number;
    disabledCount: number;
  }> {
    const [
      totalCount,
      parentCount,
      childCount,
      enabledCount,
      disabledCount,
    ] = await Promise.all([
      this.prisma.lov.count(),
      this.prisma.lov.count({ where: { parentCode: null } }),
      this.prisma.lov.count({ where: { parentCode: { not: null } } }),
      this.prisma.lov.count({ where: { isEnabled: true } }),
      this.prisma.lov.count({ where: { isEnabled: false } }),
    ]);

    return {
      totalCount,
      parentCount,
      childCount,
      enabledCount,
      disabledCount,
    };
  }
}
