/**
 * @fileoverview サーバー一覧ページの各行に表示される操作ドロップダウンメニューコンポーネント
 * @description
 * タスクメニューからタスクを選択：
 * - 操作ログのエクスポート：パラメータ入力モーダル表示 → パラメータ入力と検証 → 最終確認モーダル表示 → バックエンド処理呼び出し → 実行結果のUI反映
 * - 管理項目定義のインポート：パラメータ入力モーダル表示 → パラメータ入力と検証 → 最終確認モーダル表示 → バックエンド処理呼び出し → 実行結果のUI反映
 * - 管理項目定義のエクスポート：最終確認モーダル表示 → バックエンド処理呼び出し → 実行結果のUI反映
 *
 * タスクメニュー表示制御：
 * - 操作ログのエクスポート：ユーザーの基本契約プランコード（STANDARD/LIGHT_B）かつサーバ種別（JP1/ITDM2統括マネージャ・中継マネージャ）の場合のみ表示
 * - 管理項目定義のインポート/エクスポート：サーバ種別（JP1/ITDM2統括マネージャ・中継マネージャ）の場合のみ表示
 * - いずれの操作も利用不可の場合はボタン自体を非表示
 *
 * バリデーション処理：
 * - 操作ログエクスポート：開始日・終了日の必須入力、日付順序、最大日数超過チェック
 * - 管理項目定義インポート：ファイルの必須入力チェック
 *
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */
"use client";

import {
  FORM_FIELD_NAMES,
  PORTAL_ERROR_MESSAGES,
  ServerType,
} from "@/app/lib/definitions";
import OperationLogExportModal from "./modals/operation-log-export-modal";
import ManagementDefinitionImportModal from "./modals/management-definition-import-modal";
import ConfirmModal from "@/app/ui/ConfirmModal";
import { useState, useEffect } from "react";
import { createTask } from "@/app/lib/actions/tasks";
import { TASK_TYPE } from "@/app/lib/definitions";
import MessageModal from "@/app/ui/message-modal";
import { initDropdowns } from "flowbite";
import { formatMessage } from "@/app/lib/utils";
import { useServerAction } from "@/app/hooks/use-server-action";

/**
 * サーバ行内操作ドロップダウンコンポーネント
 * 本コンポーネントは、各サーバ行に対して利用可能な操作（例：操作ログのエクスポート、管理項目定義のインポート/エクスポート）を
 * FlowbiteのDropdown UI（hoverトリガー、黒色メニュー、幅統一）で提供します。
 *
 * ビジネスルール：
 * - 操作ログのエクスポートは、canExportOplog=true かつサーバタイプがGENERAL_MANAGERまたはRELAY_MANAGERの場合のみ表示されます。
 * - 管理項目定義のインポート/エクスポートは、サーバタイプがGENERAL_MANAGERまたはRELAY_MANAGERの場合のみ表示されます。
 * - いずれの操作も利用不可の場合はボタン自体を非表示にします。
 *
 * @param {string} serverId - サーバID
 * @param {string} serverName - サーバ名
 * @param {string} serverTypeCode - サーバタイプのコード
 * @param {boolean} canExportOplog - 現在ユーザーが操作ログエクスポート可能かどうか
 * @param {number} maxExportDaysSpan - 操作ログエクスポートの最大日数

 * @returns {JSX.Element|null} 行内操作ドロップダウンメニュー（操作不可時はnull）
 */
export default function ServerActionsDropdown(props: {
  serverId: string;
  serverName: string;
  serverTypeCode: string;
  canExportOplog: boolean;
  maxExportDaysSpan: number;
}) {
  // propsを分割代入で取得
  const {
    serverId,
    serverName,
    serverTypeCode,
    canExportOplog,
    maxExportDaysSpan,
  } = props;
  // 状態管理用のReactフックを初期化
  const [isClient, setIsClient] = useState(false);
  const [message, setMessage] = useState<{
    isError: boolean;
    text: string;
  } | null>(null);

  // 統一 Server Action 実行フック
  const { execute: executeServerAction, isLoading } = useServerAction({
    onSuccess: (result) => {
      setMessage({ isError: !result.success, text: result.message || "" });
    },
    onError: (error) => {
      setMessage({
        isError: true,
        text: error.message,
      });
    }
  });

  useEffect(() => {
    setIsClient(true);
  }, []);

  // マウント後にFlowbiteのDropdownを初期化
  useEffect(() => {
    if (isClient) {
      try {
        if (typeof initDropdowns === "function") {
          initDropdowns();
        } else if (
          typeof window !== "undefined" &&
          (window as any).Flowbite &&
          typeof (window as any).Flowbite.init === "function"
        ) {
          (window as any).Flowbite.init();
        }
      } catch (e) {
        // 初期化時の例外を無視
      }
    }
  }, [isClient]);

  // ユーザーの基本契約プランコードとサーバー種別による操作ログエクスポート表示制御
  const showExportOplog =
    canExportOplog &&
    (serverTypeCode === ServerType.GENERAL_MANAGER ||
      serverTypeCode === ServerType.RELAY_MANAGER);

  // サーバー種別による管理項目定義インポート/エクスポート表示制御
  const canManageDefinition =
    serverTypeCode === ServerType.GENERAL_MANAGER ||
    serverTypeCode === ServerType.RELAY_MANAGER;

  // モーダルの表示状態を管理（TASK_TYPEと対応）
  const [isOplogExportModalOpen, setOplogExportModalOpen] = useState(false);
  const [isMgmtItemImportModalOpen, setMgmtItemImportModalOpen] =
    useState(false);

  const [showOplogExportConfirm, setShowOplogExportConfirm] = useState(false);
  const [showMgmtItemExportConfirm, setShowMgmtItemExportConfirm] =
    useState(false);
  const [pendingMgmtItemImportParams, setPendingMgmtItemImportParams] =
    useState<{ file: File; originalFileName: string } | null>(null);
  const [showMgmtItemImportConfirm, setShowMgmtItemImportConfirm] =
    useState(false);

  // タスクパラメータ状態
  const [pendingOplogExportParams, setPendingOplogExportParams] = useState<{
    exportStartDate: string;
    exportEndDate: string;
  } | null>(null);

  /**
   * 操作ログエクスポートパラメータモーダルでパラメータが送信された際の処理
   * @param {object} params - エクスポートパラメータ（開始日・終了日）
   * @returns {Promise<void>}
   * @description
   * 操作ログエクスポートパラメータを受け取り、createTaskを呼び出してタスクを作成する。
   */
  const handleOplogExportParamsSubmit = (params: {
    exportStartDate: string;
    exportEndDate: string;
  }) => {
    setOplogExportModalOpen(false);
    setPendingOplogExportParams(params);
    setShowOplogExportConfirm(true);
  };

  /**
   * 操作ログエクスポート二次確認OK時の処理
   * @returns {Promise<void>}
   * @description
   * エクスポートタスクを作成し、結果をメッセージモーダルで表示する。
   */
  const handleOplogExportConfirm = async () => {
    if (!pendingOplogExportParams) return;

    await executeServerAction(async () => {
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.OPLOG_EXPORT);
      formData.append("serverId", serverId);
      formData.append(
        "exportStartDate",
        pendingOplogExportParams.exportStartDate,
      );
      formData.append("exportEndDate", pendingOplogExportParams.exportEndDate);

      return await createTask(formData);
    });

    // 成功時のクリーンアップ
    setShowOplogExportConfirm(false);
    setPendingOplogExportParams(null);
  };

  /**
   * 操作ログエクスポート二次確認キャンセル時の処理
   * @description
   * 確認モーダルを閉じ、表単モーダルを再表示する。
   */
  const handleOplogExportCancel = () => {
    setShowOplogExportConfirm(false);
    setOplogExportModalOpen(true);
  };
  /**
   * 管理項目定義インポートパラメータモーダルでパラメータが送信された際の処理
   * @param {object} params - インポートパラメータ（ファイル・元ファイル名）
   * @description
   * インポートパラメータを受け取り、二次確認モーダルを表示する。
   */
  const handleMgmtItemImportParamsSubmit = (params: {
    file: File;
    originalFileName: string;
  }) => {
    setMgmtItemImportModalOpen(false);
    setPendingMgmtItemImportParams(params);
    setShowMgmtItemImportConfirm(true);
  };

  /**
   * 管理項目定義インポート二次確認OK時の処理
   * @returns {Promise<void>}
   * @description
   * インポートタスクを作成し、結果をメッセージモーダルで表示する。
   */
  const handleMgmtItemImportConfirm = async () => {
    if (!pendingMgmtItemImportParams) return;

    await executeServerAction(async () => {
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_IMPORT);
      formData.append("serverId", serverId);
      formData.append("importFile", pendingMgmtItemImportParams.file);
      formData.append(
        "originalFileName",
        pendingMgmtItemImportParams.originalFileName,
      );

      return await createTask(formData);
    });

    // 成功時のクリーンアップ
    setShowMgmtItemImportConfirm(false);
    setPendingMgmtItemImportParams(null);
  };

  /**
   * 管理項目定義インポート二次確認キャンセル時の処理
   * @description
   * 確認モーダルを閉じ、表単モーダルを再表示する。
   */
  const handleMgmtItemImportCancel = () => {
    setShowMgmtItemImportConfirm(false);
    setMgmtItemImportModalOpen(true);
  };

  /**
   * 管理項目定義エクスポート二次確認OK時の処理
   * @returns {Promise<void>}
   * @description
   * エクスポートタスクを作成し、結果をメッセージモーダルで表示する。
   */
  const handleMgmtItemExportConfirm = async () => {
    await executeServerAction(async () => {
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_EXPORT);
      formData.append("serverId", serverId);

      return await createTask(formData);
    });

    // 成功時のクリーンアップ
    setShowMgmtItemExportConfirm(false);
  };

  /**
   * 管理項目定義エクスポート二次確認キャンセル時の処理
   * @description
   * 確認モーダルを閉じ、保留中のパラメータをクリアする。
   */
  const handleMgmtItemExportCancel = () => {
    setShowMgmtItemExportConfirm(false);
  };

  // いずれの操作も利用不可の場合は、ドロップダウン自体を表示しない
  if (!showExportOplog && !canManageDefinition) {
    return null;
  }

  // 各サーバ行ごとに一意なIDを生成
  const dropdownId = `dropdown-actions-${serverId}`;
  const buttonId = `dropdownButton-${serverId}`;

  // 結果表示用のモーダル、パラメータ入力用モーダル、確認モーダルを含むドロップダウンメニューをレンダリング
  // ボタンとメニューのDOMはisClient条件下で描画
  return (
    <div className="relative inline-block text-left w-56 min-w-[180px] max-w-[240px]">
      <MessageModal
        isOpen={!!message && !!message.text}
        onClose={() => setMessage(null)}
        message={!message?.isError ? message?.text : undefined}
        error={message?.isError ? message?.text : undefined}
      />
      <OperationLogExportModal
        isOpen={isOplogExportModalOpen}
        maxExportDaysSpan={maxExportDaysSpan}
        initialValues={pendingOplogExportParams || undefined}
        onSubmit={handleOplogExportParamsSubmit}
        onClose={() => {
          setOplogExportModalOpen(false);
          setPendingOplogExportParams(null);
        }}
      />
      <ManagementDefinitionImportModal
        isOpen={isMgmtItemImportModalOpen}
        initialValues={pendingMgmtItemImportParams || undefined}
        onSubmit={handleMgmtItemImportParamsSubmit}
        onClose={() => {
          setMgmtItemImportModalOpen(false);
          setPendingMgmtItemImportParams(null);
        }}
      />
      <ConfirmModal
        isOpen={showOplogExportConfirm}
        title="操作ログのエクスポート"
        message={`${serverName}の操作ログをエクスポートします。\nよろしいですか？`}
        onConfirm={handleOplogExportConfirm}
        onCancel={handleOplogExportCancel}
        confirmText="OK"
        cancelText="キャンセル"
        loading={isLoading}
      />
      <ConfirmModal
        isOpen={showMgmtItemExportConfirm}
        title="管理項目定義のエクスポート"
        message={`${serverName}の管理項目定義をエクスポートします。\nよろしいですか？`}
        onConfirm={handleMgmtItemExportConfirm}
        onCancel={handleMgmtItemExportCancel}
        confirmText="OK"
        cancelText="キャンセル"
        loading={isLoading}
      />
      <ConfirmModal
        isOpen={showMgmtItemImportConfirm}
        title="管理項目定義のインポート"
        message={`${serverName}の管理項目定義をインポートします。\nよろしいですか？`}
        onConfirm={handleMgmtItemImportConfirm}
        onCancel={handleMgmtItemImportCancel}
        confirmText="OK"
        cancelText="キャンセル"
        loading={isLoading}
      />
      {isClient ? (
        <>
          <button
            id={buttonId}
            data-dropdown-toggle={dropdownId}
            data-dropdown-trigger="hover"
            // ボタンスタイルを設定
            className="rounded bg-gradient-dark drop-shadow-dark shadow-dark px-3 py-2 text-center text-xs font-medium text-white hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300 w-56 min-w-[180px] max-w-[240px] flex items-center justify-between disabled:opacity-50"
            type="button"
            aria-haspopup="true"
            aria-expanded="false"
          >
            タスクを選択
            <svg
              className="w-4 h-4 ml-2"
              aria-hidden="true"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>
          <div
            id={dropdownId}
            className="z-50 hidden bg-gray-800 divide-y divide-gray-700 rounded-lg shadow w-56 min-w-[180px] max-w-[240px] absolute right-0 mt-2"
            role="menu"
            aria-labelledby={buttonId}
          >
            <ul className="py-2 text-sm text-white" role="none">
              {showExportOplog && (
                <li>
                  <button
                    className="w-full text-left px-4 py-2 bg-gray-800 hover:bg-gray-900 text-white disabled:opacity-50"
                    role="menuitem"
                    onClick={() => {
                      setOplogExportModalOpen(true);
                    }}
                  >
                    操作ログのエクスポート
                  </button>
                </li>
              )}
              {canManageDefinition && (
                <>
                  <li>
                    <button
                      className="w-full text-left px-4 py-2 bg-gray-800 hover:bg-gray-900 text-white disabled:opacity-50"
                      role="menuitem"
                      onClick={() => {
                        setMgmtItemImportModalOpen(true);
                      }}
                    >
                      管理項目定義のインポート
                    </button>
                  </li>
                  <li>
                    <button
                      className="w-full text-left px-4 py-2 bg-gray-800 hover:bg-gray-900 text-white disabled:opacity-50"
                      role="menuitem"
                      onClick={() => setShowMgmtItemExportConfirm(true)}
                    >
                      管理項目定義のエクスポート
                    </button>
                  </li>
                </>
              )}
            </ul>
          </div>
        </>
      ) : null}
    </div>
  );
}
