# 组件：Runbook作业监控函数 (Runbook Monitor Function)

## 1. 概要 (Overview)

### 1.1. 目的 (Purpose)
本Azure Function (`RunbookMonitorFunc`) 作为一个定时触发的服务，负责主动监控所有已提交到Azure Automation且当前状态为`RUNBOOK_SUBMITTED`的后台任务。其核心职责包括：轮询这些作业在Azure Automation中的执行状态，检测作业是否已完成（成功或失败）或是否已运行超时，并将这些原始的作业结束事件（包含`Task.id`及结果/错误/超时指示）作为消息发送到Azure Service Bus的`RunbookStatusQueue`队列，供后续的[`RunbookProcessorFunc`](./function-runbook-processor.md)进行最终处理。同时，本函数会将已轮询到最终状态或检测到超时的任务在数据库中的状态从`RUNBOOK_SUBMITTED`更新为`RUNBOOK_PROCESSING`。

### 1.2. 范围 (Scope)
本文档详细描述`RunbookMonitorFunc`的技术设计，包括其定时触发机制、核心轮询与监控逻辑、与Azure SQL Database及Azure Automation API的交互、向Service Bus发送消息的机制、错误处理以及相关的配置项。

### 1.3. 组件类型 (Component Type)
*   Azure Function (Node.js/TypeScript 运行时)
*   触发器: 定时触发器 (Timer Trigger)

### 1.4. 名词定义 (Glossary References)
*   **RunbookStatusQueue**: Azure Service Bus队列，用于接收本函数发送的、包含Runbook原始执行结果的消息。其名称由环境变量 `SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME` 指定。
*   **RUNBOOK_TIMEOUT_SECONDS**: 环境变量，定义Runbook作业允许的最大执行时长。本函数使用此值判断作业是否超时。
*   **RUNBOOK_MONITOR_INTERVAL_SECONDS**: 环境变量，定义本函数定时触发器的执行间隔。
*   其他相关术语请参考项目核心术语表 [`项目术语表`](../../../definitions/glossary.md)。
*   本文档中所有用户可见的错误和提示信息，均引用自项目核心错误消息定义文档 [`错误消息定义`](../../../definitions/error-messages.md)。
*   本文档中所有环境变量的定义，均参考项目核心环境变量指南 [`环境变量指南`](../../../guides/environment-variables.md)。
*   任务状态码常量（如 `TASK_STATUS_RUNBOOK_SUBMITTED_CODE`, `TASK_STATUS_RUNBOOK_PROCESSING_CODE`）及其他内部常量定义在 `app/lib/definitions.ts`。

## 2. 功能规格 (Functional Specifications)

### 2.1. 主要流程 (Main Process Flow)

```mermaid
graph TD
    A["定时触发器触发<br/>(间隔: RUNBOOK_MONITOR_INTERVAL_SECONDS)"] --> B{RunbookMonitorFunc被执行};
    B --> C["1.从Task表查询所有状态为<br/>'RUNBOOK_SUBMITTED'的任务列表"];
    C -- "无此类任务 或 查询失败" --> EndEarly["结束处理 (记录日志)"];
    C -- "查询到任务列表" --> D["2.遍历每个'RUNBOOK_SUBMITTED'状态的任务"];
    D --> E{"2.1 检查任务是否超时?<br/>(now() - task.startedAt > RUNBOOK_TIMEOUT_SECONDS)"};
    E -- 是 (超时) --> F_Timeout["2.1.1 标记为超时"];
    F_Timeout --> G_SendMessage;
    E -- 否 (未超时) --> H_PollStatus["2.2 调用Azure Automation API<br/>获取该作业(Task.id)的当前状态和输出"];
    H_PollStatus -- "API调用失败" --> I_ApiError["记录API错误, 跳过此任务本次轮询"];
    H_PollStatus -- "API调用成功" --> J_JobStatus{"2.2.1 作业状态是?"};
    J_JobStatus -- "仍在运行 (New, Activating, Running, Blocked, Disconnected)" --> K_Skip["跳过此任务本次轮询, 继续下一个"];
    J_JobStatus -- "已结束 (Completed, Failed, Stopped, Suspended等)" --> L_Finished["2.2.2 标记为已结束, 提取结果/错误"];
    L_Finished --> G_SendMessage["2.3 构造原始结果消息<br/>(含taskId, automationStatus, processedStatusForQueue, resultData)"];
    G_SendMessage --> M_SendToQueue["2.4 将消息发送到RunbookStatusQueue"];
    M_SendToQueue -- "发送失败" --> N_QueueError["记录队列发送错误, 跳过此任务本次轮询"];
    M_SendToQueue -- "发送成功" --> O_UpdateTaskDb["2.5 更新Task表状态为'RUNBOOK_PROCESSING'<br/>(针对已发送消息的任务)"];
    O_UpdateTaskDb -- "更新失败" --> P_DbUpdateError["记录DB更新错误"];
    O_UpdateTaskDb -- "更新成功" --> K_Skip;
    I_ApiError --> K_Skip;
    P_DbUpdateError --> K_Skip;
    K_Skip --> D;
    D -- "所有任务处理完毕" --> Q_End["3.记录本次轮询执行摘要, 结束处理"];
    EndEarly --> Q_End;
```
**图 2.1: RunbookMonitorFunc 主要处理流程图**

### 2.2. 功能点描述 (Functional Points)
1.  **定时触发**: 函数按照环境变量 `RUNBOOK_MONITOR_INTERVAL_SECONDS` (默认300秒/5分钟) 配置的固定间隔自动执行。
2.  **查询目标任务**: 从数据库`Task`表获取所有当前`status`为 `TASK_STATUS_RUNBOOK_SUBMITTED_CODE` ("RUNBOOK_SUBMITTED") 的任务记录。
3.  **逐任务监控与处理**: 对每个查询到的任务执行以下操作：
    *   **超时检测**:
        *   比较当前时间与任务的`startedAt`时间戳。如果运行时间已超过环境变量 `RUNBOOK_TIMEOUT_SECONDS` (默认18000秒/5小时) 定义的最大允许时长，则将此任务视为已超时。
        *   对于超时的任务，构造一个指示“因超时而被中止”的原始结果消息。`processedStatusForQueue` 字段将被设置为特定值（如 `'TIMEOUT_DETECTED_BY_MONITOR'`）以供`RunbookProcessorFunc`识别。
    *   **轮询Azure Automation作业状态 (若未超时)**:
        *   使用任务的`id` (即`Task.id`，它也是Azure Automation作业的ID) 调用Azure Automation API（例如，`Job_Get` 操作）获取该作业的当前状态、输出流和错误流。
        *   **若作业仍在运行**: (例如，状态为 `New`, `Activating`, `Running`, `Blocked`, `Disconnected`)，则本次轮 statistique跳过此作业，不进行任何处理。
        *   **若作业已结束**: (例如，状态为 `Completed`, `Failed`, `Stopped`, `Stopping`, `Suspended`, `Suspending`)，则提取作业的最终状态、所有输出流内容（通常为JSON格式的业务结果或文件路径列表）、以及所有错误流内容。构造包含这些原始结果的事件消息。`processedStatusForQueue` 字段可设置为类似 `'COMPLETED_FROM_AUTOMATION'` 或 `'FAILED_FROM_AUTOMATION'` 的值。
4.  **发送结果消息到队列**:
    *   对于检测到超时或轮询到已结束状态的作业，将构造好的原始结果消息（包含`taskId`、Automation作业状态、`processedStatusForQueue`、以及结果/错误数据）发送到Azure Service Bus的`RunbookStatusQueue`队列（队列名称由环境变量 `SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME` 指定）。
5.  **更新任务数据库状态**:
    *   对于已成功发送结果消息到`RunbookStatusQueue`的任务，将其在`Task`表中的`status`从 `TASK_STATUS_RUNBOOK_SUBMITTED_CODE` 更新为 `TASK_STATUS_RUNBOOK_PROCESSING_CODE` ("RUNBOOK_PROCESSING")。这表示该任务的原始结果已捕获，等待最终处理。
6.  **日志记录**: 详细记录每次轮询的开始和结束、查询到的任务数量、每个任务的处理情况（超时、获取状态、发送消息、更新DB）、遇到的任何API调用错误、队列发送错误或数据库更新错误。

### 2.3. 业务规则 (Business Rules)
*   本函数只关注状态为`RUNBOOK_SUBMITTED`的任务。
*   超时判断基于配置的环境变量`RUNBOOK_TIMEOUT_SECONDS`。
*   所有从Azure Automation获取的作业结束事件（无论成功、失败还是其他异常结束状态）以及本函数检测到的超时事件，都将被统一格式化并发送到`RunbookStatusQueue`。
*   一旦任务的原始结果被发送到队列，其在`Task`表中的状态即变为`RUNBOOK_PROCESSING`，本函数后续轮询将不再处理此任务。

### 2.4. 前提条件 (Preconditions)
*   Azure Function 定时触发器已正确配置。
*   Azure SQL Database (`Task`表) 正常运行且可访问。
*   Azure Automation 服务正常运行，Function App的托管身份或配置的服务主体具有调用Azure Automation API获取作业状态和输出的权限。
*   Azure Service Bus (`RunbookStatusQueue`) 正常运行，Function App具有向此队列发送消息的权限。
*   所有必需的环境变量（特别是 `RUNBOOK_MONITOR_INTERVAL_SECONDS`, `RUNBOOK_TIMEOUT_SECONDS`, `SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME`）已正确设置。

### 2.5. 制约事项 (Constraints)
*   本函数的执行频率（由`RUNBOOK_MONITOR_INTERVAL_SECONDS`控制）直接影响到作业完成状态被检测到的延迟。间隔过长可能导致用户感知到任务结果更新不及时。间隔过短可能增加对Azure Automation API的调用频率和成本。
*   Azure Automation API本身可能有速率限制，如果同时有大量作业需要轮询，本函数的设计需要考虑分批处理或避免过于频繁的API调用。
*   本函数执行时间受Azure Functions的超时限制（通过环境变量 `FUNCTION_TIMEOUT_SECONDS` 配置）。一次轮询处理的任务数量不宜过多，以防单次执行超时。

### 2.6. 注意事项 (Notes)
*   **错误处理的健壮性**: 单个任务在轮询或处理过程中发生的错误（如API调用失败、消息发送失败、DB更新失败）不应中断对其他任务的监控。函数应能优雅处理这些错误，记录日志，并继续处理列表中的下一个任务。
*   **幂等性考虑**: 虽然本函数的主要操作（查询、发送消息、更新状态）在正常流程下不易产生重复处理问题，但如果因为错误重试导致函数重新执行相同的轮询周期，需要确保不会对同一个已处理的任务重复发送消息或更新状态。通过在发送消息后立即更新任务状态为`RUNBOOK_PROCESSING`，可以有效避免重复处理。
*   **Azure Automation 作业输出处理**: Runbook脚本的输出（通过`Write-Output`）是本函数获取业务结果的关键。Runbook应确保其输出格式规范、易于解析（例如，JSON字符串）。如果输出内容过大，可能需要考虑Runbook将结果写入Azure Files工作区，并通过输出流仅返回文件路径的元数据。

---

## 3. 技术设计与实现细节 (Technical Design & Implementation)

### 3.1. 技术栈与依赖 (Technology Stack & Dependencies)
*   **运行时**: Azure Functions (Node.js/TypeScript)。
*   **触发器**: 定时触发器 (`schedule` 在 `function.json` 中通过 `%RUNBOOK_MONITOR_INTERVAL_SECONDS%` CRON表达式或TimeSpan格式配置，需转换为有效格式)。
*   **数据库交互**: Prisma ORM (通过 `app/lib/data.ts` 或直接 `app/lib/prisma.ts`)。
*   **Azure Automation交互**: 使用 `@azure/arm-automation` (Azure SDK for JavaScript/TypeScript - Management Plane) 或通过REST API调用。
*   **消息队列SDK**: `@azure/service-bus` SDK (用于向`RunbookStatusQueue`发送消息)。
*   **日志**: `app/lib/logger.ts`。
*   **核心定义与常量**: `app/lib/definitions.ts`。

### 3.2. 详细界面元素定义 (Detailed UI Element Definitions)
*   本组件为后端Azure Function，无直接用户界面。

### 3.3. 详细事件处理逻辑 (Detailed Event Handling)
*   本组件由定时器触发，其核心处理逻辑见3.6节。

### 3.4. 数据结构与API/Server Action交互 (Data Structures and API/Server Action Interaction)

#### 3.4.1. 输出消息结构 (to `RunbookStatusQueue`)
发送到`RunbookStatusQueue`的消息体为一个JSON对象，预期包含以下字段：

```typescript
// 示例消息结构 (由 RunbookMonitorFunc 发送)
interface RunbookJobResultForQueue {
  taskId: string; // 即 Azure Automation 作业ID (Task.id)
  runbookJobStatusFromAutomation: string | null; // 从Azure Automation API获取的原始作业状态 (e.g., "Completed", "Failed", "Stopped")。若为监控器检测到的超时，此字段可能为null或特定值。
  processedStatusForQueue: 
    | "COMPLETED_FROM_AUTOMATION" 
    | "FAILED_FROM_AUTOMATION" 
    | "STOPPED_ETC_FROM_AUTOMATION" // 代表 Stopped, Suspended 等需要干预的状态
    | "TIMEOUT_DETECTED_BY_MONITOR"; // 由本监控函数检测到的超时 // 此字段供[`RunbookProcessorFunc`](./function-runbook-processor.md)识别
  resultData?: { // 可选，根据作业状态和类型
    outputStreams?: string[]; // Runbook的標準輸出流內容數組
    errorStreams?: string[];  // Runbook的錯誤流內容數組
    // (未来可扩展) outputFiles?: { name: string; pathInWorkspace: string; size?: number }[]; // 如果Runbook将结果写入工作区文件
    errorMessageFromFile?: string | null; // 如果Runbook约定将详细错误写入工作区的特定文件 (如 exports/errordetail.txt)
    rawErrorMessageFromJob?: string | null; // 从Automation作业原始错误流提取的文本
  } | null;
}
```

#### 3.4.2. 与数据库的交互 (`Task` 表)
详见3.5节。

#### 3.4.3. 与Azure Automation API的交互 (关键操作)
*   **`Job_Get`**: 获取指定作业ID (`Task.id`) 的详细信息，包括当前状态 (`properties.status`)。
*   **`Job_GetOutput`**: 获取指定作业ID的输出流内容。
*   **`Job_GetRunbookContent`** (可能间接相关，用于获取Runbook定义，但不直接用于监控)。
*   **`JobStream_ListByJob`**: (替代方案) 获取作业的所有流（输出、错误、警告、调试、进度、详细）。

#### 3.4.4. 序列图 (Mermaid `sequenceDiagram`)

```mermaid
sequenceDiagram
    participant Timer as "定时触发器"
    participant MonitorFunc as "RunbookMonitorFunc"
    participant Database as "Azure SQL Database (Task表)"
    participant AzAutomation as "Azure Automation API"
    participant SBStatusQ as "Azure Service Bus (RunbookStatusQueue)"

    Timer->>MonitorFunc: 触发执行
    activate MonitorFunc
    MonitorFunc->>Database: 1. 查询状态为 'RUNBOOK_SUBMITTED' 的Tasks
    activate Database
    Database-->>MonitorFunc: Task列表 (e.g., taskA, taskB)
    deactivate Database

    loop 对每个Task (taskX)
        MonitorFunc->>MonitorFunc: 2.1 检查 taskX 是否超时?
        alt taskX 超时
            MonitorFunc->>MonitorFunc: 2.1.1 构造超时结果消息
        else taskX 未超时
            MonitorFunc->>AzAutomation: 2.2.1 获取 taskX (JobId) 的状态/输出
            activate AzAutomation
            AzAutomation-->>MonitorFunc: 作业状态和输出
            deactivate AzAutomation
            alt 作业仍在运行
                MonitorFunc->>MonitorFunc: 2.2.2 跳过此任务
            else 作业已结束 (Completed/Failed/etc.)
                MonitorFunc->>MonitorFunc: 2.2.3 构造结束结果消息
            end
        end
        
        opt 已构造结果消息 (超时或结束)
            MonitorFunc->>SBStatusQ: 2.3 发送结果消息到 RunbookStatusQueue
            activate SBStatusQ
            SBStatusQ-->>MonitorFunc: 发送成功/失败
            deactivate SBStatusQ
            alt 消息发送成功
                MonitorFunc->>Database: 2.4 更新 taskX 状态为 'RUNBOOK_PROCESSING'
                activate Database
                Database-->>MonitorFunc: 更新成功/失败
                deactivate Database
            end
        end
    end
    MonitorFunc->>MonitorFunc: 3. 记录轮询摘要
    deactivate MonitorFunc
```

### 3.5. 数据库设计与访问详情 (Database Design and Access Details)

#### 3.5.1. 相关表引用
*   **`Task` 表**: 主要目标表，用于读取状态为`RUNBOOK_SUBMITTED`的任务，并更新其状态为`RUNBOOK_PROCESSING`。

#### 3.5.2. 主要数据查询/变更逻辑

1.  **查询待监控的任务**:
    *   **目的**: 获取所有需要检查状态的Runbook作业。
    *   **Prisma 操作**:
        ```typescript
        const pendingTasks = await prisma.task.findMany({
          where: { status: TASK_STATUS_RUNBOOK_SUBMITTED_CODE },
          // 可选: orderBy: { startedAt: 'asc' } 以优先处理较早开始的任务
          // 可选: take: BATCH_SIZE (如果一次处理任务过多可能导致Function超时)
        });
        ```
2.  **更新任务状态**:
    *   **目的**: 将已成功发送结果到`RunbookStatusQueue`的任务标记为不再需要本函数监控。
    *   **Prisma 操作**:
        ```typescript
        await prisma.task.update({
          where: { id: taskId },
          data: { status: TASK_STATUS_RUNBOOK_PROCESSING_CODE },
        });
        ```
*   **事务管理**:
    *   查询操作是只读的。
    *   更新任务状态的操作是针对单个任务的，通常不需要显式事务包裹。如果一次轮询中更新多个任务状态，可以考虑将所有成功的更新操作收集起来，使用`prisma.$transaction([...updates])`批量执行，但这会增加逻辑复杂性，单个更新失败可能导致整个批次失败。更简单的做法是逐个更新，单个更新失败不影响其他。

### 3.6. 关键后端逻辑/算法 (Key Backend Logic/Algorithms)

`RunbookMonitorFunc`的核心算法是一个循环处理流程：

1.  **初始化**: 记录轮询开始。
2.  **获取待办列表**: 从数据库查询所有`Task.status === 'RUNBOOK_SUBMITTED'`的任务。
3.  **迭代处理**: 对列表中的每个`task`：
    a.  **超时检查**:
        i.  计算 `elapsedTime = new Date() - new Date(task.startedAt)`。
        ii. 如果 `elapsedTime > (RUNBOOK_TIMEOUT_SECONDS * 1000)`，则认为任务超时。
            1.  构造 `RunbookJobResultForQueue` 消息，`processedStatusForQueue` 设为 `'TIMEOUT_DETECTED_BY_MONITOR'`。
            2.  继续步骤 (d)。
    b.  **轮询Automation API**: (如果未超时)
        i.  调用 `AutomationClient.job.get(resourceGroupName, automationAccountName, task.id)` 获取作业详情。
        ii. 根据返回的 `job.properties.status` 判断：
            *   如果状态表示仍在运行（如 `Running`, `Activating`），则记录日志，跳到下一个`task`。
            *   如果状态表示已结束（如 `Completed`, `Failed`, `Stopped`），则调用 `AutomationClient.jobStream.listByJob(...)` (或 `job.getOutput` 等) 获取输出流和错误流。
                1. 构造 `RunbookJobResultForQueue` 消息，`runbookJobStatusFromAutomation` 设为API返回的原始状态，`processedStatusForQueue` 根据原始状态映射（例如`Completed` -> `COMPLETED_FROM_AUTOMATION`），`resultData` 填充流内容。
                2. 继续步骤 (d)。
        iii. 如果API调用失败，记录错误，跳到下一个`task`。
    c.  (此分支由a或b触发) **发送消息与更新DB**:
        i.  将构造好的`RunbookJobResultForQueue`消息发送到`RunbookStatusQueue`。
        ii. 如果消息发送成功，则调用`prisma.task.update()`将当前`task.id`的状态更新为`RUNBOOK_PROCESSING`。记录成功。
        iii. 如果消息发送失败或DB更新失败，记录错误。不改变任务原状态，允许下次轮询重试。
4.  **结束**: 记录本次轮询处理的任务数、成功发送消息数、遇到的错误数等摘要信息。

### 3.7. 错误处理详情 (Detailed Error Handling)

| #  | 错误场景描述 (中文) | 触发位置 | 引用的消息ID/消息键 (用户可见部分，本函数不直接更新用户可见消息) | 系统内部处理及日志记录建议 (中文) | 日志级别 |
|----|-----------------|----------|------------------------------------|---------------------------------|-----------|
| 1  | 查询`Task`表失败 (获取待监控列表时) | `prisma.task.findMany` | (内部错误) | 记录详细DB错误。函数应优雅退出本次执行，等待下次定时触发。 | ERROR |
| 2  | 调用Azure Automation API失败 (网络、权限、无效JobId等) | `AutomationClient.job.get` 等 | (内部错误) | 记录详细的API错误响应或SDK异常。跳过当前任务，继续处理其他任务。 | WARN/ERROR |
| 3  | 发送消息到`RunbookStatusQueue`失败 | `ServiceBusClient.createSender.sendMessages` | (内部错误) | 记录详细的Service Bus SDK错误。**不**更新该任务的DB状态，以便下次轮询重试。 | ERROR |
| 4  | 更新`Task`表状态为`RUNBOOK_PROCESSING`失败 | `prisma.task.update` | (内部错误) | 记录详细DB错误。此时消息已发送到队列，但DB状态未更新，可能导致下次轮询重复发送消息（`RunbookProcessorFunc`需能处理重复消息）。这是一个需要关注的潜在数据不一致点。 | ERROR |
| 5  | Function自身执行超时 (超出`FUNCTION_TIMEOUT_SECONDS`) | Azure Functions运行时 | (内部错误) | Azure平台记录超时。本次轮询可能只处理了部分任务。未处理的任务将在下次轮询时重试。 | ERROR |

### 3.8. 配置项 (Configuration)

#### 3.8.1. 值列表 (LOV) 定义 (源自 `docs/definitions/lov-definitions.md`)
*   `TASK_STATUS` (父级代码): 本函数主要关注 `TASK_STATUS_RUNBOOK_SUBMITTED_CODE`，并将其更新为 `TASK_STATUS_RUNBOOK_PROCESSING_CODE`。

#### 3.8.2. 环境变量 (源自 `docs/guides/environment-variables.md`)
*   `RUNBOOK_MONITOR_INTERVAL_SECONDS`: 本Function定时触发器的执行间隔。
*   `RUNBOOK_TIMEOUT_SECONDS`: 判断Runbook作业是否超时的阈值。
*   `SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME`: 目标Service Bus队列名称。
*   `AZURE_AUTOMATION_ACCOUNT_NAME`, `AZURE_AUTOMATION_RESOURCE_GROUP_NAME` (或通过SDK配置的客户端参数)。
*   `MSSQL_PRISMA_URL`, `AZURE_SERVICEBUS_CONNECTION_STRING`。
*   `LOG_LEVEL`。
*   `FUNCTION_TIMEOUT_SECONDS` (若在`host.json`中通过此配置)。

#### 3.8.3. 错误与提示消息 (源自 `docs/definitions/error-messages.md`)
*   本函数不直接产生或更新用户可见的任务结果消息。它仅将原始结果转发给`RunbookStatusQueue`。

### 3.9. 注意事项与其他 (Notes/Miscellaneous)
*   **CRON表达式配置**: 环境变量`RUNBOOK_MONITOR_INTERVAL_SECONDS`的值需要被转换为Azure Functions Timer Trigger支持的CRON表达式或TimeSpan格式字符串，配置在`function.json`的`schedule`属性中。例如，如果值为300，对应的CRON表达式可以是 `0 */5 * * * *` (每5分钟的第0秒执行)。
*   **批量处理与分页**: 如果一次轮询可能获取大量待处理任务，导致Function执行时间过长，应考虑在查询`Task`表时进行分页 (`skip`, `take`)，或者在函数内部实现分批处理逻辑，每次只处理一部分任务，并在日志中记录当前批次和总批次，以便跟踪。
*   **API调用成本与限制**: 频繁调用Azure Automation API可能会产生费用或达到API速率限制。`RUNBOOK_MONITOR_INTERVAL_SECONDS`的设置应平衡实时性和成本/限制。
*   **托管身份**: 与Azure Automation和Service Bus交互时，强烈推荐使用托管身份进行认证。
