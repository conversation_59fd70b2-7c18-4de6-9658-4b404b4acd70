# 数据模型: 计划可用产品 (PlanProduct)

*   **表名 (逻辑名)**: `PlanProduct`
*   **对应UI界面**: N/A (主要由系统内部用于定义契约计划与产品媒体之间的可用关系)
*   **主要用途**: 作为 `Plan` (契约计划信息) 表和 `ProductMedia` (产品媒体) 表之间的多对多关联（中间）表。它记录了特定契约计划下，哪些产品媒体（特定产品代码和版本）是可用的。

## 1. 字段定义

| 字段名 (Prisma/英文) | 数据类型 (SQL参考) | PK | FK | UQ | Nullable | Default  | 描述 (中文)                                                                                             |
| :------------------- | :----------------- | :--- | :--- | :--- | :------- | :------- | :------------------------------------------------------------------------------------------------------ |
| `id`                 | VARCHAR(36)        | ●    |      |      |          | `cuid()` | 主键。CUID格式，由系统自动生成。                                                                            |
| `productCode`        | VARCHAR(XX)        |      | ●    |      |          |          | **外键**。关联到 `ProductMedia` 表的 `productCode` 字段。与 `version` 共同构成指向 `ProductMedia` 的复合外键。 |
| `version`            | VARCHAR(50)        |      | ●    |      |          |          | **外键**。关联到 `ProductMedia` 表的 `version` 字段。与 `productCode` 共同构成指向 `ProductMedia` 的复合外键。   |
| `planId`             | VARCHAR(XX)        |      | ●    |      | Yes      |          | **可选外键**。关联到 `Plan` 表的 `planId` (业务唯一键)。表示此产品可用性条目属于哪个契约计划。                  |

*PK: Primary Key, FK: Foreign Key, UQ: Unique Key*
*XX表示具体长度，取决于实际的数据库Schema定义。*

## 2. 关系

*   **对 `Plan` (`plan`)**: 可选的多对一关系。通过 `planId` 字段关联到 `Plan` 表的 `planId` 唯一键。一个产品可用性条目可以属于一个契约计划，或者在 `planId` 为 NULL 的情况下不直接属于任何特定计划（需确认此业务逻辑是否适用）。
*   **对 `ProductMedia` (`productMedia`)**: 多对一关系。通过复合字段 (`productCode`, `version`) 关联到 `ProductMedia` 表的 `@@unique([productCode, version])` 复合唯一键。

## 3. 索引

*   `PRIMARY KEY (id)`
*   `INDEX idx_planId_plan_product (planId)` (Prisma Schema已定义，用于优化按计划ID的查询)
*   `INDEX idx_productCode_version_plan_product (productCode, version)` (Prisma Schema已定义，用于优化按产品代码和版本的查询，并支持外键约束)
*   (可选) `UNIQUE KEY UQ_PlanProduct_Plan_ProductVersion (planId, productCode, version)` (如果业务要求一个计划下每个产品版本的关联是唯一的)