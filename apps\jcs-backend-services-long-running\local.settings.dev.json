{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "FUNCTIONS_WORKER_RUNTIME": "node", "AzureWebJobsFeatureFlags": "EnableWorkerIndexing", "MSSQL_PRISMA_URL": "sqlserver://**************:65220;database=jp2;user=SA;password=Uknowit1^_^;encrypt=DANGER_PLAINTEXT;", "AZURE_SERVICEBUS_NAMESPACE_HOSTNAME__fullyQualifiedNamespace": "export-tasks-queue.servicebus.windows.net", "SERVICE_BUS_TASK_INPUT_QUEUE_NAME": "task-input-queue-dev", "SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME": "runbook-status-queue-dev", "SERVICE_BUS_TASK_CONTROL_QUEUE_NAME": "task-control-queue-dev", "AZURE_STORAGE_BLOB_CONNECTION_STRING": "DefaultEndpointsProtocol=https;AccountName=stuatepusw2;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "AZURE_STORAGE_FILES_CONNECTION_STRING": "DefaultEndpointsProtocol=https;AccountName=stuatepusw2;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF": "assetsfield-def-dev", "AZURE_STORAGE_CONTAINER_OPLOGS": "oplogs-dev", "AZURE_AUTOMATION_ACCOUNT_NAME": "aa-uat-ep-eastus", "SUBSCRIPTION_ID": "ef7e9c57-0352-4755-a3c7-55a9356cab2a", "RESOURCE_GROUP_NAME": "rg-uat-ep-eastus", "RUNBOOK_MGMT_ITEM_IMPORT": "Import-Management-Item", "RUNBOOK_MGMT_ITEM_EXPORT": "Export-Management-Item", "RUNBOOK_OPLOG_EXPORT": "Export-Operation-Log", "RUNBOOK_MONITOR_INTERVAL_SECONDS": 300}}