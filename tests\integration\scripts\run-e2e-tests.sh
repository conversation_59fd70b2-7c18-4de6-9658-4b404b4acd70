#!/bin/bash

# 🚀 E2E 测试一键运行脚本
# 
# 本脚本采用 Playwright webServer 最佳实践，自动化管理所有服务：
# - Next.js 前端应用（生产模式）
# - Azure Functions 标准服务
# - Azure Functions 长时运行服务
# 
# 使用方法：
#   ./scripts/run-e2e-tests.sh                    # 运行所有测试
#   ./scripts/run-e2e-tests.sh server-list        # 运行特定测试
#   ./scripts/run-e2e-tests.sh --headed           # 有头模式运行
#   ./scripts/run-e2e-tests.sh --debug            # 调试模式运行

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查必要的工具
check_prerequisites() {
    log_info "检查必要的工具..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装。请安装 Node.js 18+ 版本。"
        exit 1
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装。请安装 npm。"
        exit 1
    fi
    
    # 检查 Azure Functions Core Tools
    if ! command -v func &> /dev/null; then
        log_warning "Azure Functions Core Tools 未安装。"
        log_warning "请运行: npm install -g azure-functions-core-tools@4 --unsafe-perm true"
        log_warning "Azure Functions 测试将被跳过。"
        export SKIP_AZURE_FUNCTIONS=true
    fi
    
    log_success "工具检查完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装测试依赖..."
    
    # 安装集成测试依赖
    if [ ! -d "node_modules" ]; then
        npm install
    fi
    
    # 安装 Next.js 应用依赖
    if [ ! -d "../../apps/jcs-endpoint-nextjs/node_modules" ]; then
        log_info "安装 Next.js 应用依赖..."
        (cd ../../apps/jcs-endpoint-nextjs && npm install)
    fi
    
    # 安装 Azure Functions 依赖
    if [ "$SKIP_AZURE_FUNCTIONS" != "true" ]; then
        if [ ! -d "../../apps/jcs-backend-services-standard/node_modules" ]; then
            log_info "安装标准 Azure Functions 依赖..."
            (cd ../../apps/jcs-backend-services-standard && npm install)
        fi
        
        if [ ! -d "../../apps/jcs-backend-services-long-running/node_modules" ]; then
            log_info "安装长时运行 Azure Functions 依赖..."
            (cd ../../apps/jcs-backend-services-long-running && npm install)
        fi
    fi
    
    log_success "依赖安装完成"
}

# 环境验证
validate_environment() {
    log_info "验证测试环境..."
    
    # 检查环境变量文件
    if [ ! -f "../../apps/jcs-endpoint-nextjs/.env.test.local" ]; then
        log_error "缺少 .env.test.local 文件。请确保测试环境配置正确。"
        exit 1
    fi
    
    # 运行环境验证脚本
    if [ -f "validate-test-environment.js" ]; then
        node validate-test-environment.js
    fi
    
    log_success "环境验证完成"
}

# 清理旧的测试结果
cleanup_old_results() {
    log_info "清理旧的测试结果..."
    
    # 清理测试结果目录
    if [ -d "../test-results" ]; then
        rm -rf ../test-results
    fi
    
    # 清理 Playwright 报告
    if [ -d "playwright-report" ]; then
        rm -rf playwright-report
    fi
    
    # 清理测试输出
    if [ -d "test-results" ]; then
        rm -rf test-results
    fi
    
    log_success "清理完成"
}

# 运行测试
run_tests() {
    local test_args="$@"
    
    log_info "开始运行 E2E 测试..."
    log_info "Playwright 将自动启动所有必要的服务"
    
    # 构建 Playwright 命令
    local playwright_cmd="npx playwright test"
    
    # 添加用户指定的参数
    if [ ! -z "$test_args" ]; then
        playwright_cmd="$playwright_cmd $test_args"
    fi
    
    log_info "执行命令: $playwright_cmd"
    
    # 运行测试
    if eval $playwright_cmd; then
        log_success "测试运行完成"
        
        # 显示报告信息
        if [ -d "playwright-report" ]; then
            log_info "测试报告已生成: playwright-report/index.html"
            log_info "运行 'npx playwright show-report' 查看详细报告"
        fi
    else
        log_error "测试运行失败"
        exit 1
    fi
}

# 主函数
main() {
    log_info "🚀 启动 E2E 测试流程"
    log_info "========================================"
    
    # 确保在正确的目录
    if [ ! -f "playwright.config.ts" ]; then
        log_error "请在 tests/integration 目录下运行此脚本"
        exit 1
    fi
    
    # 执行测试流程
    check_prerequisites
    install_dependencies
    validate_environment
    cleanup_old_results
    run_tests "$@"
    
    log_success "🎉 E2E 测试流程完成"
}

# 运行主函数
main "$@"
