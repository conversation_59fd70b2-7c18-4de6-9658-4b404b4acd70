# 部署流程指南

本文档详细说明了系统中各个组件的部署流程。

## 1. Next.js应用部署

### 前提条件
- 已配置Azure App Service
- 已获取Azure App Service的Git部署URL

### 部署步骤
1. 进入Next.js应用目录：
   ```powershell
   cd apps/jcs-endpoint-nextjs
   ```

2. 初始化Git仓库（如果尚未初始化）：
   ```powershell
   git init
   ```

3. 添加Azure远程仓库：
   ```powershell
   git remote add azure <azure-git-url>
   ```

4. 提交更改并推送：
   ```powershell
   git add .
   git commit -m "Deployment commit"
   git push azure master:master
   ```

## 2. Azure Functions部署

### 前提条件
- 已创建Azure Function App
- 已安装必要的构建工具（Node.js, pnpm）
- 已安装PowerShell 5.0或更高版本

### 部署步骤
1. 运行打包脚本：
   ```powershell
   cd scripts
   .\build-and-package-functions.ps1
   ```

2. 生成的zip包位于 `dist/` 目录

3. 使用Azure Portal或Azure CLI部署zip包：
   ```powershell
   az functionapp deployment source config-zip -g <resource-group> -n <function-app-name> --src <path-to-zip>
   ```

## 3. Azure Automation Runbooks部署

### 前提条件
- 已创建Azure Automation Account
- 已安装Azure PowerShell模块

### 部署步骤
1. 运行部署脚本：
   ```powershell
   cd scripts
   .\deploy-runbooks-locally.ps1 -ResourceGroupName <resource-group> -AutomationAccountName <automation-account>
   ```

2. 按照提示登录Azure（如果需要）

3. 等待部署完成

## 验证部署

每个组件部署后，请按照以下步骤验证：

1. Next.js应用：
   - 访问应用URL
   - 检查控制台日志

2. Azure Functions：
   - 在Azure Portal中检查Function App状态
   - 测试各个Function端点

3. Azure Automation：
   - 在Azure Portal中检查Runbook状态
   - 执行测试运行 