# 组件：手册列表 (Manual List)

## 1. 概要 (Overview)

### 1.1 目的 (Purpose)
本组件为“JCS 端点资产与任务管理系统”的已登录用户（特指顾客系统管理员）提供一个集中的界面，用于浏览和下载与本服务相关的各类可用手册文档，如产品手册、操作指南、使用说明等。用户可以通过此功能方便地获取所需的官方参考资料。

### 1.2 用户故事/需求 (User Stories/Requirements)
- 作为一名顾客系统管理员，我希望能够查看一个包含所有我当前契约下可用的服务相关手册的列表。
- 作为一名顾客系统管理员，我希望能看到每个手册的清晰名称、唯一的资料编号以及文件大小。
- 作为一名顾客系统管理员，我希望能方便地从列表中下载我需要的手册文件。
- 作为一名顾客系统管理员，我希望能根据手册名称或资料编号快速筛选和查找我需要的手册。

### 1.3 高层依赖与交互 (High-Level Dependencies & Interactions)
- **用户认证与授权**: 用户的身份（特别是关联的契约计划ID）决定了其能在列表中看到哪些手册。
- **主界面 (Main Screen)**: 本组件的入口点是[主界面](./02-main-screen.md)侧边栏的“マニュアル一覧 (手册列表)”菜单项。
- **数据存储**:
    - 手册的元数据（手册名称、资料编号、文件名、大小等）存储在Azure SQL Database的 `ProductManual` 表中 (根据`fs.md`的DB设计，表名为 `ProductManual`，用于存储手册信息，而 `PlanManual` 表关联契约计划ID和资料编号，控制可见性)。
- **文件存储与下载**:
    - 手册文件（通常为PDF等文档格式）实际存储在Azure Blob Storage的特定容器（如 `product-manuals`）中，并可能按资料编号等规则组织。
    - 用户下载文件时，前端通过后端API获取一个安全的、有时限的共享访问签名 (SAS) 链接指向Blob Storage中的文件。
- **后端API (Next.js API Routes)**: 前端通过调用后端API来获取用户可见的手册列表和生成文件下载链接。

## 2. 功能规格 (Functional Specifications)

### 2.1 主要流程 (High-Level Flow)
1.  用户成功登录系统后，通过侧边导航栏访问“マニュアル一覧 (手册列表)”菜单项。
2.  系统加载并向用户展示其有权访问的手册列表，每条记录包含手册名称、资料编号、文件大小等信息。手册名称本身作为可点击的下载链接。
3.  用户可以使用界面提供的筛选框输入关键字，点击搜索或按回车后，列表将根据匹配结果刷新。
4.  用户可以点击列表表头对手册信息进行排序。
5.  用户可以点击“手册名称”列中的手册名链接来下载对应的手册文件。
    ```mermaid
    sequenceDiagram
        participant User as 👤 用户 (Browser)
        participant ManualListScreen as 📚 手册列表界面
        participant NextJsApiRoutes as 🌐 Next.js API Routes
        participant ManualDB as 💾 ProductManual/PlanManual表 (SQL DB)
        participant AzureBlobStorage as ☁️ Azure Blob Storage

        User->>ManualListScreen: 打开手册列表
        ManualListScreen->>NextJsApiRoutes: 请求手册列表数据 (GET /api/manuals)
        NextJsApiRoutes->>ManualDB: 查询用户可见的手册元数据 (基于用户契约计划ID)
        ManualDB-->>NextJsApiRoutes: 返回手册元数据列表
        NextJsApiRoutes-->>ManualListScreen: 显示手册列表

        alt 用户下载手册
            User->>ManualListScreen: 点击手册A的下载链接
            ManualListScreen->>NextJsApiRoutes: 请求手册A文件的下载URL (GET /api/manuals/A_id/download-url)
            NextJsApiRoutes->>AzureBlobStorage: 为手册A文件生成SAS Token
            AzureBlobStorage-->>NextJsApiRoutes: 返回带SAS Token的URL
            NextJsApiRoutes-->>ManualListScreen: 返回安全的下载URL
            ManualListScreen->>User: (浏览器)开始下载文件
        end
    ```
6.  用户可以使用分页控件浏览更多的手册记录。
7.  用户可以点击界面右上角的“更新”图标按钮，手动刷新整个手册列表。

### 2.2 业务规则 (Business Rules)
-   **信息来源与可见性**:
    *   手册的元数据（名称、资料编号、大小、对应的文件名等）从门户数据库的 `ProductManual` 表中获取。
    *   用户可见的手册范围由其契约计划ID通过 `PlanManual` 表（或类似机制）关联确定。用户只能查看和下载其有权访问的手册。
-   **只读显示**: 列表中的所有手册信息均为只读展示。
-   **文件存储路径**: 手册文件存储在Azure Blob Storage的 `product-manuals` 容器下，可能按资料编号分子目录组织（例如：`product-manuals/{资料编号}/{文件名}`）。
-   **下载机制**: 与操作日志下载类似，用户下载文件时，后端API为目标Blob生成一个具有读取权限且有时间限制（默认为2小时，可由`LOV`表 `AZURE_STORAGE.SAS_TTL_SECONDS` 配置）的共享访问签名 (SAS) Token。
-   **默认排序** (依据 `fs.md` 对マニュアル一覧的ソート规则):
    *   默认按“マニュアル名称 (手册名称)”的字典序升序（A→Z, 0→9）排列。

### 2.3 用户界面概述 (User Interface Overview)

-   **界面草图/核心区域**: (参考 `fs.md` 图4.10.6 (1) 作为高层概念)
    1.  **筛选区**: 位于列表上方，提供文本输入框进行关键字筛选，以及搜索和清除按钮。
    2.  **手册列表表格**:
        *   列：マニュアル名称 (手册名称，兼作下载链接)、資料番号 (资料编号)、サイズ (文件大小)。
        *   每行代表一个可用的手册。
    3.  **分页控件**: 位于列表下方。
    4.  **更新按钮**: 通常位于列表右上角。
-   **主要交互点**:
    *   用户在筛选框中输入文本，进行列表筛选。
    *   用户点击可排序的列标题（如“マニュアル名称”、“資料番号”、“サイズ”），进行升序/降序排序。
    *   用户点击“マニュアル名称”列中的手册名链接，浏览器开始下载对应手册文件。
    *   用户使用分页控件浏览更多记录。
    *   用户点击“更新”按钮，重新加载列表。
-   **画面項目 (Screen Items - High Level)**:
    *   输入：筛选关键字。
    *   显示：手册名称 (可下载)、手册的资料编号、手册文件的大小。
    *   用户可操作：筛选、排序、分页、下载手册文件。
-   **Tab键顺序** (依据 `fs.md` 描述):
    *   Tab键焦点在列表区域时，应按“マニュアル名称”列从上到下的顺序遍历下载链接。

### 2.4 前提条件 (Preconditions)
-   用户必须已通过[登录功能](./01-login.md)完成身份验证，并拥有有效的活动会话。
-   系统管理员已在 `ProductManual` 数据库表中正确录入了手册的元数据，并通过 `PlanManual` 表（或类似机制）配置了用户契约计划可访问的手册范围。
-   对应的手册文件已上传到Azure Blob Storage的 `product-manuals` 容器中，并与数据库元数据中的路径一致。
-   后端用于获取手册列表和生成下载链接的API端点必须可用。

### 2.5 制约事项 (Constraints/Limitations)
-   **浏览器兼容性**: 仅正式支持 Microsoft Edge 和 Google Chrome 浏览器。
-   **语言支持**: 界面显示语言仅支持日语。
-   **列表性能**: 如果手册数量非常庞大，列表的加载、筛选和分页性能可能会受到影响。

### 2.6 注意事项 (Notes/Considerations)
-   手册文件的大小应以用户友好的单位显示（例如 KB, MB, GB）。
-   “资料编号”是每个手册的唯一标识，便于用户精确查找。

### 2.7 错误处理概述 (Error Handling Overview)
-   **列表加载失败**: 如果后端API调用失败导致无法获取手册列表，界面应向用户显示通用的错误提示信息（例如，“手册列表加载失败，请稍后重试。”），并记录详细技术错误。
-   **文件下载失败**:
    *   如果请求下载链接时后端API出错，应提示用户“无法获取下载链接，请重试。”
    *   如果生成的下载链接无效（SAS Token过期、文件在Blob Storage中不存在或无权限），用户点击下载后浏览器层面会报错或下载失败。
-   **无权访问手册**: 如果用户因权限配置问题理论上不应看到任何手册，列表应显示为空或特定提示（例如，“当前没有您可访问的手册。”）。

### 2.8 相关功能参考 (Related Functional References)
*   **主要入口**: [主界面 (Main Screen)](./02-main-screen.md) - 侧边栏的“マニュアル一覧”菜单是访问本功能的路径。
*   **认证关联**: [登录 (Login)](./01-login.md) - 用户的登录身份（特别是契约计划ID）决定了其能看到的手册范围。
*   **系统整体架构**: [系统架构](../../architecture/system-architecture.md) - 描述了手册数据如何从存储到呈现给用户的流程。
*   **核心数据模型**:
    *   [手册数据模型](../../data-models/ProductManual.md) - 定义了手册元数据的主要来源表结构。
    *   [计划手册数据模型](../../data-models/PlanManual.md) - 定义了用户契约计划与可访问手册之间的关联。
*   **配置参考**: [系统配置表](../../data-models/Lov.md) - 可能包含与SAS Token有效期 (`AZURE_STORAGE.SAS_TTL_SECONDS`) 等相关的配置。
*   **源功能规格**: [功能规格书](../../docs-delivery/functional-specifications/fs.md) - 本组件功能规格的原始日文描述，特别是其“4.10 マニュアル一覧”章节。
