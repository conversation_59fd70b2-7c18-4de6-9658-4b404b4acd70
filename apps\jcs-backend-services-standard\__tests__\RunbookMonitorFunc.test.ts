/**
 * @file RunbookMonitorFunc.test.ts
 * @description
 * RunbookMonitorFunc の単体テストファイルです。
 * Jest を用いて、Runbook監視関数の主要な分岐、例外処理、外部サービス連携の動作を検証します。
 *
 * 【設計意図】
 * - Runbook監視関数の主要な分岐・例外・外部連携の網羅的なテストを自動化。
 * - データベース/Automation/ServiceBus等の外部依存をモックし、異常系も含めて堅牢性を担保。
 * - 100%のコードカバレッジを目指し、全ての分岐・例外処理を網羅的に検証。
 *
 * 【主なテストカバレッジ】
 * 1. RUNBOOK_SUBMITTEDタスクあり/なし
 * 2. startedAt未設定・タイムアウト分岐
 * 3. ジョブステータス判定（実行中、終了状態、未知ステータス）
 * 4. DB更新（成功、失敗、楽観ロック失敗）
 * 5. ServiceBus送信（成功、失敗、ロールバック）
 * 6. Automation API（成功、失敗、ジョブ不存在）
 * 7. 全体例外処理
 *
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { RunbookMonitorFunc } from "../RunbookMonitorFunc/RunbookMonitorFunc";
import { prisma } from "../lib/prisma";
import * as azureClients from "../lib/azureClients";
import { AppConstants } from "../lib/constants";

// Azure Clients のモック
jest.mock("../lib/azureClients");

// Prisma のモック
jest.mock("../lib/prisma", () => {
  const { mockDeep } = require("jest-mock-extended");
  return { prisma: mockDeep() };
});

// Service Bus のモック
const sendMessagesMock = jest.fn();
const senderMock = { sendMessages: sendMessagesMock };
const serviceBusClientMock = { createSender: jest.fn(() => senderMock) };

jest.mock("@azure/service-bus", () => ({
  ServiceBusClient: jest.fn(() => serviceBusClientMock),
}));

/**
 * @fileoverview RunbookMonitorFunc関数の単体テスト。
 * @description Azure FunctionsのRunbookMonitorFuncの主要分岐・例外・外部サービス連携を網羅的に検証する。
 * 試験観点：Runbook監視関数の正常系・異常系・外部サービス連携の分岐網羅性、外部依存のモックによる堅牢性検証。
 * 試験対象：RunbookMonitorFunc.ts（RunbookMonitorFunc Azure Function本体）。
 */
describe("RunbookMonitorFunc 単体テスト", () => {
  let context: any;
  let mockTimer: any;

  beforeEach(() => {
    /**
     * Azure Functions の InvocationContext をモックし、必須フィールドを全て補完
     */
    context = {
      invocationId: "test-invoke",
      functionName: "RunbookMonitorFunc",
      extraInputs: [],
      extraOutputs: [],
      bindingData: {},
      traceContext: {},
      log: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
    };

    /**
     * Timer オブジェクトをモック
     */
    mockTimer = {
      isPastDue: false,
      schedule: { adjustForDST: true },
      scheduleStatus: { last: '', lastUpdated: '', next: '' },
      severity: 0
    };

    jest.clearAllMocks();

    // Azure Clients のファクトリー関数をモック
    (azureClients.createServiceBusClient as jest.Mock).mockReturnValue(serviceBusClientMock);
  });

  /**
   * 試験観点：監視対象タスクなし分岐の検証。
   * 試験対象：RunbookMonitorFuncのタスクなし分岐。
   * 試験手順：
   * 1. RUNBOOK_SUBMITTEDタスクが存在しない場合をテスト。
   * 確認項目：
   * - context.logに"存在しません"が出力されること。
   * - getAutomationJobStatusが呼ばれないこと。
   */
  it("RUNBOOK_SUBMITTEDタスクが存在しない場合: 即終了", async () => {
    (prisma.task.findMany as any).mockResolvedValue([]);

    await RunbookMonitorFunc(mockTimer, context);

    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("存在しません"));
    expect(azureClients.getAutomationJobStatus).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：startedAt未設定分岐の検証。
   * 試験対象：RunbookMonitorFuncのstartedAt未設定分岐。
   * 試験手順：
   * 1. startedAtが未設定のタスクをテスト。
   * 確認項目：
   * - context.warnに"startedAt未設定のためスキップ"が出力されること。
   * - getAutomationJobStatusが呼ばれないこと。
   */
  it("startedAt未設定タスク: スキップ", async () => {
    const task = {
      id: "task1",
      status: AppConstants.TaskStatus.RunbookSubmitted,
      startedAt: null,
      updatedAt: new Date()
    };
    (prisma.task.findMany as any).mockResolvedValue([task]);

    await RunbookMonitorFunc(mockTimer, context);

    expect(context.warn).toHaveBeenCalledWith(expect.stringContaining("startedAt未設定のためスキップ"));
    expect(azureClients.getAutomationJobStatus).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：タイムアウト分岐の検証。
   * 試験対象：RunbookMonitorFuncのタイムアウト分岐。
   * 試験手順：
   * 1. タイムアウトしたタスクをテスト（6時間前に開始）。
   * 確認項目：
   * - context.logに"タイムアウトしました"が出力されること。
   * - DB更新とServiceBus送信が実行されること。
   */
  it("タイムアウトタスク: Timeout処理", async () => {
    const task = {
      id: "task1",
      status: AppConstants.TaskStatus.RunbookSubmitted,
      startedAt: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6時間前
      updatedAt: new Date()
    };
    (prisma.task.findMany as any).mockResolvedValue([task]);
    (prisma.task.updateMany as any).mockResolvedValue({ count: 1 });
    sendMessagesMock.mockResolvedValue(undefined);

    await RunbookMonitorFunc(mockTimer, context);

    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("タイムアウトしました"));
    expect(prisma.task.updateMany).toHaveBeenCalledWith({
      where: { id: "task1", updatedAt: task.updatedAt },
      data: { status: AppConstants.TaskStatus.RunbookProcessing }
    });
    expect(sendMessagesMock).toHaveBeenCalledWith({
      body: { taskId: "task1", automationJobStatus: "Timeout", exception: "" }
    });
  });

  /**
   * 試験観点：Job不存在分岐の検証。
   * 試験対象：RunbookMonitorFuncのJob不存在分岐。
   * 試験手順：
   * 1. Azure Automation Jobが存在しない場合をテスト。
   * 確認項目：
   * - context.warnに"ジョブが存在しません"が出力されること。
   * - DB更新が実行されないこと。
   */
  it("Azure Automation Job不存在: スキップ", async () => {
    const task = {
      id: "task1",
      status: AppConstants.TaskStatus.RunbookSubmitted,
      startedAt: new Date(Date.now() - 60 * 1000), // 1分前
      updatedAt: new Date()
    };
    (prisma.task.findMany as any).mockResolvedValue([task]);
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: false,
      statusCode: 404
    });

    await RunbookMonitorFunc(mockTimer, context);

    expect(context.warn).toHaveBeenCalledWith(expect.stringContaining("ジョブが存在しません"));
    expect(prisma.task.updateMany).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：実行中ステータス分岐の検証。
   * 試験対象：RunbookMonitorFuncの実行中ステータス分岐。
   * 試験手順：
   * 1. ジョブステータスが実行中状態の場合をテスト。
   * 確認項目：
   * - context.logに"実行中状態のため処理対象外"が出力されること。
   * - DB更新が実行されないこと。
   */
  it("ジョブステータス実行中: 処理対象外", async () => {
    const task = {
      id: "task1",
      status: AppConstants.TaskStatus.RunbookSubmitted,
      startedAt: new Date(Date.now() - 60 * 1000), // 1分前
      updatedAt: new Date()
    };
    (prisma.task.findMany as any).mockResolvedValue([task]);
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: true,
      statusCode: 200,
      jobData: { properties: { status: "Running", exception: "" } }
    });

    await RunbookMonitorFunc(mockTimer, context);

    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("実行中状態のため処理対象外"));
    expect(prisma.task.updateMany).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：終了状態分岐の検証。
   * 試験対象：RunbookMonitorFuncの終了状態分岐。
   * 試験手順：
   * 1. ジョブステータスが終了状態の場合をテスト。
   * 確認項目：
   * - context.logに"終了状態のため処理対象"が出力されること。
   * - DB更新とServiceBus送信が実行されること。
   */
  it("ジョブステータス終了状態: 処理対象", async () => {
    const task = {
      id: "task1",
      status: AppConstants.TaskStatus.RunbookSubmitted,
      startedAt: new Date(Date.now() - 60 * 1000), // 1分前
      updatedAt: new Date()
    };
    (prisma.task.findMany as any).mockResolvedValue([task]);
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: true,
      statusCode: 200,
      jobData: { properties: { status: "Completed", exception: "test exception" } }
    });
    (prisma.task.updateMany as any).mockResolvedValue({ count: 1 });
    sendMessagesMock.mockResolvedValue(undefined);

    await RunbookMonitorFunc(mockTimer, context);

    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("終了状態のため処理対象"));
    expect(prisma.task.updateMany).toHaveBeenCalledWith({
      where: { id: "task1", updatedAt: task.updatedAt },
      data: { status: AppConstants.TaskStatus.RunbookProcessing }
    });
    expect(sendMessagesMock).toHaveBeenCalledWith({
      body: { taskId: "task1", automationJobStatus: "Completed", exception: "test exception" }
    });
  });

  /**
   * 試験観点：未知ステータス分岐の検証。
   * 試験対象：RunbookMonitorFuncの未知ステータス分岐。
   * 試験手順：
   * 1. ジョブステータスが未知の状態の場合をテスト。
   * 確認項目：
   * - context.warnに"未知ジョブステータス"が出力されること。
   * - DB更新が実行されないこと。
   */
  it("ジョブステータス未知: スキップ", async () => {
    const task = {
      id: "task1",
      status: AppConstants.TaskStatus.RunbookSubmitted,
      startedAt: new Date(Date.now() - 60 * 1000), // 1分前
      updatedAt: new Date()
    };
    (prisma.task.findMany as any).mockResolvedValue([task]);
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: true,
      statusCode: 200,
      jobData: { properties: { status: "UnknownStatus", exception: "" } }
    });

    await RunbookMonitorFunc(mockTimer, context);

    expect(context.warn).toHaveBeenCalledWith(expect.stringContaining("未知ジョブステータス"));
    expect(prisma.task.updateMany).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：API失敗分岐の検証。
   * 試験対象：RunbookMonitorFuncのAPI失敗分岐。
   * 試験手順：
   * 1. Azure Automation API呼び出しが失敗する場合をテスト。
   * 確認項目：
   * - context.errorに"API呼び出し失敗"が出力されること。
   * - DB更新が実行されないこと。
   */
  it("Azure Automation API失敗: スキップ", async () => {
    const task = {
      id: "task1",
      status: AppConstants.TaskStatus.RunbookSubmitted,
      startedAt: new Date(Date.now() - 60 * 1000), // 1分前
      updatedAt: new Date()
    };
    (prisma.task.findMany as any).mockResolvedValue([task]);
    (azureClients.getAutomationJobStatus as jest.Mock).mockRejectedValue(
      new Error("API呼び出し失敗")
    );

    await RunbookMonitorFunc(mockTimer, context);

    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("API呼び出し失敗"));
    expect(prisma.task.updateMany).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：楽観ロック失敗分岐の検証。
   * 試験対象：RunbookMonitorFuncの楽観ロック失敗分岐。
   * 試験手順：
   * 1. DB更新が楽観ロック失敗（0件更新）の場合をテスト。
   * 確認項目：
   * - context.errorに"楽観ロック失敗"が出力されること。
   * - ServiceBus送信が実行されないこと。
   */
  it("DB更新楽観ロック失敗: スキップ", async () => {
    const task = {
      id: "task1",
      status: AppConstants.TaskStatus.RunbookSubmitted,
      startedAt: new Date(Date.now() - 60 * 1000), // 1分前
      updatedAt: new Date()
    };
    (prisma.task.findMany as any).mockResolvedValue([task]);
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: true,
      statusCode: 200,
      jobData: { properties: { status: "Completed", exception: "" } }
    });
    (prisma.task.updateMany as any).mockResolvedValue({ count: 0 }); // 楽観ロック失敗

    await RunbookMonitorFunc(mockTimer, context);

    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("楽観ロック失敗"));
    expect(sendMessagesMock).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：DB更新例外分岐の検証。
   * 試験対象：RunbookMonitorFuncのDB更新例外分岐。
   * 試験手順：
   * 1. DB更新が例外で失敗する場合をテスト。
   * 確認項目：
   * - context.errorに"DB更新失敗"が出力されること。
   * - ServiceBus送信が実行されないこと。
   */
  it("DB更新例外: スキップ", async () => {
    const task = {
      id: "task1",
      status: AppConstants.TaskStatus.RunbookSubmitted,
      startedAt: new Date(Date.now() - 60 * 1000), // 1分前
      updatedAt: new Date()
    };
    (prisma.task.findMany as any).mockResolvedValue([task]);
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: true,
      statusCode: 200,
      jobData: { properties: { status: "Completed", exception: "" } }
    });
    (prisma.task.updateMany as any).mockRejectedValue(new Error("DB更新失敗"));

    await RunbookMonitorFunc(mockTimer, context);

    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("DB更新失敗"));
    expect(sendMessagesMock).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：ServiceBus送信失敗・ロールバック成功分岐の検証。
   * 試験対象：RunbookMonitorFuncのServiceBus送信失敗分岐。
   * 試験手順：
   * 1. ServiceBus送信が失敗し、ロールバック成功の場合をテスト。
   * 確認項目：
   * - context.errorに"送信失敗"が出力されること。
   * - context.logに"戻しました"が出力されること。
   */
  it("ServiceBus送信失敗・ロールバック成功", async () => {
    const task = {
      id: "task1",
      status: AppConstants.TaskStatus.RunbookSubmitted,
      startedAt: new Date(Date.now() - 60 * 1000), // 1分前
      updatedAt: new Date()
    };
    (prisma.task.findMany as any).mockResolvedValue([task]);
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: true,
      statusCode: 200,
      jobData: { properties: { status: "Completed", exception: "" } }
    });
    (prisma.task.updateMany as any)
      .mockResolvedValueOnce({ count: 1 }) // 最初の更新は成功
      .mockResolvedValueOnce({ count: 1 }); // ロールバックも成功
    sendMessagesMock.mockRejectedValue(new Error("ServiceBus送信失敗"));

    await RunbookMonitorFunc(mockTimer, context);

    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("送信失敗"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("戻しました"));
    expect(prisma.task.updateMany).toHaveBeenCalledTimes(2); // 更新とロールバック
  });

  /**
   * 試験観点：ServiceBus送信失敗・ロールバック失敗分岐の検証。
   * 試験対象：RunbookMonitorFuncのロールバック失敗分岐。
   * 試験手順：
   * 1. ServiceBus送信が失敗し、ロールバック失敗（0件）の場合をテスト。
   * 確認項目：
   * - context.errorに"送信失敗"が出力されること。
   * - context.errorに"戻し失敗"が出力されること。
   */
  it("ServiceBus送信失敗・ロールバック失敗（0件）", async () => {
    const task = {
      id: "task1",
      status: AppConstants.TaskStatus.RunbookSubmitted,
      startedAt: new Date(Date.now() - 60 * 1000), // 1分前
      updatedAt: new Date()
    };
    (prisma.task.findMany as any).mockResolvedValue([task]);
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: true,
      statusCode: 200,
      jobData: { properties: { status: "Completed", exception: "" } }
    });
    (prisma.task.updateMany as any)
      .mockResolvedValueOnce({ count: 1 }) // 最初の更新は成功
      .mockResolvedValueOnce({ count: 0 }); // ロールバック失敗（0件）
    sendMessagesMock.mockRejectedValue(new Error("ServiceBus送信失敗"));

    await RunbookMonitorFunc(mockTimer, context);

    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("送信失敗"));
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("戻し失敗"));
  });

  /**
   * 試験観点：ServiceBus送信失敗・ロールバック例外分岐の検証。
   * 試験対象：RunbookMonitorFuncのロールバック例外分岐。
   * 試験手順：
   * 1. ServiceBus送信が失敗し、ロールバック例外の場合をテスト。
   * 確認項目：
   * - context.errorに"送信失敗"が出力されること。
   * - context.errorに"戻し失敗"が出力されること。
   */
  it("ServiceBus送信失敗・ロールバック例外", async () => {
    const task = {
      id: "task1",
      status: AppConstants.TaskStatus.RunbookSubmitted,
      startedAt: new Date(Date.now() - 60 * 1000), // 1分前
      updatedAt: new Date()
    };
    (prisma.task.findMany as any).mockResolvedValue([task]);
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: true,
      statusCode: 200,
      jobData: { properties: { status: "Completed", exception: "" } }
    });
    (prisma.task.updateMany as any)
      .mockResolvedValueOnce({ count: 1 }) // 最初の更新は成功
      .mockRejectedValueOnce(new Error("ロールバック例外")); // ロールバック例外
    sendMessagesMock.mockRejectedValue(new Error("ServiceBus送信失敗"));

    await RunbookMonitorFunc(mockTimer, context);

    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("送信失敗"));
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("戻し失敗"));
  });

  /**
   * 試験観点：タスク処理中例外分岐の検証。
   * 試験対象：RunbookMonitorFuncのタスク処理中例外分岐。
   * 試験手順：
   * 1. タスク処理中に予期せぬ例外が発生する場合をテスト。
   * 確認項目：
   * - context.errorに"予期せぬ内部エラー"が出力されること。
   * - 処理が継続されること。
   */
  it("タスク処理中予期せぬ例外: 継続", async () => {
    const task = {
      id: "task1",
      status: AppConstants.TaskStatus.RunbookSubmitted,
      startedAt: new Date(Date.now() - 60 * 1000), // 1分前
      updatedAt: new Date()
    };
    (prisma.task.findMany as any).mockResolvedValue([task]);

    // startedAtアクセス時に例外を発生させる（タスク処理の内部で例外を発生）
    Object.defineProperty(task, 'startedAt', {
      get() {
        throw new Error("予期せぬ例外");
      }
    });

    await RunbookMonitorFunc(mockTimer, context);

    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("予期せぬ内部エラー"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("正常に完了"));
  });

  /**
   * 試験観点：全体例外処理分岐の検証。
   * 試験対象：RunbookMonitorFuncの全体例外処理。
   * 試験手順：
   * 1. Task取得で致命的例外が発生する場合をテスト。
   * 確認項目：
   * - context.errorに"データ取得失敗または致命的エラー"が出力されること。
   */
  it("Task取得致命的例外: エラーログ記録", async () => {
    (prisma.task.findMany as any).mockRejectedValue(new Error("致命的例外"));

    await RunbookMonitorFunc(mockTimer, context);

    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("データ取得失敗または致命的エラー"));
  });

  /**
   * 試験観点：jobDataなし分岐の検証。
   * 試験対象：RunbookMonitorFuncのjobDataなし分岐。
   * 試験手順：
   * 1. jobDataがundefinedの場合をテスト。
   * 確認項目：
   * - 空文字列がデフォルト値として使用されること。
   * - 処理が継続されること。
   */
  it("jobDataなし: デフォルト値使用", async () => {
    const task = {
      id: "task1",
      status: AppConstants.TaskStatus.RunbookSubmitted,
      startedAt: new Date(Date.now() - 60 * 1000), // 1分前
      updatedAt: new Date()
    };
    (prisma.task.findMany as any).mockResolvedValue([task]);
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: true,
      statusCode: 200,
      jobData: undefined // jobDataがundefined
    });

    await RunbookMonitorFunc(mockTimer, context);

    expect(context.warn).toHaveBeenCalledWith(expect.stringContaining("未知ジョブステータス"));
  });

  /**
   * 試験観点：properties未定義分岐の検証。
   * 試験対象：RunbookMonitorFuncのproperties未定義分岐。
   * 試験手順：
   * 1. jobData.propertiesがundefinedの場合をテスト。
   * 確認項目：
   * - 空文字列がデフォルト値として使用されること。
   * - 処理が継続されること。
   */
  it("jobData.propertiesなし: デフォルト値使用", async () => {
    const task = {
      id: "task1",
      status: AppConstants.TaskStatus.RunbookSubmitted,
      startedAt: new Date(Date.now() - 60 * 1000), // 1分前
      updatedAt: new Date()
    };
    (prisma.task.findMany as any).mockResolvedValue([task]);
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: true,
      statusCode: 200,
      jobData: { properties: undefined } // propertiesがundefined
    });

    await RunbookMonitorFunc(mockTimer, context);

    expect(context.warn).toHaveBeenCalledWith(expect.stringContaining("未知ジョブステータス"));
  });

  /**
   * 試験観点：正常系フローの網羅的検証。
   * 試験対象：RunbookMonitorFuncの正常系フロー。
   * 試験手順：
   * 1. 正常系の完全なフローをテスト。
   * 確認項目：
   * - 全ての処理が正常に実行されること。
   * - 適切なログが出力されること。
   */
  it("正常系: 完全フロー", async () => {
    const testDate = new Date();
    const task = {
      id: "task1",
      status: AppConstants.TaskStatus.RunbookSubmitted,
      startedAt: new Date(Date.now() - 60 * 1000), // 1分前
      updatedAt: testDate
    };
    (prisma.task.findMany as any).mockResolvedValue([task]);
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: true,
      statusCode: 200,
      jobData: { properties: { status: "Completed", exception: "test exception" } }
    });
    (prisma.task.updateMany as any).mockResolvedValue({ count: 1 });
    sendMessagesMock.mockResolvedValue(undefined);

    await RunbookMonitorFunc(mockTimer, context);

    // 各ステップの確認
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("監視関数開始"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("タスク数: 1"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("ジョブステータス取得成功"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("終了状態のため処理対象"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("ステータス更新完了"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("メッセージ送信完了"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("正常に完了"));

    // API呼び出しの確認
    expect(azureClients.getAutomationJobStatus).toHaveBeenCalledWith("task1");
    expect(prisma.task.updateMany).toHaveBeenCalledWith({
      where: { id: "task1", updatedAt: testDate },
      data: { status: AppConstants.TaskStatus.RunbookProcessing }
    });
    expect(sendMessagesMock).toHaveBeenCalledWith({
      body: { taskId: "task1", automationJobStatus: "Completed", exception: "test exception" }
    });
  });
});