# JP1_ポータル開発 詳細設計書
## 操作ログ一覧機能

## 機能概要

### 基本情報

|項目|内容|
|---|---|
|機能ID|JP1S008|
|機能名|操作ログ一覧|
|処理形態|オンライン|
|サイクル|随時|

### 機能説明

本機能は、システム利用者が行った操作の履歴を確認する機能である。

本機能の構成を以下に示す。

|#|分類|ソフトウェア|用途|
|---|---|---|---|
|1|フレームワーク|Next.js|軽量型ウェブサイトの構築と管理|
|2|CSS|Tailwind|簡単かつ効率的なスタイル設定|
|3|ORM|Prisma|データベースの操作とクエリ|

## テーブル定義

### テーブル1：操作ログ (OperationLog)

|#|論理名|物理名|データ型|ディフォルト値|NOT NULL|ユニーク|主キー|備考|
|---|---|---|---|---|---|---|---|---|
|1|ID|id|nvarchar(1000)|UUIDを使う|Y|-|Y|Prismaスキーマにおいて、@id @default(cuid())とする|
|2|操作ログファイル名|name|nvarchar(1000)|-|Y|-|-|-|
|3|ファイルサイズ|size|int|-|Y|-|-|-|
|4|登録日時|createdAt|datetime2|-|Y|-|-|-|
|5|保管期限|retentionAt|datetime2|-|Y|-|-|-|
|6|契約ID|licenseId|nvarchar(1000)|-|-|-|-|-|
|7|ファイル名|fileName|nvarchar(1000)|-|-|-|-|-|

### テーブル2：値の一覧 (Lov)

|#|論理名|物理名|データ型|ディフォルト値|NOT NULL|ユニーク|主キー|備考|
|---|---|---|---|---|---|---|---|---|
|1|ID|id|nvarchar(1000)|UUIDを使う|Y|-|Y|Prismaスキーマにおいて、@id @default(cuid())とする|
|2|コード|code|nvarchar(1000)|-|Y|Y|-|-|
|3|名前|name|nvarchar(1000)|-|Y|-|-|-|
|4|親コード|parentCode|nvarchar(1000)|-|Y|Y|-|-|
|5|値|value|nvarchar(1000)|-|Y|-|-|-|
|6|有効性の情報|isEnabled|bit|-|Y|Y|-|0:false、1:true|

## 画面項目定義

### ヘッダ部情報

|#|項目名|種別|桁/バイト|必須|文字種|ソート順|論理DBからの入力|初期表示|フォーマット/備考|
|---|---|---|---|---|---|---|---|---|---|
|1|フィルダーの入力|input|-|-|-|-|-|ブランク|最大は全角100文字を入力できる|
|2|フィルダーの検索|button|-|-|-|-|-|活性|-|
|3|フィルダーのクリア|button|-|-|-|-|-|活性|-|
|4|改ページのエリア|div|-|-|-|-|-|-|-|
|5|前のページ|button|-|-|-|-|-|非活性|-|
|6|次のページ|button|-|-|-|-|-|表示条件に従う|＜一覧のページ数が1の以上＞活性＜上記以外＞非活性|
|7|指定のページ|button|-|-|-|-|-|表示条件に従う|＜一覧のページ数が1の以上＞活性＜上記以外＞非活性|
|8|行数/ページ:|label|-|-|-|-|-|行数/ページ:|-|
|9|行数/ページの選択項目|button|-|-|-|-|-|活性|「10、30、50」固定|

### 操作ログ一覧

|#|項目名|種別|桁/バイト|必須|文字種|ソート順|論理DBからの入力|初期表示|フォーマット/備考|
|---|---|---|---|---|---|---|---|---|---|
|1|操作ログ一覧エリア|table|-|-|-|-|-|活性|-|
|2|ログ名|label|-|-|-|-|-|ログ名|-|
|3|ログ名ソート|button|-|-|-|-|-|非表示|-|
|4|ログ名|link|-|-|-|操作ログ|ログ名、リンク|DBに従う|「フォーマット」oplog_YYYYMMDD-yyyymmdd_NNN.zip YYYYMMDDは開始日、yyyymmddは最終日、NNNは連番(001～999)「ハイパーリンク」リンクをハイパーリンクとして表示|
|5|登録日時|label|-|-|-|-|-|登録日時|-|
|6|登録日時ソート|button|-|-|-|-|-|表示|ディフォルトソート：ソートアイコンを降順のアイコンにする|
|7|登録日時|label|-|-|-|操作ログ|登録日時|DBに従う|登録日時を「YYYY/MM/DD hh:mm:ss」形式で表示する。日付はブラウザのタイムゾーンで表示する|
|8|保管期限|label|-|-|-|-|-|保管期限|-|
|9|保管期限ソート|button|-|-|-|-|-|表示|ディフォルトソート：ソートアイコンを降順のアイコンにする|
|10|保管期限|label|-|-|-|操作ログ|登録日時|DBに従う|保管期限を「YYYY/MM/DD hh:mm:ss」形式で表示する。日付はブラウザのタイムゾーンで表示する|
|11|サイズ|label|-|-|-|-|-|サイズ|-|
|12|サイズソート|button|-|-|-|-|-|非表示|-|
|13|サイズ|label|-|-|-|操作ログ|サイズ|DBに従う|「xxx KB」、「xxx MB」又は「xxx GB」形式で表示する|
|14|スクロールバー|scroll|-|-|-|-|-|表示条件に従う|操作ログ一覧の表示範囲内、該当ページのサーバリストを表示でき場合、非表示とする。その以外、表示とする|

## イベント定義

### イベント1：初期表示

1. DB情報取得
   - フィルター: ''
   - ページ: 1
   - 行数/ページ: 10
   - ソートキー: 登録日時
   - ソートオーダー: 降順
   
2. 画面表示
   - 画面項目定義シートに従い、画面を表示する。

### イベント2：更新ボタンを押下

1. DB情報取得
   - 検索条件をクリアされなく、操作ログデータを取得する。

2. 画面表示
   - フィルターの条件をクリアされなく、ソートをクリアし、行数/ページは10に変更し、操作ログ一覧を再表示する。

### イベント3：フィルターに文字を入力

1. 画面表示
   - 入力した文字を表示する。

### イベント4：フィルターの検索ボタンを押下

1. DB情報取得
   - 検索条件に、「フィルター」は入力した文字列に変更して、操作ログデータを取得する。

2. 画面表示
   1. 操作ログ一覧を再表示する。
   2. 改ページのエリアを再表示する。

### イベント5：フィルターのクリアボタンを押下

1. DB情報取得
   - 検索条件に、「フィルター」をクリアして、操作ログデータを取得する。

2. 画面表示
   1. フィルターに入力した文字をクリアする。
   2. 操作ログ一覧を再表示する。
   3. 改ページのエリアを再表示する。

### イベント6：前のページボタンを押下

1. DB情報取得
   - 検索条件に、「ページ」を1減らした後、操作ログデータを取得する。

2. 画面表示
   1. 操作ログ一覧を再表示する。
   2. 次のページボタンを活性になる。
   3. 該当のページは1の場合、前のページボタンを非活性になる。その以外の場合、活性になる。
   4. 改ページのエリアを再表示する。

### イベント7：次のページボタンを押下

1. DB情報取得
   - 検索条件に、「ページ」を1増やした後、操作ログデータを取得する。

2. 画面表示
   1. 操作ログ一覧を再表示する。
   2. 前のページボタンを活性になる。
   3. 一覧の最大のページ数は該当のページの場合、次のページボタンを非活性になる。その以外の場合、活性になる。
   4. 改ページのエリアを再表示する。

### イベント8：指定のページボタンを押下

1. DB情報取得
   - 検索条件に、「ページ」を指定のページに変更して、操作ログデータを取得する。

2. 画面表示
   1. 操作ログ一覧を再表示する。
   2. 該当のページは1の場合、前のページボタンを非活性になる。その以外の場合、活性になる。
   3. 一覧の最大のページ数は該当のページの場合、次のページボタンを非活性になる。その以外の場合、活性になる。
   4. 改ページのエリアを再表示する。

### イベント9：行数/ページの選択項目ボタンを押下

1. DB情報取得
   - 検索条件に、「行数/ページ」を行数/ページの選択項目に変更して、「ページ」を1に変更して、操作ログデータを取得する。

2. 画面表示
   1. 操作ログ一覧を再表示する。
   2. 指定のページを1になる。
   3. 前のページボタンを非活性になる。
   4. 一覧の最大のページ数は1の場合、次のページボタンを非活性になる。その以外の場合、活性になる。
   5. 改ページのエリアを再表示する。

### イベント10：ログ名リンクをクリック

1. 画面表示
   - 当該JP1/ITDM2操作ログファイルをダウンロードする。

### イベント11：列のヘッダをクリック

1. DB情報取得
   1. 該当の列はソートキーの場合、検索条件に、「ソートオーダー」を逆のソートオーダーに変換して、操作ログデータを取得する。
      - ソートオーダーはascの場合、descに変換する。
      - ソートオーダーはdescの場合、ascに変換する。
   2. 上記の以外の場合、検索条件に、「ソートキー」を該当の列の物理名に変更して、「ソートオーダー」をascに設定して、操作ログデータを取得する。

2. 画面表示
   1. 操作ログ一覧を再表示する。
   2. 該当の列はソートキーの場合、ソートアイコンを逆のソートアイコンになる。
   3. 上記の以外の場合、ソートアイコンを昇順のアイコンになる。
   4. 改ページのエリアを再表示する。

## SQLクエリ定義

### 操作ログを取得

操作ログの取得には以下のPrismaクエリを使用します：

```typescript
await prisma.operationLog.findMany({
    where: {
        licenseId: 現在のユーザー.licenseId,
        OR: [
            {
                name: { contains: filterValue },
            },
            {
                createdAt: { contains: filterValue },
            },
            {
                retentionAt: { contains: filterValue },
            },
            {
                size: { contains: filterValue },
            }
        ]
    },
    skip: (page - 1) * pageSize,
    take: pageSize,
    orderBy: {
        [sortKey]: sortOrder
    }
});
```
