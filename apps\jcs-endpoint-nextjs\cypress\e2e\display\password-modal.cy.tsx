describe("初期化表示のテスト", () => {
  describe("パスワード変更ダイアログ", () => {
    const validCredentials = {
      userId: "hitachi.taro.aa",
      password: "changeit!@#",
    };

    it("パスワード変更フォームが正しく表示される", () => {
      cy.visit("/login");
      cy.get("#userId").type(validCredentials.userId);
      cy.get("#password").type(validCredentials.password);
      cy.get("button").contains("ログイン").click();
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("パスワード変更").click();
      cy.get("#confirmPassword").should("be.empty");
      cy.get("#password").should("be.empty");
      cy.get("#newPassword").should("be.empty");
      cy.get("input.border-red-500").should("have.length", 0);
      cy.get("form label").contains(validCredentials.userId);
    });
  });
});
