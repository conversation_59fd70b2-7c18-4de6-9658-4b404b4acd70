# 🧪 测试用例管理脚本

本目录包含用于管理和分析JCS Endpoint Monorepo项目测试用例的脚本工具。

---

## 📁 脚本列表

### 1. `check-test-comments.js`
**功能**: 检查测试用例注释规范合规性

**用法**:
```bash
node scripts/check-test-comments.js
```

### 2. `fix-test-comments.js`
**功能**: 自动修复测试用例注释中的关键词顺序问题

### 3. `extract-test-cases.js`
**功能**: 抽取测试用例生成Markdown格式的详细报告

### 4. `extract-test-cases-json.js`
**功能**: 抽取测试用例生成结构化JSON格式报告

既存のテストケースコードから情報を抽出し、Excel 形式の詳細なテスト報告書を生成します。

**機能:**
- 全プロジェクトのテストファイルを自動スキャン
- JSDoc コメントから試験観点、試験対象、試験手順、確認項目を抽出
- プロジェクト別にシートを分けて整理
- 概要シートで全体統計を表示

**使用方法:**
```bash
# npm コマンドで実行
npm run generate-test-excel

# または直接実行
node scripts/generate-test-excel.js
```

**出力:**
- `docs-delivery/unit-test-report/JCS_テスト報告書_[タイムスタンプ].xlsx`

### 2. テストケース Excel テンプレート作成 (`create-test-template.js`)

手動でテストケースを記入するための Excel テンプレートを作成します。

**機能:**
- 標準的なテストケース記入フォーマット
- サンプルデータ付き
- 統計情報シート
- 日本語フォント対応

**使用方法:**
```bash
# npm コマンドで実行
npm run create-test-template

# または直接実行
node scripts/create-test-template.js
```

**出力:**
- `docs-delivery/unit-test-report/JCS_単元テスト報告書_テンプレート.xlsx`

## 🚀 セットアップ

### 前提条件
- Node.js (v14 以上)
- npm

### 依存関係のインストール
```bash
# プロジェクトルートで実行
npm install
```

## 📊 生成される Excel ファイルの構造

### テスト報告書 (`generate-test-excel.js`)

#### シート構成:
1. **テスト概要** - 全体統計とプロジェクト別サマリー
2. **jcs-endpoint-nextjs** - フロントエンドテストケース
3. **jcs-backend-services-standard** - 標準バックエンドテストケース  
4. **jcs-backend-services-long-running** - 長時間実行バックエンドテストケース

#### 各シートの列構成:
| 列 | 内容 | 説明 |
|---|---|---|
| No. | 連番 | テストケースの通し番号 |
| テストケース名 | テスト名 | `it()` または `test()` の第一引数 |
| 試験観点 | 観点 | JSDoc の `試験観点:` から抽出 |
| 試験対象 | 対象 | JSDoc の `試験対象:` から抽出 |
| 試験手順 | 手順 | JSDoc の `試験手順:` から抽出 |
| 確認項目 | 確認 | JSDoc の `確認項目:` から抽出 |

### テンプレート (`create-test-template.js`)

#### シート構成:
1. **単元テスト報告書** - メインのテストケース記入シート
2. **テスト統計** - 進捗管理用統計シート

## 💡 テストケースコメントの書き方

スクリプトが正しく情報を抽出できるよう、以下の形式でコメントを記述してください：

```javascript
/**
 * 試験観点: 正常なデータ処理の確認
 * 試験対象: ユーザー登録機能
 * 試験手順: 
 *   1. 有効なユーザーデータを準備する
 *   2. 登録APIを呼び出す
 *   3. レスポンスを確認する
 * 確認項目: 
 *   - ステータスコード200が返されること
 *   - ユーザーIDが正しく生成されること
 */
it('正常系：有効なデータでユーザー登録が成功する', async () => {
  // テストコード
});
```

## 🔧 カスタマイズ

### プロジェクト設定の変更

`generate-test-excel.js` の `PROJECTS` 配列を編集することで、対象プロジェクトを変更できます：

```javascript
const PROJECTS = [
  {
    name: 'プロジェクト名',
    path: 'テストファイルのパス',
    title: 'Excel シートに表示するタイトル'
  }
];
```

### Excel スタイルの変更

各スクリプト内の以下の部分を編集してスタイルをカスタマイズできます：
- フォント設定
- 色設定 (`fgColor`)
- 列幅設定
- 境界線スタイル

## 📁 出力ファイルの場所

すべての生成ファイルは `docs-delivery/unit-test-report/` ディレクトリに保存されます。

## ⚠️ 注意事項

1. **文字エンコーディング**: テストファイルは UTF-8 で保存してください
2. **ファイル名**: テストファイルは `.test.ts` または `.test.tsx` で終わる必要があります
3. **コメント形式**: JSDoc 形式のコメントのみが抽出対象です
4. **Excel 互換性**: 生成されるファイルは Excel 2016 以降で動作確認済みです

## 🐛 トラブルシューティング

### よくある問題

**Q: テストケースが抽出されない**
A: 以下を確認してください：
- ファイル名が `.test.ts` または `.test.tsx` で終わっているか
- JSDoc コメントが正しい形式で記述されているか
- テストファイルが指定されたディレクトリに存在するか

**Q: Excel ファイルが開けない**
A: 以下を試してください：
- Excel のバージョンが 2016 以降であることを確認
- ファイルが完全にダウンロード/生成されていることを確認
- 他のアプリケーションでファイルが開かれていないことを確認

**Q: 日本語が文字化けする**
A: Excel の設定で以下を確認してください：
- システムロケールが日本語に設定されているか
- フォントが日本語対応フォント（メイリオ等）に設定されているか

## 📞 サポート

問題が発生した場合は、以下の情報と共にお問い合わせください：
- 実行したコマンド
- エラーメッセージ（あれば）
- Node.js のバージョン (`node --version`)
- 対象のテストファイルの例