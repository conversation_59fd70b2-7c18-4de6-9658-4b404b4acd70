# 系统核心架构图与解读

## 1. 引言

本文档旨在提供JCS端点资产与任务管理系统的核心架构图，并对其主要构成和关键交互进行高级抽象层面的解读。它是对主系统架构设计文档（SAD）中核心可视化内容的独立展示，便于快速理解系统整体结构和运作模式。

更详细的架构设计、组件职责、数据模型、安全设计等内容，请参考完整的《[JCS 端点资产与任务管理系统 - 架构设计文档](./system-architecture.md)》。

## 2. 核心架构图

以下是本系统的核心架构图，展示了主要组件、它们之间的交互以及关键数据流。

```mermaid
graph TD
    %% 定义样式
    classDef user_nextjs_app fill:#e3f2fd,stroke:#1976d2,stroke-width:2px;
    classDef keycloak_idp fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px;
    classDef servicebus fill:#fce4ec,stroke:#c2185b,stroke-width:2px;
    classDef az_function fill:#fff3e0,stroke:#f57c00,stroke-width:2px;
    classDef az_function_timeout fill:#ffebee,stroke:#c62828,stroke-width:2px;
    classDef database fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px;
    classDef storage fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px;
    classDef automation fill:#e0f7fa,stroke:#0097a7,stroke-width:2px;
    classDef vm_hrw_components fill:#f1f8e9,stroke:#689f38,stroke-width:2px;
    classDef monitor_ops fill:#fafafa,stroke:#616161,stroke-width:2px;

    %% 用户与Next.js应用层
    subgraph "应用与用户交互层 (4)"
        direction LR
        User["👤 用户 (顾客系统管理者)"] -- "访问/登录/操作" --> NextJsApp["🌐 Next.js 应用<br/>(门户Frontend + API Backend)"]:::user_nextjs_app
        NextJsApp -- "认证交互 (OIDC)" --> Keycloak["🔐 Keycloak (IdP)"]:::keycloak_idp
    end

    %% 消息队列层 (Azure Service Bus)
    subgraph "消息与事件驱动层 (5)"
        direction TB
        NextJsApp -- "任务消息 (Task.id,...)" --> SB_TaskInputQ["📥 SB: TaskInputQueue (任务输入队列)"]:::servicebus
        SB_TaskInputQ -.-> SB_TaskInputQ_DLQ["DLQ (TaskInputQueue)"]:::servicebus -- "触发" --> TaskExecuteTimeoutFunc["⚡ Func: TaskExecuteTimeoutFunc<br/>(任务执行超时处理)"]:::az_function_timeout

        NextJsApp -- "取消消息 (Task.id)" --> SB_TaskControlQ["🔄 SB: TaskControlQueue (任务控制队列)"]:::servicebus
        SB_TaskControlQ -.-> SB_TaskControlQ_DLQ["DLQ (TaskControlQueue)"]:::servicebus -- "触发" --> TaskCancelTimeoutFunc["⚡ Func: TaskCancellationTimeoutFunc<br/>(任务取消超时处理)"]:::az_function_timeout
        
        RunbookMonitorFunc -- "Runbook原始结果消息" --> SB_TaskStatusQ["📤 SB: TaskStatusQueue (任务状态队列)"]:::servicebus
        SB_TaskStatusQ -.-> SB_TaskStatusQ_DLQ["DLQ (TaskStatusQueue)"]:::servicebus -- "触发" --> StatusProcessorTimeoutFunc["⚡ Func: StatusProcessorTimeoutFunc<br/>(状态处理超时)"]:::az_function_timeout
    end

    %% 后端核心服务 (Azure Functions)
    subgraph "后端核心服务 (6)"
        direction TB
        SB_TaskInputQ --> TaskExecuteFunc["⚡ Func: TaskExecuteFunc<br/>(任务执行函数)"]:::az_function
        TaskExecuteFunc -- "读/写" --> SQLDB["💾 Azure SQL Database (数据库)"]:::database
        TaskExecuteFunc -- "管理" --> AzureFiles_Work["📁 Azure Files (工作区)"]:::storage
        TaskExecuteFunc -- "启动Runbook作业<br/>(指定目标HRW Group)" --> AzAutomationAPI["🤖 Azure Automation API"]:::automation
        
        SB_TaskControlQ --> TaskCancellationFunc["⚡ Func: TaskCancellationFunc<br/>(任务取消函数)"]:::az_function
        TaskCancellationFunc -- "条件更新" --> SQLDB

        SB_TaskStatusQ --> StatusProcessorFunc["⚡ Func: StatusProcessorFunc<br/>(状态处理函数)"]:::az_function
        StatusProcessorFunc -- "读/写/删" --> AzureFiles_Work
        StatusProcessorFunc -- "上传" --> Blob_FinalOutput["📦 Azure Blob Storage (最终成果物)"]:::storage
        StatusProcessorFunc -- "更新" --> SQLDB

        TimerTrigger["⏰ 定时触发器<br/>(按 RUNBOOK_MONITOR_INTERVAL_SECONDS)"] --> RunbookMonitorFunc["⚡ Func: RunbookMonitorFunc<br/>(Runbook作业监控与结果轮询)"]:::az_function
        RunbookMonitorFunc -- "状态查询/更新" --> SQLDB
        RunbookMonitorFunc -- "作业状态/输出获取, 作业停止(若超时/异常)" --> AzAutomationAPI
        
        TaskExecuteTimeoutFunc -- "更新/读" --> SQLDB; TaskExecuteTimeoutFunc -- "停止作业(可能)" --> AzAutomationAPI; TaskExecuteTimeoutFunc -- "清理" --> AzureFiles_Work
        TaskCancelTimeoutFunc -- "更新(可能)" --> SQLDB
        StatusProcessorTimeoutFunc -- "更新" --> SQLDB; StatusProcessorTimeoutFunc -- "清理" --> AzureFiles_Work
    end

    %% 自动化执行层 (Azure Automation & HRW)
    subgraph "自动化执行层 (7)"
        direction TB
        AzAutomationAPI -- "调度作业到特定HRW Group" --> HRW_Group["🌐 HRW Group (HRW组)<br/>(例如: hrwg-&lt;azureVmName&gt;)"]:::automation
        HRW_Group --> VM_HRW["🤖 VM: Hybrid Runbook Worker (HRW) Agent"]:::vm_hrw_components
        VM_HRW -- "执行Runbook脚本" --> VM_Runbook["📜 VM: PowerShell Runbook"]:::vm_hrw_components
    end
    
    %% 监控 & 运维
    subgraph "监控与运维 (10)"
        AzureMonitor["📊 Azure Monitor<br/>(日志, 指标, 告警)"]:::monitor_ops
        NextJsApp -.-> AzureMonitor; TaskExecuteFunc -.-> AzureMonitor; TaskCancellationFunc -.-> AzureMonitor; StatusProcessorFunc -.-> AzureMonitor; RunbookMonitorFunc -.-> AzureMonitor
        TaskExecuteTimeoutFunc -.-> AzureMonitor; TaskCancelTimeoutFunc -.-> AzureMonitor; StatusProcessorTimeoutFunc -.-> AzureMonitor
        AzAutomationAPI -- "作业日志/状态" --> AzureMonitor
        VM_Runbook -- "脚本日志 (通过AMA)" --> AzureMonitor; SQLDB -- "性能/日志" --> AzureMonitor; HRW_Group -- "Agent健康状况" --> AzureMonitor
        SB_TaskInputQ -- "队列深度/DLQ计数" --> AzureMonitor
        SB_TaskStatusQ -- "队列深度/DLQ计数" --> AzureMonitor
    end
    
    SQLDB <-. "数据存取" .-> NextJsApp; 
    Blob_FinalOutput <-. "文件上传/下载链接生成" .-> NextJsApp;
```

## 3. 架构解读

本系统采用基于Azure PaaS服务的云原生架构，旨在实现用户友好的门户体验和可靠的后台任务自动化。其核心架构可以从以下几个逻辑层面进行解读：

### 3.1. 应用与用户交互层
*   **Next.js 应用**: 作为系统的统一入口，提供前端用户界面（UI）和后端API逻辑（通过API Routes或Server Actions实现）。用户通过浏览器访问此应用，进行身份认证、信息查阅和任务发起等操作。
*   **Keycloak**: 外部身份提供商（IdP），负责所有用户的认证（包括MFA）和授权令牌的颁发。Next.js应用与其集成，确保访问安全。

### 3.2. 消息与事件驱动层
*   **Azure Service Bus**: 作为系统内部各后端组件之间异步通信的核心。通过定义不同的队列（任务输入队列`TaskInputQueue`、任务状态队列`TaskStatusQueue`、任务控制队列`TaskControlQueue`），实现任务请求、状态反馈和控制命令的可靠解耦传递。
*   **死信队列 (DLQ)**: 每个主队列都配有DLQ，用于捕获处理失败（如对应Function超时）的消息，并由专门的超时处理Function进行补偿或记录。

### 3.3. 后端核心服务层 (Azure Functions)
一系列无服务器Azure Functions构成了后端的核心业务处理和编排逻辑：
*   **`TaskExecuteFunc`**: 监听`TaskInputQueue`，负责新任务的接收、校验、并发控制（通过`ContainerConcurrencyStatus`表）、工作区准备（Azure Files），并将Runbook作业提交到Azure Automation（指定目标HRW Group）。
*   **`TaskCancellationFunc`**: 监听`TaskControlQueue`，处理用户发起的任务取消请求（主要针对未开始执行的任务）。
*   **`RunbookMonitorFunc`**: (定时触发)
    1.  **超时监控**: 监控长时间运行（状态为`RUNBOOK_SUBMITTED`）的Azure Automation作业，若超过预设阈值（通过环境变量`RUNBOOK_TIMEOUT_SECONDS`配置），则尝试停止该作业。
    2.  **结果轮询**: 定期轮询所有已提交到Azure Automation且状态为`RUNBOOK_SUBMITTED`的作业，获取其最终执行状态（成功/失败/被中止）和原始结果/错误信息。
    3.  **结果转发**: 将获取到的作业结束事件（包含原始结果）作为消息发送到`TaskStatusQueue`，并更新`Task`表状态为`PROCESSING_COMPLETE`。
*   **`StatusProcessorFunc`**: 监听`TaskStatusQueue`，接收`RunbookMonitorFunc`转发的原始作业结果。负责执行最终的后处理操作，包括：将结果文件从Azure Files工作区归档到Azure Blob Storage，更新`Task`表为最终状态（`COMPLETED_SUCCESS`或`COMPLETED_ERROR`），释放并发锁，清理工作区。
*   **各类超时处理Functions**: 监听对应主队列的DLQ，处理因主Function执行超时而产生的消息，执行补偿逻辑或记录详细错误。

### 3.4. 自动化执行层
*   **Azure Automation Account**: 存储、管理和执行PowerShell Runbook。
*   **Hybrid Runbook Worker (HRW) & Group**: HRW Agent部署在目标VM上，负责实际执行Runbook脚本。每个目标VM配置为一个独立的HRW Group (例如 `hrwg-<azureVmName>`)，由`TaskExecuteFunc`在提交作业时指定，以实现精确调度。
*   **PowerShell Runbooks**: 在HRW上执行，负责与目标VM上的Docker容器交互，执行具体业务逻辑（如日志导出、定义导入/导出），并在Azure Files工作区进行文件操作。其执行结果和错误通过标准输出流暴露给Azure Automation，由`RunbookMonitorFunc`轮询获取。

### 3.5. 数据持久化与存储层
*   **Azure SQL Database**: 存储系统的核心结构化数据，如任务详情 (`Task`表，包含内部状态如`QUEUED`, `PENDING_CANCELLATION`, `RUNBOOK_SUBMITTED`, `PROCESSING_COMPLETE`等)、服务器配置 (`Server`表，包含`azureVmName`和`dockerContainerName`)、许可证信息 (`License`表)、容器并发状态 (`ContainerConcurrencyStatus`表)、操作日志元数据 (`OperationLog`表) 以及系统配置值 (`LOV`表)。
*   **Azure Blob Storage**: 用于持久化存储最终的成果物（如导出的操作日志、导出的管理项目定义文件）以及用户上传的临时文件（如导入用的CSV）。通过SAS Token机制提供安全下载。
*   **Azure Files**: 作为Azure Automation Runbook执行时的主要临时文件共享工作区，用于VM与云端Functions之间中继文件。

### 3.6. 监控与运维层
*   **Azure Monitor**: 集中收集和分析来自系统所有组件的日志、指标和跟踪数据，提供告警功能，保障系统的可观测性和可维护性。

## 4. 关键交互流程示例：后台任务处理

1.  用户通过Next.js门户界面发起一个后台任务（例如，为某个服务器导出操作日志）。
2.  Next.js后端的Server Action (`createTaskAction`) 接收请求，进行初步校验，在`Task`表中创建一条状态为`QUEUED`的记录，并将包含任务参数的消息发送到`TaskInputQueue`。
3.  `TaskExecuteFunc`监听到新消息，进行并发检查（查询并更新`ContainerConcurrencyStatus`表），准备Azure Files工作区，然后调用Azure Automation API将对应的PowerShell Runbook作业提交到目标服务器的HRW Group，并更新`Task`状态为`RUNBOOK_SUBMITTED`。
4.  VM上的HRW Agent接收并执行Runbook。Runbook将结果输出到Azure Files工作区，并通过标准流报告其执行情况。
5.  `RunbookMonitorFunc`定时轮询Azure Automation。
    *   如果发现某个`RUNBOOK_SUBMITTED`状态的作业已运行超时，则尝试停止该作业，构造超时失败消息发送到`TaskStatusQueue`，并更新`Task`状态为`PROCESSING_COMPLETE`。
    *   如果发现某个`RUNBOOK_SUBMITTED`状态的作业已正常结束（成功或失败），则获取其原始结果/错误，构造相应的消息发送到`TaskStatusQueue`，并更新`Task`状态为`PROCESSING_COMPLETE`。
6.  `StatusProcessorFunc`监听到来自`TaskStatusQueue`的消息，解析原始结果。
    *   如果Runbook成功：将结果文件从工作区归档到Azure Blob Storage，更新`Task`状态为`COMPLETED_SUCCESS`，记录归档路径。
    *   如果Runbook失败或超时：更新`Task`状态为`COMPLETED_ERROR`，记录错误详情。
    *   统一进行并发锁释放和工作区清理。
7.  用户可以在门户的“任务列表”界面查看任务的最终状态和结果（如下载链接或错误信息）。

## 5. 设计原则摘要

*   **异步与解耦**: 通过Azure Service Bus实现核心流程的异步化和组件解耦。
*   **状态驱动**: `Task`表中的状态精确驱动后续处理流程。
*   **集中监控与轮询**: `RunbookMonitorFunc`集中负责对Automation作业的超时监控和结果获取。
*   **PaaS优先**: 最大化利用Azure PaaS服务的能力。
*   **安全设计**: 身份认证、授权、密钥管理、网络安全、数据安全贯穿始终。
