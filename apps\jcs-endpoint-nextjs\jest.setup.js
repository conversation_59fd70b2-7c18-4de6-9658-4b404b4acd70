/**
 * @file jest.setup.js
 * @description 
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

// Optional: configure or set up a testing framework before each test.
// If you delete this file, remove `setupFilesAfterEnv` from `jest.config.js`

// Used for __tests__/testing-library.js
// Learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom'

// グローバルで iron-session をモックし、session エラーを回避
jest.mock("iron-session", () => ({
  getIronSession: jest.fn(() => Promise.resolve({ user: { licenseId: "license123" } }))
}));

// グローバルで next/headers をモックし、cookies エラーを回避
jest.mock("next/headers", () => ({
  cookies: jest.fn(() => ({
    get: jest.fn(() => ({ value: "dummy-session" })),
    set: jest.fn(),
    delete: jest.fn(),
  })),
}));
