describe("初期化表示のテスト", () => {
  describe("メイン画面", () => {
    const validCredentials = {
      userId: "hitachi.taro.aa",
      password: "changeit!@#",
    };

    beforeEach(() => {
      cy.visit("/login");
      cy.get("#userId").type(validCredentials.userId);
      cy.get("#password").type(validCredentials.password);
      cy.get("button").click();
    });

    it("ナビゲーションバーが正しく表示される", () => {
      cy.get("header").should("not.contain", "JP1 Cloud Service");
      cy.get("header").should("contain", "現在のユーザー:");
      cy.get("header").should("contain", "hitachi.taro.aa");
      cy.get("header button").contains("ログアウト");
      cy.get("header button").contains("パスワード変更");
      cy.get("header button").contains("お知らせ");
      cy.get("header button").contains("ライセンス");
    });

    it("サイドバーが正しく表示される", () => {
      cy.get("aside").should("contain", "管理");
      cy.get("aside").should("contain", "サーバ一覧");
      cy.get("aside").should("contain", "ファイル");
      cy.get("aside").should("contain", "操作ログ一覧");
      cy.get("aside").should("contain", "製品媒体一覧");
      cy.get("aside").should("contain", "マニュアル一覧");
      cy.get("aside").should("contain", "提供ファイル一覧");
      cy.get("aside").should("contain", "サポート情報一覧");
      cy.get("aside .from-blue-600").should("contain", "サーバ一覧");
    });

    it("パンくずリストが正しく表示される", () => {
      cy.get("nav").should("contain", "管理");
      cy.get("nav").should("contain", "サーバ一覧");
      cy.get("nav img").should("have.attr", "src");
    });
  });
});
