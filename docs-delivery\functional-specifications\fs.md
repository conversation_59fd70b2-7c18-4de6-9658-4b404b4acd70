**WORDの機能を利用して、最新の目次を生成する。**

**目次はリボンの［参考資料］－［目次］－「自動作成の目次2」を使用して作成する。**

# 概要

## 開発背景

ユーザーに資産配布管理サービスを使用するための窓口としてポータルシステムを提供する。

## 機能の目的

ユーザー（顧客システム管理者）は、Ｗｅｂブラウザを通して、ポータルシステムにアクセスしサービスを利用できる。JP1/ITDM2（統括マネージャ）、JP1/ITDM2（中継マネージャ）、秘文（管理コンソール）の全体的な資産配布管理情報の参照ができ、詳細情報は各製品の画面より確認することができる。

システム管理者（資産配布管理サービスの管理者）は、ポータルＷｅｂアプリ、ポータルＤＢ、JP1/ITDM2（統括マネージャ）、JP1/ITDM2（中継マネージャ）、秘文（管理コンソール）を用意し管理する。

# ユースケース

(省略)

# キャプションおよびバージョン情報

(省略)

# 資産配布管理サービス ポータル機能

資産配布管理サービスのユーザー（顧客システム管理者）は、ポータルシステムへアクセスしてサービスを利用できる。Webブラウザ（Microsoft
Edge と Google
Chrome、言語種別は日本語のみ、表示言語は日本語のみ）を使用してポータル画面を操作することでサービスを利用できる。

<img src="media/image2.png" style="width:6.33958in;height:1.42708in" />

図4.1　画面イメージ

表4.1ポータルシステム画面の機能一覧

<table>
<colgroup>
<col style="width: 7%" />
<col style="width: 29%" />
<col style="width: 62%" />
</colgroup>
<thead>
<tr>
<th>#</th>
<th>機能名称</th>
<th>概要</th>
</tr>
</thead>
<tbody>
<tr>
<td><ol type="1">
<li></li>
</ol></td>
<td>ログイン</td>
<td>ポータルへログインし、サービス利用を開始する。</td>
</tr>
<tr>
<td><ol start="2" type="1">
<li></li>
</ol></td>
<td>メイン画面</td>
<td>ナビゲーションバー、サイドバーとパンくずリストを表示する。</td>
</tr>
<tr>
<td><ol start="3" type="1">
<li></li>
</ol></td>
<td>サーバ一覧</td>
<td>資産配布管理情報表示のハイパーリンクを表示する。各サーバの管理項目定義を個別にインポート・エクスポートすることと、期間を指定して期間中の操作ログをエクスポートすることが可能。</td>
</tr>
<tr>
<td><ol start="4" type="1">
<li></li>
</ol></td>
<td>お知らせ情報表示</td>
<td>ポータルのシステム管理者から、ユーザーへのメッセージを表示する。</td>
</tr>
<tr>
<td><ol start="5" type="1">
<li></li>
</ol></td>
<td>ログアウト</td>
<td>ポータルからログアウトし、サービスの利用を終了し、ログイン待ち状態に戻る。</td>
</tr>
<tr>
<td><ol start="6" type="1">
<li></li>
</ol></td>
<td>パスワード変更</td>
<td>ユーザーが自分のパスワードを変更する。</td>
</tr>
<tr>
<td><ol start="7" type="1">
<li></li>
</ol></td>
<td>ライセンス情報</td>
<td>ユーザーが契約しているライセンスの情報を表示する。</td>
</tr>
<tr>
<td><ol start="8" type="1">
<li></li>
</ol></td>
<td>操作ログ一覧</td>
<td>ユーザーが本サービスの操作ログ機能で取得されている操作ログを確認する。</td>
</tr>
<tr>
<td><ol start="9" type="1">
<li></li>
</ol></td>
<td>製品媒体一覧</td>
<td>ユーザーが利用可能な製品媒体を確認する。</td>
</tr>
<tr>
<td><ol start="10" type="1">
<li></li>
</ol></td>
<td>マニュアル一覧</td>
<td>ユーザーが利用可能なマニュアルを確認する。</td>
</tr>
<tr>
<td><ol start="11" type="1">
<li></li>
</ol></td>
<td>提供ファイル一覧</td>
<td>ユーザーが提供ファイルを確認する。</td>
</tr>
<tr>
<td><ol start="12" type="1">
<li></li>
</ol></td>
<td>サポート情報一覧</td>
<td>ユーザーがサポート情報を確認する。</td>
</tr>
<tr>
<td><ol start="13" type="1">
<li></li>
</ol></td>
<td>タスク一覧</td>
<td>ユーザーが操作ログのエクスポート、管理項目定義のインポート・エクスポートの実行結果を確認する。実行待ちのタスクを中止することが可能。</td>
</tr>
</tbody>
</table>

ポータルシステムのシステム構成図を以下に示す。

![](media/image3.png)

図4.2　システム構成図

## ログイン

### 目的

不正なユーザーによるアクセスを防止するためにログイン認証を実施する。

### 前提条件

未ログインの状態が必要である。システム管理者は、ユーザーがポータルシステムを利用する前に以下の情報は、システム管理者はKEYCLOAKシステムのユーザー管理機能を操作してユーザーを作成し、ユーザーID、契約IDとパスワードを設定する必要があります。

[KEYCLOAKにおけるユーザー作成参照](#keycloakにおけるユーザー作成)

### 制限事項

特になし。

### 注意事項

特になし。

### 他FSとの関係

特になし。

### 機能詳細

資産配布管理サービスのユーザー（顧客システム管理者）は、ポータルシステムへアクセスしてサービスを利用する。

アクセスした際に、portalの初期画面からKEYCLOAKのログイン画面に遷移し、ユーザーIDとパスワードを入力し、検証成功後にモバイル検証を行い、モバイル検証成功後に、利用を開始する。

ログインした後次の画面（[サーバ一覧](#メイン画面)）へ遷移する。

ポータルのログイン状態は30分有効であり、有効期限はKEYCLOAKのレルムで設定できる、ブラウザの起動設定で「新しいタブを開く」が適用されている場合、ブラウザを閉じることでユーザーがログアウトできるようになっている。

<img src="media/image7.png" style="width:6.15208in;height:5.30694in" />

図4.1.6 (1)　画面イメージ(portalトップページ)

<img src="media/image8.png" style="width:6.33958in;height:5.32292in" />

図4.1.6 (2)　画面イメージ(Keycloakログイン画面)

<img src="media/image9.png" style="width:6.33958in;height:5.34514in" />

図4.1.6 (3)　画面イメージ(Keycloakモバイル初回検証画面)

<img src="media/image10.png" style="width:6.33958in;height:5.29653in" />

図4.1.6 (4)　画面イメージ(Keycloakモバイル初回以外検証画面)

<img src="media/image11.png" style="width:6.33958in;height:5.31597in" />

図4.1.6 (5)　画面イメージ(Keycloakログイン失敗画面)

#### ログイン手順

ポータルログイン画面のログインボタンをクリックし、KEYCLOAKのログイン検証画面遷移、管理者がKEYCLOAK側に作成したアカウントとパスワードを入力して、KEYCLOAK
テーブル上にあるユーザーと暗号化されたパスワードを照合し、一致の場合モバイル認証画面を遷移して、モバイル持っているワンタイムコードを入力して、送信ボタンを押下して、認証も成功の場合ログインが許可される。

#### 画面項目

「ユーザーID」：8-50小文字の半角英数字とドットマークが入力できる。

「パスワード」：パスワード規則は以下とする。

・8文字以上、128文字以下

・半角英数字、および次に示す記号を使用

「!」、「"」、「#」、「\$」、「%」、「&」、「'」、「(」、「)」、「\*」、「+」、「,」、「-」、「.」（ピリオド）、「/」、「:」、「;」、「\<」、「=」、「\>」、「?」、「@」、「\[」、「\\、「\]」、「^」、「\_」、「\`」、「{」、「\|」、「}」、「~」、および半角スペース

#### ログイン操作

ユーザーID、パスワードを入力した後、ボタンを押下しログインできる。

初期表示時エラーエリアは表示されない。ログインに失敗したときにエラーエリアが表示され，エラーの詳細が表示される。

#### お知らせエリア

お知らせエリアは、ポータルシステムテーブルから下記の条件を満たした「お知らせ」を抽出して、表示する。

①システムに共通のお知らせを表示する。

#### タブオーダー

タブオーダーは「ユーザーID」→「パスワード」→「ログイン」の順とする。

#### エラー処理

KEYCLOAK画面の「ユーザーID」や「パスワード」の誤り（KEYCLOAKテーブル上のユーザーID、暗号化されたパスワードとの不一致）、入力文字数不足や文字数オーバの場合、入力が制限されている文字を入力した場合、KEYCLOAKテーブルへのアクセスでエラーがあった場合は、パスワードをクリアし、ユーザー名とパスワードの入力ボックスを赤くし、ユーザー名の入力ボックスの下にエラーメッセージを表示します：

「無効なユーザー名またはパスワードです。」

#### 環境無効化時のログイン抑止機能

\[契約情報\]-\[環境無効化フラグ\]が有効の場合、当該契約IDに結びつくすべてのユーザーIDでのログインが抑止される。

\[環境無効化フラグ\]は、サービスの運用者が設定する。例)
サービス利用開始日前まで環境無効化フラグを有効(ポータル利用不可)にし、サービス利用開始日にフラグを無効(ポータル利用可能)に変更する。

| 項番 | 入力ユーザーID | 入力パスワード | 環境無効化フラグ | 　 | ポータルの動作 |
|---:|----|----|----|----|----|
| 1 | DBに存在する(正常) | DBのパスワードと一致(正常) | 無効(利用可能) | 　 | 【正常】ログインを完了しメイン画面に遷移 |
| 2 | 　 | 　 | 有効(利用不可) | 　 | 【異常】エラーメッセージ(利用不可)を表示しログインを抑止 |
| 3 | 　 | DBのパスワードと不一致(異常) | 無効(利用可能) | 　 | 【異常】エラーメッセージ(ID/PW不正)を表示しログインを抑止 ※1 |
| 4 | 　 | 　 | 有効(利用不可) | 　 | 【異常】エラーメッセージ(ID/PW不正)を表示しログインを抑止 ※1 |
| 5 | DBに存在しない(異常) | － | － | 　 | 【異常】エラーメッセージ(ID/PW不正)を表示しログインを抑止 |

#### ロックアウト機能

一定回数でパスワード入力ミスによるログインエラーが続いた場合、当該ユーザーIDが一定時間ロックアウトされログインできなくなる。

ロックアウト閾値は10回、ロックアウト時間は30分とする。(10回連続でパスワードの認証ができなかった場合、keycloakはアカウントをロックします、11回目以降はログイン不可とし、30分経過により再度ログインが可能とする)

ロックアウトを検知した場合、ポータルはDBの\[ユーザー情報\]の「ロックアウトフラグ」を有効化してロックアウト状態とする。

Keycloakはログイン時、ロックアウト最終更新日時を参照して、ログインしようとしているユーザーIDがロックアウト状態であるかどうかチェックする。

※環境無効フラグがオンの時ログイン抑止機能が優先で実施される。

[(7)
環境無効化時のログイン抑止機能を参照してください。](#環境無効化時のログイン抑止機能)

<table>
<colgroup>
<col style="width: 4%" />
<col style="width: 12%" />
<col style="width: 12%" />
<col style="width: 12%" />
<col style="width: 10%" />
<col style="width: 14%" />
<col style="width: 10%" />
<col style="width: 21%" />
</colgroup>
<thead>
<tr>
<th style="text-align: center;">項番</th>
<th style="text-align: center;">入力ユーザーID</th>
<th style="text-align: center;">入力パスワード</th>
<th style="text-align: center;">ロックアウトフラグ</th>
<th style="text-align: center;">ロックアウトからの経過時間</th>
<th style="text-align: center;">ポータルの動作</th>
<th style="text-align: center;">ロックアウトカウントの遷移</th>
<th style="text-align: center;">ロックアウトフラグの遷移</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align: right;">1</td>
<td rowspan="4">DBに存在する(正常)</td>
<td rowspan="3">DBのパスワードと一致(正常)</td>
<td>ロックアウトしていない(正常)</td>
<td>－</td>
<td>【正常】ログインを完了しメイン画面に遷移</td>
<td>0回にリセット</td>
<td>遷移なし(無効のまま)</td>
</tr>
<tr>
<td style="text-align: right;">2</td>
<td rowspan="2">ロックアウトしている(異常)</td>
<td>30分未満</td>
<td>【異常】エラーメッセージ(ロックアウト中)を表示しログインを抑止</td>
<td>遷移なし</td>
<td>遷移なし(有効のまま)</td>
</tr>
<tr>
<td style="text-align: right;">3</td>
<td>30分以上</td>
<td>【正常】ログインを完了しメイン画面に遷移</td>
<td>0回にリセット</td>
<td>有効→無効に遷移</td>
</tr>
<tr>
<td style="text-align: right;">4</td>
<td>DBのパスワードと不一致(異常)</td>
<td>　－</td>
<td>　－</td>
<td>【異常】エラーメッセージ(ID/PW不正)を表示しログインを抑止</td>
<td>1回カウントアップ</td>
<td><p>ロックアウトカウントのカウントアップ後の値により以下のいずれかの動作</p>
<p>(1)ロックアウトカウントが特定回数未満</p>
<p>ロックアウトフラグの遷移なし(無効のまま)</p>
<p>(2)ロックアウトカウントが特定回数以上</p>
<p>ロックアウトフラグを無効→有効に遷移</p></td>
</tr>
<tr>
<td style="text-align: right;">5</td>
<td>DBに存在しない(異常)</td>
<td>－</td>
<td>－</td>
<td>－</td>
<td>【異常】エラーメッセージ(ID/PW不正)を表示しログインを抑止</td>
<td>－</td>
<td>－</td>
</tr>
</tbody>
</table>

#### ログイン監査

ログイン成功の場合、ポータルの監査ログに保存する。

> ログイン失敗の場合、keycloakの監査ログに保存する。

## メイン画面

### 目的

ナビゲーションバー、サイドバーとパンくずリストを表示する。

### 前提条件

ログイン状態が必要である。

### パスワード制限事項

特になし。

### 注意事項

特になし。

### 他FSとの関係

特になし。

### 機能詳細

ログインした後、メイン画面に遷移する。画面上部で「ナビゲーションバー」を表示する。

「ナビゲーションバー」の右側に共通的な以下の機能を提供する：

ログインユーザーの情報表示（「現在のユーザー」で示されたユーザーID）

お知らせの表示（「お知らせ」ボタン）

ログインユーザーのパスワード変更（「パスワード変更」ボタン）

ログアウト（「ログアウト」ボタン）

ライセンス（ 「ライセンス」ボタン）

画面の左側に「サイドバー」を表示する。以下のメニューを提供する：

| 項番 | 一階層メニュー | 二階層メニュー | 動作 |
|----|----|----|----|
| 1 | 管理 |  | 管理の二階層メニューリストをオープン/クローズする |
| 2 |  | サーバ一覧 | サーバ一覧画面表示する |
| 3 |  | タスク一覧 | タスク一覧画面を表示する |
| 4 | ファイル |  | ファイルの二階層メニューリストをオープン/クローズする |
| 5 |  | 操作ログ一覧 | 操作ログ一覧画面表示する |
| 6 |  | 製品媒体一覧 | 製品媒体一覧画面表示する |
| 7 |  | マニュアル一覧 | マニュアル一覧画面表示する |
| 8 |  | 提供ファイル一覧 | 提供ファイル一覧画面表示する |
| 9 |  | サポート情報一覧 | サポート情報一覧画面表示する |

「ナビゲーションバー」の直下に「パンくずリスト」を表示する。

<img src="media/image12.png" style="width:6.225in;height:4.42708in" />

図4.2.6 (1)　画面イメージ

#### タブオーダー

タブオーダーは「ログアウト」→「パスワード変更」→「お知らせ」→「ライセンス」→「更新」→「フィルターの入力」→「フィルターの検索」→「フィルターのクリア」→「前ページ」(有効な場合)→「ページ数」(現在のページ番号を飛ばす)→「次ページ」(有効な場合)→「行数/ページの選択項目」→
一覧リストの部品
→「管理」→「サーバ一覧」→「タスク一覧」→「ファイル」→「操作ログ一覧」→「製品媒体一覧」→「マニュアル一覧」→「提供ファイル一覧」→「サポート情報一覧」→ブラウザの部品→「ログアウト」の順とする。

#### 更新アイコンボタン

「更新アイコンボタン」をクリックすると、検索条件をクリアされなく、一覧のソート、「ページ番号」と「行数/ページ」をリセットして最新の情報に更新する。

## サーバ一覧

### 目的

JP1/ITDM2（統括マネージャ）、JP1/ITDM2（中継マネージャ）と秘文（管理コンソール）の詳細情報を表示する。加えて、ユーザーは本画面から特定のサーバに対し、バックグラウンドで実行されるタスク（操作ログのエクスポート、管理項目定義のインポート、管理項目定義のエクスポートなど）を開始することができる。

### 前提条件

ログイン状態が必要である。

システム管理者は、ユーザーがポータルシステムを利用する前に以下の情報をポータルシステムテーブルに格納しておく：

- サーバ名

- 種別

- 管理画面URL

- 【非公開】各マネージャサーバに対応する、バックグラウンドタスクを実行するVM（Azure
  Automation Hybrid Runbook Worker (HRW)
  が動作するVM）上の対象Dockerコンテナ名

- 【非公開】各ライセンスに対応するAzure VM名および関連するAzure
  Automation Hybrid Runbook Workerグループ

- 【非公開】操作ログのエクスポート機能で指定できる期間の最大日数

  加えて、Azure環境において以下の設定が完了していること：

<!-- -->

- Azure Service Bus:
  タスク要求メッセージ用のTaskInputQueue、タスク制御コマンド（中止等）用のTaskControlQueue、タスク状態通知メッセージ用のRunbookStatusQueueが、それぞれの特性（例：配信不能キュー
  (dead-letter queue)
  の構成、メッセージの既定の有効期間など）とともにService
  Bus名前空間内に設定されている。

- Azure Functions:
  タスク実行関数TaskExecuteFunc、タスク中止関数TaskCancellationFunc、Runbookジョブ処理関数RunbookProcessorFuncとそれぞれの関数のタイムアウトを処理する関数TaskExecuteTimeoutFunc、TaskCancellationTimeoutFunc、RunbookProcessorTimeoutFunc、及びRunbookジョブ監視関数RunbookMonitorFuncがそれぞれのトリガー（Azure
  Service Busキュー、タイマー等）とバインディング（Azure SQL DB, Azure
  Files, Azure Blob Storage, Azure Automation等）とともに関数アプリ
  (Function App)
  内にデプロイ・設定されている。各関数はマネージドIDを使用して他のAzureサービスにアクセスする。

- Azure Automation:
  PowerShellベースの業務Runbook（例：Export-ContainerLogs.ps1,
  Import-ContainerData.ps1）がAutomationアカウントに登録され、公開されている。各ターゲットVMにはHybrid
  Runbook Worker (HRW)
  がインストール・構成され、適切なHRWグループに割り当てられており、Azure
  Automationサービスと通信可能である。VMはAzure
  Filesワークスペースにアクセスできる必要がある。

- Azure Files: Azure Automation
  Runbook実行時の一時的なファイル共有ワークスペース（例：\\storageaccount.file.core.windows.net\share_name\TaskWorkspaces\\が準備され、対象VM（HRW経由）およびAzure
  Functionsからアクセス可能であること。

- Azure SQL Database:
  タスク情報（タスクID、タスク種別、ステータス、サーバID等）、VM/コンテナのタスク実行状態（BUSY/IDLE）を管理するためのテーブルスキーマが定義され、データベースが利用可能であること。

### 制限事項

特になし。

### 注意事項

特になし。

### 他FSとの関係

特になし。

### 機能詳細（外部仕様）

サーバ一覧は、資産配布管理サービスのユーザーに以下の情報を表示する：

「サーバ名」：利用できるサーバの名称

「種別」：サーバの種別

「管理画面」：対象サーバ管理画面へのハイパーリンク

「タスク」：ITDM2のマネージャサーバ（単体・中継・統括問わず）の場合、当該サーバに対して実行可能なバックグラウンドタスクを選択するためのドロップダウンボタンを表示する。ITDM2のマネージャサーバ以外の場合は空白を表示する。バックグラウンドタスクには操作ログのエクスポート、管理項目定義のインポート、管理項目定義のエクスポートの3つがある。ライセンスプランによって操作ログのエクスポートをサポートしない場合もある。

ユーザーはこの画面から、表示されているサーバ一覧に対しフィルタリング検索を行うことができる。検索条件と一致するサーバが表示され、ユーザーはサーバ名、種別を確認することができ、特定のサーバに対しタスクを実行することができる。また、ユーザーは「管理画面」ハイパーリンクより各サーバの管理画面に遷移することができる。

タスクの「操作ログのエクスポート」を選択した場合、操作ログのエクスポート期間を指定するモーダルダイアログが表示される。ユーザーが無効な日付範囲、または指定できるエクスポート期間の最大日数よりも長い期間を指定した場合はエラーメッセージを表示する。

タスクの「管理項目定義のインポート」を選択した場合、インポートするCSVファイルを選択するためのファイルアップロードダイアログが表示される。このダイアログからはCSVファイルだけが選択できる。

タスクの「管理項目定義のエクスポート」を選択した場合、「管理項目定義をエクスポートしますか？」といった主旨の確認ダイアログが表示される。

ユーザーがタスクを選択し、確認ダイアログのOKボタンをクリックした後、対象サーバに他の実行中のタスクがなければ、「タスクの実行を受け付けました。実行状態はタスク一覧でご確認ください。対象サーバ：{0}　タスク種別：{1}」（※{0}：対象サーバ名　{1}：タスク種別名）といったメッセージを表示する。ユーザーはタスクの終了を待つ必要がなく、すぐに他の操作を行うことが可能である。タスクの進捗状況と最終結果は「タスク一覧」画面で確認する。

ユーザーがタスクを選択し、確認ダイアログのOKボタンをクリックした後、対象サーバに他の実行中のタスクがある場合、「{0}に対するタスクを実行中のため実行できません。実行中のタスクが完了してから再度実行してください。」（※{0}：対象サーバ名）といったメッセージを表示する。

<img src="media/image13.png" style="width:6.33958in;height:4.50833in" /><img src="media/image14.png" style="width:6.33819in;height:4.50694in" />図4.3.6
(1)　画面イメージ

図4.3.6 (2)　画面イメージ

#### タブオーダー

タブオーダーは列「管理画面」の上から下への順とする。

#### ハイパーリンク

ハイパーリンクをクリックすると、ブラウザの新しいタブに管理画面を開く。

#### フィルタリング

一覧の表示項目のフィルタリングができ、フィルターは一覧の中の全項目を検索対象にする，フィルターに入力した文字列が含まれる行がフィルタリングされて表示する。

フィルターに検索文字列を入力して、「虫眼鏡アイコン」を押したタイミングでフィルタリングが実行される。

「×」アイコンを押下したら、テキストボックスの文字列をクリアして、一覧の内容も検索条件にあわせて再表示する。

#### ソート

> 「タスク」以外の項目はソートができ、デフォルトのソート条件は「サーバ名」の辞書順（A→Z,
> 0→9の順番）とする。

①▲▼がついているカラムを選択したら、ソート順を逆にする。  
②▲▼がついていないカラムを選択したら、今までのソート条件を取り消し、選択したカラムの昇順▲でソートする。

#### ページネーション

- 一覧の表示項目のページネーションができ、ページ番号に対応するデータが表示され、異なる行数でページ分割してデータを表示する。

- ページネーションの選択肢は「10」、「30」、「50」ページ、デフォルトは\[10\]である。

### 機能詳細（内部仕様）

画面上に表示されるサーバ名、種別、管理画面のURLの情報はポータルデータベースの「サーバ」テーブルから取得される。

「ライセンス」テーブルからログインしているユーザーのライセンスが属する基本契約プランを取得・判定する。STANDARDとLIGHT_Bの場合はタスクドロップダウンメニューに操作ログのエクスポートサブメニューを表示する。LIGHT_Aの場合は操作ログのエクスポートサブメニューを表示しない。

操作ログのエクスポートタスクを選択した時に指定できるエクスポート期間の最大日数は、「値の一覧」テーブルの
OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN
の値で設定され、デフォルトで30日となる。

タスクを一意に識別するために、ポータルのサーバ側プログラムはUUIDとしてタスクIDを生成する。

ユーザーが実行するタスクを選択し、必要なパラメータ（エクスポートするログの期間、管理項目定義ファイル等）を指定した後、ポータルのサーバ側のプログラムはまず対象サーバに該当するVMコンテナのタスク実行状態（ステータス）を、データベースの「コンテナ実行状態」テーブルから事前確認する。コンテナが処理可能（処理可能というのは、コンテナのステータスがIDLEか、レコードが存在しない。レコードが存在しない場合はステータスがIDLEのコンテナレコードを1件作成する。）と判断された場合に限り、新規タスクを開始する。コンテナが他のタスクで使用中（ステータスがBUSY）の場合は、ユーザーに「タスクを実行中のため実行できません。実行中のタスクが完了してから再度実行してください。」といったメッセージを通知し、新規タスクを開始しない。コンテナのステータスがIDLE
とBUSYのどちらでもない場合は、「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。」といった主旨のメッセージをユーザーに通知し、新規タスクを開始しない。

管理項目定義のインポートタスクを選択した時、ユーザーがアップロードしたファイルに対し、サーバ側でファイルの拡張子についてチェックを行う。CSVファイルでない場合「無効なファイル形式です。」といった主旨のエラーメッセージをフロント側に返し、ユーザーに表示する。チェックOKの場合、サーバ側プログラムはファイルをAzure
Blob Storageへアップロードする。Azure Blob
Storageのディレクトリ構成は「6.2 Azure Blob
Storageディレクトリ構造の設計」を参照してください。

入力パラメータの検証とコンテナのタスク実行状態の確認によって、タスクの開始に問題はないと判断した場合、ポータルのサーバ側プログラムはタスクID・タスク名（{サーバ名}-{タスク種別}-{YYYYMMDDHHmmss}）・タスク種別・初期ステータス（QUEUED）・実行ユーザー・対象VM名・コンテナ名などの情報を含む新規タスクレコードを「タスク」テーブルに作成する。その後Azure
Service
BusのTaskInputQueueにタスクIDとタスク種別によって必要なパラメータなどの情報を含むタスク実行要求メッセージを送信し、画面上に受け付けのメッセージを表示する。

タスク処理はAzure FunctionsおよびAzure
Automationにより非同期で実行される。Azure
Functionsで実行されるポータル関数の詳細は「8.ポータル関数」を参照してください。Azure
Automationの機能については「9.Azure Automation と Hybrid Runbook
Worker」を、ジョブとして実行されるRunbook（基盤スクリプト）の詳細は基盤スクリプト設計書を参照してください。

「タスク」テーブルにおいて、タスクのレコードはマネージャサーバごとに、タスクの種別問わず、上限件数（「値の一覧」テーブルの
TASK_CONFIG.
MAX_RETENTION_COUNTの値で設定され、デフォルトで10件）まで保持される。対象サーバがすでに上限件数以上のタスクレコードを保持している場合に新規タスクが開始される時、タスク実行関数TaskExecuteFuncがレコードの削除を行う。詳細は「8.3.1
ポータル関数」の「(1) TaskExecuteFunc
(タスク実行関数)」を参照してください。

タスクはタイムアウトによりエラーで終了する可能性がある。タイムアウトに関する仕様は「8.3.2
Azure Functionsにおけるタイムアウトの仕様」と「9.3.3
Automationジョブのタイムアウトについて」を参照してください。

1.  
2.  
3.  
4.  1)  
    2)  
    3)  1.  
        2.  
        3.  
        4.  
        5.  
        6.  

## お知らせ表示

### 目的

サービス提供者からユーザーへのお知らせを表示する。

### 前提条件

ログイン状態が必要である。

システム管理者は、ユーザーに通知するお知らせについて、以下の情報をポータルシステムテーブルに格納しておく：

- お知らせ

### 制限事項

特になし。

### 注意事項

特になし。

### 他FSとの関係

特になし。

### 機能詳細

ログインした後、ナビゲーションバーの「お知らせ」ボタンを表示する。

「お知らせ」の内容として、システム管理者からのメッセージを表示する。

「お知らせ」ボタンを押下すると、「お知らせ」画面をポップアップする。

システム管理者は、すべてのユーザーに対して何かお知らせすることがあった場合にポータルシステムテーブルの「お知らせ」用の情報にメッセージを格納する。ポータルシステムテーブル上の「お知らせ」用の情報の領域はテキスト形式となる。

本画面では、お知らせエリアは、ポータルシステムテーブルから下記の条件を満たした「お知らせ」を抽出して、表示する。

①システムに共通のお知らせ

②ログインユーザーに紐づく契約プランIDと一致するお知らせ

③ログインユーザーに紐づく契約IDと一致するお知らせ

④ログインユーザーのユーザーIDと一致するお知らせ

「お知らせ」は固定表示、ポータルシステムテーブルより読み取った情報を「お知らせ」の下部に表示する。

ポータルシステムテーブルからの読み取りが失敗した場合は空欄とする。お知らせの内容がダイアログの表示領域に表示しきれない場合、スクロールバーを表示し、スクロールバーを下にスライドすることで全行表示できる。

「お知らせ」のポップアップの右上の「X」または右下の「閉じる」を押下すると、「お知らせ」画面を閉じる。

> 図4.4.6 (1)　画面イメージ
>
> ![](media/image15.png)

## ログアウト

### 目的

ユーザーのIDをログアウトする。

### 前提条件

ログイン状態が必要である。

### 制限事項

特になし。

### 注意事項

特になし。

### 他FSとの関係

特になし。

### 機能詳細

「ログアウト」ボタンを押下すると確認ダイアログがポップアップされるので、「OK」ボタンをクリックしてサービスの利用を終了することができる。ログアウトした後、ログイン画面に戻る。

「キャンセル」ボタンをクリックすると、ログアウトされなく、確認ダイアログが閉じられる。

「×」ボタンをクリックすると、ログアウトされなく、確認ダイアログが閉じられる。

「サーバ一覧」にてJP1/ITDM2の管理画面を開いた場合のJP1/ITDM2の画面とのログアウトの連動はしない。それぞれの画面よりログアウトする必要がある。ログアウトの監査ログを保存する。

![](media/image17.png)

図4.5.6 (1)　画面イメージ

## パスワード変更

### 目的

資産配布管理サービスのユーザーが自分のパスワードを変更するために使用する。

### 前提条件

ログイン状態である。

### 制限事項

特になし。

### 注意事項

特になし。

### 他FSとの関係

特になし。

### 機能詳細

ログインした後に、ナビゲーションバーの「パスワード変更」ボタンを表示する。「パスワード変更」ボタンをクリックすると、パスワード変更画面をポップアップする。

パスワード変更ダイアログは次の操作をすると表示する。

共通メニューから「パスワード変更」をクリックする。

これにより、ユーザーに対してパスワード変更のための画面をポップアップする。

表示項目は以下になる：

​「ユーザーID」ログインしているユーザーIDを表示する。

「現在のパスワード」現在のパスワードを入力する。

​「新しいパスワード​」新しいパスワードを入力する。

新しいパスワード​規則は以下とする。

・8文字以上、128文字以下

・半角英数字、および次に示す記号を使用

「!」、「"」、「#」、「\$」、「%」、「&」、「'」、「(」、「)」、「\*」、「+」、「,」、「-」、「.」（ピリオド）、「/」、「:」、「;」、「\<」、「=」、「\>」、「?」、「@」、「\[」、「\\、「\]」、「^」、「\_」、「\`」、「{」、「\|」、「}」、「~」、および半角スペース

・2種類以上の文字の組み合わせ

・ユーザーIDと異なる文字列

・パスワードを変更する場合は、現在のパスワードと異なる文字列

​「新しいパスワード（確認）」 新しいパスワードを再度入力して確認する。

ユーザーはこれらのフィールドに対応する情報を入力する。

ユーザーが情報を入力したら、​「OK」​ボタンをクリックする。

システムはユーザーの新しいパスワードが有効性を確認し、有効の場合変更を許可する。変更に成功した場合、ダイアログが閉じられ、ユーザーのパスワードが更新される。変更に失敗した場合、エラーメッセージが表示される。

> 「ワンタイムコード」 モバイルデバイスが生成するワンタイムパスワード。

・半角数字6桁

桁数はKeycloakの設定によって決定され、Keycloakでは6桁または8桁を設定することができるが、Microsoft
Authenticatorが8桁をサポートしていないため、ワンタイムパスワード長は6桁に設定する。

「キャンセル」ボタンをクリックすると、ダイアログが閉じられる。

新しいパスワードと新しいパスワード（確認）が一致しない場合、「確認用パスワードが正しくありません。」のメッセージが表示される。

> <img src="media/image18.png" style="width:6.33958in;height:4.38125in" />

図4.6.6 (1)　画面イメージ

## ライセンス情報

### 目的

資産配布管理サービスのユーザー（顧客のシステム管理者）が契約しているライセンス情報を確認するために使用する。ライセンスの種別（製品版/評価版）、有効期限、ライセンスで管理できるクライアントの上限数など契約内容に関する情報を提供する。

### 前提条件

ログイン状態である。

システム管理者は、ユーザーがポータルシステムを利用する前に以下の情報をポータルシステムテーブルに格納しておく：

- ライセンス種別

- 有効期限

- ライセンス保有数

### 制限事項

特になし。

### 注意事項

特になし。

### 他FSとの関係

特になし。

### 機能詳細

ログインした後に、ナビゲーションバーに「ライセンス」ボタンを表示する。「ライセンス」ボタンをクリックすると、ライセンス情報ダイアログをポップアップする。

ライセンス情報ダイアログは、以下の情報を表示する：

- 「ライセンス種別」ライセンスの種別を表示し、「製品版」または「評価版」であることを示す。

- 「有効期限」ライセンスでサービスを利用できる有効期限（利用終了日）を「YYYY/MM/DD」形式で表示する。有効期限が存在しない場合、「-」を表示する。

- 「ライセンス保有数」ライセンスで管理できるクライアントの上限数を数値形式で表示する。表示範囲は0から9999999までである。

ユーザーはライセンス情報画面から自分の契約内容のみ確認できる。ユーザーはクライアント数の上限超過を確認する際に、ライセンス保有数とライセンス使用数を突き合わせて確認する。

> 共通メニューから「ライセンス」をクリックすると、ポップアップ画面が表示される。
>
> 「閉じる」ボタンをクリックすると、画面を閉じる。
>
> ![](media/image19.png)

図4.7.6 (1)　画面イメージ

## 操作ログ一覧

### 目的

<span id="_Toc152606726"
class="anchor"></span>本画面は、資産配布管理サービスのユーザーが参照可能な操作ログの一覧を表示する。ユーザーが「サーバ一覧」画面の「操作ログのエクスポート」タスク機能（コマンド実行機能）を通じてオンデマンドでエクスポートした操作ログ（タスクの保持件数で管理される）、およびそれ以外の方法でシステムに記録された操作ログ（例：手動でアップロードされたログ等。保管期限で管理される）が含まれる。ユーザーは、必要に応じて操作ログの検索及びダウンロードができる。

### 前提条件

ログイン状態である。

システム管理者は、ユーザーが参照/ダウンロード可能な操作ログについて、以下の情報をポータルシステムテーブルに格納しておく：

- ログ名

- 登録日時

- サイズ

- 保管期限

  対応するログファイルが、Azure Blob
  Storageの所定のコンテナ（デフォルトで「oplogs」）内の、契約IDごとのディレクトリ配下に格納されていること。

### 制限事項

特になし。

### 注意事項

<span id="_Toc152606729" class="anchor"></span>特になし。

### 他FSとの関係

特になし。

### 機能詳細

ログインした後に、サイドバーに「操作ログ一覧」メニューを表示する。「操作ログ一覧」メニューをクリックすると、操作ログ一覧画面に遷移する。

操作ログ一覧は、以下の情報を表示する：

- 「ログ名」操作ログのログ名を表示し、ユーザーはハイパーリンクをクリックすることで当該JP1/ITDM2操作ログファイルをダウンロードする。

  - タスク実行によりエクスポートされた操作ログの場合、ファイル名は
    {タスク名}\_{連番}.zip
    となる。（タスク名：{ServerName}-{タスク種別}-{YYYYMMDDHHmmss}）

  - その他の操作ログの場合、ファイル名は
    oplog\_{開始年月日YYYYMMDD}-{終了年月日YYYYMMDD}\_{シーケンス番号}.zip
    となる。

- 操作ログのURLは、システム管理者が事前にAzure
  Blobに保存したものである。

- 「登録日時」登録された日時を表示する。

- 「サイズ」ログファイルのサイズを表示する。

- 「保管期限」操作ログをポータルに保管する期限(ユーザーがダウンロードすることができる期限)を表示する。保管期限を「YYYY/MM/DD
  hh:mm:ss」形式で表示する。タスク実行によりエクスポートされた操作ログには適用されないため、「-」（ハイフン）と表示する。

- 日付はブラウザのタイムゾーンで表示する。

ユーザーはこの画面から、操作ログを検索する機能の利用ができる。検索条件に一致する操作ログが表示され、ユーザーは操作ログの「ログ名」、「登録日時」、「保管期限」と「サイズ」を確認できる。また、ユーザーは操作ログをダウンロードして保存することができる。

> <img src="media/image20.png" style="width:6.33958in;height:4.39375in" />
> 図4.8.6 (1)　画面イメージ

#### タブオーダー

> タブオーダーは列「ログ名」の上から下への順とする。

#### フィルタリング

一覧の表示項目のフィルタリングができ、フィルターは一覧の中の全項目を検索対象となり，フィルターに入力した文字列が含まれた行がフ抽出されて表示する。

#### ソート

> 操作ログ一覧の全項目はソートができ、デフォルトのソート条件は「登録日時」の降順（日時の近い順番）とする。

①▲▼がついているカラムを選択したら、ソート順を逆にする。  
②▲▼がついていないカラムを選択したら、今までのソート条件を取り消し、選択したカラムの昇順▲でソートする。

#### ページネーション

一覧の表示項目のページネーションができ、ページ番号に対応するデータが表示され、異なる行数でページ分割してデータを表示する。

ページネーションの選択肢は「10」、「30」、「50」ページ、デフォルトは「10」である。

## 製品媒体一覧

### 目的

> 製品媒体一覧は、資産配布管理サービスのユーザーに製品媒体の一覧を表示し、必要に応じて製品媒体の検索及びダウンロードができる。

### 前提条件

> ログイン状態である。
>
> システム管理者は、ユーザーがポータルシステムを利用する前に以下の情報をポータルシステムテーブルに格納しておく：

- 製品名

- 製品形名

- バージョン

- OS

- リリース日

- 製品媒体

- 製品媒体のサイズ

- ドキュメント

- ドキュメントのサイズ

### 制限事項

> 特になし。

### 注意事項

> 特になし。

### 他FSとの関係

> 特になし。

### 機能詳細

> ログインした後に、サイドバーに「製品媒体一覧」メニューを表示する。「製品媒体一覧」メニューをクリックすると、製品媒体一覧画面に遷移する。
>
> 製品媒体一覧は、資産配布管理サービスのユーザーに以下の情報を表示する：

- 「製品名」：製品媒体の名称を表示する。

- 「製品形名」：製品媒体のコードを表示する。

- 「バージョン」：製品媒体のバージョンを表示する。

- 「OS」：製品媒体が対応するオペレーティングシステムを表示する。

- 「リリース日」：製品媒体のリリース日を表示する。

- 「製品媒体」：製品媒体のファイル名を表示する。ユーザーはリンクをクリックすることで当該製品媒体ファイルをダウンロードすることができる。

- 「製品媒体のサイズ」：製品媒体のファイルのサイズを表示する。

- 「ドキュメント」：製品に付属するドキュメント(リリースノート、取扱説明書など)を圧縮したファイル名を表示する。ユーザーはリンクをクリックすることで当該ドキュメントの圧縮ファイルをダウンロードすることができる。

- 「ドキュメントのサイズ」：製品に付属するドキュメントのサイズを表示する。

- 製品媒体とドキュメントのURLは、システム管理者が事前にAzure
  Blobに保存したものである。

ユーザーはこの画面から、製品媒体を検索する機能の利用ができる。検索条件に一致する製品媒体が表示され、ユーザーは製品媒体の「製品名」、「製品形名」、「OS」、「リリース日」、「製品媒体のサイズ」、「ドキュメント」と「ドキュメントのサイズ」を確認できる。また、ユーザーは製品媒体とドキュメントをダウンロードして保存することができる。

> <img src="media/image21.png" style="width:6.33958in;height:4.50815in"
> alt="D:\WXFile\WXWork\1688851867745867\Cache\Image\2023-12\Media1(1).png" />
> 図4.9.6 (1)　画面イメージ
>
> <img src="media/image22.png" style="width:6.33958in;height:4.50815in"
> alt="D:\WXFile\WXWork\1688851867745867\Cache\Image\2023-12\Media2(1).png" />
> 図4.9.6 (2)　画面イメージ

#### タブオーダー

> タブオーダーは以下とする。

1行目の「製品媒体」→1行目の「ドキュメント」→2行目の「製品媒体」→2行目の「ドキュメント」→…n行目の「製品媒体」→n行目の「ドキュメント」（nは一つページのデータ数である。）

#### フィルタリング

一覧の表示項目のフィルタリングができ、フィルターは一覧の中の全項目を検索対象とする。フィルターに入力した文字列が含まれる行がフィルタリングされて表示する。

#### ソート

> 製品媒体一覧の全項目はソートができ、下記のソートルールを参照する。
>
> ①第1ソート条件：ユーザーが選択した項目とソート順でソートする。
>
> ②第2ソート条件：ポータルシステム内部の固定条件として、第1ソート条件が「製品名」以外の場合、「製品名」の辞書順（A→Z,
> 0→9の順番）でソートする。
>
> ③▲▼がついているカラムを選択したら、ソート順を逆にする。  
> ④▲▼がついていないカラムを選択したら、今までのソート条件を取り消し、選択したカラムの昇順▲でソートする。
>
> デフォルトのソート条件は以下とする。
>
> ①第1ソート条件：「リリース日」の降順（日付の近い順番）でソートする。
>
> ②第2ソート条件：「製品名」の辞書順（A→Z, 0→9の順番）でソートする。

#### ページネーション

一覧の表示項目のページネーションができ、ページ番号に対応するデータが表示され、異なる行数でページを分割し、データが表示される。

ページネーションの選択肢は「10」、「30」、「50」ページ、デフォルトは「10」である。

## マニュアル一覧

### 目的

> 本サービスのユーザー(顧客のシステム管理者)は本サービスに関連する利用可能な製品マニュアル、取扱説明書、利用ガイドといったマニュアルをダウンロードするための画面である。
>
> 本サービスが提供するマニュアルを一覧で表示し、本サービスのユーザーは本画面からマニュアルをダウンロードすることができる。

### 前提条件

> ログイン状態が必要である。
>
> システム管理者は、ユーザーがポータルシステムを利用する前に以下の情報をポータルシステムテーブルに格納しておく。

- マニュアル名称

- 資料番号

- サイズ

### 制限事項

> 特になし。

### 注意事項

> 特になし。

### 他FSとの関係

> 特になし。

### 機能詳細

> ログインした後、サイドバーに「マニュアル一覧」メニューを表示する。「マニュアル一覧」メニューをクリックすると、マニュアル一覧画面に遷移する。
>
> マニュアル一覧は、資産配布管理サービスのユーザーに以下の情報を表示する：

- 「マニュアル名称」：マニュアル名である。

- 「資料番号」：マニュアルの資料番号(マニュアルごとに付与される識別番号)
  である。

- 「サイズ」：マニュアルのダウンロードファイルサイズを表示する。ファイルサイズの数値と単位(KB/MB/GB)の形式である。

> ユーザーはこの画面から、マニュアルを検索する機能の利用ができる。検索条件に一致するマニュアルが表示され、ユーザーはマニュアルの「マニュアル名称」、「資料番号」と「サイズ」を確認できる。また、ユーザーはマニュアルをダウンロードして保存することができる。
>
> <img src="media/image23.png" style="width:6.33958in;height:4.50815in"
> alt="D:\WXFile\WXWork\1688851867745867\Cache\Image\2023-12\Manual(1).png" />
>
> 図4.10.6 (1)　画面イメージ

#### タブオーダー

> タブオーダーは列「マニュアル名称」の上から下への順とする。

#### フィルタリング

> 一覧の表示項目のフィルタリングができ、フィルターは一覧の中の全項目を検索対象にする，フィルターに入力した文字列が含まれる行がフィルタリングされて表示する。

#### ソート

> マニュアル一覧の全項目はソートができ、デフォルトのソート条件は「マニュアル名称」の辞書順（A→Z,
> 0→9の順番）とする。
>
> ①▲▼がついているカラムを選択したら、ソート順を逆にする。  
> ②▲▼がついていないカラムを選択したら、今までのソート条件を取り消し、選択したカラムの昇順▲でソートする。

#### ページネーション

> 一覧の表示項目のページネーションができ、ページ番号に対応するデータが表示され、異なる行数でページを分割し、データが表示される。

ページネーションの選択肢は「10」、「30」、「50」ページ、デフォルトは「10」である。

## 提供ファイル一覧

### 目的

> 本サービスのユーザー(顧客のシステム管理者)は本サービスに関連する利用可能な設定ファイルやツールなどのファイルをダウンロードするための画面である。
>
> 本サービスが提供する提供ファイルを一覧で表示し、本サービスのユーザーは本画面から提供ファイルをダウンロードすることができる。

### 前提条件

> ログイン状態が必要である。
>
> システム管理者は、ユーザーがポータルシステムを利用する前に以下の情報をポータルシステムテーブルに格納しておく。

- ファイル名

- 説明

- 最終更新日時

- サイズ

### 制限事項

> 特になし。

### 注意事項

> 特になし。

### 他FSとの関係

> 特になし。

### 機能詳細

> ログインした後、サイドバーに「提供ファイル一覧」メニューを表示する。「提供ファイル一覧」メニューをクリックすると、提供ファイル一覧画面に遷移する。
>
> 提供ファイル一覧は、資産配布管理サービスのユーザーに以下の情報を表示する：

- 「ファイル名」：提供ファイルのファイル名を表示する。

- 「説明」：提供ファイルの説明を表示する。

- 「最終更新日時」：提供ファイルの最終更新日時を表示する。リリースの日付を「YYYY/MM/DD
  hh:mm:ss」形式で表示する。日付はブラウザのタイムゾーンで表示する。

- 「サイズ」：提供ファイルのダウンロードファイルサイズを表示する。ファイルサイズの数値と単位(KB/MB/GB)の形式である。

> ユーザーはこの画面から、提供ファイルを検索する機能の利用ができる。検索条件に一致する提供ファイルが表示され、ユーザーは提供ファイルの「ファイル名」、「説明」、「最終更新日時」と「サイズ」を確認できる。また、ユーザーは提供ファイルをダウンロードして保存することができる。
>
> 図4.11.6
> (1)　画面イメージ<img src="media/image24.png" style="width:6.33958in;height:4.50764in"
> alt="D:\WXFile\WXWork\1688851867745867\Cache\Image\2023-12\ProvidedFile(1).png" />

#### タブオーダー

> タブオーダーは列「ファイル名」の上から下への順とする。

#### フィルタリング

> 一覧の表示項目のフィルタリングができ、フィルターは一覧の中の全項目を検索対象にする，フィルターに入力した文字列が含まれる行がフィルタリングされて表示する。

#### ソート

> 提供ファイル一覧の全項目はソートができ、下記のソートルールを参照する。
>
> ①第1ソート条件：ユーザーが選択した項目とソート順でソートする。
>
> ②第2ソート条件：ポータルシステム内部の固定条件として、第1ソート条件が「ファイル名」以外の場合、「ファイル名」の
> 辞書順（A→Z, 0→9の順番）でソートする。
>
> ③▲▼がついているカラムを選択したら、ソート順を逆にする。  
> ④▲▼がついていないカラムを選択したら、今までのソート条件を取り消し、選択したカラムの昇順▲でソートする。
>
> デフォルトのソート条件は以下とする。
>
> ①第1ソート条件：「最終更新日時」の降順（日時の近い順番）でソートする。
>
> ②第2ソート条件：「ファイル名」の辞書順（A→Z, 0→9の順番）でソートする。

#### ページネーション

一覧の表示項目のページネーションができ、ページ番号に対応するデータが表示され、異なる行数でページ分割してデータを表示する。

ページネーションの選択肢は「10」、「30」、「50」ページ、デフォルトは「10」である。

## サポート情報一覧

### 目的

> 本サービスのユーザー(顧客のシステム管理者)が本サービスの障害回避・予防に関する情報や注意喚起情報といったサポート情報を確認するための画面である。
>
> 本サービスの提供者(日立)から発信したサポート情報を一覧で表示し、本サービスのユーザーは本画面からサポート情報を確認できる。

### 前提条件

> ログイン状態が必要である。
>
> システム管理者は、ユーザーが参照可能なサポート情報について、以下の情報をポータルシステムテーブルに格納しておく。

- 最終更新日

- タイトル

- 製品

- 重要度

### 制限事項

> 特になし。

### 注意事項

> 特になし。

### 他FSとの関係

> 特になし。

### 機能詳細

> ログインした後、サイドバーに「サポート情報一覧」メニューを表示する。「サポート情報一覧」メニューをクリックすると、サポート情報一覧画面に遷移する。
>
> サポート情報一覧は、資産配布管理サービスのユーザーに以下の情報を表示する：

- 「最終更新日」：サポート情報の内容が最後に更新された日。最終更新日を「YYYY/MM/DD」形式で表示する。日付はブラウザのタイムゾーンで表示する。

- 「タイトル」：サポート情報のタイトルを表示する。

- 「製品」：サポート情報の対象製品を表示する。

- 「重要度」：サポート情報の重要度を表示する。

> 以下のいずれかを表示
>
> AAA：業務システムの運用が停止し、発生頻度が高い
>
> AA：業務システムの運用が停止する可能性がある
>
> A：業務システムの運用が停止する可能性は低い
>
> B：業務システムの運用に与える影響が少ない
>
> C：業務システムの運用に与える影響は殆ど無い
>
> -：重要度なし(予防保守情報や使用時の注意事項など)
>
> 「公開日」：サポート情報が最初に公開された日。最終更新日を「YYYY/MM/DD」形式で表示する。日付はブラウザのタイムゾーンで表示する。
>
> ユーザーはこの画面から、サポート情報を検索する機能の利用ができる。検索条件に一致するサポート情報が表示され、ユーザーはサポート情報の「最終更新日」、「タイトル」、「製品」、「重要度」と「公開日」を確認できる。また、ユーザーはサポート情報をダウンロードして保存することができる。
>
> <img src="media/image25.png" style="width:6.33958in;height:4.50815in"
> alt="D:\WXFile\WXWork\1688851867745867\Cache\Image\2023-12\SupportFile(1).png" />
>
> 図4.12.6 (1)　画面イメージ

#### タブオーダー

> タブオーダーは列「タイトル」の上から下への順とする。

#### フィルタリング

> 一覧の表示項目のフィルタリングができ、フィルターは一覧の中の全項目を検索対象にする，フィルターに入力した文字列が含まれる行がフィルタリングされて表示する。

#### ソート

> サポート情報一覧の全項目はソートができ、下記のソートルールを参照する。
>
> ①第1ソート条件：ユーザーが選択した項目とソート順でソートする。
>
> ②第2ソート条件：ポータルシステム内部の固定条件として、第1ソート条件が「製品」以外の場合、「製品」の辞書順（A→Z,
> 0→9の順番）でソートする。
>
> ③▲▼がついているカラムを選択したら、ソート順を逆にする。  
> ④▲▼がついていないカラムを選択したら、今までのソート条件を取り消し、選択したカラムの昇順▲でソートする。
>
> デフォルトのソート条件は以下とする。
>
> ①第1ソート条件：「最終更新日」の降順（日付の近い順番）でソートする。
>
> ②第2ソート条件：「製品」の辞書順（A→Z, 0→9の順番）でソートする。

#### ページネーション

一覧の表示項目のページネーションができ、ページ番号に対応するデータが表示され、異なる行数でページ分割してデータを表示する。

ページネーションの選択肢は「10」、「30」、「50」ページ、デフォルトは「10」である。

## タスク一覧

### 目的

本画面は、資産配布管理サービスのユーザーが、サーバ一覧画面から開始した各種バックグラウンドタスク（操作ログのエクスポート、管理項目定義のインポート・エクスポート）の実行状態、結果詳細、および関連情報を確認するための画面である。

### 前提条件

- ユーザーがポータルシステムにログインしていること。

- Azure Functionsの関数 (TaskExecuteFunc, TaskCancellationFunc,
  RunbookMonitorFunc, RunbookProcessorFunc)
  は、タスクのライフサイクル（受付、準備、Runbookジョブの作成と監視、中止処理、最終結果処理）を通じ、その状態を「タスク」テーブルに随時更新する責務を負う。具体的には、タスクIDをキーとしてタスクのステータス、開始日時、終了日時等を更新する。各タスク種別に応じた結果（「正常終了」時のエクスポートファイルパス、「エラー」時のエラーメッセージ等）も記録されること。

### 制限事項

> 特になし。

### 注意事項

　特になし。

### 他FSとの関係

> 特になし。

### 機能詳細（外部仕様）

> ログイン後、サイドバーの「タスク一覧」メニューよりタスク一覧画面へ遷移する。本画面にて、サーバ一覧画面から開始された各種バックグラウンドタスク（操作ログのエクスポート、管理項目定義のインポート・エクスポート）の実行状態と結果詳細を確認することができる。また、ステータスが「実行待ち」のタスクの中止操作も本画面から行うことができる。
>
> ユーザーには、自分及び同じライセンス内の他のユーザーが開始したタスクの一覧を表示する。
>
> タスク一覧画面では、ユーザーに以下の情報を表示する：

- 「タスク名」:
  タスクを識別するためにシステムが自動生成する名称。{ServerName}-{タスク種別}-{YYYYMMDDHHmmss}で構成する。

- 「ステータス」：タスクの現在の状態。「実行待ち」「実行中」「正常終了」「エラー」「中止待ち」「中止」のいずれかを表示する。タスクのステータスについての詳細は「資産配布管理サービス
  ポータル機能仕様書 別紙 タスクのステータス.xlsx」を参照してください。

- 「開始日時」：タスクの実行が開始した日時。

- 「終了日時」：タスクが終了した日時。

- 「サーバ名」：タスクの実行対象のサーバ名。

- 「タスク種別」：実行タスクの種別。「操作ログのエクスポート」、「管理項目定義のインポート」、「管理項目定義のエクスポート」のいずれかを表示する。

- 「実行ユーザー」：タスクを実行したユーザー名。

- 「タスク詳細」：タスクのステータスに応じて以下の情報を表示、または操作を提供する。

  - 「実行待ち」：「中止」ボタンを表示する。

  - 「実行中」と「中止待ち」：空白を表示する（「中止」ボタンは表示しない）。ユーザーの中止要求がタイムアウトした場合は「タスクの中止がタイムアウトしました。」といったメッセージを表示する。ユーザーは中止操作を行ったがタスクがすでに実行し始めた場合は「タスクの実行がすでに開始したため中止できませんでした。」といったメッセージを表示する。

  - 「正常終了」：実行結果の詳細を表示する。操作ログのエクスポートタスクの場合は「操作ログ一覧画面で操作ログファイルをダウロードしてください。ログ名は{タスク名}\_{連番}となる。」を表示する。管理項目定義のインポートタスクの場合は空欄と表示する。管理項目定義のエクスポートタスクの場合、リンクとなる「ダウンロード」を表示し、クリックすることでエクスポートされたCSVファイル（ファイル名：assetsfield_def.csv）をダウンロードする。

  - 「エラー」：「エラー詳細を表示」のリンクを表示して、クリックするとポップアップで詳細なエラーメッセージを表示する。

  - 「中止」：「ユーザーによってタスクが中止されました。」といったメッセージを表示する。

    　　タスク一覧画面に表示される情報はユーザーがこの画面を開いた時点の情報であり、最新の情報を確認したい場合は手動で右上の更新ボタンを押す。

    　ステータスが「実行中」のタスクは中止不可とし、そのまま「正常終了」か「エラー」（タイムアウト等含む）となる。ステータスが「実行待ち」のタスクだけが中止可能であり、中止ボタンが表示される。「実行待ち」のタスクに対して中止操作を行ったユーザーには、「タスクの中止を受け付けました。最新の状態はタスク一覧をご確認ください。タスク名：{0}」（※{0}：タスク名）といったメッセージを表示した後、タスクのステータスを「中止待ち」に更新する。画面上は常に最新情報を表示しているわけではないため、「実行待ち」のタスクに対しユーザーが中止ボタンをクリックする時、実際はタスクの実行がすでに開始した場合がある。この場合は「タスクの実行がすでに開始したため中止できませんでした。」といったメッセージをユーザーに表示する。中止操作の受け付けは完了したが、タスクの実行が先に開始した場合もある。この場合は「タスクの実行がすでに開始したため中止できませんでした。」といったメッセージをタスク詳細に表示する。また、「実行待ち」のタスクに対しユーザーが中止ボタンをクリックする時、実際は他のユーザーがすでに該当タスクに対し中止操作を行った場合がある。この場合は同じく受け付けのメッセージを表示するが、ステータスの更新は行わない。

    <img src="media/image26.png" style="width:6.33958in;height:4.50833in" /><img src="media/image27.png" style="width:6.33958in;height:4.50833in" />図4.13.6
    (1)　画面イメージ

図4.13.6 (2)　画面イメージ

#### タブオーダー

> タブオーダーは上から下への順とする。

#### フィルタリング

> 画面上部にフィルターの入力ボックスと検索ボタン、クリアボタンが配置されている。フィルターの入力ボックスに文字列を入力して、検索ボタンをクリックすると、全項目から入力された文字列を検索し、一致した行だけを表示する。クリアボタンをクリックすると入力ボックスの内容と検索条件をクリアして、タスク一覧を再表示する。

#### ソート

> タスク一覧のタスク詳細以外の項目はソートができ、下記のソートルールを参照する。
>
> ①第1ソート条件：ユーザーが選択した項目とソート順でソートする。
>
> ②第2ソート条件：ポータルシステム内部の固定条件として、第1ソート条件が「タスク名」以外の場合、「タスク名」の辞書順（A→Z,
> 0→9の順番）でソートする。
>
> ③▲▼がついているカラムを選択したら、ソート順を逆にする。  
> ④▲▼がついていないカラムを選択したら、今までのソート条件を取り消し、選択したカラムの昇順▲でソートする。
>
> デフォルトのソート条件は以下とする。
>
> ①第1ソート条件：「開始日時」の降順（日時の近い順番）でソートする。
>
> ②第2ソート条件：「タスク名」の辞書順（A→Z, 0→9の順番）でソートする。

#### ページネーション

一覧の表示項目のページネーションができ、ページ番号に対応するデータが表示され、異なる行数でページ分割してデータを表示する。ページネーションの選択肢に「10」「30」「50」があり、デフォルトは「10」である。

### 機能詳細（内部仕様）

　画面上に表示されるタスクの情報はデータベースの「タスク」テーブルと「値の一覧」テーブルから取得される。更新ボタンが押された場合はデータベースから最新のデータを取得して画面に更新する。

タスクの種別とステータスは「値の一覧」テーブルのTASK_TYPEとTASK_STATUSで定義される。タスクのステータスについての詳細は「資産配布管理サービス
ポータル機能仕様書 別紙 タスクのステータス.xlsx」を参照してください。

「タスク」テーブルにおいて、タスクのレコードはマネージャサーバごとに、タスクの種別問わず、上限件数（「値の一覧」テーブルの
TASK_CONFIG.
MAX_RETENTION_COUNTの値で設定され、デフォルトで10件）まで保持される。

管理項目定義のエクスポートタスクが正常終了した結果として、タスク詳細にエクスポートされたCSVファイルをダウンロードするためのリンクを表示するのだが、ユーザーがダウンロードリンクをクリックする時、ポータルシステムのサーバ側のプログラムは「タスク」テーブルから該当タスクレコードの「ダウンロードパス」フィールドを取得し、フロント側に返す。フロント側はこの「ダウンロードパス」が示したAzure
Blob
Storageのパスからファイルをダウンロードする。ダウンロードした管理項目定義のエクスポートファイルのファイル名はassetsfield_def.csv
に固定する。Azure Blob
Storageにおける具体的なパス構成は「6.ストレージ設計」の「6.2 Azure Blob
Storageディレクトリ構造の設計」を参照してください。

ステータスがQUEUEDのタスクにだけ中止ボタンを表示する。画面上は常に最新情報を表示しているわけではないため、ユーザーが中止ボタンをクリックする時、タスクのステータスがすでにQUEUEDではない可能性がある。このため、中止ボタンが押されたら、ポータルのサーバ側のプログラムはまず「タスク」テーブルから該当タスクの現在のステータスを確認する。ステータスがQUEUEDの場合は画面上に受け付けのメッセージを表示し、「タスク」テーブルから該当タスクのステータスをPENDING_CANCELLATIONに更新し、Azure
Service
BusのTaskControlQueueに中止要求のメッセージ（パラメータにはタスクIDなどが含まれる）を送信する。ステータスがPENDING_CANCELLATION
/
CANCELLEDの場合なら、該当タスクに対しすでに他のユーザーが中止操作を行ったため、画面上に受け付けのメッセージを表示するが、テーブルの更新と中止要求のメッセージ送信は行わない。ステータスがRUNBOOK_SUBMITTED
/ RUNBOOK_PROCESSING / COMPLETED_SUCCESS /
COMPLETED_ERRORの場合なら、画面上に「タスクの実行がすでに開始したため中止できませんでした。」といったメッセージを表示して、テーブルの更新と中止要求のメッセージ送信は行わない。中止要求はAzure
Functions のタスク中止関数TaskCancellationFuncにより処理される。Azure
Functionsで実行されるポータル関数の詳細は「8.ポータル関数」を参照してください。

#  データベース設計

## データベーステーブル

<img src="media/image28.png" style="width:5.59236in;height:9.35139in" />

> このデータベースには、以下の主要なテーブルが含まれている：

###  **\[ユーザー\] (User)**

【02-20まで】

ユーザー情報を格納するテーブルで、ユーザーID、暗号化されたパスワード、契約ID、パスワード最終更新日時、ログイン失敗数、ロックアウト最終更新日時などのユーザーに関する情報が含まれている。

- 1つのライセンスは複数のユーザーに関連付けられることがある。

- 1つのユーザーは複数のお知らせに関連付けられることがある。

- ロックアウト最終更新日時の更新タイミングは下記二つ：

  1.  ログイン失敗数は最大回数になると、ロックアウト最終更新日時を現在の日時に記入する。

  2.  ロックアウトからの経過時間は最大経過時間以上かつ入力ユーザーIDとパスワードが正しい場合、ロックアウト最終更新日時をクリアする。

【02-30以降】

本テーブルにデータを格納しない。本テーブルは02-20との互換性の目的で存在する。

なお、02-30以降は本テーブルに相当する情報(ユーザー情報)はKeyCloakで格納・管理する。

サービス運用者は、02-30以降にユーザー情報を新規登録する場合は、KeyCloakに登録し本テーブルには情報を格納しない。

02-20までに本テーブルに格納していたユーザー情報は、サービス運用者がKeyCloakにデータ移行し、本テーブルの情報は削除する(ユーザー情報の多重管理防止の観点)。

ポータルプログラムは、02-30以降は本テーブルに対する操作(データ追加・変更・参照・削除)を行わない。ユーザー情報はKeyCloakから取得する。本テーブルの情報をキーとして他テーブルを参照する場合は、KeyCloakから取得した情報を使用する(userIdとlicenseId)。

### **\[ログインの監査ログ\] (AuditLogin)**

ログインのログ情報を記録するためのテーブルで、監査タイプ、ユーザーID、契約ID、ログインメッセージ、保管期限などが含まれている。

監査ログをどんどん増えるので、定期にツールでDBを接続して、ログインの監査ログテーブルのデータを削除します。

### **\[お知らせ\] (Notification)**

共通、契約向けとユーザー向けのお知らせ情報を管理するためのテーブルで、内容、公開日、タイプ、契約ID、契約プランID、通番とユーザーIDなどが含まれている。

- 契約プラン向け、契約向けとユーザー向けの場合、1つの契約プランまたは1つのユーザーまたは1つのライセンスは複数のお知らせに関連付けられることがある。

### **\[ライセンス\] (License)**

ライセンス情報を格納するテーブルで、契約ID、ライセンス種別、有効期限、保有数、メンテナンスフラグ、環境無効化フラグ、基本契約プランコードなどが含まれている。

- 1つのライセンスは複数のユーザーに関連付けられることがある。

- 1つのライセンスは複数のお知らせに関連付けられることがある。

- 1つのライセンスは複数のサーバに関連付けられることがある。

- 1つのライセンスは複数の操作ログに関連付けられることがある。

- 1つのライセンスは複数の利用可能製品に関連付けられることがある。

- 1つのライセンスは複数の契約プラン情報に関連付けられることがある。

- 1つのライセンスは複数のタスクに関連付けられることがある。

### **\[契約プラン情報\] (Plan)**

契約プラン情報を格納するテーブルで、契約プラン名と契約プランIDが含まれている。

- 1つの契約プラン情報は複数のライセンスに関連付けられることがある。

### **\[ライセンスとプランの関連情報\] (LicensePlan)**

ライセンスとプランの関連情報を格納するテーブルで、契約ID、契約プランIDが含まれている。

### **\[操作ログ\] (OperationLog)**

操作ログ情報を記録するためのテーブルで、契約ID、ログ名、サイズ、保管期限と登録日時、タスクID（操作ログのエクスポートタスクで作成された場合）、ファイル名などが含まれている。

1つのライセンスは複数の操作ログに関連付けられることがある。

1つのタスクは複数の操作ログに関連付けられることがある。

### **\[利用可能製品\] (PlanProduct)**

ライセンスに基づいて、利用可能製品情報を格納するテーブルで、契約ID、製品コードとバージョンなどが含まれている。

- 1つのライセンスは複数の利用可能製品に関連付けられることがある。

### **\[製品媒体\] (ProductMedia)**

製品媒体情報を格納するテーブルで、製品名、製品コード、バージョン、OS、リリース日、製品媒体名、製品媒体のサイズ、ドキュメント名とドキュメントのサイズなどが含まれている。

- 1つの利用可能製品は1つの製品媒体に関連付けられることがある。

### **\[サーバ\] (Server)**

サーバ情報を格納するテーブルで、契約ID、サーバ名、種別と管理画面のURLおよびバックグラウンドタスクを実行するのに必要なAzure
VM名、Dockerコンテナ名、Hybrid Runbook Worker
Group名などが含まれている。

- 1つのライセンスは複数のサーバに関連付けられることがある。

- 1つのサーバは1つのライセンスに関連付けられる。

- 1つのライセンスは複数のAzure VMに関連付けられることがある。

- 1つのAzure VMは複数のライセンスに関連付けられることがある。

- サーバの実体はDockerコンテナそのもののため、1つのサーバは1つのDockerコンテナを表す。

- 1つのサーバは1つのAzure VMに関連付けられる。

- 1つのAzure VMは複数のサーバに関連付けられることがある。

- 1つのAzure VMは1つのHybrid Runbook Worker Groupに関連付けられる。

### **\[利用可能**マニュアル**\] (PlanManual)**

マニュアル情報を格納するテーブルで、契約プランIDと資料番号が含まれている。

- 1つの契約プランは複数のマニュアルに関連付けられることがある。

### **\[製品**マニュアル**\] (ProductManual)**

マニュアル情報を格納するテーブルで、マニュアル名前、資料番号、ファイル名とサイズが含まれている。

- 1つの製品マニュアルは複数のマニュアルに関連付けられることがある。

### **\[利用可能提供ファイル\] (PlanProvidedFile)**

利用可能提供ファイル情報を格納するテーブルで、契約プランIDとファイル名前が含まれている。

- 1つの契約プランは複数の利用可能提供ファイルに関連付けられることがある。

### **\[提供ファイル\] (ProvidedFile)**

提供ファイル情報を格納するテーブルで、提供ファイル名、説明、最終更新日時、サイズとファイル名前が含まれている。

- 1つの提供ファイルは複数の利用可能提供ファイルに関連付けられることがある

### **\[対象サポート情報\] (PlanSupport)**

対象サポート情報を格納するテーブルで、契約プランIDと通番が含まれている。

- 1つの提供ファイルは複数の対象サポート情報に関連付けられることがある

### **\[サポート情報\] (SupportFile)**

対象サポート情報を格納するテーブルで、通番、最終更新日時、タイトル、製品、重要度、公開日とファイル名前が含まれている。

- 1つのサポート情報は複数の対象サポート情報に関連付けられることがある

### **\[値の一覧\] (Lov)**

値の一覧を格納するテーブルで、システム全体で使用される値の一覧を管理するデータベーステーブルである。LOVは、関連するデータ型に基づいて、データを正確に検証するための手段として使用される。各LOVエントリは、コード、名前、親コード、値、および有効性の情報を格納している。
isEnabledフィールドは、LOVエントリがデフォルトで有効であることを示すためにtrueに設定されている。

このLOVテーブルは、自己参照を通じて親LOVと子LOVを関連付けている。これにより、システム内の異なるエンティティとの関連性を構築し、値の一覧が階層的な構造を持つ場合でも柔軟に対応できる。

特定のデータ型を拡張および管理する機能を許可している。

- ライセンスタイプ

- サーバタイプ

- OSタイプ

- サポート情報の重要度

- ロックアウト設定

| \# | コード |  | 名前 | 親コード | 値 | 有効性 |
|---:|----|----|----|----|----|----|
| 1 | SERVER_TYPE |  | サーバ種別 | 　 | 　 | isEnabled |
| 2 | SERVER_TYPE.GENERAL_MANAGER |  | 統括マネージャ | SERVER_TYPE | JP1/ITDM2(統括マネージャ) | isEnabled |
| 3 | SERVER_TYPE.RELAY_MANAGER |  | 中継マネージャ | SERVER_TYPE | JP1/ITDM2(中継マネージャ) | isEnabled |
| 4 | SERVER_TYPE.HIBUN_CONSOLE |  | 管理コンソール | SERVER_TYPE | 秘文(管理コンソール) | isEnabled |
| 5 | LICENSE_TYPE |  | ライセンスタイプ | 　 | 　 | isEnabled |
| 6 | LICENSE_TYPE.PROD |  | 製品版表示名 | LICENSE_TYPE | 製品版 | isEnabled |
| 7 | LICENSE_TYPE.TRIAL |  | 評価版表示名 | LICENSE_TYPE | 評価版 | isEnabled |
| 8 | OS_TYPE |  | OSタイプ | 　 | 　 | isEnabled |
| 9 | OS_TYPE.WIN |  | Windows表示名 | OS_TYPE | Windows | isEnabled |
| 10 | OS_TYPE.LINUX |  | Linux表示名 | OS_TYPE | Linux | isEnabled |
| 11 | OS_TYPE.AIX |  | AIX表示名 | OS_TYPE | AIX | isEnabled |
| 12 | OS_TYPE.SOLARIS |  | Solaris表示名 | OS_TYPE | Solaris | isEnabled |
| 13 | OS_TYPE.HPUX |  | HP-UX表示名 | OS_TYPE | HP-UX | isEnabled |
| 14 | OS_TYPE.MACOS |  | Mac表示名 | OS_TYPE | Mac | isEnabled |
| 15 | LOCKOUT |  | ロックアウト設定 |  |  | isEnabled |
| 16 | LOCKOUT.MAX_FAILED_TIMES |  | ロックアウト失敗最大回数 | LOCKOUT | 10 | isEnabled |
| 17 | LOCKOUT.TIME_SECONDS |  | ロックアウト時間 | LOCKOUT | 1800 | isEnabled |
| 18 | SUPPORT_IMPORTANCE |  | サポート情報の重要度 |  |  |  |
| 19 | SUPPORT_IMPORTANCE.NONE |  | 重要度なし(予防保守情報や使用時の注意事項など) | SUPPORT_IMPORTANCE | \- | isEnabled |
| 20 | SUPPORT_IMPORTANCE.AAA |  | 業務システムの運用が停止し、発生頻度が高い | SUPPORT_IMPORTANCE | AAA | isEnabled |
| 21 | SUPPORT_IMPORTANCE.AA |  | 業務システムの運用が停止する可能性がある | SUPPORT_IMPORTANCE | AA | isEnabled |
| 22 | SUPPORT_IMPORTANCE.A |  | 業務システムの運用が停止する可能性は低い | SUPPORT_IMPORTANCE | A | isEnabled |
| 23 | SUPPORT_IMPORTANCE.B |  | 業務システムの運用に与える影響が少ない | SUPPORT_IMPORTANCE | B | isEnabled |
| 24 | SUPPORT_IMPORTANCE.C |  | 業務システムの運用に与える影響は殆ど無い | SUPPORT_IMPORTANCE | C | isEnabled |
| 25 | LOCKOUT | ロックアウト設定 |  |  | isEnabled |  |
| 26 | LOCKOUT.MAX_FAILED_TIMES | ロックアウト失敗最大回数 | LOCKOUT | 10 | isEnabled |  |
| 27 | LOCKOUT.TIME_SECONDS | ロックアウト時間 | LOCKOUT | 1800 | isEnabled |  |
| 28 | AZURE_STORAGE | Azure Blob 設定 |  |  | isEnabled |  |
| 29 | AZURE_STORAGE.CONTAINER_OPLOGS | 操作ログコンテナ名 | AZURE_STORAGE | oplogs | isEnabled |  |
| 30 | AZURE_STORAGE.CONTAINER_PRODUCT_MEDIAS | 製品媒体コンテナ名 | AZURE_STORAGE | product-medias | isEnabled |  |
| 31 | AZURE_STORAGE.CONTAINER_PRODUCT_MANUALS | マニュアルコンテナ名 | AZURE_STORAGE | product-manuals | isEnabled |  |
| 32 | AZURE_STORAGE.CONTAINER_PROVIDED_FILES | 提供ファイルコンテナ名 | AZURE_STORAGE | provided-files | isEnabled |  |
| 33 | AZURE_STORAGE.CONTAINER_SUPPORT_FILES | サポートファイルコンテナ名 | AZURE_STORAGE | support-files | isEnabled |  |
| 34 | AZURE_STORAGE.CONTAINER_ASSETSFIELD_DEF | 管理項目定義コンテナ名 | AZURE_STORAGE | assetsfield-def | isEnabled |  |
| 35 | AZURE_STORAGE.SAS_TTL_SECONDS | SAS有効期限 | AZURE_STORAGE | 7200 | isEnabled |  |
| 36 | OPERATION_LOG_CONFIG | 操作ログ関連設定 |  |  | isEnabled |  |
| 37 | OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN | 操作ログのエクスポート期間最大日数 | OPERATION_LOG_CONFIG | 30 | isEnabled |  |
| 38 | TASK_CONFIG | タスク関連設定 |  |  | isEnabled |  |
| 39 | TASK_CONFIG. MAX_RETENTION_COUNT | サーバ単位のタスク保有上限件数 | TASK_CONFIG | 10 | isEnabled |  |
| 40 | TASK_TYPE | タスク種別 |  |  | isEnabled |  |
| 41 | TASK_TYPE.OPLOG_EXPORT | 操作ログのエクスポート | TASK \_TYPE | 操作ログのエクスポート | isEnabled |  |
| 42 | TASK_TYPE.MGMT_ITEM_IMPORT | 管理項目定義のインポート | TASK \_TYPE | 管理項目定義のインポート | isEnabled |  |
| 43 | TASK_TYPE.MGMT_ITEM_EXPORT | 管理項目定義のエクスポート | TASK \_TYPE | 管理項目定義のエクスポート | isEnabled |  |
| 44 | TASK_STATUS | タスクステータス |  |  | isEnabled |  |
| 45 | TASK_STATUS.QUEUED | 実行待ち | TASK_STATUS | 実行待ち | isEnabled |  |
| 46 | TASK_STATUS. RUNBOOK_SUBMITTED | Runbookジョブ作成完了 | TASK_STATUS | 実行中 | isEnabled |  |
| 47 | TASK_STATUS. RUNBOOK_PROCESSING | Runbookジョブ処理中 | TASK_STATUS | 実行中 | isEnabled |  |
| 48 | TASK_STATUS. COMPLETED_SUCCESS | 正常終了 | TASK_STATUS | 正常終了 | isEnabled |  |
| 49 | TASK_STATUS. COMPLETED_ERROR | エラー | TASK_STATUS | エラー | isEnabled |  |
| 50 | TASK_STATUS.CANCELLED | 中止 | TASK_STATUS | 中止 | isEnabled |  |
| 51 | TASK_STATUS. PENDING_CANCELLATION | 中止待ち | TASK_STATUS | 中止待ち | isEnabled |  |

### **\[タスク\] (Task)**

ポータルから開始されたバックグラウンドタスク（Azure Automation
Runbookジョブとして実行される）の実行状態と結果を管理するためのテーブルである。主要な列として、タスクID（id）、タスク名（taskName）、タスク種別（taskType。「値の一覧」テーブル参照）、ステータス（status。「値の一覧」テーブル参照）、レコード作成日時（submittedAt）、開始日時（startedAt）、終了日時（endedAt）、実行ユーザー（submittedByUserId）、契約ID（licenseId）、サーバID（targetServerId）、パラメータ（parametersJson、JSON形式）、タスク詳細（resultMessage）等が含まれる。

- 1つのライセンスは複数のタスクに関連付けられることがある。

- 1つのタスクは1つのライセンスに関連付けられる。

- 1つのサーバは複数のタスクに関連付けられることがある。

- 1つのタスクは1つのサーバに関連付けられる。

- 操作ログのエクスポートタスクの場合、正常終了したタスクのレコードは、「操作ログ」テーブルの対応するレコードにタスクIDで関連付けられる。

　レコードの作成・参照・更新・削除について（Azure
Functionsで実行されるポータル関数の詳細は「8.
ポータル関数」を参照してください）：

- 作成：ユーザーがサーバ一覧画面からバックグラウンドタスクを選択し、対象コンテナに実行中のタスクがない場合、ポータルシステムのサーバ側プログラムが「タスク」テーブルにステータスがQUEUEDの新規レコードを1件作成する。

- 参照：ユーザーがタスク一覧画面を開く時、ポータルシステムのサーバ側プログラムが「タスク」テーブルからユーザーと同じライセンスのタスクレコードを取得する。

- 更新：

  - Azure Service
    BusのTaskInputQueueのメッセージで起動されるタスク実行関数TaskExecuteFuncがRunbookジョブを作成した後、「タスク」テーブルから該当タスクレコードのステータスをQUEUEDからRUNBOOK_SUBMITTEDに更新する。

  - Runbookジョブ監視関数RunbookMonitorFuncは周期的に起動され、すべてのRUNBOOK_SUBMITTEDステータスのタスクに対し、実行時間及び関連するRunbookジョブの現在のステータス（Azure
    Automationから取得したジョブのステータス。ポータルで定義したタスクのステータスとは別物）を監視する。ジョブが正常終了した場合、異常状態になった場合、或いは実行時間がタイムアウトした場合、RunbookMonitorFuncは「タスク」テーブルから該当タスクレコードのステータスをRUNBOOK_SUBMITTEDからRUNBOOK_PROCESSINGに更新する。

  - Azure Service
    BusのRunbookStatusQueueのメッセージで起動されるRunbookジョブ処理関数RunbookProcessorFuncはメッセージの内容によって、「タスク」テーブルから該当タスクレコードのステータスをRUNBOOK_PROCESSINGからCOMPLETED_SUCCESSかCOMPLETED_ERRORに更新する。

  - タスクのステータスがQUEUEDで、ユーザーがタスク一覧画面から中止操作を行った時、ポータルのサーバ側のプログラムは「タスク」テーブルから該当タスクレコードのステータスをQUEUEDからPENDING_CANCELLATIONに更新する。

  - Azure Service
    BusのTaskControlQueueのメッセージで起動されるタスク中止関数TaskCancellationFuncは、「タスク」テーブルから該当タスクレコードのステータスをPENDING_CANCELLATIONからCANCELLEDに更新する。

  - タスク実行関数TaskExecuteFunc、Runbookジョブ処理関数RunbookProcessorFuncがタイムアウトした時、それぞれのタイムアウト処理用関数TaskExecuteTimeoutFunc、RunbookProcessorTimeoutFuncが「タスク」テーブルから該当タスクレコードのステータスをCOMPLETED_ERRORに更新する。

- 削除：「タスク」テーブルにおいて、タスクのレコードはマネージャサーバごとに、タスクの種別問わず、上限件数（「値の一覧」テーブルの
  TASK_CONFIG.
  MAX_RETENTION_COUNTの値で設定され、デフォルトで10件）まで保持される。対象サーバがすでに上限件数以上のタスクレコードを保持している場合に新規タスクが開始される時、タスク実行関数TaskExecuteFuncがレコードの削除を行う。詳細は「8.3.1
  ポータル関数」の「(1) TaskExecuteFunc
  (タスク実行関数)」を参照してください。

### **\[コンテナ実行状態\](ContainerConcurrencyStatus)**

エンドポイントVM上の各管理対象コンテナの現在のタスク実行状態（IDLE /
BUSY）を管理するためのテーブルである。Azure Functions
のタスク実行関数TaskExecuteFuncがタスクを実行する前にコンテナの空き状況を確認し、BUSY状態に更新するために使用される。また、Runbookジョブ処理関数RunbookProcessorFuncがタスク完了後にコンテナをIDLE状態に戻すために使用される。ポータルからの事前状態確認もこのテーブルを参照する。

主要な列として、対象VM名（targetVmName）、対象コンテナ名（targetContainerName）、ステータス（status。IDLEとBUSYのいずれか）、使用中のタスクID（currentTaskId。対象コンテナを使用しているタスクのID）、コンテナ状態最終更新日時（lastStatusChangeAt）等が含まれる。

レコードの作成・参照・更新・削除について（Azure
Functionsで実行されるポータル関数の詳細は「8.
ポータル関数」を参照してください）：

- 作成：ユーザーがサーバ一覧画面からバックグラウンドタスクを選択する時、ポータルシステムのサーバ側のプログラムが「コンテナ実行状態」テーブルから、対象サーバのVM名とコンテナ名の組み合わせに対してタスク実行状態（ステータス）を確認する。この時、レコードが存在しないなら新規で作成する。初期状態はstatus
  = IDLE, currentTaskId = NULL。

- 参照：ユーザーがサーバ一覧画面からバックグラウンドタスクを選択する時、ポータルシステムのサーバ側のプログラムが「コンテナ実行状態」テーブルから、対象サーバのVM名とコンテナ名の組み合わせに対してタスク実行状態（ステータス）を確認する。また、タスク実行関数TaskExecuteFuncがRunbookジョブを作成する前にも同様の確認を行う。

- 更新：

  - タスク実行関数TaskExecuteFuncがRunbookジョブを作成する前に、「コンテナ実行状態」テーブルから、対象サーバのVM名とコンテナ名の組み合わせに対してタスク実行状態（ステータス）を確認する。IDLEであればBUSYに更新して、Runbookジョブを作成する。

  - Azure Service
    BusのRunbookStatusQueueのメッセージで起動されるRunbookジョブ処理関数RunbookProcessorFuncが「コンテナ実行状態」テーブルから該当コンテナレコードのステータスをBUSYからIDLEに更新する。

  - タスク実行関数TaskExecuteFunc、Runbookジョブ処理関数RunbookProcessorFuncがタイムアウトした時、それぞれのタイムアウト処理用関数TaskExecuteTimeoutFunc、RunbookProcessorTimeoutFuncが「コンテナ実行状態」テーブルから該当コンテナレコードのステータスをBUSYからIDLEに更新する（IDLEならそのままにする）。

- 削除：本設計では、このテーブルからのレコードの物理的な削除は積極的には行わない。不要となったVM/コンテナのレコードは冗長データとして残存する可能性があるが、運用上の大きな問題とはならない想定である。必要に応じて、別途バッチ処理等でのアーカイブやクリーンアップを検討する。

## データベースマイグレーション

このデータベースの変更を管理し、新しいバージョンへの移行を容易にするために、Prisma
Migrate を使用している。マイグレーションは以下の目的で使用される：

- データベーステーブルの変更やスキーマの更新を追跡し、新しいバージョンに反映する。

- データベースの変更履歴を管理し、バージョン間の一貫性を保つ。

データベースマイグレーションの実行には、Prisma CLI
コマンドを使用する。これにより、スムーズなデータベーススキーマの更新と移行が可能になる。

## データベースシード

データベースの初期データを準備するために、Prisma Seed
機能を使用している。シードは以下の目的で使用される：

- テスト環境や開発環境でのデータベースの初期化とサンプルデータの投入。

- アプリケーションの初回セットアップ時に必要なデフォルトデータの挿入。

Prisma Seed
を使用することで、データベースの初期データを効率的かつ一貫性のある方法で生成できる。

# ストレージ設計

本システムでは、ファイルストレージとして主にAzure Blob
StorageおよびAzure Filesの2種類のAzureストレージサービスを利用する。

- Azure Blob Storage：

  - ユーザーがポータルからダウンロード可能な最終成果物（操作ログファイル、製品媒体、マニュアル、提供ファイル、サポート情報添付ファイル、管理項目定義ファイル等）の格納先である。

  - 管理項目定義のインポート機能において、ユーザーがアップロードするCSVファイルの一時的な格納場所としても利用される場合がある（Next.js
    API Routes経由でのアップロード、またはSAS
    Tokenを利用したフロントエンドからの直接アップロード）。

- Azure Files：

  - Azure Automation
    RunbookがエンドポイントVM上で実行される際の、主要な一時ファイル共有ワークスペースとして機能する。

  - VM（Hybrid Runbook Worker経由）とAzure Functions (TaskExecuteFunc,
    RunbookProcessorFunc)
    間で、タスク処理中の中間ファイルを中継するために利用される。

各ファイルの具体的なAzure Blob
Storage上のパスは、本章で後述するファイル種別ごとのコンテナ名、ディレクトリ構造（契約ID、製品識別子等に基づく）、および対応のデータベースレコードのファイル名を組み合わせて組み立てられる。

Azure
Files上のワークスペースパスは、タスク毎に一意なディレクトリ（タスクIDで区別する。例：\\\<ストレージアカウント名\>.file.core.windows.net\\共有名\>\TaskWorkspaces\\TaskID\>\\がタスク実行関数TaskExecuteFuncにより準備される。Runbookジョブを作成する時にパラメータとしてタスクIDが渡される。当該ワークスペースは、タスク完了後にRunbookジョブ処理関数RunbookProcessorFuncによりクリーンアップされる。

## ダウンロードポリシー

#### ユーザーがダウンロードをクリック

ユーザーが画面でダウンロードボタンをクリックすると、サービスはAzure Blob
Storageへのアクセスを可能にする特別なリンク、共有アクセス署名（SAS）を生成する。

#### SASの生成

■背景、前提

Azure
BLOBに格納したファイルをダウンロードする場合、Azureの要件(\*1)によりShared
Access Signatures (SAS)を使用して、Azure
BLOBへの一時的なアクセス許可をクライアントに付与する必要がある。

\*1：Azure
BLOBのファイルにアクセスするためのURIは一般公開されるため、URIが盗聴されるとセキュリティ侵害リスクとなる。セキュリティ確保の観点から、アクセス制限の仕組みとしてSASによるコントロールが必須となっている。

資産配布管理サービス(本サービス)において、操作ログ/製品媒体と付属ドキュメント/マニュアル/提供ファイル/サポート情報といった提供物をサービスポータルからサービスのユーザーに提供するにあたり、これらの提供物をAzure
BLOBに格納する方式を採用する。

■SASの種類

本サービスのシステム構成で実現可能、かつ、必要最低限のセキュリティを確保できるSASを決定する。

<table>
<colgroup>
<col style="width: 5%" />
<col style="width: 10%" />
<col style="width: 14%" />
<col style="width: 29%" />
<col style="width: 13%" />
<col style="width: 25%" />
</colgroup>
<thead>
<tr>
<th style="text-align: center;">項番</th>
<th style="text-align: center;">検討項目</th>
<th style="text-align: center;">検討案</th>
<th style="text-align: center;">説明</th>
<th style="text-align: center;">検討結果</th>
<th style="text-align: center;">根拠</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align: right;">1</td>
<td>SASの種類</td>
<td>ユーザー委任SAS</td>
<td>Azure Active Directoryによるアクセス認証(Microsoft
Entra資格情報)によって保護される<br />
Microsoftが推奨する、3種類の中で最高レベルの方式。</td>
<td>不採用</td>
<td>Azure ADが前提となる</td>
</tr>
<tr>
<td style="text-align: right;">2</td>
<td>　</td>
<td>サービスSAS</td>
<td>ストレージ アカウント キー(共有キー)で保護される<br />
1つのSASで1つのリソースにアクセス可能</td>
<td>採用</td>
<td>アクセス許可範囲が局所化できる</td>
</tr>
<tr>
<td style="text-align: right;">3</td>
<td>　</td>
<td>アカウントSAS</td>
<td>ストレージ アカウント キー(共有キー)で保護される<br />
1つのSASで複数のリソースにアクセス可能<br />
また、サービスSASに比べて多様なアクセス権限付与が可能</td>
<td>不採用</td>
<td>アクセス許可範囲と権限が広範にできることによるセキュリティリスクが増大</td>
</tr>
</tbody>
</table>

**上記の検討により、SASの種類は サービスSAS を採用する。**

■SASの有効期限

・ダウンロードするファイルのサイズとダウンロード頻度を考慮して、有効期限切れによるダウンロード失敗(\*1)が発生しない十分な有効期限を決定する。

\*1：ポータルからファイルをダウンロードする際、クライアント(サービスユーザーの運用端末)からサーバ(ポータル)に対して複数のリクエストを出してダウンロードを行う。SASの有効期限はダウンロードを最初に実行したリクエストの時刻を起点に決定される(リクエストごとに有効期限はリセットされない)。このため、後続のダウンロードリクエストが有効期限後に発生した場合、当該リクエストは拒否されることになるため、ダウンロードが失敗するケースがある。

| 項番 | 検討項目 | 検討案 | 説明 | 検討結果 | 根拠 |
|---:|----|----|----|----|----|
| 1 | SASの有効期限 | 1時間 | 本サービスで想定する最大サイズ(2GB)がダウンロードできる時間 | 不採用 | 通信速度低下時にダウンロードに失敗する可能性がある |
| 2 | 　 | 2時間 | 本サービスで想定する最大サイズ(5GB)がダウンロードできる時間 | 採用 | 通信速度低下時にダウンロードに失敗する可能性がある |
| 3 | 　 | 4時間 | 低速度回線でもダウンロード可能できる時間 | 不採用 | 常時低速度回線は考慮しない(現状は日本国内販売のみ) |

・ダウンロードサイズと通信速度ごとのダウンロード時間の試算

| 　 | 通信速度 | 1Mbps |  | 5Mbps |  | 10Mbps |  | 100Mbps |  | 1000Mbps |  |
|----|---:|:--:|:--:|:--:|:--:|:--:|:--:|:--:|:--:|:--:|:--:|
| 　 | (想定環境) | (一時的な回線速度低下) |  | (公衆回線と速度低下時の中間) |  | (スマホ3G回線) |  | (スマホ4G、ADSL回線) |  | (スマホ5G、光回線回線) |  |
| ダウンロードサイズ | 　 | 1 | 　 | 5 | 　 | 10 | 　 | 100 | 　 | 1000 | 　 |
| 100MB | 100 | 13.3 | 分 | 2.7 | 分 | 1.3 | 分 | 8.0 | 秒 | 0.8 | 秒 |
| 500MB | 500 | 1.1 | 時間 | 13.3 | 分 | 6.7 | 分 | 40.0 | 秒 | 4.0 | 秒 |
| 1GB | 1000 | 2.2 | 時間 | 26.7 | 分 | 13.3 | 分 | 80.0 | 秒 | 8.0 | 秒 |
| 2GB | 2000 | 4.4 | 時間 | 53.3 | 分 | 26.7 | 分 | 2.7 | 分 | 16.0 | 秒 |
| 5GB | 5000 | 11.1 | 時間 | 2.2 | 時間 | 1.1 | 時間 | 6.7 | 分 | 40.0 | 秒 |
| 10GB | 10000 | 22.2 | 時間 | 4.4 | 時間 | 2.2 | 時間 | 13.3 | 分 | 80.0 | 秒 |

★5GBが本サービスで想定する最大サイズ

**上記の検討により、SASの有効期限は 2時間を採用する。**

本サービスは、ユーザーのリクエストに応じて、一時的な読み取り権限を持つSASを生成し、デフォルトで有効期限を2時間に設定している。テーブルのLOVのSAS有効期限を変更して、

有効期限を変更できる。具体は[\[値の一覧\](Lov)](#値の一覧-lov)を参照してください。

#### 画面でのダウンロード

ダウンロードリンクをクリックすると、新しいタブを開く。画面は、提供されたSASを使用してファイルをダウンロードする。このSASにより、ユーザーは安全で制限されたアクセスを得ることができる。SASは期限切れると無効になる。ダウンロードファイルが存在している場合、開いたタブをクローズする。ダウンロードファイルが存在しない場合、開いたタブ上にエラーメッセージは表示される。

## Azure Blob Storageディレクトリ構造の設計

### 操作ログ

#### Container 名

操作ログ用の Container を作成し、「oplogs」という名前を付ける。

#### ディレクトリ構造 

契約IDごとにディレクトリを作成する。例えば、「hisol」のような形式で保存する。

タスクの実行によりエクスポートされたログの場合、契約IDディレクトリの下にタスクIDディレクトリ（例：hisol/{taskID}/）を作成し、そのタスクでエクスポートされたログファイルを格納する。Runbook（基盤スクリプト）によってエクスポートされた時のログファイル名は固定でexportoplog\_\<連番\>.zipであるが、Runbookジョブ処理関数RunbookProcessorFuncによってAzure
Filesの一時ワークスペースからAzure Blob
Storageにコピーされる時は{タスク名}\_{連番}.zipにリネームされる。

エクスポートタスク以外で生成されたログの場合は契約IDディレクトリに格納される。

#### Blob 名

Blob
名には契約IDと（タスクの実行によりエクスポートされたログの場合はタスクIDと）ファイル名を含める。

例えば、「hisol/oplog_20231110-125959_005.zip」や「hisol/{taskID}/{タスク名}\_001.zip」のような形式で
Blob を保存する。

#### Blob URL

各 Blob にアクセスするための URL は、Container 名と Blob 名を含める。

例えば、「https:// jp1ph2blob.blob.core.windows.net/oplogs/ hisol
/oplog_20231110-125959_005.zip」のような形式である。

### 製品媒体

#### Container 名

製品媒体用の Container を作成し、「product-medias」という名前を付ける。

#### ディレクトリ構造 

製品形名及びバージョンの組み合わせを使用してディレクトリを作成する。例えば、「P-1J42-7FCL
/ 12-00-04」のような形式で保存する。

#### Blob 名

Blob 名には製品形名、バージョン及びファイル名を含める。

例えば、「P-1J42-7FCL/12-00-04/jp1itdm2_120004_hpux_doc.zip」のような形式で
Blob を保存する。

#### Blob URL

各 Blob にアクセスするための URL は、Container 名と Blob 名を含める。

例えば、「https:// jp1ph2blob.blob.core.windows.net/ product-medias /
P-1J42-7FCL/12-00-04/jp1itdm2_120004_hpux_doc.zip」のような形式である。

### マニュアル

#### Container 名

マニュアル用の Container
を作成し、「product-manuals」という名前を付ける。

#### ディレクトリ構造 

資料番号ごとディレクトリを作成する。例えば、「01-20240401」のような形式で保存する。

#### Blob 名

Blob 名には資料番号及びファイル名を含める。

例えば、「01-20240401/H03E1530.PDF」のような形式で Blob を保存する。

#### Blob URL

各 Blob にアクセスするための URL は、Container 名と Blob 名を含める。

例えば、「https://jp1ph2blob.blob.core.windows.net/product-manuals/01-20240401/H03E1530.PDF」のような形式である。

### 提供ファイル

#### Container 名

提供ファイル用の Container
を作成し、「provided-files」という名前を付ける。

#### Blob 名

Blob 名にはファイル名を含める。

例えば、「jp1cs_endpoint_cert.zip」のような形式で Blob を保存する。

#### Blob URL

各 Blob にアクセスするための URL は、Container 名と Blob 名を含める。

例えば、「https://jp1ph2blob.blob.core.windows.net/provided-files/jp1cs_endpoint_cert.zip」のような形式である。

### サポート情報

#### Container 名

サポート情報用の Container
を作成し、「support-files」という名前を付ける。

#### ディレクトリ構造 

通番ごとディレクトリを作成する。例えば、「1」のような形式で保存する。

#### Blob 名

Blob 名には資料番号及びファイル名を含める。

例えば、「1/H03E1530.PDF」のような形式で Blob を保存する。

#### Blob URL

各 Blob にアクセスするための URL は、Container 名と Blob 名を含める。

例えば、「https://jp1ph2blob.blob.core.windows.net/support-files/1/H03E1530.PDF」のような形式である。

### 管理項目定義

管理項目定義のインポート・エクスポートタスクにおいて、Azure Blob
Storageはインポート用CSVファイルのアップロード先およびエクスポート結果CSVファイルの格納先として利用される。

#### Container 名

管理項目定義ファイル（インポート用CSVファイル、エクスポート結果CSVファイル）用の
Container を作成し、「assetsfield-def」という名前を付ける。

#### ディレクトリ構造 

契約ID毎のディレクトリ（例：hisol/）を作成し、その配下に以下のディレクトリを配置する。

- imports/{taskID}/:
  該当インポートタスクでユーザーがアップロードした管理項目定義CSVファイルの格納用。

- exports/{taskID}/:
  該当エクスポートタスクでエクスポートされた管理項目定義CSVファイルの格納用。

#### インポート用CSVファイルのBlob名とBlob URL

ポータルのサーバ側プログラムはユーザーがアップロードした任意のファイル名のCSVファイルを、固定のassetsfield_def.csvにリネームし、{licenseID}/imports/{taskID}/assetsfield_def.csvといったBlob名でAzure
Blob
Storageに一時保存する。タスク実行関数TaskExecuteFuncはこのファイルを後述のAzure
Filesワークスペースへコピーする。

Azure Blob
Storageに一時保存されるインポート用CSVファイルは、通常はユーザーへの直接ダウンロード提供を意図しない。システム内部（主にタスク実行関数TaskExecuteFunc）でのみアクセスされる。

各 Blob にアクセスするための URL は、Container 名と Blob
名を含める。例えば、「https://jp1ph2blob.blob.core.windows.net/assetsfield-def/{licenseID}/imports/{taskID}/assetsfield_def.csv」のような形式である。

#### エクスポート結果CSVファイルのBlob名とBlob URL

Runbook（基盤スクリプト）によってエクスポートされた管理項目定義CSVファイルのファイル名は固定でassetsfield_def.csvである。Runbookジョブ処理関数RunbookProcessorFuncは、Azure
Filesワークスペースからエクスポート結果のCSVファイルを、{licenseID}/exports/{taskID}/assetsfield_def.csvといったBlob名でAzure
Blob
Storageに保存する。このBlob名は「タスク一覧」画面からファイルをダウンロードする時に使用される。ポータルは、上記URLに対しSAS
Tokenを生成し、ユーザーに安全なダウンロードリンクを提供する。

各 Blob にアクセスするための URL は、Container 名と Blob
名を含める。例えば、「https://jp1ph2blob.blob.core.windows.net/assetsfield-def/{licenseID}/exports/{taskID}/assetsfield_def.csv」のような形式である。

## Azure Filesディレクトリ構造の設計

Azure
Functionsのタスク実行関数TaskExecuteFuncがタスクの実行を開始する前に、各タスク（TaskID）に対し、Azure
Files共有ストレージ上にTaskWorkspaces/\<TaskID\>/のようなタスク固有の一時ワークスペースを作成し、その下にインポート用のディレクトリimportsとエクスポート用のディレクトリexportsを作成する。ディレクトリ構造とそれぞれの役割は以下とする。

- インポート用ディレクトリTaskWorkspaces/\<TaskID\>/imports/：タスク実行関数TaskExecuteFuncは、Azure
  Blob
  Storageからユーザーがアップロードしたファイルをこのディレクトリにコピーする（例：管理項目定義のCSVファイルassetsfield_def.csv）。対象VMコンテナで実行されるRunbook（基盤スクリプト）はこのディレクトリからインポートファイルを読み込む。

- エクスポート用ディレクトリTaskWorkspaces/\<TaskID\>/exports/：対象VMコンテナで実行されるRunbook（基盤スクリプト）はこのディレクトリに、中間ファイルやエクスポート結果ファイル（例：操作ログファイルexportoplog_001.zip）、エラーメッセージファイル(errordetail.txt)などを出力する。

　タスク実行終了後のRunbookジョブ処理関数RunbookProcessorFuncは、Azure
Files共有ストレージから該当タスクのディレクトリ（TaskWorkspaces/\<TaskID\>/ごと）をクリーンアップ（削除）する。

# Azure Service Bus

## 目的

Azure Service
Busは、本システム内の主要コンポーネント間の非同期メッセージ通信基盤である。タスク要求、制御（中止）要求、および状態通知の確実な伝達を担い、システムの疎結合性と耐障害性を向上させる。

## 前提条件

Azure Service Bus名前空間および以下のキューが構成済みであること。

- TaskInputQueue: 新規タスク要求受信用。

- TaskControlQueue: タスク制御（中止）受信用。

- RunbookStatusQueue: Runbookジョブステータスの通知受信用。

各キューには、配信不能キュー(dead-letter
queue)の構成やメッセージ有効期間等の適切な設定がなされていること。

関連コンポーネント（Next.js API Routes、Azure
Functions等）は、各キューへの適切な送受信権限を有する。

## 機能詳細

#### TaskInputQueue

- Next.js API
  Routesから送信される新規タスク実行要求メッセージを受信する。

- 本キューのメッセージはAzure Functions
  のタスク実行関数TaskExecuteFuncにより処理される。

#### TaskControlQueue

- Next.js API Routesから送信されるタスク中止要求メッセージを受信する。

- 本キューのメッセージはAzure Functions
  のタスク中止関数TaskCancellationFuncにより処理される。

#### RunbookStatusQueue

- Azure
  AutomationのRunbookジョブを監視する関数RunbookMonitorFuncから送信されるジョブステータスメッセージを受信する。

- 本キューのメッセージはAzure Functions
  のRunbookジョブ処理関数RunbookProcessorFuncにより処理される。

#### dead-letter queue

- TaskInputQueue、TaskControlQueue、RunbookStatusQueueには各自のdead-letter
  queueがあり、それぞれのメッセージはそれぞれのAzure Functions
  のタイムアウト処理関数TaskExecuteTimeoutFunc、TaskCancellationTimeoutFunc、RunbookProcessorTimeoutFuncにより処理される。

- Azure Functions
  の関数TaskExecuteFunc、TaskCancellationFunc、RunbookProcessorFuncのタイムアウトにより、TaskInputQueue、TaskControlQueue、RunbookStatusQueueから各自のdead-letter
  queueに、メッセージがAzure Service Busによって移動される。

# ポータル関数

## 目的

ポータル関数というのは、ポータルシステムのために作成され、Azure
Functions上で実行される関数のことである。主にバックグラウンドタスクの実行、タスクステータスの管理、および他Azureサービス（Azure
SQL DB, Azure Files, Azure Blob Storage, Azure
Automation等）との連携ロジックを担当する。

## 前提条件

Azure Functionsの関数アプリ (Function App)
が適切なホスティングプランと共に作成・デプロイ済みであること。

各関数が、対応するトリガー（例：Service
Busキュー）および入出力バインディングと共に正しく構成されている。

関数アプリは、連携対象のAzureサービスへのアクセス権限（推奨：マネージドID経由）を有する。

関連する環境変数（例：データベース接続文字列、Automationアカウント名等）が関数アプリに設定済みである。

## 機能詳細

### ポータル関数

本システムで利用されるAzure Function関数は以下の通りである。

#### TaskExecuteFunc (タスク実行関数)

TaskInputQueue (Azure Service Bus)
のメッセージ（タスクの実行要求）をトリガーとして起動される。タスクの実行を開始するための事前検証、ファイルのコピー、ワークスペースの準備、Azure
Automation APIの呼び出し、データベースの更新を担当する。

責務：

- 入力パラメータが正しいかどうかチェックし、正しくなければタスクを開始しない。タスクのステータスをCOMPLETED_ERRORに更新し、タスク詳細を対応したエラーメッセージに更新する。

- 対象タスクのステータスをデータベースの「タスク」テーブルから確認し、PENDING_CANCELLATIONかCANCELLEDの場合（中止操作が行われた場合）はタスクを開始しない。

- 対象VMコンテナのタスク実行状態（ステータス）をデータベースの「コンテナ実行状態」テーブルから確認し、BUSYの場合はタスクを開始しない。タスクのステータスをCOMPLETED_ERRORに更新し、タスク詳細を「{0}に対するタスクを実行中のため実行できません。実行中のタスクが完了してから再度実行してください。」（※{0}：対象サーバ名）といったメッセージに更新する。

- 事前チェックに問題がなければ、「コンテナ実行状態」テーブルから対象VMコンテナのステータスをBUSYに更新する。

- Azure
  Files上にタスクIDごとのタスク用一時ワークスペースを準備する。「6.3Azure
  Filesディレクトリ構造の設計」を参照してください。

- 管理項目定義のインポートタスクの場合、ユーザーがアップロードしたファイル（ポータルのサーバ側プログラムによって固定のファイル名assetsfield_def.csvでAzure
  Blob Storageにアップロードされる）をAzure Blob StorageからAzure
  Filesワークスペースのインポート用ディレクトリTaskWorkspaces/\<TaskID\>/imports/にコピーする。

- タスクの開始に問題がなければ、パラメータとしてタスクID、実行するRunbook（基盤スクリプト）名、Hybrid
  Runbook Worker Group名、実行コンテナ名などの情報を渡し、Azure
  Automation
  のRunbookジョブ作成APIを呼び出す。Runbookジョブを取得、ストップする時に必要なジョブ名にはタスクIDを設定する。

- タスクの開始日時をDBに記録し、タスクのステータスをRUNBOOK_SUBMITTEDに更新する。

- 「タスク」テーブルにおいて、タスクのレコードはマネージャサーバごとに、タスクの種別問わず、上限件数（「値の一覧」テーブルの
  TASK_CONFIG.
  MAX_RETENTION_COUNTの値で設定され、デフォルトで10件）まで保持される。Runbookジョブの作成とタスクステータスの更新までの処理に成功した時、対象サーバが上限件数よりも多いタスクレコードを保持している場合、タスクレコードの件数が上限件数になるまで、タスク実行関数TaskExecuteFuncがレコード作成日時の古い順から対象サーバと関連する、かつステータスがCOMPLETED_SUCCESS
  / COMPLETED_ERROR /
  CANCELLEDのいずれかであるタスクレコードを削除する。対象サーバにステータスがCOMPLETED_SUCCESS
  / COMPLETED_ERROR /
  CANCELLEDのタスクレコードがない場合はタスクレコードの件数が上限件数を超えても削除を行わない。タスクのレコードを削除する時、Azure
  Blob
  Storageから該当タスクと関連するファイル（インポートタスクでユーザーがアップロードしたファイルと、エクスポートタスクで出力されたファイル。タスクIDで絞り込む）をも一緒に削除する。一回の操作ログのエクスポートタスクで複数のログファイルがエクスポートされた場合は関連するログファイルをすべて削除する。削除処理が失敗しても今回のタスク実行関数の実行対象タスクのステータスに影響を与えない。

#### TaskCancellationFunc (タスク中止関数)

TaskControlQueue (Azure Service Bus)
のメッセージ（タスクの中止要求）をトリガーとして起動される。タスクを中止するためのデータベース更新を担当する。

責務：

- 対象タスクのステータスをデータベースの「タスク」テーブルから確認する。

- PENDING_CANCELLATIONの場合はステータスをCANCELLEDに更新し、タスク詳細を「ユーザーによってタスクが中止されました」といったメッセージに更新する。

- RUNBOOK_SUBMITTEDかRUNBOOK_PROCESSINGの場合（すでにRunbookジョブが作成され、実行中の場合）はタスクのステータスを更新せず、タスク詳細を「タスクの実行がすでに開始したため中止できませんでした」といったメッセージに更新する。

- COMPLETED_SUCCESS / COMPLETED_ERROR /
  CANCELLEDの場合（タスクがすでに終了した場合）は何もしない。

#### RunbookProcessorFunc (Runbookジョブ処理関数)

RunbookStatusQueue (Azure Service Bus)
のメッセージ（Runbookジョブステータス通知）をトリガーとして起動される。Runbookジョブ終了後の後処理として、データベースの更新、ファイルのコピー、ワークスペースのクリーンアップ、場合によってAzure
Automation APIの呼び出しなどを担当する。

責務：

- RunbookStatusQueueのメッセージからRunbookジョブのステータスを取得する。

- RunbookジョブのステータスがCompletedの場合、「タスク」テーブルの該当タスクのステータスをCOMPLETED_SUCCESSに更新し、タスク種別によってタスク詳細を更新する（4.13.6タスク一覧の機能詳細（外部仕様）を参照）。

- RunbookジョブのステータスがCompletedかつエクスポートタスクの場合、Azure
  FilesワークスペースのエクスポートディレクトリTaskWorkspaces/\<TaskID\>/exports/から、Runbook（基盤スクリプト）が出力したファイルをAzure
  Blob Storageへコピーする。

  - 操作ログのエクスポートタスクの場合、Runbook（基盤スクリプト）が固定で出力したファイルexportoplog\_\<連番\>.zipを{タスク名}\_{連番}.zipにリネームしてAzure
    Blob Storageへコピーする。

  - 管理項目定義のエクスポートタスクの場合、Runbook（基盤スクリプト）が固定で出力したファイル名assetsfield_def.csvをそのままにする。エクスポートされたファイルをAzure
    FilesからAzure Blob Storageにコピーする。

- RunbookジョブのステータスがCompletedかつエクスポートタスクの場合、エクスポートされたファイルの情報を関連のテーブルに更新/追加する。

  - 操作ログのエクスポートタスクの場合、エクスポートされたすべてのログファイルのログ名、サイズ、登録日時、タスクIDなどの情報を「操作ログ」テーブルに新規で追加する。

  - 管理項目定義のエクスポートタスクの場合、ユーザーがダウンロードするためのリンクとして、ファイルがAzure
    Blob StorageにおけるBlob
    URLを、「タスク」テーブルの該当タスクレコードの「ダウンロードパス」フィールドに更新する。

- RunbookジョブのステータスがFailedの場合、Azure
  Files上のタスク用一時ワークスペースのエクスポートディレクトリTaskWorkspaces/\<TaskID\>/exports/からエラーメッセージファイル(errordetail.txt)の存在を確認する。エラーメッセージファイルが存在している場合、「タスク」テーブルの該当タスクのステータスをCOMPLETED_ERRORに更新し、タスク詳細を「タスクの実行に失敗しました。エラー詳細：{0}」（※{0}：エラーメッセージファイルの内容）といったメッセージに更新する。エラーメッセージファイルが存在しない場合、「タスク」テーブルの該当タスクのステータスをCOMPLETED_ERRORに更新し、タスク詳細を「タスクの実行に失敗しました。サポートサービスにお問い合わせください。」といったメッセージに更新する。

- RunbookジョブのステータスがRemoving / Stopped /
  Stoppingの場合、「タスク」テーブルの該当タスクのステータスをCOMPLETED_ERRORに更新し、タスク詳細を「システムによりタスクの実行を中止しました。タスクの実行中にサービスのメンテナンスが開始された可能性があります。サービスのメンテナンス終了後に再度実行してください。」といったメッセージに更新する。

- RunbookジョブのステータスがResuming / Suspended /
  Suspendingの場合、「タスク」テーブルの該当タスクのステータスをCOMPLETED_ERRORに更新し、タスク詳細を「システムによりタスクの実行を中止しました。タスクの実行中にサービスのメンテナンスが開始された可能性があります。サービスのメンテナンス終了後に再度実行してください。」といったメッセージに更新する。タスクIDのジョブ名でAzure
  Automation のRunbookジョブストップAPIを呼び出す。

- RunbookジョブのステータスがTimeoutの場合、「タスク」テーブルの該当タスクのステータスをCOMPLETED_ERRORに更新し、タスク詳細を「タスクの完了が確認できないため、システムによってタスクを中止しました。タスクが操作ログのエクスポートの場合、エクスポートするログの期間を短くして再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。」といったメッセージに更新する。タスクIDのジョブ名でAzure
  Automation のRunbookジョブストップAPIを呼び出す。

- Runbookジョブのステータスに関係なく、Azure
  Files上のタスクIDごとのタスク用一時ワークスペースをクリーンアップする。「6.3Azure
  Filesディレクトリ構造の設計」を参照してください。

- 「コンテナ実行状態」テーブルの該当VMコンテナのタスク実行状態（ステータス）をIDLEに更新する。

#### TaskExecuteTimeoutFunc（タスク実行タイムアウト関数）

TaskInputQueueのdead-letter queue (Azure Service Bus)
のメッセージをトリガーとして起動される。タスク実行関数TaskExecuteFuncの実行時間がタイムアウトした時の対応処理として、データベースの更新、ワークスペースのクリーンアップ、場合によってAzure
Automation APIの呼び出しを担当する。

責務：

- 対象タスクのステータスをデータベースの「タスク」テーブルから確認し、RUNBOOK_SUBMITTEDの場合であれば、タスク実行関数TaskExecuteFuncがタイムアウトする前に、すでにAzure
  AutomationのRunbookジョブ作成APIが呼び出されたと判断する。タスクIDのジョブ名でAzure
  AutomationのRunbookジョブストップAPIを呼び出してRunbookジョブをストップする。タスクのステータスがRUNBOOK_SUBMITTED以外の場合であればRunbookジョブストップAPIの呼び出しは不要である。

- 対象タスクのステータスをCOMPLETED_ERRORに、タスク詳細を「タスクの完了が確認できないため、システムによってタスクを中止しました。タスクが操作ログのエクスポートの場合、エクスポートするログの期間を短くして再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。」といったエラーメッセージに更新する。

- Azure
  Files上のタスクIDごとのタスク用一時ワークスペースをクリーンアップする。「6.3Azure
  Filesディレクトリ構造の設計」を参照してください。

- 「コンテナ実行状態」テーブルの該当VMコンテナのタスク実行状態（ステータス）をIDLEに更新する。

#### TaskCancellationTimeoutFunc（タスク中止タイムアウト関数）

TaskControlQueueのdead-letter queue (Azure Service Bus)
のメッセージをトリガーとして起動される。タスク中止関数TaskCancellationFuncの実行時間がタイムアウトした時の対応処理として、データベースの更新を担当する。

責務：

- 対象タスクのステータスをデータベースの「タスク」テーブルから確認する。

- PENDING_CANCELLATIONの場合はタスクのステータスをCOMPLETED_ERRORに更新し、タスク詳細を「タスクの中止がタイムアウトしました。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。」といったメッセージに更新する。

- RUNBOOK_SUBMITTED /
  RUNBOOK_PROCESSINGの場合はタスクのステータスを更新せず、タスク詳細を「タスクの中止がタイムアウトしました。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。」といったメッセージに更新する。

- COMPLETED_SUCCESS / COMPLETED_ERROR /
  CANCELLEDの場合（タスクがすでに終了した場合）は何もしない。

#### RunbookProcessorTimeoutFunc（Runbookジョブ処理タイムアウト関数）

RunbookStatusQueueのdead-letter queue (Azure Service Bus)
のメッセージをトリガーとして起動される。Runbookジョブ処理関数RunbookProcessorFuncの実行時間がタイムアウトした時の対応処理として、データベースの更新、ワークスペースのクリーンアップを担当する。

責務：

- 「タスク」テーブルから対象タスクのステータスをCOMPLETED_ERRORに、タスク詳細を「タスクの完了が確認できないため、システムによってタスクを中止しました。タスクが操作ログのエクスポートの場合、エクスポートするログの期間を短くして再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。」といったエラーメッセージに更新する。

- Azure
  Files上のタスクIDごとのタスク用一時ワークスペースをクリーンアップする。「6.3Azure
  Filesディレクトリ構造の設計」を参照してください。

- 「コンテナ実行状態」テーブルの該当VMコンテナのタスク実行状態（ステータス）をIDLEに更新する。

#### RunbookMonitorFunc（Runbookジョブ監視関数）

時間間隔が5分（Azure
Functionsの環境変数RUNBOOK_MONITOR_INTERVAL_SECONDSにて設定可能）のタイマーをトリガーとして起動される。タスクステータスがRUNBOOK_SUBMITTEDのタスクを対象に、関連するRunbookジョブのステータスとタスクの実行時間を監視する。後処理が必要になった場合、タスクのステータスを更新し、Azure
Service Bus
のRunbookStatusQueueにメッセージを送信して、RunbookProcessorFuncを起動する。

責務：

- 「タスク」テーブルからステータスがRUNBOOK_SUBMITTEDのタスクをすべて取得し、監視する。

- タスクの開始日時から現時点までの経過時間を計算し、5時間（Azure
  Functionsの環境変数RUNBOOK_TIMEOUT_SECONDSにて設定可能）を超えた場合、実行されているRunbookジョブがタイムアウトしたと判断する。Azure
  Service Bus
  のRunbookStatusQueueにRunbookジョブのステータスがTimeoutのメッセージを送信し、タスクのステータスをRUNBOOK_PROCESSINGに更新する。

- タスクIDのジョブ名でAzure
  Automationのジョブ取得APIを呼び出して、Runbookジョブの現在のステータスを確認する。

- RunbookジョブのステータスがNew / Activating / Running / Blocked /
  Disconnectedの場合、何もしない。

- RunbookジョブのステータスがCompleted / Failed / Removing / Resuming /
  Stopped / Stopping / Suspended /
  Suspendingの場合、メッセージにRunbookジョブのステータスを入れてAzure
  Service Bus
  のRunbookStatusQueueに送信し、タスクのステータスをRUNBOOK_PROCESSINGに更新する。

### Azure Functionsにおけるタイムアウトの仕様

Azure Functionsで実行される関数は、Azure
Functionsのホスティングプランおよびプロジェクトファイルhost.jsonのfunctionTimeoutプロパティにより実行時間が制限される。Azure
FunctionsにFUNCTION_TIMEOUT_SECONDSの名前で環境変数を作成し、デフォルトで300秒（5分）の値をAzure
Functions関数のタイムアウト時間としてfunctionTimeoutプロパティに設定する。

Azure
Functionsが関数の実行時間を監視し、タイムアウト時間になっても関数がまだ終了していない場合、Azure
Functionsは関数の実行を強制的に中止する。この時関数のトリガーとなったAzure
Service Busのメッセージは処理完了の確認が取れなくなるため、Azure Service
BusによってTaskInputQueue、TaskControlQueue、或いはRunbookStatusQueueから、それぞれのdead-letter
queueに移動される。各dead-letter queue をトリガーとするAzure
Functionsのタイムアウト処理関数TaskExecuteTimeoutFunc、TaskCancellationTimeoutFunc、RunbookProcessorTimeoutFuncは起動され、8.3.1で記載された処理を行う。

# Azure Automation と Hybrid Runbook Worker

## 目的

Azure
Automationは、PowerShellベースのRunbook（自動化スクリプト）を一元的に管理・実行するサービスである。本システムでは、エンドポイントVM上での実際のタスク処理（コマンド実行、ファイル処理等）を実行するために利用される。Hybrid
Runbook Worker (HRW) は、Azure
Automationがオンプレミスまたは特定の仮想ネットワーク内のVM上でRunbookを実行するためのエージェント機能である。

## 前提条件

Azure Automationアカウントが作成済みであること。

業務ロジックを実装したPowerShell
Runbook（基盤スクリプト）がAutomationアカウントに登録・公開済みである。Runbookは必要なパラメータ（タスクID、コンテナ名等）を受け取れる設計である。

対象VMにHybrid Runbook Worker (HRW)
Agentがインストール・構成され、Automationアカウントに登録され、適切なHRWグループに割り当てられている。

HRWが動作するVMは、Azure
Filesのタスクワークスペースへのアクセスが可能である。

VMのOS、Docker Engine、ネットワーク、HRW
Agent等の基盤環境は、VM所有者の責任範囲で適切に構成・維持されている。

## 機能詳細

### PowerShell Runbook (基盤スクリプト)

Azure Functionsのタスク実行関数TaskExecuteFuncからAzure Automation
API経由で非同期に、Automationジョブとして起動される。実際の業務処理を担当し、対象コンテナに対し業務コマンド（操作ログのエクスポートコマンド、管理項目定義のインポートコマンド、管理項目定義のエクスポートコマンド）を実行するスクリプトである。

責務：

- 起動パラメータ（タスクID、コンテナ名等）の受け取りと検証。

- タスクIDで指定されたAzure
  Files上のタスク用一時ワークスペースでのファイル操作（インポートファイルの読み込み、中間ファイルやエクスポート結果ファイル、エラーメッセージファイルerrordetail.txtの書き込み）。

- docker exec や docker cp
  等のコマンドを用いた、対象VM上のDockerコンテナとの対話。

- 必要に応じたファイル収集および圧縮処理（例：Compress-Archive）。

- Write-Output等のコマンドレットによる実行ログの出力（Azure Monitor
  Agent (AMA) により収集される）。

　基盤スクリプトの詳細については基盤スクリプト設計書を参照してください。

### Hybrid Runbook Worker (HRW)

エンドポイントVM上で動作し、Azure
AutomationからRunbookジョブを受信・実行するエージェントである。

Azureの仕様により、Workerは自身がアクティブであることをAzure
Automationに30秒ごとにポーリングし、Runbookジョブを受信する。Runbook実行のためのPowerShell環境を提供し、受信したスクリプトの実行を行う。

### Automationジョブのタイムアウトについて

Hybrid Runbook
Workerで実行するRunbookジョブには実行時間の制限がない。このため過度なデータ量や例外により長時間を経てもまだ終了せず、実行中のままのジョブがあっても、Automation側は特に監視や中止を行わない。上記の状況を避けるために、以下の仕様でジョブのタイムアウトを監視する。

Azure
FunctionsにRUNBOOK_TIMEOUT_SECONDSの名前で環境変数を、デフォルトで18000秒（5時間）の値で、Runbookジョブのタイムアウト時間として作成する。

Azure Functionsのタスク実行関数TaskExecuteFunc がAzure
AutomationのAPIを呼び出してRunbookジョブを作成する時、タスクの開始日時をデータベースの「タスク」テーブルに記録し、タスクのステータスをRUNBOOK_SUBMITTEDに更新する。

タイマーをトリガーとし、5分（Azure
Functionsの環境変数RUNBOOK_MONITOR_INTERVAL_SECONDSにて設定可能）ごとに起動されるAzure
FunctionsのRunbookジョブ監視関数RunbookMonitorFuncは、「タスク」テーブルからステータスがRUNBOOK_SUBMITTEDのタスクを監視する。開始日時から現時点までの経過時間がRUNBOOK_TIMEOUT_SECONDSの値を超えたタスクがあれば、そのタスクで作成したRunbookジョブがタイムアウトしたと判断して、Azure
Service
BusのRunbookStatusQueueに該当タスクがタイムアウトしたことを示すメッセージを送信する。

RunbookStatusQueue
をトリガーとして起動されるRunbookジョブ処理関数RunbookProcessorFuncはタイムアウトのメッセージを受信すると、Azure
AutomationのAPIを呼び出してタイムアウトしたRunbookジョブをストップし、「タスク」テーブルから該当タスクのステータスをCOMPLETED_ERRORに更新し、「コンテナ実行状態」テーブルから該当コンテナのステータスをIDLEに更新する。関数についての詳細は「8.ポータル関数」を参照してください。

# セキュリティ

## ユーザー認証

### 認証方式

資産配布管理サービスでは、ユーザー認証において、以下の認証方式が使用される。これらの認証方式は、Keycloak
プラットフォームを使用して実装されている。

多要素認証**：**

- ユーザーはユーザーIDとパスワードとワンタイムパスワードを提供してログインする。

- Keycloakのログイン画面でユーザーIDとパスワードの検証に成功した後、Keycloakモバイル認証画面に移行し、ワンタイムパスワードの入力を求められる。

- （初めてログインする場合、ユーザーはモバイル認証APPでKeycloakモバイル認証画面のQRCODEをスキャンして、ユーザーアカウントと携帯電話のバインドを行う必要がある。）ユーザーはモバイル認証APPが生成したワンタイムパスワードをKeycloakモバイル認証画面に入力する。認証が成功すると、ユーザーはシステムへのアクセスを許可される。一致しない場合、認証に失敗する。

- すべての資格認定に合格した後、ユーザーセッションは JSON Web
  Token（JWT）によって管理される。

### 認証フロー

**ユーザー認証のフローは以下の通りである：**

1\. ユーザーはログイン画面で契約ID、ユーザーIDとパスワードを入力する。

2\. Keycloakは提供されたユーザー名とパスワードを検証する。

3\. ユーザー名とパスワードの認証に成功すると、Keycloak
のモバイル認証画面に移行して、ワンタイムパスワードの入力を求められる。

（初めてログインする場合、Keycloakモバイル認証画面でQRCODEが表示され、ユーザーはモバイル認証APPのスキャン機能を使ってQRCODEを読み込んで、ユーザーアカウントと携帯電話のバインドを行う必要がある。）

4\.
ユーザーがモバイル認証APPで生成されたワンタイムパスワードをKeycloakモバイル認証画面に入力する。

5\. モバイル認証に成功すると、Keycloak は許可コードをportalに戻す。

> 6\. portalは許可コードを使用してKeycloak
> にaccess_token※取得要求を送信する。

7\.
portalがaccess_token※の取得に成功した後、JWT解析access_tokenによるユーザー情報を取得する。

8\. ユーザーは認証され、システムへのアクセスが許可される。

※5.-7.は、認証方式のユーザーセッションの関連記述に対するフローである。

※**access_token**:
access_tokenは、ユーザーが認証に成功した後にKeycloakからクライアント・アプリケーションに発行されるJSON
Web Token（JWT）です。

### ログアウトとログイン無効化

**ユーザーは以下の手順でログアウトできる：**

1\. ユーザーはログアウトボタンを選択する。

2\. クライアントはサーバにログアウトリクエストを送信する。

3\. サーバはユーザーセッションからJWT
Cookieを無効化し、ユーザーをログアウトさせる。

**また、ログインセッションの無効化プロセスは以下のいずれである：**

- JWT（JSON Web
  Token）はログインセッションの有効期限を持つ。JWTが有効期限切れになると、ユーザーは自動的にログアウトする。

- ブラウザの起動設定で「新しいタブを開く」が適用されている場合、ユーザーがブラウザを閉じるとセッションが無効化され、ユーザーはログアウトする。

### セキュリティ対策

ユーザー認証とセッション管理に関するセキュリティは非常に重要である。資産配布管理サービスは以下のセキュリティ措置を採用している：

- パスワードはハッシュ化され、安全に保存される。明文のパスワードは、セキュリティのため、および日本国法規制に従い、システム内外のどこにも保存されず、ログファイルにも含まれない。

- JWTの署名を使用してデータの完全性とセキュリティを確保する。

- ユーザーセッションの有効期限が設定され、セッションの不正使用を防止する。

# その他

## 前提条件

### 開発環境のセットアップ

開発チームは、次の環境で作業する必要がある：

- Node.js 20 LTS 以上

- Visual Studio Codeまたは適切なコードエディタ

- Gitバージョン管理システム

### アクセス許可と認証

システムには、ユーザー認証とアクセス許可管理の実装が必要である。これにより、ユーザーは適切な機能へのアクセス許可を持ち、セキュリティが強化される。

## 技術構成

<table>
<colgroup>
<col style="width: 6%" />
<col style="width: 17%" />
<col style="width: 14%" />
<col style="width: 33%" />
<col style="width: 11%" />
<col style="width: 15%" />
</colgroup>
<thead>
<tr>
<th style="text-align: center;">項番</th>
<th style="text-align: center;">技術要素</th>
<th style="text-align: center;">バージョン</th>
<th style="text-align: center;">説明</th>
<th style="text-align: center;">ライセンス</th>
<th style="text-align: center;">備考</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align: right;">1</td>
<td>Next.js</td>
<td>13.5.6</td>
<td>Node.jsをベースとしたWebアプリケーションフレームワーク</td>
<td>MIT</td>
<td>Ver.13.4.19のキャッシュ機能はエラーがありますので、Ver.13.5.6に変更する。</td>
</tr>
<tr>
<td style="text-align: right;">2</td>
<td>Node.js</td>
<td>v20</td>
<td>サーバサイドjavascript環境</td>
<td>MIT</td>
<td></td>
</tr>
<tr>
<td style="text-align: right;">3</td>
<td>Prisma</td>
<td>4.16.2</td>
<td>Node.jsのORM(オブジェクト関係マッピング)、DBアクセス抽象化</td>
<td>Apache 2.0</td>
<td></td>
</tr>
<tr>
<td style="text-align: right;">4</td>
<td>SWR</td>
<td>2.2.2</td>
<td>クライアントJavaScriptからのデータ取得とそれに関連する操作を提供するReact
Hooksライブラリ(データフェッチライブラリ)</td>
<td>MIT</td>
<td></td>
</tr>
<tr>
<td style="text-align: right;">5</td>
<td>Cypress</td>
<td>12.17.4</td>
<td>Webアプリケーション用テストツール</td>
<td>MIT</td>
<td></td>
</tr>
<tr>
<td style="text-align: right;">6</td>
<td>Jest</td>
<td>29.5.0</td>
<td>React用テストツール</td>
<td>MIT</td>
<td></td>
</tr>
<tr>
<td style="text-align: right;">7</td>
<td>tailwindcss</td>
<td>3.3.3</td>
<td>簡単かつ効率的なスタイル設定に使用する。</td>
<td>MIT</td>
<td></td>
</tr>
<tr>
<td style="text-align: right;">8</td>
<td>Flowbite</td>
<td>2.1.1</td>
<td>フロントエンドの開発に役立つツールとコンポーネントキットであり、コンポーネント設計とユーザーインターフェースの構築をサポートする。</td>
<td>MIT</td>
<td>Next.js のVer.13.5.6とマッチさせるため、Ver.2.1.1に変更する。</td>
</tr>
<tr>
<td style="text-align: right;">9</td>
<td>Azure Blob Storage</td>
<td>12.17.0</td>
<td>ファイルやデータの保存、管理、アクセスを可能になる。アプリケーション内でファイルのアップロード、ダウンロード、保存などの操作に適している。</td>
<td>MIT</td>
<td></td>
</tr>
<tr>
<td style="text-align: right;">10</td>
<td>winston</td>
<td>3.11.0</td>
<td>winston を使用すると、複数のログ出力を設定し、異なるログ情報を処理できます。ログのレベルやフォーマットをカスタマイズすることも可能で、コンソール、ファイル、データベースなどさまざまな出力先にログ情報を記録できます。</td>
<td>MIT</td>
<td></td>
</tr>
<tr>
<td style="text-align: right;">11</td>
<td>dayjs</td>
<td>1.11.9</td>
<td>Day.js
は日付と時刻を設定・検証・操作・表示する最小のモダンブラウザ向け
JavaScript ライブラリであり、 Moment.js の API
との広い互換性を持ちます。 Moment.js を使ったことがあればすぐにでも
Day.js を使い始めることができます。</td>
<td>MIT</td>
<td></td>
</tr>
<tr>
<td style="text-align: right;">12</td>
<td>Keycloak.js</td>
<td>24.0.1</td>
<td>Keycloak.jsはJavaScriptのライブラリであり、Keycloak認証サーバと連結するために使われる。JavaScriptアプリケーションを保護する簡単な方法を提供し、認証されたユーザーだけが特定のリソースや機能にアクセスできるようにします。Keycloakはオープンソースのアイデンティティ・アクセス管理ソフトウェアであり、ユーザーに対する認証・認可機能をアプリケーションに提供する。</td>
<td>MIT</td>
<td></td>
</tr>
<tr>
<td style="text-align: right;">13</td>
<td>Keycloak</td>
<td>24.0.1</td>
<td>Keycloakは、認証サービスと認証サービスを提供するオープンソースのJavaアプリケーションです。Java
Enterprise Edition（Java EE）テクノロジーを使用して、OAuth 2.0やOpenID
Connectなど、さまざまな認証方法をサポートしています。</td>
<td>MIT</td>
<td></td>
</tr>
<tr>
<td style="text-align: right;">14</td>
<td>@azure/identity</td>
<td>3.4.2</td>
<td>Azure Identity
ライブラリは、便利なトークン認証実装セットを通じて、Microsoft Entra ID
(以前の Azure Active Directory)のトークン認証機能を提供する。</td>
<td>MIT</td>
<td></td>
</tr>
<tr>
<td style="text-align: right;">15</td>
<td>@azure/service-bus</td>
<td>7.9.5</td>
<td><p>アプリケーションのクライアント ライブラリ @azure/service-bus
を使用して、以下の機能を提供する。</p>
<p>・Azure Service Bus キューまたはトピックにメッセージを送信する</p>
<p>・Azure Service Bus
キューまたはサブスクリプションからメッセージを受信する</p>
<p>・Azure Service Bus
名前空間でキュー/トピック/サブスクリプション/ルールを作成/取得/削除/更新/一覧表示する</p></td>
<td>MIT</td>
<td></td>
</tr>
<tr>
<td style="text-align: right;">16</td>
<td>uuid</td>
<td>11.1.0</td>
<td>一意の識別子を生成する機能を提供する。</td>
<td>MIT</td>
<td></td>
</tr>
</tbody>
</table>

## 制限事項

プロジェクト実行中に考慮すべき制限事項は以下の通りである：

- 顧客毎に１～２名のユーザー（顧客管理者）がポータルに対してアクセスし、それぞれ１0回／日の頻度で３０分～１時間程度の接続時間、軽量のポータルサイトを想定する。

## 障害時の動作

障害やエラーが発生した場合、ユーザーに適切な情報を提供し、問題の特定と解決をスムーズに行うための機能を備えている。

### エラーページと情報

ポータルシステムはエラーや障害が発生した場合、カスタムのエラーページを表示する。このエラーページには、エラーメッセージと問題の解決方法が含まれており、ユーザーが何か問題で、どのように対処できるかが明確に説明されている。

### エラーステータスコード

ポータルシステムはHTTPエラーの正確なステータスコード（例: 500 Internal
Server
Error）を返す。これにより、開発者や管理者は問題を迅速に特定し、適切な措置を講じることができる。

### エラーログ記録

ポータルシステムはエラーメッセージを正確にログファイルに記録する。管理者や開発者はこれらのログを確認し、問題を診断することができる。ポータルで出力したログは、ユーザー非公開とする。サービスの運用者は必要に応じて、ログファイルを直接参照または収集・監視ツールなどを使用してポータルのエラー検知および調査を行う。

Azure App
Serviceはアプリケーションのログ記録との機能が提供している。Azure portal
でクォータ (MB)とリテンション期間 (日)が設定できる。

ログの保存位置は以下のテーブルを参照する。

| ログのタイプ | ディレクトリ | 説明 |
|----|----|----|
| アプリケーション ログ | /LogFiles/YYYY_MM_DD_lw1sdlwk0001WH_default_docker.log | 1 つ以上のテキスト ファイルが含まれる。 |

[App Service ファイル
システムに格納されているログ](https://learn.microsoft.com/ja-jp/azure/app-service/troubleshoot-diagnostic-logs)の場合に最も簡単な方法は、次の場所にある
ZIP ファイルをブラウザでダウンロードすることである。

- Linux: https://\<app-name\>.scm.azurewebsites.net/api/logs/docker/zip

### エラーリスト(portal側)

<table>
<colgroup>
<col style="width: 6%" />
<col style="width: 23%" />
<col style="width: 32%" />
<col style="width: 36%" />
</colgroup>
<thead>
<tr>
<th style="text-align: center;">項番</th>
<th style="text-align: center;">エラーコード</th>
<th style="text-align: center;">エラーメッセージ</th>
<th style="text-align: center;">要因</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align: right;">1</td>
<td>EMEC0002</td>
<td>確認用パスワードが正しくありません。</td>
<td>パスワード変更の時、確認用パスワードが一致しない。</td>
</tr>
<tr>
<td style="text-align: right;">2</td>
<td>EMEC0003</td>
<td>現在のパスワードが正しくありません。</td>
<td><p>パスワード変更の時、現在のパスワードが入力範囲外、または入力不可の文字を含めている。</p>
<p>入力範囲：文字数が8文字以上、128文字以下</p></td>
</tr>
<tr>
<td style="text-align: right;">3</td>
<td>EMEC0005</td>
<td>現在ポータルは利用できません。</td>
<td>環境無効化フラグが有効である。</td>
</tr>
<tr>
<td style="text-align: right;">4</td>
<td>EMEC0006</td>
<td>データベースに一時的に接続できません。しばらくしてから再度ポータルにアクセスしてください。</td>
<td>データベースに接続できない。</td>
</tr>
<tr>
<td style="text-align: right;">5</td>
<td>EMEC0007</td>
<td>サーバに一時的に接続できません。しばらくしてから再度ポータルにアクセスしてください。</td>
<td>サーバは一時的にAzure BlobまたはKeycloakとの接続ができない。</td>
</tr>
<tr>
<td style="text-align: right;">6</td>
<td>EMEC0008</td>
<td>パスワードを変更しました。</td>
<td>パスワードが成功に変更された。</td>
</tr>
<tr>
<td style="text-align: right;">7</td>
<td>EMEC0009</td>
<td>新しいパスワードは8文字以上、128文字以下のパスワードを入力してください。</td>
<td>新しいパスワードの文字列が8文字以下、128文字以上である。</td>
</tr>
<tr>
<td style="text-align: right;">8</td>
<td>EMEC0010</td>
<td>新しいパスワードには2種類以上の文字の組み合わせを入力してください。</td>
<td>新しいパスワードの文字列が1種類だけ組み合わせる。</td>
</tr>
<tr>
<td style="text-align: right;">9</td>
<td>EMEC0011</td>
<td>新しいパスワードにはユーザーIDと異なる文字列を入力してください。</td>
<td>新しいパスワードの文字列がユーザーIDと同じである。</td>
</tr>
<tr>
<td style="text-align: right;">10</td>
<td>EMEC0012</td>
<td>新しいパスワードには現在のパスワードと異なる文字列を入力してください。</td>
<td>新しいパスワードの文字列が現在のパスワードと同じである。</td>
</tr>
<tr>
<td style="text-align: right;">11</td>
<td>EMEC0013</td>
<td>パスワード変更に失敗しました。</td>
<td>パスワード変更に失敗した。</td>
</tr>
<tr>
<td rowspan="2" style="text-align: right;">12</td>
<td rowspan="2">EMEC0014</td>
<td rowspan="2">ワンタイムコードが正しくありません。</td>
<td>①ワンタイムパスワードには数字以外の内容が含まれている。</td>
</tr>
<tr>
<td>②ワンタイムパスワードの文字数は6桁ではない。</td>
</tr>
<tr>
<td rowspan="2" style="text-align: right;">13</td>
<td rowspan="2">EMEC0015</td>
<td
rowspan="2">現在のパスワードまたはワンタイムコードが正しくありません。</td>
<td>①ユーザーIDと現在のパスワードが正しい組み合わせではない</td>
</tr>
<tr>
<td>②入力したワンタイムコードとモバイル認証アプリケーションに表示されているワンタイムコードが不一致</td>
</tr>
<tr>
<td style="text-align: right;">14</td>
<td>EMEC0016</td>
<td>{0}を指定してください。</td>
<td><p>必須入力項目が指定されていない。</p>
<p>※{0}：入力項目</p></td>
</tr>
<tr>
<td style="text-align: right;">15</td>
<td>EMEC0017</td>
<td>無効なファイル形式です。CSVファイルを指定してください。</td>
<td>管理項目定義のインポート時、ユーザーがCSV形式以外のファイルを選択した。</td>
</tr>
<tr>
<td style="text-align: right;">16</td>
<td>EMEC0018</td>
<td>ファイルのアップロードに失敗しました。時間をおいてから再度実行してください。</td>
<td>管理項目定義のインポートのファイルアップロード処理中に、ネットワークまたはサーバ側でエラーが発生した。</td>
</tr>
<tr>
<td style="text-align: right;">17</td>
<td>EMEC0019</td>
<td>サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0019)</td>
<td><p>ポータルバックエンド (Next.js API Routes)
が、ユーザーのタスク実行要求またはタスク中止要求をメッセージキュー
(Azure Service Bus) へ正常に送信できなかった。</p>
<p>※{0}：開始/中止</p></td>
</tr>
<tr>
<td style="text-align: right;">18</td>
<td>EMEC0020</td>
<td>{0} 日を超える期間が指定されました。{0}
日以内の期間を指定して再度実行してください。</td>
<td><p>操作ログのエクスポート時、ユーザーが指定した期間が、値の一覧で設定された最大日数を超過した。</p>
<p>※{0}：設定された最大日数</p></td>
</tr>
<tr>
<td style="text-align: right;">19</td>
<td>EMEC0021</td>
<td>サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)</td>
<td><p>ポータルがタスクの実行発行前または中止発行前のパラメータ検証か状態確認処理（例：Next.js
API Routesによるデータベースへの問い合わせ）に失敗した。</p>
<p>※{0}：開始/中止</p></td>
</tr>
<tr>
<td style="text-align: right;">20</td>
<td>EMEC0022</td>
<td><p>{0}に対するタスクを実行中のため実行できません。</p>
<p>実行中のタスクが完了してから再度実行してください。</p></td>
<td><p>対象サーバが現在他のタスクで使用されている。</p>
<p>※{0}：対象サーバ名</p></td>
</tr>
<tr>
<td style="text-align: right;">21</td>
<td>EMEC0023</td>
<td>タスクの実行がすでに開始したため中止できませんでした。</td>
<td>ユーザーがQUEUEDステータスのタスクに対し中止操作を行ったが、対象タスクのステータスがすでにRUNBOOK_SUBMITTED、RUNBOOK_PROCESSING、COMPLETED_SUCCESS、COMPLETED_ERRORになった。</td>
</tr>
<tr>
<td style="text-align: right;">22</td>
<td>EMEC0024</td>
<td>終了日は開始日以降の日付を指定してください。</td>
<td>操作ログのエクスポートタスクを選択した時、終了日に開始日よりも前の日付を指定した。</td>
</tr>
<tr>
<td style="text-align: right;">23</td>
<td>EMEC0025</td>
<td><p>タスクの実行を受け付けました。実行状態はタスク一覧でご確認ください。</p>
<p>対象サーバ：{0}</p>
<p>タスク種別：{1}</p></td>
<td><p>タスクの実行要求の受け付けが完了した。</p>
<p>※{0}：対象サーバ名</p>
<p>{1}：タスク種別名</p></td>
</tr>
<tr>
<td style="text-align: right;">24</td>
<td>EMEC0026</td>
<td><p>タスクの中止を受け付けました。最新の状態はタスク一覧をご確認ください。</p>
<p>タスク名：{0}</p></td>
<td><p>タスクの中止要求の受け付けが完了した。</p>
<p>※{0}：タスク名</p></td>
</tr>
<tr>
<td style="text-align: right;">25</td>
<td>EMEC0027</td>
<td>サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0027)</td>
<td><p>ポータルがタスクの実行発行中または中止発行中に未分類の内部エラーを検出した。</p>
<p>※{0}：開始/中止</p></td>
</tr>
</tbody>
</table>

### エラーリスト(KEYCLOAK側)

| 項番 | エラーコード | エラーメッセージ | 要因 |
|----|----|----|----|
| 1 | 　 | 無効なユーザー名またはパスワードです。 | ①ログインの時、入力されたパスワードがデータベースのパスワードと一致しない。 |
|  |  |  | ②ログインの時、入力されたユーザーIDはデータベースに存在しない。 |
|  |  |  | ③ユーザーがロックされている状態で再ログインする。 |

### エラーリスト(タスク詳細)

<table>
<colgroup>
<col style="width: 6%" />
<col style="width: 23%" />
<col style="width: 31%" />
<col style="width: 38%" />
</colgroup>
<thead>
<tr>
<th style="text-align: center;">項番</th>
<th style="text-align: center;">エラーコード</th>
<th style="text-align: center;">エラーメッセージ</th>
<th style="text-align: center;">要因</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align: right;">1</td>
<td>EMET0001</td>
<td><p>{0}に対するタスクを実行中のため実行できません。</p>
<p>実行中のタスクが完了してから再度実行してください。</p></td>
<td><p>指定されたコンテナが他のタスクを実行中であることをシステムが検出した。データの一貫性を保証するため、新しいタスク要求は一時的に処理できない。
(「コンテナ実行状態」テーブルのstatus が BUSY の場合)</p>
<p>※{0}：対象サーバ名</p></td>
</tr>
<tr>
<td style="text-align: right;">2</td>
<td>EMET0002</td>
<td>タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0002)</td>
<td>タスク実行中に一時ファイルや作業データを格納するためのAzure
Filesストレージ領域へのアクセスに失敗した（例：ファイルの作成、読み取り、書き込み、または削除時に問題が発生した）。</td>
</tr>
<tr>
<td style="text-align: right;">3</td>
<td>EMET0003</td>
<td>タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0003)</td>
<td>タスク実行中にAzure Blob
Storageへのアクセスに失敗した（例：ファイルのアップロード、取得、または削除時に問題が発生した）。</td>
</tr>
<tr>
<td style="text-align: right;">4</td>
<td>EMET0004</td>
<td>ユーザーによってタスクが中止されました。</td>
<td>以前に送信されたタスクは正常に中止された。</td>
</tr>
<tr>
<td style="text-align: right;">5</td>
<td>EMET0005</td>
<td>タスクの完了が確認できないため、システムによってタスクを中止しました。タスクが操作ログのエクスポートの場合、エクスポートするログの期間を短くして再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMET0005)</td>
<td><p>Azure
Functionsのタスク実行関数TaskExecuteFunc、Runbookジョブ処理関数RunbookProcessorFuncの実行時間が設定された最大実行許容時間（FUNCTION_TIMEOUT_SECONDS）を超えてもまだ完了していない。</p>
<p>またはRunbookジョブの実行時間が設定された最大実行許容時間（RUNBOOK_TIMEOUT_SECONDS）を超えてもまだ完了していない。</p></td>
</tr>
<tr>
<td style="text-align: right;">6</td>
<td>EMET0006</td>
<td>タスクの中止がタイムアウトしました。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMET0006)</td>
<td>Azure
Functionsのタスク中止関数TaskCancellationFuncの実行時間が設定された最大実行許容時間（FUNCTION_TIMEOUT_SECONDS）を超えてもまだ完了していない。</td>
</tr>
<tr>
<td rowspan="2" style="text-align: right;">7</td>
<td rowspan="2">EMET0007</td>
<td
rowspan="2">タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0007)</td>
<td
rowspan="2">タスク関連のデータベース操作（例：タスクステータスの記録、データの更新）を実行中の時にAzure
SQL Databaseとの対話に失敗した。</td>
</tr>
<tr>
</tr>
<tr>
<td style="text-align: right;">8</td>
<td>EMET0008</td>
<td>タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0008)</td>
<td>システムがタスクの実行中に未分類の内部エラーを検出した。</td>
</tr>
<tr>
<td style="text-align: right;">9</td>
<td>EMET0009</td>
<td>タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)</td>
<td>タスク実行の準備中に、必要なパラメータか設定情報（例：ターゲットVM名またはコンテナ名）を取得できなかった。</td>
</tr>
<tr>
<td style="text-align: right;">10</td>
<td>EMET0010</td>
<td>システムによりタスクの実行を中止しました。タスクの実行中にサービスのメンテナンスが開始された可能性があります。サービスのメンテナンス終了後に再度実行してください。</td>
<td>エンドポイント管理のサービスメンテナンスによるタスクの強制中止、または、緊急の障害対応によりサービス運用者がタスクを強制的に中止した。</td>
</tr>
<tr>
<td style="text-align: right;">11</td>
<td>EMET0011</td>
<td>タスクの実行がすでに開始したため中止できませんでした。</td>
<td>ユーザーがQUEUEDステータスのタスクに対し中止操作を行ったが、対象タスクのステータスがすでにRUNBOOK_SUBMITTED、RUNBOOK_PROCESSING、COMPLETED_SUCCESS、COMPLETED_ERRORになった。</td>
</tr>
<tr>
<td style="text-align: right;">12</td>
<td>EMET0012</td>
<td><p>タスクの実行に失敗しました。</p>
<p>エラー詳細：</p>
<p>{0}</p></td>
<td><p>基盤スクリプトでエラーが発生した。</p>
<p>発生し得るエラーは以下のとおり。詳細は基盤スクリプトの機能設計書を参照。</p>
<p>・業務エラー(インポートcsvフォーマットエラー)</p>
<p>・内部エラー(パラメータ不正、ファイルアクセスエラー、メモリ不足、DBアクセスエラー、JP1/ITDM2-Managerサービス停止)</p>
<p>※{0}：基盤スクリプトのエラーメッセージ。エラーメッセージファイル(errordetail.txt)の内容を表示する。</p></td>
</tr>
<tr>
<td style="text-align: right;">13</td>
<td>EMET0013</td>
<td>タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0013)</td>
<td>Runbookジョブの起動でエラーが発生し、基盤スクリプトが起動されなかった。</td>
</tr>
<tr>
<td style="text-align: right;">14</td>
<td>EMET0014</td>
<td>タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0014)</td>
<td>Azure Automation
API（Runbookジョブの作成、取得、ストップ）の呼び出しに失敗した。</td>
</tr>
</tbody>
</table>

# 付録

## 環境変数

<table>
<colgroup>
<col style="width: 5%" />
<col style="width: 18%" />
<col style="width: 20%" />
<col style="width: 55%" />
</colgroup>
<thead>
<tr>
<th style="text-align: right;">#</th>
<th>物理名</th>
<th>デフォルト値</th>
<th>備考</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align: right;">1</td>
<td>APP_CACHE_TTL_SECONDS</td>
<td>7200</td>
<td>キャッシュを再検証するまでの秒数。</td>
</tr>
<tr>
<td style="text-align: right;">2</td>
<td>AZURE_STORAGE_CONNECTION_STRING</td>
<td>-</td>
<td><p>アクセス キーを含む接続文字列です。アクセス
キーは、このストレージ
アカウントに対するアプリケーションの要求を認証します。</p>
<p>ポータルサービスをデプロイする時に、該当環境変数を設定する。【変更不可】</p>
<p>下記のような値を参照してください。</p>
<p>【DefaultEndpointsProtocol=https;AccountName=jp1ph2blob;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net】</p></td>
</tr>
<tr>
<td style="text-align: right;">3</td>
<td>LOG_LEVEL</td>
<td>info</td>
<td><p>①error</p>
<p>②info</p>
<p>③debug</p>
<p>レベルがこのレベル以下の場合にのみログに記録されます。</p></td>
</tr>
<tr>
<td style="text-align: right;">4</td>
<td>MSSQL_PRISMA_URL</td>
<td>-</td>
<td><p>Sqlサーバ接続のリンク文字列</p>
<p>ポータルサービスをデプロイする時に、該当環境変数を設定する。【変更不可】</p>
<p>下記のような値を参照してください。</p>
<p>【sqlserver://jp1ph2.database.windows.net:1433;database=jp1ph2;user=dlhzxt@jp1ph2;password=************;encrypt=true;trustServerCertificate=false;hostNameInCertificate=*.database.windows.net;loginTimeout=30;】</p></td>
</tr>
<tr>
<td style="text-align: right;">5</td>
<td>SCM_DO_BUILD_DURING_DEPLOYMENT</td>
<td>true</td>
<td><p>展開プロセスでは、プッシュする .zip
ファイルにはすぐに実行できる状態のアプリが含まれるものと想定されています。
既定では、カスタマイズは実行されません。
継続的インテグレーションで取得するものと同じビルド
プロセスを有効にするには、アプリケーションの設定に以下を追加します。【変更不可】</p>
<p>SCM_DO_BUILD_DURING_DEPLOYMENT=true</p>
<p>.zip プッシュ展開を使うときのこの設定の既定値は false です。
継続的インテグレーション展開の場合の既定値は true です。 true
に設定すると、展開の間にユーザーの展開関連の設定が使われます。
これらの設定は、アプリ設定として、または .zip ファイルのルートに存在する
.deployment 構成ファイルで、構成できます。</p></td>
</tr>
<tr>
<td style="text-align: right;">6</td>
<td>KEYCLOAK_REALM</td>
<td></td>
<td><p>「Realm」は、独立した認証ドメインと認証ドメインを表すトップクラスの管理ユニットです。各Realmは、独自のユーザー、役割、クライアント（アプリケーション）、グループ、および認証ポリシーを持つ自己完結型です</p>
<p>生産環境の実際の配置に基づいて記入して、現在しばらく確定することができません</p></td>
</tr>
<tr>
<td style="text-align: right;">7</td>
<td>KEYCLOAK_CLIENT</td>
<td></td>
<td><p>「Client」とは、Keycloak
Realmに登録されているアプリケーションまたはサービスであり、Keycloakを介したユーザー認証と承認が必要です。</p>
<p>生産環境の実際の配置に基づいて記入して、現在しばらく確定することができません</p></td>
</tr>
<tr>
<td style="text-align: right;">8</td>
<td>KEYCLOAK_REDIRECT_URL</td>
<td></td>
<td><p>ユーザーが認証プロセスを完了したら、Keycloakはユーザーをクライアント・アプリケーションのURLにリダイレクトする必要があります。</p>
<p>生産環境の実際の配置に基づいて記入して、現在しばらく確定することができません</p></td>
</tr>
<tr>
<td style="text-align: right;">9</td>
<td>KEYCLOAK_CLIENT_SECRET</td>
<td></td>
<td><p>CLIENT_SECRETは、クライアント・アプリケーションのアイデンティティを検証するためのセキュリティ資格証明です。これは、クライアントとKeycloak間の通信を保護するために、クライアントがKeycloakに登録する際に生成する鍵です。</p>
<p>生産環境の実際の配置に基づいて記入して、現在しばらく確定することができません</p></td>
</tr>
<tr>
<td style="text-align: right;">10</td>
<td>KEYCLOAK_INTERNAL_DOMAIN_NAME</td>
<td></td>
<td><p>KeyCloakサーバのAzure内部URL。</p>
<p>ポータルプログラムがKeyCloakサーバに通信する(Azure内通信する)ときに当該環境変数のURLを使用する。</p></td>
</tr>
<tr>
<td style="text-align: right;">11</td>
<td>KEYCLOAK_PUBLIC_DOMAIN_NAME</td>
<td></td>
<td><p>KeyCloakサーバの外部公開URL。</p>
<p>ポータルプログラムがKeyCloakサーバへのリダイレクトURLを設定するときに当該環境変数のURLを使用する。</p></td>
</tr>
<tr>
<td style="text-align: right;">12</td>
<td>JWT_MAX_AGE_SECONDS</td>
<td>1800</td>
<td>アイドル セッションが期限切れになり、有効でなくなるまでの時間。</td>
</tr>
<tr>
<td style="text-align: right;">13</td>
<td>AZURE_SERVICEBUS_CONNECTION_STRING</td>
<td></td>
<td>Azure Service Busへの接続文字列。Next.js API RoutesおよびAzure
FunctionsがService Busキュー（TaskInputQueue, TaskControlQueue,
RunbookStatusQueue等）へのメッセージ送受信に使用する。【セキュリティ保護対象、デプロイ時に設定、変更不可】</td>
</tr>
<tr>
<td style="text-align: right;">14</td>
<td>AZURE_AUTOMATION_ACCOUNT_NAME</td>
<td></td>
<td>利用するAzure
Automationアカウント名。TaskExecuteFuncが特定のRunbookを起動する際に、対象のアカウントを指定するために使用する。【デプロイ時に設定】</td>
</tr>
<tr>
<td style="text-align: right;">15</td>
<td>RUNBOOK_TIMEOUT_SECONDS</td>
<td>18000</td>
<td>Azure
AutomationのRunbookジョブの最大実行許容時間（秒）。Runbookジョブ監視関数RunbookMonitorFuncがRunbookジョブの実行時間を監視し、この時間になってもRunbookジョブがまだ終了していない場合はタイムアウトになり、RunbookMonitorFuncはAzure
AutomationにRunbookジョブをストップしてもらう。</td>
</tr>
<tr>
<td style="text-align: right;">16</td>
<td>FUNCTION_TIMEOUT_SECONDS</td>
<td>300</td>
<td>Azure Functionsの関数（特にTaskExecuteFunc,
RunbookProcessorFunc等の時間がかかる可能性のある関数）の最大実行許容時間（秒）。Azure
Functionsが関数の実行時間を監視し、この時間になっても関数がまだ終了していない場合はタイムアウトになり、Azure
Functionsによって強制的に中止される。</td>
</tr>
<tr>
<td style="text-align: right;">17</td>
<td>RUNBOOK_MONITOR_INTERVAL_SECONDS</td>
<td>300</td>
<td>Runbookジョブ監視関数RunbookMonitorFuncの起動トリガーとなるタイマーの時間間隔（秒）。</td>
</tr>
<tr>
<td style="text-align: right;">18</td>
<td>AZURE_FUNCTIONS_APP_NAME</td>
<td></td>
<td>Azure
Functionsの関数アプリ名。【デプロイ時に設定、必要に応じて利用】</td>
</tr>
<tr>
<td style="text-align: right;">19</td>
<td>AZURE_SQLDB_CONNECTION_STRING_REF</td>
<td></td>
<td>Azure SQL Databaseへの接続文字列（またはKey
Vault等のシークレット参照名）。Azure Functions (TaskExecuteFunc,
TaskCancellationFunc, RunbookProcessorFunc) およびNext.js API
Routesがデータベースアクセスに使用する。【セキュリティ保護対象、デプロイ時に設定】</td>
</tr>
<tr>
<td style="text-align: right;">20</td>
<td>AZURE_STORAGE_CONTAINER_OPLOGS</td>
<td>oplogs</td>
<td>操作ログファイルを保存するAzure Blob Storageのコンテナ名</td>
</tr>
<tr>
<td style="text-align: right;">21</td>
<td>AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF</td>
<td>assetsfield-def</td>
<td>管理項目定義ファイルを保存するAzure Blob Storageのコンテナ名</td>
</tr>
<tr>
<td style="text-align: right;">22</td>
<td>SERVICE_BUS_TASK_INPUT_QUEUE_NAME</td>
<td></td>
<td>Azure Service BusのTaskInputQueueの名前</td>
</tr>
<tr>
<td style="text-align: right;">23</td>
<td>SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME</td>
<td></td>
<td>Azure Service BusのRunbookStatusQueueの名前</td>
</tr>
<tr>
<td style="text-align: right;">24</td>
<td>SERVICE_BUS_TASK_CONTROL_QUEUE_NAME</td>
<td></td>
<td>Azure Service BusのTaskControlQueueの名前</td>
</tr>
</tbody>
</table>

# 補足説明

## KEYCLOAKにおけるユーザー作成

### KEYCLOAKのユーザー管理画面開く

<img src="media/image29.png" style="width:6.33958in;height:3.21944in" />

### ユーザーの追加ボタンをクリックして、追加ユーザー画面を開きます

必要なユーザー情報を記入し、必要なユーザー・アクションの値をConfigure
OTPに選択して作成ボタンをクリックします

<img src="media/image30.png" style="width:6.33958in;height:3.20278in" />

### ユーザーが作成に成功したら、ユーザーのパスワードを設定します

<img src="media/image31.png" style="width:6.33958in;height:3.21319in" />

> \(1\)

<img src="media/image32.png" style="width:6.33958in;height:3.22778in" />

\(2\)

<img src="media/image33.png" style="width:6.33958in;height:3.22083in" />

> \(3\)

<img src="media/image34.png" style="width:6.33958in;height:3.20139in" />

\(4\)

－以上－
