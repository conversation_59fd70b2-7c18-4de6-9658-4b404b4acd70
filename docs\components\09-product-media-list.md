# 组件：产品媒体列表 (Product Media List)

## 1. 概要 (Overview)

### 1.1 目的 (Purpose)
本组件旨在为“JCS 端点资产与任务管理系统”的已登录用户提供一个集中的界面，用以浏览和下载其有权访问的产品安装媒体及其相关的配套文档（如版本说明、手册等）。用户可以通过此功能获取所需产品的安装包和参考资料。

### 1.2 用户故事/需求 (User Stories/Requirements)
- 作为一名顾客系统管理员，我希望能够查看一个清晰的产品媒体列表，其中包含我当前契约下可用的所有产品及其不同版本和操作系统平台的安装包。
- 作为一名顾客系统管理员，我希望能看到每个产品媒体的详细信息，如产品名称、型号、版本号、支持的操作系统、发布日期以及文件大小。
- 作为一名顾客系统管理员，我希望能方便地从列表中下载所需的产品安装媒体文件。
- 作为一名顾客系统管理员，我还希望能下载与产品媒体相关的配套文档（如版本说明、安装手册），以便更好地理解和使用产品。
- 作为一名顾客系统管理员，我希望能根据产品名称、型号、版本或操作系统等关键字快速筛选和查找我需要的产品媒体。

### 1.3 高层依赖与交互 (High-Level Dependencies & Interactions)
- **用户认证与授权**: 用户的身份（特别是关联的契约ID或许可的特定产品）决定了其能在列表中看到哪些产品媒体。
- **主界面 (Main Screen)**: 本组件的入口点是[主界面](./02-main-screen.md)侧边栏的“製品媒体一覧 (产品媒体列表)”菜单项。
- **数据存储**:
    - 产品媒体的元数据（产品名、型号、版本、OS、发布日期、文件名、大小、关联文档的文件名和大小等）存储在Azure SQL Database的 `ProductMedia` 表中。
    - 用户可访问的产品范围可能通过 `PlanProduct` 表（关联契约与产品）进行控制。
- **文件存储与下载**:
    - 产品媒体文件和相关文档文件实际存储在Azure Blob Storage的特定容器（如 `product-medias`）中，并按产品型号、版本等规则组织。
    - 用户下载文件时，前端通过后端API获取一个安全的、有时限的共享访问签名 (SAS) 链接指向Blob Storage中的文件。
- **后端API (Next.js API Routes)**: 前端通过调用后端API来获取用户可见的产品媒体列表和生成文件下载链接。

## 2. 功能规格 (Functional Specifications)

### 2.1 主要流程 (High-Level Flow)
1.  用户成功登录系统后，通过侧边导航栏访问“製品媒体一覧 (产品媒体列表)”菜单项。
2.  系统加载并向用户展示其有权访问的产品媒体列表，每条记录包含产品名称、型号、版本、操作系统、发布日期、产品媒体（下载链接和大小）、相关文档（下载链接和大小）等信息。
3.  用户可以使用界面提供的筛选框输入关键字，点击搜索或按回车后，列表将根据匹配结果刷新。
4.  用户可以点击列表表头对产品媒体信息进行排序。
5.  用户可以点击“产品媒体”列或“相关文档”列中的文件名链接来下载对应的文件。
    ```mermaid
    sequenceDiagram
        participant User as 👤 用户 (Browser)
        participant ProductMediaListScreen as 📦 产品媒体列表界面
        participant NextJsApiRoutes as 🌐 Next.js API Routes
        participant ProductMediaDB as 💾 ProductMedia/PlanProduct表 (SQL DB)
        participant AzureBlobStorage as ☁️ Azure Blob Storage

        User->>ProductMediaListScreen: 打开产品媒体列表
        ProductMediaListScreen->>NextJsApiRoutes: 请求产品媒体列表数据 (GET /api/product-media)
        NextJsApiRoutes->>ProductMediaDB: 查询用户可见的产品媒体元数据 (基于用户契约/权限)
        ProductMediaDB-->>NextJsApiRoutes: 返回产品媒体元数据列表
        NextJsApiRoutes-->>ProductMediaListScreen: 显示产品媒体列表

        alt 用户下载产品媒体文件
            User->>ProductMediaListScreen: 点击产品A的"产品媒体"下载链接
            ProductMediaListScreen->>NextJsApiRoutes: 请求产品A媒体文件的下载URL (GET /api/product-media/A_media/download-url)
            NextJsApiRoutes->>AzureBlobStorage: 为产品A媒体文件生成SAS Token
            AzureBlobStorage-->>NextJsApiRoutes: 返回带SAS Token的URL
            NextJsApiRoutes-->>ProductMediaListScreen: 返回安全的下载URL
            ProductMediaListScreen->>User: (浏览器)开始下载文件
        end
    ```
6.  用户可以使用分页控件浏览更多的产品媒体记录。
7.  用户可以点击界面右上角的“更新”图标按钮，手动刷新整个产品媒体列表。

### 2.2 业务规则 (Business Rules)
-   **信息来源与可见性**:
    *   产品媒体的元数据从门户数据库的 `ProductMedia` 表中获取。
    *   用户可见的产品媒体范围由其契约ID通过 `PlanProduct` 表（或类似机制）关联确定。用户只能查看和下载其有权访问的产品媒体。
-   **只读显示**: 列表中的所有产品媒体信息均为只读展示。
-   **文件存储路径**: 产品媒体文件和文档文件存储在Azure Blob Storage的 `product-medias` 容器下，可能按产品型号和版本分子目录组织（例如：`product-medias/{产品形名}/{版本}/{文件名}`）。
-   **下载机制**: 与操作日志下载类似，用户下载文件时，后端API为目标Blob生成一个具有读取权限且有时间限制（默认为2小时，可由`LOV`表 `AZURE_STORAGE.SAS_TTL_SECONDS` 配置）的共享访问签名 (SAS) Token。
-   **日期显示**: 所有日期（如发布日期）的显示应遵循用户浏览器的时区设置。
-   **默认排序** (依据 `fs.md` 对产品媒体列表的ソート规则):
    1.  主要排序键：“リリース日 (发布日期)”，降序（最新的在前）。
    2.  次要排序键：“製品名 (产品名称)”，字典序升序（A→Z, 0→9）。

### 2.3 用户界面概述 (User Interface Overview)

-   **界面草图/核心区域**: (参考 `fs.md` 图4.9.6 (1) 和 (2) 作为高层概念)
    1.  **筛选区**: 位于列表上方，提供文本输入框进行关键字筛选，以及搜索和清除按钮。
    2.  **产品媒体列表表格**:
        *   列：製品名 (产品名称)、製品形名 (产品型号)、バージョン (版本)、OS (操作系统)、リリース日 (发布日期)、製品媒体 (产品媒体文件名，兼作下载链接)、製品媒体のサイズ (产品媒体大小)、ドキュメント (相关文档文件名，兼作下载链接)、ドキュメントのサイズ (相关文档大小)。
        *   每行代表一个可用的产品媒体项。
    3.  **分页控件**: 位于列表下方。
    4.  **更新按钮**: 通常位于列表右上角。
-   **主要交互点**:
    *   用户在筛选框中输入文本，进行列表筛选。
    *   用户点击可排序的列标题，进行升序/降序排序。
    *   用户点击“製品媒体”列或“ドキュメント”列中的文件名链接，浏览器开始下载对应文件。
    *   用户使用分页控件浏览更多记录。
    *   用户点击“更新”按钮，重新加载列表。
-   **画面項目 (Screen Items - High Level)**:
    *   输入：筛选关键字。
    *   显示：产品名称、型号、版本、操作系统、发布日期、产品媒体文件名与大小、相关文档文件名与大小。
    *   用户可操作：筛选、排序、分页、下载文件。
-   **Tab键顺序** (依据 `fs.md` 描述，强调文件链接的顺序):
    *   Tab键焦点在列表区域时，应优先遍历当前页可见的“产品媒体”下载链接，然后是对应的“文档”下载链接，按行顺序进行。例如：第一行的产品媒体链接 -> 第一行的文档链接 -> 第二行的产品媒体链接 -> 第二行的文档链接 -> ...

### 2.4 前提条件 (Preconditions)
-   用户必须已通过[登录功能](./01-login.md)完成身份验证，并拥有有效的活动会话。
-   系统管理员已在 `ProductMedia` 数据库表中正确录入了产品媒体的元数据，并通过 `PlanProduct` 表（或类似机制）配置了用户契约可访问的产品范围。
-   对应的产品媒体文件和文档文件已上传到Azure Blob Storage的 `product-medias` 容器中，并与数据库元数据中的路径一致。
-   后端用于获取产品媒体列表和生成下载链接的API端点必须可用。

### 2.5 制约事项 (Constraints/Limitations)
-   **浏览器兼容性**: 仅正式支持 Microsoft Edge 和 Google Chrome 浏览器。
-   **语言支持**: 界面显示语言仅支持日语。
-   **列表性能**: 如果用户有权访问的产品媒体数量非常庞大，列表的加载、筛选和分页性能可能会受到影响。
-   **文件大小**: `fs.md` 未明确单个文件大小上限，但超大文件（如数GB）的下载体验可能受用户网络环境影响。

### 2.6 注意事项 (Notes/Considerations)
-   产品媒体文件和文档的文件大小应以用户友好的单位显示（例如 KB, MB, GB）。
-   “发布日期”的格式应在日语环境下清晰易懂。
-   如果某个产品媒体没有关联的文档，则“ドキュメント”列应显示为空或特定提示（如“-”），且不提供下载链接。

### 2.7 错误处理概述 (Error Handling Overview)
-   **列表加载失败**: 如果后端API调用失败导致无法获取产品媒体列表，界面应向用户显示通用的错误提示信息（例如，“产品媒体列表加载失败，请稍后重试。”），并记录详细技术错误。
-   **文件下载失败**:
    *   如果请求下载链接时后端API出错，应提示用户“无法获取下载链接，请重试。”
    *   如果生成的下载链接无效（SAS Token过期、文件在Blob Storage中不存在或无权限），用户点击下载后浏览器层面会报错或下载失败。
-   **无权访问产品**: 如果用户因权限配置问题理论上不应看到任何产品媒体，列表应显示为空或特定提示（例如，“当前没有您可访问的产品媒体。”）。

### 2.8 相关功能参考 (Related Functional References)
*   **主要入口**: [主界面 (Main Screen)](./02-main-screen.md) - 侧边栏的“製品媒体一覧”菜单是访问本功能的途径。
*   **认证关联**: [登录 (Login)](./01-login.md) - 用户的登录身份（特别是契约ID）决定了其能看到的产品媒体范围。
*   **系统整体架构**: `../../architecture/system-architecture.md` - 描述了产品媒体数据如何从存储到呈现给用户的流程。
*   **核心数据模型**:
    *   `../../data-models/product-media-table.md` (假设文件名) - 定义了产品媒体元数据的主要来源表结构。
    *   `../../data-models/plan-product-table.md` (假设文件名) - 定义了用户契约与可访问产品之间的关联。
*   **配置参考**: `../../data-models/lov-table.md` (假设文件名) - 可能包含与SAS Token有效期 (`AZURE_STORAGE.SAS_TTL_SECONDS`) 等相关的配置。
*   **源功能规格**: `fs.md` (通常位于 `docs-delivery/機能仕様書/` 或项目指定的源文档位置) - 本组件功能规格的原始日文描述，特别是其“4.9 製品媒体一覧”章节。
