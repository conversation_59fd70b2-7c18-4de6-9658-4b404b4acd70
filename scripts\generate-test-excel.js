#!/usr/bin/env node

/**
 * テストケースExcel報告書生成スクリプト
 * 
 * 既存のテストケース抽取データからExcel形式のテスト報告書を生成する
 * 
 * 使用方法:
 *   npm run generate-test-excel
 *   または
 *   node scripts/generate-test-excel.js
 */

const fs = require('fs');
const path = require('path');
const ExcelJS = require('exceljs');

// プロジェクトディレクトリの定義
const PROJECTS = [
  {
    name: 'jcs-endpoint-nextjs',
    path: 'apps/jcs-endpoint-nextjs/__tests__',
    title: 'フロントエンドテスト報告（jcs-endpoint-nextjs）'
  },
  {
    name: 'jcs-backend-services-standard', 
    path: 'apps/jcs-backend-services-standard/__tests__',
    title: '標準バックエンドサービステスト報告（jcs-backend-services-standard）'
  },
  {
    name: 'jcs-backend-services-long-running',
    path: 'apps/jcs-backend-services-long-running/__tests__',
    title: '長時間実行バックエンドサービステスト報告（jcs-backend-services-long-running）'
  }
];

/**
 * ディレクトリを再帰的にスキャンしてテストファイルを取得
 */
function findTestFiles(dir) {
  const testFiles = [];
  
  if (!fs.existsSync(dir)) {
    console.warn(`ディレクトリが存在しません: ${dir}`);
    return testFiles;
  }
  
  function scanDirectory(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath);
      } else if (item.endsWith('.test.ts') || item.endsWith('.test.tsx')) {
        testFiles.push(fullPath);
      }
    }
  }
  
  scanDirectory(dir);
  return testFiles;
}

/**
 * テストファイルからテストケースを抽出
 */
function extractTestCases(filePath) {
  const content = fs.readFileSync(filePath, 'utf-8');
  const testCases = [];

  // まず、JSDocコメント付きのテストケースを抽出
  const patternWithComment = /\/\*\*([\s\S]*?)\*\/\s*(?:it|test)\s*\(\s*["'`]([^"'`]+)["'`]/g;
  const extractedTestNames = new Set();

  let match;
  while ((match = patternWithComment.exec(content)) !== null) {
    const commentContent = match[1];
    const testName = match[2];

    // 4つの必須項目を抽出
    const testPoint = extractField(commentContent, '試験観点');
    const testTarget = extractField(commentContent, '試験対象');
    const testProcedure = extractField(commentContent, '試験手順');
    const verificationItems = extractField(commentContent, '確認項目');

    testCases.push({
      name: testName,
      testPoint: testPoint || '',
      testTarget: testTarget || '',
      testProcedure: testProcedure || '',
      verificationItems: verificationItems || '',
      filePath: filePath
    });

    extractedTestNames.add(testName);
  }

  // 次に、コメントなしのテストケースを抽出
  const patternWithoutComment = /^\s*(?:it|test)\s*\(\s*["'`]([^"'`]+)["'`]/gm;

  while ((match = patternWithoutComment.exec(content)) !== null) {
    const testName = match[1];

    // 既に抽出済みでない場合のみ追加
    if (!extractedTestNames.has(testName)) {
      testCases.push({
        name: testName,
        testPoint: '',
        testTarget: '',
        testProcedure: '',
        verificationItems: '',
        filePath: filePath
      });
    }
  }

  return testCases;
}

/**
 * コメントブロックから特定のフィールドを抽出
 */
function extractField(commentContent, fieldName) {
  // フィールド名の後の内容を抽出する簡単なパターン
  const pattern = new RegExp(`${fieldName}[：:]\\s*([\\s\\S]*?)(?=\\n\\s*\\*\\s*(?:試験観点|試験対象|試験手順|確認項目|$))`);
  const match = commentContent.match(pattern);

  if (!match) return '';

  return match[1]
    .replace(/\n\s*\*/g, '\n')  // 行頭の * を削除
    .replace(/^\s+|\s+$/g, '')  // 前後の空白を削除
    .trim();
}

/**
 * Excelワークブックを作成
 */
async function createExcelWorkbook(allTestCases) {
  const workbook = new ExcelJS.Workbook();
  
  // メタデータ設定
  workbook.creator = 'JCS Endpoint Test Report Generator';
  workbook.lastModifiedBy = 'JCS Endpoint Test Report Generator';
  workbook.created = new Date();
  workbook.modified = new Date();

  // 概要シートを作成
  await createSummarySheet(workbook, allTestCases);

  // 各プロジェクトのシートを作成
  for (const project of allTestCases) {
    await createProjectSheet(workbook, project);
  }

  return workbook;
}

/**
 * 概要シートを作成
 */
async function createSummarySheet(workbook, allTestCases) {
  const worksheet = workbook.addWorksheet('テスト概要');
  
  // ヘッダー設定
  worksheet.mergeCells('A1:F1');
  worksheet.getCell('A1').value = 'JCS Endpoint システム テスト報告書';
  worksheet.getCell('A1').font = { size: 16, bold: true };
  worksheet.getCell('A1').alignment = { horizontal: 'center' };

  // 作成日
  worksheet.getCell('A3').value = '作成日:';
  worksheet.getCell('B3').value = new Date().toLocaleDateString('ja-JP');
  worksheet.getCell('A3').font = { bold: true };

  // 総テストケース数
  const totalTestCases = allTestCases.reduce((sum, project) => sum + project.testCases.length, 0);
  worksheet.getCell('A4').value = '総テストケース数:';
  worksheet.getCell('B4').value = totalTestCases;
  worksheet.getCell('A4').font = { bold: true };

  // テスト結果摘要テーブル
  worksheet.getCell('A6').value = 'テスト結果摘要';
  worksheet.getCell('A6').font = { size: 14, bold: true };

  // テーブルヘッダー
  worksheet.getCell('A8').value = 'プロジェクト';
  worksheet.getCell('B8').value = 'テストケース数';
  worksheet.getRow(8).font = { bold: true };
  worksheet.getRow(8).fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFE0E0E0' }
  };

  // データ行
  let currentRow = 9;
  allTestCases.forEach(project => {
    worksheet.getCell(`A${currentRow}`).value = project.title;
    worksheet.getCell(`B${currentRow}`).value = project.testCases.length;
    currentRow++;
  });

  // 合計行
  worksheet.getCell(`A${currentRow}`).value = '合計';
  worksheet.getCell(`B${currentRow}`).value = totalTestCases;
  worksheet.getRow(currentRow).font = { bold: true };
  worksheet.getRow(currentRow).fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFFFCC00' }
  };

  // 列幅調整
  worksheet.getColumn('A').width = 50;
  worksheet.getColumn('B').width = 15;

  // 境界線設定
  const tableRange = `A8:B${currentRow}`;
  worksheet.getCell(tableRange).border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };
}

/**
 * プロジェクトシートを作成
 */
async function createProjectSheet(workbook, project) {
  const worksheet = workbook.addWorksheet(project.name);
  
  // ヘッダー設定
  worksheet.mergeCells('A1:F1');
  worksheet.getCell('A1').value = project.title;
  worksheet.getCell('A1').font = { size: 14, bold: true };
  worksheet.getCell('A1').alignment = { horizontal: 'center' };

  if (project.testCases.length === 0) {
    worksheet.getCell('A3').value = 'このプロジェクトにはテストケースが見つかりませんでした。';
    return;
  }

  // テーブルヘッダー
  const headers = ['No.', 'テストケース名', '試験観点', '試験対象', '試験手順', '確認項目'];
  let currentRow = 3;
  
  headers.forEach((header, index) => {
    const cell = worksheet.getCell(currentRow, index + 1);
    cell.value = header;
    cell.font = { bold: true };
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };
    cell.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    };
  });

  // ファイル別にグループ化
  const fileGroups = {};
  project.testCases.forEach(testCase => {
    const fileName = path.basename(testCase.filePath, path.extname(testCase.filePath));
    if (!fileGroups[fileName]) {
      fileGroups[fileName] = [];
    }
    fileGroups[fileName].push(testCase);
  });

  let testCaseNumber = 1;
  currentRow = 4;

  // 各ファイルグループのテストケースを追加
  Object.entries(fileGroups).forEach(([fileName, testCases]) => {
    // ファイル名セクションヘッダー
    worksheet.mergeCells(`A${currentRow}:F${currentRow}`);
    const fileHeaderCell = worksheet.getCell(`A${currentRow}`);
    fileHeaderCell.value = `### ${fileName}`;
    fileHeaderCell.font = { bold: true, size: 12 };
    fileHeaderCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFF0F0F0' }
    };
    currentRow++;

    // テストケースデータ
    testCases.forEach(testCase => {
      const rowData = [
        testCaseNumber,
        testCase.name,
        testCase.testPoint,
        testCase.testTarget,
        testCase.testProcedure.replace(/<br>/g, '\n'),
        testCase.verificationItems.replace(/<br>/g, '\n')
      ];

      rowData.forEach((data, colIndex) => {
        const cell = worksheet.getCell(currentRow, colIndex + 1);
        cell.value = data;
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
        
        // テキストの折り返し設定
        if (colIndex >= 2) { // 試験観点以降の列
          cell.alignment = { 
            wrapText: true, 
            vertical: 'top',
            horizontal: 'left'
          };
        }
      });

      testCaseNumber++;
      currentRow++;
    });

    currentRow++; // ファイルグループ間のスペース
  });

  // 列幅調整
  worksheet.getColumn(1).width = 5;   // No.
  worksheet.getColumn(2).width = 40;  // テストケース名
  worksheet.getColumn(3).width = 25;  // 試験観点
  worksheet.getColumn(4).width = 30;  // 試験対象
  worksheet.getColumn(5).width = 50;  // 試験手順
  worksheet.getColumn(6).width = 30;  // 確認項目

  // 行の高さを自動調整
  worksheet.eachRow((row, rowNumber) => {
    if (rowNumber > 3) {
      row.height = undefined; // 自動調整
    }
  });
}

/**
 * メイン処理
 */
async function main() {
  console.log('🔍 テストケース抽出を開始します...\n');

  const allTestCases = [];
  let totalTestCases = 0;

  // 各プロジェクトのテストケースを抽出
  PROJECTS.forEach(project => {
    console.log(`📁 ${project.name} を処理中...`);

    const testFiles = findTestFiles(project.path);
    console.log(`   見つかったテストファイル: ${testFiles.length}個`);

    const projectTestCases = [];

    testFiles.forEach(filePath => {
      const testCases = extractTestCases(filePath);
      projectTestCases.push(...testCases);

      const fileName = path.basename(filePath);
      console.log(`   ${fileName}: ${testCases.length}個のテストケース`);
    });

    allTestCases.push({
      name: project.name,
      title: project.title,
      testCases: projectTestCases
    });

    totalTestCases += projectTestCases.length;
    console.log(`   ${project.name} 合計: ${projectTestCases.length}個\n`);
  });

  console.log(`📊 総テストケース数: ${totalTestCases}個\n`);

  // Excelワークブックを作成
  console.log('📝 Excel報告書を生成中...');
  const workbook = await createExcelWorkbook(allTestCases);

  // 出力ディレクトリを確保
  const outputDir = 'docs-delivery/unit-test-report';
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  // ファイルに保存
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
  const outputPath = path.join(outputDir, `JCS_テスト報告書_${timestamp}.xlsx`);
  
  await workbook.xlsx.writeFile(outputPath);

  console.log(`✅ Excel テスト報告書が生成されました: ${outputPath}`);
  console.log(`📈 抽出されたテストケース数: ${totalTestCases}個`);
  console.log(`📋 生成されたシート数: ${allTestCases.length + 1}個 (概要 + 各プロジェクト)`);
}

// スクリプト実行
if (require.main === module) {
  main().catch(error => {
    console.error('❌ エラーが発生しました:', error);
    process.exit(1);
  });
}

module.exports = {
  main,
  createExcelWorkbook,
  extractTestCases,
  findTestFiles
};