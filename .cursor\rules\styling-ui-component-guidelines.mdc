---
description: 
globs: 
alwaysApply: true
---
# Styling and UI Component Guidelines (for apps/jcs-endpoint-nextjs)

This document provides guidelines for styling, creating, and using UI components within the `apps/jcs-endpoint-nextjs` application. Consistency in UI and component structure is key for a maintainable and user-friendly application.

**Primary Tools & Configurations:**
*   **Styling Engine:** Tailwind CSS (configured in `tailwind.config.js`). Utility-first CSS is the primary approach.
*   **UI Widgets/Components:** Flowbite (integrated as a Tailwind CSS plugin, see `tailwind.config.js`). Use Flowbite's pre-built components and styles where appropriate.
*   **Global Styles:** `styles/globals.css` (for base styles, font imports, or global overrides not easily achievable with Tailwind utilities).
*   **Custom Tailwind Theme:** `tailwind.config.js` defines custom fonts (`fontFamily.body`), gradient backgrounds (e.g., `bg-gradient-blue`, `bg-gradient-header`), custom shadows (`boxShadow`, `dropShadow`), and z-index values. **MUST** utilize these theme extensions for consistency.
*   **Component Structure:** UI components are located in `app/ui/`.
*   **Iconography:** React Icons.

**Primary References:**
*   `tailwind.config.js` (for available custom utilities, colors, fonts, etc.).
*   `styles/globals.css` (for base global styles).
*   Existing components in `app/ui/` as examples of structure and styling.
*   Flowbite documentation (for usage of its components).
*   Tailwind CSS documentation.
*   Relevant component design documents in the Monorepo's `docs/components/` for specific UI requirements.

## Core Styling Principles

1.  **Utility-First with Tailwind CSS:** Prioritize using Tailwind CSS utility classes directly in JSX for styling. Avoid writing custom CSS in separate files unless absolutely necessary (e.g., complex animations, highly specific selectors not covered by Tailwind).
2.  **Leverage Theme Extensions:** **MUST** use the custom theme defined in `tailwind.config.js` for:
    *   **Fonts:** Apply `font-body` where appropriate.
    *   **Colors & Gradients:** Use predefined gradients like `bg-gradient-blue`, `bg-gradient-dark`, `bg-gradient-light`, `bg-gradient-header`. For other colors, use Tailwind's default color palette or define custom colors in the theme if widely needed.
    *   **Shadows:** Use custom shadows like `shadow-dark`, `drop-shadow-blue`.
3.  **Flowbite for Common UI Patterns:** For common UI elements like modals, tooltips, dropdowns, date pickers, etc., first check if a suitable Flowbite component/pattern exists and utilize it. Ensure Flowbite components are styled consistently with the project's Tailwind theme.
    *   Flowbite interactivity is often initialized via JavaScript (e.g., `initModals()`, `initCollapses()` as seen in `ui/header.tsx` and `ui/sidebar.tsx`).
4.  **Responsive Design:** Implement responsive design using Tailwind's responsive prefixes (e.g., `sm:`, `md:`, `lg:`, `xl:`). Ensure UIs are usable across common screen sizes.
5.  **Accessibility (A11y):** Strive for accessible UIs. Use semantic HTML, provide ARIA attributes where necessary, ensure keyboard navigability, and maintain sufficient color contrast.
6.  **Consistency:** Aim for visual consistency across the application. Reuse existing components and styling patterns.

## UI Component Development (`app/ui/`)

1.  **Location:**
    *   **General Reusable Components:** Place directly under `app/ui/` (e.g., `app/ui/breadcrumb.tsx`, `app/ui/pagination.tsx`, `app/ui/spinner.tsx`).
    *   **Feature-Specific Components:** Place in subdirectories named after the feature or data type they relate to (e.g., `app/ui/servers/table.tsx`, `app/ui/manuals/table.tsx`). This mirrors the structure seen for table components.
    *   Modals specific to a feature can also be in such subdirectories (e.g., if `PasswordModal` was only for a user profile page, it might be in `app/ui/profile/password-modal.tsx`).
2.  **Naming Convention:** PascalCase for component filenames and function/class names (e.g., `UserProfileCard.tsx`).
3.  **Props:** Define clear and typed (TypeScript) props for components. Use interfaces or types for prop definitions.
4.  **State Management:**
    *   For local component state, use React's `useState` and `useEffect`.
    *   For state shared across multiple components that are not direct parent/child, consider React Context or lifting state up. Global state solutions like Zustand/Redux are not explicitly in the tech stack for frontend state beyond SWR's caching.
5.  **Client Components:** Components requiring interactivity, lifecycle hooks (`useEffect`), or state (`useState`) **MUST** include the `"use client";` directive at the top of the file.
6.  **Server Components:** Components primarily for displaying data fetched on the server should be Server Components (default in App Router).
7.  **Modals (`flowbite`):**
    *   Modals are often defined as separate components (e.g., `app/ui/license-modal.tsx`).
    *   They are typically initialized and controlled (show/hide) via JavaScript from a parent component (e.g., `Header.tsx` controlling multiple modals).
    *   Ensure unique IDs for modal elements and their trigger/control mechanisms.
8.  **Tables:**
    *   Table structures often include a reusable `Thead` component (`app/ui/thead.tsx`) for sortable headers.
    *   Data is typically passed as props to table components, which then map over the data to render rows.

## Specific UI Patterns Observed

*   **Header (`app/ui/header.tsx`):** Contains user information, logout, password change, notification, and license modal triggers. Manages modal instances.
*   **Sidebar (`app/ui/sidebar.tsx`):** Collapsible navigation menu using `navLinks` from `app/lib/definitions.ts` and Flowbite for collapse behavior.
*   **Breadcrumbs (`app/ui/breadcrumb.tsx`):** Dynamically generated based on `pathname` and `navLinks`.
*   **Pagination & PageSize (`app/ui/pagination.tsx`, `app/ui/page-size.tsx`):** Common components for list views.
*   **Search/Filter (`app/ui/search.tsx`):** Component for filtering table data.
*   **Error Display:**
    *   Inline errors in forms (e.g., `LoginForm` in `login/page.tsx` or `PasswordModal`).
    *   Modal errors/messages (`MessageModal`).
    *   Page-level errors via `error.tsx` boundaries.

---

**NOTE TO CURSOR:**
1.  When creating or styling UI components, **MUST** prioritize Tailwind CSS utility classes and adhere to the custom theme defined in `tailwind.config.js`.
2.  Utilize Flowbite components for common UI patterns where appropriate and ensure they are styled consistently.
3.  Follow the existing component organization within `app/ui/`. Place general components at the root of `ui/` and feature-specific ones in subdirectories.
4.  Ensure client components that need state or effects have the `"use client";` directive.
5.  Always refer to existing components for established patterns before creating new ones.
6.  Consult `docs/components/` for detailed UI specifications for specific features, including layout, elements, and behavior.


---