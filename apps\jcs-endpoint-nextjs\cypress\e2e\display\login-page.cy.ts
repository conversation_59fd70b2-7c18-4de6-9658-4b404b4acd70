import fixture from "../../fixtures/Notification.json";

describe("初期化表示のテスト", () => {
  describe("ログイン画面", () => {
    beforeEach(() => {
      cy.visit("/login");
    });
    it("ログインフォームが正しく表示される", () => {
      cy.visit("/login");
      cy.get("h1").contains("JP1 Cloud Service");
      cy.get("h1").contains("エンドポイント管理");
      cy.get("#userId").should("be.empty");
      cy.get("#password").should("be.empty");
      cy.get("input.border-red-500").should("have.length", 0);
      cy.get(".transform-gpu").should("have.class", "hidden");
      cy.get("span").contains(
        "Copyright© 2024, Hitachi, Ltd. Copyright© 2024, Hitachi Solutions, Ltd.",
      );
    });

    it("お知らせ一覧が正しく表示される", () => {
      cy.get(".font-semibold").contains("お知らせ");

      cy.get(".whitespace-pre-line").then((elements) => {
        const dates = elements.map((index, element) => {
          // @ts-ignore
          const match = /(\d{4}\/\d{1,2}\/\d{1,2})\s/.exec(element.textContent);

          return match ? match[1] : null;
        });

        for (let i = 0; i < dates.length - 1; i++) {
          const current = new Date(dates[i]);
          const next = new Date(dates[i + 1]);
          expect(current).to.be.gte(next);
        }

        expect(dates.length).to.be.eq(
          fixture.Notification.filter((n) => n.type === "SYSTEM").length,
        );
      });
    });
  });
});
