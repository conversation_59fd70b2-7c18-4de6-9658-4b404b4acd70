// 模拟 portal-error
jest.mock("@/app/lib/portal-error", () => ({
  handleServerError: jest.fn((error) => {
    throw error;
  }),
}));

// 模拟 prisma
jest.mock("@/app/lib/prisma", () => ({
  __esModule: true,
  default: {
    containerConcurrencyStatus: {
      findUnique: jest.fn(),
    },
    task: {
      findMany: jest.fn(),
    },
    lov: {
      findMany: jest.fn(),
      findFirst: jest.fn(),
    },
    server: {
      findUnique: jest.fn(),
    },
    license: {
      findUnique: jest.fn(),
    },
  },
}));

// 然后导入其他模块
import { ServerDataTasks } from "@/app/lib/data/tasks";
import { expect } from "@jest/globals";
import prisma from "@/app/lib/prisma";

const prismaMock = prisma as jest.Mocked<typeof prisma>;

// iron-sessionのモック
jest.mock("iron-session", () => ({
  getIronSession: jest.fn().mockResolvedValue({
    user: {
      tz: "Asia/Tokyo",
      licenseId: "test-license-123",
    },
  }),
}));

// next/headersのモック
jest.mock("next/headers", () => ({
  cookies: jest.fn(),
}));

// next/cacheのモック
jest.mock("next/cache", () => ({
  revalidateTag: jest.fn(),
  unstable_cache: jest.fn((fn) => {
    // unstable_cache をモックして、渡された関数を実行する関数を返す
    return async () => {
      return await fn();
    };
  }),
}));

// sessionのモック
jest.mock("@/app/lib/session", () => ({
  getSession: jest.fn().mockResolvedValue({
    user: { licenseId: "test-license-123" },
  }),
}));

// prismaのモック（上記で設定済み）

// ServerDataLovのモック
jest.mock("@/app/lib/data/lov", () => ({
  ServerDataLov: {
    fetchCachedLov: jest.fn(),
  },
}));

// utilsのモック
jest.mock("@/app/lib/utils", () => ({
  castValueToLabel: jest.fn((value: string, _lovData: any) => {
    const mockLovMap: { [key: string]: string } = {
      "TASK_STATUS.QUEUED": "実行待ち",
      "TASK_STATUS.COMPLETED_SUCCESS": "正常終了",
      "TASK_STATUS.COMPLETED_ERROR": "エラー",
      "TASK_STATUS.CANCELLED": "中止",
      "TASK_TYPE.OPLOG_EXPORT": "操作ログのエクスポート",
      "TASK_TYPE.MGMT_ITEM_IMPORT": "管理項目定義のインポート",
      "TASK_TYPE.MGMT_ITEM_EXPORT": "管理項目定義のエクスポート",
    };
    return mockLovMap[value] || value;
  }),
}));

/**
 * @fileoverview ServerDataTasks データ層単体テスト
 * @description タスクデータ取得・編集処理の全機能を検証する。
 * コンテナ並行実行状態確認、タスク一覧データ取得・編集、
 * フィルタリング・ソート・ページング処理、LOV値変換処理、
 * 日時変換・フォーマット処理を含む。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */
describe("ServerDataTasks", () => {
  const mockLovData = [
    { code: "QUEUED", label: "実行待ち" },
    { code: "COMPLETED_SUCCESS", label: "正常終了" },
    { code: "COMPLETED_ERROR", label: "エラー" },
    { code: "CANCELLED", label: "中止" },
    { code: "OPLOG_EXPORT", label: "操作ログのエクスポート" },
    { code: "MGMT_ITEM_IMPORT", label: "管理項目定義のインポート" },
    { code: "MGMT_ITEM_EXPORT", label: "管理項目定義のエクスポート" },
  ];

  const mockTasks = [
    {
      id: "task-1",
      taskName: "TestServer-操作ログのエクスポート-20240101100000",
      status: "TASK_STATUS.QUEUED",
      taskType: "TASK_TYPE.OPLOG_EXPORT",
      submittedAt: new Date("2024-01-01T09:59:00Z"),
      startedAt: new Date("2024-01-01T10:00:00Z"),
      endedAt: null,
      updatedAt: new Date("2024-01-01T10:00:00Z"),
      submittedByUserId: "<EMAIL>",
      targetVmName: "vm-test",
      targetContainerName: "container-test",
      targetServerName: "TestServer",
      targetHRWGroupName: "group-test",
      targetServerId: "server-1",
      parametersJson: "{}",
      resultMessage: null,
      errorMessage: null,
      errorCode: null,
      licenseId: "test-license-123",
      createdAt: new Date("2024-01-01T09:59:00Z"),
    },
    {
      id: "task-2",
      taskName: "TestServer2-管理項目定義のエクスポート-20240101110000",
      status: "TASK_STATUS.COMPLETED_SUCCESS",
      taskType: "TASK_TYPE.MGMT_ITEM_EXPORT",
      submittedAt: new Date("2024-01-01T10:59:00Z"),
      startedAt: new Date("2024-01-01T11:00:00Z"),
      endedAt: new Date("2024-01-01T11:05:00Z"),
      updatedAt: new Date("2024-01-01T11:05:00Z"),
      submittedByUserId: "<EMAIL>",
      targetVmName: "vm-test2",
      targetContainerName: "container-test2",
      targetServerName: "TestServer2",
      targetHRWGroupName: "group-test2",
      targetServerId: "server-2",
      parametersJson: "{}",
      resultMessage: null,
      errorMessage: null,
      errorCode: null,
      licenseId: "test-license-123",
      createdAt: new Date("2024-01-01T10:59:00Z"),
    },
  ] as any[];

  beforeEach(() => {
    jest.clearAllMocks();
    const { ServerDataLov } = require("@/app/lib/data/lov");
    (ServerDataLov.fetchCachedLov as jest.Mock).mockResolvedValue(mockLovData);
  });

  /**
   * 試験観点：コンテナ並行実行状態確認処理
   * 試験対象：ServerDataTasks.getContainerStatus関数のコンテナ状態取得機能
   * 試験手順：
   * 1. 正常なコンテナステータス取得処理を実行
   * 2. レコードが存在しない場合の処理を実行
   * 確認項目：
   * - Azure VM名とDockerコンテナ名を複合キーとして使用すること
   * - BUSYステータスが正しく取得されること
   * - レコードが存在しない場合はnullが返されること
   */
  describe("getContainerStatus", () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    /**
     * 試験観点：BUSYステータス取得機能の動作確認
     * 試験対象：fetchContainerConcurrencyStatus関数の正常系処理
     * 試験手順：
     * 1. BUSYステータスのコンテナでfetchContainerConcurrencyStatusを呼び出し
     * 確認項目：
     * - BUSYステータスが正しく取得されること
     */
    it("正常系: BUSYステータスの取得", async () => {
      // 実際のコードは select: { status: true } を使用するため、statusプロパティのみを返す
      (prismaMock.containerConcurrencyStatus.findUnique as jest.Mock).mockResolvedValue({
        status: "BUSY",
      });

      const result = await ServerDataTasks.getContainerStatus("vm1", "container1");

      expect(result).toBe("BUSY");
      expect(prismaMock.containerConcurrencyStatus.findUnique).toHaveBeenCalledWith({
        select: {
          status: true,
        },
        where: {
          targetVmName_targetContainerName: {
            targetVmName: "vm1",
            targetContainerName: "container1",
          },
        },
      });
    });

    /**
     * 試験観点：レコード不存在時の境界条件確認
     * 試験対象：fetchContainerConcurrencyStatus関数の境界値処理
     * 試験手順：
     * 1. 存在しないコンテナでfetchContainerConcurrencyStatusを呼び出し
     * 確認項目：
     * - nullが返されること
     */
    it("境界条件: レコードが存在しない場合はnullを返す", async () => {
      (prismaMock.containerConcurrencyStatus.findUnique as jest.Mock).mockResolvedValue(null);

      const result = await ServerDataTasks.getContainerStatus("vm1", "container1");

      expect(result).toBeNull();
    });

    /**
     * 試験観点：IDLEステータス取得機能の動作確認
     * 試験対象：fetchContainerConcurrencyStatus関数の正常系処理
     * 試験手順：
     * 1. IDLEステータスのコンテナでfetchContainerConcurrencyStatusを呼び出し
     * 確認項目：
     * - IDLEステータスが正しく取得されること
     */
    it("正常系: IDLEステータスの取得", async () => {
      (prismaMock.containerConcurrencyStatus.findUnique as jest.Mock).mockResolvedValue({
        status: "IDLE",
      });

      const result = await ServerDataTasks.getContainerStatus("vm2", "container2");

      expect(result).toBe("IDLE");
    });
  });

  /**
   * 試験観点：タスク一覧データ取得・編集処理
   * 試験対象：ServerDataTasks.fetchFilteredTasks関数のデータ取得と編集機能
   * 試験手順：
   * 1. フィルタリング、ソート、ページング処理の確認
   * 2. LOV値変換処理の確認
   * 3. 日時変換・フォーマット処理の確認
   * 確認項目：
   * - 契約IDに基づくタスク一覧取得
   * - ステータス・タスク種別の日本語名称変換
   * - メモリ内でのフィルタリング・ソート・ページング処理
   */
  describe("fetchFilteredTasks", () => {
    beforeEach(() => {
      jest.clearAllMocks();

      // Mock the Prisma queries used in fetchCachedTasks
      (prismaMock.task.findMany as jest.Mock).mockResolvedValue(mockTasks);
      (prismaMock.lov.findMany as jest.Mock).mockImplementation((args: any) => {
        console.log("prismaMock.lov.findMany called with:", args);
        if (args.where.parentCode === "TASK_STATUS") {
          return Promise.resolve([
            { code: "TASK_STATUS.QUEUED", name: "実行待ち", value: "TASK_STATUS.QUEUED" },
            { code: "TASK_STATUS.COMPLETED_SUCCESS", name: "正常終了", value: "TASK_STATUS.COMPLETED_SUCCESS" },
          ] as any);
        } else if (args.where.parentCode === "TASK_TYPE") {
          return Promise.resolve([
            { code: "TASK_TYPE.OPLOG_EXPORT", name: "操作ログのエクスポート", value: "TASK_TYPE.OPLOG_EXPORT" },
            { code: "TASK_TYPE.MGMT_ITEM_EXPORT", name: "管理項目定義のエクスポート", value: "TASK_TYPE.MGMT_ITEM_EXPORT" },
          ] as any);
        }
        return Promise.resolve([] as any);
      });
    });

    /**
     * 試験観点：基本的なタスク一覧取得機能の動作確認
     * 試験対象：fetchFilteredTasks関数の正常系処理
     * 試験手順：
     * 1. 基本的な条件でfetchFilteredTasksを呼び出し
     * 確認項目：
     * - タスク一覧が正しく取得されること
     */
    it("正常系: 基本的なタスク一覧取得", async () => {
      const result = await ServerDataTasks.fetchFilteredTasks("", 10, 1, "startedAt", "desc");

      expect(prismaMock.task.findMany).toHaveBeenCalledWith({
        where: { licenseId: "test-license-123" },
      });

      expect(result).toHaveLength(2);
      // データの順序は実装によって異なる可能性があるため、IDで確認
      const task1 = result!.find(t => t.id === "task-1");
      const task2 = result!.find(t => t.id === "task-2");
      expect(task1?.taskName).toBe("TestServer-操作ログのエクスポート-20240101100000");
      expect(task2?.taskName).toBe("TestServer2-管理項目定義のエクスポート-20240101110000");
    });

    /**
     * 試験観点：フィルタリング機能の動作確認
     * 試験対象：fetchFilteredTasks関数のフィルタリング処理
     * 試験手順：
     * 1. フィルタ条件を指定してfetchFilteredTasksを呼び出し
     * 確認項目：
     * - 指定条件に一致するタスクのみが取得されること
     */
    it("正常系: フィルタリング処理", async () => {
      const result = await ServerDataTasks.fetchFilteredTasks("TestServer2", 10, 1, "startedAt", "desc");

      // フィルタリング結果の確認
      expect(result).toHaveLength(1);
      expect(result![0].targetServerName).toBe("TestServer2");
    });

    /**
     * 試験観点：ソート機能の動作確認
     * 試験対象：fetchFilteredTasks関数のソート処理
     * 試験手順：
     * 1. タスク名昇順でfetchFilteredTasksを呼び出し
     * 確認項目：
     * - タスクが指定順序でソートされること
     */
    it("正常系: ソート処理（タスク名昇順）", async () => {
      const result = await ServerDataTasks.fetchFilteredTasks("", 10, 1, "taskName", "asc");

      // ソート結果の確認（タスク名昇順）
      expect(result![0].taskName).toBe("TestServer-操作ログのエクスポート-20240101100000");
      expect(result![1].taskName).toBe("TestServer2-管理項目定義のエクスポート-20240101110000");
    });

    /**
     * 試験観点：ページング機能の動作確認
     * 試験対象：fetchFilteredTasks関数のページング処理
     * 試験手順：
     * 1. ページサイズとページ番号を指定してfetchFilteredTasksを呼び出し
     * 確認項目：
     * - 指定ページのタスクが正しく取得されること
     */
    it("正常系: ページング処理", async () => {
      // 大量のタスクデータを生成
      const largeMockTasks = Array.from({ length: 25 }, (_, index) => ({
        id: `task-${index}`,
        taskName: `TestServer${index}-操作ログのエクスポート-20240101${String(index).padStart(6, '0')}`,
        status: "QUEUED",
        taskType: "OPLOG_EXPORT",
        submittedAt: new Date(`2024-01-01T${String(9 + index).padStart(2, '0')}:59:00Z`),
        startedAt: new Date(`2024-01-01T${String(10 + index).padStart(2, '0')}:00:00Z`),
        endedAt: null,
        updatedAt: new Date(`2024-01-01T${String(10 + index).padStart(2, '0')}:00:00Z`),
        submittedByUserId: `testuser${index}@example.com`,
        targetVmName: `vm-test${index}`,
        targetContainerName: `container-test${index}`,
        targetServerName: `TestServer${index}`,
        targetHRWGroupName: `group-test${index}`,
        targetServerId: `server-${index}`,
        parametersJson: "{}",
        resultMessage: null,
        errorMessage: null,
        errorCode: null,
        licenseId: "test-license-123",
        createdAt: new Date(`2024-01-01T${String(9 + index).padStart(2, '0')}:59:00Z`),
      })) as any[];

      (prismaMock.task.findMany as jest.Mock).mockResolvedValue(largeMockTasks);

      // 2ページ目（11-20件目）を取得
      const result = await ServerDataTasks.fetchFilteredTasks("", 10, 2, "startedAt", "desc");

      // ページング結果の確認
      expect(result).toHaveLength(10);
      // 2ページ目なので、インデックス10-19のデータが取得される
    });

    /**
     * 試験観点：フィルター結果なしの境界条件確認
     * 試験対象：fetchFilteredTasks関数の境界値処理
     * 試験手順：
     * 1. 該当データがないフィルタ条件でfetchFilteredTasksを呼び出し
     * 確認項目：
     * - 空の結果が返されること
     */
    it("境界条件: 空のフィルター結果", async () => {
      const result = await ServerDataTasks.fetchFilteredTasks("存在しないサーバー", 10, 1, "startedAt", "desc");

      expect(result).toHaveLength(0);
    });

    /**
     * 試験観点：LOV値変換機能の動作確認
     * 試験対象：fetchFilteredTasks関数のLOV値変換処理
     * 試験手順：
     * 1. LOV値を含むタスクでfetchFilteredTasksを呼び出し
     * 確認項目：
     * - LOV値が正しく変換されること
     */
    it("正常系: LOV値変換処理", async () => {
      const result = await ServerDataTasks.fetchFilteredTasks("", 10, 1, "startedAt", "desc");

      // ステータスとタスク種別が日本語に変換されることを確認
      // 注意：実際の実装では、データの順序が異なる可能性があるため、IDで確認
      const task1 = result!.find(t => t.id === "task-1");
      const task2 = result!.find(t => t.id === "task-2");

      expect(task1?.status).toBe("実行待ち"); // QUEUED → 実行待ち
      expect(task1?.taskType).toBe("操作ログのエクスポート"); // OPLOG_EXPORT → 操作ログのエクスポート
      expect(task2?.status).toBe("正常終了"); // COMPLETED_SUCCESS → 正常終了
      expect(task2?.taskType).toBe("管理項目定義のエクスポート"); // MGMT_ITEM_EXPORT → 管理項目定義のエクスポート
    });
  });

  /**
   * 試験観点：タスク総ページ数取得処理
   * 試験対象：ServerDataTasks.fetchTasksPages関数の総ページ数計算機能
   * 試験手順：
   * 1. 基本的な総ページ数計算処理を実行
   * 2. フィルタリング適用時の総ページ数計算処理を実行
   * 3. キャッシュリフレッシュ処理を実行
   * 確認項目：
   * - フィルター条件に基づく総ページ数の正確な計算
   * - キャッシュリフレッシュフラグの動作
   */
  describe("fetchTasksPages", () => {
    beforeEach(() => {
      jest.clearAllMocks();

      // Mock the Prisma queries used in fetchCachedTasks
      (prismaMock.task.findMany as jest.Mock).mockResolvedValue(mockTasks);
      (prismaMock.lov.findMany as jest.Mock).mockImplementation((args: any) => {
        if (args.where.parentCode === "TASK_STATUS") {
          return Promise.resolve([
            { code: "TASK_STATUS.QUEUED", name: "実行待ち", value: "TASK_STATUS.QUEUED" },
            { code: "TASK_STATUS.COMPLETED_SUCCESS", name: "正常終了", value: "TASK_STATUS.COMPLETED_SUCCESS" },
          ] as any);
        } else if (args.where.parentCode === "TASK_TYPE") {
          return Promise.resolve([
            { code: "TASK_TYPE.OPLOG_EXPORT", name: "操作ログのエクスポート", value: "TASK_TYPE.OPLOG_EXPORT" },
            { code: "TASK_TYPE.MGMT_ITEM_EXPORT", name: "管理項目定義のエクスポート", value: "TASK_TYPE.MGMT_ITEM_EXPORT" },
          ] as any);
        }
        return Promise.resolve([] as any);
      });
    });

    /**
     * 試験観点：基本的なページ数計算機能の動作確認
     * 試験対象：fetchTasksPages関数のページ数計算ロジック
     * 試験手順：
     * 1. 基本的な条件でfetchTasksPagesを呼び出し
     * 確認項目：
     * - 正しいページ数が計算されること
     */
    it("正常系: 基本的な総ページ数計算", async () => {
      const result = await ServerDataTasks.fetchTasksPages("", 10, false);

      // 2件のタスクで10件/ページなので、1ページ
      expect(result).toBe(1);
    });

    /**
     * 試験観点：フィルタリング適用時のページ数計算確認
     * 試験対象：fetchTasksPages関数のフィルタリング機能
     * 試験手順：
     * 1. フィルタ条件を指定してfetchTasksPagesを呼び出し
     * 確認項目：
     * - フィルタリング後の正しいページ数が計算されること
     */
    it("正常系: フィルタリング適用時の総ページ数計算", async () => {
      const result = await ServerDataTasks.fetchTasksPages("TestServer2", 10, false);

      // フィルタリング後1件なので、1ページ
      expect(result).toBe(1);
    });

    /**
     * 試験観点：データなし時の境界条件確認
     * 試験対象：fetchTasksPages関数の境界値処理
     * 試験手順：
     * 1. データが存在しない状態でfetchTasksPagesを呼び出し
     * 確認項目：
     * - ページ数が0となること
     */
    it("境界条件: データなし時の総ページ数", async () => {
      (prismaMock.task.findMany as jest.Mock).mockResolvedValue([]);

      const result = await ServerDataTasks.fetchTasksPages("", 10, false);

      expect(result).toBe(0);
    });

    /**
     * 試験観点：キャッシュリフレッシュ機能の動作確認
     * 試験対象：fetchTasksPages関数のキャッシュ制御機能
     * 試験手順：
     * 1. キャッシュリフレッシュフラグを有効にしてfetchTasksPagesを呼び出し
     * 確認項目：
     * - キャッシュリフレッシュが正しく動作すること
     */
    it("正常系: キャッシュリフレッシュフラグの動作", async () => {
      // キャッシュリフレッシュありの場合
      await ServerDataTasks.fetchTasksPages("", 10, true);

      // キャッシュリフレッシュなしの場合
      await ServerDataTasks.fetchTasksPages("", 10, false);

      // 両方とも正常に処理されることを確認
      expect(prismaMock.task.findMany).toHaveBeenCalledTimes(2);
    });
  });
});