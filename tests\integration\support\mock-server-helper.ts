/**
 * @fileoverview Mock Server测试辅助工具
 * @description
 * 为集成测试提供Mock Server的管理和配置功能。
 * 包括服务器启动/停止、环境变量设置、作业状态控制等辅助方法。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { AzureAutomationMockServer, MockJobStatus } from './azure-automation-mock-server';

/**
 * Mock Server测试辅助类
 */
export class MockServerHelper {
  private server: AzureAutomationMockServer | null = null;
  private originalEnvVars: Record<string, string | undefined> = {};

  constructor(
    private port: number = 3001
  ) {}

  /**
   * 启动Mock Server并设置环境变量
   */
  async startServer(): Promise<void> {
    if (this.server) {
      throw new Error('Mock Server is already running');
    }

    this.server = new AzureAutomationMockServer(this.port);
    await this.server.start();

    // 保存原始环境变量
    this.saveOriginalEnvVars();

    // 设置测试环境变量
    this.setTestEnvVars();

    console.log(`[MockServerHelper] Server started at ${this.server.getBaseUrl()}`);
  }

  /**
   * 停止Mock Server并恢复环境变量
   */
  async stopServer(): Promise<void> {
    if (this.server) {
      await this.server.stop();
      this.server = null;
    }

    // 恢复原始环境变量
    this.restoreOriginalEnvVars();

    console.log('[MockServerHelper] Server stopped and environment restored');
  }

  /**
   * 保存原始环境变量
   */
  private saveOriginalEnvVars(): void {
    const envVarsToSave = [
      'AZURE_MANAGEMENT_BASE_URL'
    ];

    envVarsToSave.forEach(varName => {
      this.originalEnvVars[varName] = process.env[varName];
    });
  }

  /**
   * 设置测试环境变量
   */
  private setTestEnvVars(): void {
    if (!this.server) {
      throw new Error('Server not started');
    }

    process.env.AZURE_MANAGEMENT_BASE_URL = this.server.getBaseUrl();

    console.log('[MockServerHelper] Test environment variables set');
  }

  /**
   * 恢复原始环境变量
   */
  private restoreOriginalEnvVars(): void {
    Object.entries(this.originalEnvVars).forEach(([varName, originalValue]) => {
      if (originalValue === undefined) {
        delete process.env[varName];
      } else {
        process.env[varName] = originalValue;
      }
    });

    this.originalEnvVars = {};
    console.log('[MockServerHelper] Original environment variables restored');
  }

  /**
   * 清理所有作业
   */
  async clearAllJobs(): Promise<void> {
    if (!this.server) {
      throw new Error('Server not started');
    }

    const response = await fetch(`${this.server.getBaseUrl()}/mock/jobs`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      throw new Error(`Failed to clear jobs: ${response.status} ${response.statusText}`);
    }

    console.log('[MockServerHelper] All jobs cleared');
  }

  /**
   * 设置作业状态
   */
  async setJobStatus(jobName: string, status: MockJobStatus): Promise<void> {
    if (!this.server) {
      throw new Error('Server not started');
    }

    const response = await fetch(`${this.server.getBaseUrl()}/mock/jobs/${jobName}/status`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ status })
    });

    if (!response.ok) {
      throw new Error(`Failed to set job status: ${response.status} ${response.statusText}`);
    }

    console.log(`[MockServerHelper] Job '${jobName}' status set to '${status}'`);
  }

  /**
   * 获取所有作业
   */
  async getAllJobs(): Promise<any[]> {
    if (!this.server) {
      throw new Error('Server not started');
    }

    const response = await fetch(`${this.server.getBaseUrl()}/mock/jobs`);

    if (!response.ok) {
      throw new Error(`Failed to get jobs: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.jobs;
  }

  /**
   * 等待作业达到指定状态
   */
  async waitForJobStatus(jobName: string, expectedStatus: MockJobStatus, timeoutMs: number = 5000): Promise<void> {
    if (!this.server) {
      throw new Error('Server not started');
    }

    const startTime = Date.now();
    
    while (Date.now() - startTime < timeoutMs) {
      const jobs = await this.getAllJobs();
      const job = jobs.find(j => j.name === jobName);
      
      if (job && job.status === expectedStatus) {
        console.log(`[MockServerHelper] Job '${jobName}' reached status '${expectedStatus}'`);
        return;
      }

      await new Promise(resolve => setTimeout(resolve, 100));
    }

    throw new Error(`Timeout waiting for job '${jobName}' to reach status '${expectedStatus}'`);
  }

  /**
   * 获取服务器基础URL
   */
  getBaseUrl(): string {
    if (!this.server) {
      throw new Error('Server not started');
    }
    return this.server.getBaseUrl();
  }



  /**
   * 设置失败场景
   */
  async setFailureScenario(endpoint: string, method: string, statusCode: number, errorResponse?: any): Promise<void> {
    if (!this.server) {
      throw new Error('Server not started');
    }

    const response = await fetch(`${this.server.getBaseUrl()}/mock/failure-scenarios`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        endpoint,
        method,
        statusCode,
        errorResponse,
        enabled: true
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to set failure scenario: ${response.status} ${response.statusText}`);
    }

    console.log(`[MockServerHelper] Failure scenario set for ${method}:${endpoint} -> ${statusCode}`);
  }

  /**
   * 清理所有失败场景
   */
  async clearFailureScenarios(): Promise<void> {
    if (!this.server) {
      throw new Error('Server not started');
    }

    const response = await fetch(`${this.server.getBaseUrl()}/mock/failure-scenarios`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      throw new Error(`Failed to clear failure scenarios: ${response.status} ${response.statusText}`);
    }

    console.log('[MockServerHelper] All failure scenarios cleared');
  }

  /**
   * 获取所有失败场景
   */
  async getFailureScenarios(): Promise<any[]> {
    if (!this.server) {
      throw new Error('Server not started');
    }

    const response = await fetch(`${this.server.getBaseUrl()}/mock/failure-scenarios`);

    if (!response.ok) {
      throw new Error(`Failed to get failure scenarios: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.scenarios;
  }

  /**
   * 检查服务器是否运行
   */
  isRunning(): boolean {
    return this.server !== null;
  }
}

/**
 * 全局Mock Server Helper实例
 */
export const mockServerHelper = new MockServerHelper();
