/**
 * @fileoverview タスク一覧ページ（/dashboard/tasks）の主要なサーバーコンポーネント
 * @description
 * タスク一覧ページ（/dashboard/tasks）の主要なサーバーコンポーネント。
 * URLのクエリパラメータを解析して、一覧のフィルタリング、ページネーション、ソートなどの状態を管理する。
 * 解析した状態に基づき、データ取得（総ページ数など）をサーバーサイドで行い、
 * 検索、ページネーション、ページサイズ、タスク表など、主要なUIコンポーネントを統合してページのレイアウトを構築する。
 *
 * 主な設計思想:
 * - Next.js App Routerのサーバーコンポーネントとしての特性を最大限に活用し、データ取得とUIレンダリングをサーバーサイドで完結させることで、高いパフォーマンスを実現する
 * - URLクエリパラメータを信頼できる唯一の情報源とし、ページの表示状態を決定する。これにより、ページの再読み込みや共有時にも状態が維持される
 * - ReactのSuspenseとスケルトンコンポーネントを組み合わせることで、データロード中のユーザーエクスペリエンスを向上させる
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { ServerDataTasks } from "@/app/lib/data/tasks";
import { generateSecureId } from "@/app/lib/utils";
import PageSize from "@/app/ui/page-size";
import Pagination from "@/app/ui/pagination";
import RefreshToken from "@/app/ui/refreshToken";
import Search from "@/app/ui/search";
import Table from "@/app/ui/tasks/table";
import { TableSkeleton } from "@/app/ui/skeletons";
import { Metadata } from "next";
import { Suspense } from "react";

// Next.jsのメタデータAPI。ページの<head>内の<title>タグなどを設定します。
export const metadata: Metadata = {
  title: "タスク一覧",
};

/**
 * タスク一覧ページのメインコンポーネント（サーバーコンポーネント）
 *
 * Next.jsから渡されるプロパティを受け取り、URLのクエリパラメータを解析してタスク一覧ページのUIを構築する
 * @param props - コンポーネントのプロパティ
 * @param props.searchParams - URLクエリパラメータオブジェクト
 * @param props.searchParams.filter - タスク名による検索フィルタ文字列
 * @param props.searchParams.page - 表示するページ番号
 * @param props.searchParams.size - 1ページあたりの表示件数
 * @param props.searchParams.sort - ソート対象のカラム名
 * @param props.searchParams.order - ソート順序（昇順または降順）
 * @returns タスク一覧ページのJSX要素
 */
export default async function Page({
  searchParams,
}: {
  searchParams?: {
    filter?: string;
    page?: string;
    size?: string;
    sort?:
      | "taskName"
      | "status"
      | "startedAt"
      | "endedAt"
      | "targetServerName"
      | "taskType"
      | "submittedByUserId";
    order?: "asc" | "desc";
  };
}) {
  // 1. URLクエリパラメータの解析と状態の決定
  // searchParamsオブジェクトから各値を取り出し、未指定の場合のデフォルト値を設定します。
  // これにより、ページの表示状態（フィルタ、ページ番号、ソート順など）が一意に決まります。
  const filter = searchParams?.filter || "";
  const size = Number(searchParams?.size) || 10; // デフォルトのページサイズは10件
  const currentPage = Number(searchParams?.page) || 1; // デフォルトは1ページ目
  const sort = searchParams?.sort || "startedAt"; // デフォルトのソートキーは開始日時
  const order = searchParams?.order || "desc"; // デフォルトのソート順は降順

  // 2. 総ページ数の取得
  // 解析したフィルタとページサイズを基に、サーバーサイドで総ページ数を計算します。
  // このデータはPaginationコンポーネントに渡され、ページネーションUIを構築するために使用されます。
  // !searchParams?.page は、初回アクセス時（pageパラメータなし）にキャッシュをリフレッシュするトリガーとして利用します。
  const totalPages =
    (await ServerDataTasks.fetchTasksPages(filter, size, !searchParams?.page)) || 0;

  // 3. セッションリフレッシュ用キーの生成
  // RefreshTokenコンポーネントのkeyプロパティにセキュアな一意識別子を渡すことで、
  // ページ遷移ごとにコンポーネントを意図的に再マウントさせ、セッションの有効期限を延長する処理をトリガーします。
  const refresh = generateSecureId(true); // サーバーサイドなのでtrue

  // ヘッダーUIエリア：検索とページネーション
  // メインコンテンツエリア：タスクテーブル
  // バックグラウンドコンポーネント：ユーザーセッションの有効期限をバックグラウンドで更新
  return (
    <div className="p-4 h-full flex flex-col">
      <div className="flex-column flex flex-wrap items-center justify-between rounded-t-xl border border-gray-200 bg-gray-100 p-2 md:flex-row">
        <Search />
        {totalPages > 0 && (
          <div className="flex items-center">
            <Pagination totalPages={totalPages} />
            <PageSize />
          </div>
        )}
      </div>
      <div className="relative overflow-x-auto overflow-y-auto shadow-md rounded-b-lg">
        <Suspense
          key={filter + currentPage + size + sort + order}
          fallback={<TableSkeleton />}
        >
          {/* @ts-expect-error Server Component */}
          <Table
            filter={filter}
            page={currentPage}
            size={size}
            sort={sort}
            order={order}
          />
        </Suspense>
      </div>
      <RefreshToken key={refresh} />
    </div>
  );
}
