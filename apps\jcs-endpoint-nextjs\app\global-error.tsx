/**
 * @file global-error.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

"use client";

import { PORTAL_ERROR_MESSAGES } from "./lib/definitions";

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <html>
      <body>
        <h2>${PORTAL_ERROR_MESSAGES.EMEC0007}</h2>
        <button onClick={() => reset()}>再試行</button>
      </body>
    </html>
  );
}
