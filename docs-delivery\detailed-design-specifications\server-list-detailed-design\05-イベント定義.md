### イベント1：初期表示

ユーザーがブラウザでサーバ一覧画面のURL (`/dashboard/servers`) へアクセスすると、ポータルシステムはサーバ一覧の表示に必要な初期データをデータベースから取得し、画面に表示する。

1.  **ページ初期化処理**<br/>
    URLに検索パラメータが存在しない場合、サーバ一覧画面コンポーネント (以降、ページコンポーネント) は以下の初期条件を設定する。

| 項目名 | 設定内容 |
|:---|:---|
| フィルター（filter） | - |
| ページ（page） | 1 |
| 行数/ページ（size） | 10 |
| ソートキー（sort） | サーバ名の物理名 |
| ソートオーダー（order） | 'asc' |

2.  **データ取得と編集処理**<br/>
    本画面では、複数の項目に対する横断的なフィルター機能（キーワード検索）を提供するため、またデータ総量が限定的である想定から、現在のログインユーザーの契約IDに紐づくサーバ情報の全件を取得しキャッシュする仕様とする。
    キャッシュされたサーバデータがない場合、ページコンポーネントは、契約IDが現在のログインユーザーの契約IDと一致するサーバレコードの全件をデータベースのServerテーブルから取得する。取得したデータに対し、サーバ種別の日本語名称への変換（本設計書「参照値一覧- サーバ種別」を参照）を行う。その後サーバデータをキャッシュし、キャッシュされた全項目は文字列に変換される。
    キャッシュされたサーバデータに対し、指定されたフィルター条件、ソート条件、ページネーション条件に基づいて、下記のメモリ内での絞り込み、並び替え、ページ分割処理を行い、編集したサーバ一覧データおよび総ページ数をページコンポーネントへ返却する。
    ●　絞り込み：サーバ名、サーバ種別（日本語表示名）、管理画面URL、以上の項目について、filterの文字列がキャッシュされたいずれかの項目の文字列に含まれているサーバデータを選出する。英語の大文字小文字を区別しない。
    ●　並び替え：ソートキーsortとソート順orderによってデータをソートする。
    ●　ページ分割処理：先頭の ( page - 1 ) * size 件をスキップして、size件のデータを取得する。
    
    データ取得処理の実行中は、画面にスケルトンローディング（テーブルの仮の骨組み）を表示する。

3.  **画面レンダリングと表示処理**<br/>
    ページコンポーネントは、取得したデータに基づき画面各項目をサーバサイドでレンダリングする。レンダリング結果はクライアントブラウザへ送信され、画面が表示される。
    各行のタスクドロップダウンメニュー項目は、当該サーバの種別に基づき表示・非表示が制御される。対象サーバの種別が「JP1/ITDM2(統括マネージャ)」（コード: SERVER_TYPE.GENERAL_MANAGER）または「JP1/ITDM2(中継マネージャ)」（コード: SERVER_TYPE.RELAY_MANAGER）である場合に限り表示される。さらに、ユーザーの基本契約プランコードを契約(License)テーブルから取得し、STANDARDかLIGHT_Bの場合はタスクメニューに操作ログのエクスポートのサブメニューを表示する。LIGHT_Aの場合はタスクメニューに操作ログのエクスポートのサブメニューを表示しない。
    ページネーションコントロールは、取得した総ページ数に基づき適切に初期化される（「前のページ」ボタンは非活性、「次のページ」ボタンは総ページ数に応じて活性/非活性）。
    データ取得処理中にエラーが発生した場合（例：データベース接続不可が発生した場合等）は、Next.js のエラー処理部品により、エラーメッセージが表示される。

### イベントNo 2：更新ボタンを押下

| # | イベント名 | 処理内容 |
|:---|:---|:---|
| 1 | DB情報取得 | 検索条件をクリアされなく、サーバデータを取得する。 |
| 2 | 画面表示 | フィルターの条件をクリアされなく、ソートをクリアし、行数/ページは10に変更し、サーバ一覧を再表示する。 |

### イベントNo 3：フィルターに文字を入力

| # | イベント名 | 処理内容 |
|:---|:---|:---|
| 1 | 画面表示 | 入力した文字を表示する。 |

### イベントNo 4：フィルターの検索ボタンを押下

| # | イベント名 | 処理内容 |
|:---|:---|:---|
| 1 | DB情報取得 | 検索条件に、「フィルター」は入力した文字列に変更して、サーバデータを取得する。 |
| 2 | 画面表示 | （１）サーバ一覧を再表示する。<br/>（２）改ページのエリアを再表示する。 |

### イベントNo 5：フィルターのクリアボタンを押下

| # | イベント名 | 処理内容 |
|:---|:---|:---|
| 1 | DB情報取得 | 検索条件に、「フィルター」をクリアして、サーバデータを取得する。 |
| 2 | 画面表示 | （１）フィルターに入力した文字をクリアする。<br/>（２）サーバ一覧を再表示する。<br/>（3）改ページのエリアを再表示する。 |

### イベントNo 6：前のページボタンを押下

| # | イベント名 | 処理内容 |
|:---|:---|:---|
| 1 | DB情報取得 | 検索条件に、「ページ」を1減らした後、サーバデータを取得する。 |
| 2 | 画面表示 | （１）サーバ一覧を再表示する。<br/>（２）次のページボタンを活性になる。<br/>（３）該当のページは1の場合、前のページボタンを非活性になる。その以外の場合、活性になる。<br/>（４）改ページのエリアを再表示する。 |

### イベントNo 7：次のページボタンを押下

| # | イベント名 | 処理内容 |
|:---|:---|:---|
| 1 | DB情報取得 | 検索条件に、「ページ」を1増やした後、サーバデータを取得する。 |
| 2 | 画面表示 | （１）サーバ一覧を再表示する。<br/>（２）前のページボタンを活性になる。<br/>（３）一覧の最大のページ数は該当のページの場合、次のページボタンを非活性になる。その以外の場合、活性になる。<br/>（４）改ページのエリアを再表示する。 |

### イベントNo 8：指定のページボタンを押下

| # | イベント名 | 処理内容 |
|:---|:---|:---|
| 1 | DB情報取得 | 検索条件に、「ページ」を指定のページに変更して、サーバデータを取得する。 |
| 2 | 画面表示 | （１）サーバ一覧を再表示する。<br/>（２）該当のページは1の場合、前のページボタンを非活性になる。その以外の場合、活性になる。<br/>（３）一覧の最大のページ数は該当のページの場合、次のページボタンを非活性になる。その以外の場合、活性になる。<br/>（４）改ページのエリアを再表示する。 |

### イベントNo 9：行数/ページの選択項目ボタンを押下

| # | イベント名 | 処理内容 |
|:---|:---|:---|
| 1 | DB情報取得 | 検索条件に、「行数/ページ」を行数/ページの選択項目に変更して、「ページ」を1に変更して、サーバデータを取得する。 |
| 2 | 画面表示 | （１）サーバ一覧を再表示する。<br/>（２）指定のページを1になる。<br/>（３）前のページボタンを非活性になる。<br/>（４）一覧の最大のページ数は1の場合、次のページボタンを非活性になる。その以外の場合、活性になる。<br/>（５）改ページのエリアを再表示する。 |

### イベントNo 10：ハイパーリンクをクリック

| # | イベント名 | 処理内容 |
|:---|:---|:---|
| 1 | 画面表示 | 別タブでJP1/ITDM2または秘文の管理画面を開く。 |

### イベントNo 11：列のヘッダをクリック

| # | イベント名 | 処理内容 |
|:---|:---|:---|
| 1 | DB情報取得 | （１）該当の列はソートキーの場合、検索条件に、「ソートオーダー」を逆のソートオーダーに変換して、サーバデータを取得する。<br/>　※	ソートオーダーはascの場合、descに変換する。<br/>　　ソートオーダーはdescの場合、ascに変換する。<br/>（２）上記の以外の場合、検索条件に、「ソートキー」を該当の列の物理名に変更して、「ソートオーダー」をascに設定して、サーバデータを取得する。 |
| 2 | 画面表示 | （１）サーバ一覧を再表示する。<br/>（２）該当の列はソートキーの場合、ソートアイコンを逆のソートアイコンになる。<br/>（３）上記の以外の場合、ソートアイコンを昇順のアイコンになる。<br/>（４）改ページのエリアを再表示する。 |

### イベントNo 12：タスクメニューからタスクを選択

本イベント群（12.1, 12.2, 12.3）は、ユーザーが各行のタスクドロップダウンメニューから特定のバックグラウンドタスクを選択した際の処理を定義する。全てのタスク実行要求は、最終的にサーバサイドで実行される createTaskAction サーバアクションにより処理される。createTaskAction の詳細は、本設計書の「タスク受付処理詳細」シートを参照してください。

#### イベント12.1：「操作ログのエクスポート」選択時

1.  **パラメータ入力モーダル表示**<br/>
    ページコンポーネントは、操作ログエクスポートのパラメータ入力用モーダルダイアログ (以降、パラメータモーダル) を表示するための準備処理を行う。
    *   ページコンポーネントは、データベースの 値の一覧(Lov) テーブルから、コード OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN に対応する設定値（操作ログのエクスポート期間最大日数）を取得し、キャッシュする。取得失敗時は、システムで定義されたデフォルト値（30日）を使用する。
    *   ページコンポーネントは、パラメータモーダルへ渡す情報として、対象サーバの名称、上記で取得した最大日数、およびモーダルからの送信指示（入力された日付パラメータを含む）とクローズ指示を受け付けるための内部コールバック関数を準備する。
    
    準備ができた後、ページコンポーネントはパラメータモーダルを表示させる。

2.  **パラメータ入力と検証**<br/>
    ユーザーは表示されたパラメータモーダル内で、操作ログをエクスポートする期間の開始日および終了日を入力する。開始日と終了日の入力ボックスは編集不可で、キーボードによる入力はできない。カレンダーのアイコンをクリックして、カレンダーメニューから日付を選択する形で入力する。カレンダーメニューの初期値はクリックした日の日付となる。選択された日付はYYYY / MM / DDフォーマットで表示される。
    *   入力値が変更される都度、またはOKボタンが押下される際、パラメータモーダル内部でクライアントサイドの入力検証が実行される。
    *   検証ルール
        *   開始日と終了日の両方が入力されていること。未入力の場合、エラーメッセージ EMEC0016「{入力項目}を指定してください。」（{入力項目}は「開始日」または「終了日」で置換）を表示する。
        *   終了日は開始日以降の日付であること。違反する場合、エラーメッセージ EMEC0024「終了日は開始日以降の日付を指定してください。」を表示する。
        *   指定された期間（終了日 - 開始日 + 1日）が、ステップ1で取得した最大日数を超過していないこと。超過する場合、エラーメッセージ EMEC0020「{0} 日を超える期間が指定されました。{0} 日以内の期間を指定して再度実行してください。」（{0}は最大日数で置換）を表示する。
    *   クライアントサイド検証でエラーが検出された場合、該当するエラーメッセージがパラメータモーダル内に表示され、後続の処理は中断される。
    
    パラメータモーダルのキャンセルか×ボタンをクリックすると、パラメータモーダルを閉じてサーバ一覧画面に戻る。

3.  **最終確認モーダル表示**<br/>
    パラメータモーダルでのクライアントサイド検証が成功し、ユーザーがパラメータモーダルのOKボタンを押下すると、パラメータモーダルは入力された開始日と終了日をページコンポーネントへコールバックする。
    ページコンポーネントは、受け取った日付パラメータと対象サーバ情報に基づき、汎用の最終確認モーダルダイアログ（以降、最終確認モーダル）を表示するための準備を行う：最終確認モーダルへ渡す情報として、タイトル（「操作ログのエクスポート」）、確認メッセージ（「{0}の操作ログをエクスポートします。\nよろしいですか？」、{0}は対象サーバ名で置換）を設定する。
    準備ができた後、ページコンポーネントは最終確認モーダルを表示させる。

4.  **バックエンド処理呼び出し**<br/>
    ユーザーが最終確認モーダルのOKボタンを押下すると、ページコンポーネントは createTaskAction サーバアクション の呼び出し処理を開始する。
    *   ページコンポーネントは、FormData オブジェクトを生成し、以下の情報を含める。
        *   taskType: 文字列 TASK_TYPE.OPLOG_EXPORT (データベース 値の一覧（Lov） テーブルのタスク種別定義を参照)
        *   serverId: 対象サーバのID (文字列)
        *   exportStartDate: パラメータモーダルで入力された開始日 ("YYYY-MM-DD"形式の文字列)
        *   exportEndDate: パラメータモーダルで入力された終了日 ("YYYY-MM-DD"形式の文字列)
    *   ページコンポーネントは、生成した FormData を用いて createTaskAction サーバアクション を呼び出す。createTaskAction の入力パラメータと返り値の詳細は本設計書「タスク受付処理詳細」シートを参照してください。
    
    最終確認モーダルのキャンセルか×ボタンをクリックすると、createTaskAction サーバアクションを呼び出さずに最終確認モーダルを閉じて、入力した値はそのままで前のパラメータモーダルを表示する。

5.  **実行結果のUI反映**<br/>
    createTaskAction サーバアクション から CreateTaskActionResult オブジェクト（本設計書「タスク受付処理詳細」シートの返り値定義参照）が返却される。
    *   ページコンポーネントは、まずパラメータモーダルおよび最終確認モーダルを閉じる。
    *   CreateTaskActionResult の success フィールドが true の場合：情報ポップアップでmessageフィールドの内容（EMEC0025「タスクの実行を受け付けました。タスクのステータスはタスク一覧画面で確認してください。\n対象サーバ：{0}\nタスク種別：{1}」、{0}は対象サーバ名、{1}はタスク種別名で置換。タスク種別名はデータベース 値の一覧Lovテーブルのタスク種別定義から取得する。）をユーザーへ表示する。
    *   CreateTaskActionResult の success フィールドが false の場合：エラーポップアップでmessageフィールドの内容（例：終了日が開始日よりも前の日付の場合は EMEC0024「終了日は開始日以降の日付を指定してください。」）をユーザーへ表示する。

#### イベント12.2：「管理項目定義のインポート」選択時

1.  **パラメータ入力モーダル表示**<br/>
    ページコンポーネントは、管理項目定義インポートのパラメータ入力用モーダルダイアログ (以降、パラメータモーダル) を表示するための準備処理を行う：ページコンポーネントは、パラメータモーダルへ渡す情報として、対象サーバの名称、許可されるファイル拡張子リスト（['.csv']）、およびモーダルからの送信指示（選択されたファイルオブジェクトを含む）とクローズ指示を受け付けるための内部コールバック関数を準備する。
    準備ができた後、ページコンポーネントはパラメータモーダルを表示させる。

2.  **パラメータ入力と検証**<br/>
    ユーザーは表示されたパラメータモーダル内で、インポートする管理項目定義のCSVファイルをローカルから選択する。ファイルの入力ボックスは編集不可で、キーボードによる入力はできない。フォルダのアイコンをクリックして、ファイル選択ダイアログからファイルを選択する形で入力する。ファイル選択ダイアログは１つだけ、かつCSVファイルだけを選択できる。選択されたファイルのパスが入力ボックスに表示される。ファイル選択ダイアログの初期パスはデスクトップとなる。
    *   入力値が変更される都度、またはOKボタンが押下される際、パラメータモーダル内部でクライアントサイドの入力検証が実行される。
    *   検証ルール
        *   ファイルが選択されていること。未選択の場合、エラーメッセージ EMEC0016「{入力項目}を指定してください。」（{入力項目}は「ファイル」で置換）を表示する。
    *   クライアントサイド検証でエラーが検出された場合、該当するエラーメッセージがパラメータモーダル内に表示され、後続の処理は中断される。
    
    パラメータモーダルのキャンセルか×ボタンをクリックすると、パラメータモーダルを閉じてサーバ一覧画面に戻る。

3.  **最終確認モーダル表示**<br/>
    パラメータモーダルでのクライアントサイド検証が成功し、ユーザーがパラメータモーダルのOKボタンを押下すると、パラメータモーダルは選択されたファイルオブジェクトをページコンポーネントへコールバックする。
    ページコンポーネントは、受け取ったファイルオブジェクトと対象サーバ情報に基づき、汎用の最終確認モーダルダイアログ（以降、最終確認モーダル）を表示するための準備を行う：最終確認モーダルへ渡す情報として、タイトル（「管理項目定義のインポート」）、確認メッセージ（「{0}の管理項目定義をインポートします。\nよろしいですか？」、{0}は対象サーバ名で置換）を設定する。
    準備ができた後、ページコンポーネントは最終確認モーダルを表示させる。

4.  **バックエンド処理呼び出し**<br/>
    ユーザーが最終確認モーダルのOKボタンを押下すると、ページコンポーネントは createTaskAction サーバアクション の呼び出し処理を開始する。
    *   ページコンポーネントは、FormData オブジェクトを生成し、以下の情報を含める。
        *   taskType: 文字列 TASK_TYPE.MGMT_ITEM_IMPORT (データベース 値の一覧（Lov） テーブルのタスク種別定義を参照)
        *   serverId: 対象サーバのID (文字列)
        *   importFile: パラメータモーダルで選択された File オブジェクト
        *   originalFileName: 選択されたファイルの元の名称 (File オブジェクトの name 属性から取得)
    *   ページコンポーネントは、生成した FormData を用いて createTaskAction サーバアクション を呼び出す。
    
    最終確認モーダルのキャンセルか×ボタンをクリックすると、createTaskAction サーバアクションを呼び出さずに最終確認モーダルを閉じて、入力した値はそのままで前のパラメータモーダルを表示する。

5.  **実行結果のUI反映**<br/>
    createTaskAction サーバアクション から CreateTaskActionResult オブジェクトが返却される。
    *   ページコンポーネントは、まずパラメータモーダルおよび最終確認モーダルを閉じる。
    *   CreateTaskActionResult の success フィールドが true の場合：情報ポップアップでmessageフィールドの内容（EMEC0025「タスクの実行を受け付けました。タスクのステータスはタスク一覧画面で確認してください。\n対象サーバ：{0}\nタスク種別：{1}」、{0}は対象サーバ名、{1}はタスク種別名で置換。タスク種別名はデータベース 値の一覧Lovテーブルのタスク種別定義から取得する。）をユーザーへ表示する。
    *   CreateTaskActionResult の success フィールドが false の場合：エラーポップアップでmessageフィールドの内容（例：ファイルアップロード失敗時は EMEC0018「ファイルのアップロードに失敗しました。時間をおいてから再度実行してください。」、サーバサイドでのファイル形式検証失敗時は EMEC0017「無効なファイル形式です。CSVファイルを指定してください。」）をユーザーへ表示する。

#### イベント12.3：「管理項目定義のエクスポート」選択時

1.  **最終確認モーダル表示**<br/>
    ページコンポーネントは、管理項目定義エクスポートの汎用最終確認モーダルダイアログ（以降、最終確認モーダル）を表示するための準備処理を行う：最終確認モーダルへ渡す情報として、タイトル（「管理項目定義のエクスポート」）、確認メッセージ（「{0}の管理項目定義をエクスポートします。\nよろしいですか？」、{0}は対象サーバ名で置換）を設定する。
    準備ができた後、ページコンポーネントは最終確認モーダルを表示させる。

2.  **バックエンド処理呼び出し**<br/>
    ユーザーが最終確認モーダルのOKボタンを押下すると、ページコンポーネントは createTaskAction サーバアクション の呼び出し処理を開始する。
    *   ページコンポーネントは、FormData オブジェクトを生成し、以下の情報を含める。
        *   taskType: 文字列 TASK_TYPE.MGMT_ITEM_EXPORT (データベース 値の一覧（Lov） テーブルのタスク種別定義を参照)
        *   serverId: 対象サーバのID (文字列)
    *   ページコンポーネントは、生成した FormData を用いて createTaskAction サーバアクション を呼び出す。
    
    最終確認モーダルのキャンセルか×ボタンをクリックすると、createTaskAction サーバアクションを呼び出さずに、最終確認モーダルを閉じてサーバ一覧画面に戻る。

3.  **実行結果のUI反映**<br/>
    createTaskAction サーバアクション から CreateTaskActionResult オブジェクトが返却される。
    *   ページコンポーネントは、まず最終確認モーダルを閉じる。
    *   CreateTaskActionResult の success フィールドが true の場合：情報ポップアップでmessageフィールドの内容（EMEC0025「タスクの実行を受け付けました。タスクのステータスはタスク一覧画面で確認してください。\n対象サーバ：{0}\nタスク種別：{1}」、{0}は対象サーバ名、{1}はタスク種別名で置換。タスク種別名はデータベース 値の一覧Lovテーブルのタスク種別定義から取得する。）をユーザーへ表示する。
    *   CreateTaskActionResult の success フィールドが false の場合：エラーポップアップでmessageフィールドの内容（例：コンテナ実行中であれば EMEC0022「{0}に対するタスクを実行中のため実行できません。\n実行中のタスクが完了してから再度実行してください。」、{0}は対象サーバ名で置換）をユーザーへ表示する。