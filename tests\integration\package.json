{"name": "integration", "version": "1.0.0", "description": "jcs-endpoint-monorepo 的 Playwright 集成测试项目", "main": "index.js", "scripts": {"_comment1": "=== 自动模式（webServer 启用）===", "test": "npx playwright test", "test:debug": "playwright test --config=playwright.config.debug.ts", "test:headed": "npx playwright test --headed", "test:ui": "npx playwright test --ui", "_comment2": "=== 手动模式（webServer 禁用）===", "_comment3": "=== 通用工具 ===", "test:specific": "npx playwright test --grep", "show-report": "npx playwright show-report ../test-results/html-report", "show-trace": "npx playwright show-trace ../test-results/test-output", "clean-results": "rimraf ../test-results", "_comment4": "=== 构建检查 ===", "check-builds": "node scripts/check-builds.js", "check-builds:quiet": "node scripts/check-builds.js --quiet", "check-builds:json": "node scripts/check-builds.js --json", "_comment5": "=== Mock Server ===", "start-mock-server": "ts-node scripts/start-mock-server.ts", "mock-server:dev": "MOCK_SERVER_PORT=3001 ts-node scripts/start-mock-server.ts", "_comment6": "=== 调试模式 ===", "test:debug:headed": "playwright test --config=playwright.config.debug.ts --headed", "_comment7": "=== 脚本方式（兼容性）===", "e2e": "bash ./scripts/run-e2e-tests.sh", "e2e:headed": "bash ./scripts/run-e2e-tests.sh --headed", "e2e:debug": "bash ./scripts/run-e2e-tests.sh --debug", "_comment8": "=== Azurite 服务 ===", "start:azurite": "azurite --blobHost 0.0.0.0 --queueHost 0.0.0.0 --silent --location azurite-data --debug azurite-debug.log"}, "author": "", "license": "ISC", "devDependencies": {"@playwright/test": "^1.43.1", "@types/express": "^4.17.21", "@types/node": "^24.0.15", "azurite": "^3.31.0", "rimraf": "^5.0.1", "ts-node": "^10.9.0", "typescript": "^5.0.0"}, "dependencies": {"@prisma/client": "^6.12.0", "dotenv": "^17.2.0", "express": "^4.18.2", "iron-session": "^8.0.4"}}