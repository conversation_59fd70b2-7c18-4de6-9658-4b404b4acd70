/**
 * @file license-info.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

"use client";

import { useEffect } from "react";
import useLicense from "../hooks/use-license";
import { LicenseAPI, PORTAL_ERROR_MESSAGES } from "../lib/definitions";
import Spinner from "./spinner";

interface LicenseInfoProps {
  onError?: (error: string) => void;
}

// ライセンス情報コンポーネント
export default function LicenseInfo({ onError }: LicenseInfoProps) {
  const { data, isLoading, error } = useLicense();
  const fetchedLicense = data as LicenseAPI;
  useEffect(() => {
    if (data && data.error) {
      onError && onError(data.error);
    } else if (error) {
      onError && onError(PORTAL_ERROR_MESSAGES.EMEC0007);
    }
  }, [isLoading]);
  return (
    <table className="w-full text-sm font-medium text-left rtl:text-right text-gray-500 dark:text-gray-400">
      <tbody>
        <tr className="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
          <th
            scope="row"
            className="px-6 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white"
          >
            ライセンス種別:
          </th>
          <td className="px-6 py-2">
            {(!isLoading &&
              fetchedLicense &&
              fetchedLicense.type &&
              fetchedLicense.typeLov.value) || <Spinner />}
          </td>
        </tr>
        <tr className="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
          <th
            scope="row"
            className="px-6 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white"
          >
            有効期限:
          </th>
          <td className="px-6 py-2">
            {!isLoading && fetchedLicense && fetchedLicense.expiredAt
              ? new Date(fetchedLicense.expiredAt).toLocaleDateString()
              : "-"}
          </td>
        </tr>
        <tr className="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
          <th
            scope="row"
            className="px-6 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white"
          >
            ライセンス保有数:
          </th>
          <td className="px-6 py-2">
            {!isLoading && fetchedLicense ? (
              fetchedLicense.maxClients
            ) : (
              <Spinner />
            )}
          </td>
        </tr>
      </tbody>
    </table>
  );
}
