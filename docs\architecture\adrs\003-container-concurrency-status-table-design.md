# ADR-003: ContainerConcurrencyStatus表的设计决策

**状态 (Status)**: 已接受 (Accepted)

**决策者 (Deciders)**: chen

**决策日期 (Date)**: 2025-06-04

## 上下文 (Context)

JCS端点资产与任务管理系统需要执行针对特定目标Azure虚拟机（VM）上特定Docker容器的后台任务（例如，日志导出、配置导入/导出）。由于这些任务可能会修改容器状态或依赖独占访问容器内的资源，因此必须确保在任何给定时间，只有一个任务可以操作同一个目标容器，以防止并发冲突、数据不一致或任务执行失败。

这就要求系统具备一个机制来跟踪和管理每个目标容器的并发访问状态（例如，是空闲`IDLE`还是繁忙`BUSY`）。

## 决策 (Decision)

我们决定**保留一个独立的数据库表，名为`ContainerConcurrencyStatus`，专门用于跟踪和管理目标VM上各Docker容器的并发执行状态。**

该表的核心字段将包括：
*   `targetVmName`: 目标Azure虚拟机的名称（来源于`Server.azureVmName`）。
*   `targetContainerName`: 目标VM上受管理的Docker容器的名称（来源于`Server.dockerContainerName`）。
*   `status`: 容器当前的并发状态，主要值为 `IDLE` (空闲) 和 `BUSY` (繁忙)。
*   `currentTaskId`: 当状态为 `BUSY` 时，存储当前正在占用该容器的任务的 `Task.id`。
*   `lastStatusChangeAt`: 状态最后变更的时间戳。

**交互逻辑**:
1.  `TaskExecuteFunc` (任务执行函数) 在准备提交一个新的Runbook作业到Azure Automation之前，会查询`ContainerConcurrencyStatus`表。
    *   如果对应`(targetVmName, targetContainerName)`的记录不存在，则创建一条新记录，初始状态为`IDLE`。
    *   如果记录存在且状态为`IDLE`，则原子地（例如，在数据库事务内使用带条件的UPDATE或行锁）将其状态更新为`BUSY`，并记录下当前任务的`taskId`。只有成功获取到锁（即更新成功），`TaskExecuteFunc`才会继续提交作业。
    *   如果状态为`BUSY`，则表示容器繁忙，新任务将快速失败（例如，`TaskExecuteFunc`更新`Task`表状态为`COMPLETED_ERROR`并注明原因）。
2.  当任务最终完成（无论成功或失败）后，`StatusProcessorFunc`（处理来自`TaskStatusQueue`的消息）或相关的超时/错误处理函数（如`RunbookMonitorFunc`在中止超时作业后，或各DLQ处理函数）将负责将`ContainerConcurrencyStatus`表中对应容器的状态原子地更新回`IDLE`，并清除`currentTaskId`。

## 后果 (Consequences)

### 正面影响:

*   **明确的职责分离**: `ContainerConcurrencyStatus`表专注于运行时并发状态管理，与存储服务器配置信息的`Server`表分离。这符合单一职责原则，使得数据模型更清晰。
*   **优化的并发控制逻辑**: 对一个专门的状态表进行原子的“检查并设置”操作通常比在一个混合了配置和状态的大表上更容易实现和优化。数据库可以更有效地处理对这个小而专用的表的并发更新请求。
*   **降低对`Server`表的写争用**: `Server`表主要存储相对静态的配置信息，查询操作远多于更新操作。将高频更新的并发状态信息移出，可以减少对`Server`表的写锁定和潜在的性能瓶颈。
*   **可维护性和可测试性**: 独立的并发控制机制更容易理解、测试和维护。
*   **健壮的资源释放**: 多个组件（`StatusProcessorFunc`, `RunbookMonitorFunc`, DLQ处理函数）都有责任在任务结束或异常终止时释放并发锁，确保容器不会被永久锁定。

### 负面影响与风险:

*   **增加了一个数据表**: 相对于将状态字段合并到`Server`表，这增加了一个表，略微增加了数据模型的复杂性。
*   **数据一致性依赖于事务**: `TaskExecuteFunc`在获取锁（更新`ContainerConcurrencyStatus`）和创建/更新`Task`表记录时，这些操作应尽可能在同一数据库事务中完成，以保证原子性。同样，`StatusProcessorFunc`在更新最终任务状态和释放锁时也应考虑事务。
*   **记录生命周期管理**: 如`fs.v1.md`中所述，`ContainerConcurrencyStatus`表的记录通常不主动物理删除。虽然这在功能上通常不是问题，但长期来看可能需要考虑对不再活跃的VM/容器记录的归档或清理策略（尽管FS认为这不是大问题）。

## 备选方案 (Considered Options)

1.  **将并发状态字段合并到 `Server` 表**:
    *   在`Server`表中增加`concurrencyStatus`和`currentTaskId`等字段。
    *   **优点**: 表面上减少了一个表。
    *   **缺点**:
        *   增加了`Server`表上并发原子更新的复杂性（需要行级锁或复杂的乐观锁）。
        *   混合了配置数据和高频变化的运行时状态数据，可能影响`Server`表的性能和索引维护。
        *   数据职责不够清晰。
        *   管理员修改`Server`配置（如容器名）与运行时状态更新之间可能存在潜在冲突。

**最终选择保留独立的`ContainerConcurrencyStatus`表，是基于其在职责清晰性、并发控制实现的健壮性、以及最小化对核心配置表（`Server`表）的写干扰方面的综合优势。** 这种设计被认为是更稳健和可扩展的。

## 更多信息 (More Information)

*   此决策与`fs.v1.md`中对`[コンテナ実行状態](ContainerConcurrencyStatus)`表的详细描述一致。
*   `TaskExecuteFunc`, `StatusProcessorFunc`, `RunbookMonitorFunc` 以及各DLQ超时处理函数的设计都将依赖此表来实现对目标容器的独占访问控制。

---
