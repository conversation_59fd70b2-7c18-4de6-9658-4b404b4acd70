# 汎用セキュリティ開発チェックリスト

## 1. データチェック機能

- [ ] **1-1**: 入力値の妥当性をチェックする機能は、サーバ側アプリケーションには必ず組み込む。クライアント側はどちらでも構わない。なお、入力値のチェック機能は、一元化する（チェックする項目が同じ場合は、１つのモジュールで処理する）。
- [ ] **1-2**: ユーザ入力フォームでユーザID・パスワードなど個人情報・機密情報を送信する場合は、GETメソッドではなくPOSTメソッドで送信する。URLパラメータを使用する場合は、情報漏えい防止のため、URLパラメータには秘密にすべき情報（例: a) ユーザIDとパスワード, b) クレジットカード番号, c) 個人情報, d) プライベートデータ, e)セッションID）を格納しないようにする。
- [ ] **1-3**: 送られてきたデータは、全て妥当性をチェックする（データのフォーマットだけではなく、受け付けるべき値として妥当かもチェックする）。例えば、入力ボックス内の値、ラジオボタン項目の値、チェックボックス項目の値、hiddenフィールドの値、Cookieの値などをチェックする。
- [ ] **1-4**: ユーザに送ったデータはすべて改ざんされて送り返される可能性がある。このため、ユーザのステータスなどセッション管理上重要な情報はユーザ側に送らず、サーバ側でセッション変数に格納して管理する。特に、hiddenフィールドやCookieの情報に注意する。
- [ ] **1-5**: モジュール毎にデコード処理を行うと、不要なデコード処理が入り込みやすいため、デコード処理は一元化する。
- [ ] **1-6**: 入力文字内のNULL文字（\0, %00）は排除する。URLのリクエストパラメタに「%00」が含まれていたら排除する。（「%00」がデコードされると「0x00」に変換され、OSやPerl、C/C++で作成したモジュールでは、「0x00」をNULL文字と解釈するため、予想外のデータが読み出される可能性がある。）
- [ ] **1-7**: HTTPレスポンスヘッダのContent-Type フィールドに文字コード(charset)を明示的に指定する。例：「Content-Type:text/html; charset=UTF-8」
- [ ] **1-8**: 入力された文字に対するエスケープ処理（サニタイジング）は、リクエストを受け取った時ではなく、HTML生成時に行う。エスケープ処理（サニタイジング）は、一元化し、`'` → `&#39;` / `""` → `&quot;` / `>` → `&gt;` / `<` → `&lt;` / `%` → `&#37;` / `(` → `&#40;` / `)` → `&#41;` / `;` → `&#59;` / `&` → `&amp;` / `+` → `&#43;` のように行う。
- [ ] **1-9**: 入力チェック機能をWebアプリケーションに実装し、条件に合わない値を入力された場合は、処理を先に進めず、再入力を求めるようにする。入力文字のチェックは、入力を許可する文字のパターン（ホワイトリスト）を作成し、それ以外は拒否して処理を中止する。
- [ ] **1-10**: タグの属性値は「""（ダブルクォート）」でくくる。
- [ ] **1-11**: 次の部分にはユーザが入力した文字を埋め込まない: URL属性、イベントハンドラ属性（onで始まるタグ属性）、`<SCRIPT>`タグ、コメントタグ、スタイル属性、外部スタイルシートへのリンク。
- [ ] **1-12**: URL を出力するときは、「http://」や「https://」で始まるURLのみを許可するようにする。許可するURLは、ホワイトリストを作成して管理する。
- [ ] **1-13**: DOM操作の際、意図しないDOMツリーの変更を避けるため、innerHTML等での操作ではなく、createElement()、setAttribute()、createTextNode()などのDOM操作用のメソッドやプロパティを使用する。
- [ ] **1-14**: 出力内容に応じたエスケープ処理を行う。例えばsrc属性やhref属性の値としてURLを出力する際はencodeURIComponent()を使用してURLエンコードする。
- [ ] **1-15**: SSI（Server Side Include）を使用する場合、入力文字のチェックを許可する文字のパターン（ホワイトリスト）で行い、それ以外は拒否して処理を中止する。
- [ ] **1-16**: 入力データをHTTPレスポンスヘッダに書き出す際、そこに含まれる改行コード（Cr および Lf）がそのまま出力されないようにする。具体的にはHTTPヘッダ内に出力する値にURLエンコーディングを施す。
- [ ] **1-17**: プログラムから別プログラムの起動を避ける。やむを得ない場合は、内部でシェルを呼び出さないAPIを使用する。内部でシェルを呼び出せるAPIの使用は避ける。
- [ ] **1-18**: 外部から取り込んだデータを元に内部コマンドや別のプログラムを起動する場合、入力文字のチェックは、入力を許可する文字のパターン（ホワイトリスト）を作成し、それ以外は拒否して処理を中止する。
- [ ] **1-19**: アップロード等で取り込んだ画像データが想定した形式であることをチェックする（Content-typeチェックだけでは不十分）。画像データがスクリプトとして実行されないように、Webサーバへのリクエストで直接アクセスされない場所に格納し、画像応答スクリプトを介して表示するなどの対策を講じる。
- [ ] **1-20**: LDAPを使用する場合、入力文字のチェックは許可する文字のパターン（ホワイトリスト）を作成して行い、それ以外は拒否するか、特殊文字のエスケープ処理を行ってからLDAPクエリを発行する。
- [ ] **1-21**: ファイルを開く際、固定のディレクトリを指定し、かつファイル名にディレクトリ名が含まれないようにする。`basename()`などを利用してディレクトリ名を取り除く。
- [ ] **1-22**: ファイル名を指定した入力パラメタの値から、OSのパス名解釈でディレクトリを指定できる文字列（`/`, `../`, `..\` 等）を検出した場合は、処理を中止する。デコード処理が完了した後にチェックを行う。
- [ ] **1-23**: SQL文はユーザが入力したデータを定数としてSQL文中に埋め込むのではなく、バインド機構を使用する。
- [ ] **1-24**: (バインド機構が使用できない場合) DBMSアクセスをすべてストアドプロシジャ呼び出しで実装する。
- [ ] **1-25**: (文字列連結でSQLを構成する場合) 特殊文字（`'`, `;` 等）を適切に処理（入力禁止またはエスケープ）する。
- [ ] **1-26**: XMLデータベースを使用する場合、入力文字のチェックを許可する文字のパターン（ホワイトリスト）で行うか、特殊文字をエスケープ処理してからXPathクエリを発行する。
- [ ] **1-27**: 監査ログ出力時に、ファイル名などに含まれる改行やタブなどの特殊文字をチェックして排除する。
- [ ] **1-28**: 問い合わせフォーム等から入力される文字を埋め込んでメールを作成する場合、メールヘッダ（To, Cc, Bcc, Subjectなど）の値は固定化するか、改行コードの入力を禁止する。Webアプリケーションの実行環境や言語に用意されているメール送信用APIを使用する。
- [ ] **1-29**: プログラムの動作に大きな影響を与える環境変数の値がすべて所定の仕様を満たしていることを厳密に検査する。

## 2. エラー処理

- [ ] **2-1**: エラー発生時の理由を説明するメッセージは最小限にする。内部エラー情報をそのまま表示しない。
- [ ] **2-2**: エラーの詳細情報は、（セキュリティで保護された）監査ログにのみ記録する。ユーザには知らせない。
- [ ] **2-3**: エラーメッセージには、ユーザの存在、関数名などシステムの内部構造を示す情報は表示しない。
- [ ] **2-4**: サーバのエラー情報やスタックトレースはクライアントに送信しない。

## 3. 暗号化機能

- [ ] **3-1**: 暗号処理等に使用する乱数（暗号鍵や初期ベクトル等）には予測不可能な乱数を使う。予測不可能な乱数とは、FIPS 140認証を取得したライブラリなどが提供するものを指す。Cランタイムのrand関数のような予測可能なものは使用しない。
- [ ] **3-2**: 該当する送信データだけをTLSにより暗号化処理を行うのではなく、入力操作を行うログイン画面ページや問合せページからTLSによる暗号化処理を行う。

## 4. アクセス制御

- [ ] **4-1**: ファイルを作成する際は、そのファイルに対するアクセス権も同時に設定し、必要なユーザにのみアクセス権限を与える。
- [ ] **4-2**: Webアプリケーションにおけるアクセス認可は、1.ログイン有無、2.ページ単位の許可、3.パラメタ単位の許可の3段階の構成で行う。

## 5. セッション管理機能

- [ ] **5-1**: セッションIDは、同一ユーザであってもアクセスごとに異なるランダムな値（十分な長さで類推が困難）にする。Webアプリケーションサーバソフトなどに備わっているセッション管理機構を使用する。
- [ ] **5-2**: 一定時間、クライアントからの通信（操作）がない場合、セッションを強制的に切断する。セッションの有効期限は有限にする。
- [ ] **5-3**: セッションIDは、Cookieに格納するか、または、POSTメソッドのhiddenフィールドに格納して受け渡す。URLパラメタに格納しない。
- [ ] **5-4**: ログインの前に発行したセッションIDは、ログイン後には使用しない。ログイン成功後に、新しくセッションを開始するようにする。
- [ ] **5-5**: セッションとユーザとの対応付けはサーバで行う。ユーザの状態・情報はセッションIDには持たせない。
- [ ] **5-6**: 通信のセッション終了時にセッションのデータは完全に削除する。処理開始時にはデータ（変数）に初期値を設定する。
- [ ] **5-7**: Cookieは必ずサーバサイドで生成する。
- [ ] **5-8**: Cookieの有効ドメインを適切に設定する。TLSの場合、セッションIDを格納するCookieにはsecureフラグを立て、非TLS通信で送信されないようにする。

## 6. パスワード管理機能

- [ ] **6-1**: パスワードは平文（生データ）ではなく、ハッシュ値を管理する。レインボー攻撃から保護するため、パスワードごとにランダムなソルトを付けてからハッシュ値を求める。ハッシュ値とソルトはそれぞれ別の場所に分けて管理し、認証時はハッシュ値同士を比較する。ハッシュアルゴリズムはSHA-256以上の強度のものを使用する。

## 7. メモリ管理

- [ ] **7-1**: (C/C++/Java/.NET) メモリ不足でエラーになった場合に例外をキャッチできない関数は使用しない、またはメモリリークに注意して実装する。
- [ ] **7-2**: 確保したリソースは、使用後に必ず解放する。
- [ ] **7-3**: 個人情報はユーザの入力後、アプリケーションが取得し、使用後はすぐに破棄してメモリをクリアする。
- [ ] **7-4**: 1000件以上データのある表に対し、パフォーマンスの低い検索（非インデックス列、全件検索など）を行わない。

## 8. ファイル送受信機能

- [ ] **8-1**: ファイルをダウンロードさせる際、一時ファイルをサーバに作成・保存せず、極力メモリ中で処理を行う。やむを得ず一時ファイルを作成する場合はセキュアな方法で行う。
- [ ] **8-2**: クライアントからアクセスされない拡張子は無効にする。
- [ ] **8-3**: アップロードしたファイルの内容をWeb サーバに保存せず、バイナリデータ型データとしてデータベースに保存する。

## 9. 情報漏洩防止対策

- [ ] **9-1**: 入力後の応答メッセージが認証情報の推測のヒントとならないようにする（例：「ユーザIDまたはパスワードが間違っています」）。
- [ ] **9-2**: 個人情報や機密情報はメモリ中に保持したりキャッシュせず、必要な時にデータベースなどから取り出す。
- [ ] **9-3**: ログインページ以降のページはキャッシュできないようにサーバ側でWebブラウザのキャッシュ機能を禁止する（`Cache-Control: private, no-store, no-cache, must-revalidate` 等のヘッダを設定）。
- [ ] **9-4**: 重要な情報は、メモリロック（mlock, VirtualLock）を使用してページング領域にページアウトされないようにする。
- [ ] **9-5**: URLのパラメタ部分に個人情報・機密情報を入れ、外部サイトへのリンクが存在する場合、リダイレクタを経由してリンク先に飛ぶようにする。
- [ ] **9-6**: ソースコードにデータベースなどへアクセスするためのユーザID、パスワード、暗号鍵などの秘密情報を埋め込まず、非公開ディレクトリ上に置いた設定ファイルから読み込む。

## 10. マルチタスク構造のプログラム

- [ ] **10-1**: 複数プロセスが並列に動作した場合にタイミングによって処理や結果が異ならないように設計する（競合状態の防止）。
- [ ] **10-2**: マルチスレッドのアプリケーション基盤（JavaサーブレットやASP.NETなど）では、不要な共有変数は使用せず、ローカル変数を使用する。
- [ ] **10-3**: ローカル変数でも、クラスの実装によっては、スレッドセーフではない挙動をする場合があるため注意する。
- [ ] **10-4**: 共有変数を使用する必要がある場合は、情報が他の処理によって勝手に書き換えられてしまわないよう排他制御を行う。
- [ ] **10-5**: 機密情報や個人情報を格納するオブジェクト、変数は、使用前に初期化する。

## 11. Windows全般

- [ ] **11-1**: (IIS/ISAPI) ISAPIアプリケーションではバッファオーバーフローや標準表記バグに注意し、不要な機能は無効化、ユーザ認証、綿密なレビューを行う。
- [ ] **11-2**: WindowsのサービスプロセスではUIモジュールとの通信にインパーソネーションした名前付きパイプを使う。
- [ ] **11-3**: インパーソネーションしたスレッドで子プロセスを生成する場合、CreateProcessAsUserを使ってインパーソネーションさせる。
- [ ] **11-4**: パス名をパラメタとして受け取るプログラムでは、Windowsの柔軟なパス名解釈の悪用を防止するため、パス名の正規化と検証（`\\?\`プレフィックスの使用、`GetFullPathName`, `GetLongPathName`等）を行う。
- [ ] **11-5**: パス名をパラメタとして受け取るプログラムでは、末尾のドット、大文字小文字の区別、UNC共有、メールスロット等、ファイル名に関する誤りに注意する。
- [ ] **11-6**: サーバ名やユーザ名に基づいて処理を振り分けるプログラムでは、名前を標準表記に変換してから確認する。
- [ ] **11-7**: パス名にドライブ文字を表す以外のコロン（`:`）が含まれていたら、NTFSの代替データストリームの不正使用防止のため、不当なパスとして排除する。
- [ ] **11-8**: CreateProcess等のプロセス実行APIを使用する場合、実行ファイルのパスにスペースが含まれる場合はダブルクォートで囲むなど、意図しないプログラムが起動されないようパラメタ指定に注意する。
- [ ] **11-9**: インパーソネーション関数が失敗した場合は、エラーを発生させ、クライアントの要求処理を実行しない。
- [ ] **11-10**: ディレクトリ階層を再帰的に処理するアプリケーションでは、ディレクトリジャンクションを判別し、無限ループや誤ったファイル削除を防ぐ。
- [ ] **11-11**: 共有ライブラリを使用する場合、設定されているPATH環境変数に依存せず、プログラムでPATH環境変数を設定し直す。
- [ ] **11-12**: 一時ファイルは乱数等を使用してランダムなファイル名で作成する。
- [ ] **11-13**: 拡張子に基づいてプログラムを自動起動する場合、ShellExecute()の利用を避けるか、利用できる拡張子をホワイトリストで限定する、ファイル内容を確認する、ユーザに警告する等の対策を行う。
- [ ] **11-14**: ファイルオープン処理を行う際は、「ファイルの存在」と「一般ファイル」であること（予約デバイス名でない）を確認する。

## 12. UNIX/Linux全般

- [ ] **12-1**: clearenv()を呼び出して環境変数をクリアする。
- [ ] **12-2**: PATH環境変数にはカレントディレクトリ(.)を指定しない。プログラムは絶対パス名で呼び出す。
- [ ] **12-3**: 共有ライブラリを使用する場合、設定されているPATH環境変数に依存せず、プログラムでPATH環境変数を設定し直す。
- [ ] **12-4**: シェルは最新版のbashを使用し、古いシェルでは環境変数IFSを再設定する。
- [ ] **12-5**: ネットワークサービスはforkで子プロセスを生成して複数クライアントからの接続を受け付ける。
- [ ] **12-6**: 別プログラムの起動は、system()やpopen()を避け、fork()とexecve()等を組み合わせて行う。
- [ ] **12-7**: 子プロセスに継承するファイル記述子と環境変数は、close-on-execフラグやexecve等で必要最小限に制限する。
- [ ] **12-8**: setuidビットを立てたroot権限プログラムは設けない。
- [ ] **12-9**: setgidを使ってroot権限を持たない専用グループを作成し、プログラムをその専用グループに入れる。
- [ ] **12-10**: 子プロセスにroot権限が不要な場合、親プロセスは権限を放棄して子プロセスを起動する。
- [ ] **12-11**: プログラムが作成する一時ファイルは、共通のディレクトリは使用せず、プログラム専用のテンポラリディレクトリを作成し、そこに作成する。
- [ ] **12-12**: 一時ファイルの作成には、mkstemp関数またはtmpfile関数を使用する。tmpnam, tempnam, mktempは脆弱なので使用しない。
- [ ] **12-13**: エラーによる削除失敗に備え、一時ファイルは、作成と同時にunlink（削除）する。
- [ ] **1-14**: setrlimit()を使用し、コアファイルの生成を禁止して、重要な情報がコアファイルから漏洩することを防ぐ。
- [ ] **12-15**: OSコマンドインジェクション防止のため、エスケープ処理に使用する記号は、 `""`, `'`, `\` 以外の記号にする。
- [ ] **12-16**: ファイルを開く際、lstat→open→fstatの組み合わせでシンボリックリンク攻撃やファイルレースコンディションを防止する。
- [ ] **12-17**: ファイルを作成する際は、同名のファイルやシンボリックリンクがないことを確認し、creat()ではなくopen()で行う。
- [ ] **12-18**: ファイルレースコンディション問題を避けるため、パス名を引数に取るAPI（stat, chmod等）の代わりにファイル記述子を引数に取るAPI（fstat, fchmod等）を使用する。

## 13. C/C++

- [ ] **13-1**: データの転記を行う箇所は、strncpy等の書き込み上限バイト数を指定できる関数を使用し、バッファオーバーフローを防止する。
- [ ] **13-2**: 書き込み上限バイト数を指定できないライブラリ関数を使用する場合、上限バイト数でバッファを確保して使用する。
- [ ] **13-3**: バッファへの書き込みをループ処理で行う場合、ループの終了条件で必ずバッファ境界をチェックする。
- [ ] **13-4**: バッファオーバーフロー対策がなされているライブラリ（Safe CRT等）やコンパイラオプション（/GS, -fstack-protector等）を使用する。
- [ ] **13-5**: データのサイズをあらかじめ決められない場合は、malloc()等を使用して動的にメモリを確保する。
- [ ] **13-6**: printf系関数の書式引数には、外部から入力したデータは原則用いない。やむを得ない場合でも%n書式は許可しない。
- [ ] **13-7**: (GCC) コンパイルオプション -Wformat=2 を指定して、フォーマット文字列攻撃の脆弱性を警告させる。
- [ ] **13-8**: (GCC) コンパイルオプション -D_FORTIFY_SOURCE=2 を指定して、%n書式の使用をエラーとして検知させる。
- [ ] **13-9**: ASLR（アドレス空間配置のランダム化）やデータ実行防止（DEP）機能を利用して、フォーマット文字列攻撃を防ぐ。
- [ ] **13-10**: 整数オーバーフロー攻撃を防止するため、できる限り符号なし整数を使い、事前・事後検査を行う。
- [ ] **13-11**: メモリリークを避けるため、構造化例外処理（try-catch）を用いて実装する。
- [ ] **13-12**: リソースを確保する場合は、RAII（Resource Acquisition Is Initialization）パターンを活用し、コンストラクタで確保、デストラクタで解放されるようにする。
- [ ] **13-13**: メモリリークが発生しないかツールを使ってチェックする。
- [ ] **13-14**: ポインタは宣言時にNULLで初期化する。
- [ ] **13-15**: malloc等の呼び出しの直前で代入先ポインタのメモリ解放を行う。
- [ ] **13-16**: ポインタが宣言されているスコープの中の処理の最後のタイミングでポインタをクリーンナップする。

## 14. Java

- [ ] **14-1**: セキュリティポリシーファイルで、必要なクラスのみにパーミッションを与える。
- [ ] **14-2**: 原則としてどのクラスにも大きな特権（AllPermission等）を与えない。
- [ ] **14-3**: シリアル化されたオブジェクトファイルの出力先フォルダやDBテーブルへのアクセス権を適切に設定する。
- [ ] **14-4**: シリアル化されたオブジェクトが信頼できないネットワークを流れる場合は、通信を暗号化する。
- [ ] **14-5**: (Java Applet) 作成するクラスを特権部分と一般部分に分ける。
- [ ] **14-6**: 設定されている環境変数CLASSPATHに依存せず、プログラムでCLASSPATHを設定し直す。
- [ ] **14-7**: マルチスレッド処理の場合、共通リソースの処理のスレッド同期をとるためにsynchronizedを使用する。

## 15. ASP.NET

- [ ] **15-1**: VIEWSTATEに個人情報や機密情報を入れる場合は、`EnableViewStateMAC="true"`と`machineKey validation="3DES"`を設定して暗号化を有効にする。

## 16. ASP（Active Server Pages）

- [ ] **16-1**: FileSystemObjectでファイルをオープンする際は、Server.MapPathメソッドの結果として得られた物理パスを与える。
- [ ] **16-2**: Requestオブジェクトを使用する際は、コレクション（QueryString, Form等）の指定を省略しない。

## 17. Perl

- [ ] **17-1**: ファイルの読み書きには、openではなく、sysopenを使用する。
- [ ] **17-2**: open, glob, execなどを使用する箇所を限定し、与える引数にシェルコマンドが混入しないようにする。
- [ ] **17-3**: Taintモードを使用し、外部から入力されたデータを追跡し、正規表現で洗浄する。

## 18. インストーラ

- [ ] **18-1**: インストール処理を行うプログラムが作成するディレクトリやファイルに設定するアクセス権は必要最小限にする。
- [ ] **18-2**: インストール後に一時ファイルをすべて削除する。
- [ ] **18-3**: 機密性の高いファイルに対してアクセス権を設定する。
- [ ] **18-4**: インストール時にOSや他アプリケーションの既存の設定を勝手に変更しない。

## 19. コンパイル

- [ ] **19-1**: アプリケーションを本番運用用にビルドする際は、デバッグモードをオフにする。
- [ ] **19-2**: アプリケーションを本番運用用にビルドする際は、デバッグ情報(デバッグシンボル)は含まない指定にする。
- [ ] **19-3**: コンパイル時の最適化によるメモリクリア命令（memset等）の削除を防止する。

## 20. Webページの設計

- [ ] **20-1**: ユーザに対してアドレスバー・ステータスバーを隠したり、右クリックを禁止したりせず、WebページのURL情報を表示してユーザ自身がアクセスしているWebページが本物か否か確認できるようにする。
- [ ] **20-2**: FRAMEやIFRAMEは極力利用しない。特にTLSによる保護を必要とするセキュアページでは利用しない。
- [ ] **20-3**: 表示しているページがTLSで保護されていることをユーザが確認できるように、httpsのページにhttpコンテンツは混在させない。
- [ ] **20-4**: Webブラウザ上の｢戻る｣機能を使用した際、個人情報やユーザID、パスワード等が残らないようにする。また、ログアウト後に｢戻る｣機能を使用した際もログイン時を想定した機能が操作できないようにする。
- [ ] **20-5**: URLリダイレクトは原則使用しない。使用する場合はリダイレクト先URLの作成時に外部パラメタを利用しない。
- [ ] **20-6**: ヘッダの出力を直接行わず、Webアプリケーションの実行環境や言語に用意されているヘッダ出力用API を使用する。

## 21. 設計一般

- [ ] **21-1**: 一般ユーザの処理と特権処理を明確に分けて設計を行う。
- [ ] **21-2**: セキュリティを考慮しなければならないインタフェースと通常のインタフェースを明確に分けて設計する。
- [ ] **21-3**: 重要なデータをモジュール間で受け渡しをしない等、セキュリティを考慮したモジュール分割設計をする。
- [ ] **21-4**: 可能なら部品やモジュールは、セキュリティ設計されたもの（安全性が確認されているもの）を使用する。
- [ ] **21-5**: 処理（入力、DB出力、表示等）をモジュールや関数に分けた設計をする。
- [ ] **21-6**: プロセスがリソースを利用する場合はロック時間を出来る限り短くし、ロック順序を考慮してデッドロックを防止する。
- [ ] **21-7**: Webアプリケーションでは安全性の確認されていないサンプルプログラムやテンプレートは利用しないこと。

## 22. クライアントを含めたシステム全体のセキュリティ設計

- [ ] **22-1**: (IE) ActiveXコントロールを提供する場合は署名し、「未署名のActiveXコントロールのダウンロード」を有効にする設定を強要しない。
- [ ] **22-2**: (IE) プログラムをダウンロードさせる場合は署名し、「ダウンロードしたプログラムの署名を確認する」をオフにする設定を強要しない。

## 23. Node.js/React

- [ ] **23-1**: クライアントからの入力に対して適切な検証とサニタイズが行われているか確認し、悪意のある入力と攻撃を防ぐ。
- [ ] **23-2**: 動的なコンテンツをレンダリングする際に適切なエスケープやサニタイズを行っているか確認し、クロスサイトスクリプティング（XSS）攻撃を防ぐ。
- [ ] **23-3**: データベースとのやり取りにおいて、パラメータ化されたクエリやバインド機構を使用しているか確認し、SQL インジェクション攻撃を防ぐ。
- [ ] **23-4**: アップロードされたファイルの種類を検証し、ファイルを安全な場所に保存することで、任意のコード実行やファイル上書き攻撃を防ぐ。
- [ ] **23-5**: ユーザー状態の変更に関与する操作に適切な CSRF 対策措置が行われているか確認する。
- [ ] **23-6**: セッション ID の生成と保存方法、およびセッションの有効性検証を確認する。
- [ ] **23-7**: データの転送と保存プロセスで適切な暗号化と保護措置が行われているか確認する。
- [ ] **23-8**: 適切なエラーハンドリングメカニズムとログ記録が実装されているか確認し、機密情報の漏洩を防ぐ。
- [ ] **23-9**: ユーザーが許可されていないリソースにアクセスできないよう、適切な権限とアクセス制御が行われているか確認する。
- [ ] **23-10**: 使用しているサードパーティライブラリのセキュリティと更新頻度を確認し、既知の脆弱性のあるライブラリを避ける。