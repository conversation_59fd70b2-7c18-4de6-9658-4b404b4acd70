# JCS Endpoint NextJS 测试修复指南

## 概述

本文档提供了修复当前失败测试用例的详细步骤和代码示例。

## 修复优先级和时间估算

| 优先级 | 测试文件 | 失败用例数 | 预计修复时间 | 难度 |
|--------|----------|------------|--------------|------|
| 🔴 高 | operation-log-export-modal.test.tsx | 4 | 30分钟 | 低 |
| 🟡 中 | confirm-modal.test.tsx | 3 | 1小时 | 中 |
| 🟡 中 | servers/table.test.tsx | 3 | 1.5小时 | 中 |
| 🟡 中 | tasks/actions-modals.test.tsx | 7 | 2小时 | 中 |

## 详细修复步骤

### 1. 修复 OperationLogExportModal 测试 (高优先级)

**问题**: Mock 配置缺少 `FORM_FIELD_NAMES` 定义

**修复步骤**:

1. 打开 `__tests__/ui/servers/modals/operation-log-export-modal.test.tsx`
2. 找到第55-61行的 mock 配置
3. 添加 `FORM_FIELD_NAMES` 到 mock 对象中

**修复代码**:
```typescript
// 修改前 (第55-61行)
jest.mock("@/app/lib/definitions", () => ({
  PORTAL_ERROR_MESSAGES: {
    EMEC0016: "{0}を指定してください。",
    EMEC0024: "終了日は開始日以降の日付を指定してください。",
    EMEC0020: "{0}日を超える期間が指定されました。{1}日以内の期間を指定して再実行してください。",
  },
}));

// 修改后
jest.mock("@/app/lib/definitions", () => ({
  PORTAL_ERROR_MESSAGES: {
    EMEC0016: "{0}を指定してください。",
    EMEC0024: "終了日は開始日以降の日付を指定してください。",
    EMEC0020: "{0}日を超える期間が指定されました。{1}日以内の期間を指定して再実行してください。",
  },
  FORM_FIELD_NAMES: {
    START_DATE: "開始日",
    END_DATE: "終了日",
    MGMT_ITEM_CSV_FILE: "ファイル",
    MAX_DAYS: "最大日数",
  },
}));
```

### 2. 修复 ConfirmModal 测试

**问题**: 测试期望的按钮文本与实际渲染不匹配

**诊断步骤**:
1. 在测试中添加 `screen.debug()` 查看实际渲染内容
2. 检查 ConfirmModal 组件的按钮渲染逻辑

**修复示例**:
```typescript
// 在失败的测试中添加调试代码
it("正常系: モーダル基本UI要素の表示", () => {
  render(<ConfirmModal {...mockProps} />);
  
  // 添加调试输出
  screen.debug();
  
  expect(screen.getByText("確認")).toBeInTheDocument();
  expect(screen.getByText("この操作を実行しますか？")).toBeInTheDocument();
  
  // 可能需要修改按钮查找方式
  // 原来: expect(screen.getByRole("button", { name: "OK" })).toBeInTheDocument();
  // 修改为: expect(screen.getByRole("button", { name: /OK|確認/ })).toBeInTheDocument();
});
```

### 3. 修复 ServersTable 测试

**问题**: 找不到 "タスクを選択" 文本和按钮

**诊断步骤**:
1. 检查 ServersTable 组件的当前实现
2. 确认任务下拉菜单的实际渲染内容
3. 检查组件是否需要特定的 props 才能渲染任务选择功能

**可能的修复方案**:
```typescript
// 检查组件是否需要特定条件才显示任务选择
const mockPropsWithTasks = {
  ...mockProps,
  // 可能需要添加特定的服务器类型或权限
  servers: [
    {
      ...mockServer,
      serverType: 'GENERAL_MANAGER', // 或其他需要的类型
    }
  ],
  // 或者添加权限相关的 props
  hasTaskPermission: true,
};

render(await ServersTable(mockPropsWithTasks));
```

### 4. 修复 TaskActionsModals 测试

**问题**: 找不到预期的按钮和文本内容

**诊断步骤**:
1. 检查 TaskActions 组件使用的模态框组件
2. 确认模态框的按钮文本和消息内容
3. 检查是否使用了新的 ConfirmModal 组件

## 通用修复策略

### 1. 调试失败测试的通用方法

```typescript
// 在失败的测试中添加调试代码
it("失败的测试用例", () => {
  render(<Component {...props} />);
  
  // 输出完整的 DOM 结构
  screen.debug();
  
  // 输出所有可访问的角色
  console.log('Available roles:', screen.getAllByRole(/.*/).map(el => el.tagName));
  
  // 输出所有文本内容
  console.log('All text content:', document.body.textContent);
  
  // 然后根据实际输出调整测试断言
});
```

### 2. 更新测试断言的策略

```typescript
// 使用更灵活的查询方式
// 原来: screen.getByRole("button", { name: "OK" })
// 改为: screen.getByRole("button", { name: /OK|確認|实行/ })

// 使用 queryBy 而不是 getBy 来避免抛出错误
// 原来: expect(screen.getByText("某文本")).toBeInTheDocument();
// 改为: expect(screen.queryByText("某文本")).toBeInTheDocument();

// 使用更宽松的文本匹配
// 原来: screen.getByText("完全匹配的文本")
// 改为: screen.getByText(/部分匹配的文本/)
```

### 3. Mock 配置最佳实践

```typescript
// 确保 mock 包含所有必要的导出
jest.mock("@/app/lib/definitions", () => ({
  // 包含原始模块的所有导出
  ...jest.requireActual("@/app/lib/definitions"),
  // 然后覆盖需要 mock 的部分
  PORTAL_ERROR_MESSAGES: {
    // mock 的错误消息
  },
}));
```

## 验证修复的步骤

### 1. 单个测试文件验证
```bash
# 运行特定测试文件
npm test -- __tests__/ui/servers/modals/operation-log-export-modal.test.tsx

# 运行特定测试用例
npm test -- --testNamePattern="開始日未入力時のバリデーションエラー"
```

### 2. 完整测试套件验证
```bash
# 运行所有测试
npm test

# 运行测试并生成覆盖率报告
npm test -- --coverage
```

### 3. 监控模式
```bash
# 在开发过程中使用监控模式
npm test -- --watch
```

## 预防未来测试失败的建议

### 1. 建立测试维护流程
- 组件修改时同时更新相关测试
- 定期运行完整测试套件
- 在 CI/CD 中集成测试检查

### 2. 改进测试编写质量
- 使用更稳定的查询方式（data-testid）
- 避免依赖具体的文本内容
- 使用页面对象模式组织测试代码

### 3. 增强测试工具
- 考虑使用 Testing Library 的用户事件库
- 添加视觉回归测试
- 集成端到端测试框架

## 修复完成检查清单

- [ ] OperationLogExportModal mock 配置已修复
- [ ] ConfirmModal 按钮查找问题已解决
- [ ] ServersTable 任务选择功能测试已修复
- [ ] TaskActionsModals 模态框测试已修复
- [ ] 所有测试用例通过
- [ ] 测试覆盖率保持在合理水平
- [ ] 文档已更新

---

**修复指南版本**: v1.0  
**最后更新**: 2025-01-15
