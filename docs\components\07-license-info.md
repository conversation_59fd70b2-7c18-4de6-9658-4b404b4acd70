# 组件：许可证信息 (License Information)

## 1. 概要 (Overview)

### 1.1 目的 (Purpose)
本组件旨在为“JCS 端点资产与任务管理系统”的已登录用户（特指顾客系统管理员）提供查看其当前所签订契约的许可证相关详细信息的功能。这些信息主要包括许可证的类型（如产品版、评估版）、服务的有效期限以及该许可证允许管理的最大客户端数量，帮助用户了解其服务订阅的状态和限制。

### 1.2 用户故事/需求 (User Stories/Requirements)
- 作为一名顾客系统管理员，我希望能够方便地查看我当前使用的服务许可证是产品版还是评估版。
- 作为一名顾客系统管理员，我希望能够清晰地看到我的服务许可证的有效截止日期，以便我能提前规划续约或了解服务何时到期。
- 作为一名顾客系统管理员，我希望能够知道我的许可证允许管理的最大客户端数量，以便我能判断当前客户端数量是否接近或已超过上限。
- 作为一名顾客系统管理员，我希望这些许可证信息是以只读方式展示的，因为我不应该有权限直接修改它们。

### 1.3 高层依赖与交互 (High-Level Dependencies & Interactions)
- **用户认证与会话**: 用户的身份（特别是关联的契约ID/许可证ID）是获取正确许可证信息的关键，依赖于有效的用户会话。
- **主界面 (Main Screen)**: 本组件的主要入口点是[主界面](./02-main-screen.md)导航栏上的“ライセンス (许可证)”按钮。
- **数据存储**: 许可证的详细信息（类型、有效期、客户端数等）存储在Azure SQL Database的 `License` 表中，并可能关联到 `LOV` 表获取类型描述（如"製品版", "評価版"）。
- **后端API (Next.js API Routes)**: 前端通过调用后端API来获取与当前用户契约关联的许可证信息。该API负责从数据库查询并返回这些数据。

## 2. 功能规格 (Functional Specifications)

### 2.1 主要流程 (High-Level Flow)
1.  用户成功登录系统后，主界面导航栏显示“ライセンス (许可证)”按钮。
2.  用户点击“ライセンス (许可证)”按钮。
3.  前端向后端API发送请求，获取与当前用户（通常通过其契约ID）关联的许可证信息。
4.  后端API根据用户的契约ID，从 `License` 表中查询对应的许可证记录。
5.  后端API将查询到的许可证详细信息返回给前端。
6.  前端在一个模态对话框（许可证信息窗口）中清晰地展示获取到的信息。
7.  用户阅读完毕后，可以点击对话框的“閉じる (关闭)”按钮或窗口右上角的“X”按钮关闭对话框。

```mermaid
sequenceDiagram
    participant User as 👤 用户 (Browser)
    participant MainScreen as 🖥️ 主界面
    participant LicenseModal as 📄 许可证信息弹窗
    participant NextJsApiRoutes as 🌐 Next.js API Routes
    participant LicenseDB as 💾 License表 (SQL DB)

    User->>MainScreen: 点击导航栏“ライセンス”按钮
    MainScreen->>NextJsApiRoutes: GET /api/license-info (请求当前用户许可证信息)
    NextJsApiRoutes->>LicenseDB: 查询License表 (基于当前用户的licenseId)
    LicenseDB-->>NextJsApiRoutes: 返回许可证详细数据
    NextJsApiRoutes-->>MainScreen: HTTP 200 OK, body: { licenseDetails: {...} }
    MainScreen->>LicenseModal: 打开许可证信息弹窗并传入数据
    LicenseModal->>User: 显示许可证类型, 有效期限, 客户端上限数
    User->>LicenseModal: 点击“閉じる”按钮
    LicenseModal-->>MainScreen: 关闭弹窗
```

### 2.2 业务规则 (Business Rules)
-   **信息来源**: 许可证信息（类型、有效期限、客户端上限数）从门户数据库的 `License` 表中获取。许可证类型的显示文本（如“製品版”、“評価版”）可能来源于 `LOV` 表（通过 `License.licenseType` 关联 `LOV.code` where `LOV.parentCode = 'LICENSE_TYPE'`）。
-   **用户特定信息**: 系统必须确保每个登录用户只能查看到与其自身契约（许可证ID）相关联的许可证信息。
-   **只读显示**: 许可证信息在本界面中仅为只读展示，用户不能通过此界面修改任何许可证参数。
-   **有效期限显示**:
    *   有效期限应以“YYYY/MM/DD”格式显示服务的结束日期。
    *   如果许可证没有明确的固定有效期限（例如，可能是永久许可或按需订阅的某种形式），则此字段应显示为“-”（横杠）。
-   **客户端数量显示**: 许可证允许管理的客户端上限数量以数值形式显示，范围为0至9,999,999。
-   **客户端超限判断辅助**: `fs.md`提及“利用者はクライアント数の上限超過を確認する際に、ライセンス保有数とライセンス使用数を突き合わせて確認する。” 这暗示系统某处（可能非此弹窗，或此弹窗未来增强）会显示“实际使用数”，以便用户自行比较。当前 `07-license-info.md` 仅要求显示“保有数”（上限数）。

### 2.3 用户界面概述 (User Interface Overview)

-   **入口点**:
    *   主界面导航栏右侧的“ライセンス (许可证)”按钮。
-   **许可证信息对话框 (模态)**:
    *   **触发**: 点击“ライセンス (许可证)”按钮后弹出。
    *   **标题**: 可能为“ライセンス情報 (许可证信息)”或类似文本。
    *   **显示内容/字段**:
        1.  **ライセンス種別 (许可证类型)**: 显示文本，例如“製品版”或“評価版”。
        2.  **有効期限 (有效期限)**: 显示日期（YYYY/MM/DD格式）或“-”。
        3.  **ライセンス保有数 (许可证可管理客户端数量)**: 显示数值。
    *   **操作按钮**:
        *   “閉じる (关闭)”：用于关闭对话框。
    *   **关闭操作**: 通常对话框右上角也会提供标准的“X”关闭图标，其行为应等同于点击“閉じる (关闭)”按钮。
-   **界面草图**: (参考 `fs.md` 图4.7.6 (1) 作为许可证信息对话框的高层概念)
    *   `![许可证信息对话框示意图](./assets/license-info-modal-sketch.png)` (假设存在此图)

### 2.4 前提条件 (Preconditions)
-   用户必须已成功通过[登录功能](./01-login.md)完成身份验证，并拥有有效的活动会话。
-   门户系统管理员必须已在 `License` 数据库表中为当前用户的契约正确配置了许可证类型、有效期限和客户端上限数等信息。
-   后端用于获取许可证信息的API端点 (`/api/license-info` 或类似) 必须可用。

### 2.5 制约事项 (Constraints/Limitations)
-   **浏览器兼容性**: 仅正式支持 Microsoft Edge 和 Google Chrome 浏览器。
-   **语言支持**: 界面显示语言仅支持日语。
-   **信息更新**: 界面显示的许可证信息是获取时刻的快照。如果后台信息发生变更，用户需要重新打开对话框才能看到最新信息（除非实现自动刷新）。
-   **非实时使用数**: 当前描述的界面仅显示“保有数”（上限），不直接显示“实时使用数”。用户需要通过其他途径（如各产品管理界面）获取实际使用数来进行比较。

### 2.6 注意事项 (Notes/Considerations)
-   “有效期限”的日期格式应确保在日语环境下显示正确且易于理解。
-   如果许可证存在多种复杂状态（如即将到期、已过期但有宽限期等），当前界面设计较为简单，仅显示基本信息。未来可考虑增强提示。

### 2.7 错误处理概述 (Error Handling Overview)
-   **信息加载失败**:
    *   如果用户点击“ライセンス”按钮后，后端API调用失败（例如，网络错误、服务器内部错误、数据库连接失败，或未能找到与当前用户关联的许可证记录），导致无法获取许可证信息，对话框应显示一个用户友好的错误提示信息（例如，“无法加载许可证信息，请稍后重试或联系管理员。”）。
    *   详细的技术错误应记录在前端控制台和/或后端日志中。
-   **数据显示为空或异常**: 如果成功从后端获取数据，但某些字段值在数据库中确实为空（例如，有效期限未设置），则界面应按规定显示“-”，而非显示错误或`null`/`undefined`等原始值。

### 2.8 相关功能参考 (Related Functional References)
*   **主要入口**: [主界面 (Main Screen)](./02-main-screen.md) - 导航栏上的“ライセンス”按钮是访问本功能的唯一途径。
*   **认证关联**: [登录 (Login)](./01-login.md) - 用户的登录身份（特别是契约ID）决定了其能看到的许可证信息。
*   **系统整体架构**: `../../architecture/system-architecture.md` - 描述了许可证信息如何从数据源流向用户界面。
*   **核心数据模型**: `../../data-models/license-table.md` (假设文件名) - 定义了许可证信息存储的表结构。
*   **可能的配置值来源**: `../../data-models/lov-table.md` (假设文件名) - 可能包含“许可证类型”的显示文本定义。
*   **源功能规格**: `fs.md` (通常位于 `docs-delivery/機能仕様書/` 或项目指定的源文档位置) - 本组件功能规格的原始日文描述，特别是其“4.7 ライセンス情報”章节。
