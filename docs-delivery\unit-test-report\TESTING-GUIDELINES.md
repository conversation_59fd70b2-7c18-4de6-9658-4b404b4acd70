# 测试指导原则 (Testing Guidelines)

## 📋 **目录**
- [核心理念](#核心理念)
- [设计文档驱动测试方法](#设计文档驱动测试方法)
- [测试优先级策略](#测试优先级策略)
- [测试用例编写规范](#测试用例编写规范)
- [业务缺陷检测重点](#业务缺陷检测重点)
- [覆盖率指标解读](#覆盖率指标解读)
- [实施检查清单](#实施检查清单)

---

## 🎯 **核心理念**

### **测试的真正目的**
> **测试的核心目的是发现代码缺陷，而不是追求覆盖率数字**

- ✅ **业务逻辑正确性验证** > 代码语法正确性验证
- ✅ **设计需求符合性检查** > 代码行数覆盖率
- ✅ **关键路径质量保证** > 全面覆盖所有分支
- ✅ **真实场景模拟测试** > 人工构造的边界条件

### **错误的测试方法**
❌ **代码实现 → 测试用例**（只能验证语法正确性）
❌ **追求100%覆盖率**（可能忽略真正的业务风险）
❌ **孤立的单元测试**（无法发现集成问题）

### **正确的测试方法**
✅ **设计文档 → 业务需求 → 测试用例 → 代码验证**
✅ **专注业务关键路径**（80/20原则）
✅ **真实业务场景模拟**（并发、异常、边界条件）

---

## 📖 **设计文档驱动测试方法**

### **Step 1: 设计文档分析**
1. **识别业务流程**：梳理设计文档中的主要业务流程
2. **提取关键需求**：找出设计文档中的关键业务需求和约束条件
3. **识别风险点**：分析可能出现问题的业务场景

**示例**：
```markdown
# 设计文档分析 - RunbookProcessorTimeoutFunc
## 关键业务需求：
- 楽観ロック制御（updatedAt字段）
- Container更新条件：targetVmName + targetContainerName + status=BUSY + currentTaskId
- 错误处理：DB更新失败时回滚事务但继续处理
- Azure Files删除：TaskWorkspaces/<taskId>/目录

## 风险点：
- 并发更新导致楽観ロック失败
- Container状态不一致
- Azure Files删除失败影响后续处理
```

### **Step 2: 业务场景设计**
基于设计文档，设计真实的业务测试场景：

1. **正常业务流程**：验证设计文档描述的标准流程
2. **异常业务场景**：验证错误处理和补偿逻辑
3. **并发竞争场景**：验证楽観ロック和数据一致性
4. **边界条件场景**：验证输入验证和边界处理

### **Step 3: 测试用例实现**
```typescript
/**
 * 试験観点：楽観ロック制御の正確性検証（设计文档6章の要件）
 * 试験対象：RunbookProcessorTimeoutFuncの楽観ロック制御分岐
 * 试験手順：
 * 1. Task読取後に他プロセスがupdatedAtを更新した場合をシミュレート
 * 2. updateMany の count が 0 になることを確認
 * 3. トランザクションがロールバックされることを確認
 * 確認項目：
 * - 楽観ロック失敗が正しく検出されること
 * - 適切なエラーメッセージが記録されること
 */
it("楽観ロック失敗: 並行更新検出", async () => {
  // 実際の業務シナリオに基づくテスト実装
});
```

---

## 🎯 **测试优先级策略**

### **Priority 1: 业务关键路径 (80%的精力)**
- **データ整合性**：楽観ロック、トランザクション制御
- **状態管理**：タスクステータス、コンテナ状態の一貫性
- **補償処理**：エラー発生時の適切な補償ロジック
- **外部連携**：Azure Services、データベース操作

### **Priority 2: 异常处理和边界条件 (15%的精力)**
- **入力検証**：不正なメッセージ、パラメータ
- **例外処理**：ネットワークエラー、タイムアウト
- **リソース制限**：メモリ不足、接続数制限

### **Priority 3: 非关键分支 (5%的精力)**
- **ログ出力**：デバッグ用ログの内容
- **パフォーマンス**：処理時間の最適化
- **コード品質**：リファクタリング、可読性

---

## 📝 **测试用例编写规范**

### **必须遵循 .augment-guidelines.md 规则 2.5**
每个测试用例必须包含以下日文关键词：

```typescript
/**
 * 試験観点：[测试的业务观点和目的]
 * 試験対象：[被测试的具体功能或分支]
 * 試験手順：
 * 1. [具体的测试步骤1]
 * 2. [具体的测试步骤2]
 * 3. [具体的测试步骤3]
 * 確認項目：
 * - [需要验证的具体项目1]
 * - [需要验证的具体项目2]
 */
```

### **测试用例命名规范**
- **正常系**: `正常系: [业务场景描述]`
- **異常系**: `異常系: [异常场景描述]`
- **補償処理**: `補償処理: [补偿场景描述]`
- **境界条件**: `境界条件: [边界场景描述]`

### **Mock策略**
1. **外部依赖必须Mock**：数据库、Azure Services、文件系统
2. **业务逻辑不要Mock**：保持真实的业务逻辑流程
3. **错误场景要精确Mock**：确保能触发特定的错误分支

---

## 🔍 **业务缺陷检测重点**

### **数据一致性缺陷**
- [ ] 楽観ロック制御是否正确实现
- [ ] 事务边界是否合理设置
- [ ] 并发更新是否会导致数据不一致

### **状态管理缺陷**
- [ ] 状态转换是否符合业务规则
- [ ] 异常情况下状态是否能正确恢复
- [ ] 状态检查逻辑是否完整

### **错误处理缺陷**
- [ ] 补偿处理是否覆盖所有异常场景
- [ ] 错误信息是否准确且有助于问题定位
- [ ] 系统是否能从错误中优雅恢复

### **集成接口缺陷**
- [ ] 外部服务调用是否有适当的重试机制
- [ ] 网络异常是否能正确处理
- [ ] 接口契约是否与设计文档一致

---

## 📊 **覆盖率指标解读**

### **覆盖率目标**
- **业务关键路径**: 95%+ (必须达到)
- **异常处理分支**: 80%+ (重要)
- **边界条件分支**: 60%+ (可选)
- **调试和日志代码**: 不强制要求

### **覆盖率质量评估**
```
✅ 高质量覆盖率：基于设计文档的业务场景测试
❌ 低质量覆盖率：为了数字而编写的人工测试

示例：
✅ 测试楽観ロック在并发更新时的行为
❌ 测试某个if分支的true/false路径
```

### **覆盖率报告分析**
1. **未覆盖行分析**：是否为业务关键逻辑？
2. **分支覆盖分析**：是否为真实业务场景？
3. **函数覆盖分析**：是否为核心业务函数？

---

## ✅ **实施检查清单**

### **开发阶段**
- [ ] 设计文档已完成并经过评审
- [ ] 基于设计文档识别了关键业务场景
- [ ] 制定了测试优先级和策略

### **测试编写阶段**
- [ ] 测试用例遵循日文注释规范
- [ ] 优先覆盖业务关键路径
- [ ] Mock策略合理且不影响业务逻辑验证
- [ ] 测试场景基于真实业务需求

### **测试执行阶段**
- [ ] 所有测试用例通过
- [ ] 业务关键路径覆盖率达标
- [ ] 发现的缺陷已记录并修复

### **质量评估阶段**
- [ ] 测试是否发现了真正的业务缺陷
- [ ] 覆盖率是否反映了真实的业务风险
- [ ] 测试维护成本是否合理

---

## 🎯 **成功案例参考**

### **RunbookProcessorTimeoutFunc (100%覆盖率)**
- ✅ 基于设计文档验证楽観ロック制御
- ✅ 验证Container更新的具体WHERE条件
- ✅ 测试Azure Files删除的真实业务场景
- ✅ 覆盖所有补偿处理分支

### **TaskCancellationTimeoutFunc (100%覆盖率)**
- ✅ 验证DLQ补償処理的完整流程
- ✅ 测试楽観ロック失败的具体场景
- ✅ 覆盖所有错误处理和状态转换

### **lib/utils.ts (100%覆盖率)**
- ✅ 验证Azure Files操作的各种场景
- ✅ 测试错误类型判定函数的准确性
- ✅ 覆盖文件删除的异常处理逻辑

---

## 📚 **相关文档**
- [.augment-guidelines.md](../.augment-guidelines.md) - 代码和测试规范
- [设计文档目录](.) - 各功能的详细设计文档
- [测试用例示例](__tests__/) - 高质量测试用例参考

---

**最后更新**: 2025-07-10
**维护者**: Development Team
