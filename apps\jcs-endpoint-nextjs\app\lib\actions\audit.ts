/**
 * @file audit.ts
 * @description 監査ログ関連サーバーアクションのモジュール。ユーザー操作・システムイベントの監査記録をAuditActionsクラスから提供する。
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

import Logger, { LogFunctionSignature } from "@/app/lib/logger";
import prisma from "@/app/lib/prisma";

/**
 * 監査ログ関連のサーバーアクションを提供するクラス。
 *
 * なぜ：本クラスは、ユーザー操作やシステムイベントの監査記録を一元的に管理し、
 * セキュリティ要件や運用監査要件を満たすために設計されています。
 * これにより、操作履歴の追跡性・証跡性を担保し、将来的な監査要件拡張にも柔軟に対応可能です。
 */
export class AuditActions {
  /**
   * ログイン監査イベントを記録するServer Action。
   *
   * @param {"LOGIN"|"LOGIN_FAILURE"|"LOGOUT"} auditType 监査种别
   * @param {string} userId ユーザーID
   * @param {string} [loginMessage] 追加メッセージ
   * @returns {Promise<void>} 监査ログ记录的完成
   * @throws DBエラー时は例外をスロー
   *
   * 为何：所有のログイン/ログアウト操作を监査迹としてDBに记录し、セキュリティ・運用要件を満たすため
   */
  @LogFunctionSignature()
  static async createAuditEvent(
    auditType: "LOGIN" | "LOGIN_FAILURE" | "LOGOUT",
    userId: string,
    loginMessage?: string,
  ): Promise<void> {
    try {
      await prisma.auditLogin.create({
        data: {
          auditType,
          userId,
          loginMessage: loginMessage || "",
        },
      });
    } catch (error: any) {
      Logger.error({ message: error.message, stack: error.stack });
      throw error;
    }
  }
} 