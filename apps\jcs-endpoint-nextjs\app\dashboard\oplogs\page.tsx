/**
 * @file page.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2024, 2025, Hitachi Solutions, Ltd.
 */

import ServerData from "@/app/lib/data";
import Table from "@/app/ui/oplogs/table";
import PageSize from "@/app/ui/page-size";
import Pagination from "@/app/ui/pagination";
import RefreshToken from "@/app/ui/refreshToken";
import Search from "@/app/ui/search";
import { TableSkeleton } from "@/app/ui/skeletons";
import { Metadata } from "next";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "操作ログ一覧",
};

export default async function Page({
  searchParams,
}: {
  searchParams?: {
    filter?: string;
    page?: string;
    size?: string;
    sort?: "name" | "createdAt" | "retentionAt" | "size";
    order?: "asc" | "desc";
  };
}) {
  const filter = searchParams?.filter || "";
  const size = Number(searchParams?.size) || 10;
  const currentPage = Number(searchParams?.page) || 1;
  const sort = searchParams?.sort || "createdAt";
  const order = searchParams?.order || "desc";

  const totalPages =
    (await ServerData.fetchOplogsPages(filter, size, !searchParams?.page)) || 0;
  const refresh = Math.floor(Math.random() * Math.random() * 101);
  return (
    <div className="p-4 h-full flex flex-col">
      <div className="flex-column flex flex-wrap items-center justify-between rounded-t-xl border border-gray-200 bg-gray-100 p-2 md:flex-row">
        <Search />
        {totalPages > 0 && (
          <div className="flex items-center">
            <Pagination totalPages={totalPages} />
            <PageSize />
          </div>
        )}
      </div>
      <div className="relative overflow-x-auto overflow-y-auto shadow-md rounded-b-lg">
        <Suspense
          key={filter + currentPage + size + sort + order}
          fallback={<TableSkeleton />}
        >
          {/* @ts-expect-error Server Component */}
          <Table
            filter={filter}
            page={currentPage}
            size={size}
            sort={sort}
            order={order}
          />
        </Suspense>
      </div>
      <RefreshToken key={refresh}/>
    </div>
  );
}
