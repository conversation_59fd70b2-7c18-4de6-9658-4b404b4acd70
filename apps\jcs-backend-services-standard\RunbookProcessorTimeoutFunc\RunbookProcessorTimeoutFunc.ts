/**
 * @fileoverview Runbookジョブ処理タイムアウト関数 (RunbookProcessorTimeoutFunc)
 * @description
 * RunbookProcessorFuncの実行が例外やタイムアウトによって3回失敗し、RunbookStatusQueueのDLQへ
 * 配信されたメッセージを処理する補償関数。タスクの状態を適切にクリーンアップし、
 * リソースの解放とエラー状態への更新を行う。
 *
 * @trigger Azure Service Bus - RunbookStatusQueue のDLQメッセージ
 * @input RunbookStatusQueue のDLQから受信する、元のRunbookジョブ実行結果メッセージ
 * @output Azure SQL Database (Task、ContainerConcurrencyStatus) のレコード更新、
 *         Azure Files上のタスク作業ディレクトリ削除
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { app, InvocationContext } from "@azure/functions";
import { prisma } from "../lib/prisma";
import { AppConstants } from "../lib/constants";
import { createShareServiceClient } from "../lib/azureClients";
import { formatTaskErrorMessage, deleteTaskWorkspaceDirectory } from "../lib/utils";

/**
 * RunbookProcessorTimeoutFunc - Runbookジョブ処理タイムアウト時の補償処理
 *
 * 処理ステップ:
 * 1-3. DLQメッセージ受信・解析、タスク情報取得・ステータス確認（RUNBOOK_PROCESSING）
 * 4-7. DBトランザクション（コンテナ実行状態・タスクテーブル更新・コミット）
 * 8-9. Azure Files作業ディレクトリ削除・正常終了ログ記録
 *
 * エラー処理:
 * taskId不正・Task存在なし・ステータス不正時は処理終了、
 * DB更新失敗時はトランザクションロールバック、その他失敗時はログ記録して処理継続。
 */
export async function RunbookProcessorTimeoutFunc(message: unknown, context: InvocationContext): Promise<void> {
  let taskId: string | undefined;
  let task: any = null;

  try {
    // 1. Azure Service Bus の RunbookStatusQueue/$DeadLetterQueue からメッセージを受信し、解析する
    context.log("[RunbookProcessorTimeoutFunc] RunbookStatusQueue/$DeadLetterQueue からメッセージ受信");

    // メッセージ基本校验
    if (!message || typeof message !== 'object') {
      context.error("[RunbookProcessorTimeoutFunc] メッセージが不正です。処理を終了します。");
      return;
    }

    // taskId 抽出・校验
    const messageBody = message as { taskId?: string };
    const extractedTaskId = messageBody.taskId;

    if (!extractedTaskId || typeof extractedTaskId !== "string") {
      context.error("[RunbookProcessorTimeoutFunc] taskIdが特定できません。処理を終了します。");
      return;
    }

    taskId = extractedTaskId;
    context.log(`[RunbookProcessorTimeoutFunc] 受信taskId: ${taskId}`);

    // 2. taskId を使用して Task テーブルを検索し、タスクの関連情報（status、対象VM名、対象コンテナ名）を取得する
    task = await prisma.task.findUnique({
      where: { id: taskId },
      select: {
        status: true,
        targetVmName: true,
        targetContainerName: true,
        updatedAt: true
      }
    });

    if (!task) {
      // タスクが存在しない、または取得失敗の場合はエラーをログに記録し処理を終了する
      context.error(`[RunbookProcessorTimeoutFunc] taskId=${taskId} のタスクがデータベースに存在しません。処理を終了します。`);
      return;
    }

    context.log(`[RunbookProcessorTimeoutFunc] タスク情報取得成功: taskId=${taskId}, status=${task.status}, targetVmName=${task.targetVmName}, targetContainerName=${task.targetContainerName}`);

    // 3. Task.statusがRUNBOOK_PROCESSINGか判定する。RUNBOOK_PROCESSINGの場合は処理を続行する
    if (task.status !== AppConstants.TaskStatus.RunbookProcessing) {
      // RUNBOOK_PROCESSINGでない場合はログに記録し処理を終了する
      context.log(`[RunbookProcessorTimeoutFunc] タスク${taskId}のステータスが${task.status}のため処理対象外です。処理を終了します。`);
      return;
    }

    // 4. データベーストランザクションを開始する
    // 5. コンテナ実行状態ContainerConcurrencyStatus テーブルから対象コンテナを status = IDLE, currentTaskId = NULL で更新する
    // 6. Task テーブルの該当タスクを status = COMPLETED_ERROR, endedAt = 現在の日時（UTC協定世界時）,
    //    resultMessage = EMET0005 (タイムアウト)のエラーメッセージ, errorCode = EMET0005 で更新する
    // 7. データベーストランザクションをコミットする
    try {
      await prisma.$transaction(async (tx) => {
        // 5. コンテナ実行状態ContainerConcurrencyStatus テーブルから対象コンテナを status = IDLE, currentTaskId = NULL で更新する
        // 条件：対象VM名 = ステップ2.で取得した対象VM名、対象コンテナ名 = ステップ2.で取得した対象コンテナ名、
        // ステータス（status） = BUSY、使用中のタスクID（currentTaskId） = 入力パラメータのtaskId
        const containerUpdateResult = await tx.containerConcurrencyStatus.updateMany({
          where: {
            targetVmName: task.targetVmName,
            targetContainerName: task.targetContainerName,
            status: "BUSY",
            currentTaskId: taskId,
          },
          data: {
            status: "IDLE",
            currentTaskId: null
          },
        });

        if (containerUpdateResult.count === 0) {
          // 更新失敗または更新した件数が0件の場合はトランザクションをロールバックする
          context.error(`[RunbookProcessorTimeoutFunc] コンテナ実行状態テーブル更新失敗: 更新件数0件`);
          throw new Error("コンテナ実行状態テーブル更新失敗");
        }

        // 6. Task テーブルの該当タスクを更新する
        // 更新条件：ID = 入力パラメータのtaskId、最終更新日時 = ステップ2.で取得した最終更新日時
        const taskUpdateResult = await tx.task.updateMany({
          where: {
            id: taskId,
            updatedAt: task.updatedAt // 楽観ロック制御
          },
          data: {
            status: AppConstants.TaskStatus.CompletedError,
            endedAt: new Date(),
            resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0005),
            errorCode: AppConstants.ERROR_CODES.EMET0005,
          },
        });

        if (taskUpdateResult.count === 0) {
          // 更新失敗または更新した件数が0件の場合はトランザクションをロールバックする
          context.error(`[RunbookProcessorTimeoutFunc] タスクテーブル更新失敗: 更新件数0件`);
          throw new Error("タスクテーブル更新失敗");
        }

        context.log(`[RunbookProcessorTimeoutFunc] DBトランザクション成功: taskId=${taskId}`);
      });
    } catch (err: any) {
      // 更新失敗または更新した件数が0件の場合はトランザクションをロールバックする。エラー詳細をログに記録し、処理を継続する
      context.error(`[RunbookProcessorTimeoutFunc] DBトランザクション失敗:`, err);
    }

    // 8. Azure Files上のタスク作業ディレクトリ（TaskWorkspaces/<taskId>/）の削除を試みる
    try {
      const shareServiceClient = createShareServiceClient();
      const shareClient = shareServiceClient.getShareClient(AppConstants.AZURE_FILES.TASK_WORKSPACES_SHARE);
      const taskDirClient = shareClient.getDirectoryClient(taskId);
      await deleteTaskWorkspaceDirectory(taskDirClient, context);
      context.log(`[RunbookProcessorTimeoutFunc] Azure Files作業ディレクトリ削除成功: TaskWorkspaces/${taskId}/`);
    } catch (err: any) {
      // 削除に失敗した場合はエラー詳細をログに記録し、処理を継続する
      context.error(`[RunbookProcessorTimeoutFunc] Azure Files作業ディレクトリ削除失敗:`, err);
    }

    // 9. 正常終了のログを記録し、処理を終了する（メッセージACK）
    context.log(`[RunbookProcessorTimeoutFunc] タスクID ${taskId} のタイムアウト補償処理が正常に完了しました。`);

  } catch (err: any) {
    // 予期せぬ内部エラー：エラーログ記録。処理終了。（メッセージACK）
    context.error(`[RunbookProcessorTimeoutFunc] 予期せぬ内部エラーが発生しました:`, err);
  }
}

// FunctionとService Busキューのバインド設定
app.serviceBusQueue("RunbookProcessorTimeoutFunc", {
  connection: "AZURE_SERVICEBUS_NAMESPACE_HOSTNAME",
  queueName: "%SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME%/$DeadLetterQueue",
  handler: RunbookProcessorTimeoutFunc,
}); 