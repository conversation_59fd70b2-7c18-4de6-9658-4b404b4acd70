describe("API エンドポイントの可用性のテスト", () => {
  describe("API - システムお知らせ一覧取得", () => {
    const validCredentials = {
      userId: "hitachi.taro.aa",
      password: "changeit!@#",
    };

    describe("セッションが存在しない場合", () => {
      it("セッションが存在しない場合、システムお知らせ一覧が正常に取得できる", () => {
        cy.request({
          method: "GET",
          url: "/api/notifications/system",
          failOnStatusCode: false,
        }).then((response) => {
          expect(response.status).to.eq(200);

          expect(response.body).to.be.an("array");

          expect(response.body[0].id).to.be.a("string");
          expect(response.body[0].content).to.be.a("string");
          expect(response.body[0].publishedAt).to.match(
            /^\d{4}\/\d{2}\/\d{2}$/,
          );
        });
      });
    });

    describe("セッションが存在する場合", () => {
      // @ts-ignore
      let cookies;

      before(() => {
        Cypress.Cookies.debug(true);
        cy.visit("/login");
        cy.get("#userId").type(validCredentials.userId);
        cy.get("#password").type(validCredentials.password);
        cy.get("button").click();

        cy.wait(3000);
        cy.getCookies()
          .should("have.length.gt", 0)
          .then((cookiesArray) => {
            cookies = cookiesArray;
          });
      });

      it("システムお知らせ一覧が正常に取得できる", () => {
        cy.request({
          method: "GET",
          url: "/api/notifications/system",
          headers: {
            // @ts-ignore
            Cookie: `${cookies[0].name}=${cookies[0].value}`,
          },
        }).then((response) => {
          expect(response.status).to.eq(200);

          expect(response.body).to.be.an("array");

          expect(response.body[0].id).to.be.a("string");
          expect(response.body[0].content).to.be.a("string");
          expect(response.body[0].publishedAt).to.match(
            /^\d{4}\/\d{2}\/\d{2}$/,
          );
        });
      });
    });
  });
});
