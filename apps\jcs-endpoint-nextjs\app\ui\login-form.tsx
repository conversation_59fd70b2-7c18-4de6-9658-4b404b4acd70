/**
 * @file login-form.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2024, 2025, Hitachi Solutions, Ltd.
 */

"use client";

import clsx from "clsx";
import { useRouter } from "next/navigation"; // Next.jsのルーター
import { useEffect, useState } from "react"; // リアクトコンポーネント用のライブラリ
import {
  PORTAL_ERROR_MESSAGES
} from "../lib/definitions";
import Spinner from "./spinner";


// ログインフォームコンポーネント
export default function LoginForm() {
  const [loading, setLoading] = useState(false); // ローディング状態の管理
  const [error, setError] = useState<string>(""); // エラーメッセージの管理
  const router = useRouter(); // ルーターのインスタンス取得
  
  useEffect(() => {
    const fetchError = async () => {
      const redirectUrl = window.location.href;
      // 契約が無効になる
      if (redirectUrl.includes("error05")) {
        setError(PORTAL_ERROR_MESSAGES.EMEC0005);
        return;
      } else if (redirectUrl.includes("error07")) {
        setError(PORTAL_ERROR_MESSAGES.EMEC0007);
        return;
      }
    };
    fetchError();
  }, []);

  // ログイン処理の関数
  const login = async () => {
    try {
      setLoading(true); // ローディング状態をtrueに設定
      // keycloakログイン画面URL取得
      const response = await fetch(`/api/login`, {
        method: "POST", // POSTリクエスト
        headers: {
          "Content-Type": "application/json", // JSON形式のヘッダー
        }
      });
      // 取得結果判定
      if (!response.ok) {
          // APIの実行に失敗しました
          setError(PORTAL_ERROR_MESSAGES.EMEC0007);
      } else {
        const data = await response.json();
        if (data.status === 200) {
          // KEYCLAOKログイン画面が開きます
          router.push(data.url);
        } else {
          // keycloakログインページのURLが正しくありません
          setError(PORTAL_ERROR_MESSAGES.EMEC0007);
        }
      }
    } catch (error) {
      setError(PORTAL_ERROR_MESSAGES.EMEC0007);
    } finally {
      setLoading(false); // ローディング状態をfalseに設定
    }
  };

  return (
    <div className="flex max-w-sm flex-col gap-2 lg:gap-2 2xl:gap-4 text-white">
      <h1 className="text-xl lg:text-xl 2xl:text-3xl font-semibold">JP1 Cloud Service</h1>
      <h1 className="mb-0 lg:mb-2 2xl:mb-4 text-lg lg:text-lg 2xl:text-2xl font-medium">エンドポイント管理</h1>
      <div className="flex items-center justify-between">
        <div className="w-64">
          <button
            onClick={login}
            className="rounded bg-gradient-light shadow-light drop-shadow-light px-4 py-2 text-center text-xs font-medium text-gray-900 hover:opacity-80 transition duration-3 00"
          >
            <Spinner
              className={clsx("inline-block mr-2", { hidden: !loading })}
            />
            ログイン
          </button>
        </div>
      </div>
      <div
        className={clsx(
          "h-24 transform-gpu rounded bg-[#F3F4F6]/50 p-4 text-gray-900",
          { invisible: !error },
        )}
      >
        <div className="mb-2 text-sm lg:text-sm 2xl:text-base font-semibold">
          <img
            src="/dialogerror_32.png"
            className="mr-2 inline-block h-4 w-4"
            alt="error"
          />
          エラー
        </div>
        <div className="text-xs lg:text-xs 2xl:text-sm font-normal">{error}</div>
      </div>
    </div>
  );
}
