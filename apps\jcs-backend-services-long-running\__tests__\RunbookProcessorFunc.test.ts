/**
 * @fileoverview RunbookProcessorFuncの単体テスト
 * @description 設計文書に基づくRunbookジョブ処理関数の業務関鍵路径・楽観ロック制御・補償処理・外部サービス連携の網羅的検証
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { RunbookProcessorFunc } from "../RunbookProcessorFunc/RunbookProcessorFunc";
import { AppConstants } from "../lib/constants";

// 外部依存のモック
const prisma = require("../lib/prisma").prisma;
const azureClients = require("../lib/azureClients");
const utils = require("../lib/utils");

jest.mock("../lib/prisma", () => ({
  prisma: {
    task: {
      findUnique: jest.fn(),
      updateMany: jest.fn()
    },
    $transaction: jest.fn(),
    operationLog: {
      createMany: jest.fn()
    },
    containerConcurrencyStatus: {
      updateMany: jest.fn()
    }
  }
}));

jest.mock("../lib/azureClients", () => ({
  createShareServiceClient: jest.fn(),
  createBlobServiceClient: jest.fn(),
  stopAutomationJob: jest.fn()
}));

jest.mock("../lib/utils", () => ({
  formatTaskErrorMessage: jest.fn((code, params) => `Error ${code}: ${params ? params.join(', ') : ''}`),
  isAzureFilesError: jest.fn(),
  isAzureBlobError: jest.fn(),
  deleteTaskWorkspaceDirectory: jest.fn()
}));

function createContext() {
  return {
    invocationId: "test-invoke-001",
    functionName: "RunbookProcessorFunc",
    log: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    trace: jest.fn(),
    debug: jest.fn(),
    info: jest.fn()
  };
}

/**
 * 正常系テスト - 設計文書の業務フロー検証
 */
describe("RunbookProcessorFunc 正常系", () => {
  let context: any;
  let mockShareClient: any;
  let mockBlobClient: any;
  let mockContainerClient: any;
  let mockDirectoryClient: any;
  let mockFileClient: any;

  beforeEach(() => {
    context = createContext();

    // Azure Files モック設定
    mockFileClient = {
      exists: jest.fn(),
      download: jest.fn(),
      getProperties: jest.fn(),
      url: "https://test.file.core.windows.net/share/task123/exports/file.zip"
    };

    mockDirectoryClient = {
      listFilesAndDirectories: jest.fn(),
      getFileClient: jest.fn(() => mockFileClient)
    };

    mockShareClient = {
      getDirectoryClient: jest.fn(() => mockDirectoryClient)
    };

    // Azure Blob Storage モック設定
    mockBlobClient = {
      syncCopyFromURL: jest.fn()
    };

    mockContainerClient = {
      getBlobClient: jest.fn(() => mockBlobClient)
    };

    (azureClients.createShareServiceClient as any).mockReturnValue({
      getShareClient: jest.fn(() => mockShareClient)
    });

    (azureClients.createBlobServiceClient as any).mockReturnValue({
      getContainerClient: jest.fn(() => mockContainerClient)
    });

    // 環境変数設定
    process.env.AZURE_STORAGE_CONTAINER_OPLOGS = "oplogs-container";
    process.env.AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF = "assetsfield-container";

    // utils モック設定
    utils.deleteTaskWorkspaceDirectory.mockResolvedValue(undefined);
    utils.formatTaskErrorMessage.mockImplementation((code: any, params: any) => `Error ${code}: ${params ? params.join(', ') : ''}`);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  /**
   * 試験観点：操作ログエクスポート完了時の正常処理検証（設計文書4.a要件）
   * 試験対象：RunbookProcessorFuncのCompleted状態・OPLOG_EXPORT処理分岐
   * 試験手順：
   * 1. automationJobStatus=Completed、taskType=OPLOG_EXPORTのメッセージを送信
   * 2. Azure Filesからexportoplog_*.zipファイルを取得
   * 3. Azure Blob StorageへのコピーとOperationLogレコード作成を実行
   * 確認項目：
   * - タスクステータスがCOMPLETED_SUCCESSに更新されること
   * - OperationLogレコードが正しく作成されること
   * - コンテナステータスがIDLEに更新されること
   */
  it("正常系: 操作ログエクスポート完了処理", async () => {
    // Arrange
    const mockTask = {
      id: "task-001",
      status: AppConstants.TaskStatus.RunbookProcessing, // 正しい定数値を使用
      taskType: AppConstants.TaskType.OpLogExport, // 正しい定数値を使用
      taskName: "SERVER01-OPLOG-20250111120000",
      licenseId: "license-001",
      targetVmName: "vm-001",
      targetContainerName: "container-001",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    // Azure Files モック - exportoplog_*.zipファイルが存在
    mockDirectoryClient.listFilesAndDirectories.mockReturnValue({
      [Symbol.asyncIterator]: async function* () {
        yield { kind: "file", name: "exportoplog_001.zip" };
        yield { kind: "file", name: "exportoplog_002.zip" };
      }
    });

    mockFileClient.getProperties.mockResolvedValue({ contentLength: 1024 });

    // Prisma モック設定
    prisma.task.findUnique.mockResolvedValue(mockTask);
    prisma.$transaction.mockImplementation(async (callback: any) => {
      return await callback({
        task: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) },
        operationLog: { createMany: jest.fn().mockResolvedValue({}) },
        containerConcurrencyStatus: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) }
      });
    });

    const message = {
      taskId: "task-001",
      automationJobStatus: "Completed"
    };

    // Act
    await RunbookProcessorFunc(message, context);

    // Assert
    expect(mockBlobClient.syncCopyFromURL).toHaveBeenCalledTimes(2);
    expect(mockDirectoryClient.listFilesAndDirectories).toHaveBeenCalled();
  });
  /**
   * 試験観点：管理項目定義エクスポート完了時の正常処理検証（設計文書4.b要件）
   * 試験対象：RunbookProcessorFuncのCompleted状態・MGMT_ITEM_EXPORT処理分岐
   * 試験手順：
   * 1. automationJobStatus=Completed、taskType=MGMT_ITEM_EXPORTのメッセージを送信
   * 2. Azure Filesからassetsfield_def.csvファイルを取得
   * 3. Azure Blob Storageへのコピーを実行
   * 確認項目：
   * - タスクステータスがCOMPLETED_SUCCESSに更新されること
   * - assetsfield_def.csvが正しいパスにコピーされること
   */
  it("正常系: 管理項目定義エクスポート完了処理", async () => {
    // Arrange
    const mockTask = {
      id: "task-002",
      status: AppConstants.TaskStatus.RunbookProcessing, // 正しい定数値を使用
      taskType: AppConstants.TaskType.MgmtItemExport, // 正しい定数値を使用
      licenseId: "license-002",
      targetVmName: "vm-002",
      targetContainerName: "container-002",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    // Azure Files モック - assetsfield_def.csvファイルが存在
    mockFileClient.exists.mockResolvedValue(true);

    // Prisma モック設定
    prisma.task.findUnique.mockResolvedValue(mockTask);
    prisma.$transaction.mockImplementation(async (callback: any) => {
      return await callback({
        task: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) },
        operationLog: { createMany: jest.fn().mockResolvedValue({}) },
        containerConcurrencyStatus: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) }
      });
    });

    const message = {
      taskId: "task-002",
      automationJobStatus: "Completed"
    };

    // Act
    await RunbookProcessorFunc(message, context);

    // Assert
    expect(mockFileClient.exists).toHaveBeenCalled();
    expect(mockBlobClient.syncCopyFromURL).toHaveBeenCalled();
  });
});

/**
 * 異常系テスト - 設計文書のエラー処理要件検証
 */
describe("RunbookProcessorFunc 異常系", () => {
  let context: any;
  let mockShareClient: any;
  let mockDirectoryClient: any;
  let mockFileClient: any;

  beforeEach(() => {
    context = createContext();

    // Azure Files モック設定
    mockFileClient = {
      exists: jest.fn(),
      download: jest.fn(),
      url: "https://test.file.core.windows.net/share/task123/exports/file.zip"
    };

    mockDirectoryClient = {
      getFileClient: jest.fn(() => mockFileClient)
    };

    mockShareClient = {
      getDirectoryClient: jest.fn(() => mockDirectoryClient)
    };

    (azureClients.createShareServiceClient as any).mockReturnValue({
      getShareClient: jest.fn(() => mockShareClient)
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  /**
   * 試験観点：必須パラメータ不足時のエラー処理検証（設計文書2要件）
   * 試験対象：RunbookProcessorFuncのメッセージ解析・検証分岐
   * 試験手順：
   * 1. taskIdまたはautomationJobStatusが不足したメッセージを送信
   * 2. エラーログ記録と例外throwを確認
   * 確認項目：
   * - 適切なエラーメッセージがログに記録されること
   * - 例外がthrowされてリトライが発生すること
   */
  it("異常系: 必須パラメータ不足", async () => {
    // Arrange
    const invalidMessage = {
      taskId: "task-001"
      // automationJobStatusが不足
    };

    // Act & Assert
    await expect(RunbookProcessorFunc(invalidMessage, context)).rejects.toThrow(
      "必須情報（taskId, automationJobStatus）が不足/不正です。"
    );
    expect(context.error).toHaveBeenCalledWith(
      expect.stringContaining("必須情報（taskId, automationJobStatus）が不足/不正です。")
    );
  });

  /**
   * 試験観点：タスク存在チェックのエラー処理検証（設計文書3要件）
   * 試験対象：RunbookProcessorFuncのタスク取得分岐
   * 試験手順：
   * 1. 存在しないtaskIdでメッセージを送信
   * 2. エラーログ記録と例外throwを確認
   * 確認項目：
   * - タスク不存在エラーがログに記録されること
   * - 例外がthrowされてリトライが発生すること
   */
  it("異常系: タスク存在なし", async () => {
    // Arrange
    prisma.task.findUnique.mockResolvedValue(null);

    const message = {
      taskId: "nonexistent-task",
      automationJobStatus: "Completed"
    };

    // Act & Assert
    await expect(RunbookProcessorFunc(message, context)).rejects.toThrow(
      "タスクID nonexistent-task がデータベースに存在しません。"
    );
    expect(context.error).toHaveBeenCalledWith(
      expect.stringContaining("タスクID nonexistent-task がデータベースに存在しません。")
    );
  });

  /**
   * 試験観点：タスクステータス不正時の処理終了検証（設計文書3要件）
   * 試験対象：RunbookProcessorFuncのステータス判定分岐
   * 試験手順：
   * 1. status=RUNBOOK_PROCESSING以外のタスクでメッセージを送信
   * 2. ログ記録と処理終了を確認
   * 確認項目：
   * - 適切なログメッセージが記録されること
   * - 例外がthrowされずに正常終了すること
   */
  it("異常系: タスクステータス不正", async () => {
    // Arrange
    const mockTask = {
      id: "task-003",
      status: AppConstants.TaskStatus.CompletedSuccess, // RUNBOOK_PROCESSING以外
      targetVmName: "vm-003",
      targetContainerName: "container-003",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    prisma.task.findUnique.mockResolvedValue(mockTask);

    const message = {
      taskId: "task-003",
      automationJobStatus: "Completed"
    };

    // Act
    await RunbookProcessorFunc(message, context);

    // Assert
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("ステータスが RUNBOOK_PROCESSING ではありません")
    );
    // 例外がthrowされないことを確認（正常終了）
  });

  /**
   * 試験観点：無効なメッセージ形式のエラー処理検証（設計文書1要件）
   * 試験対象：RunbookProcessorFuncのメッセージ基本検証分岐
   * 試験手順：
   * 1. nullまたは不正な形式のメッセージを送信
   * 2. エラーログ記録と例外throwを確認
   * 確認項目：
   * - 適切なエラーメッセージがログに記録されること
   * - 例外がthrowされてリトライが発生すること
   */
  it("異常系: 無効なメッセージ形式", async () => {
    // Arrange
    const invalidMessage = null;

    // Act & Assert
    await expect(RunbookProcessorFunc(invalidMessage, context)).rejects.toThrow(
      "メッセージが不正です。"
    );
    expect(context.error).toHaveBeenCalledWith(
      expect.stringContaining("メッセージが不正です。")
    );
  });
});

/**
 * 補償処理テスト - 設計文書の楽観ロック制御・補償ロジック検証
 */
describe("RunbookProcessorFunc 補償処理", () => {
  let context: any;
  let mockShareClient: any;
  let mockDirectoryClient: any;

  beforeEach(() => {
    context = createContext();

    // Azure Files モック設定
    mockDirectoryClient = {
      getFileClient: jest.fn()
    };

    mockShareClient = {
      getDirectoryClient: jest.fn(() => mockDirectoryClient)
    };

    (azureClients.createShareServiceClient as any).mockReturnValue({
      getShareClient: jest.fn(() => mockShareClient)
    });

    // utils モック設定
    utils.deleteTaskWorkspaceDirectory.mockResolvedValue(undefined);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  /**
   * 試験観点：楽観ロック制御の正確性検証（設計文書7要件）
   * 試験対象：RunbookProcessorFuncの楽観ロック制御分岐
   * 試験手順：
   * 1. Task読取後に他プロセスがupdatedAtを更新した場合をシミュレート
   * 2. updateMany の count が 0 になることを確認
   * 3. トランザクションがロールバックされることを確認
   * 確認項目：
   * - 楽観ロック失敗が正しく検出されること
   * - 適切なエラーメッセージが記録されること
   * - 例外がthrowされてリトライが発生すること
   */
  it("補償処理: 楽観ロック失敗検出", async () => {
    // Arrange
    const mockTask = {
      id: "task-004",
      status: AppConstants.TaskStatus.RunbookProcessing, // 正しい定数値を使用
      taskType: AppConstants.TaskType.MgmtItemImport, // 正しい定数値を使用
      targetVmName: "vm-004",
      targetContainerName: "container-004",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    prisma.task.findUnique.mockResolvedValue(mockTask);

    // 楽観ロック失敗をシミュレート（updateManyのcountが0）
    prisma.$transaction.mockImplementation(async (callback: any) => {
      const mockTx = {
        task: { updateMany: jest.fn().mockResolvedValue({ count: 0 }) },
        operationLog: { createMany: jest.fn() },
        containerConcurrencyStatus: { updateMany: jest.fn() }
      };
      // callbackを実行して例外をthrowさせる
      await callback(mockTx);
    });

    const message = {
      taskId: "task-004",
      automationJobStatus: "Completed"
    };

    // Act & Assert
    await expect(RunbookProcessorFunc(message, context)).rejects.toThrow(
      "タスクtask-004の更新失敗: 楽観ロック失敗またはタスクが存在しない"
    );
  });

  /**
   * 試験観点：コンテナ状態更新失敗時の補償処理検証（設計文書8要件）
   * 試験対象：RunbookProcessorFuncのコンテナ状態更新分岐
   * 試験手順：
   * 1. 正常なタスク処理後にコンテナ状態更新が失敗する場合をシミュレート
   * 2. updateMany の count が 0 になることを確認
   * 3. トランザクションがロールバックされることを確認
   * 確認項目：
   * - コンテナ状態更新失敗が正しく検出されること
   * - 適切なエラーメッセージが記録されること
   * - 例外がthrowされてリトライが発生すること
   */
  it("補償処理: コンテナ状態更新失敗", async () => {
    // Arrange
    const mockTask = {
      id: "task-005",
      status: AppConstants.TaskStatus.RunbookProcessing, // 正しい定数値を使用
      taskType: AppConstants.TaskType.MgmtItemImport, // 正しい定数値を使用
      targetVmName: "vm-005",
      targetContainerName: "container-005",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    prisma.task.findUnique.mockResolvedValue(mockTask);

    // コンテナ状態更新失敗をシミュレート
    prisma.$transaction.mockImplementation(async (callback: any) => {
      const mockTx = {
        task: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) },
        operationLog: { createMany: jest.fn().mockResolvedValue({}) },
        containerConcurrencyStatus: { updateMany: jest.fn().mockResolvedValue({ count: 0 }) }
      };
      // callbackを実行して例外をthrowさせる
      await callback(mockTx);
    });

    const message = {
      taskId: "task-005",
      automationJobStatus: "Completed"
    };

    // Act & Assert
    await expect(RunbookProcessorFunc(message, context)).rejects.toThrow(
      "コンテナ状態更新失敗: 対象コンテナが見つからないか既に他の状態に変更されています (VM: vm-005, Container: container-005)"
    );
  });
});

/**
 * 境界条件テスト - 設計文書のエッジケース検証
 */
describe("RunbookProcessorFunc 境界条件", () => {
  let context: any;
  let mockShareClient: any;
  let mockDirectoryClient: any;
  let mockFileClient: any;
  let mockBlobClient: any;
  let mockContainerClient: any;

  beforeEach(() => {
    context = createContext();

    // Azure Files モック設定
    mockFileClient = {
      exists: jest.fn(),
      download: jest.fn(),
      getProperties: jest.fn(),
      url: "https://test.file.core.windows.net/share/task123/exports/file.zip"
    };

    mockDirectoryClient = {
      listFilesAndDirectories: jest.fn(),
      getFileClient: jest.fn(() => mockFileClient)
    };

    mockShareClient = {
      getDirectoryClient: jest.fn(() => mockDirectoryClient)
    };

    // Azure Blob Storage モック設定
    mockBlobClient = {
      syncCopyFromURL: jest.fn()
    };

    mockContainerClient = {
      getBlobClient: jest.fn(() => mockBlobClient)
    };

    (azureClients.createShareServiceClient as any).mockReturnValue({
      getShareClient: jest.fn(() => mockShareClient)
    });

    (azureClients.createBlobServiceClient as any).mockReturnValue({
      getContainerClient: jest.fn(() => mockContainerClient)
    });

    // utils モック設定
    utils.deleteTaskWorkspaceDirectory.mockResolvedValue(undefined);
    utils.formatTaskErrorMessage.mockImplementation((code: any, params: any) => `Error ${code}: ${params ? params.join(', ') : ''}`);
    utils.isAzureBlobError.mockReturnValue(false);
    utils.isAzureFilesError.mockReturnValue(false);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
  /**
   * 試験観点：エクスポートファイル不存在時のエラー処理検証（設計文書4.a.ii要件）
   * 試験対象：RunbookProcessorFuncのファイル存在チェック分岐
   * 試験手順：
   * 1. automationJobStatus=Completed、taskType=OPLOG_EXPORTでファイルが存在しない場合
   * 2. EMET0015エラーコードでタスクステータス更新を確認
   * 確認項目：
   * - タスクステータスがCOMPLETED_ERRORに更新されること
   * - エラーコードEMET0015が設定されること
   */
  it("境界条件: エクスポートファイル不存在", async () => {
    // Arrange
    const mockTask = {
      id: "task-006",
      status: AppConstants.TaskStatus.RunbookProcessing, // 正しい定数値を使用
      taskType: AppConstants.TaskType.OpLogExport, // 正しい定数値を使用
      targetVmName: "vm-006",
      targetContainerName: "container-006",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    // Azure Files モック - ファイルが存在しない
    mockDirectoryClient.listFilesAndDirectories.mockReturnValue({
      [Symbol.asyncIterator]: async function* () {
        // 空のイテレータ（ファイルなし）
      }
    });

    prisma.task.findUnique.mockResolvedValue(mockTask);
    prisma.$transaction.mockImplementation(async (callback: any) => {
      return await callback({
        task: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) },
        operationLog: { createMany: jest.fn().mockResolvedValue({}) },
        containerConcurrencyStatus: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) }
      });
    });

    const message = {
      taskId: "task-006",
      automationJobStatus: "Completed"
    };

    // Act
    await RunbookProcessorFunc(message, context);

    // Assert
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("エクスポートファイルなし")
    );
  });

  /**
   * 試験観点：Failed状態でerrordetail.txt存在時の処理検証（設計文書4.ii要件）
   * 試験対象：RunbookProcessorFuncのFailed状態処理分岐
   * 試験手順：
   * 1. automationJobStatus=Failedでerrordetail.txtが存在する場合
   * 2. ファイル内容を読み取りエラーメッセージに設定
   * 確認項目：
   * - errordetail.txtの内容がresultMessageに設定されること
   * - エラーコードEMET0011が設定されること
   */
  it("境界条件: Failed状態・errordetail.txt存在", async () => {
    // Arrange
    const mockTask = {
      id: "task-007",
      status: AppConstants.TaskStatus.RunbookProcessing, // 正しい定数値を使用
      targetVmName: "vm-007",
      targetContainerName: "container-007",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    // errordetail.txtが存在し、内容を返す
    mockFileClient.exists.mockResolvedValue(true);
    mockFileClient.download.mockResolvedValue({
      readableStreamBody: {
        on: jest.fn((event, callback) => {
          if (event === 'data') {
            callback(Buffer.from("スクリプト実行エラーの詳細"));
          } else if (event === 'end') {
            callback();
          }
        })
      }
    });

    prisma.task.findUnique.mockResolvedValue(mockTask);
    prisma.$transaction.mockImplementation(async (callback: any) => {
      return await callback({
        task: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) },
        operationLog: { createMany: jest.fn().mockResolvedValue({}) },
        containerConcurrencyStatus: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) }
      });
    });

    const message = {
      taskId: "task-007",
      automationJobStatus: "Failed",
      exception: "Azure Automation exception"
    };

    // Act
    await RunbookProcessorFunc(message, context);

    // Assert
    expect(mockFileClient.exists).toHaveBeenCalled();
    expect(mockFileClient.download).toHaveBeenCalled();
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("基盤スクリプト実行エラー")
    );
  });

  /**
   * 試験観点：Timeout状態でのジョブ停止処理検証（設計文書4.iii要件）
   * 試験対象：RunbookProcessorFuncのTimeout状態処理分岐
   * 試験手順：
   * 1. automationJobStatus=TimeoutでAzure Automation API呼び出し
   * 2. ジョブ停止成功後のタスクステータス更新を確認
   * 確認項目：
   * - stopAutomationJobが正しいtaskIdで呼び出されること
   * - エラーコードEMET0005が設定されること
   */
  it("境界条件: Timeout状態・ジョブ停止処理", async () => {
    // Arrange
    const mockTask = {
      id: "task-008",
      status: AppConstants.TaskStatus.RunbookProcessing, // 正しい定数値を使用
      targetVmName: "vm-008",
      targetContainerName: "container-008",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    prisma.task.findUnique.mockResolvedValue(mockTask);
    (azureClients.stopAutomationJob as any).mockResolvedValue(undefined);

    prisma.$transaction.mockImplementation(async (callback: any) => {
      return await callback({
        task: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) },
        operationLog: { createMany: jest.fn().mockResolvedValue({}) },
        containerConcurrencyStatus: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) }
      });
    });

    const message = {
      taskId: "task-008",
      automationJobStatus: "Timeout"
    };

    // Act
    await RunbookProcessorFunc(message, context);

    // Assert
    expect(azureClients.stopAutomationJob).toHaveBeenCalledWith("task-008");
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("タイムアウト処理完了")
    );
  });

  /**
   * 試験観点：未対応タスク種別のエラー処理検証（設計文書4要件）
   * 試験対象：RunbookProcessorFuncの未対応タスク種別分岐
   * 試験手順：
   * 1. automationJobStatus=Completedで未対応のtaskTypeを送信
   * 2. 未対応タスク種別エラーが発生することを確認
   * 確認項目：
   * - 適切なエラーメッセージが記録されること
   * - 例外がthrowされてリトライが発生すること
   */
  it("境界条件: 未対応タスク種別エラー", async () => {
    // Arrange
    const mockTask = {
      id: "task-009",
      status: AppConstants.TaskStatus.RunbookProcessing,
      taskType: "UNKNOWN_TASK_TYPE", // 未対応のタスク種別
      targetVmName: "vm-009",
      targetContainerName: "container-009",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    prisma.task.findUnique.mockResolvedValue(mockTask);

    const message = {
      taskId: "task-009",
      automationJobStatus: "Completed"
    };

    // Act & Assert
    await expect(RunbookProcessorFunc(message, context)).rejects.toThrow(
      "未対応のタスク種別: UNKNOWN_TASK_TYPE"
    );
  });

  /**
   * 試験観点：Azure Blob操作エラー時の補償処理検証（設計文書補償要件）
   * 試験対象：RunbookProcessorFuncのBlob操作エラー分岐
   * 試験手順：
   * 1. 正常なタスク処理中にBlob操作エラーを発生させる
   * 2. EMET0003エラーコードでタスクステータス更新を確認
   * 確認項目：
   * - Blob操作エラーが正しく検出されること
   * - エラーコードEMET0003が設定されること
   */
  it("補償処理: Azure Blob操作エラー", async () => {
    // Arrange
    const mockTask = {
      id: "task-010",
      status: AppConstants.TaskStatus.RunbookProcessing,
      taskType: AppConstants.TaskType.OpLogExport,
      taskName: "SERVER01-OPLOG-20250111120000",
      licenseId: "license-010",
      targetVmName: "vm-010",
      targetContainerName: "container-010",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    // Azure Files モック - ファイルが存在
    mockDirectoryClient.listFilesAndDirectories.mockReturnValue({
      [Symbol.asyncIterator]: async function* () {
        yield { kind: "file", name: "exportoplog_001.zip" };
      }
    });

    mockFileClient.getProperties.mockResolvedValue({ contentLength: 1024 });

    // Blob操作でエラーを発生させる
    const blobError = new Error("Blob operation failed");
    utils.isAzureBlobError.mockReturnValue(true);
    mockBlobClient.syncCopyFromURL.mockRejectedValue(blobError);

    prisma.task.findUnique.mockResolvedValue(mockTask);
    prisma.$transaction.mockImplementation(async (callback: any) => {
      return await callback({
        task: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) },
        operationLog: { createMany: jest.fn().mockResolvedValue({}) },
        containerConcurrencyStatus: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) }
      });
    });

    const message = {
      taskId: "task-010",
      automationJobStatus: "Completed"
    };

    // Act
    await RunbookProcessorFunc(message, context);

    // Assert
    expect(utils.isAzureBlobError).toHaveBeenCalledWith(blobError);
    expect(context.error).toHaveBeenCalledWith(
      expect.stringContaining("Blob操作失敗")
    );
  });

  /**
   * 試験観点：Azure Files操作エラー時の補償処理検証（設計文書補償要件）
   * 試験対象：RunbookProcessorFuncのFiles操作エラー分岐
   * 試験手順：
   * 1. 正常なタスク処理中にFiles操作エラーを発生させる
   * 2. EMET0002エラーコードでタスクステータス更新を確認
   * 確認項目：
   * - Files操作エラーが正しく検出されること
   * - エラーコードEMET0002が設定されること
   */
  it("補償処理: Azure Files操作エラー", async () => {
    // Arrange
    const mockTask = {
      id: "task-011",
      status: AppConstants.TaskStatus.RunbookProcessing,
      taskType: AppConstants.TaskType.MgmtItemExport,
      licenseId: "license-011",
      targetVmName: "vm-011",
      targetContainerName: "container-011",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    // Files操作でエラーを発生させる
    const filesError = new Error("Files operation failed");
    utils.isAzureFilesError.mockReturnValue(true);
    mockFileClient.exists.mockRejectedValue(filesError);

    prisma.task.findUnique.mockResolvedValue(mockTask);
    prisma.$transaction.mockImplementation(async (callback: any) => {
      return await callback({
        task: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) },
        operationLog: { createMany: jest.fn().mockResolvedValue({}) },
        containerConcurrencyStatus: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) }
      });
    });

    const message = {
      taskId: "task-011",
      automationJobStatus: "Completed"
    };

    // Act
    await RunbookProcessorFunc(message, context);

    // Assert
    expect(utils.isAzureFilesError).toHaveBeenCalledWith(filesError);
    expect(context.error).toHaveBeenCalledWith(
      expect.stringContaining("Files操作失敗")
    );
  });

  /**
   * 試験観点：管理項目定義ファイル不存在時のエラー処理検証（設計文書4.b.ii要件）
   * 試験対象：RunbookProcessorFuncの管理項目定義ファイル存在チェック分岐
   * 試験手順：
   * 1. automationJobStatus=Completed、taskType=MGMT_ITEM_EXPORTでファイルが存在しない場合
   * 2. EMET0015エラーコードでタスクステータス更新を確認
   * 確認項目：
   * - ファイル不存在が正しく検出されること
   * - エラーコードEMET0015が設定されること
   */
  it("境界条件: 管理項目定義ファイル不存在", async () => {
    // Arrange
    const mockTask = {
      id: "task-012",
      status: AppConstants.TaskStatus.RunbookProcessing,
      taskType: AppConstants.TaskType.MgmtItemExport,
      licenseId: "license-012",
      targetVmName: "vm-012",
      targetContainerName: "container-012",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    // assetsfield_def.csvファイルが存在しない
    mockFileClient.exists.mockResolvedValue(false);

    prisma.task.findUnique.mockResolvedValue(mockTask);
    prisma.$transaction.mockImplementation(async (callback: any) => {
      return await callback({
        task: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) },
        operationLog: { createMany: jest.fn().mockResolvedValue({}) },
        containerConcurrencyStatus: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) }
      });
    });

    const message = {
      taskId: "task-012",
      automationJobStatus: "Completed"
    };

    // Act
    await RunbookProcessorFunc(message, context);

    // Assert
    expect(mockFileClient.exists).toHaveBeenCalled();
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("管理項目定義ファイルなし")
    );
  });

  /**
   * 試験観点：Failed状態でerrordetail.txt不存在時の処理検証（設計文書4.iii要件）
   * 試験対象：RunbookProcessorFuncのFailed状態・errordetail.txt不存在分岐
   * 試験手順：
   * 1. automationJobStatus=Failedでerrordetail.txtが存在しない場合
   * 2. EMET0012エラーコードでタスクステータス更新を確認
   * 確認項目：
   * - errordetail.txt不存在が正しく検出されること
   * - エラーコードEMET0012が設定されること
   */
  it("境界条件: Failed状態・errordetail.txt不存在", async () => {
    // Arrange
    const mockTask = {
      id: "task-013",
      status: AppConstants.TaskStatus.RunbookProcessing,
      targetVmName: "vm-013",
      targetContainerName: "container-013",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    // errordetail.txtが存在しない
    mockFileClient.exists.mockResolvedValue(false);

    prisma.task.findUnique.mockResolvedValue(mockTask);
    prisma.$transaction.mockImplementation(async (callback: any) => {
      return await callback({
        task: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) },
        operationLog: { createMany: jest.fn().mockResolvedValue({}) },
        containerConcurrencyStatus: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) }
      });
    });

    const message = {
      taskId: "task-013",
      automationJobStatus: "Failed",
      exception: "Azure Automation exception"
    };

    // Act
    await RunbookProcessorFunc(message, context);

    // Assert
    expect(mockFileClient.exists).toHaveBeenCalled();
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("基盤スクリプト起動エラー")
    );
  });

  /**
   * 試験観点：メンテナンス状態の処理検証（設計文書4要件）
   * 試験対象：RunbookProcessorFuncのRemoving/Stopped/Stopping状態処理分岐
   * 試験手順：
   * 1. automationJobStatus=Stoppedでメッセージを送信
   * 2. EMET0010エラーコードでタスクステータス更新を確認
   * 確認項目：
   * - メンテナンス状態が正しく処理されること
   * - エラーコードEMET0010が設定されること
   */
  it("境界条件: メンテナンス状態処理", async () => {
    // Arrange
    const mockTask = {
      id: "task-014",
      status: AppConstants.TaskStatus.RunbookProcessing,
      targetVmName: "vm-014",
      targetContainerName: "container-014",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    prisma.task.findUnique.mockResolvedValue(mockTask);
    prisma.$transaction.mockImplementation(async (callback: any) => {
      return await callback({
        task: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) },
        operationLog: { createMany: jest.fn().mockResolvedValue({}) },
        containerConcurrencyStatus: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) }
      });
    });

    const message = {
      taskId: "task-014",
      automationJobStatus: "Stopped"
    };

    // Act
    await RunbookProcessorFunc(message, context);

    // Assert
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("メンテナンス状態: Stopped")
    );
  });

  /**
   * 試験観点：Resuming状態でジョブ停止成功時の処理検証（設計文書4要件）
   * 試験対象：RunbookProcessorFuncのResuming状態・ジョブ停止成功分岐
   * 試験手順：
   * 1. automationJobStatus=ResumingでAzure Automation API成功をシミュレート
   * 2. EMET0010エラーコードでタスクステータス更新を確認
   * 確認項目：
   * - ジョブ停止が正常に完了すること
   * - エラーコードEMET0010が設定されること
   */
  it("正常系: Resuming状態・ジョブ停止成功", async () => {
    // Arrange
    const mockTask = {
      id: "task-019",
      status: AppConstants.TaskStatus.RunbookProcessing,
      targetVmName: "vm-019",
      targetContainerName: "container-019",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    (azureClients.stopAutomationJob as any).mockResolvedValue(undefined);

    prisma.task.findUnique.mockResolvedValue(mockTask);
    prisma.$transaction.mockImplementation(async (callback: any) => {
      return await callback({
        task: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) },
        operationLog: { createMany: jest.fn().mockResolvedValue({}) },
        containerConcurrencyStatus: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) }
      });
    });

    const message = {
      taskId: "task-019",
      automationJobStatus: "Resuming"
    };

    // Act
    await RunbookProcessorFunc(message, context);

    // Assert
    expect(azureClients.stopAutomationJob).toHaveBeenCalledWith("task-019");
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("ジョブ停止完了: Resuming")
    );
  });

  /**
   * 試験観点：Resuming状態でジョブ停止失敗時の処理検証（設計文書4要件）
   * 試験対象：RunbookProcessorFuncのResuming状態・ジョブ停止失敗分岐
   * 試験手順：
   * 1. automationJobStatus=ResumingでAzure Automation API失敗をシミュレート
   * 2. エラーログ記録と例外throwを確認
   * 確認項目：
   * - ジョブ停止失敗が正しく検出されること
   * - 例外がthrowされてリトライが発生すること
   */
  it("補償処理: Resuming状態・ジョブ停止失敗", async () => {
    // Arrange
    const mockTask = {
      id: "task-015",
      status: AppConstants.TaskStatus.RunbookProcessing,
      targetVmName: "vm-015",
      targetContainerName: "container-015",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    const stopError = new Error("Azure Automation API failed");
    (azureClients.stopAutomationJob as any).mockRejectedValue(stopError);

    prisma.task.findUnique.mockResolvedValue(mockTask);

    const message = {
      taskId: "task-015",
      automationJobStatus: "Resuming"
    };

    // Act & Assert
    await expect(RunbookProcessorFunc(message, context)).rejects.toThrow(
      "Azure Automation API failed"
    );
    expect(context.error).toHaveBeenCalledWith(
      expect.stringContaining("ジョブ停止失敗")
    );
  });

  /**
   * 試験観点：Timeout状態でジョブ停止失敗時の処理検証（設計文書4要件）
   * 試験対象：RunbookProcessorFuncのTimeout状態・ジョブ停止失敗分岐
   * 試験手順：
   * 1. automationJobStatus=TimeoutでAzure Automation API失敗をシミュレート
   * 2. エラーログ記録と例外throwを確認
   * 確認項目：
   * - タイムアウト時ジョブ停止失敗が正しく検出されること
   * - 例外がthrowされてリトライが発生すること
   */
  it("補償処理: Timeout状態・ジョブ停止失敗", async () => {
    // Arrange
    const mockTask = {
      id: "task-016",
      status: AppConstants.TaskStatus.RunbookProcessing,
      targetVmName: "vm-016",
      targetContainerName: "container-016",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    const stopError = new Error("Azure Automation API timeout");
    (azureClients.stopAutomationJob as any).mockRejectedValue(stopError);

    prisma.task.findUnique.mockResolvedValue(mockTask);

    const message = {
      taskId: "task-016",
      automationJobStatus: "Timeout"
    };

    // Act & Assert
    await expect(RunbookProcessorFunc(message, context)).rejects.toThrow(
      "Azure Automation API timeout"
    );
    expect(context.error).toHaveBeenCalledWith(
      expect.stringContaining("タイムアウト時ジョブ停止失敗")
    );
  });

  /**
   * 試験観点：予期しないautomationJobStatusのエラー処理検証（設計文書要件）
   * 試験対象：RunbookProcessorFuncの予期しないステータス分岐
   * 試験手順：
   * 1. 未定義のautomationJobStatusでメッセージを送信
   * 2. エラーログ記録と例外throwを確認
   * 確認項目：
   * - 予期しないステータスが正しく検出されること
   * - 例外がthrowされてリトライが発生すること
   */
  it("異常系: 予期しないautomationJobStatus", async () => {
    // Arrange
    const mockTask = {
      id: "task-017",
      status: AppConstants.TaskStatus.RunbookProcessing,
      targetVmName: "vm-017",
      targetContainerName: "container-017",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    prisma.task.findUnique.mockResolvedValue(mockTask);

    const message = {
      taskId: "task-017",
      automationJobStatus: "UnknownStatus"
    };

    // Act & Assert
    await expect(RunbookProcessorFunc(message, context)).rejects.toThrow(
      "予期しないautomationJobStatus: UnknownStatus"
    );
    expect(context.error).toHaveBeenCalledWith(
      expect.stringContaining("予期しないステータス: UnknownStatus")
    );
  });

  /**
   * 試験観点：Azure Files作業ディレクトリ削除失敗時の処理検証（設計文書5要件）
   * 試験対象：RunbookProcessorFuncのAzure Filesクリーンアップ分岐
   * 試験手順：
   * 1. 正常なタスク処理後にAzure Files削除エラーを発生させる
   * 2. エラーログ記録を確認（例外はthrowしない）
   * 確認項目：
   * - Azure Files削除失敗が正しくログに記録されること
   * - 処理が継続されること（例外がthrowされない）
   */
  it("境界条件: Azure Files削除失敗", async () => {
    // Arrange
    const mockTask = {
      id: "task-018",
      status: AppConstants.TaskStatus.RunbookProcessing,
      taskType: AppConstants.TaskType.MgmtItemImport,
      targetVmName: "vm-018",
      targetContainerName: "container-018",
      updatedAt: new Date("2025-01-11T12:00:00Z")
    };

    // Azure Files削除でエラーを発生させる
    const deleteError = new Error("Azure Files delete failed");
    utils.deleteTaskWorkspaceDirectory.mockRejectedValue(deleteError);

    prisma.task.findUnique.mockResolvedValue(mockTask);
    prisma.$transaction.mockImplementation(async (callback: any) => {
      return await callback({
        task: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) },
        operationLog: { createMany: jest.fn().mockResolvedValue({}) },
        containerConcurrencyStatus: { updateMany: jest.fn().mockResolvedValue({ count: 1 }) }
      });
    });

    const message = {
      taskId: "task-018",
      automationJobStatus: "Completed"
    };

    // Act
    await RunbookProcessorFunc(message, context);

    // Assert
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("Azure Files作業ディレクトリ削除失敗")
    );
    // 処理が正常に完了することを確認（例外がthrowされない）
  });
});
