# 组件：登录功能 (Login)

## 1. 概要 (Overview)

### 1.1 目的 (Purpose)
本组件旨在为“JCS 端点资产与任务管理系统”提供安全的用户身份验证机制。通过与外部身份提供商Keycloak集成，确保只有经过授权的用户（顾客系统管理员）才能访问门户系统及其功能，从而防止未经授权的访问并保护系统资源。本功能实现了基于用户ID、密码以及多因素认证（通过移动设备OTP）的登录流程。

### 1.2 用户故事/需求 (User Stories/Requirements)
- 作为一名顾客系统管理员，我希望能够使用我的用户ID和密码安全地登录到门户系统，以便开始使用各项资产管理和任务执行功能。
- 作为一名顾客系统管理员，我希望在登录过程中通过移动设备进行一次性密码（OTP）验证，以增强我账户的安全性。
- 作为一名系统（服务）管理员，我希望用户登录认证流程由集中的Keycloak服务处理，以便于统一管理用户账户和认证策略。
- 作为一名用户，如果我连续多次输入错误的密码，我希望我的账户会被临时锁定，以防止暴力破解尝试。
- 作为一名用户，如果我所属的合同环境被服务运营人员设置为无效，我希望在尝试登录时被明确告知无法访问。

### 1.3 高层依赖与交互 (High-Level Dependencies & Interactions)
- **身份提供商 (IdP)**: 核心依赖 **Keycloak** 服务进行用户凭据验证、多因素认证处理、用户账户管理（如密码策略、锁定策略）和会话管理。
- **门户应用 (Next.js)**:
    - 用户访问门户应用的初始页面时，若未认证，将被重定向到Keycloak的登录页面。
    - 登录成功后，Keycloak将用户重定向回门户应用，并传递认证凭据（如授权码）。
    - 门户应用后端 (API Routes) 使用此凭据从Keycloak获取访问令牌 (Access Token) 和身份令牌 (ID Token)，并建立用户会话。
- **浏览器**: 用户通过支持的Web浏览器（Microsoft Edge, Google Chrome）与门户应用及Keycloak登录页面进行交互。
- **数据库 (间接)**: Keycloak自身依赖其数据库存储用户信息、Realm配置等。门户应用数据库（Azure SQL DB）中的 `License` 表包含“环境无効化フラグ”，Keycloak在认证流程中可能需要（通过门户后端或扩展）查询此信息以实现登录抑制。`AuditLogin` 表用于记录登录审计日志。

## 2. 功能规格 (Functional Specifications)

### 2.1 主要流程 (High-Level Flow)
1.  用户通过浏览器访问门户系统的URL。
2.  门户应用检测到用户未认证，将用户重定向到Keycloak配置的登录页面。
3.  用户在Keycloak登录页面输入用户ID和密码，点击登录。
4.  Keycloak验证用户ID和密码的有效性。
    *   若无效（用户不存在、密码错误、账户锁定、环境无效等），Keycloak显示相应的错误信息。
5.  若用户ID和密码验证成功，Keycloak进入多因素认证（OTP）流程：
    *   **首次登录/OTP未配置**: Keycloak显示二维码，引导用户使用移动认证应用（如Google Authenticator, Microsoft Authenticator）扫描并配置OTP。配置完成后，用户输入当前生成的OTP。
    *   **后续登录**: Keycloak直接提示用户输入移动认证应用生成的当前OTP。
6.  Keycloak验证OTP的有效性。
    *   若无效，Keycloak显示错误信息。
7.  若OTP验证成功，Keycloak认为用户认证成功，生成授权码，并将用户重定向回门户应用预设的回调URL，并附带授权码。
8.  门户应用后端接收授权码，并使用该授权码向Keycloak交换获取Access Token和ID Token。
9.  门户应用后端验证Token的有效性，提取用户信息，建立用户会话（例如，通过加密Cookie）。
10. 门户应用将用户导航至登录后的默认页面（例如，“服务器列表”页面）。
11. 登录成功事件被记录到门户的 `AuditLogin` 审计日志中。登录失败事件由Keycloak在其审计日志中记录。

```mermaid
sequenceDiagram
    participant User as 👤 用户 (Browser)
    participant NextJsApp as 🌐 Next.js 应用 (Portal)
    participant Keycloak as 🔐 Keycloak (IdP)
    participant MobileApp as 📱 移动认证App
    participant AuditLogDB as 💾 AuditLogin表 (Portal DB)

    User->>NextJsApp: 访问门户URL
    NextJsApp-->>User: 重定向至Keycloak登录页
    User->>Keycloak: 输入用户ID和密码, 点击登录
    Keycloak->>Keycloak: 验证凭据 (密码, 账户状态, 环境有效性等)
    alt 凭据无效
        Keycloak-->>User: 显示错误信息 (如: 无效用户/密码, 账户锁定)
    else 凭据有效
        Keycloak-->>User: 请求OTP输入 (首次显示QR码)
        User->>MobileApp: 获取OTP
        MobileApp-->>User: 显示OTP
        User->>Keycloak: 输入OTP
        Keycloak->>Keycloak: 验证OTP
        alt OTP无效
            Keycloak-->>User: 显示错误信息 (如: 无效OTP)
        else OTP有效
            Keycloak-->>NextJsApp: 重定向回Portal (携带授权码)
            NextJsApp->>Keycloak: 用授权码交换Tokens (Access, ID)
            Keycloak-->>NextJsApp: 返回Tokens
            NextJsApp->>NextJsApp: 验证Tokens, 创建用户会话
            NextJsApp->>AuditLogDB: 记录登录成功日志
            NextJsApp-->>User: 导航至主界面 (如服务器列表)
        end
    end
```

### 2.2 业务规则 (Business Rules)
-   **认证强制性**: 所有需要访问受保护资源的门户功能都必须先完成登录认证。
-   **用户账户来源**: 用户账户（用户ID、密码、OTP配置）由系统管理员在Keycloak中创建和管理。
-   **用户ID规范**:
    *   长度：8至50个字符。
    *   字符集：仅允许半角小写英文字母、数字和点号 (`.`)。
-   **密码策略** (由Keycloak强制执行，`fs.md`中描述的是对Keycloak的配置要求)：
    *   长度：8至128个字符。
    *   字符集：半角英文字母（大小写敏感）、数字，以及特定符号 (`! " # $ % & ' ( ) * + , - . / : ; < = > ? @ [ \ ] ^ _ ` { | } ~` 和半角空格)。
    *   复杂度：至少包含2种不同类型的字符（例如，字母和数字）。
    *   历史限制：新密码不能与当前密码相同。
    *   用户ID限制：密码不能与用户ID相同。
-   **多因素认证 (OTP)**:
    *   强制启用，无法跳过。
    *   OTP长度为6位半角数字（此为Keycloak配置，因Microsoft Authenticator对8位OTP支持问题）。
-   **会话有效期**:
    *   用户登录后的会话（由Keycloak颁发的Token定义）有效期默认为30分钟。此值可在Keycloak的Realm设置中调整。
    *   若用户在会话有效期内无操作，会话到期后需要重新登录。
-   **环境无效化登录抑制**:
    *   若与用户关联的契约ID在门户数据库 `License` 表中的 `環境無効化フラグ` 被设置为有效（表示环境不可用），则该用户即使凭据正确也无法完成登录，Keycloak应（通过与门户后端集成或自定义SPI）阻止登录并提示用户环境不可用。
-   **账户锁定策略** (由Keycloak强制执行)：
    *   连续密码输入错误达到10次，该用户账户将被临时锁定。
    *   锁定时长为30分钟。在锁定期内，即使用户输入正确密码也无法登录。30分钟后账户自动解锁。
    *   登录成功会重置连续失败计数。
    *   环境无效化登录抑制优先于账户锁定。
-   **浏览器关闭与登出**: 若浏览器设置为“打开新标签页时从上次会话继续”（通常指恢复Cookie的设置，原文为「新しいタブを開く」が適用されている場合），则关闭浏览器可能不会立即结束Keycloak的SSO会话。标准的登出操作应通过点击门户的“登出”按钮完成，这将明确地使门户会话和Keycloak会话失效。
-   **登录审计**: 成功的登录操作由门户应用记录到 `AuditLogin` 表。失败的登录尝试（如密码错误、OTP错误、账户锁定）由Keycloak在其自身的审计日志中记录。

### 2.3 用户界面概述 (User Interface Overview)
-   **主要界面流程与页面** (参考 `fs.md` 图4.1.6 (1)-(5)):
    1.  **门户初始页 (Portal Top Page)**: 若用户未认证，通常包含一个“登录”按钮或自动重定向。 (图4.1.6 (1))
    2.  **Keycloak登录页 (Keycloak Login Screen)**: 用户在此输入用户ID和密码。包含用户ID输入框、密码输入框、登录按钮。错误信息（如“无效的用户名或密码。”）在此页面显示。 (图4.1.6 (2), (5))
    3.  **Keycloak OTP首次配置页 (Keycloak Mobile First Time OTP Setup Screen)**: 首次登录或OTP未配置时显示。包含一个二维码供移动认证应用扫描，以及OTP输入框。 (图4.1.6 (3))
    4.  **Keycloak OTP验证页 (Keycloak Mobile OTP Verification Screen)**: 后续登录时显示。包含OTP输入框。 (图4.1.6 (4))
-   **主要交互点**:
    *   用户在门户初始页点击“登录”（或被自动重定向）。
    *   用户在Keycloak登录页输入用户ID、密码，点击“登录”。
    *   （首次）用户使用移动认证应用扫描QR码，将账户与应用绑定。
    *   用户在Keycloak OTP页面输入由移动认证应用生成的6位OTP，点击“提交”或类似按钮。
-   **关键显示信息 (Keycloak页面)**:
    *   用户ID输入提示。
    *   密码输入提示。
    *   OTP输入提示。
    *   错误消息（如无效凭据、无效OTP、账户锁定、环境不可用等）。
    *   （首次OTP）QR码。

### 2.4 前提条件 (Preconditions)
-   用户处于未登录状态。
-   系统管理员已在Keycloak中正确创建并配置了用户账户，包括用户ID、初始密码（或密码设置流程）、以及启用了OTP作为必要认证步骤。
-   用户的契约ID已在Keycloak用户属性中或通过其他方式与用户账户关联，以便门户应用和Keycloak（可能通过扩展）可以检查环境有效性。
-   Keycloak服务本身及其依赖的数据库必须正常运行。
-   门户应用与Keycloak之间的OIDC/OAuth2客户端配置（如Client ID, Client Secret, Redirect URIs）必须正确。
-   用户使用的浏览器（Microsoft Edge或Google Chrome）必须能够正常访问门户应用和Keycloak服务的URL，并支持必要的Web标准（Cookies, JavaScript, TLS等）。
-   用户的移动设备上已安装兼容的OTP认证应用（如Google Authenticator, Microsoft Authenticator）。

### 2.5 制约事项 (Constraints/Limitations)
-   **浏览器支持**: 严格限定为Microsoft Edge和Google Chrome。
-   **语言支持**: 门户及Keycloak相关界面的显示语言仅支持日语。
-   **认证流程固定**: 用户必须完成用户ID/密码验证和OTP验证两个步骤才能登录，不能跳过任一步骤。
-   **会话管理**: 依赖Keycloak的会话管理机制和Token有效期。门户应用本身的会话应与Keycloak会话同步。
-   **密码保存**: 出于安全考虑，不应鼓励或依赖浏览器的密码保存功能。Keycloak登录页面可能通过`autocomplete="off"`等方式尝试阻止。

### 2.6 注意事项 (Notes/Considerations)
-   Keycloak的Realm配置（如Token有效期、密码策略、锁定策略、OTP策略）对登录功能的用户体验和安全性有直接影响，应由系统管理员根据业务需求和安全策略审慎配置。
-   门户应用需要安全地处理从Keycloak获取的Tokens，并防止CSRF、XSS等Web安全风险。
-   首次OTP配置流程的用户引导需要清晰明了。
-   “环境无效化”检查逻辑的实现方式（是在Keycloak端通过SPI实现，还是门户后端在获取Token后校验并拒绝会话）需要详细设计。`fs.md` 暗示Keycloak层面进行判断。

### 2.7 错误处理概述 (Error Handling Overview)
-   **无效用户ID或密码**: Keycloak登录页面显示统一样式错误信息（如 `fs.md` 描述的“無効なユーザー名またはパスワードです。”），密码框内容被清空，输入框可能标红。
-   **账户锁定**: 若用户因多次密码错误导致账户被锁定，在锁定期内尝试登录时，Keycloak应显示账户已锁定的信息。
-   **OTP验证失败**: Keycloak OTP页面显示OTP无效的错误信息。
-   **环境无效**: 若用户尝试登录一个已被设置为无效的环境，Keycloak（或门户在重定向后）应显示环境不可用的特定错误信息（如 `fs.md` EMEC0005）。
-   **Keycloak服务不可达或内部错误**: 若浏览器无法连接Keycloak，或Keycloak自身发生错误，用户将看到浏览器错误页面或Keycloak的通用错误页面。门户应用应能处理从Keycloak回调时可能发生的错误（如错误参数）。
-   **门户后端错误**: 若门户后端在与Keycloak交换Token或创建会话时发生错误，应向用户显示通用的系统错误页面，并记录详细日志。

### 2.8 相关功能参考 (Related Functional References)
*   **认证核心**: Keycloak身份提供商 - 所有用户认证逻辑的中心。
*   **登录后导航**: [主界面 (Main Screen)](./02-main.md) 或 [服务器列表 (Server List)](./03-server-list.md) - 用户成功登录后通常被导航到的目标页面。
*   **补充认证操作**:
  *   [密码修改 (Password Change)](./01-password-change.md) - 用户登录后修改自身密码的功能，也可能通过Keycloak实现。
  *   [登出 (Logout)](./01-logout.md) - 用户主动结束会话的功能，需要同时使门户会话和Keycloak会话失效。
*   **系统整体架构**: [系统架构](../architecture/system-architecture.md) - 描述了门户应用与Keycloak的集成方式以及认证流程在整体架构中的位置。
*   **数据模型 (间接相关)**:
  *   [License表](../models/license.md) - 包含 `環境無効化フラグ`，影响登录行为。
  *   [AuditLogin表](../models/audit-login.md) - 记录登录成功审计日志。
*   **源功能规格**: `fs.md` (通常位于 `docs-delivery/機能仕様書/` 或项目指定的源文档位置) - 本组件功能规格的原始日文描述，特别是其"4.1 ログイン"章节。
