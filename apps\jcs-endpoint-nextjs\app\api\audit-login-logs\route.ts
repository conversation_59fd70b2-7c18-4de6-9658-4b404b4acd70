/**
 * @file route.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2024, 2025, Hitachi Solutions, Ltd.
 */

import { AuditActions } from "@/app/lib/actions/audit";
import { handleApiError } from "@/app/lib/portal-error";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";
// 監査ログの結果メッセージ
const AUDIT_LOGIN_MESSAGES = {
  SUCCESS: "監査ログが正常に記録されました。",
  FAILURE: "監査ログの記録に失敗しました。",
};

// ログイン監査ログの POST リクエストの処理
export async function POST(req: Request) {
  // リクエストから監査タイプとログインメッセージを取得
  const { auditType, loginMessage } = await req.json();

  if (!["LOGIN", "LOGIN_FAILURE", "LOGOUT"].includes(auditType)) {
    return NextResponse.json({ error: "Params Error" }, { status: 400 });
  }

  // サーバーセッションを取得
  const session = await getIronSession<SessionData>(cookies(), sessionOptions);

  try {
    // 監査ログの記録
    await AuditActions.createAuditEvent(
      auditType,
      session!.user.userId,
      loginMessage,
    );

    // 正常なレスポンスを返す
    return NextResponse.json({ message: AUDIT_LOGIN_MESSAGES.SUCCESS });
  } catch (error) {
    return handleApiError(error);
  }
}
