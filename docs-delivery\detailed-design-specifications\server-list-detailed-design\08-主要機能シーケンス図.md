## 主要機能シーケンス図

本章では、サーバ一覧機能における主要な操作シーケンスを示す。図中のエラー応答に示されるmessageIdは、本設計書の「エラーメッセージ一覧」で定義される。

### サーバ一覧 データ表示シーケンス図

本図は、ユーザーがサーバ一覧画面にアクセスした際、または画面上でフィルタリング、ページネーション、ソート等の操作を行った際の、基本的なデータ表示フローの概要を示す。

```mermaid
sequenceDiagram
    title サーバ一覧 データ表示シーケンス図

    actor User as ユーザー
    participant Front as フロントエンド
    participant Next as バックエンド
    participant DB as データベース

    User->>Front: 1. サーバ一覧画面へアクセス<br/>(または操作によりURLパラメータ変更)
    activate Front
    Note over Front,Next: URLパラメータがNext.js App Routerを<br/>介してサーバコンポーネントへ渡される
    Front->>Next: 2. サーバ一覧データ要求
    activate Next
    Next->>Next: 3. URLパラメータ解析
    Next->>DB: 4. 条件に基づきサーバ情報取得
    activate DB
    DB-->>Next: 5. サーバ情報返却
    deactivate DB
    Next->>Next: 6. サーバ種別等を日本語表示へ変換 (LOV参照)
    Next-->>Front: 7. 描画用サーバ一覧データ返却
    deactivate Next
    Front->>User: 8. サーバ一覧情報を画面へ表示
    deactivate Front
```

### サーバ一覧からのタスク発行シーケンス図

#### 操作ログのエクスポートタスク　発行シーケンス図
本図は、ユーザーがサーバ一覧から「操作ログのエクスポート」タスクを発行する際の主要なインタラクションを示す。

```mermaid
sequenceDiagram
    title 操作ログのエクスポートタスク　発行シーケンス図
    
    actor User as ユーザー
    participant FrontendApp as フロントエンド<br/>(ポータル)
    participant NextJsServer as バックエンド<br/>(createTaskAction)
    participant Database as データベース
    participant MessageQueue as Service Bus キュー

    User->>FrontendApp: 「タスクを選択」メニューの<br/>「操作ログのエクスポート」を選択
    activate FrontendApp
    FrontendApp->>User: パラメータ入力モーダル表示
    User->>FrontendApp: 日付範囲等パラメータ入力
    FrontendApp->>FrontendApp: [クライアント] パラメータ検証
    alt パラメータ無効
        FrontendApp->>User: パラメータ入力モーダル内に<br/>エラーメッセージ表示<br/>(EMEC0016, EMEC0024, EMEC0020)
    else パラメータ有効
        User->>FrontendApp: パラメータ入力モーダルの<br/>「OK」ボタン押下
        FrontendApp->>User: 最終確認モーダル表示<br/>「{サーバ名}の操作ログをエクスポートします。<br/>よろしいですか？」
        User->>FrontendApp: 最終確認モーダルで「OK」押下
        FrontendApp->>NextJsServer: タスク作成リクエスト<br/>(createTaskAction 呼出)<br/>FormData: {taskType, serverId,<br/>exportStartDate, exportEndDate}

        activate NextJsServer
        NextJsServer->>NextJsServer: 共通パラメータ解析、検証
        alt 共通パラメータ検証失敗
            NextJsServer-->>FrontendApp: エラー応答 (EMEC0021)
        else 共通パラメータ検証成功
            NextJsServer->>Database: サーバ構成取得
            activate Database
            Database->>NextJsServer: サーバ構成情報
            deactivate Database
            alt サーバ構成取得失敗/構成不備
                NextJsServer-->>FrontendApp: エラー応答 (EMEC0021)
            else サーバ構成取得成功
                NextJsServer->>Database: コンテナ実行状態確認
                activate Database
                Database-->>NextJsServer: コンテナ実行状態返却
                deactivate Database
                alt コンテナ実行状態取得失敗/構成不備
                    NextJsServer-->>FrontendApp: エラー応答 (EMEC0021)
                else コンテナBUSY
                    NextJsServer-->>FrontendApp: エラー応答 (EMEC0022)
                else コンテナIDLE または レコード存在しない
                    NextJsServer->>NextJsServer: taskId生成、タスク名生成
                    NextJsServer->>NextJsServer: 操作ログのエクスポート<br/>固有パラメータ検証<br/>(日付範囲等サーバサイド検証)
                    alt 固有パラメータ検証失敗
                        NextJsServer-->>FrontendApp: エラー応答(EMEC0016, EMEC0024, EMEC0020)
                    else 固有パラメータ検証成功
                        NextJsServer->>NextJsServer: タスクレコード用パラメータ構築<br/>{ "exportStartDate", "exportEndDate" }
                        NextJsServer->>Database: DBトランザクション開始
                        activate Database
                        alt コンテナレコード<br>存在しない
                            NextJsServer->>Database: コンテナレコード作成
                        end
                        NextJsServer->>Database: Taskレコード作成
                        alt レコード作成失敗
                            NextJsServer->>Database: Txロールバック
                            NextJsServer-->>FrontendApp: エラー応答 (EMEC0021)
                        else レコード作成成功
                            NextJsServer->>Database: Txコミット
                            deactivate Database
                            NextJsServer->>MessageQueue: タスクメッセージ送信<br/>(TaskInputQueue)
                            activate MessageQueue
                            MessageQueue-->>NextJsServer: 送信結果
                            deactivate MessageQueue
                            alt メッセージ送信失敗
                                NextJsServer->>Database: タスクレコード削除
                                NextJsServer-->>FrontendApp: エラー応答 (EMEC0019)
                            else メッセージ送信成功
                                NextJsServer->>NextJsServer: キャッシュ無効化<br/>(revalidatePath)
                                NextJsServer-->>FrontendApp: 成功応答 (EMEC0025)
                            end
                        end
                    end
                end
            end
        end
        
        deactivate NextJsServer
        FrontendApp->>FrontendApp: 全てのモーダルを閉じる
        FrontendApp->>User: 処理結果表示<br/>(応答のmessageに基づく)
    end
    deactivate FrontendApp
```

#### 管理項目定義のインポートタスク　発行シーケンス図
本図は、ユーザーがサーバ一覧から「管理項目定義のインポート」タスクを発行する際の主要なインタラクションを示す。

```mermaid
sequenceDiagram
    title 管理項目定義のインポートタスク　発行シーケンス図
    
    actor User as ユーザー
    participant FrontendApp as フロントエンド<br/>(ポータル)
    participant NextJsServer as バックエンド<br/>(createTaskAction)
    participant ManagerFilesBlob as Blobストレージ<br/>(assetsfield-def コンテナ)
    participant Database as データベース
    participant MessageQueue as Service Bus キュー

    User->>FrontendApp: 「タスクを選択」メニューの<br/>「管理項目定義のインポート」を選択
    activate FrontendApp
    FrontendApp->>User: パラメータ入力モーダル表示
    User->>FrontendApp: CSVファイル選択
    FrontendApp->>FrontendApp: [クライアント]パラメータ検証
    alt パラメータ無効
        FrontendApp->>User: パラメータ入力モーダル内に<br/>エラーメッセージ表示<br/>(EMEC0016, EMEC0017, EMEC0028)
    else パラメータ有効
        User->>FrontendApp: パラメータ入力モーダルの<br/>「OK」ボタン押下
        FrontendApp->>User: 最終確認モーダル表示<br/>「{サーバ名}の管理項目定義をインポートします。<br/>よろしいですか？」
        User->>FrontendApp: 最終確認モーダルで「OK」押下
        FrontendApp->>NextJsServer: タスク作成リクエスト<br/>(createTaskAction 呼出)<br/>FormData: {taskType, serverId,<br/>importFile, originalFileName}
        activate NextJsServer
        NextJsServer->>NextJsServer: 共通パラメータ解析、検証
        alt 共通パラメータ検証失敗
            NextJsServer-->>FrontendApp: エラー応答 (EMEC0021)
        else 共通パラメータ検証成功
            NextJsServer->>Database: サーバ構成取得
            activate Database
            Database->>NextJsServer: サーバ構成情報
            deactivate Database
            alt サーバ構成取得失敗/構成不備
                NextJsServer-->>FrontendApp: エラー応答 (EMEC0021)
            else サーバ構成取得成功
                NextJsServer->>Database: コンテナ実行状態確認
                activate Database
                Database-->>NextJsServer: コンテナ実行状態返却
                deactivate Database
                alt コンテナ実行状態取得失敗/構成不備
                    NextJsServer-->>FrontendApp: エラー応答 (EMEC0021)
                else コンテナBUSY
                    NextJsServer-->>FrontendApp: エラー応答 (EMEC0022)
                else コンテナIDLE または レコード存在しない
                    NextJsServer->>NextJsServer: taskId生成、タスク名生成
                    NextJsServer->>NextJsServer: 管理項目定義のインポート<br/>固有パラメータ検証<br/>(MIMEタイプ 'text/csv' 等サーバサイド検証)
                    alt 固有パラメータ検証失敗
                        NextJsServer-->>FrontendApp: エラー応答 (EMEC0016, EMEC0017, EMEC0028)
                    else 固有パラメータ検証成功
                        NextJsServer->>ManagerFilesBlob: ファイルアップロード<br/>(パス: {licenseId}/imports/<br/>{taskId}/assetsfield_def.csv)
                        activate ManagerFilesBlob
                        ManagerFilesBlob-->>NextJsServer: アップロード結果
                        deactivate ManagerFilesBlob
                        alt アップロード失敗
                            NextJsServer-->>FrontendApp: エラー応答 (EMEC0018)
                        else アップロード成功
                            NextJsServer->>NextJsServer: タスクレコード用パラメータ構築<br/>{ "importedFileBlobPath", "originalFileName"}
                        
                            NextJsServer->>Database: DBトランザクション開始
                            activate Database
                            alt コンテナレコード存在しない
                                NextJsServer->>Database: コンテナレコード作成
                            end
                            NextJsServer->>Database: Taskレコード作成
                            
                            alt レコード作成失敗
                                NextJsServer->>Database: Txロールバック
                                NextJsServer->>ManagerFilesBlob: 一時ファイル削除
                                NextJsServer-->>FrontendApp: エラー応答 (EMEC0021)
                            else レコード作成成功
                                NextJsServer->>Database: Txコミット
                                    deactivate Database
                                NextJsServer->>MessageQueue: タスクメッセージ送信<br/>(TaskInputQueue)
                                activate MessageQueue
                                MessageQueue-->>NextJsServer: 送信結果
                                deactivate MessageQueue
                                alt メッセージ送信失敗
                                    NextJsServer->>Database: タスクレコード削除
                                    NextJsServer->>ManagerFilesBlob: 一時ファイル削除
                                    NextJsServer-->>FrontendApp: エラー応答 (EMEC0019)
                                else メッセージ送信成功
                                    NextJsServer->>NextJsServer: キャッシュ無効化<br/>(revalidatePath)
                                    NextJsServer-->>FrontendApp: 成功応答 (EMEC0025)
                                end
                                
                            end
                        end
                    end
                end
            end
        end
        deactivate NextJsServer
        
        FrontendApp->>FrontendApp: 全てのモーダルを閉じる
        FrontendApp->>User: 処理結果表示<br/>(応答のmessageに基づく)
    end
    deactivate FrontendApp
```

#### 管理項目定義エクスポートタスク発行シーケンス図
本図は、ユーザーがサーバ一覧から「管理項目定義のエクスポート」タスクを発行する際の主要なインタラクションを示す。

```mermaid
sequenceDiagram
    title 管理項目定義のエクスポートタスク　発行シーケンス図
    
    actor User as ユーザー
    participant FrontendApp as フロントエンド<br/>(ポータル)
    participant NextJsServer as バックエンド<br/>(createTaskAction)
    participant Database as データベース
    participant MessageQueue as Service Bus キュー

    User->>FrontendApp: 「タスクを選択」メニューの<br/>「管理項目定義のエクスポート」を選択
    activate FrontendApp
    FrontendApp->>User:  [フロントエンド] 最終確認モーダル表示<br/>「{サーバ名}の管理項目定義をエクスポートします。<br/>よろしいですか？」
    User->>FrontendApp: 最終確認モーダルで「OK」押下

    FrontendApp->>NextJsServer: タスク作成リクエスト<br/>(createTaskAction 呼出)<br/>FormData: {taskType, serverId}
    activate NextJsServer
    NextJsServer->>NextJsServer: 共通パラメータ解析、検証
    alt 共通パラメータ検証失敗
        NextJsServer-->>FrontendApp: エラー応答 (EMEC0021)
    else 共通パラメータ検証成功
        NextJsServer->>Database: サーバ構成取得
        activate Database
        Database->>NextJsServer: サーバ構成情報
        deactivate Database
        alt サーバ構成取得失敗/構成不備
            NextJsServer-->>FrontendApp: エラー応答 (EMEC0021)
        else サーバ構成取得成功
            NextJsServer->>Database: コンテナ実行状態確認
            activate Database
            Database-->>NextJsServer: コンテナ実行状態返却
            deactivate Database
            alt コンテナ実行状態取得失敗/構成不備
                NextJsServer-->>FrontendApp: エラー応答 (EMEC0021)
            else コンテナBUSY
                NextJsServer-->>FrontendApp: エラー応答 (EMEC0022)
            else コンテナIDLE または レコード存在しない
                NextJsServer->>NextJsServer: taskId生成、タスク名生成
                NextJsServer->>Database: DBトランザクション開始
                activate Database
                alt コンテナレコード<br/>存在しない
                    NextJsServer->>Database: コンテナレコード作成
                end
                NextJsServer->>Database: Taskレコード作成
                alt レコード作成失敗
                    NextJsServer->>Database: Txロールバック
                    NextJsServer-->>FrontendApp: エラー応答 (EMEC0021)
                else レコード作成成功
                    NextJsServer->>Database: Txコミット
                    deactivate Database
                    NextJsServer->>MessageQueue: タスクメッセージ送信<br/>(TaskInputQueue)
                    activate MessageQueue
                    MessageQueue-->>NextJsServer: 送信結果
                    deactivate MessageQueue
                    alt メッセージ送信失敗
                        NextJsServer->>Database: タスクレコード削除
                        NextJsServer-->>FrontendApp: エラー応答 (EMEC0019)
                    else メッセージ送信成功
                        NextJsServer->>NextJsServer: キャッシュ無効化<br/>(revalidatePath)
                        NextJsServer-->>FrontendApp: 成功応答 (EMEC0025)
                    end
                end
            end
        end
    end
        
    deactivate NextJsServer
    FrontendApp->>FrontendApp: 全てのモーダルを閉じる
    FrontendApp->>User: 処理結果表示<br/>(応答のmessageに基づく)
    deactivate FrontendApp
```
