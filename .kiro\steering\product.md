# JCS 端点资产与任务管理系统

JCS 端点资产与任务管理系统是一个用于管理服务器资产和相关任务的平台。系统基于 Monorepo 架构构建，包含 Next.js 前端应用和 Azure Functions 后端服务。

## 核心功能

- 服务器资产管理
- 任务创建和执行
- 任务监控和状态追踪
- 自动化操作通过 Azure Automation Runbooks

## 项目语言

项目主要使用中文进行开发和文档编写，但文件名和代码标识符使用英文。部分交付文档使用日语。

## 文档结构

项目采用 Markdown 文件作为单一事实来源 (SSoT)，所有技术设计、功能规格和实现细节的权威信息源均存储在 `docs/` 目录下。传统的交付文档（如日文的《機能仕様書》和《詳細設計書》）应视为基于这些 Markdown 文档在特定时间点的输出或总结。

任何设计变更、功能调整或实现细节的更新，都应首先在对应的 Markdown 文件中进行，然后再同步到交付文档。