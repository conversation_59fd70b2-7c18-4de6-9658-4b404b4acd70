/**
 * Playwright 全局清理脚本
 * 在所有测试结束后执行一次
 */
import { FullConfig } from '@playwright/test';
import * as path from 'path';
import * as fs from 'fs';
import { cleanupLovData, disconnectLovPrisma } from './support/lov-data.helper';

/**
 * 全局清理函数
 * @param config Playwright 配置
 */
async function globalTeardown(config: FullConfig) {
  console.log('🧹 开始 E2E 测试环境全局清理...');

  // 1. 清理 LOV 测试数据
  try {
    await cleanupLovData();
    await disconnectLovPrisma();
    console.log('🌱 LOV 测试数据清理完成');
  } catch (error) {
    console.error('❌ LOV 数据清理失败:', error);
    // 不抛出异常，避免影响测试报告生成
  }

  // 2. 检查测试结果
  const testResultsDir = path.join(__dirname, '..', 'test-results');
  const htmlReportDir = path.join(testResultsDir, 'html-report');

  // 检查是否有测试失败
  const lastRunPath = path.join(testResultsDir, '.last-run.json');
  let hasFailures = false;

  if (fs.existsSync(lastRunPath)) {
    try {
      const lastRun = JSON.parse(fs.readFileSync(lastRunPath, 'utf8'));
      hasFailures = lastRun.status === 'failed' || lastRun.failures > 0;
    } catch (error) {
      console.error('无法读取上次测试运行状态:', error);
    }
  }

  console.log('🧹 测试环境清理完成');

  if (hasFailures) {
    console.log('❌ 测试过程中发现失败');
    console.log(`📊 详细的HTML报告可在以下位置查看: ${htmlReportDir}`);
    console.log('💡 使用 "npm run show-report" 命令查看详细报告');
  } else {
    console.log('✅ 所有测试通过');
  }
}

export default globalTeardown;