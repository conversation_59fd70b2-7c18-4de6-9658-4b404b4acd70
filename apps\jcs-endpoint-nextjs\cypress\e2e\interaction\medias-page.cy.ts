import mediaFixture from "../../fixtures/ProductMedia.json";
import planFixture from "../../fixtures/PlanProduct.json";

describe("画面操作のテスト", () => {
  describe("製品媒体一覧画面", () => {
    const validCredentials = {
      userId: "hitachi.taro.aa",
      password: "changeit!@#",
    };
    const filter1 = "elect";
    const filter2 = "b25";
    const filter3 = "11-1";
    const filter4 = "mac";
    const filter5 = "12/30";
    const filter6 = "vsd";
    const filter7 = "37 k";
    const filter8 = "vsd";
    const filter9 = "1.99 G";
    const filter0 = "製品媒体一覧画面";
    const filterPage2 = "small";
    const filterRefresh = "kb";
    // @ts-ignore
    const formatBytes = (bytes) => {
      const k = 1024;
      const sizes = ["KB", "MB", "GB"];
      // @ts-ignore
      const removeTrailingZeros = (value) => {
        return value.replace(/\.?0+$/, "");
      };

      if (bytes < k * k) {
        return removeTrailingZeros((bytes / k).toFixed(2)) + " " + sizes[0];
      } else if (bytes < k * k * k) {
        return (
          removeTrailingZeros((bytes / (k * k)).toFixed(2)) + " " + sizes[1]
        );
      } else {
        return (
          removeTrailingZeros((bytes / (k * k * k)).toFixed(2)) + " " + sizes[2]
        );
      }
    };
    const medias = mediaFixture.ProductMedia.map((m) => ({
      ...m,
      mediaSize: formatBytes(m.mediaSize),
      documentSize: formatBytes(m.documentSize),
    }));
    // @ts-ignore
    const doFilter = (keyword) => {
      const uniqueProducts = planFixture.PlanProduct.filter((product) =>
        ["standard", "lighta"].includes(product.planId),
      ).map((product) => product.productCode);

      return medias.filter(
        (media) =>
          uniqueProducts.includes(media.productCode) &&
          (media.name.toLowerCase().includes(keyword.toLowerCase()) ||
            media.productCode.toLowerCase().includes(keyword.toLowerCase()) ||
            media.version.toLowerCase().includes(keyword.toLowerCase()) ||
            media.os.toLowerCase().includes(keyword.toLowerCase()) ||
            media.releasedAt.toLowerCase().includes(keyword.toLowerCase()) ||
            media.mediaName.toLowerCase().includes(keyword.toLowerCase()) ||
            media.documentName.toLowerCase().includes(keyword.toLowerCase()) ||
            media.mediaSize.toLowerCase().includes(keyword.toLowerCase()) ||
            media.documentSize.toLowerCase().includes(keyword.toLowerCase())),
      );
    };

    beforeEach(() => {
      cy.visit("/login");
      cy.get("#userId").type(validCredentials.userId);
      cy.get("#password").type(validCredentials.password);
      cy.get("button").click();
      cy.get("aside #file li:nth-child(2) a").click();
      cy.title().should("eq", "製品媒体一覧");
    });

    describe("フィルタリング", () => {
      it("製品名で検索する", () => {
        cy.get(".bg-white.h-full input").type(filter1);
        cy.get(".bg-white.h-full button").first().click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter1).length / 10)}`);
      });

      it("製品形名で検索する", () => {
        cy.get(".bg-white.h-full input").type(filter2);
        cy.get(".bg-white.h-full button").first().click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter2).length / 10)}`);
      });

      it("バージョンで検索する", () => {
        cy.get(".bg-white.h-full input").type(filter3);
        cy.get(".bg-white.h-full button").first().click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter3).length / 10)}`);
      });

      it("OSで検索する", () => {
        cy.get(".bg-white.h-full input").type(filter4);
        cy.get(".bg-white.h-full button").first().click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter4).length / 10)}`);
      });

      it("リリース日で検索する", () => {
        cy.get(".bg-white.h-full input").type(filter5);
        cy.get(".bg-white.h-full button").first().click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter5).length / 10)}`);
      });

      it("製品媒体で検索する", () => {
        cy.get(".bg-white.h-full input").type(filter6);
        cy.get(".bg-white.h-full button").first().click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter6).length / 10)}`);
      });

      it("製品媒体のサイズで検索する", () => {
        cy.get(".bg-white.h-full input").type(filter7);
        cy.get(".bg-white.h-full button").first().click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter7).length / 10)}`);
      });

      it("ドキュメントで検索する", () => {
        cy.get(".bg-white.h-full input").type(filter8);
        cy.get(".bg-white.h-full button").first().click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter8).length / 10)}`);
      });

      it("ドキュメントのサイズで検索する", () => {
        cy.get(".bg-white.h-full input").type(filter9);
        cy.get(".bg-white.h-full button").first().click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter9).length / 10)}`);
      });

      it("結果が0件", () => {
        cy.get(".bg-white.h-full input").type(filter0);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9").should(
          "have.length",
          0,
        );
      });

      it("結果が2ページ", () => {
        cy.get(".bg-white.h-full input").type(filterPage2);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", "2");
      });

      it("クリアアイコンをクリックする", () => {
        cy.get(".bg-white.h-full input").type(filter1);
        cy.get(".bg-white.h-full button").first().click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter1).length / 10)}`);
        cy.get(".bg-white.h-full button").last().click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter("").length / 10)}`);
      });
    });

    describe("ページネーション", () => {
      it("2ページボタンをクリックする", () => {
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "1");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "2");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .next()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "3");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .next()
          .next()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
        const maxPage = Math.ceil(doFilter("").length / 10);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage - 1}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .prev()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
      });

      it("3ページボタンをクリックする", () => {
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(3)
          .click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "1");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "2");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .next()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "3");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .next()
          .next()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
        const maxPage = Math.ceil(doFilter("").length / 10);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage - 1}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .prev()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
      });

      it("最大ページボタンをクリックする", () => {
        const maxPage = Math.ceil(doFilter("").length / 10);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(maxPage)
          .click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "1");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "2");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .next()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", `${maxPage}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage - 1}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .prev()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage - 2}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .prev()
          .prev()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
      });

      it("最大ページボタンの前のボタンをクリックする", () => {
        const maxPage = Math.ceil(doFilter("").length / 10);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(maxPage - 1)
          .click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "1");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "2");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .next()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", `${maxPage - 1}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .prev()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage - 2}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .prev()
          .prev()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
      });

      it("次へアイコンをクリックする", () => {
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .then((text) => {
            const maxPage = Number(text);

            for (let index = 1; index < maxPage; index++) {
              cy.get("#page-right").click();
              cy.wait(1000);

              if (index !== maxPage - 3) {
                cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
                  .contains(index)
                  .should("have.prop", "tagName", "A");
                cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
                  .contains(index + 1)
                  .should("have.prop", "tagName", "DIV");
              }
            }
          });
      });

      it("前へアイコンをクリックする", () => {
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .then((text) => {
            const maxPage = Number(text);

            for (let index = 1; index < maxPage; index++) {
              cy.get("#page-right").click();
              cy.wait(1000);

              if (index !== maxPage - 3) {
                cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
                  .contains(index)
                  .should("have.prop", "tagName", "A");
                cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
                  .contains(index + 1)
                  .should("have.prop", "tagName", "DIV");
              }
            }

            for (let index = 1; index < maxPage; index++) {
              cy.get("#page-left").click();
              cy.wait(1000);

              if (index !== maxPage - 3) {
                cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
                  .contains(maxPage - index)
                  .should("have.prop", "tagName", "DIV");
                cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
                  .contains(maxPage - index + 1)
                  .should("have.prop", "tagName", "A");
              }
            }
          });
      });

      it("行数/ページが30に選択する", () => {
        cy.get(".bg-white.h-full select").select("30");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter("").length / 30)}`);
        cy.get(".rounded-b-lg").within(() => {
          cy.get("table").then(($child) => {
            const parentClientHeight = $child.parent().height() || 0;
            const childScrollHeight = $child[0].scrollHeight;

            expect(childScrollHeight).to.be.greaterThan(parentClientHeight);
          });
        });
        cy.get("table tbody tr td:nth-child(5)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
        cy.get(".bg-white.h-full select").select("10");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter("").length / 10)}`);
        cy.get("table tbody tr td:nth-child(5)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("行数/ページが50に選択する", () => {
        cy.get(".bg-white.h-full select").select("50");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter("").length / 50)}`);
        cy.get(".rounded-b-lg").within(() => {
          cy.get("table").then(($child) => {
            const parentClientHeight = $child.parent().height() || 0;
            const childScrollHeight = $child[0].scrollHeight;

            expect(childScrollHeight).to.be.greaterThan(parentClientHeight);
          });
        });
        cy.get("table tbody tr td:nth-child(5)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
        cy.get(".bg-white.h-full select").select("10");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter("").length / 10)}`);
        cy.get("table tbody tr td:nth-child(5)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });
    });

    describe("ソート", () => {
      it("テーブルヘーダに、リリース日をクリックする", () => {
        cy.get("thead th span").contains("リリース日").click();
        cy.get("thead th span")
          .should("contain", "リリース日")
          .next("img")
          .should("have.class", "rotate-180");
        cy.get("thead th span")
          .contains("製品名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品形名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("バージョン")
          .next("img")
          .should("not.exist");
        cy.get("thead th span").contains("OS").next("img").should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体のサイズ")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメント")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメントのサイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr").then(($rows) => {
          const columnData = $rows
            .map((_, row) => {
              const releaseDate =
                // @ts-ignore
                row.querySelector("td:nth-child(5)").innerText;
              // @ts-ignore
              const name = row.querySelector("th").innerText;
              return { releaseDate, name };
            })
            .get();

          const sortedColumnData = [...columnData].sort((a, b) => {
            const releaseDateComparison = a.releaseDate.localeCompare(
              b.releaseDate,
            );
            if (releaseDateComparison !== 0) {
              return releaseDateComparison;
            }
            return a.name.localeCompare(b.name);
          });

          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、製品名をクリックする", () => {
        cy.get("thead th span").contains("製品名").click();
        cy.get("thead th span")
          .should("contain", "製品名")
          .next("img")
          .should("have.class", "rotate-180");
        cy.get("thead th span")
          .contains("リリース日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品形名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("バージョン")
          .next("img")
          .should("not.exist");
        cy.get("thead th span").contains("OS").next("img").should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体のサイズ")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメント")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメントのサイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr th").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            a.localeCompare(b),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、製品名を2回クリックする", () => {
        cy.get("thead th span").contains("製品名").click();
        cy.wait(2000);
        cy.get("thead th span").contains("製品名").click();
        cy.get("thead th span")
          .should("contain", "製品名")
          .next("img")
          .should("not.have.class", "rotate-180");
        cy.get("thead th span")
          .contains("リリース日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品形名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("バージョン")
          .next("img")
          .should("not.exist");
        cy.get("thead th span").contains("OS").next("img").should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体のサイズ")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメント")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメントのサイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr th").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、製品形名をクリックする", () => {
        cy.get("thead th span").contains("製品形名").click();
        cy.get("thead th span")
          .should("contain", "製品形名")
          .next("img")
          .should("have.class", "rotate-180");
        cy.get("thead th span")
          .contains("リリース日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("バージョン")
          .next("img")
          .should("not.exist");
        cy.get("thead th span").contains("OS").next("img").should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体のサイズ")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメント")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメントのサイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(2)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            a.localeCompare(b),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、製品形名を2回クリックする", () => {
        cy.get("thead th span").contains("製品形名").click();
        cy.wait(2000);
        cy.get("thead th span").contains("製品形名").click();
        cy.get("thead th span")
          .should("contain", "製品形名")
          .next("img")
          .should("not.have.class", "rotate-180");
        cy.get("thead th span")
          .contains("リリース日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("バージョン")
          .next("img")
          .should("not.exist");
        cy.get("thead th span").contains("OS").next("img").should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体のサイズ")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメント")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメントのサイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(2)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、バージョンをクリックする", () => {
        cy.get("thead th span").contains("バージョン").click();
        cy.get("thead th span")
          .should("contain", "バージョン")
          .next("img")
          .should("have.class", "rotate-180");
        cy.get("thead th span")
          .contains("リリース日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品形名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span").contains("OS").next("img").should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体のサイズ")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメント")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメントのサイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        // cy.get("table tbody tr td:nth-child(3)").then(($column) => {
        //   const columnData = $column.map((_, el) => el.innerText).get();

        //   const sortedColumnData = [...columnData].sort((a, b) =>
        //     a.localeCompare(b),
        //   );
        //   expect(columnData).to.deep.equal(sortedColumnData);
        // });
      });

      it("テーブルヘーダに、バージョンを2回クリックする", () => {
        cy.get("thead th span").contains("バージョン").click();
        cy.wait(2000);
        cy.get("thead th span").contains("バージョン").click();
        cy.get("thead th span")
          .should("contain", "バージョン")
          .next("img")
          .should("not.have.class", "rotate-180");
        cy.get("thead th span")
          .contains("リリース日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品形名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span").contains("OS").next("img").should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体のサイズ")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメント")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメントのサイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(3)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、OSをクリックする", () => {
        cy.get("thead th span").contains("OS").click();
        cy.get("thead th span")
          .should("contain", "OS")
          .next("img")
          .should("have.class", "rotate-180");
        cy.get("thead th span")
          .contains("リリース日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品形名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("バージョン")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体のサイズ")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメント")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメントのサイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(4)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            a.localeCompare(b),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、OSを2回クリックする", () => {
        cy.get("thead th span").contains("OS").click();
        cy.wait(2000);
        cy.get("thead th span").contains("OS").click();
        cy.get("thead th span")
          .should("contain", "OS")
          .next("img")
          .should("not.have.class", "rotate-180");
        cy.get("thead th span")
          .contains("リリース日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品形名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("バージョン")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体のサイズ")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメント")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメントのサイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(4)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、製品媒体をクリックする", () => {
        cy.get("thead th span").contains("製品媒体").click();
        cy.get("thead th span")
          .should("contain", "製品媒体")
          .next("img")
          .should("have.class", "rotate-180");
        cy.get("thead th span")
          .contains("リリース日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品形名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("バージョン")
          .next("img")
          .should("not.exist");
        cy.get("thead th span").contains("OS").next("img").should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体のサイズ")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメント")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメントのサイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        // cy.get("table tbody tr td:nth-child(6)").then(($column) => {
        //   const columnData = $column.map((_, el) => el.innerText).get();

        //   const sortedColumnData = [...columnData].sort((a, b) =>
        //     a.localeCompare(b),
        //   );
        //   expect(columnData).to.deep.equal(sortedColumnData);
        // });
      });

      it("テーブルヘーダに、製品媒体を2回クリックする", () => {
        cy.get("thead th span").contains("製品媒体").click();
        cy.wait(2000);
        cy.get("thead th span").contains("製品媒体").click();
        cy.get("thead th span")
          .should("contain", "製品媒体")
          .next("img")
          .should("not.have.class", "rotate-180");
        cy.get("thead th span")
          .contains("リリース日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品形名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("バージョン")
          .next("img")
          .should("not.exist");
        cy.get("thead th span").contains("OS").next("img").should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体のサイズ")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメント")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメントのサイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(6)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、ドキュメントをクリックする", () => {
        cy.get("thead th span").contains("ドキュメント").click();
        cy.get("thead th span")
          .should("contain", "ドキュメント")
          .next("img")
          .should("have.class", "rotate-180");
        cy.get("thead th span")
          .contains("リリース日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品形名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("バージョン")
          .next("img")
          .should("not.exist");
        cy.get("thead th span").contains("OS").next("img").should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体のサイズ")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメントのサイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(8)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            a.localeCompare(b),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、ドキュメントを2回クリックする", () => {
        cy.get("thead th span").contains("ドキュメント").click();
        cy.wait(2000);
        cy.get("thead th span").contains("ドキュメント").click();
        cy.get("thead th span")
          .should("contain", "ドキュメント")
          .next("img")
          .should("not.have.class", "rotate-180");
        cy.get("thead th span")
          .contains("リリース日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品形名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("バージョン")
          .next("img")
          .should("not.exist");
        cy.get("thead th span").contains("OS").next("img").should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体のサイズ")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメントのサイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(8)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、製品媒体のサイズをクリックする", () => {
        cy.get("thead th span").contains("製品媒体のサイズ").click();
        cy.get("thead th span")
          .should("contain", "製品媒体のサイズ")
          .next("img")
          .should("have.class", "rotate-180");
        cy.get("thead th span")
          .contains("リリース日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品形名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("バージョン")
          .next("img")
          .should("not.exist");
        cy.get("thead th span").contains("OS").next("img").should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメント")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメントのサイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(7)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            a.localeCompare(b),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、製品媒体のサイズを2回クリックする", () => {
        cy.get("thead th span").contains("製品媒体のサイズ").click();
        cy.wait(1000);
        cy.get("thead th span").contains("製品媒体のサイズ").click();
        cy.get("thead th span")
          .should("contain", "製品媒体のサイズ")
          .next("img")
          .should("not.have.class", "rotate-180");
        cy.get("thead th span")
          .contains("リリース日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品形名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("バージョン")
          .next("img")
          .should("not.exist");
        cy.get("thead th span").contains("OS").next("img").should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメント")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメントのサイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(7)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、ドキュメントのサイズをクリックする", () => {
        cy.get("thead th span").contains("ドキュメントのサイズ").click();
        cy.get("thead th span")
          .should("contain", "ドキュメントのサイズ")
          .next("img")
          .should("have.class", "rotate-180");
        cy.get("thead th span")
          .contains("リリース日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品形名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("バージョン")
          .next("img")
          .should("not.exist");
        cy.get("thead th span").contains("OS").next("img").should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメント")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体のサイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(9)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            a.localeCompare(b),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、ドキュメントのサイズを2回クリックする", () => {
        cy.get("thead th span").contains("ドキュメントのサイズ").click();
        cy.wait(1000);
        cy.get("thead th span").contains("ドキュメントのサイズ").click();
        cy.get("thead th span")
          .should("contain", "ドキュメントのサイズ")
          .next("img")
          .should("not.have.class", "rotate-180");
        cy.get("thead th span")
          .contains("リリース日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品形名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("バージョン")
          .next("img")
          .should("not.exist");
        cy.get("thead th span").contains("OS").next("img").should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("ドキュメント")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品媒体のサイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(9)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });
    });

    describe("更新", () => {
      it("更新アイコンをクリックする", () => {
        cy.get(".bg-white.h-full input").type(filterRefresh);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full select").select("30");
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filterRefresh).length / 30)}`);
        cy.get("nav img").click();
        cy.wait(1000);
        cy.get(".bg-white.h-full input").should("have.value", filterRefresh);
        cy.get(".bg-white.h-full select option:selected").then(
          (selectedOption) => {
            cy.wrap(selectedOption).invoke("text").should("include", "10");
            cy.wrap(selectedOption).invoke("val").should("eq", "10");
          },
        );
      });
    });

    describe("ブラウザ", () => {
      it("更新ボタンをクリックする", () => {
        cy.get(".bg-white.h-full input").type(filterRefresh);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full select").select("30");
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filterRefresh).length / 30)}`);
        cy.reload();
        cy.wait(1000);
        cy.get(".bg-white.h-full input").should("have.value", filterRefresh);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filterRefresh).length / 30)}`);
        cy.get(".bg-white.h-full select option:selected").then(
          (selectedOption) => {
            cy.wrap(selectedOption).invoke("text").should("include", "30");
            cy.wrap(selectedOption).invoke("val").should("eq", "30");
          },
        );
      });

      it("戻るボタンをクリックし、進むボタンをクリックする", () => {
        cy.get(".bg-white.h-full input").type(filterRefresh);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full select").select("30");
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filterRefresh).length / 30)}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .should("have.prop", "tagName", "DIV");
        cy.go("back");
        cy.wait(1000);
        cy.get(".bg-white.h-full input").should("have.value", filterRefresh);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filterRefresh).length / 30)}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(1)
          .should("have.prop", "tagName", "DIV");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .should("have.prop", "tagName", "A");
        cy.get(".bg-white.h-full select option:selected").then(
          (selectedOption) => {
            cy.wrap(selectedOption).invoke("text").should("include", "30");
            cy.wrap(selectedOption).invoke("val").should("eq", "30");
          },
        );
        cy.go("forward");
        cy.wait(1000);
        cy.get(".bg-white.h-full input").should("have.value", filterRefresh);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filterRefresh).length / 30)}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .should("have.prop", "tagName", "DIV");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(1)
          .should("have.prop", "tagName", "A");
        cy.get(".bg-white.h-full select option:selected").then(
          (selectedOption) => {
            cy.wrap(selectedOption).invoke("text").should("include", "30");
            cy.wrap(selectedOption).invoke("val").should("eq", "30");
          },
        );
      });
    });
  });
});
