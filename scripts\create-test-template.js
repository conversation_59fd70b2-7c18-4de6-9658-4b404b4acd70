#!/usr/bin/env node

/**
 * テストケースExcelテンプレート作成スクリプト
 * 
 * 単元テスト報告書のExcelテンプレートを作成する
 * 
 * 使用方法:
 *   node scripts/create-test-template.js
 */

const fs = require('fs');
const path = require('path');
const ExcelJS = require('exceljs');

/**
 * テストケーステンプレートを作成
 */
async function createTestTemplate() {
  const workbook = new ExcelJS.Workbook();
  
  // メタデータ設定
  workbook.creator = 'JCS Endpoint Test Template Generator';
  workbook.lastModifiedBy = 'JCS Endpoint Test Template Generator';
  workbook.created = new Date();
  workbook.modified = new Date();

  // メインシートを作成
  const worksheet = workbook.addWorksheet('単元テスト報告書');
  
  // ページ設定
  worksheet.pageSetup = {
    paperSize: 9, // A4
    orientation: 'landscape',
    fitToPage: true,
    fitToWidth: 1,
    fitToHeight: 0
  };

  // ヘッダー部分
  worksheet.mergeCells('A1:G1');
  const titleCell = worksheet.getCell('A1');
  titleCell.value = 'JCS Endpoint システム 単元テスト報告書';
  titleCell.font = { size: 16, bold: true, name: 'メイリオ' };
  titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
  titleCell.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFE6F3FF' }
  };

  // 基本情報セクション
  worksheet.getCell('A3').value = '作成日:';
  worksheet.getCell('B3').value = new Date().toLocaleDateString('ja-JP');
  worksheet.getCell('A3').font = { bold: true, name: 'メイリオ' };
  
  worksheet.getCell('A4').value = '作成者:';
  worksheet.getCell('B4').value = '';
  worksheet.getCell('A4').font = { bold: true, name: 'メイリオ' };

  worksheet.getCell('A5').value = 'プロジェクト:';
  worksheet.getCell('B5').value = 'JCS Endpoint システム';
  worksheet.getCell('A5').font = { bold: true, name: 'メイリオ' };

  // テストケーステーブルヘッダー
  const headers = [
    { key: 'no', header: 'No.', width: 5 },
    { key: 'testFile', header: 'テストファイル', width: 20 },
    { key: 'testCase', header: 'テストケース名', width: 35 },
    { key: 'testPoint', header: '試験観点', width: 25 },
    { key: 'testTarget', header: '試験対象', width: 25 },
    { key: 'testProcedure', header: '試験手順', width: 40 },
    { key: 'verification', header: '確認項目', width: 25 }
  ];

  // ヘッダー行の設定
  const headerRow = 7;
  headers.forEach((header, index) => {
    const cell = worksheet.getCell(headerRow, index + 1);
    cell.value = header.header;
    cell.font = { bold: true, name: 'メイリオ', size: 11 };
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFD9E2F3' }
    };
    cell.alignment = { 
      horizontal: 'center', 
      vertical: 'middle',
      wrapText: true 
    };
    cell.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    };
    
    // 列幅設定
    worksheet.getColumn(index + 1).width = header.width;
  });

  // サンプルデータ行（テンプレート用）
  const sampleData = [
    {
      no: 1,
      testFile: 'example.test.ts',
      testCase: '正常系：有効なデータでの処理が成功する',
      testPoint: '正常なデータ処理',
      testTarget: '対象機能の基本処理',
      testProcedure: '1. 有効なデータを準備\n2. 処理を実行\n3. 結果を確認',
      verification: '処理が正常に完了すること'
    },
    {
      no: 2,
      testFile: 'example.test.ts',
      testCase: '異常系：無効なデータでエラーが発生する',
      testPoint: '異常データのエラー処理',
      testTarget: '対象機能のエラーハンドリング',
      testProcedure: '1. 無効なデータを準備\n2. 処理を実行\n3. エラーを確認',
      verification: '適切なエラーが発生すること'
    }
  ];

  // サンプルデータの追加
  sampleData.forEach((data, index) => {
    const rowNum = headerRow + 1 + index;
    Object.values(data).forEach((value, colIndex) => {
      const cell = worksheet.getCell(rowNum, colIndex + 1);
      cell.value = value;
      cell.font = { name: 'メイリオ', size: 10 };
      cell.alignment = { 
        vertical: 'top',
        wrapText: true 
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });
    
    // 行の高さを調整
    worksheet.getRow(rowNum).height = 60;
  });

  // 空行を追加（データ入力用）
  for (let i = 0; i < 50; i++) {
    const rowNum = headerRow + 1 + sampleData.length + i;
    headers.forEach((header, colIndex) => {
      const cell = worksheet.getCell(rowNum, colIndex + 1);
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      cell.alignment = { 
        vertical: 'top',
        wrapText: true 
      };
    });
    worksheet.getRow(rowNum).height = 30;
  }

  // フッター情報
  const footerRow = headerRow + 1 + sampleData.length + 50 + 2;
  worksheet.getCell(`A${footerRow}`).value = '備考:';
  worksheet.getCell(`A${footerRow}`).font = { bold: true, name: 'メイリオ' };
  worksheet.getCell(`A${footerRow + 1}`).value = '・このテンプレートは自動生成されたものです';
  worksheet.getCell(`A${footerRow + 2}`).value = '・テストケースの詳細は各項目に記入してください';
  worksheet.getCell(`A${footerRow + 3}`).value = '・試験手順と確認項目は具体的に記述してください';

  return workbook;
}

/**
 * 統計シートを作成
 */
async function createStatsSheet(workbook) {
  const worksheet = workbook.addWorksheet('テスト統計');
  
  // ヘッダー
  worksheet.mergeCells('A1:D1');
  const titleCell = worksheet.getCell('A1');
  titleCell.value = 'テスト統計情報';
  titleCell.font = { size: 14, bold: true, name: 'メイリオ' };
  titleCell.alignment = { horizontal: 'center' };
  titleCell.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFE6F3FF' }
  };

  // 統計テーブル
  const statsHeaders = ['項目', '件数', '割合', '備考'];
  statsHeaders.forEach((header, index) => {
    const cell = worksheet.getCell(3, index + 1);
    cell.value = header;
    cell.font = { bold: true, name: 'メイリオ' };
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFD9E2F3' }
    };
    cell.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    };
  });

  // 統計データのサンプル
  const statsData = [
    ['総テストケース数', '=COUNTA(単元テスト報告書.C8:C1000)', '', ''],
    ['正常系テスト', '', '', ''],
    ['異常系テスト', '', '', ''],
    ['境界値テスト', '', '', ''],
    ['完了済み', '', '', ''],
    ['未実施', '', '', '']
  ];

  statsData.forEach((data, index) => {
    const rowNum = 4 + index;
    data.forEach((value, colIndex) => {
      const cell = worksheet.getCell(rowNum, colIndex + 1);
      cell.value = value;
      cell.font = { name: 'メイリオ' };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });
  });

  // 列幅調整
  worksheet.getColumn(1).width = 20;
  worksheet.getColumn(2).width = 15;
  worksheet.getColumn(3).width = 15;
  worksheet.getColumn(4).width = 30;
}

/**
 * メイン処理
 */
async function main() {
  console.log('📝 テストケースExcelテンプレートを作成中...');

  try {
    // ワークブック作成
    const workbook = await createTestTemplate();
    
    // 統計シート追加
    await createStatsSheet(workbook);

    // 出力ディレクトリを確保
    const outputDir = 'docs-delivery/unit-test-report';
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // ファイルに保存
    const outputPath = path.join(outputDir, 'JCS_単元テスト報告書_テンプレート.xlsx');
    await workbook.xlsx.writeFile(outputPath);

    console.log(`✅ テンプレートが作成されました: ${outputPath}`);
    console.log('📋 含まれるシート:');
    console.log('   - 単元テスト報告書 (メインテンプレート)');
    console.log('   - テスト統計 (統計情報用)');
    console.log('');
    console.log('💡 使用方法:');
    console.log('   1. 作成されたテンプレートファイルを開く');
    console.log('   2. サンプルデータを参考に実際のテストケースを入力');
    console.log('   3. 統計シートで全体の進捗を確認');

  } catch (error) {
    console.error('❌ エラーが発生しました:', error);
    process.exit(1);
  }
}

// スクリプト実行
if (require.main === module) {
  main();
}

module.exports = {
  createTestTemplate,
  createStatsSheet
};