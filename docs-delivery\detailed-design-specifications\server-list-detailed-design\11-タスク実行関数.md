## タスク実行関数 (TaskExecuteFunc) 詳細設計

### 概要

#### 責務
TaskExecuteFuncは、Azure Service BusのTaskInputQueueから新しいバックグラウンドタスク実行要求メッセージを受信し処理するAzure Functionsの関数である。本関数の主な責務は以下の通りである。

1.  受信したタスク実行要求メッセージを解析し、入力パラメータ（taskId）が正しいかどうか確認する。
2.  対象タスクの情報をデータベースのタスクTaskテーブルから取得し、タスクのステータスがQUEUEDであるか判定する。
3.  対象VMコンテナのタスク実行状態（ステータス）をコンテナ実行状態ContainerConcurrencyStatusテーブルから取得し、IDLEであるか判定する。
4.  コンテナ実行状態ContainerConcurrencyStatusテーブルから対象VMコンテナのステータスをBUSYに更新する。
5.  Azure Files共有ストレージ上に、各タスク（TaskID）に対応するタスク固有の一時作業ディレクトリ TaskWorkspaces/<TaskID>/ を作成し、その配下にインポート用サブディレクトリ imports/ およびエクスポート用サブディレクトリ exports/ を作成する。
6.  管理項目定義のインポートタスクの場合、Azure Blob Storageの{licenseId}/imports/{taskId}/assetsfield_def.csvパスにあるファイルを、Azure Filesワークスペースの TaskWorkspaces/<TaskID>/imports/ ディレクトリへコピーする。
7.  タスクテーブルから対象タスクのステータスをRUNBOOK_SUBMITTEDに更新する。
8.  Azure Automation APIを呼び出し、対象HRWグループに対してタスク種別に対応したRunbook（基盤スクリプト）ジョブを作成する。パラメータとしてタスクID、Runbook名、Hybrid Runbook Worker Group名、実行コンテナ名等を渡す。
9.  上記1-8の処理中にエラーが発生した時は適切に処理する：ログに記録、コンテナのステータスをIDLEに戻す、一時作業ディレクトリを削除、タスクTaskテーブルから対象タスクのステータスをCOMPLETED_ERRORに更新するなど。
10. 上記1-8の処理が成功した場合、現在のタスクの対象サーバ（Task.targetServerId）に対して、タスク記録の保有件数を上限（値の一覧LovテーブルのTASK_CONFIG.MAX_RETENTION_COUNTで定義される）内に維持するためのクリーンアップ処理を行う。このクリーンアップ処理は、当該サーバの完了済み（ステータスがCOMPLETED_SUCCESS, COMPLETED_ERROR, CANCELLEDのいずれか）タスクの中からレコードの作成日時の古い順番で選択し、保有件数上限内に収まるまで関連する操作ログOperationLogテーブルのレコード、Azure Blob Storage上の関連ファイル、Taskテーブルのレコードの順番で削除する（レコードとファイルはタスクIDで結びつけられている）。このクリーンアップ処理は、例外が発生した場合でも、catchして適切にログ記録するだけで特に対処せず、1-8の処理結果に影響を与えない形で行う。

#### トリガー
Azure Service Bus - TaskInputQueue キューメッセージ。

#### 主要入力
*   TaskInputQueue から受信するJSON形式のメッセージ。メッセージボディの主な構造は以下の通り。

| パラメータ名 | データ型 | 必須 | 説明 |
|:---:|:---|:---:|:---|
| taskId | 文字列 | ○ | タスクの一意識別子 |
*   Azure SQL Database のTaskテーブル、ContainerConcurrencyStatusテーブルからの読み取り。
*   管理項目定義インポートタスクの場合、Azure Blob Storage上の一時CSVファイルの読み取り。

#### 主要出力
*   Azure Automation Runbookジョブの作成。
*   Azure SQL Database のTaskテーブル、ContainerConcurrencyStatusテーブルのレコード更新。古いタスク記録の削除が発生した場合はTaskテーブル、OperationLogテーブルからのレコード削除。
*   Azure Files共有ストレージ上にタスク固有の一時作業ディレクトリ (TaskWorkspaces/<TaskID>/imports/ および TaskWorkspaces/<TaskID>/exports/) の作成。管理項目定義インポートタスクの場合、imports/ ディレクトリへの固定ファイル名 assetsfield_def.csv でのファイル配置。
*   古いタスク記録の削除が発生した場合、Azure Blob Storageからの関連ファイル削除。

※TaskInputQueueの名称は、環境変数 SERVICE_BUS_TASK_INPUT_QUEUE_NAME によって定義される。
※Azure Filesへのアクセスには環境変数 AZURE_STORAGE_CONNECTION_STRING で指定される接続文字列が使用される。
※データベース接続には環境変数 MSSQL_PRISMA_URL で指定される接続文字列が使用される。
※Azure Blob Storageへのアクセスには環境変数 AZURE_STORAGE_ACCOUNT_NAME が、対象コンテナの指定には環境変数 AZURE_STORAGE_CONTAINER_OPLOGS (操作ログ用)とAZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF (管理項目定義用) が使用される。
※Azure Automationアカウントの名称は環境変数 AZURE_AUTOMATION_ACCOUNT_NAME により指定される。
※サブスクリプションIDは環境変数 SUBSCRIPTION_ID により指定される。
※リソースグループ名は環境変数 RESOURCE_GROUP_NAME により指定される。
※実行するRunbook（基盤スクリプト）名は環境変数 RUNBOOK_MGMT_ITEM_IMPORT、RUNBOOK_MGMT_ITEM_EXPORT、RUNBOOK_OPLOG_EXPORT により指定される。

### 主要処理フロー

```mermaid
graph TD
    A["開始: TaskInputQueueメッセージ受信"] --> B["メッセージ解析・<br/>基本パラメータ（taskId）確認"];
    B -- "不足/不正" --> AA["ログ記録"];
    B -- "正常" --> D["タスク情報取得<br/>(タスクテーブルよりtaskId使用)"];
    D -- "取得失敗" --> AA;
    D -- "構成不備" --> BA["Taskをエラーに更新<br/>(EMET0009)"];
    D -- "取得成功" --> C{"Taskステータス確認<br/>(QUEUEDであるか)"};
    C -- "QUEUEDでない" --> AA;
    C -- "QUEUED" --> H["コンテナ情報取得<br/>(コンテナ実行状態テーブルよりVM名、コンテナ名使用)"];
    H -- "構成不備" --> BA;
    H -- "取得失敗" --> HA["Taskをエラーに更新<br/>(EMET0007)"];
    H -- "取得成功" --> F{"コンテナステータス確認<br/>(IDLEであるか)"};
    F -- "BUSY" --> FB["Taskをエラーに更新<br/>(EMET0001)"];
    F -- "IDLE" --> J["コンテナ実行状態テーブル更新(status: BUSY, currentTaskId: 入力のtaskId)"];
    J -- "更新失敗" --> GA["Taskをエラーに更新<br/>(EMET0007)"];
    J -- "更新0件" --> JA["Taskをエラーに更新<br/>(EMET0001)"];
    J -- "更新成功" --> K_CreateWorkspace["Azure Files作業ディレクトリ作成<br/>(TaskWorkspaces/{taskId}/imports, exports)"];
    K_CreateWorkspace -- "作成失敗"--> K_ErrorWorkspace["コンテナをIDLEに更新<br/>Taskをエラーに更新<br/>(EMET0002)"];
    K_CreateWorkspace -- "作成成功" --> K_TaskType{"タスク種別判定"};
        K_TaskType -- "管理項目定義インポート" --> L["一時CSVファイルをBlobからWorkspacesのimports/へassetsfield_def.csvとしてコピー"];
            L -- "コピー失敗" --> LA["コンテナをIDLEに更新<br/>作業ディレクトリ削除試行<br/>Taskをエラーに更新<br/>(EMET0002/0003)"];
            L -- "コピー成功" --> P;
        K_TaskType -- "その他タスク" --> P["タスクテーブル更新<br/>(status: RUNBOOK_SUBMITTED,<br/>startedAt: Now(UTC))"];
    P -- "更新失敗" --> PA["コンテナをIDLEに更新<br/>作業ディレクトリ削除試行<br/>Taskをエラーに更新<br/>(EMET0007)"];
    P -- "更新0件" --> PB["コンテナをIDLEに更新<br/>作業ディレクトリ削除試行<br/>ログ記録"];
    P -- "更新成功" --> N["REST APIリクエスト構築"];
    
    N --> O["Azure Automation API呼出<br/>Runbookジョブ作成"];
    O -- "API呼出失敗" --> OA["コンテナをIDLEに更新<br/>作業ディレクトリ削除試行<br/>Taskをエラーに更新<br/>(EMET0013)"];
    O -- "API呼出成功" --> Q["処理成功のログを記録する"];
    Q --> S_Cleanup["タスクレコード保持件数<br/>超過時クリーンアップ処理"];
    S_Cleanup --> R["終了"];

    AA --> R;
    BA --> R;
    FB --> R;
    GA --> R;
    JA --> R;
    HA --> R;
    K_ErrorWorkspace --> R;
    LA --> R;
    OA --> R;
    PA --> R;
    PB --> R;
```

#### 共通処理フロー詳細:

1.  Azure Service Bus の TaskInputQueue からタスクメッセージを受信し、JSON形式から解析する。受信したメッセージ内容（特にtaskId）をログに出力する。
2.  メッセージ内の基本パラメータ（taskId）の存在と値が空であるかを確認する。
    *   情報が不足/不正の場合：エラー詳細をログに出力して、処理を終了する。
3.  taskId を使用して タスクTaskテーブルを検索し、タスクの情報及びタスクレコードの最終更新日時を取得する。
    *   取得失敗の場合：エラー詳細をログに出力して、処理を終了する。
    *   取得した情報が不足/不正の場合：
        ・Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0009に、タスク詳細（resultMessage）を「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」に更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。
        ・エラー詳細をログに出力して、処理を終了する。
4.  タスクのステータスがQUEUEDであるかチェックする。QUEUEDなら処理を続行する。
    *   QUEUEDでない場合：処理対象外のため、エラー詳細をログに出力して、処理を終了する。
5.  対象VM名（Task.targetVmName）と対象コンテナ名（Task.targetContainerName）を複合キーとして、コンテナ実行状態ContainerConcurrencyStatus テーブルを検索し、対象コンテナの現在のステータスと最終更新日時を取得する。
    *   取得失敗の場合：
        ・Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0007に、タスク詳細（resultMessage）を「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0007)」に更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。
        ・エラー詳細をログに出力して、処理を終了する。
    *   取得した情報が不足/不正の場合：
        ・Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0009に、タスク詳細（resultMessage）を「タスクの実行に失败しました。サポートサービスにお問い合わせください。(EMET0009)」に更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。
        ・エラー詳細をログに出力して、処理を終了する。
6.  コンテナのステータスがIDLEであるかチェックする。IDLEなら処理を続行する。
    *   BUSY の場合（対象コンテナが先に他のタスクに占有された）：
        ・Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0001に、タスク詳細（resultMessage）を「{0}に対するタスクを実行中のため実行できません。実行中のタスクが完了してから再度実行してください。」（{0}は対象サーバ名Task.targetServerNameで置換）に更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。
        ・エラー詳細をログに出力して、処理を終了する。
7.  コンテナ実行状態ContainerConcurrencyStatus テーブルから対象コンテナの タスク実行状態（status） を'BUSY' に、使用中のタスクID（currentTaskId） を入力の taskId に更新する。条件：対象VM名と対象コンテナ名が3.で取得した情報と一致し、最終更新日時がステップ5.で取得した日時と一致する。
    *   更新に失敗した場合：
        ・Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0007に、errorMessageをシステムが出力したエラー情報に、タスク詳細（resultMessage）を「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0007)」に更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。
        ・エラー詳細をログに出力して、処理を終了する。
    *   更新した件数が0件の場合（対象コンテナが先に他のタスクに占有された）：
        ・Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0001に、タスク詳細（resultMessage）を「{0}に対するタスクを実行中のため実行できません。実行中のタスクが完了してから再度実行してください。」（{0}は対象サーバ名Task.targetServerNameで置換）に更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。
        ・エラー詳細をログに出力して、処理を終了する。
8.  Azure Files共有ストレージ上に、現在の taskId を用いたタスク固有の一時作業ディレクトリ TaskWorkspaces/<taskId>/ を作成する。その配下に、インポートファイル用サブディレクトリ imports/ およびエクスポートファイル用サブディレクトリ exports/ を作成する。
    *   ディレクトリの作成に失敗した場合：
        ・コンテナ実行状態ContainerConcurrencyStatus テーブルから対象コンテナの タスク実行状態（status） を'IDLE' に、使用中のタスクID（currentTaskId） をnull に更新する。条件：対象VM名と対象コンテナ名がステップ3.で取得した情報と一致、ステータスがBUSY、使用中のタスクID（currentTaskId）が入力のtaskIdと一致。
        ・Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0002に、errorMessageをシステムが出力したエラー情報に、タスク詳細（resultMessage）を「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0002)」に更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。
        ・エラー詳細をログに出力して、処理を終了する。
9.  タスク種別 (Task.taskType) が管理項目定義のインポート (TASK_TYPE.MGMT_ITEM_IMPORT) の場合のファイル処理。
    a.  ステップ3.で取得したタスク情報のタスクパラメータparametersJsonから、importedFileBlobPath (Blob上の一時CSVファイルパス、「{licenseId}/imports/{taskId}/assetsfield_def.csv」の形式) を取得する。
    b.  importedFileBlobPath と管理項目定義ファイルを保存するAzure Blob Storageのコンテナ名環境変数 AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF（デフォルトは「assetsfield-def」）を合わせて一時CSVファイルのパスを組み立てて、一時CSVファイルをステップ8.で作成したAzure Files上の TaskWorkspaces/<taskId>/imports/ ディレクトリへ、固定ファイル名 assetsfield_def.csv でコピーする。
    *   コピーに失敗した場合：
        ・ステップ8.で作成したAzure Files上の一時作業ディレクトリの削除を行う。
        ・コンテナ実行状態ContainerConcurrencyStatus テーブルから対象コンテナの タスク実行状態（status） を'IDLE' に、使用中のタスクID（currentTaskId） をnull に更新する。条件：対象VM名と対象コンテナ名がステップ3.で取得した情報と一致、ステータスがBUSY、使用中のタスクID（currentTaskId）が入力のtaskIdと一致。
        ・Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorMessageをシステムが出力したエラー情報に、Azure Filesによるエラーの場合はerrorCodeとタスク詳細（resultMessage）をEMET0002と「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0002)」に、Azure Blob Storageによるエラーの場合はerrorCodeとタスク詳細（resultMessage）をEMET0003と「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0003)」に更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。
        ・エラー詳細をログに出力して、処理を終了する。
    *   他のタスク種別の場合: ステップ9.はスキップする。
10. タスクTaskテーブルから入力タスクのステータスを RUNBOOK_SUBMITTED に、開始日時を現在の日時（UTC協定世界時）に更新する。条件：IDが入力のtaskIdと一致し、最終更新日時がステップ3.で取得した最終更新日時と一致する。
    *   更新に失敗した場合：
        ・ステップ8.で作成したAzure Files上の一時作業ディレクトリの削除を行う。
        ・コンテナ実行状態ContainerConcurrencyStatus テーブルから対象コンテナの タスク実行状態（status） を'IDLE' に、使用中のタスクID（currentTaskId） をnull に更新する。条件：対象VM名と対象コンテナ名がステップ3.で取得した情報と一致、ステータスがBUSY、使用中のタスクID（currentTaskId）が入力のtaskIdと一致。
        ・Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0007に、errorMessageをシステムが出力したエラー情報に、タスク詳細（resultMessage）を「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0007)」に更新する。条件：IDが入力のtaskIdと一致、最終更新日時がステップ3.で取得した最終更新日時と一致。
        ・エラー詳細をログに出力して、処理を終了する。
    *   更新した件数が0件の場合（対象タスクに対してユーザーが中止操作を行った）：
        ・ステップ8.で作成したAzure Files上の一時作業ディレクトリの削除を行う。
        ・コンテナ実行状態ContainerConcurrencyStatus テーブルから対象コンテナの タスク実行状態（status） を'IDLE' に、使用中のタスクID（currentTaskId） をnull に更新する。条件：対象VM名と対象コンテナ名がステップ3.で取得した情報と一致、ステータスがBUSY、使用中のタスクID（currentTaskId）が入力のtaskIdと一致。
        ・エラー詳細をログに出力して、処理を終了する。
11. REST API リクエストを構築する。
    以下の通りでAzureのREST APIを呼び出すリクエストの準備を行う。
    URI：PUT https://management.azure.com/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Automation/automationAccounts/{automationAccountName}/jobs/{jobName}?api-version=2023-11-01
    ※{subscriptionId}には環境変数SUBSCRIPTION_IDで指定されたサブスクリプションID、{resourceGroupName}には環境変数RESOURCE_GROUP_NAMEで指定されたリソースグループ名、{automationAccountName}には環境変数AZURE_AUTOMATION_ACCOUNT_NAMEで指定されたAutomationのアカウント名、{jobName}にはtaskIdを設定する（即ち 作成されたRunbookジョブ名 ＝ taskId）。

    Request Body：
    *   ①操作ログのエクスポートタスクの場合
        ```json
        {
          "properties": {
            "runbook": {
              "name": "環境変数RUNBOOK_OPLOG_EXPORTで指定されたRunbook（基盤スクリプト）名"
            },
            "parameters": {
              "taskId": "taskId",
              "targetContainerName": "ステップ3.で取得したTask.targetContainerName",
              "exportStartDate": "ステップ3.で取得したTask.parametersJsonからのYYYY-MM-DD形式の開始日文字列",
              "exportEndDate": "ステップ3.で取得したTask.parametersJsonからのYYYY-MM-DD形式の終了日文字列"
            },
            "runOn": "ステップ3.で取得したTask.targetHRWGroupName"
          }
        }
        ```
    *   ②管理項目定義のインポートタスクの場合
        ```json
        {
          "properties": {
            "runbook": {
              "name": "環境変数RUNBOOK_MGMT_ITEM_IMPORTで指定されたRunbook（基盤スクリプト）名"
            },
            "parameters": {
              "taskId": "taskId",
              "targetContainerName": "ステップ3.で取得したTask.targetContainerName"
            },
            "runOn": "ステップ3.で取得したTask.targetHRWGroupName"
          }
        }
        ```
    *   ③管理項目定義のエクスポートタスクの場合
        ```json
        {
          "properties": {
            "runbook": {
              "name": "環境変数RUNBOOK_MGMT_ITEM_EXPORTで指定されたRunbook（基盤スクリプト）名"
            },
            "parameters": {
              "taskId": "taskId",
              "targetContainerName": "ステップ3.で取得したTask.targetContainerName"
            },
            "runOn": "ステップ3.で取得したTask.targetHRWGroupName"
          }
        }
        ```
    ※Runbook（基盤スクリプト）側は以下の書き方でパラメータを受け取ることができる。渡すパラメータはすべて文字列の形式となる。
    （例）
    ```powershell
    param (
        [string]$taskId,
        [string]$targetContainerName
    )

    Write-Output "TaskID is $taskId"
    Write-Output "TargetContainer is $targetContainerName"
    ```
12. ステップ11.で準備したリクエストでAzure AutomationのRunbookジョブ作成APIを呼び出す。
    *   API の呼び出しに失敗した場合（レスポンスのHTTPステータスコードが201（OK）でない場合）：
        ・ステップ8.で作成したAzure Files上の一時作業ディレクトリの削除を行う。
        ・コンテナ実行状態ContainerConcurrencyStatus テーブルから対象コンテナの タスク実行状態（status） を'IDLE' に、使用中のタスクID（currentTaskId） をnull に更新する。条件：対象VM名と対象コンテナ名がステップ3.で取得した情報と一致、ステータスがBUSY、使用中のタスクID（currentTaskId）が入力のtaskIdと一致。
        ・Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0013に、errorMessageをシステムが出力したエラー情報に、タスク詳細（resultMessage）を「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0013)」に更新する。条件：IDが入力のtaskIdと一致、ステータスがRUNBOOK_SUBMITTED。
        ・エラー詳細をログに出力して、処理を終了する。
    
    サンプルレスポンス
    Status code: 201
    ```json
    {
      "id": "/subscriptions/********-3ed7-4a72-a187-0c8ab644ddab/resourceGroups/mygroup/providers/Microsoft.Automation/automationAccounts/ContoseAutomationAccount/jobs/jobName",
      "name": "foo",
      "type": "Microsoft.Automation/AutomationAccounts/Jobs",
      "properties": {
        "jobId": "5b8a3960-e8ab-45f6-bec6-567df8467d1a",
        "runbook": {
          "name": "TestRunbook"
        },
        "provisioningState": "Processing",
        "creationTime": "2018-02-01T05:53:30.243+00:00",
        "endTime": null,
        "exception": null,
        "lastModifiedTime": "2018-02-01T05:53:30.243+00:00",
        "lastStatusModifiedTime": "2018-02-01T05:53:30.243+00:00",
        "startTime": null,
        "status": "New",
        "statusDetails": "None",
        "parameters": {
          "tag01": "value01",
          "tag02": "value02"
        },
        "runOn": ""
      }
    }
    ```
13. ステップ12.までの処理が正常に完了した場合、以下のタスクレコード保持件数クリーンアップ処理を実行し、例外が発生した場合でも、catchして適切にログ記録するだけで特に対処せず、1.-12.の処理結果に影響を与えない形で行う。
    a.  ステップ3.で取得したタスク情報の対象サーバID（Task.targetServerId）を条件にタスクTaskテーブルを検索し、対象サーバIDのタスクレコードの件数を取得する。
    b.  対象サーバIDのタスクレコードの件数が、値の一覧LovテーブルのTASK_CONFIG.MAX_RETENTION_COUNTで設定された保有件数上限を超過しているか、超過の場合何件超過しているのか確認する。
    c.  超過している場合、レコードの作成日時`submittedAt`の古い順から、ステータスが終了済み（COMPLETED_SUCCESS, COMPLETED_ERROR, CANCELLEDのいずれか）のタスクレコードを、超過した件数分選択して、削除対象とする。選択できるレコードの件数が超過した件数より少ない場合は選択できる分のレコードを選択する。選択できるレコードが存在しない場合は削除を行わない。
    d.  選択された各タスクレコードについて、関連する操作ログOperationLogテーブルのレコード、Azure Blob Storage上の関連ファイル（操作ログのエクスポートファイル、管理項目定義のエクスポートファイル、管理項目定義インポート時の一時アップロードファイル等）、Taskテーブルのレコードの順番で削除する。関連レコード、ファイルはタスクIDで結びつけられている。
    e.  このクリーンアップ処理中のエラー（例：一部ファイルの削除失敗等）はcatchした後ログに記録し、特に対処を行わない。
14. 正常終了ログを記録し、処理を終了する（メッセージACK）。

※対象コンテナのステータスがBUSY、作業ディレクトリ作成失敗、Runbookジョブ作成API呼び出し失敗などのエラー発生時は補償処理としてDB更新を行うが、更新件数が0件の場合は、例外をthrowすることで、タスク実行タイムアウト関数を起動する。

### 主要なデータ及び外部サービスとの対話詳細

#### Azure Service Bus (TaskInputQueue) からのメッセージ受信
*   本Functionは TaskInputQueue のメッセージをトリガーとして起動する。
*   メッセージはJSON形式であり、その主な構造は本設計書「概要 - 主要入力」セクションを参照。
*   TaskInputQueueの最大配信数は1に設定されているため、タスク実行関数は一回だけ起動される。タスク実行関数が処理成功した場合メッセージは完了確認（ACK）されキューから削除される。タスク実行関数が例外やタイムアウトにより処理失敗した場合メッセージは破棄され、Azure Service BusによってTaskInputQueueのDLQへ送られる。

#### データベース (Task, ContainerConcurrencyStatus 等) との対話
*   **Task テーブル**:
    *   taskId をキーに読み取り、タスクに関する情報、例えばタスク種別（taskType）、現在のステータス (status)、サーバ名（targetServerName）、VM 名（targetVmName） 、コンテナ名（targetContainerName）、HRWグループ名（targetHRWGroupName）、タスクパラメータ (parametersJson)、最終更新日時（updatedAt） などを取得する。
    *   処理の進行や結果に応じて、ステータスstatus, 開始日時startedAt, 最終更新日時updatedAt, タスク詳細resultMessage, エラーコードerrorCode, エラーメッセージerrorMessage の各フィールドを更新する。
    *   エラー発生時は状況に応じて対象タスクのレコードを status = 'COMPLETED_ERROR', errorCode = 該当のエラーコード, resultMessage = 該当のエラーメッセージ で更新する。
    *   タスクレコード保持件数超過時のクリーンアップ処理では、古いタスクレコードが削除される。
*   **ContainerConcurrencyStatus テーブル**:
    *   (azureVmName, dockerContainerName) を複合キーとして読み取り、現在のコンテナ実行状態 (status)と最終更新日時 を確認する。
    *   status を BUSY に、currentTaskId を入力のタスクIDに更新。
    *   コンテナのステータスをBUSYに更新した後、処理中にエラーが発生した時は対象コンテナのレコードを status = 'IDLE', currentTaskId = null で更新する。
*   **OperationLog テーブル**:
    *   タスクレコード保持件数超過時のクリーンアップ処理では、古いタスクレコードと関連する操作ログのレコードが削除される。

#### Azure Automation API との対話
*   REST APIを使用して、指定されたAutomationアカウントにRunbookジョブを作成する。
*   主な送信データ：ジョブ名（＝タスクID）、Runbook名、ターゲットHRWグループ名 (Task.targetHRWGroupName)、Runbookへの入力パラメータ（タスクID、コンテナ名、タスク固有パラメータ等）。

#### Azure Files / Azure Blob Storage との対話
*   **Azure Files**:
    *   各タスクの作業ディレクトリ TaskWorkspaces/<taskId>/ を作成し、その配下に imports/ および exports/ サブディレクトリを作成する。Runbookはこの作業ディレクトリを基準にファイル操作を行う。
    imports/ はインポートタスクの入力ファイル格納用、exports/ はエクスポートタスクの出力ファイルおよび中間ファイル、エラーメッセージファイルerrordetail.txt格納用である。
    *   管理項目定義インポートタスクの場合、Blobから一時CSVファイルを TaskWorkspaces/<taskId>/imports/ ディレクトリに、固定ファイル名 assetsfield_def.csv でダウンロードする。
*   **Azure Blob Storage**:
    *   管理項目定義インポートタスクの場合、Task.parametersJson内のimportedFileBlobPath（「{licenseId}/imports/{taskId}/assetsfield_def.csv」）に基づき、環境変数AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF （デフォルトで「assetsfield-def」） で指定されるコンテナからCSVファイルをAzure Files作業領域へダウンロードする。
    *   タスクレコード保持件数超過時のクリーンアップ処理では、削除対象のタスクレコードと関連するBlob Storage上のファイルが削除される。

### エラー処理メカニズム

| エラーシナリオ | 関連エラーコード | 対処・補償ロジック |
|:---|:---|:---|
| 入力パラメータのタスクIDが不正/不足 | - | エラー詳細をログに出力して、処理を終了する。（メッセージACK） |
| タスクレコードを取得する際にレコードが存在しない/DB読み取り失敗 | - | エラー詳細をログに出力して、処理を終了する。（メッセージACK） |
| タスクのステータスがQUEUEDでない | - | エラー詳細をログに出力して、処理を終了する。（メッセージACK） |
| コンテナ実行状態レコードを取得する際にレコードが存在しない/タスク情報またはコンテナ実行状態情報の構成不備 (タスク種別taskType、ステータスstatusなどの不正/不足) | EMET0009 | Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0009に、タスク詳細（resultMessage）を「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)」に更新する。エラー詳細をログに出力して、処理を終了する。（メッセージACK） |
| コンテナのステータスがすでにBUSY / コンテナのステータスをBUSYに更新した件数が0件 | EMET0001 | Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0001に、タスク詳細（resultMessage）を「{0}に対するタスクを実行中のため実行できません。実行中のタスクが完了してから再度実行してください。」（{0}は対象サーバ名Task.targetServerNameで置換）に更新する。エラー詳細をログに出力して、処理を終了する。（メッセージACK） |
| コンテナ実行状態レコードを取得する際にDB読み取り失敗/DB更新（タスクレコード、コンテナ実行状態レコード）失敗 | EMET0007 | コンテナのステータスをBUSYに更新した場合はIDLEに更新する（戻す）。Azure Files上の一時作業ディレクトリが作成された場合は一時作業ディレクトリを削除する。Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0007に、errorMessageをシステムが出力したエラー情報に、タスク詳細（resultMessage）を「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0007)」に更新する。エラー詳細をログに出力して、処理を終了する。（メッセージACK） |
| タスクのステータスを更新した件数が0件 | - | コンテナのステータスをIDLEに更新する（戻す）。Azure Files上の一時作業ディレクトリを削除する。エラー詳細をログに出力して、処理を終了する。（メッセージACK） |
| Azure Files作業ディレクトリ作成失敗、(管理項目定義インポートタスクの場合) BlobからAzure Filesへの一時CSVファイルコピー失敗 | ・Azure FilesのエラーならEMET0002<br>・BlobのエラーならEMET0003 | コンテナのステータスをIDLEに更新する（戻す）。Azure Files上の一時作業ディレクトリが作成された場合は一時作業ディレクトリを削除する。Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorMessageをシステムが出力したエラー情報に、errorCodeとタスク詳細（resultMessage）を該当のエラーコードとエラーメッセージに更新する。エラー詳細をログに出力して、処理を終了する。（メッセージACK） |
| Azure Automation Runbookジョブ作成API の呼び出し失敗 | EMET0013 | コンテナのステータスをIDLEに更新する（戻す）。Azure Files上の一時作業ディレクトリを削除する。Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0013に、errorMessageをシステムが出力したエラー情報に、タスク詳細（resultMessage）を「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0013)」に更新する。エラー詳細をログに出力して、処理を終了する。（メッセージACK） |
| 予期せぬ内部エラー | EMET0008 | コンテナのステータスをBUSYに更新した場合はIDLEに更新する（戻す）。Azure Files上の一時作業ディレクトリが作成された場合は一時作業ディレクトリを削除する。Taskテーブルの該当タスクのステータスをCOMPLETED_ERRORに、errorCodeをEMET0008に、errorMessageをシステムが出力したエラー情報に、タスク詳細（resultMessage）を「タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0008)」に更新する。エラー詳細をログに出力して、処理を終了する。（メッセージACK） |
| エラー発生時の補償処理にも失敗/DB更新件数が0件 | - | エラー詳細をログに記録した後、例外をthrowすることでキューメッセージが破棄され、DLQに配信されて、タスク実行タイムアウト関数が起動される。 |

#### タイムアウトについて
Azure Functionsがタスク実行関数の実行時間を監視し、host.jsonのfunctionTimeoutプロパティで設定されたタイムアウト時間（5分）になっても関数がまだ終了していない場合、Azure Functionsは関数の実行を強制的に中止する。

#### リトライについて
TaskInputQueueの最大配信数は1に設定されているため、タスク実行関数は一回だけ実行される。例外やタイムアウトにより処理失敗の場合でもリトライは行わない。この場合はトリガーとなったメッセージが破棄され、Azure Service BusによってTaskInputQueueのDLQへ送られて、タスク実行タイムアウト関数が起動される。