# 组件：任务执行函数 (Task Execute Function)

## 1. 概要 (Overview)

### 1.1. 目的 (Purpose)
本Azure Function (`TaskExecuteFunc`) 作为后台任务处理流程的起点，负责接收从门户系统提交的、已通过初步校验和并发检查（只读）的新后台任务请求。其核心职责包括：从数据库获取任务详细信息和执行上下文，原子性地获取目标容器的并发锁，准备执行环境（如Azure Files工作区、下载导入文件），最终使用任务ID (`Task.id`) 作为作业ID向Azure Automation提交相应的Runbook作业，并更新任务状态。提交后的作业状态监控由[`RunbookMonitorFunc`](./function-runbook-monitor.md)负责，其最终结果由[`RunbookProcessorFunc`](./function-runbook-processor.md)处理。在其核心职责完成后，本函数还会异步执行针对当前服务器的旧已完成任务记录的保留策略清理逻辑。

### 1.2. 范围 (Scope)
本文档详细描述`TaskExecuteFunc`的技术设计，包括其触发机制、核心处理逻辑、与数据库、Azure Files、Azure Blob Storage及Azure Automation的交互、错误处理机制以及相关的配置项。它处理所有类型的后台任务，包括但不限于操作日志导出 (`TASK_TYPE.OPLOG_EXPORT`)、管理项目定义导入 (`TASK_TYPE.MGMT_ITEM_IMPORT`) 和管理项目定义导出 (`TASK_TYPE.MGMT_ITEM_EXPORT`)。

### 1.3. 组件类型 (Component Type)
*   Azure Function (Node.js/TypeScript 运行时)
*   触发器: Azure Service Bus Queue Trigger (监听 `TaskInputQueue`)

### 1.4. 名词定义 (Glossary References)
*   **TaskInputQueue**: Azure Service Bus队列，用于接收包含新任务ID (`taskId`) 的消息。其名称由环境变量 `SERVICE_BUS_TASK_INPUT_QUEUE_NAME` 指定。
*   **Azure Files工作区**: 为每个任务在Azure Files上创建的临时工作目录，路径格式为 `TaskWorkspaces/{taskId}/`，包含 `imports/` 和 `exports/` 子目录。
*   **容器并发锁**: 通过数据库表 `ContainerConcurrencyStatus` 实现的机制，确保同一时间只有一个任务能操作特定VM上的特定Docker容器。
*   **任务记录保留策略**: 系统定义的规则，用于自动清理旧的已完成任务记录及其关联资源，以控制数据量。
*   其他相关术语请参考项目核心术语表 [`项目术语表`](../../../definitions/glossary.md)。
*   本文档中所有用户可见的错误和提示信息，均引用自项目核心错误消息定义文档 [`错误消息定义`](../../../definitions/error-messages.md)。
*   本文档中所有环境变量的定义，均参考项目核心环境变量指南 [`环境变量指南`](../../../guides/environment-variables.md)。
*   任务状态码常量（如 `TASK_STATUS_QUEUED_CODE`, `TASK_STATUS_RUNBOOK_SUBMITTED_CODE`）及其他内部常量定义在 `app/lib/definitions.ts`。

## 2. 功能规格 (Functional Specifications)

### 2.1. 主要流程 (Main Process Flow)

```mermaid
graph TD
    A["TaskInputQueue接收到任务消息<br/>(包含taskId)"] --> B{TaskExecuteFunc被触发};
    B --> C["1.解析消息, 获取taskId<br/>记录日志: 开始处理任务"];
    C --> D["2.从Task表查询任务详情<br/>(使用taskId)"];
    D -- "任务不存在或状态非QUEUED<br/>(或PENDING_CANCELLATION/CANCELLED)" --> D_Error_InvalidTask["2.1 处理无效任务<br/>(更新状态为ERROR或记录日志, 终止)"];
    D -- "任务有效 (状态QUEUED)" --> E["3.参数与执行上下文校验<br/>(targetVmName, dockerContainerName, hrwGroupName等<br/>及任务特定参数如importedFileBlobPath)"];
    E -- "校验失败 (如配置缺失)" --> F_Error_Config["3.1 更新Task状态为ERROR<br/>(EMET0009), 终止"];
    E -- "校验成功" --> G["4.原子性获取容器并发锁<br/>(更新ContainerConcurrencyStatus)"];
    G -- "获取锁失败 (容器BUSY)" --> H_Error_Busy["4.1 更新Task状态为ERROR<br/>(EMET0001), 终止"];
    G -- "获取锁成功" --> I["5.准备Azure Files工作区<br/>(创建 TaskWorkspaces/{taskId}/imports|exports)"];
    I -- "工作区准备失败" --> J_Error_Workspace["5.1 回滚并发锁<br/>更新Task状态为ERROR (EMET0002)<br/>终止"];
    I -- "工作区准备成功" --> K{任务类型是否为<br/>'TASK_TYPE.MGMT_ITEM_IMPORT'?};
    K -- 是 --> L["5.2 从Blob临时存储下载导入文件<br/>(源路径来自Task.parametersJson.importedFileBlobPath)<br/>到工作区 imports/assetsfield_def.csv<br/>并立即删除Blob临时文件"];
    L -- "下载或删除Blob失败" --> M_Error_ImportFile["5.2.1 回滚并发锁, 清理工作区<br/>更新Task状态为ERROR (EMET0003)<br/>终止"];
    L -- "下载并删除Blob成功" --> N;
    K -- 否 --> N["6.提交Runbook作业至Azure Automation<br/>(使用Task.id作为作业ID, <br/>hrwGroupName来自Task记录)"];
    N -- "作业提交失败" --> O_Error_SubmitJob["6.1 回滚并发锁, 清理工作区<br/>(若有导入文件, 已下载则保留在工作区供后续清理)<br/>更新Task状态为ERROR (EMET0013)<br/>终止"];
    N -- "作业提交成功" --> P["6.2 更新Task状态为RUNBOOK_SUBMITTED<br/>记录startedAt"];
    P --> Q["7.(异步) 执行任务记录保留策略清理<br/>(针对当前targetServerId)"];
    Q --> R["8.记录成功处理日志"];
    D_Error_InvalidTask --> Z_End["结束处理"];
    F_Error_Config --> Z_End;
    H_Error_Busy --> Z_End;
    J_Error_Workspace --> Z_End;
    M_Error_ImportFile --> Z_End;
    O_Error_SubmitJob --> Z_End;
    R --> Z_End;
```
**图 2.1: TaskExecuteFunc 主要处理流程图**

### 2.2. 功能点描述 (Functional Points)
1.  **消息触发与解析**: 函数由`TaskInputQueue`中的新消息触发。消息体包含需要执行的任务的ID (`taskId`)。
2.  **任务详情与状态校验**:
    *   根据`taskId`从数据库`Task`表查询完整的任务记录。
    *   **校验任务存在性与初始状态**:
        *   若任务记录不存在，或其当前`status`不是 `TASK_STATUS_QUEUED_CODE` ("QUEUED")，则记录错误并终止处理。
        *   若任务状态为 `TASK_STATUS_PENDING_CANCELLATION_CODE` ("PENDING_CANCELLATION") 或 `TASK_STATUS_CANCELLED_CODE` ("CANCELLED")，则也应中止处理，记录日志表明任务已被取消或请求取消，不继续执行。
3.  **参数与执行上下文校验**:
    *   从查询到的`Task`记录中，校验执行该任务所必需的上下文信息是否完整有效，例如 `targetVmName`, `dockerContainerName`, `hrwGroupName`。
    *   对于特定任务类型（如 `'TASK_TYPE.MGMT_ITEM_IMPORT'`），校验其在 `Task.parametersJson` 中存储的特定参数（如 `importedFileBlobPath`, `originalFileName`）是否存在且格式基本正确。
    *   若校验失败（例如，关键配置缺失），则更新`Task.status`为 `TASK_STATUS_COMPLETED_ERROR_CODE` ("COMPLETED_ERROR")，`resultMessage` 更新为消息键 `EMET0009` 对应的日文消息，并终止处理。
4.  **容器并发锁获取**:
    *   原子地（在数据库事务内）尝试获取目标容器 (`targetVmName`, `dockerContainerName`) 的并发执行锁。此操作涉及读取并更新 `ContainerConcurrencyStatus` 表中对应记录：将其 `status` 从 `'IDLE'` 更新为 `'BUSY'`，并将 `currentTaskId` 设置为当前任务的 `taskId`。
    *   若获取锁失败（例如，记录状态已为 `'BUSY'` 且 `currentTaskId` 不同），则更新当前`Task.status`为 `TASK_STATUS_COMPLETED_ERROR_CODE`，`resultMessage` 更新为消息键 `EMET0001` 对应的日文消息 (占位符 `{0}` 替换为 `Task.targetServerName`)，并终止处理。
5.  **执行环境准备**:
    *   **创建Azure Files工作区**: 在Azure Files文件共享上，为当前任务创建一个唯一的工作区目录，路径格式为 `TaskWorkspaces/{taskId}/`。在该目录下创建 `imports/` 和 `exports/` 两个子目录。
        *   若工作区创建失败（例如，权限问题、Azure Files服务不可用），则必须回滚第4步获取的并发锁（将`ContainerConcurrencyStatus`状态改回`IDLE`，清除`currentTaskId`），更新`Task.status`为 `TASK_STATUS_COMPLETED_ERROR_CODE`，`resultMessage` 更新为消息键 `EMET0002` 对应的日文消息，并终止处理。
    *   **处理导入文件 (仅当 `taskType` 为 `'TASK_TYPE.MGMT_ITEM_IMPORT'`)**:
        *   从 `Task.parametersJson` 中获取 `importedFileBlobPath` (指向临时上传到 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` 容器下 `{licenseId}/imports/{taskId}/assetsfield_def.csv` 的相对路径)。
        *   从Azure Blob Storage的该临时位置下载CSV文件（其Blob名为 `assetsfield_def.csv`），并将其保存到当前任务的Azure Files工作区的 `imports/assetsfield_def.csv` 路径下。
        *   **下载成功后，立即删除Azure Blob Storage中的该临时文件 (`{licenseId}/imports/{taskId}/assetsfield_def.csv`)。**
        *   若下载或删除Blob文件失败，则必须回滚并发锁，清理已创建的工作区（如果部分创建），更新`Task.status`为 `TASK_STATUS_COMPLETED_ERROR_CODE`，`resultMessage` 更新为消息键 `EMET0003` 对应的日文消息，并终止处理。
6.  **Runbook作业提交与状态更新**:
    *   构造向Azure Automation提交作业所需的参数，包括：
        *   Runbook名称 (根据`Task.taskType`映射，例如 `'TASK_TYPE.OPLOG_EXPORT'` 对应 `Export-OperationLog.ps1`)。
        *   Runbook参数 (从`Task.parametersJson`和`Task`表其他字段提取，例如`Task.id`作为`taskId`参数，`Task.dockerContainerName`作为`containerName`参数，导入任务的源文件路径为工作区内的`imports/assetsfield_def.csv`，操作日志导出的日期范围等)。
        *   目标Hybrid Runbook Worker组名 (从`Task.hrwGroupName`获取)。
    *   **使用当前`Task.id` (即`taskId`) 作为Azure Automation作业的ID (JobId)**，调用Azure Automation API提交Runbook作业。
    *   **作业提交成功**:
        *   更新`Task.status`为 `TASK_STATUS_RUNBOOK_SUBMITTED_CODE` ("RUNBOOK_SUBMITTED")。
        *   更新`Task.startedAt`为当前时间戳。
    *   **作业提交失败**:
        *   回滚并发锁。
        *   清理工作区。
        *   更新`Task.status`为 `TASK_STATUS_COMPLETED_ERROR_CODE`。
        *   `resultMessage` 更新为消息键 `EMET0013` 对应的日文消息。
        *   终止处理。
7.  **任务记录保留策略执行 (异步)**:
    *   在上述核心职责（特别是作业已成功提交到Azure Automation）完成后，本函数将针对当前任务的 `targetServerId`，异步地（不阻塞主流程的完成）执行任务记录保留策略。
    *   此策略确保每个服务器 (`targetServerId`) 在`Task`表中保留的**已完成**状态 (`COMPLETED_SUCCESS`, `COMPLETED_ERROR`, `CANCELLED`) 的任务记录数量不超过系统配置的上限（该上限值从 `LOV` 表 `TASK_CONFIG.MAX_RETENTION_COUNT` 获取，其定义参见[`LOV值列表定义`](../../../definitions/lov-definitions.md)）。
    *   如果需要清理，将按`submittedAt`字段升序（即最旧的优先）删除超出的已完成任务记录。
    *   **联动删除关联资源**: 在删除旧的`Task`记录时，必须联动删除其所有关联的外部资源：
        *   对于**操作日志导出任务** (`'TASK_TYPE.OPLOG_EXPORT'`)：查询`OperationLog`表获取所有由该`Task.id`生成的记录，并删除这些记录及其在Azure Blob Storage中对应的所有日志文件（文件路径由`OperationLog`记录或约定规则确定，容器由环境变量 `AZURE_STORAGE_CONTAINER_OPLOGS` 指定）。
        *   对于**管理项目定义导出任务** (`'TASK_TYPE.MGMT_ITEM_EXPORT'`)：如果`Task.outputBlobPath`字段有值，则删除该路径指向的Azure Blob Storage中的文件（容器由环境变量 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` 指定）。
        *   对于**管理项目定义导入任务** (`'TASK_TYPE.MGMT_ITEM_IMPORT'`)：如果`Task.parametersJson`中包含`importedFileBlobPath`且该文件在Blob中仍然存在（理论上应已被本函数步骤5.2成功下载后删除，此处为补充清理），则删除该临时上传文件（容器由环境变量 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` 指定）。
    *   此清理操作的失败不应影响当前新任务的整体处理结果，但应记录详细错误日志。
8.  **日志记录**: 对所有关键步骤、参数、执行结果及错误进行详细日志记录。

### 2.3. 业务规则 (Business Rules)
*   本函数处理所有类型的后台任务，其具体执行的Runbook和参数化逻辑根据`Task.taskType`动态确定。
*   严格执行容器并发控制，确保同一容器在同一时间只执行一个任务。
*   对于管理项目定义导入任务，临时上传的CSV文件在被下载到工作区后，必须立即从Blob临时存储中删除。
*   任务记录的保留和清理遵循系统定义的策略，并负责联动删除关联的Blob存储文件。

### 2.4. 前提条件 (Preconditions)
*   Azure Service Bus (`TaskInputQueue`) 正常运行。
*   Azure SQL Database (`Task`, `ContainerConcurrencyStatus`, `Server`, `Lov` 等表) 正常运行且可访问。
*   Azure Files 服务正常运行且已配置，Function App具有访问权限。
*   Azure Blob Storage 服务正常运行且已配置，Function App具有对相关容器的读写删权限。
*   Azure Automation 服务正常运行，目标Runbook已发布，目标HRW组已配置且HRW Agent在线。
*   Function App的托管身份或配置的服务主体具有调用Azure Automation API提交作业的权限。
*   相关的`LOV`定义（特别是`TASK_STATUS`, `TASK_TYPE`, `TASK_CONFIG.MAX_RETENTION_COUNT`）和错误消息定义已在系统中正确配置。
*   所有必需的环境变量已正确设置。

### 2.5. 制约事项 (Constraints)
*   本函数执行时间受Azure Functions的超时限制（通过环境变量 `FUNCTION_TIMEOUT_SECONDS` 配置）。其核心操作（DB交互、文件准备、提交作业）应设计为在此限制内完成。长时间运行的Runbook作业由`RunbookMonitorFunc`进行异步监控。
*   对Azure Files和Blob Storage的操作性能可能受网络和服务本身限制。
*   任务记录保留策略的清理操作是异步的，其完成时间不确定，且其失败不会阻塞新任务的处理。

### 2.6. 注意事项 (Notes)
*   **幂等性**: Service Bus消息可能被重复投递。本函数需要具备幂等性处理能力。例如，如果一个已成功提交到Azure Automation的任务消息再次被处理，函数应能识别（例如通过检查`Task.status`是否已为`RUNBOOK_SUBMITTED`或更高阶状态）并避免重复提交作业或重复获取并发锁。
*   **错误处理与资源回收**: 在任何步骤失败后，必须确保已获取的并发锁得到释放，已创建的临时工作区得到清理（如果适用）。这是保证系统稳定性的关键。
*   **安全性**: 处理从`Task.parametersJson`中获取的参数（如Blob路径）时，需注意路径的合法性和安全性，防止路径操纵等攻击。
*   **配置驱动**: Runbook名称与`Task.taskType`的映射关系、Runbook参数的构造方式等，应考虑通过配置（例如，代码中的映射表或更灵活的配置方式）进行管理，以提高可维护性。

---

## 3. 技术设计与实现细节 (Technical Design & Implementation)

### 3.1. 技术栈与依赖 (Technology Stack & Dependencies)
*   **运行时**: Azure Functions (Node.js/TypeScript)。
*   **触发器**: Azure Service Bus Queue Trigger。
    *   队列名称: 由环境变量 `SERVICE_BUS_TASK_INPUT_QUEUE_NAME` 指定。
*   **数据库交互**: Prisma ORM (通过 `app/lib/data.ts` 封装的数据库服务或直接使用 `app/lib/prisma.ts`)。
*   **文件存储交互**:
    *   Azure Files: 使用 `@azure/storage-file-share` SDK。
    *   Azure Blob Storage: 使用 `@azure/storage-blob` SDK。
*   **Azure Automation交互**: 使用 `@azure/arm-automation` (Azure SDK for JavaScript/TypeScript - Management Plane) 或通过REST API调用。需配置相应的认证凭据（推荐使用托管身份）。
*   **消息队列SDK**: `@azure/service-bus` (主要用于消息属性读取，如果需要)。
*   **日志**: `app/lib/logger.ts`。
*   **核心定义与常量**: `app/lib/definitions.ts` (包含`TASK_STATUS`常量、消息键等)。
*   **UUID生成**: `uuid`库。

### 3.2. 详细界面元素定义 (Detailed UI Element Definitions)
*   本组件为后端Azure Function，无直接用户界面。

### 3.3. 详细事件处理逻辑 (Detailed Event Handling)
*   本组件由Service Bus消息触发，其核心处理逻辑见3.6节。

### 3.4. 数据结构与API/Server Action交互 (Data Structures and API/Server Action Interaction)

#### 3.4.1. 输入消息结构 (from `TaskInputQueue`)
消息体为一个JSON对象，预期包含以下核心字段：
```typescript
// 示例消息结构
{
  "taskId": "cuid-or-uuid-of-the-task-to-execute" 
  // TaskExecuteFunc 将使用此 taskId 从数据库查询完整的任务详情和参数
}
```

#### 3.4.2. 与数据库的交互
详见3.5节。

#### 3.4.3. 与Azure Files的交互 (SDK层面)
*   `ShareDirectoryClient.createIfNotExists()`: 创建任务工作区主目录 (`TaskWorkspaces/{taskId}/`)。
*   `ShareDirectoryClient.createSubDirectory()`: 创建 `imports/` 和 `exports/` 子目录。
*   `ShareFileClient.uploadData()` 或 `ShareFileClient.uploadStream()`: (如果需要直接写入文件，但通常是Runbook写入，本函数主要是创建目录结构；对于导入任务，是从Blob下载到本地临时文件再上传到Files，或直接流式传输)。
*   `ShareDirectoryClient.deleteIfExists()`: 在错误处理或资源回收时删除工作区目录。
*   **对于导入任务**: `ShareFileClient.uploadStream()` (或类似方法) 用于将从Blob下载的文件流写入到Azure Files工作区的 `imports/assetsfield_def.csv`。

#### 3.4.4. 与Azure Blob Storage的交互 (SDK层面 - 仅针对导入任务的文件下载和临时文件删除)
*   `BlobClient.downloadToBuffer()` 或 `BlobClient.downloadToFile()` 或 `BlobClient.downloadStream()`: 用于从临时存储位置 (`{licenseId}/imports/{taskId}/assetsfield_def.csv`，容器由 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` 指定) 下载管理项目定义导入的CSV文件。
*   `BlobClient.deleteIfExists()`: 下载成功后，立即删除该临时Blob文件。
*   (保留策略清理时) `BlobServiceClient.getContainerClient(...).getBlobClient(...).deleteIfExists()`: 用于删除旧任务关联的Blob文件。

#### 3.4.5. 与Azure Automation的交互 (SDK层面 - `AutomationClient` 或 REST API)
*   `automationClient.job.create()`: 提交新的Runbook作业。需要提供：
    *   Resource Group Name, Automation Account Name (来自环境变量或配置)。
    *   Job ID (使用`Task.id`)。
    *   Runbook Name。
    *   Runbook Parameters (一个对象，键值对形式)。
    *   Hybrid Runbook Worker Group Name (从`Task.hrwGroupName`获取)。

#### 3.4.6. 序列图 (Mermaid `sequenceDiagram`)

```mermaid
sequenceDiagram
    participant SBInputQ as "Azure Service Bus (TaskInputQueue)"
    participant FuncApp as "TaskExecuteFunc (Azure Function)"
    participant Database as "Azure SQL Database"
    participant AzFiles as "Azure Files (Workspaces)"
    participant AzBlob as "Azure Blob Storage (Imports/Exports)"
    participant AzAutomation as "Azure Automation API"

    SBInputQ->>FuncApp: 消息: { taskId: "task123" }
    activate FuncApp
    FuncApp->>FuncApp: 1. 解析 taskId
    FuncApp->>Database: 2. 查询Task详情 (task123)
    activate Database
    Database-->>FuncApp: Task对象 (status: 'QUEUED', params, execContext)
    deactivate Database
    FuncApp->>FuncApp: 3. 参数与上下文校验
    alt 校验失败
        FuncApp->>Database: 更新Task状态为ERROR
        FuncApp-->>SBInputQ: (处理结束或消息入DLQ)
    else 校验成功
        FuncApp->>Database: 4. 原子性获取容器并发锁 (ContainerConcurrencyStatus)
        activate Database
        Database-->>FuncApp: 锁获取结果
        deactivate Database
        alt 获取锁失败 (容器BUSY)
            FuncApp->>Database: 更新Task状态为ERROR (EMET0001)
            FuncApp-->>SBInputQ: (处理结束或消息入DLQ)
        else 获取锁成功
            FuncApp->>AzFiles: 5.1 创建工作区 TaskWorkspaces/task123/
            activate AzFiles
            AzFiles-->>FuncApp: 工作区创建结果
            deactivate AzFiles
            alt 工作区创建失败
                FuncApp->>Database: 回滚并发锁, 更新Task状态为ERROR (EMET0002)
                FuncApp-->>SBInputQ: (处理结束或消息入DLQ)
            else 工作区创建成功
                opt 任务类型为 MGMT_ITEM_IMPORT
                    FuncApp->>AzBlob: 5.2.1 下载导入文件 (e.g., assetsfield-def-imports/lic1/imports/task123/assetsfield_def.csv)
                    activate AzBlob
                    AzBlob-->>FuncApp: 文件流/内容
                    deactivate AzBlob
                    FuncApp->>AzFiles: 5.2.2 上传文件到工作区 imports/assetsfield_def.csv
                    activate AzFiles
                    AzFiles-->>FuncApp: 上传成功
                    deactivate AzFiles
                    FuncApp->>AzBlob: 5.2.3 删除Blob临时导入文件
                    activate AzBlob
                    AzBlob-->>FuncApp: 删除成功
                    deactivate AzBlob
                end
                alt 导入文件处理失败 (下载/上传/删除Blob)
                    FuncApp->>Database: 回滚并发锁, 清理工作区, 更新Task状态为ERROR (EMET0003)
                    FuncApp-->>SBInputQ: (处理结束或消息入DLQ)
                else (导入文件处理成功 或 非导入任务)
                    FuncApp->>AzAutomation: 6.1 提交Runbook作业 (JobId: task123, hrwGroup, params)
                    activate AzAutomation
                    AzAutomation-->>FuncApp: 作业提交结果
                    deactivate AzAutomation
                    alt 作业提交失败
                        FuncApp->>Database: 回滚并发锁, 清理工作区, 更新Task状态为ERROR (EMET0013)
                        FuncApp-->>SBInputQ: (处理结束或消息入DLQ)
                    else 作业提交成功
                        FuncApp->>Database: 6.2 更新Task状态为RUNBOOK_SUBMITTED, startedAt
                        FuncApp->>FuncApp: 7. (异步) 执行任务记录保留策略 (含可能的Blob删除)
                        FuncApp->>FuncApp: 8. 记录成功日志
                        FuncApp-->>SBInputQ: (消息处理完成)
                    end
                end
            end
        end
    end
    deactivate FuncApp
```

### 3.5. 数据库设计与访问详情 (Database Design and Access Details)

#### 3.5.1. 相关表引用
*   **`Task` 表**: 读取任务详情、参数、执行上下文；更新`status`, `startedAt`, `resultMessage`。
*   **`ContainerConcurrencyStatus` 表**: 读取并原子性更新容器的`status`和`currentTaskId`以实现并发锁。
*   **`Server` 表**: (间接) `createTaskAction` 已将所需信息（如`hrwGroupName`）冗余存储到`Task`表中，本函数直接从`Task`表读取。
*   **`LOV` 表**: 读取`TASK_CONFIG.MAX_RETENTION_COUNT`等配置。
*   **`OperationLog` 表**: 在执行任务记录保留策略时，可能需要删除与旧操作日志导出任务关联的记录。

#### 3.5.2. 主要数据查询/变更逻辑 (通过 `app/lib/data.ts` 或直接 Prisma 调用)

1.  **查询任务详情**:
    ```typescript
    prisma.task.findUnique({ where: { id: taskId } });
    ```
2.  **原子性获取/更新并发锁 (在事务内)**:
    ```typescript
    // 伪代码，实际实现需要更严谨的原子更新或SELECT FOR UPDATE (若DB支持且Prisma封装)
    const lock = await prisma.containerConcurrencyStatus.findUnique({
      where: { targetVmName_targetContainerName: { targetVmName, targetContainerName } }
    });
    if (lock && lock.status === 'BUSY' && lock.currentTaskId !== taskIdToAcquire) { /* 失败 */ }
    else {
      await prisma.containerConcurrencyStatus.update({
        where: { targetVmName_targetContainerName: { targetVmName, targetContainerName } },
        data: { status: 'BUSY', currentTaskId: taskIdToAcquire, lastStatusChangeAt: new Date() }
      });
      // (如果记录不存在，应由createTaskAction创建，此处假设已存在)
    }
    ```
3.  **回滚并发锁 (在事务内或独立操作)**:
    ```typescript
    prisma.containerConcurrencyStatus.update({
      where: { targetVmName_targetContainerName: { targetVmName, targetContainerName } },
      data: { status: 'IDLE', currentTaskId: null, lastStatusChangeAt: new Date() }
    });
    ```
4.  **更新任务状态**:
    ```typescript
    prisma.task.update({
      where: { id: taskId },
      data: { status: newStatus, startedAt?: new Date(), resultMessage?: messageText }
    });
    ```
5.  **任务记录保留策略**:
    *   查询已完成任务计数: `prisma.task.count({ where: { targetServerId, status: { in: ['COMPLETED_SUCCESS', 'COMPLETED_ERROR', 'CANCELLED'] } } })`
    *   查询待删除的旧任务: `prisma.task.findMany({ where: { targetServerId, status: { in: [...] } }, orderBy: { submittedAt: 'asc' }, take: numToDelete })`
    *   删除任务: `prisma.task.delete({ where: { id: oldTaskId } })`
    *   删除关联OperationLog: `prisma.operationLog.deleteMany({ where: { generatedByTaskId: oldTaskId } })`

*   **事务管理**: 获取并发锁的操作应在数据库事务中进行，以确保原子性。主流程中涉及多个步骤（获取锁、创建工作区、提交作业、更新任务状态），理想情况下，如果提交作业失败能可靠回滚（例如Azure Automation API调用是幂等的或有补偿机制），则整个核心流程可以考虑放在一个更大的事务中。但考虑到外部API调用的复杂性和时长，通常将并发锁获取作为一个独立的短事务，后续若失败则执行补偿操作（释放锁）。任务记录保留策略的数据库操作也应在事务内执行。

### 3.6. 关键后端逻辑/算法 (Key Backend Logic/Algorithms)

`TaskExecuteFunc` 的核心算法已在2.1节的流程图中清晰描述。关键的逻辑点包括：

1.  **顺序执行与错误处理分支**: 严格按照步骤进行，每一步的失败都会导致流程进入相应的错误处理分支，执行资源回滚（并发锁、工作区）和状态更新。
2.  **并发锁的原子获取**: 必须确保并发锁的检查和更新是一个原子操作，以防止竞态条件。
3.  **导入文件的处理**: 特定于导入任务的下载和临时Blob删除逻辑。
4.  **Runbook参数构造**: 根据任务类型和`Task`记录中的信息，动态构造传递给Runbook的参数。
5.  **任务记录保留策略的异步执行**:
    *   获取配置的保留数量。
    *   查询当前服务器的已完成任务。
    *   如果超出限制，按先进先出原则确定待删除任务。
    *   对每个待删除任务，执行关联的Blob文件删除（根据任务类型和`outputBlobPath`或`OperationLog`记录）。
    *   删除数据库中的`OperationLog`记录（如果适用）。
    *   删除数据库中的`Task`记录。
    *   所有这些删除操作应在一个事务内完成（针对单个旧任务的清理）。此清理过程应容错，单个旧任务清理失败不应影响其他任务的清理或新任务的执行。

### 3.7. 错误处理详情 (Detailed Error Handling)

| #  | 错误场景描述 (中文) | 触发位置 | 引用的消息ID/消息键 (用户可见部分，更新到`Task.resultMessage`) | 系统内部处理及日志记录建议 (中文) | 日志级别 |
|----|-----------------|----------|------------------------------------|---------------------------------|-----------|
| 1  | Service Bus消息格式无效或`taskId`缺失 | Function触发，消息解析阶段 | (内部错误，不更新Task) | 记录错误，包含原始消息。消息可能进入DLQ。 | ERROR |
| 2  | 根据`taskId`未找到任务，或任务状态非`QUEUED` (或已被取消) | 查询`Task`表后 | (内部错误，若任务非`QUEUED`但存在，则按原样保留；若不存在，则不更新) | 记录错误/警告，包含`taskId`和当前状态。安全结束，避免消息重试死循环。 | WARN/ERROR |
| 3  | 执行任务所需配置或参数缺失/无效 (从`Task`记录读取时发现) | 参数与执行上下文校验阶段 | `EMET0009` | 更新`Task.status`为`ERROR`。记录详细的缺失/无效配置项。 | ERROR |
| 4  | 获取容器并发锁失败 (容器繁忙) | 并发锁获取阶段 | `EMET0001` (占位符为`Task.targetServerName`) | 更新`Task.status`为`ERROR`。记录目标容器和当前占用任务ID（如果可获取）。 | INFO |
| 5  | Azure Files工作区创建/访问失败 | 工作区准备阶段 | `EMET0002` | **回滚并发锁**。更新`Task.status`为`ERROR`。记录详细的Azure Files SDK错误。 | ERROR |
| 6  | 管理项目定义导入时，从Blob下载文件或删除临时Blob失败 | 导入文件处理阶段 | `EMET0003` | **回滚并发锁**。清理部分创建的工作区。更新`Task.status`为`ERROR`。记录详细的Azure Blob SDK错误。 | ERROR |
| 7  | 向Azure Automation提交Runbook作业失败 | 作业提交阶段 | `EMET0013` | **回滚并发锁**。清理工作区。更新`Task.status`为`ERROR`。记录详细的Azure Automation API错误。 | ERROR |
| 8  | 数据库查询/更新操作失败 (通用) | 任何DB交互点 | `EMET0007` | 记录详细DB错误。若在获取锁之后、提交作业之前的关键阶段失败，需确保有补偿机制（如超时处理函数）能最终释放锁和清理资源。通常Service Bus重试有助于解决临时DB问题。 | ERROR |
| 9  | 任务记录保留策略执行中删除Blob文件失败 | 异步保留策略逻辑 | (不直接更新当前新任务的`resultMessage`，但应记录到特定日志) | 记录详细的Blob删除错误及关联的旧`taskId`。不影响新任务处理。 | WARN |
| 10 | Function执行超时 (超出`FUNCTION_TIMEOUT_SECONDS`) | Azure Functions运行时 | (不直接更新，但消息会进入`TaskInputQueue`的DLQ，由[`TaskExecuteTimeoutFunc`](./function-task-execute-timeout.md)处理并可能更新为`EMET0005`) | Azure平台记录超时。消息进入DLQ。 | ERROR |

### 3.8. 配置项 (Configuration)

#### 3.8.1. 值列表 (LOV) 定义 (源自 `docs/definitions/lov-definitions.md`)
*   `TASK_STATUS` (父级代码): 使用其下定义的内部状态码常量，如 `TASK_STATUS_QUEUED_CODE`, `TASK_STATUS_RUNBOOK_SUBMITTED_CODE`, `TASK_STATUS_COMPLETED_ERROR_CODE`。
*   `TASK_TYPE` (父级代码): 用于确定要执行的Runbook名称和参数构造逻辑。
*   `TASK_CONFIG.MAX_RETENTION_COUNT`: 用于任务记录保留策略。

#### 3.8.2. 环境变量 (源自 `docs/guides/environment-variables.md`)
*   `SERVICE_BUS_TASK_INPUT_QUEUE_NAME`: 本Function监听的队列名称。
*   `MSSQL_PRISMA_URL`: Prisma连接数据库。
*   `AZURE_STORAGE_CONNECTION_STRING`: 连接Azure Storage (Files 和 Blob)。
*   `AZURE_STORAGE_FILE_SHARE_NAME`: (假设存在，或硬编码/从其他配置获取) Azure Files共享名称。
*   `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF`: (用于导入任务) 管理项目定义导入文件的临时Blob容器名。
*   `AZURE_STORAGE_CONTAINER_OPLOGS`: (用于保留策略) 操作日志文件的Blob容器名。
*   `AZURE_AUTOMATION_ACCOUNT_NAME`: 目标Automation账户名。
*   `AZURE_AUTOMATION_RESOURCE_GROUP_NAME`: (假设存在，或硬编码/从其他配置获取) Automation账户所在的资源组名。
*   `LOG_LEVEL`: 日志级别。
*   `FUNCTION_TIMEOUT_SECONDS`: (若在`host.json`中通过此配置) 本函数的执行超时。

#### 3.8.3. 错误与提示消息 (源自 `docs/definitions/error-messages.md`)
本函数内部主要使用以下消息键来更新`Task.resultMessage`：
*   `EMET0001`: "...タスクを実行中のため実行できません..."
*   `EMET0002`: "...サポートサービスにお問い合わせください。(EMET0002)" (工作区访问失败)
*   `EMET0003`: "...サポートサービスにお問い合わせください。(EMET0003)" (产物/临时文件存储访问失败)
*   `EMET0007`: "...サポートサービスにお問い合わせください。(EMET0007)" (数据库更新错误)
*   `EMET0009`: "...サポートサービスにお問い合わせください。(EMET0009)" (配置或参数缺失/无效)
*   `EMET0013`: "...サポートサービスにお問い合わせください。(EMET0013)" (Runbook作业启动失败)

#### 3.8.4. Runbook名称与TaskType映射 (示例，应在代码中维护此映射)
| TaskType Code (来自LOV) | Runbook Script Name (.ps1) |
|---------------------------|----------------------------|
| `TASK_TYPE.OPLOG_EXPORT` | `Export-OperationLog.ps1` |
| `TASK_TYPE.MGMT_ITEM_IMPORT` | `Import-ManagementDefinition.ps1` |
| `TASK_TYPE.MGMT_ITEM_EXPORT` | `Export-ManagementDefinition.ps1` |
| ... (其他任务类型) ... | ... (对应Runbook脚本) ... |

### 3.9. 注意事项与其他 (Notes/Miscellaneous)
*   **并发锁的健壮性**: 并发锁的获取与释放是本函数最关键的部分之一。必须确保在各种错误路径下，已获取的锁都能被正确释放，以避免死锁情况。下游的超时处理函数（如[`TaskExecuteTimeoutFunc`](./function-task-execute-timeout.md) 和 [`RunbookProcessorTimeoutFunc`](./function-runbook-processor-timeout.md)）也应包含尝试释放锁的逻辑作为最后防线。
*   **Azure SDK的认证**: 与Azure Files, Blob Storage, Automation交互时，推荐使用托管身份 (Managed Identity) 进行认证，以避免在配置中存储敏感凭据。
*   **异步保留策略的监控**: 由于任务记录保留策略是异步执行的，其执行成功与否需要通过日志进行监控。如果频繁失败，可能需要人工介入检查。
*   **冷启动影响**: 作为Service Bus触发的Azure Function，首次调用或长时间不活动后再次调用可能会有冷启动延迟。对于需要快速响应的任务，可以考虑使用Azure Functions Premium Plan并配置预热实例，但这会增加成本。
