@echo off
setlocal enabledelayedexpansion

REM JCS Endpoint Monorepo セキュリティ監査スクリプト (Windows版)
REM 全プロジェクトの依存関係脆弱性をチェックし、レポートを生成する

echo === JCS Endpoint Monorepo セキュリティ監査開始 ===
echo 実行日時: %date% %time%
echo.

REM 監査結果を保存するディレクトリ
set AUDIT_DIR=security-audit-reports
for /f "tokens=1-3 delims=/ " %%a in ('date /t') do set DATESTAMP=%%c%%a%%b
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set TIMESTAMP=%%a%%b
set TIMESTAMP=%TIMESTAMP: =0%
set REPORT_DIR=%AUDIT_DIR%\%DATESTAMP%_%TIMESTAMP%

if not exist "%REPORT_DIR%" mkdir "%REPORT_DIR%"

REM 各プロジェクトの監査実行
set PROJECTS=apps\jcs-endpoint-nextjs apps\jcs-backend-services-standard apps\jcs-backend-services-long-running

echo === 依存関係脆弱性スキャン開始 ===
echo.

for %%p in (%PROJECTS%) do (
    echo --- %%p の監査実行中 ---
    
    if exist "%%p\package.json" (
        cd "%%p"
        
        REM npm auditの実行（JSONフォーマットで出力）
        echo npm audit実行中...
        npm audit --audit-level=moderate --json > "..\..\%REPORT_DIR%\%%p_audit.json" 2>nul
        
        REM 人間が読みやすい形式でも出力
        echo 詳細レポート生成中...
        npm audit --audit-level=moderate > "..\..\%REPORT_DIR%\%%p_audit.txt" 2>nul
        
        REM 高・重大な脆弱性のみを抽出
        echo 高リスク脆弱性抽出中...
        npm audit --audit-level=high --json > "..\..\%REPORT_DIR%\%%p_high_risk.json" 2>nul
        
        cd ..\..
        echo ✓ %%p 監査完了
    ) else (
        echo ⚠ %%p が見つからないか、package.jsonが存在しません
    )
    echo.
)

REM サマリーレポート生成
echo === サマリーレポート生成中 ===

set SUMMARY_FILE=%REPORT_DIR%\security_audit_summary.md

echo # JCS Endpoint Monorepo セキュリティ監査レポート > "%SUMMARY_FILE%"
echo. >> "%SUMMARY_FILE%"
echo **実行日時**: %date% %time% >> "%SUMMARY_FILE%"
echo **監査対象**: 全プロジェクト (Next.js + Azure Functions) >> "%SUMMARY_FILE%"
echo. >> "%SUMMARY_FILE%"
echo ## 監査結果サマリー >> "%SUMMARY_FILE%"
echo. >> "%SUMMARY_FILE%"

REM 各プロジェクトの結果を集計
for %%p in (%PROJECTS%) do (
    set PROJECT_NAME=%%p
    set AUDIT_FILE=%REPORT_DIR%\!PROJECT_NAME!_audit.json
    
    if exist "!AUDIT_FILE!" (
        echo ### !PROJECT_NAME! >> "%SUMMARY_FILE%"
        echo. >> "%SUMMARY_FILE%"
        echo **詳細レポート**: `!PROJECT_NAME!_audit.txt` >> "%SUMMARY_FILE%"
        echo **高リスク脆弱性**: `!PROJECT_NAME!_high_risk.json` >> "%SUMMARY_FILE%"
        echo. >> "%SUMMARY_FILE%"
    )
)

echo ## 推奨アクション >> "%SUMMARY_FILE%"
echo. >> "%SUMMARY_FILE%"
echo 1. **高・重大な脆弱性の確認**: 各プロジェクトの `*_high_risk.json` ファイルを確認 >> "%SUMMARY_FILE%"
echo 2. **依存関係の更新**: `npm update` または `npm audit fix` の実行を検討 >> "%SUMMARY_FILE%"
echo 3. **手動修正**: 自動修正できない脆弱性の手動対応 >> "%SUMMARY_FILE%"
echo 4. **定期実行**: 月次でこのスクリプトを実行 >> "%SUMMARY_FILE%"
echo. >> "%SUMMARY_FILE%"
echo ## ファイル一覧 >> "%SUMMARY_FILE%"
echo. >> "%SUMMARY_FILE%"
echo - `security_audit_summary.md`: このサマリーレポート >> "%SUMMARY_FILE%"
echo - `*_audit.txt`: 各プロジェクトの詳細監査結果 >> "%SUMMARY_FILE%"
echo - `*_audit.json`: 各プロジェクトの機械可読な監査結果 >> "%SUMMARY_FILE%"
echo - `*_high_risk.json`: 各プロジェクトの高リスク脆弱性のみ >> "%SUMMARY_FILE%"
echo. >> "%SUMMARY_FILE%"
echo --- >> "%SUMMARY_FILE%"
echo **次回実行推奨日**: 1ヶ月後 >> "%SUMMARY_FILE%"

echo ✓ サマリーレポート生成完了: %SUMMARY_FILE%
echo.

echo === セキュリティ監査完了 ===
echo 結果は以下のディレクトリに保存されました:
echo   %REPORT_DIR%\
echo.
echo サマリーレポートを確認してください:
echo   type "%SUMMARY_FILE%"
echo.
echo 高リスク脆弱性がある場合は、速やかに対応を検討してください。

pause
