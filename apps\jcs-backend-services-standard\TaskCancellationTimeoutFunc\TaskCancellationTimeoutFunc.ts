/**
 * @fileoverview タスク中止タイムアウト関数 (TaskCancellationTimeoutFunc)
 * @description
 * TaskCancellationFuncの実行が例外やタイムアウトによって3回失敗し、TaskControlQueueのDLQへ
 * 配信されたメッセージを処理する補償関数。PENDING_CANCELLATION状態のタスクを
 * COMPLETED_ERROR状態に更新し、タスク中止タイムアウトエラーを設定する。
 *
 * @trigger Azure Service Bus - TaskControlQueue のDLQメッセージ
 * @input TaskControlQueue のDLQから受信する、元のタスク中止要求メッセージ
 * @output Azure SQL Database (Task) のレコード更新
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */
import { prisma } from "../lib/prisma";
import { AppConstants } from "../lib/constants";
import { formatTaskErrorMessage } from "../lib/utils";
import { app, InvocationContext } from "@azure/functions";

/**
 * TaskCancellationTimeoutFunc - タスク中止タイムアウト時の補償処理
 *
 * 処理ステップ:
 * 1. TaskControlQueue DLQメッセージ受信・解析、taskId検証
 * 2. タスク情報取得・ステータス確認（PENDING_CANCELLATION）
 * 3. 楽観ロック制御によるタスクステータス更新（COMPLETED_ERROR/EMET0006）
 *
 * エラー処理:
 * taskId不正・タスク存在なし・ステータス不正・DB更新失敗時は例外throw（リトライ）。
 */
export async function TaskCancellationTimeoutFunc(message: unknown, context: InvocationContext): Promise<void> {
  let taskId: string | undefined;
  let task: any = null;

  try {
    context.log("[TaskCancellationTimeoutFunc] TaskControlQueue/$DeadLetterQueue からメッセージ受信");

    // メッセージ基本校验
    if (!message || typeof message !== 'object') {
      context.error("[TaskCancellationTimeoutFunc] メッセージが不正です。処理を終了します。");
      return;
    }

    // 1. メッセージ解析・taskId取得
    const messageBody = message as { taskId?: string };
    const extractedTaskId = messageBody.taskId;

    if (!extractedTaskId || typeof extractedTaskId !== "string") {
      context.error("[TaskCancellationTimeoutFunc] taskIdが不正です。処理を終了します。");
      return;
    }

    taskId = extractedTaskId;
    context.log(`[TaskCancellationTimeoutFunc] 受信taskId: ${taskId}`);

    // 2. データベースからタスク情報取得
    task = await prisma.task.findUnique({
      where: { id: taskId },
      select: { status: true, updatedAt: true }
    });
    if (!task) {
      context.error(`[TaskCancellationTimeoutFunc] タスクID ${taskId} がデータベースに存在しない。処理を終了する。`);
      return;
    }

    // 3. タスク状態確認（PENDING_CANCELLATIONのみ処理）
    if (task.status !== AppConstants.TaskStatus.PendingCancellation) {
      context.error(`[TaskCancellationTimeoutFunc] タスクID ${taskId} のステータスが PENDING_CANCELLATION ではない (現在: ${task.status})。処理を終了する。`);
      return;
    }

    // 4. PENDING_CANCELLATION → COMPLETED_ERROR へ更新（楽観ロック制御）
    const updateResult = await prisma.task.updateMany({
      where: {
        id: taskId,
        updatedAt: task.updatedAt
      },
      data: {
        status: AppConstants.TaskStatus.CompletedError,
        resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0006),
        errorCode: AppConstants.ERROR_CODES.EMET0006,
      },
    });

    if (updateResult.count === 0) {
      context.error(`[TaskCancellationTimeoutFunc] タスクID ${taskId} のデータベース更新失敗または更新件数が0件。処理を終了する。`);
      return;
    }

    context.log(`[TaskCancellationTimeoutFunc] タスクID ${taskId} をCOMPLETED_ERROR/EMET0006で更新完了。`);
  } catch (err: any) {
    context.error(`[TaskCancellationTimeoutFunc] タスクID ${taskId} の補償処理中にエラー発生:`, err);

    return;
  }
}

// FunctionとService Busキューのバインド設定
app.serviceBusQueue("TaskCancellationTimeoutFunc", {
  connection: "AZURE_SERVICEBUS_NAMESPACE_HOSTNAME",
  queueName: "%SERVICE_BUS_TASK_CONTROL_QUEUE_NAME%/$DeadLetterQueue",
  handler: TaskCancellationTimeoutFunc,
}); 