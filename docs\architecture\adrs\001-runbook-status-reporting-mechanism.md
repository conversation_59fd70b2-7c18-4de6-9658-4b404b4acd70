# ADR-001: Runbook结果上报机制的选择

**状态 (Status)**: 已接受 (Accepted)

**决策者 (Deciders)**: chen

**决策日期 (Date)**: 2025-06-04

## 上下文 (Context)

在JCS端点资产与任务管理系统中，后台任务的核心业务逻辑由在目标VM的Hybrid Runbook Worker (HRW) 上执行的PowerShell Runbook实现。Runbook执行完毕后（无论成功或失败），其最终状态和结果（如成功时产生的输出文件路径、失败时的错误信息）需要被系统核心后端（Azure Functions）感知，以便进行后续处理，例如更新数据库中的任务状态、将结果文件归档到Azure Blob Storage、释放并发锁、清理临时工作区等。

原始设想或功能规格书 (`fs.v1.md`) 中描述的机制是Runbook在执行完毕后，主动将其状态和结果信息通过VM的托管身份发送到Azure Service Bus的`TaskStatusQueue`队列。

然而，在技术评估和实践中发现，直接在PowerShell Runbook中使用标准的`Az.ServiceBus`模块来可靠、便捷地通过托管身份向Azure Service Bus发送数据平面消息存在挑战：
*   `Az`模块主要面向Azure资源管理（控制平面），对数据平面操作的支持不如专门的SDK。
*   使用托管身份进行Service Bus数据平面认证的便捷性在`Az.ServiceBus` cmdlet中可能不足。
*   依赖连接字符串会引入安全风险和管理复杂性。
*   直接调用Service Bus REST API需要在PowerShell中手动处理Token获取、请求构造和错误处理，显著增加了Runbook脚本的复杂性和脆弱性。

因此，需要选择一种更健壮、可靠且易于维护的机制，让系统能够获取Runbook的执行结果。

## 决策 (Decision)

我们决定**不采用Runbook主动上报其执行结果到Service Bus队列的方案**。

取而代之，我们将采用一种**基于外部定时轮询的机制**来获取Azure Automation作业的状态和结果。具体实现为：

1.  **扩展现有（或FS中定义的）`RunbookMonitorFunc` (Azure Function, Timer Trigger) 的职责。**
2.  此`RunbookMonitorFunc`将：
    *   **继续负责监控**长时间运行（状态为`RUNBOOK_SUBMITTED`）的Azure Automation作业，若超过预设的超时阈值 (`RUNBOOK_TIMEOUT_SECONDS`)，则尝试停止该作业。
    *   **新增职责：定期轮询**所有已提交到Azure Automation且状态为`RUNBOOK_SUBMITTED`的作业，以获取其最终执行状态（例如，`Completed`, `Failed`, `Stopped`）。
    *   当轮询到作业已结束（无论是正常完成、失败、还是被此函数因超时而中止）时，`RunbookMonitorFunc`将负责从Azure Automation作业的输出流或错误流中提取原始结果信息（如工作区文件路径、错误摘要等）。
    *   然后，`RunbookMonitorFunc`将构造一个包含`taskId`、`automationJobId`、原始Automation层面作业状态以及这些原始结果信息的消息，将此消息**发送到Azure Service Bus的`TaskStatusQueue`**。
    *   同时，`RunbookMonitorFunc`将更新对应`Task`记录在数据库中的内部状态为`PROCESSING_COMPLETE`，表示Azure Automation层面的执行已结束，其结果已被捕获并等待后续处理。
3.  后续的`StatusProcessorFunc`将监听`TaskStatusQueue`，接收由`RunbookMonitorFunc`转发过来的原始结果消息，并执行统一的后处理操作（文件归档、工作区清理、并发锁释放、更新`Task`表为最终的`COMPLETED_SUCCESS`或`COMPLETED_ERROR`状态）。

## 后果 (Consequences)

### 正面影响:

*   **Runbook脚本显著简化**: PowerShell Runbook不再需要包含任何与Service Bus消息发送相关的复杂逻辑（如获取Token、构造消息、处理发送错误和重试）。它们只需专注于执行核心业务，并通过标准输出/错误流报告结果。这降低了Runbook的开发和维护难度，提高了其健壮性。
*   **对HRW环境依赖降低**: 无需在HRW环境中配置额外的SDK或确保特定PowerShell模块（用于Service Bus数据平面操作）的可用性和兼容性。
*   **集中化的结果获取与初步处理**: Runbook作业状态的获取和原始结果的提取逻辑集中在`RunbookMonitorFunc`中，便于管理和调试。
*   **错误处理的统一**: 所有类型的作业结束事件（成功、失败、超时）都通过`RunbookMonitorFunc`汇聚，并以统一的格式发送到`TaskStatusQueue`，简化了`StatusProcessorFunc`对不同结束场景的处理。
*   **提高了系统的整体可靠性**: 如果Runbook执行节点（VM）在尝试上报时遇到网络问题或自身故障，可能会导致状态丢失。外部轮询机制（只要Automation服务本身可靠）更能保证最终状态能被系统捕获。

### 负面影响与风险:

*   **任务结果感知的延迟**: 由于采用轮询机制，系统感知到Runbook作业完成并开始后续处理的时间，将取决于`RunbookMonitorFunc`的轮询间隔 (`RUNBOOK_MONITOR_INTERVAL_SECONDS`)。如果间隔较长，用户可能会感觉到任务完成后状态更新的延迟。
*   **增加了对Azure Automation API的调用频率**: `RunbookMonitorFunc`需要定期查询所有相关作业的状态。如果并发运行的任务数量非常大，或者轮询间隔非常短，可能会对Azure Automation服务的API限流产生压力（尽管在合理范围内通常可控）。
*   **`RunbookMonitorFunc`的职责更重**: 该Function现在需要处理更多逻辑（超时监控、状态轮询、结果提取、消息发送、数据库状态更新）。其自身的健壮性、错误处理和执行时间管理变得更加重要。
*   **`Task`表状态的短暂不一致性**: 在`RunbookMonitorFunc`轮询到作业结束、发送消息到队列、并更新`Task`状态为`PROCESSING_COMPLETE`与`StatusProcessorFunc`实际处理完消息并更新为最终状态之间，存在一个时间窗口。在此期间，任务的内部状态与其实际的最终业务状态（例如，文件是否已归档）可能不完全同步。但这是异步处理的常见特性。
*   **与FS (`fs.v1.md`) 的偏差**: `fs.v1.md`中描述Runbook主动上报结果。此决策意味着架构设计（SAD）将与FS在该具体实现机制上存在差异。这需要在项目内部明确沟通和记录，确保所有相关方理解此技术决策的理由和影响。

## 备选方案 (Considered Options)

1.  **Runbook直接调用Service Bus REST API (使用托管身份)**:
    *   **优点**: Runbook主动上报，结果实时性可能更高。
    *   **缺点**: PowerShell中手动实现获取Azure AD Token、构造HTTP请求（包括认证头）、处理Service Bus特定的消息属性和错误响应等逻辑非常复杂，易出错，且难以维护。显著增加了Runbook脚本的复杂性。

2.  **Runbook调用一个HTTP Relay Azure Function**:
    *   **优点**: Runbook只需发起一个简单的HTTP请求。消息发送到Service Bus的复杂性由Relay Function（可以使用更适合的语言和SDK，如Node.js或C#）封装。可以利用VM托管身份进行Function的认证。
    *   **缺点**: 增加了一个额外的Azure Function组件及其维护成本。Runbook仍需包含HTTP调用逻辑。需要确保Relay Function的高可用性和安全性。
    *   **备注**: 此方案在技术上是可行的，并且比直接调用REST API简单，但轮询方案在进一步简化Runbook和HRW环境依赖方面有优势。

3.  **Runbook将结果写入共享存储 (如Azure Files)，由外部服务读取**:
    *   **优点**: Runbook只需写文件。
    *   **缺点**: 需要一个机制来通知外部服务何时去读取文件。可能依赖于文件系统事件（复杂且不可靠）或额外的信号机制。文件锁定和并发读写也可能成为问题。轮询Azure Automation API通常更直接。

**最终选择轮询方案，是基于对Runbook简单性、HRW环境依赖最小化、以及集中化结果管理的权衡。**

## 更多信息 (More Information)

*   相关讨论和背景请参考项目架构设计会议记录 [链接到相关会议纪要或讨论记录 - 如果有的话]。
*   `RunbookMonitorFunc` 和 `StatusProcessorFunc` 的详细设计在其各自的组件设计文档中描述。

---
