# 测试问题自主解决指南

## 🎯 目标
建立标准化流程，让AI能够自主诊断和解决常见的Playwright测试问题，无需人工干预。

## 📋 标准解决流程

### 1. 元素找不到问题

#### 症状识别
- `TimeoutError: locator.click: Timeout 10000ms exceeded`
- `waiting for getByRole('xxx')`
- `waiting for locator('xxx')`

#### 自动解决步骤
1. **使用ElementDebugger**：
   ```typescript
   const debugger = createElementDebugger(page);
   await debugger.getPageDebugInfo();
   const element = await debugger.findSelectElement('每页显示数量选择器');
   ```

2. **多选择器策略**：
   - 通用选择器 (`select`, `button`, `input`)
   - 属性选择器 (`[role="xxx"]`, `[data-testid="xxx"]`)
   - 文本选择器 (`text="xxx"`, `:has-text("xxx")`)
   - CSS类选择器 (`[class*="xxx"]`)

3. **如果仍然找不到**：
   - 检查页面是否正确加载
   - 添加网络拦截器
   - 增加等待时间
   - 检查元素是否在iframe中

### 2. 认证问题

#### 症状识别
- 页面重定向到登录页
- 401/403错误
- RefreshToken相关错误

#### 自动解决步骤
1. **添加网络拦截器**：
   ```typescript
   await interceptAuthRequests(page);
   ```

2. **确保正确登录**：
   ```typescript
   await loginAs(page, { role: UserRole.ADMIN });
   ```

### 3. 页面加载问题

#### 症状识别
- 页面内容为空
- 表格不存在
- React错误

#### 自动解决步骤
1. **标准等待序列**：
   ```typescript
   await page.waitForLoadState('networkidle');
   await page.waitForTimeout(2000); // React hydration
   await page.waitForSelector('table', { timeout: 10000 });
   ```

2. **错误处理**：
   ```typescript
   try {
     await page.waitForSelector('table', { timeout: 10000 });
   } catch (error) {
     console.log('页面可能有问题，跳过测试');
     return;
   }
   ```

### 4. 断言失败问题

#### 症状识别
- `expect(received).toBe(expected)`
- `expect(received).toContain(expected)`

#### 自动解决步骤
1. **使用灵活断言**：
   ```typescript
   // 不要假设固定值
   expect(rows).toBeGreaterThan(0);
   
   // 检查功能是否存在
   if (url.includes('sort=')) {
     expect(url).toContain('sort=');
   } else {
     console.log('功能可能还未实现');
   }
   ```

2. **添加调试信息**：
   ```typescript
   console.log('实际值:', actualValue);
   console.log('期望值:', expectedValue);
   ```

## 🔧 自动修复模板

### 通用测试修复模板
```typescript
test('测试名称', async ({ page }) => {
  // 1. 设置网络拦截器
  await interceptAuthRequests(page);
  
  // 2. 创建测试数据
  await createTestData();
  
  // 3. 登录
  await loginAs(page, { role: UserRole.ADMIN });
  
  // 4. 访问页面
  await page.goto('/target-page');
  
  // 5. 等待页面加载
  const debugger = createElementDebugger(page);
  await debugger.waitForPageReady();
  await debugger.getPageDebugInfo();
  
  // 6. 查找元素（使用调试器）
  const element = await debugger.findSelectElement('目标元素');
  if (!element) {
    console.log('元素不存在，跳过测试');
    return;
  }
  
  // 7. 执行操作
  try {
    await element.click();
  } catch (error) {
    console.log('操作失败，尝试其他方法');
    // 备用方案
  }
  
  // 8. 灵活验证
  const result = await getResult();
  if (result.success) {
    expect(result.value).toBeTruthy();
  } else {
    console.log('功能可能还未实现');
  }
});
```

## 📝 问题诊断检查清单

遇到测试失败时，按以下顺序检查：

- [ ] 是否添加了网络拦截器？
- [ ] 是否正确等待页面加载？
- [ ] 元素选择器是否正确？
- [ ] 是否有React hydration问题？
- [ ] 断言是否过于严格？
- [ ] 是否需要错误处理？
- [ ] 测试数据是否正确创建？
- [ ] 是否有权限问题？

## 🔧 实际案例：搜索按钮超时问题

### 问题现象
- **第一次运行**：`waitForFunction` 超时失败
- **第二次运行**：通过
- **原因**：点击搜索按钮后，URL变化的时机不稳定

### 根本原因分析
1. **开发环境 vs 测试环境差异**：
   - 开发环境：人工操作有自然延迟，给React足够处理时间
   - 测试环境：Playwright执行极快，可能在React状态更新前就等待URL变化

2. **时机竞争问题**：
   - React状态更新、网络请求、URL变化的时机不确定
   - `waitForFunction` 依赖特定的URL模式，容易超时

### 修复策略
```typescript
// ❌ 不稳定的方法
await page.waitForFunction(
  (term) => window.location.href.includes(`filter=${term}`),
  searchTerm,
  { timeout: 10000 }
);

// ✅ 稳定的方法
await page.waitForTimeout(1000); // 给React足够时间处理状态更新
await page.waitForLoadState('networkidle'); // 等待网络请求完成
console.log('✅ 搜索请求已处理完成');
```

### 核心原则
1. **避免依赖时机敏感的条件**（如URL变化）
2. **使用稳定的等待策略**（固定延迟 + networkidle）
3. **关注功能结果而非实现细节**
4. **提供详细的调试信息**

## 🎯 目标结果

通过这套流程，AI应该能够：
1. 自动识别常见测试问题
2. 应用标准化解决方案
3. 提供详细的调试信息
4. 优雅地处理功能未实现的情况
5. 减少对人工干预的依赖
6. 解决时机竞争和不稳定性问题
