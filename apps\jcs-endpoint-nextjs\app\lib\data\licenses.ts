/**
 * @fileoverview ライセンス関連データ操作モジュール
 * @description ライセンス情報の取得、検証等のデータアクセスロジックを提供する
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import prisma from "@/app/lib/prisma";
import { LogFunctionSignature } from "../logger";
import { handleServerError } from "../portal-error";

/**
 * ライセンス関連のデータ操作を行う静的クラス
 * ライセンス情報の取得、検証等を担当する
 */
export class ServerDataLicenses {
  /**
   * 指定されたライセンスIDでライセンス情報を検証し取得する
   * 
   * @param {string} licenseId - 検証対象のライセンスID
   * @returns {Promise<any | null>} ライセンス情報、見つからない場合はnull
   * @throws データベースアクセスエラー時はhandleServerErrorに委譲される
   */
  @LogFunctionSignature()
  static async checkLicense(licenseId: string) {
    try {
      const license = await prisma.license.findUnique({
        where: { licenseId },
        select: {
          id: true,
          licenseId: true,
          type: true,
          expiredAt: true,
          maxClients: true,
          isMaintenance: true,
          isDisabled: true,
          basicPlan: true,
        },
      });
      return license;
    } catch (error) {
      handleServerError(error);
    }
  }
}
