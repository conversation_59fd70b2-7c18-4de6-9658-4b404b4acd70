/**
 * @fileoverview プランデータ生成器
 * @description 開発環境用の大量のプランデータと関連データを生成する
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { PrismaClient } from '@prisma/client';
import { BaseGenerator } from './base.generator';

/**
 * プランデータ生成器クラス
 * 様々なプランとその関連データを大量に生成する
 */
export class PlanGenerator extends BaseGenerator {
  private readonly PLAN_COUNT = 20; // 生成するプラン数
  private readonly BATCH_SIZE = 5;

  constructor(prisma: PrismaClient) {
    super(prisma, 'PlanGenerator');
  }

  /**
   * 生成器名を取得する
   * @returns 生成器名
   */
  getGeneratorName(): string {
    return 'プランデータ';
  }

  /**
   * 生成予定のデータ数を取得する
   * @returns 生成予定数
   */
  getExpectedCount(): number {
    return this.PLAN_COUNT;
  }

  /**
   * 既存のプランデータをクリーンアップする
   * 関連する全ての関連テーブルも削除する
   */
  async cleanup(): Promise<void> {
    try {
      // 関連テーブルを順序立てて削除
      await this.prisma.planSupport.deleteMany({});
      await this.prisma.planProvidedFile.deleteMany({});
      await this.prisma.planManual.deleteMany({});
      await this.prisma.planProduct.deleteMany({});
      await this.prisma.licensePlan.deleteMany({});
      await this.prisma.notification.updateMany({
        where: { planId: { not: null } },
        data: { planId: null },
      });
      
      // プランデータを削除
      const deleteResult = await this.prisma.plan.deleteMany({});
      console.log(`既存プランデータを削除しました (${deleteResult.count}件)`);
    } catch (error) {
      console.error('プランデータのクリーンアップに失敗しました', error);
      throw error;
    }
  }

  /**
   * プランデータを生成する
   * プランとその関連データを大量に生成する
   * @returns 生成されたデータの数
   */
  async generate(): Promise<number> {
    try {
      // 基本プランを生成
      const planCount = await this.generatePlans();
      
      // 関連データを生成
      await this.generatePlanRelations();
      
      return planCount;
    } catch (error) {
      console.error('プランデータ生成中にエラーが発生しました', error);
      throw error;
    }
  }

  /**
   * プランデータを生成する
   * @returns 生成されたプラン数
   */
  private async generatePlans(): Promise<number> {
    return await this.generateInBatches(
      this.PLAN_COUNT,
      this.BATCH_SIZE,
      async (startIndex: number, count: number) => {
        return await this.generatePlanBatch(startIndex, count);
      }
    );
  }

  /**
   * プランデータのバッチを生成する
   * @param startIndex 開始インデックス
   * @param count 生成数
   * @returns 生成されたプランデータ
   */
  private async generatePlanBatch(
    startIndex: number,
    count: number
  ): Promise<any[]> {
    const plans = [];

    for (let i = 0; i < count; i++) {
      const planIndex = startIndex + i + 1;
      const planInfo = this.generatePlanInfo(planIndex);
      
      const plan = {
        name: planInfo.name,
        planId: planInfo.planId,
      };

      plans.push(plan);
    }

    // バッチでデータベースに挿入
    await this.prisma.plan.createMany({
      data: plans,
    });

    return plans;
  }

  /**
   * プラン情報を生成する
   * @param index プランインデックス
   * @returns プラン情報
   */
  private generatePlanInfo(index: number): {
    name: string;
    planId: string;
  } {
    const planTypes = [
      { name: 'LIGHT_A プラン', id: 'LIGHT_A' },
      { name: 'LIGHT_B プラン', id: 'LIGHT_B' },
      { name: 'STANDARD プラン', id: 'STANDARD' },
      { name: 'PREMIUM プラン', id: 'PREMIUM' },
      { name: 'ENTERPRISE プラン', id: 'ENTERPRISE' },
      { name: 'BASIC プラン', id: 'BASIC' },
      { name: 'PROFESSIONAL プラン', id: 'PROFESSIONAL' },
      { name: 'ADVANCED プラン', id: 'ADVANCED' },
      { name: 'ULTIMATE プラン', id: 'ULTIMATE' },
      { name: 'STARTER プラン', id: 'STARTER' }
    ];

    const planTypeIndex = index % planTypes.length;
    const versionIndex = Math.floor(index / planTypes.length) + 1;
    const planType = planTypes[planTypeIndex];

    return {
      name: `${planType.name} v${versionIndex}`,
      planId: `${planType.id}_V${versionIndex}_${index.toString().padStart(3, '0')}`,
    };
  }

  /**
   * プラン関連データを生成する
   */
  private async generatePlanRelations(): Promise<void> {
    const plans = await this.prisma.plan.findMany();
    const productMedias = await this.prisma.productMedia.findMany();
    const productManuals = await this.prisma.productManual.findMany();
    const providedFiles = await this.prisma.providedFile.findMany();
    const supportFiles = await this.prisma.supportFile.findMany();
    const licenses = await this.prisma.license.findMany();

    console.log('プラン関連データの生成を開始します');

    for (const plan of plans) {
      // プラン製品関連を生成
      if (productMedias.length > 0) {
        await this.generatePlanProducts(plan.planId, productMedias);
      }

      // プランマニュアル関連を生成
      if (productManuals.length > 0) {
        await this.generatePlanManuals(plan.planId, productManuals);
      }

      // プラン提供ファイル関連を生成
      if (providedFiles.length > 0) {
        await this.generatePlanProvidedFiles(plan.planId, providedFiles);
      }

      // プランサポート関連を生成
      if (supportFiles.length > 0) {
        await this.generatePlanSupports(plan.planId, supportFiles);
      }

      // ライセンスプラン関連を生成
      if (licenses.length > 0) {
        await this.generateLicensePlans(plan.planId, licenses);
      }
    }

    console.log('プラン関連データの生成が完了しました');
  }

  /**
   * プラン製品関連を生成する
   * @param planId プランID
   * @param productMedias 製品媒体リスト
   */
  private async generatePlanProducts(planId: string, productMedias: any[]): Promise<void> {
    const selectedProducts = this.shuffleArray(productMedias).slice(0, this.faker.randomInt(3, 8));
    
    const planProducts = selectedProducts.map(product => ({
      planId,
      productCode: product.productCode,
      version: product.version,
    }));

    await this.prisma.planProduct.createMany({
      data: planProducts,
    });
  }

  /**
   * プランマニュアル関連を生成する
   * @param planId プランID
   * @param productManuals 製品マニュアルリスト
   */
  private async generatePlanManuals(planId: string, productManuals: any[]): Promise<void> {
    const selectedManuals = this.shuffleArray(productManuals).slice(0, this.faker.randomInt(2, 6));
    
    const planManuals = selectedManuals.map(manual => ({
      planId,
      serialNo: manual.serialNo,
    }));

    await this.prisma.planManual.createMany({
      data: planManuals,
    });
  }

  /**
   * プラン提供ファイル関連を生成する
   * @param planId プランID
   * @param providedFiles 提供ファイルリスト
   */
  private async generatePlanProvidedFiles(planId: string, providedFiles: any[]): Promise<void> {
    const selectedFiles = this.shuffleArray(providedFiles).slice(0, this.faker.randomInt(2, 5));
    
    const planProvidedFiles = selectedFiles.map(file => ({
      planId,
      name: file.name,
    }));

    await this.prisma.planProvidedFile.createMany({
      data: planProvidedFiles,
    });
  }

  /**
   * プランサポート関連を生成する
   * @param planId プランID
   * @param supportFiles サポートファイルリスト
   */
  private async generatePlanSupports(planId: string, supportFiles: any[]): Promise<void> {
    const selectedSupports = this.shuffleArray(supportFiles).slice(0, this.faker.randomInt(3, 7));

    const planSupports = selectedSupports.map(support => ({
      planId,
      serialNo: support.serialNo,
    }));

    await this.prisma.planSupport.createMany({
      data: planSupports,
    });
  }

  /**
   * ライセンスプラン関連を生成する
   * @param planId プランID
   * @param licenses ライセンスリスト
   */
  private async generateLicensePlans(planId: string, licenses: any[]): Promise<void> {
    // 各プランに2-4個のライセンスを関連付ける
    const selectedLicenses = this.shuffleArray(licenses).slice(0, this.faker.randomInt(2, 5));

    const licensePlans = selectedLicenses.map(license => ({
      licenseId: license.licenseId,
      planId,
    }));

    await this.prisma.licensePlan.createMany({
      data: licensePlans,
    });
  }

  /**
   * プランの統計情報を取得する
   * @returns 統計情報
   */
  async getStatistics(): Promise<{
    totalPlans: number;
    totalPlanProducts: number;
    totalPlanManuals: number;
    totalPlanProvidedFiles: number;
    totalPlanSupports: number;
    totalLicensePlans: number;
    planWithMostProducts: any;
    planWithMostManuals: any;
  }> {
    const [
      totalPlans,
      totalPlanProducts,
      totalPlanManuals,
      totalPlanProvidedFiles,
      totalPlanSupports,
      totalLicensePlans,
      planProductCounts,
      planManualCounts,
    ] = await Promise.all([
      this.prisma.plan.count(),
      this.prisma.planProduct.count(),
      this.prisma.planManual.count(),
      this.prisma.planProvidedFile.count(),
      this.prisma.planSupport.count(),
      this.prisma.licensePlan.count(),
      this.prisma.planProduct.groupBy({
        by: ['planId'],
        _count: { planId: true },
        orderBy: { _count: { planId: 'desc' } },
        take: 1,
      }),
      this.prisma.planManual.groupBy({
        by: ['planId'],
        _count: { planId: true },
        orderBy: { _count: { planId: 'desc' } },
        take: 1,
      }),
    ]);

    const planWithMostProducts = planProductCounts.length > 0
      ? await this.prisma.plan.findUnique({
          where: { planId: planProductCounts[0].planId },
          select: { name: true, planId: true },
        })
      : null;

    const planWithMostManuals = planManualCounts.length > 0
      ? await this.prisma.plan.findUnique({
          where: { planId: planManualCounts[0].planId },
          select: { name: true, planId: true },
        })
      : null;

    return {
      totalPlans,
      totalPlanProducts,
      totalPlanManuals,
      totalPlanProvidedFiles,
      totalPlanSupports,
      totalLicensePlans,
      planWithMostProducts,
      planWithMostManuals,
    };
  }

  /**
   * 配列からランダムな要素を選択する
   * @param array 選択元の配列
   * @returns ランダムに選択された要素
   */
  private randomFromArray<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }
}
