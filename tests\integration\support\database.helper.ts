import { PrismaClient } from '@prisma/client';

/**
 * 🗄️ 数据库测试辅助器 - 业界最佳实践
 * 
 * 功能：
 * - 数据库连接管理
 * - 测试数据创建和清理
 * - 事务管理
 * - 数据验证
 * - 性能监控
 */
export class DatabaseHelper {
  private prisma: PrismaClient;
  private isConnected = false;

  constructor() {
    this.prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.MSSQL_PRISMA_URL,
        },
      },
      log: ['error', 'warn'],
    });
  }

  /**
   * 🔌 连接数据库
   */
  async connect(): Promise<void> {
    if (!this.isConnected) {
      await this.prisma.$connect();
      this.isConnected = true;
      console.log('🔌 数据库连接已建立');
    }
  }

  /**
   * 🔌 断开数据库连接
   */
  async disconnect(): Promise<void> {
    if (this.isConnected) {
      await this.prisma.$disconnect();
      this.isConnected = false;
      console.log('🔌 数据库连接已断开');
    }
  }

  /**
   * 🧹 清理测试数据
   */
  async cleanTestData(): Promise<void> {
    console.log('🧹 清理测试数据...');
    
    try {
      // 使用事务确保数据一致性
      await this.prisma.$transaction(async (tx) => {
        // 按照外键依赖顺序删除数据
        // 注意：根据你的实际数据模型调整删除顺序
        
        // 示例：删除测试相关的数据
        // await tx.taskExecution.deleteMany({
        //   where: { createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } }
        // });
        
        // await tx.managementItem.deleteMany({
        //   where: { name: { startsWith: 'test-' } }
        // });
        
        console.log('✅ 测试数据清理完成');
      });
    } catch (error) {
      console.error('❌ 测试数据清理失败:', error);
      throw error;
    }
  }

  /**
   * 📊 创建测试数据
   */
  async createTestData(): Promise<TestDataSet> {
    console.log('📊 创建测试数据...');
    
    try {
      const testData = await this.prisma.$transaction(async (tx) => {
        // 创建测试用户
        const testUser = await tx.user.create({
          data: {
            email: '<EMAIL>',
            name: 'Test User',
            role: 'USER',
          },
        });

        // 创建测试管理项目
        const testManagementItem = await tx.managementItem.create({
          data: {
            name: 'test-management-item',
            description: 'Test Management Item for E2E Testing',
            status: 'ACTIVE',
            createdBy: testUser.id,
          },
        });

        return {
          user: testUser,
          managementItem: testManagementItem,
        };
      });

      console.log('✅ 测试数据创建完成');
      return testData;
    } catch (error) {
      console.error('❌ 测试数据创建失败:', error);
      throw error;
    }
  }

  /**
   * 🔍 验证数据状态
   */
  async verifyDataState(expectedState: Partial<DataState>): Promise<boolean> {
    try {
      const actualState: DataState = {
        userCount: await this.prisma.user.count(),
        managementItemCount: await this.prisma.managementItem.count(),
        taskExecutionCount: await this.prisma.taskExecution.count(),
      };

      // 验证每个字段
      for (const [key, expectedValue] of Object.entries(expectedState)) {
        const actualValue = actualState[key as keyof DataState];
        if (actualValue !== expectedValue) {
          console.warn(`❌ 数据状态不匹配: ${key} 期望 ${expectedValue}, 实际 ${actualValue}`);
          return false;
        }
      }

      console.log('✅ 数据状态验证通过');
      return true;
    } catch (error) {
      console.error('❌ 数据状态验证失败:', error);
      return false;
    }
  }

  /**
   * 📈 获取数据库性能指标
   */
  async getPerformanceMetrics(): Promise<DatabaseMetrics> {
    try {
      const startTime = Date.now();
      
      // 执行一个简单的查询来测试响应时间
      await this.prisma.$queryRaw`SELECT 1`;
      
      const responseTime = Date.now() - startTime;

      // 获取连接池状态（如果支持）
      const metrics: DatabaseMetrics = {
        responseTime,
        connectionCount: 1, // Prisma 不直接暴露连接池信息
        timestamp: new Date().toISOString(),
      };

      return metrics;
    } catch (error) {
      console.error('❌ 获取数据库性能指标失败:', error);
      throw error;
    }
  }

  /**
   * 🔄 执行原始 SQL 查询
   */
  async executeRawQuery(query: string, params: any[] = []): Promise<any> {
    try {
      return await this.prisma.$queryRawUnsafe(query, ...params);
    } catch (error) {
      console.error('❌ 原始查询执行失败:', error);
      throw error;
    }
  }

  /**
   * 🎯 等待数据库状态
   */
  async waitForDatabaseState(
    condition: () => Promise<boolean>,
    timeout = 30000,
    interval = 1000
  ): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      if (await condition()) {
        return;
      }
      
      await new Promise(resolve => setTimeout(resolve, interval));
    }
    
    throw new Error(`数据库状态等待超时 (${timeout}ms)`);
  }

  /**
   * 📊 获取 Prisma 客户端（用于高级操作）
   */
  getPrismaClient(): PrismaClient {
    return this.prisma;
  }
}

// 类型定义
export interface TestDataSet {
  user: any; // 根据你的 Prisma 模型调整类型
  managementItem: any;
}

export interface DataState {
  userCount: number;
  managementItemCount: number;
  taskExecutionCount: number;
}

export interface DatabaseMetrics {
  responseTime: number;
  connectionCount: number;
  timestamp: string;
}
