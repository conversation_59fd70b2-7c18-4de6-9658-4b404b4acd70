# 组件：登出 (Logout)

## 1. 概要 (Overview)

### 1.1 目的 (Purpose)
本组件为“JCS 端点资产与任务管理系统”的已登录用户提供一个安全退出系统的标准操作方式。其核心目的是通过明确的登出流程，及时终止用户当前的活动会话，清除相关的认证凭据（如浏览器端的会话Cookie），并将用户导航回登录界面，从而防止在共享或不安全环境下因会话未结束而导致未授权访问或信息泄露。

### 1.2 用户故事/需求 (User Stories/Requirements)
- 作为一名已登录的用户，我希望能够在我完成工作或需要离开时，通过点击一个明确的“登出”按钮来安全地退出门户系统。
- 作为一名已登录的用户，我希望在点击“登出”按钮后，系统能给我一个确认的机会，以防止我因误操作而意外退出。
- 作为一名已登录的用户，我希望在确认登出后，我的当前会话立即失效，并且系统能自动将我导航回登录页面。
- 作为一名系统管理员，我希望用户的登出操作能够被记录到审计日志中，以便追踪用户活动。

### 1.3 高层依赖与交互 (High-Level Dependencies & Interactions)
- **用户认证与会话管理 (Keycloak & Next.js)**:
    - 登出操作需要使门户应用（Next.js）自身建立的用户会话失效（例如，清除会话Cookie）。
    - 同时，为了实现单点登出 (Single Sign-Out, SLO)，理想情况下也应通知Keycloak使IdP端的会话失效。这通常通过重定向到Keycloak的登出端点，并携带必要的参数（如 `id_token_hint` 和 `post_logout_redirect_uri`）来实现。
- **主界面 (Main Screen)**: 登出功能的入口点通常位于[主界面](./02-main-screen.md)的全局导航栏上。
- **登录界面 (Login)**: 用户成功登出后，系统应将用户重定向到[登录界面](./01-login.md)。
- **审计日志 (Audit Logging)**: 登出事件应被记录到门户的 `AuditLogin` 表或专门的审计日志中。

## 2. 功能规格 (Functional Specifications)

### 2.1 主要流程 (High-Level Flow)
1.  用户在门户系统的导航栏（通常位于主界面）点击“ログアウト (登出)”按钮。
2.  系统弹出一个确认对话框，询问用户是否确定要登出（例如，“本当にログアウトしますか？(确定要登出吗？)”）。对话框包含“OK (是/确定)”和“キャンセル (否/取消)”按钮，以及一个关闭图标“X”。
3.  用户进行选择：
    *   **用户点击“OK”**:
        a.  门户应用后端API被调用，执行登出逻辑：
            i.  清除服务器端的用户会话信息。
            ii. 清除浏览器端的会话Cookie。
            iii.记录登出事件到审计日志。
        b.  前端被重定向到Keycloak的登出端点（如果配置了SLO），以使Keycloak会话失效。
        c.  Keycloak处理完登出后，将用户重定向回门户应用指定的登出后跳转URL（通常是登录页面）。
        d.  用户最终看到登录页面。
    *   **用户点击“キャンセル”或“X”**:
        a.  确认对话框关闭。
        b.  用户保持在当前页面，会话继续有效。

```mermaid
sequenceDiagram
    participant User as 👤 用户 (Browser)
    participant MainScreen as 🖥️ 主界面
    participant LogoutConfirmModal as 👋 登出确认弹窗
    participant NextJsApiRoutes as 🌐 Next.js API Routes (Logout Endpoint)
    participant Keycloak as 🔐 Keycloak (IdP)
    participant AuditLogDB as 💾 AuditLogin表

    User->>MainScreen: 点击“ログアウト”按钮
    MainScreen->>LogoutConfirmModal: 打开登出确认弹窗
    LogoutConfirmModal->>User: 显示 "本当にログアウトしますか？" 及 "OK", "キャンセル" 按钮

    alt 用户点击 "OK"
        LogoutConfirmModal-->>NextJsApiRoutes: POST /api/auth/logout (或类似端点)
        NextJsApiRoutes->>NextJsApiRoutes: 清除服务器端会话, 清除会话Cookie (Set-Cookie with expiry)
        NextJsApiRoutes->>AuditLogDB: 记录登出审计日志
        NextJsApiRoutes-->>Keycloak: (可选SLO) 重定向至Keycloak登出端点 (携带 id_token_hint, post_logout_redirect_uri)
        Keycloak->>Keycloak: 使IdP会话失效
        Keycloak-->>User: 重定向至 post_logout_redirect_uri (门户登录页)
        User->>MainScreen: (浏览器导航) 显示登录页面
    else 用户点击 "キャンセル" 或 "X"
        LogoutConfirmModal-->>MainScreen: 关闭弹窗
        MainScreen->>User: 用户停留在当前页面
    end
```

### 2.2 业务规则 (Business Rules)
-   **登出确认**: 任何登出操作前，必须向用户显示一个确认对话框，以防止因误点击导致的意外登出。
-   **会话立即失效**: 用户确认登出后，门户应用必须立即采取措施使其当前会话失效（例如，服务器端销毁会话数据，客户端清除会话Cookie）。
-   **重定向至登录页**: 成功登出后，用户必须被自动重定向到系统的登录页面。
-   **SLO (Single Sign-Out) 考虑**: 如果系统与Keycloak实现了OIDC，强烈建议实现单点登出。这意味着门户登出时，不仅要结束门户自身的会话，还应尝试结束Keycloak的会话，以防止用户在同一浏览器访问其他已通过此Keycloak实例认证的应用时仍处于登录状态。`fs.md` 未明确SLO，但这是最佳实践。
-   **与外部系统会话的独立性**: 正如 `fs.md` 所述，门户系统的登出操作不应也无法联动登出用户可能已通过“服务器列表”访问的外部系统（如JP1/ITDM2管理画面）的会话。用户需要分别从那些系统中登出。
-   **审计日志**: 用户的登出操作必须被记录到审计日志（如门户的 `AuditLogin` 表），包含用户ID、登出时间等信息。

### 2.3 用户界面概述 (User Interface Overview)

-   **入口点**:
    *   主界面导航栏右侧的“ログアウト (登出)”按钮。
-   **登出确认对话框**:
    *   **触发**: 点击“ログアウト (登出)”按钮后弹出。
    *   **标题**: 可能为“ログアウトの確認 (登出确认)”或类似文本。
    *   **内容/提示信息**: “本当にログアウトしますか？ (您确定要登出吗？)”或类似确认文本。
    *   **操作按钮**:
        *   “OK” (或 “はい (是)”, “ログアウトする (登出)”)：用于确认执行登出。
        *   “キャンセル (取消)” (或 “いいえ (否)”): 用于取消登出操作并关闭对话框。
    *   **关闭操作**: 通常对话框右上角也会提供标准的“X”关闭图标，其行为应等同于点击“キャンセル (取消)”按钮。
-   **界面草图**: (参考 `fs.md` 图4.5.6 (1) 作为登出确认对话框的高层概念)
    *   `![登出确认对话框示意图](./assets/logout-confirm-modal-sketch.png)` (假设存在此图)

### 2.4 前提条件 (Preconditions)
-   用户必须已通过[登录功能](./01-login.md)成功登录到门户系统，并且拥有一个有效的活动会话。

### 2.5 制约事项 (Constraints/Limitations)
-   **浏览器兼容性**: 仅正式支持 Microsoft Edge 和 Google Chrome 浏览器。
-   **语言支持**: 界面显示语言仅支持日语。
-   **外部系统会话独立**: 登出操作仅限于门户系统本身的会话和（如果实现SLO）Keycloak的会话，不影响用户在其他独立系统中的登录状态。

### 2.6 注意事项 (Notes/Considerations)
-   为确保安全，登出后应彻底清除所有与用户会话相关的本地存储信息（如localStorage, sessionStorage中可能存在的敏感临时数据，尽管最佳实践是避免在此类存储中存放敏感信息）。
-   如果实现了SLO，需要正确配置Keycloak客户端的 `Post Logout Redirect URIs` 以确保用户被安全地重定向回登录页面。
-   应防范CSRF攻击对登出端点的影响（例如，如果登出是通过POST请求，则需要CSRF Token保护）。

### 2.7 错误处理概述 (Error Handling Overview)
-   **登出API调用失败**: 如果前端调用后端登出API时发生网络错误或服务器返回错误状态码：
    *   应向用户显示一个通用的错误提示（例如，“登出操作失败，请稍后重试或联系管理员。”）。
    *   用户的会话可能仍处于活动状态，不应将用户强制导航到登录页，除非能确认会话确实已部分失效。
    *   详细的技术错误应记录在前端控制台和/或后端日志中。
-   **SLO流程中的错误**: 如果在与Keycloak进行单点登出交互时发生错误（例如，Keycloak服务不可达，或重定向配置错误）：
    *   门户应用应尽力保证自身会话已结束。
    *   可能无法完美结束Keycloak会话，但最终仍应尝试将用户导航到登录页。
    *   这类错误通常对用户不直接可见（用户可能已被重定向），但应被系统监控和记录。
-   一般情况下，登出操作是一个相对简单且预期总能成功的流程，复杂错误场景较少。

### 2.8 相关功能参考 (Related Functional References)
*   **主要入口**: [主界面 (Main Screen)](./02-main-screen.md) - 导航栏上的“ログアウト”按钮是访问本功能的主要途径。
*   **认证关联**: [登录 (Login)](./01-login.md) - 用户的登录身份决定了其会话的有效性。
*   **系统整体架构**: [系统架构](../../architecture/system-architecture.md) - 描述了登出功能的数据流和与后端服务的交互。
*   **核心数据模型**: [审计登录数据模型](../../data-models/AuditLogin.md) - 定义了登出事件的记录结构。
*   **源功能规格**: [功能规格书](../../docs-delivery/functional-specifications/fs.md) - 本组件功能规格的原始日文描述，特别是其“4.5 ログアウト”章节。
