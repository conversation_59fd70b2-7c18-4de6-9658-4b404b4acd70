/**
 * @file header.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2024, 2025, Hitachi Solutions, Ltd.
 */

import { SessionData } from "@/app/lib/session";
import { initModals, Modal } from "flowbite";
import { useRouter } from "next/navigation";
import { FC, useEffect, useState } from "react";
import LicenseModal from "./license-modal";
import LogoutModal from "./logout-modal";
import MessageModal from "./message-modal";
import NotificationModal from "./notification-modal";
import PasswordModal from "./password-modal";
import Spinner from "./spinner";

// ナビゲーションバーコンポーネント
const Header: FC<Record<string, never>> = function () {

  const [session, setSession] = useState<SessionData>();
  const router = useRouter(); // ルーターのインスタンス取得
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");
  const [notificationModal, setNotificationModal] = useState<Modal>();
  const [notificationModalVisible, setNotificationModalVisible] =
    useState(false);
  const [licenseModal, setLicenseModal] = useState<Modal>();
  const [licenseModalVisible, setLicenseModalVisible] = useState(false);
  const [passwordModal, setPasswordModal] = useState<Modal>();
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);

  const [messageModalVisible, setMessageModalVisible] = useState(false);
  const [logoutModal, setLogoutModal] = useState<Modal>();
  const initNotificationModal = () => {
    const $targetEl = document.getElementById("notification-modal");
    const options = {
      backdropClasses: "bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-40",
      closable: false,
      onHide: () => {
        setNotificationModalVisible(false);
      },
      onShow: () => {
        setNotificationModalVisible(true);
      },
    };
    const instanceOptions = {
      id: "notification-modal",
      override: true,
    };
    const modal = new Modal($targetEl, options, instanceOptions);
    setNotificationModal(modal);
  };
  const initLicenseModal = () => {
    const $targetEl = document.getElementById("license-modal");
    const options = {
      backdropClasses: "bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-40",
      closable: false,
      onHide: () => {
        setLicenseModalVisible(false);
      },
      onShow: () => {
        setLicenseModalVisible(true);
      },
    };
    const instanceOptions = {
      id: "license-modal",
      override: true,
    };
    const modal = new Modal($targetEl, options, instanceOptions);
    setLicenseModal(modal);
  };
  const initPasswordModal = () => {
    const $targetEl = document.getElementById("password-modal");
    const options = {
      backdropClasses: "bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-40",
      closable: false,
      onHide: () => {
        setPasswordModalVisible(false);
      },
      onShow: () => {
        setPasswordModalVisible(true);
      },
    };
    const instanceOptions = {
      id: "password-modal",
      override: true,
    };
    const modal = new Modal($targetEl, options, instanceOptions);
    setPasswordModal(modal);
  };

  const initLogoutModal = () => {
    const $targetEl = document.getElementById("logout-modal");
    const options = {
      backdropClasses: "bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-40",
      closable: false,
    };
    const instanceOptions = {
      id: "logout-modal",
      override: true,
    };
    const modal = new Modal($targetEl, options, instanceOptions);
    setLogoutModal(modal);
  };

  const getSession = async () => {
    const response = await fetch(`/api/ironSession`, {
      method: "POST", // POSTリクエスト
      headers: {
        "Content-Type": "application/json", // JSON形式のヘッダー
      }
    });
    
    if (response.status === 200) {
      const data = await response.json();
      setSession(data.data);
      return true;
    } else {
      router.push("/login");
    }
  }
  
  const openNotificationModal = async () => {
    const res = getSession();
    if (await res && notificationModal) {
      notificationModal.show();
    }
  };
  const closeNotificationModal = () => {
    if (notificationModal) {
      notificationModal.hide();
    }
  };
  const openLicenseModal = async () => {
    const res = getSession();
    if (await res && licenseModal) {
      licenseModal.show();
    }
  };
  const closeLicenseModal = () => {
    if (licenseModal) {
      licenseModal.hide();
    }
  };
  const openPasswordModal = async () => {
    const res = getSession();
    if (await res && passwordModal) {
      passwordModal.show();
    }
  };
  const closePasswordModal = () => {
    if (passwordModal) {
      passwordModal.hide();
    }
  };
  const openMessageModal = (message: string) => {
    if (passwordModal) {
      passwordModal.hide();
    }
    setMessage(message);
    setError("");
  };
  const closeMessageModal = () => {
    setMessageModalVisible(false);
    setMessage("");
    setError("");
  };
  const openErrorModal = (error: string) => {
    notificationModal?.hide();
    passwordModal?.hide();
    licenseModal?.hide();
    logoutModal?.hide();
    setMessage("");
    setError(error);
  };
  const openLogoutModal = () => {
    if (logoutModal) {
      logoutModal.show();
    }
  };
  const closeLogoutModal = () => {
    if (logoutModal) {
      logoutModal.hide();
    }
  };

  useEffect(() => {
    getSession();
    initModals();
    initNotificationModal();
    initLicenseModal();
    initPasswordModal();
    initLogoutModal();
  }, []);
  useEffect(() => {
    if (message || error) {
      setMessageModalVisible(true);
    }

    if (session) {
      const sessionToken: string = document.cookie; // セッショントークンを取得
      document.cookie = `${sessionToken}; path=/; SameSite=None; Secure`; // 有効期限を削除
    }
  }, [message, error, session]);

  return (
    <>
      <MessageModal
        message={message}
        error={error}
        isOpen={messageModalVisible}
        onClose={closeMessageModal}
      />
      <LogoutModal onError={openErrorModal} onClose={closeLogoutModal} />
      <NotificationModal
        isOpen={notificationModalVisible}
        onClose={closeNotificationModal}
        onError={openErrorModal}
      />
      <LicenseModal
        isOpen={licenseModalVisible}
        onClose={closeLicenseModal}
        onError={openErrorModal}
      />
      <PasswordModal
        id={session ? session.user.id! : ""}
        userId={session ? session.user.userId! : ""}
        isOpen={passwordModalVisible}
        onClose={closePasswordModal}
        onOK={openMessageModal}
        onError={openErrorModal}
      />
      <header className="sticky top-0 z-20">
        <div className="absolute inset-0 bg-[url('/login_wave.png')] bg-[top_69%_left_380%] bg-no-repeat opacity-60 xl:bg-[top_72%_left_150%]"></div>
        <nav className="border-gray-700 bg-gray-800 pb-2 pt-4 sm:px-4">
          <div className="mb-2 mx-auto flex flex-wrap items-center justify-between">
            <a
              href="/"
              className="flex items-center space-x-3 rtl:space-x-reverse"
            >
              <span className="self-center whitespace-nowrap px-3 text-xl font-semibold text-white"></span>
            </a>
            <div className="z-10 flex md:order-2">
              <label className="mr-2 text-sm font-medium leading-8 text-white">
                現在のユーザー:
              </label>
              <label className="mr-2 text-sm font-medium leading-8 text-white">
                {(session && session.user.userId) || <Spinner />}
              </label>
              <button
                type="button"
                className="rounded bg-gradient-dark drop-shadow-dark shadow-dark px-3 py-2 text-center text-xs 
                font-medium text-white hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
                onClick={openLogoutModal}
              >
                ログアウト
              </button>
            </div>
          </div>
          <div className="mx-auto flex flex-wrap items-center justify-between">
            <div />
            <div className="z-10 flex md:order-2">
              <button
                type="button"
                className="mr-2 rounded bg-gradient-dark drop-shadow-dark shadow-dark px-3 py-2 text-center text-xs 
                font-medium text-white hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
                onClick={openPasswordModal}
              >
                パスワード変更
              </button>
              <button
                type="button"
                className="mr-2 rounded bg-gradient-dark drop-shadow-dark shadow-dark px-3 py-2 text-center text-xs 
                font-medium text-white hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
                onClick={openNotificationModal}
              >
                お知らせ
              </button>
              <button
                type="button"
                className="rounded bg-gradient-dark drop-shadow-dark shadow-dark px-3 py-2 text-center text-xs 
                font-medium text-white hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
                onClick={openLicenseModal}
              >
                ライセンス
              </button>
            </div>
          </div>
        </nav>
      </header>
    </>
  );
};

export default Header;
