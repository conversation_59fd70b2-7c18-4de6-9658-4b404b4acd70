# Azure Functions打包脚本
param(
    [string]$AppName = "jcs-backend-services",
    [string]$OutputDir = "..\dist"
)

$ErrorActionPreference = "Stop"

# 确保输出目录存在
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir | Out-Null
}

$FunctionsDir = "..\apps\$AppName\functions"
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$ZipFilename = "${AppName}_functions_${Timestamp}.zip"
$ZipPath = Join-Path $OutputDir $ZipFilename

Write-Host "--- 准备Azure Functions打包 ---"
Set-Location $FunctionsDir

Write-Host "1. 安装生产依赖..."
pnpm install --prod

Write-Host "2. 构建TypeScript..."
if (Test-Path "tsconfig.json") {
    pnpm run build
}

Write-Host "3. 创建Zip包: $ZipFilename ..."

# 创建临时目录用于打包
$TempDir = Join-Path $env:TEMP "functions_package_$Timestamp"
if (Test-Path $TempDir) {
    Remove-Item $TempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $TempDir | Out-Null

# 复制需要的文件到临时目录
Get-ChildItem -Path . -Exclude @(".git", "node_modules", "local.settings.json", "*.ts", "tsconfig.json", "dist") |
    Copy-Item -Destination $TempDir -Recurse

# 创建zip文件
Compress-Archive -Path "$TempDir\*" -DestinationPath $ZipPath -Force

# 清理临时目录
Remove-Item $TempDir -Recurse -Force

Set-Location $PSScriptRoot
Write-Host "--- Azure Functions包已创建: $ZipPath ---" 