/**
 * @fileoverview Next.jsアプリケーションの設定ファイル
 * @description ビルド設定、セキュリティヘッダー、キャッシュ制御を定義する
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

/** @type {import('next').NextConfig} */
const nextConfig = {
  typescript: {
    // 本番環境でのセキュリティ強化のため、TypeScriptエラーを無視しない
    ignoreBuildErrors: false,
  },
  experimental: {
    serverActions: true,
  },
  // React厳格モードを有効化（コード健壮性向上のため）
  reactStrictMode: true,
  swcMinify: true,
  async headers() {
    return [
      // 全ページ共通のセキュリティヘッダー
      {
        source: '/(.*)',
        headers: [
          // 基本セキュリティヘッダー
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          },
          // Content Security Policy（XSS攻撃とコード注入攻撃の防止）
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // Next.jsの動的スクリプトに必要
              "style-src 'self' 'unsafe-inline'", // Tailwind CSSのインラインスタイルに必要
              "img-src 'self' data: blob:",
              "font-src 'self' data:",
              "connect-src 'self'",
              "frame-src 'none'",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self'",
              "frame-ancestors 'none'"
            ].join('; ')
          },
        ],
      },
      // 認証関連ページのキャッシュ制御
      {
        source: '/dashboard/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'private, no-store, no-cache, must-revalidate'
          },
          {
            key: 'Pragma',
            value: 'no-cache'
          },
          {
            key: 'Expires',
            value: '0'
          }
        ]
      },
      // APIエンドポイントのキャッシュ制御
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'private, no-store, no-cache, must-revalidate'
          }
        ]
      },
      // ログインページのキャッシュ制御
      {
        source: "/login",
        headers: [
          {
            key: "Pragma",
            value: "no-cache",
          },
          {
            key: "Cache-Control",
            value: "private, no-store, no-cache, must-revalidate",
          },
          {
            key: "Expires",
            value: "0",
          },
        ],
      },
    ];
  },
};

// const withKeycloak = require('next-keycloak-adapter')

// module.exports = withKeycloak({
//   keycloak: {
//     url: env("KEYCLOAK_URL"),
//     realm: env("KEYCLOAK_REALM"),
//     clientId: env("KEYCLOAK_CLIENT_ID"),
//   },
// })

module.exports = nextConfig;
