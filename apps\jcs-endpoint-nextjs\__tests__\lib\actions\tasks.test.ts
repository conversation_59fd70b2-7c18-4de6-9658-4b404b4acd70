/**
 * @fileoverview タスク受付処理の単体テスト
 * @description サーバ一覧画面から発行される全てのバックグラウンドタスク（操作ログのエクスポート、
 * 管理項目定義のインポート・エクスポート）の実行要求を統一的に処理するサーバサイドロジックを検証する。
 * 入力パラメータの解析・検証、前処理、データベース登録、Service Bus送信の一連の処理を含む。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

// 模拟 portal-error
jest.mock("@/app/lib/portal-error", () => ({
  handleServerError: jest.fn((error) => {
    const { PORTAL_ERROR_MESSAGES } = require("@/app/lib/definitions");
    const { PrismaClientInitializationError } = require("@prisma/client/runtime/library");

    if (error instanceof PrismaClientInitializationError) {
      throw new Error(PORTAL_ERROR_MESSAGES.EMEC0006);
    }
    throw new Error(PORTAL_ERROR_MESSAGES.EMEC0007);
  }),
}));

// 模拟 logger 来显示实际错误
jest.mock("@/app/lib/logger", () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    error: jest.fn((data) => {
      console.log("Logger.error called with:", data.message);
      if (data.error) {
        console.log("Error details:", data.error.message || data.error.toString());
        console.log("Error stack:", data.error.stack);
      }
    }),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  LogFunctionSignature: () => (target: any, propertyKey: string, descriptor: PropertyDescriptor) => descriptor,
}));

// 模拟 prisma
jest.mock("@/app/lib/prisma", () => ({
  __esModule: true,
  default: {
    task: {
      create: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
    },
    containerConcurrencyStatus: {
      findUnique: jest.fn(),
      upsert: jest.fn(),
    },
    server: {
      findUnique: jest.fn(),
    },
    $transaction: jest.fn(),
  },
}));

// 模拟 iron-session
jest.mock("iron-session", () => ({
  getIronSession: jest.fn(),
}));

// 模拟 next/headers
jest.mock("next/headers", () => ({
  cookies: jest.fn(() => ({})),
}));

// 模拟 next/cache
jest.mock("next/cache", () => ({
  revalidatePath: jest.fn(),
}));

// 模拟 Azure Blob Storage
jest.mock("@/app/lib/integrations/azure-blob", () => ({
  BlobActions: {
    uploadFile: jest.fn(),
    deleteBlob: jest.fn(),
  },
}));

// 模拟 Azure Service Bus
jest.mock("@/app/lib/integrations/azure-service-bus", () => ({
  ServiceBusActions: {
    sendMessage: jest.fn(),
  },
}));

// 模拟 ServerDataServers
jest.mock("@/app/lib/data/servers", () => ({
  ServerDataServers: {
    getServerDetailsForTask: jest.fn(),
  },
}));

// 模拟 ServerDataLov
jest.mock("@/app/lib/data/lov", () => ({
  ServerDataLov: {
    fetchLov: jest.fn(),
  },
}));

// 模拟 ServerDataTasks
jest.mock("@/app/lib/data/tasks", () => ({
  ServerDataTasks: {
    getContainerStatus: jest.fn(),
  },
}));

import { expect } from "@jest/globals";
import { createTask, refreshTaskList } from "@/app/lib/actions/tasks";
import { PORTAL_ERROR_MESSAGES, TASK_TYPE, TASK_STATUS, CONTAINER_STATUS, FORM_FIELD_NAMES } from "@/app/lib/definitions";
import { formatMessage } from "@/app/lib/utils";
import { getIronSession } from "iron-session";
import prisma from "@/app/lib/prisma";
import { ServiceBusActions } from "@/app/lib/integrations/azure-service-bus";

const prismaMock = prisma as jest.Mocked<typeof prisma>;

// 通用模拟设置函数
function setupCommonMocks() {
  // 环境变量设置
  process.env.AZURE_SERVICEBUS_NAMESPACE_HOSTNAME = "test.servicebus.windows.net";
  process.env.SERVICE_BUS_TASK_CONTROL_QUEUE_NAME = "task-control";
  process.env.SERVICE_BUS_TASK_INPUT_QUEUE_NAME = "task-input";
  process.env.AZURE_STORAGE_ACCOUNT_NAME = "teststorage";
  process.env.AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF = "assetsfield-def";
  process.env.APP_CACHE_TTL_SECONDS = "3600";

  // 会话模拟
  (getIronSession as jest.Mock).mockResolvedValue({
    user: {
      userId: "testUser",
      licenseId: "license123",
      tz: "Asia/Tokyo",
    },
  });

  // 获取模拟的模块并设置默认行为
  const serverDataServers = require("@/app/lib/data/servers").ServerDataServers;
  const serverDataTasks = require("@/app/lib/data/tasks").ServerDataTasks;
  const serverDataLov = require("@/app/lib/data/lov").ServerDataLov;
  const blobActions = require("@/app/lib/integrations/azure-blob").BlobActions;
  const serviceBusActions = require("@/app/lib/integrations/azure-service-bus").ServiceBusActions;

  // 设置默认模拟行为
  serverDataServers.getServerDetailsForTask = jest.fn().mockResolvedValue({
    name: "TestServer",
    licenseId: "license123",
    azureVmName: "vm-test",
    dockerContainerName: "container-test",
    hrwGroupName: "group-test",
  });

  serverDataTasks.getContainerStatus = jest.fn().mockResolvedValue("IDLE");

  serverDataLov.fetchLov = jest.fn().mockImplementation((code: string) => {
    const lovMap: { [key: string]: { value: string } } = {
      "TASK_TYPE.MGMT_ITEM_IMPORT": { value: "管理項目定義のインポート" },
      "TASK_TYPE.MGMT_ITEM_EXPORT": { value: "管理項目定義のエクスポート" },
      "TASK_TYPE.OPLOG_EXPORT": { value: "操作ログのエクスポート" },
      "OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN": { value: "5" },
    };
    return Promise.resolve(lovMap[code] || null);
  });

  blobActions.uploadFile = jest.fn().mockResolvedValue("uploads/test-file.csv");
  serviceBusActions.sendMessage = jest.fn().mockResolvedValue(undefined);

  return {
    serverDataServers,
    serverDataTasks,
    serverDataLov,
    blobActions,
    serviceBusActions,
  };
}

// モックの設定
jest.mock("next/headers", () => ({
  cookies: jest.fn(),
}));

jest.mock("iron-session", () => ({
  getIronSession: jest.fn(),
}));

jest.mock("@/app/lib/data/servers", () => ({
  ServerDataServers: {
    getServerDetailsForTask: jest.fn(),
  },
}));

jest.mock("@/app/lib/data/tasks", () => ({
  ServerDataTasks: {
    getContainerStatus: jest.fn(),
  },
}));

jest.mock("@/app/lib/data/lov", () => ({
  ServerDataLov: {
    fetchCachedLov: jest.fn(),
  },
}));

jest.mock("@azure/storage-blob", () => {
  const mBlobServiceClient = {
    getContainerClient: jest.fn(() => ({
      getBlockBlobClient: jest.fn(() => ({
        uploadData: jest.fn().mockResolvedValue({}),
        upload: jest.fn().mockResolvedValue({}),
      })),
    })),
  };
  const BlobServiceClient = Object.assign(
    jest.fn(() => mBlobServiceClient),
    {
      fromConnectionString: jest.fn(() => mBlobServiceClient),
    }
  );
  return { BlobServiceClient };
});

jest.mock("@/app/lib/integrations/azure-service-bus", () => ({
  ServiceBusActions: {
    sendMessage: jest.fn().mockResolvedValue(undefined),
  },
}));

jest.mock("next/cache", () => ({
  revalidatePath: jest.fn(),
  revalidateTag: jest.fn(),
}));

jest.mock("@/app/lib/integrations/azure-blob", () => ({
  BlobActions: {
    uploadFile: jest.fn().mockResolvedValue("mock/blob/path.csv"),
    deleteBlob: jest.fn().mockResolvedValue(undefined),
  },
}));



describe("タスク受付処理（createTaskAction Server Action）", () => {

  beforeEach(() => {
    setupCommonMocks();
    (prismaMock.task.create as jest.Mock).mockReset();
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  describe("管理項目定義のインポート", () => {
    beforeEach(() => {
      setupCommonMocks();
      (prismaMock.task.create as jest.Mock).mockReset();
    });

    /**
     * 試験観点：管理項目定義インポートタスクの正常系処理
     * 試験対象：createTaskAction関数の管理項目定義インポート処理機能
     * 試験手順：
     * 1. 正常なCSVファイル、MGMT_ITEM_IMPORTタスク種別、サーバーID、ファイル名をFormDataに設定
     * 2. Azure Blob Storageへの一時アップロード処理を実行
     * 3. データベーストランザクション開始、タスクレコード作成
     * 4. Service BusのTaskInputQueueへメッセージ送信
     * 5. 関連パスのキャッシュ無効化
     * 確認項目：
     * - 処理結果がtrueで返されること
     * - タスク実行受付完了メッセージが返されること
     * - Azure Blob Storageへファイルがアップロードされること
     * - Service Busへメッセージが送信されること
     */
    it("正常系：管理定義インポートタスクを作成する", async () => {
      const mockTask = {
        id: "task123",
        taskName: "管理定義インポート",
        taskType: TASK_TYPE.MGMT_ITEM_IMPORT,
        status: TASK_STATUS.QUEUED,
        submittedAt: new Date(),
        startedAt: null,
        endedAt: null,
        updatedAt: new Date(),
        submittedByUserId: "testUser",
        licenseId: "license123",
        targetServerId: "server789",
        targetServerName: "TestServer",
        targetVmName: "vm-test",
        targetContainerName: "container-test",
        targetHRWGroupName: "group-test",
        parametersJson: null,
        resultMessage: null,
        outputBlobPath: null,
        errorMessage: null,
        errorCode: null,
      };
      let createArgs: any = undefined;
      // @ts-expect-error Prisma $transaction のモックは型安全性を保証しないため、型エラーを無視する
      prismaMock.$transaction = jest.fn(async (cb) => {
        const tx = {
          containerConcurrencyStatus: {
            findUnique: jest.fn().mockResolvedValue({}),
            create: jest.fn().mockResolvedValue({}),
          },
          task: {
            create: jest.fn().mockImplementation((args) => {
              createArgs = args;
              return mockTask;
            }),
          },
        } as unknown as typeof prismaMock;
        await cb(tx);
      });
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_IMPORT);
      formData.append("serverId", "server789");
      formData.append("importFile", new Blob(["test content"], { type: "text/csv" }), "test.csv");
      formData.append("originalFileName", "test.csv");
      const result = await createTask(formData);
      expect(result.success).toBe(true);
      expect(result.message).toContain("TestServer");
      expect(createArgs.data.targetContainerName).toBe("container-test");
      expect(createArgs.data.targetHRWGroupName).toBe("group-test");
    });

    /**
     * 試験観点：不正なファイル形式の場合のエラー検証。
     * 試験対象：管理項目定義インポートタスク作成処理
     * 試験手順：
     *   1. 拡張子がcsv以外のファイルをFormDataに設定する。
     *   2. タスク作成処理を実行する。
     *   3. エラーメッセージが返ることを確認する。
     * 確認項目：
     *   - result.successがfalseであること。
     *   - result.messageがEMEC0017であること。
     */
    it("異常系：無効なファイル形式の場合はエラーを返す", async () => {
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_IMPORT);
      formData.append("serverId", "server789");
      formData.append("importFile", new Blob(["test content"], { type: "text/plain" }), "test.txt");
      formData.append("originalFileName", "test.txt");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toBe(PORTAL_ERROR_MESSAGES.EMEC0017);
    });

    /**
     * 試験観点：共通パラメータ解析失敗（taskType不足）
     * 試験対象：設計文書「06-タスク受付処理詳細.md」共通パラメータ解析
     * 試験手順：
     * 1. taskTypeを含まないFormDataを作成
     * 2. createTaskActionを実行
     * 3. EMEC0021エラーが返されることを確認
     * 確認項目：
     * - success: falseが返されること
     * - EMEC0021「サーバの接続に失敗したため、タスクを{0}できませんでした」（{0}は「開始」で置換）が返されること
     */
    it("異常系：タスク種別が不足している場合はエラーを返す", async () => {
      const formData = new FormData();
      formData.append("serverId", "server789");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toContain("サーバの接続に失敗したため、タスクを開始できませんでした");
    });

    /**
     * 試験観点：importFileが不足している場合のエラー検証。
     * 試験対象：管理項目定義インポートタスク作成処理
     * 試験手順：
     *   1. importFileをFormDataに設定しない。
     *   2. タスク作成処理を実行する。
     *   3. エラーメッセージが返ることを確認する。
     * 確認項目：
     *   - result.successがfalseであること。
     *   - result.messageがEMEC0016（ファイル）であること。
     */
    it("異常系：importFileが不足している場合はエラーを返す", async () => {
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_IMPORT);
      formData.append("serverId", "server789");
      formData.append("originalFileName", "test.csv");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toBe(
        PORTAL_ERROR_MESSAGES.EMEC0016.replace("{0}", FORM_FIELD_NAMES.MGMT_ITEM_CSV_FILE)
      );
    });

    /**
     * 試験観点：originalFileNameが不足している場合のエラー検証。
     * 試験対象：管理項目定義インポートタスク作成処理
     * 試験手順：
     *   1. originalFileNameをFormDataに設定しない。
     *   2. タスク作成処理を実行する。
     *   3. エラーメッセージが返ることを確認する。
     * 確認項目：
     *   - result.successがfalseであること。
     *   - result.messageがEMEC0016（ファイル）であること。
     */
    it("異常系：originalFileNameが不足している場合はエラーを返す", async () => {
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_IMPORT);
      formData.append("serverId", "server789");
      formData.append("importFile", new Blob(["test content"], { type: "text/csv" }), "test.csv");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toBe(
        PORTAL_ERROR_MESSAGES.EMEC0016.replace("{0}", FORM_FIELD_NAMES.MGMT_ITEM_CSV_FILE)
      );
    });

    /**
     * 試験観点：ファイルサイズ超過時のエラー検証
     * 試験対象：createTask（管理項目定義インポート）
     * 試験手順：
     *   1. 最大サイズを超えるファイルを作成する
     *   2. createTaskActionを実行する
     *   3. エラーメッセージがEMEC0028であることを確認する
     * 確認項目：
     *   - result.successがfalseであること
     *   - result.messageがEMEC0028（ファイルサイズ超過）であること
     */
    it("異常系：ファイルサイズ超過時はEMEC0028を返す", async () => {
      // 10MB を超える大きなファイルを作成（FILE_VALIDATION.CSV.MAX_FILE_SIZE = 10MB）
      const largeContent = "a".repeat(11 * 1024 * 1024); // 11MB
      const largeFile = new File([largeContent], "large.csv", { type: "text/csv" });

      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_IMPORT);
      formData.append("serverId", "server789");
      formData.append("importFile", largeFile);
      formData.append("originalFileName", "large.csv");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toBe(PORTAL_ERROR_MESSAGES.EMEC0028);
    });

    /**
     * 試験観点：Blobアップロード失敗時のエラー検証。
     * 試験対象：管理項目定義インポートタスク作成処理
     * 試験手順：
     *   1. BlobActions.uploadFileが例外を投げるようにモックする。
     *   2. タスク作成処理を実行する。
     *   3. エラーメッセージがEMEC0018であることを確認する。
     * 確認項目：
     *   - result.successがfalseであること。
     *   - result.messageがEMEC0018であること。
     */
    it("異常系：Blobアップロード失敗時はEMEC0018を返す", async () => {
      const { BlobActions } = require("@/app/lib/integrations/azure-blob");
      (BlobActions.uploadFile as jest.Mock).mockRejectedValueOnce(new Error("upload failed"));
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_IMPORT);
      formData.append("serverId", "server789");
      formData.append("importFile", new File(["test"], "test.csv", { type: "text/csv" }));
      formData.append("originalFileName", "test.csv");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toBe(PORTAL_ERROR_MESSAGES.EMEC0018);
    });

    /**
     * 試験観点：ServiceBus送信失敗時のエラー検証。
     * 試験対象：管理項目定義インポートタスク作成処理
     * 試験手順：
     *   1. ServiceBusActions.sendMessageが例外を投げるようにモックする。
     *   2. タスク作成処理を実行する。
     *   3. エラーメッセージがEMEC0019であることを確認する。
     * 確認項目：
     *   - result.successがfalseであること。
     *   - result.messageがEMEC0019であること。
     */
    it("異常系：ServiceBus送信失敗時はEMEC0019を返す", async () => {
      const { ServiceBusActions } = require("@/app/lib/integrations/azure-service-bus");
      (ServiceBusActions.sendMessage as jest.Mock).mockRejectedValueOnce(new Error("ServiceBus failed"));
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_IMPORT);
      formData.append("serverId", "server789");
      formData.append("importFile", new File(["test"], "test.csv", { type: "text/csv" }));
      formData.append("originalFileName", "test.csv");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toBe(
        PORTAL_ERROR_MESSAGES.EMEC0019.replace("{0}", "開始")
      );
    });

    /**
     * 試験観点：DBトランザクション失敗時のエラー検証。
     * 試験対象：管理項目定義インポートタスク作成処理
     * 試験手順：
     *   1. prisma.$transactionが例外を投げるようにモックする。
     *   2. タスク作成処理を実行する。
     *   3. エラーメッセージがEMEC0006であることを確認する。
     * 確認項目：
     *   - result.successがfalseであること。
     *   - result.messageがEMEC0006であること。
     */
    it("異常系：DBトランザクション失敗時はEMEC0006を返す", async () => {
      const arrayBufferSpy = jest.spyOn(global.Blob.prototype, "arrayBuffer").mockResolvedValue(new ArrayBuffer(8));
      const mBlockBlobClient = require("@azure/storage-blob").BlobServiceClient.fromConnectionString().getContainerClient().getBlockBlobClient();
      mBlockBlobClient.uploadData.mockResolvedValue({});
      const prisma = require("@/app/lib/prisma").default;
      const txSpy = jest.spyOn(prisma, "$transaction").mockRejectedValue(new Error("DB error"));
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_IMPORT);
      formData.append("serverId", "server789");
      formData.append("importFile", new File(["test"], "test.csv", { type: "text/csv" }));
      formData.append("originalFileName", "test.csv");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toContain("サーバの接続に失敗したため、タスクを開始できませんでした");
      arrayBufferSpy.mockRestore();
      txSpy.mockRestore();
    });
  });

  /**
   * @fileoverview 管理項目定義のエクスポートタスクに関するテストである。
   * @description 必須パラメータ・DB例外等の正常系・異常系を検証する。
   * <AUTHOR>
   * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
   * 試験観点：パラメータ検証、DB例外処理。
   * 試験対象：createTask（管理項目定義エクスポート）
   */
  describe("管理項目定義のエクスポート", () => {
    beforeEach(() => {
      const { serverDataServers } = setupCommonMocks();
      // サーバー詳細モックの設定を上書き
      serverDataServers.getServerDetailsForTask.mockResolvedValue({
        name: "TestServer",
        licenseId: "license123",
        azureVmName: "vm-test",
        dockerContainerName: "container-test",
        hrwGroupName: "group-test",
      });
      (prismaMock.task.create as jest.Mock).mockReset();
    });

    /**
     * 試験観点：正常なパラメータでタスク作成が成功すること。
     * 試験対象：createTask（管理項目定義エクスポート）
     * 試験手順：
     *   1. 正常なタスク種別・サーバーIDをFormDataに設定する。
     *   2. createTaskActionを実行する。
     *   3. 成功メッセージにサーバー名が含まれることを確認する。
     * 確認項目：
     *   - result.successがtrueであること。
     *   - result.messageにサーバー名が含まれること。
     */
    it("正常系：管理定義エクスポートタスクを作成する", async () => {
      const mockTask = {
        id: "task456",
        taskName: "管理定義エクスポート",
        taskType: TASK_TYPE.MGMT_ITEM_EXPORT,
        status: TASK_STATUS.QUEUED,
        submittedAt: new Date(),
        startedAt: null,
        endedAt: null,
        updatedAt: new Date(),
        submittedByUserId: "testUser",
        licenseId: "license123",
        targetServerId: "server789",
        targetServerName: "TestServer",
        targetVmName: "vm-test",
        targetContainerName: "container-test",
        targetHRWGroupName: "group-test",
        parametersJson: null,
        resultMessage: null,
        outputBlobPath: null,
        errorMessage: null,
        errorCode: null,
      };
      let createArgs: any = undefined;
      // @ts-expect-error Prisma $transaction のモックは型安全性を保証しないため、型エラーを無視する
      prismaMock.$transaction = jest.fn(async (cb) => {
        const tx = {
          containerConcurrencyStatus: {
            findUnique: jest.fn().mockResolvedValue({}),
            create: jest.fn().mockResolvedValue({}),
          },
          task: {
            create: jest.fn().mockImplementation((args) => {
              createArgs = args;
              return mockTask;
            }),
          },
        } as unknown as typeof prismaMock;
        await cb(tx);
      });
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_EXPORT);
      formData.append("serverId", "server789");
      const result = await createTask(formData);
      expect(result.success).toBe(true);
      expect(result.message).toContain("TestServer");
      expect(createArgs.data.targetContainerName).toBe("container-test");
      expect(createArgs.data.targetHRWGroupName).toBe("group-test");
    });

    /**
     * 試験観点：共通パラメータ解析失敗（taskType不足）
     * 試験対象：設計文書「06-タスク受付処理詳細.md」共通パラメータ解析
     * 試験手順：
     * 1. taskTypeを含まないFormDataを作成
     * 2. createTaskActionを実行
     * 3. EMEC0021エラーが返されることを確認
     * 確認項目：
     * - success: falseが返されること
     * - EMEC0021「サーバの接続に失敗したため、タスクを{0}できませんでした」（{0}は「開始」で置換）が返されること
     */
    it("異常系：タスク種別が不足している場合はエラーを返す", async () => {
      const formData = new FormData();
      formData.append("serverId", "server789");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toContain("サーバの接続に失敗したため、タスクを開始できませんでした");
    });

    /**
     * 試験観点：共通パラメータ解析失敗（serverId不足）
     * 試験対象：設計文書「06-タスク受付処理詳細.md」共通パラメータ解析
     * 試験手順：
     * 1. serverIdを含まないFormDataを作成
     * 2. createTaskActionを実行
     * 3. EMEC0021エラーが返されることを確認
     * 確認項目：
     * - success: falseが返されること
     * - EMEC0021「サーバの接続に失敗したため、タスクを{0}できませんでした」（{0}は「開始」で置換）が返されること
     */
    it("異常系：serverIdが不足している場合はエラーを返す", async () => {
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_EXPORT);
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toContain("サーバの接続に失敗したため、タスクを開始できませんでした");
    });

    /**
     * 試験観点：サーバ実行構成取得失敗時のエラー検証
     * 試験対象：createTask（管理項目定義エクスポート）
     * 試験手順：
     *   1. ServerDataServers.getServerDetailsForTaskが例外を投げるようにモックする
     *   2. createTaskActionを実行する
     *   3. エラーメッセージがEMEC0021であることを確認する
     * 確認項目：
     *   - result.successがfalseであること
     *   - result.messageがEMEC0021（サーバ接続失敗）であること
     */
    it("異常系：サーバ実行構成取得失敗時はEMEC0021を返す", async () => {
      const { serverDataServers } = setupCommonMocks();
      serverDataServers.getServerDetailsForTask.mockRejectedValue(new Error("DB connection error"));
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_EXPORT);
      formData.append("serverId", "server789");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toContain("サーバの接続に失敗したため、タスクを開始できませんでした");
    });

    /**
     * 試験観点：サーバが見つからない場合のエラー検証
     * 試験対象：createTask（管理項目定義エクスポート）
     * 試験手順：
     *   1. ServerDataServers.getServerDetailsForTaskがnullを返すようにモックする
     *   2. createTaskActionを実行する
     *   3. エラーメッセージがEMEC0021であることを確認する
     * 確認項目：
     *   - result.successがfalseであること
     *   - result.messageがEMEC0021（サーバ接続失敗）であること
     */
    it("異常系：サーバが見つからない場合はEMEC0021を返す", async () => {
      const { serverDataServers } = setupCommonMocks();
      serverDataServers.getServerDetailsForTask.mockResolvedValue(null);
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_EXPORT);
      formData.append("serverId", "server789");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toContain("サーバの接続に失敗したため、タスクを開始できませんでした");
    });

    /**
     * 試験観点：サーバ構成情報不足時のエラー検証
     * 試験対象：createTask（管理項目定義エクスポート）
     * 試験手順：
     *   1. ServerDataServers.getServerDetailsForTaskが不完全な構成情報を返すようにモックする
     *   2. createTaskActionを実行する
     *   3. エラーメッセージがEMEC0021であることを確認する
     * 確認項目：
     *   - result.successがfalseであること
     *   - result.messageがEMEC0021（サーバ接続失敗）であること
     */
    it("異常系：サーバ構成情報不足時はEMEC0021を返す", async () => {
      const { serverDataServers } = setupCommonMocks();
      serverDataServers.getServerDetailsForTask.mockResolvedValue({
        name: "TestServer",
        licenseId: "license123",
        azureVmName: null, // 構成情報不足
        dockerContainerName: "container-test",
        hrwGroupName: "group-test",
      });
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_EXPORT);
      formData.append("serverId", "server789");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toContain("サーバの接続に失敗したため、タスクを開始できませんでした");
    });

    /**
     * 試験観点：コンテナステータス取得失敗時のエラー検証
     * 試験対象：createTask（管理項目定義エクスポート）
     * 試験手順：
     *   1. ServerDataTasks.getContainerStatusが例外を投げるようにモックする
     *   2. createTaskActionを実行する
     *   3. エラーメッセージがEMEC0021であることを確認する
     * 確認項目：
     *   - result.successがfalseであること
     *   - result.messageがEMEC0021（サーバ接続失敗）であること
     */
    it("異常系：コンテナステータス取得失敗時はEMEC0021を返す", async () => {
      const { serverDataTasks } = setupCommonMocks();
      serverDataTasks.getContainerStatus.mockRejectedValue(new Error("Container status fetch failed"));
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_EXPORT);
      formData.append("serverId", "server789");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toContain("サーバの接続に失敗したため、タスクを開始できませんでした");
    });

    /**
     * 試験観点：DBトランザクション失敗時のエラー検証。
     * 試験対象：createTask（管理項目定義エクスポート）
     * 試験手順：
     *   1. prisma.$transactionが例外を投げるようにモックする。
     *   2. createTaskActionを実行する。
     *   3. エラーメッセージがEMEC0006であることを確認する。
     * 確認項目：
     *   - result.successがfalseであること。
     *   - result.messageがEMEC0006であること。
     */
    it("異常系：DBトランザクション失敗時はEMEC0006を返す", async () => {
      const prisma = require("@/app/lib/prisma").default;
      const txSpy = jest.spyOn(prisma, "$transaction").mockRejectedValue(new Error("DB error"));
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_EXPORT);
      formData.append("serverId", "server789");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      // 根据设计文档第120行：DB事务失败时应返回EMEC0021
      expect(result.message).toContain("サーバの接続に失敗したため、タスクを開始できませんでした");
      txSpy.mockRestore();
    });
  });

  /**
   * @fileoverview 操作ログエクスポートタスクに関するテストである。
   * @description 日付範囲・最大日数・DB例外等の正常系・異常系を検証する。
   * <AUTHOR>
   * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
   * 試験観点：日付範囲検証、最大日数検証、DB例外処理。
   * 試験対象：createTask（操作ログエクスポート）
   */
  describe("操作ログエクスポート", () => {
    beforeEach(() => {
      const { serverDataServers, serverDataLov } = setupCommonMocks();
      // サーバー詳細モックの設定を上書き
      serverDataServers.getServerDetailsForTask.mockResolvedValue({
        name: "TestServer",
        licenseId: "license123",
        azureVmName: "vm-test",
        dockerContainerName: "container-test",
        hrwGroupName: "group-test",
      });
      (prismaMock.task.create as jest.Mock).mockReset();
      // LOV設定を上書き
      serverDataLov.fetchLov.mockImplementation((code: string) => {
        const lovMap: { [key: string]: { value: string } } = {
          "TASK_TYPE.MGMT_ITEM_IMPORT": { value: "管理項目定義のインポート" },
          "TASK_TYPE.MGMT_ITEM_EXPORT": { value: "管理項目定義のエクスポート" },
          "TASK_TYPE.OPLOG_EXPORT": { value: "操作ログのエクスポート" },
          "OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN": { value: "5" },
        };
        return Promise.resolve(lovMap[code] || null);
      });
    });

    /**
     * 試験観点：正常なパラメータでタスク作成が成功すること。
     * 試験対象：createTask（操作ログエクスポート）
     * 試験手順：
     *   1. 正常な日付範囲・最大日数・タスク種別・サーバーIDをFormDataに設定する。
     *   2. createTaskActionを実行する。
     *   3. 成功メッセージにサーバー名が含まれることを確認する。
     * 確認項目：
     *   - result.successがtrueであること。
     *   - result.messageにサーバー名が含まれること。
     */
    it("正常系：操作ログエクスポートタスクを作成する", async () => {
      const mockTask = {
        id: "task789",
        taskName: "操作ログエクスポート",
        taskType: TASK_TYPE.OPLOG_EXPORT,
        status: TASK_STATUS.QUEUED,
        submittedAt: new Date(),
        startedAt: null,
        endedAt: null,
        updatedAt: new Date(),
        submittedByUserId: "testUser",
        licenseId: "license123",
        targetServerId: "server789",
        targetServerName: "TestServer",
        targetVmName: "vm-test",
        targetContainerName: "container-test",
        targetHRWGroupName: "group-test",
        parametersJson: JSON.stringify({ exportStartDate: "2023-01-01", exportEndDate: "2023-01-05", maxDays: 5 }),
        resultMessage: null,
        outputBlobPath: null,
        errorMessage: null,
        errorCode: null,
      };
      let createArgs: any = undefined;
      // @ts-expect-error Prisma $transaction のモックは型安全性を保証しないため、型エラーを無視する
      prismaMock.$transaction = jest.fn(async (cb) => {
        const tx = {
          containerConcurrencyStatus: {
            findUnique: jest.fn().mockResolvedValue({}),
            create: jest.fn().mockResolvedValue({}),
          },
          task: {
            create: jest.fn().mockImplementation((args) => {
              createArgs = args;
              return mockTask;
            }),
          },
        } as unknown as typeof prismaMock;
        await cb(tx);
      });
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.OPLOG_EXPORT);
      formData.append("serverId", "server789");
      formData.append("exportStartDate", "2023-01-01");
      formData.append("exportEndDate", "2023-01-05");
      formData.append("maxExportDaysSpan", "5");
      const result = await createTask(formData);
      expect(result.success).toBe(true);
      expect(result.message).toBe(
        PORTAL_ERROR_MESSAGES.EMEC0025.replace("{0}", "TestServer").replace("{1}", "操作ログのエクスポート")
      );
      expect(createArgs.data.targetContainerName).toBe("container-test");
      expect(createArgs.data.targetHRWGroupName).toBe("group-test");
    });

    /**
     * 試験観点：タスク種別が不足している場合のエラー検証。
     * 試験対象：createTask（操作ログエクスポート）
     * 試験手順：
     *   1. taskTypeをFormDataに設定しない。
     *   2. createTaskActionを実行する。
     *   3. エラーメッセージが返ることを確認する。
     * 確認項目：
     *   - result.successがfalseであること。
     *   - result.messageがEMEC0016（タスク種別）であること。
     */
    it("異常系：タスク種別が不足している場合はエラーを返す", async () => {
      const formData = new FormData();
      formData.append("serverId", "server789");
      formData.append("exportStartDate", "2023-01-01");
      formData.append("exportEndDate", "2023-01-05");
      formData.append("maxExportDaysSpan", "5");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toContain("サーバの接続に失敗したため、タスクを開始できませんでした");
    });

    /**
     * 試験観点：共通パラメータ解析失敗（serverId不足）
     * 試験対象：設計文書「06-タスク受付処理詳細.md」共通パラメータ解析
     * 試験手順：
     * 1. serverIdを含まないFormDataを作成
     * 2. createTaskActionを実行
     * 3. EMEC0021エラーが返されることを確認
     * 確認項目：
     * - success: falseが返されること
     * - EMEC0021「サーバの接続に失敗したため、タスクを{0}できませんでした」（{0}は「開始」で置換）が返されること
     */
    it("異常系：serverIdが不足している場合はエラーを返す", async () => {
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.OPLOG_EXPORT);
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toContain("サーバの接続に失敗したため、タスクを開始できませんでした");
    });

    /**
     * 試験観点：exportStartDateが不足している場合のエラー検証。
     * 試験対象：createTask（操作ログエクスポート）
     * 試験手順：
     *   1. exportStartDateをFormDataに設定しない。
     *   2. createTaskActionを実行する。
     *   3. エラーメッセージが返ることを確認する。
     * 確認項目：
     *   - result.successがfalseであること。
     *   - result.messageがEMEC0016（開始日）であること。
     */
    it("異常系：exportStartDateが不足している場合はエラーを返す", async () => {
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.OPLOG_EXPORT);
      formData.append("serverId", "server789");
      formData.append("exportEndDate", "2023-01-05");
      formData.append("maxExportDaysSpan", "5");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toBe(
        PORTAL_ERROR_MESSAGES.EMEC0016.replace("{0}", FORM_FIELD_NAMES.START_DATE)
      );
    });

    /**
     * 試験観点：exportEndDateが不足している場合のエラー検証。
     * 試験対象：createTask（操作ログエクスポート）
     * 試験手順：
     *   1. exportEndDateをFormDataに設定しない。
     *   2. createTaskActionを実行する。
     *   3. エラーメッセージが返ることを確認する。
     * 確認項目：
     *   - result.successがfalseであること。
     *   - result.messageがEMEC0016（終了日）であること。
     */
    it("異常系：exportEndDateが不足している場合はエラーを返す", async () => {
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.OPLOG_EXPORT);
      formData.append("serverId", "server789");
      formData.append("exportStartDate", "2023-01-01");
      formData.append("maxExportDaysSpan", "5");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toBe(
        PORTAL_ERROR_MESSAGES.EMEC0016.replace("{0}", FORM_FIELD_NAMES.END_DATE)
      );
    });

    /**
     * 試験観点：日付形式不正時のエラー検証
     * 試験対象：createTask（操作ログエクスポート）
     * 試験手順：
     *   1. 不正な形式の開始日を設定する
     *   2. createTaskActionを実行する
     *   3. エラーメッセージがEMEC0016であることを確認する
     * 確認項目：
     *   - result.successがfalseであること
     *   - result.messageがEMEC0016（開始日）であること
     */
    it("異常系：開始日の形式が不正な場合はEMEC0016を返す", async () => {
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.OPLOG_EXPORT);
      formData.append("serverId", "server789");
      formData.append("exportStartDate", "2023/01/01"); // 不正な形式
      formData.append("exportEndDate", "2023-01-05");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toBe(
        formatMessage(PORTAL_ERROR_MESSAGES.EMEC0016, [FORM_FIELD_NAMES.START_DATE])
      );
    });

    /**
     * 試験観点：終了日 < 開始日の場合のエラー検証
     * 試験対象：createTask（操作ログエクスポート）
     * 試験手順：
     *   1. 終了日が開始日より前の日付を設定する
     *   2. createTaskActionを実行する
     *   3. エラーメッセージがEMEC0024であることを確認する
     * 確認項目：
     *   - result.successがfalseであること
     *   - result.messageがEMEC0024（日付順序エラー）であること
     */
    it("異常系：終了日が開始日より前の場合はEMEC0024を返す", async () => {
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.OPLOG_EXPORT);
      formData.append("serverId", "server789");
      formData.append("exportStartDate", "2023-01-10");
      formData.append("exportEndDate", "2023-01-05"); // 開始日より前
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toBe(PORTAL_ERROR_MESSAGES.EMEC0024);
    });

    /**
     * 試験観点：最大日数設定取得失敗時のエラー検証。
     * 試験対象：createTask（操作ログエクスポート）
     * 試験手順：
     *   1. データベースからの最大日数設定取得をnullで失敗させる。
     *   2. createTaskActionを実行する。
     *   3. エラーメッセージが返ることを確認する。
     * 確認項目：
     *   - result.successがfalseであること。
     *   - result.messageがEMEC0021（サーバ接続失敗）であること。
     */
    it("異常系：最大日数設定取得失敗時はEMEC0021を返す", async () => {
      const { serverDataLov } = setupCommonMocks();
      serverDataLov.fetchLov.mockImplementation((code: string) => {
        if (code === "OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN") return Promise.resolve(null);
        return Promise.resolve(null);
      });
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.OPLOG_EXPORT);
      formData.append("serverId", "server789");
      formData.append("exportStartDate", "2023-01-01");
      formData.append("exportEndDate", "2023-01-05");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toContain("サーバの接続に失敗したため、タスクを開始できませんでした");
    });

    /**
     * 試験観点：日付範囲が最大日数を超える場合のエラー検証。
     * 試験対象：createTask（操作ログエクスポート）
     * 試験手順：
     *   1. exportStartDateとexportEndDateの日数差がmaxExportDaysSpanを超える値を設定する。
     *   2. createTaskActionを実行する。
     *   3. エラーメッセージがEMEC0020（最大日数超過）であることを確認する。
     * 確認項目：
     *   - result.successがfalseであること。
     *   - result.messageがEMEC0020（最大日数超過）であること。
     */
    it("異常系：日付範囲が最大日数を超える場合はエラーを返す", async () => {
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.OPLOG_EXPORT);
      formData.append("serverId", "server789");
      formData.append("exportStartDate", "2023-01-01");
      formData.append("exportEndDate", "2023-01-10");
      formData.append("maxExportDaysSpan", "5");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      const { formatMessage } = require("@/app/lib/utils");
      expect(result.message).toBe(formatMessage(PORTAL_ERROR_MESSAGES.EMEC0020, ["5", "5"]));
    });

    /**
     * 試験観点：DBトランザクション失敗時のエラー検証。
     * 試験対象：createTask（操作ログエクスポート）
     * 試験手順：
     *   1. prisma.$transactionが例外を投げるようにモックする。
     *   2. createTaskActionを実行する。
     *   3. エラーメッセージがEMEC0006であることを確認する。
     * 確認項目：
     *   - result.successがfalseであること。
     *   - result.messageがEMEC0006であること。
     */
    it("異常系：DBトランザクション失敗時はEMEC0006を返す", async () => {
      const prisma = require("@/app/lib/prisma").default;
      const txSpy = jest.spyOn(prisma, "$transaction").mockRejectedValue(new Error("DB error"));
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.OPLOG_EXPORT);
      formData.append("serverId", "server789");
      formData.append("exportStartDate", "2023-01-01");
      formData.append("exportEndDate", "2023-01-05");
      formData.append("maxExportDaysSpan", "5");
      // mock DB异常分支
      // 需mock ServerDataLov.fetchLov 返回合法
      const { serverDataLov } = setupCommonMocks();
      serverDataLov.fetchLov.mockImplementation((code: string) => {
        if (code === "OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN") return Promise.resolve({ value: "5" });
        return Promise.resolve(null);
      });
      (prismaMock.task.create as jest.Mock).mockRejectedValue(new Error("DB error"));
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      // 根据设计文档第120行：DB事务失败时应返回EMEC0021
      expect(result.message).toContain("サーバの接続に失敗したため、タスクを開始できませんでした");
      txSpy.mockRestore();
    });

    /**
     * 試験観点：コンテナがBUSYの場合のエラー検証。
     * 試験対象：createTask（操作ログエクスポート）
     * 試験手順：
     *   1. ServerDataTasks.getContainerStatusがBUSYを返すようにモックする。
     *   2. 正常なパラメータでcreateTaskActionを実行する。
     *   3. EMEC0022エラーが返ることを確認する。
     * 確認項目：
     *   - result.successがfalseであること。
     *   - result.messageがEMEC0022であること。
     */
    it("異常系：コンテナがBUSYの場合はEMEC0022を返す", async () => {
      const { serverDataTasks } = setupCommonMocks();
      serverDataTasks.getContainerStatus.mockResolvedValue(CONTAINER_STATUS.BUSY);
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.OPLOG_EXPORT);
      formData.append("serverId", "server789");
      formData.append("exportStartDate", "2023-01-01");
      formData.append("exportEndDate", "2023-01-05");
      formData.append("maxExportDaysSpan", "5");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toBe(
        PORTAL_ERROR_MESSAGES.EMEC0022.replace("{0}", "TestServer")
      );
    });

    /**
     * 試験観点：コンテナステータスが未知の文字列の場合のエラー検証
     * 試験対象：createTask（管理項目定義エクスポート）
     * 試験手順：
     *   1. ServerDataTasks.getContainerStatusが未知の文字列を返すようにモックする
     *   2. 正常なパラメータでcreateTaskActionを実行する
     *   3. EMEC0021エラーが返ることを確認する
     * 確認項目：
     *   - result.successがfalseであること
     *   - result.messageがEMEC0021（サーバ接続失敗）であること
     */
    it("異常系：コンテナステータスが未知の文字列の場合はEMEC0021を返す", async () => {
      const { serverDataTasks } = setupCommonMocks();
      serverDataTasks.getContainerStatus.mockResolvedValue("UNKNOWN_STATUS");
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_EXPORT);
      formData.append("serverId", "server789");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toContain("サーバの接続に失敗したため、タスクを開始できませんでした");
    });

    /**
     * 試験観点：コンテナステータスが空文字列の場合のエラー検証
     * 試験対象：createTask（操作ログエクスポート）
     * 試験手順：
     *   1. ServerDataTasks.getContainerStatusが空文字列を返すようにモックする
     *   2. 正常なパラメータでcreateTaskActionを実行する
     *   3. EMEC0021エラーが返ることを確認する
     * 確認項目：
     *   - result.successがfalseであること
     *   - result.messageがEMEC0021（サーバ接続失敗）であること
     */
    it("異常系：コンテナステータスが空文字列の場合はEMEC0021を返す", async () => {
      const { serverDataTasks } = setupCommonMocks();
      serverDataTasks.getContainerStatus.mockResolvedValue("");
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.OPLOG_EXPORT);
      formData.append("serverId", "server789");
      formData.append("exportStartDate", "2023-01-01");
      formData.append("exportEndDate", "2023-01-05");
      formData.append("maxExportDaysSpan", "5");
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toContain("サーバの接続に失敗したため、タスクを開始できませんでした");
    });

    /**
     * 試験観点：コンテナステータスが数値文字列の場合のエラー検証
     * 試験対象：createTask（管理項目定義インポート）
     * 試験手順：
     *   1. ServerDataTasks.getContainerStatusが数値文字列を返すようにモックする
     *   2. 正常なパラメータでcreateTaskActionを実行する
     *   3. EMEC0021エラーが返ることを確認する
     * 確認項目：
     *   - result.successがfalseであること
     *   - result.messageがEMEC0021（サーバ接続失敗）であること
     */
    it("異常系：コンテナステータスが数値文字列の場合はEMEC0021を返す", async () => {
      const { serverDataTasks } = setupCommonMocks();
      serverDataTasks.getContainerStatus.mockResolvedValue("123");
      const formData = new FormData();
      formData.append("taskType", TASK_TYPE.MGMT_ITEM_IMPORT);
      formData.append("serverId", "server789");
      formData.append("file", new File(["test content"], "test.csv", { type: "text/csv" }));
      const result = await createTask(formData);
      expect(result.success).toBe(false);
      expect(result.message).toContain("サーバの接続に失敗したため、タスクを開始できませんでした");
    });
  });

  /**
   * 試験観点：ContainerConcurrencyStatusが存在しない場合に新規作成されること。
   * 試験対象：createTask（全タスク種別共通）
   * 試験手順：
   *   1. prismaMock.$transactionでtx.containerConcurrencyStatus.createをモック。
   *   2. createが呼ばれることを検証。
   * 確認項目：
   *   - createが1回呼ばれること。
   */
  it("ContainerConcurrencyStatusが存在しない場合は新規作成される", async () => {
    // Arrange
    const formData = new FormData();
    formData.append("taskType", TASK_TYPE.MGMT_ITEM_EXPORT);
    formData.append("serverId", "server789");
    // 依存モック設定（トランザクション分岐に入るため）
    const { serverDataTasks, serverDataServers } = setupCommonMocks();
    serverDataTasks.getContainerStatus.mockResolvedValue(null);
    serverDataServers.getServerDetailsForTask.mockResolvedValue({
      name: "TestServer",
      licenseId: "license123",
      azureVmName: "vm-test",
      dockerContainerName: "container-test",
      hrwGroupName: "group-test",
    });
    // mock session
    (getIronSession as jest.Mock).mockResolvedValue({ user: { userId: "testUser", licenseId: "license123", tz: "Asia/Tokyo" } });

    // Service Bus模拟
    (ServiceBusActions.sendMessage as jest.Mock).mockResolvedValue(undefined);
    // mock tx
    const tx = {
      containerConcurrencyStatus: {
        findUnique: jest.fn().mockResolvedValue(null),
        create: jest.fn().mockResolvedValue({}),
      },
      task: {
        create: jest.fn().mockResolvedValue({ id: "task123" }),
      },
    } as unknown as typeof prismaMock;
    // @ts-expect-error Prisma $transaction のモックは型安全性を保証しないため、型エラーを無視する
    prismaMock.$transaction = jest.fn(cb => cb(tx));
    // Act
    await createTask(formData);
    // Assert
    expect(tx.containerConcurrencyStatus.create).toHaveBeenCalledTimes(1);
  });

  /**
   * 試験観点：taskNameが指定されたフォーマットで正しく生成されること
   * 試験対象：createTask関数のタスク名生成機能（全タスク種別共通）
   * 試験手順：
   * 1. prismaMock.$transactionでtx.task.createをモック設定
   * 2. createTaskAction関数を実行
   * 3. data.taskNameが"{ServerName}-{タスク種別}-{YYYYMMDDHHmmss}"形式であることを検証
   * 確認項目：
   * - taskNameがサーバー名、タスク種別、タイムスタンプを含む正しいフォーマットであること
   * - タイムスタンプ部分が14桁の数字であること
   */
  it("taskNameが正しいフォーマットで生成される", async () => {
    // Arrange
    const formData = new FormData();
    formData.append("taskType", TASK_TYPE.MGMT_ITEM_EXPORT);
    formData.append("serverId", "server789");
    // 依存モック設定（トランザクション分岐に入るため）
    const { serverDataTasks, serverDataServers } = setupCommonMocks();
    serverDataTasks.getContainerStatus.mockResolvedValue(null);
    serverDataServers.getServerDetailsForTask.mockResolvedValue({
      name: "TestServer",
      licenseId: "license123",
      azureVmName: "vm-test",
      dockerContainerName: "container-test",
      hrwGroupName: "group-test",
    });
    // mock session
    (getIronSession as jest.Mock).mockResolvedValue({ user: { userId: "testUser", licenseId: "license123", tz: "Asia/Tokyo" } });

    // Service Bus模拟
    (ServiceBusActions.sendMessage as jest.Mock).mockResolvedValue(undefined);
    let capturedTaskName = "";
    const tx = {
      containerConcurrencyStatus: {
        findUnique: jest.fn().mockResolvedValue(null),
        create: jest.fn().mockResolvedValue({}),
      },
      task: {
        create: jest.fn().mockImplementation(({ data }) => {
          capturedTaskName = data.taskName;
          return { id: "task123" };
        }),
      },
    } as unknown as typeof prismaMock;
    // @ts-expect-error Prisma $transaction のモックは型安全性を保証しないため、型エラーを無視する
    prismaMock.$transaction = jest.fn(cb => cb(tx));
    // Act
    await createTask(formData);
    // Assert
    expect(capturedTaskName).toMatch(/^TestServer\-管理項目定義のエクスポート\-\d{14}$/);
  });

  /**
   * 試験観点：予期せぬ内部例外発生時のエラー検証
   * 試験対象：createTask（全タスク種別共通）
   * 試験手順：
   *   1. getIronSessionが予期せぬ例外を投げるようにモックする
   *   2. createTaskActionを実行する
   *   3. エラーメッセージがEMEC0027であることを確認する
   * 確認項目：
   *   - result.successがfalseであること
   *   - result.messageがEMEC0027（予期せぬ例外）であること
   */
  it("異常系：予期せぬ内部例外発生時はEMEC0027を返す", async () => {
    const { getIronSession } = require("iron-session");
    (getIronSession as jest.Mock).mockRejectedValue(new Error("Unexpected internal error"));

    const formData = new FormData();
    formData.append("taskType", TASK_TYPE.MGMT_ITEM_EXPORT);
    formData.append("serverId", "server789");
    const result = await createTask(formData);
    expect(result.success).toBe(false);
    expect(result.message).toContain("サーバの接続に失敗したため、タスクを開始できませんでした");
  });

});