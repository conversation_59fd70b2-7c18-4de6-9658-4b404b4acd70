/**
 * @fileoverview Prisma Clientのインスタンスを初期化し、全Functionで共通利用できるようエクスポートするモジュール
 * @description
 * Prisma Clientのインスタンスを初期化し、全Functionで共通利用できるようエクスポートするモジュール。
 * データベース接続の一元管理・型安全なORM操作を実現。
 * Prisma Clientのシングルトン化により、データベース接続の効率化・リソース最適化を図る。
 * 型安全なデータベース操作・自動補完・スキーマ同期を担保。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { PrismaClient } from "@prisma/client";

export const prisma = new PrismaClient();
