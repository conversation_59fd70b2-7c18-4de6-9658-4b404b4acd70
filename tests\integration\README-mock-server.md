# Azure Automation Mock Server

Azure Automation Mock Server 是为 JCS Endpoint 系统集成测试而设计的模拟服务器，用于模拟 Azure Automation REST API 的各种操作。

## 功能特性

- **完整的 Azure Automation API 模拟**：支持作业创建、状态查询、停止等核心操作
- **状态自动变化**：模拟真实的作业状态变化流程（New → Activating → Running）
- **失败场景模拟**：支持通过 API 动态设置各种失败场景，包括不同的 HTTP 状态码和错误响应
- **测试辅助功能**：提供手动设置作业状态、清理作业等测试辅助端点
- **无认证设计**：简化测试环境，无需复杂的认证配置

## 快速开始

### 1. 安装依赖

```bash
cd tests/integration
npm install
```

### 2. 启动 Mock Server

```bash
# 使用默认配置启动
npm run start-mock-server

# 使用自定义配置启动
MOCK_SERVER_PORT=3001 MOCK_API_KEY=my-api-key npm run start-mock-server

# 开发模式启动
npm run mock-server:dev
```

### 3. 配置 Azure Functions

在 Azure Functions 的配置文件中设置以下环境变量：

```json
{
  "Values": {
    "AZURE_MANAGEMENT_BASE_URL": "http://localhost:3001"
  }
}
```

## API 端点

### 生产 API 端点

这些端点模拟真实的 Azure Automation REST API：

#### 创建作业
```
PUT /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Automation/automationAccounts/{automationAccount}/jobs/{jobName}
```

#### 获取作业状态
```
GET /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Automation/automationAccounts/{automationAccount}/jobs/{jobName}
```

#### 停止作业
```
POST /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Automation/automationAccounts/{automationAccount}/jobs/{jobName}/stop
```

### 测试辅助端点

这些端点仅用于测试，不存在于真实的 Azure API 中：

#### 健康检查
```
GET /health
```

#### 清理所有作业
```
DELETE /mock/jobs
```

#### 手动设置作业状态
```
PUT /mock/jobs/{jobName}/status
Body: { "status": "Completed" }
```

#### 获取所有作业
```
GET /mock/jobs
```

#### 设置失败场景
```
PUT /mock/failure-scenarios
Body: {
  "endpoint": "/subscriptions/.../jobs/test-job",
  "method": "GET",
  "statusCode": 500,
  "errorResponse": {
    "error": {
      "code": "InternalServerError",
      "message": "Simulated failure"
    }
  },
  "enabled": true
}
```

#### 清理失败场景
```
DELETE /mock/failure-scenarios
```

#### 获取失败场景
```
GET /mock/failure-scenarios
```

## 使用示例

### 在集成测试中使用

```typescript
import { MockServerHelper } from '../support/mock-server-helper';
import { MockJobStatus } from '../support/azure-automation-mock-server';

const mockHelper = new MockServerHelper();

// 启动服务器
await mockHelper.startServer();

// 清理作业和失败场景
await mockHelper.clearAllJobs();
await mockHelper.clearFailureScenarios();

// 设置作业状态
await mockHelper.setJobStatus('test-job', MockJobStatus.Completed);

// 设置失败场景
await mockHelper.setFailureScenario(
  '/subscriptions/.../jobs/test-job',
  'GET',
  500,
  { error: { code: 'InternalServerError', message: 'Test failure' } }
);

// 等待作业状态
await mockHelper.waitForJobStatus('test-job', MockJobStatus.Running, 5000);

// 停止服务器
await mockHelper.stopServer();
```

### 在 Azure Functions 中使用

改造后的 `authenticatedFetch` 函数会自动根据环境变量选择连接到 Mock Server 或真实的 Azure API：

```typescript
// 这个调用会自动路由到 Mock Server（如果配置了本地模式）
const response = await authenticatedFetch(
  '/subscriptions/test-sub/resourceGroups/test-rg/providers/Microsoft.Automation/automationAccounts/test-account/jobs/my-job',
  { method: 'GET' }
);
```

## 作业状态流程

Mock Server 模拟以下作业状态变化流程：

1. **New** - 作业刚创建
2. **Activating** - 作业正在激活（100ms 后自动变化）
3. **Running** - 作业正在运行（500ms 后自动变化）
4. **Completed/Failed/Stopped** - 终态（需要手动设置或通过停止操作）

## 失败场景模拟

Mock Server 支持动态设置失败场景，用于测试各种错误情况：

### 支持的失败类型

- **HTTP 状态码错误**：400, 401, 403, 404, 500, 503 等
- **自定义错误响应**：可以设置符合 Azure API 格式的错误响应
- **特定端点失败**：可以针对特定的 URL 路径和 HTTP 方法设置失败

### 常见失败场景示例

```typescript
// 模拟服务器内部错误
await mockHelper.setFailureScenario(
  '/subscriptions/.../jobs/test-job',
  'GET',
  500,
  {
    error: {
      code: 'InternalServerError',
      message: 'The service is temporarily unavailable'
    }
  }
);

// 模拟权限不足错误
await mockHelper.setFailureScenario(
  '/subscriptions/.../jobs/test-job/stop',
  'POST',
  403,
  {
    error: {
      code: 'Forbidden',
      message: 'Insufficient permissions to stop the job'
    }
  }
);

// 模拟请求参数错误
await mockHelper.setFailureScenario(
  '/subscriptions/.../jobs/invalid-job',
  'PUT',
  400,
  {
    error: {
      code: 'BadRequest',
      message: 'Invalid runbook name specified'
    }
  }
);
```

## 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `MOCK_SERVER_PORT` | `3001` | Mock Server 监听端口 |
| `AZURE_MANAGEMENT_BASE_URL` | `https://management.azure.com` | Azure Management API 基础URL |

## 故障排除

### 端口冲突
如果端口 3001 被占用，可以通过环境变量指定其他端口：
```bash
MOCK_SERVER_PORT=3002 npm run start-mock-server
```

### 连接失败
确保 `AZURE_MANAGEMENT_BASE_URL` 指向正确的 Mock Server 地址（如 `http://localhost:3001`）。

## 开发和扩展

### 添加新的 API 端点

1. 在 `azure-automation-mock-server.ts` 的 `setupRoutes()` 方法中添加新路由
2. 实现对应的处理方法
3. 在集成测试中添加相应的测试用例

### 自定义作业行为

可以通过修改 `createJob()` 和 `updateJobStatus()` 方法来自定义作业的行为和状态变化逻辑。

## 注意事项

- Mock Server 仅用于开发和测试环境，不应在生产环境中使用
- 作业数据和失败场景配置存储在内存中，服务器重启后会丢失
- Mock Server 不提供认证机制，适合隔离的测试环境使用
- 失败场景设置会影响所有匹配的请求，测试完成后记得清理
