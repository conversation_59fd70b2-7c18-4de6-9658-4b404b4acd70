---
mode: 'agent'
---
## Monorepo项目实施指南 (无外部共享包，本地Git部署，无CI/CD)

**版本:** 2.0
**日期:** 2024年5月29日

### 1. 引言与目标

本指南为 "JCS 端点资产与任务管理系统" 项目提供了一套在特定约束条件下实施Monorepo（单一代码仓库）的策略和方法。主要目标是建立一个清晰、可维护的项目结构，支持开发团队的协作，并为AI编程助手提供有效的上下文信息。

**核心约束：**

*   **无外部共享代码包 (`packages/`)**: 代码复用限制在各应用或自动化单元内部。
*   **Next.js应用通过本地Git部署**: 保持现有的本地Git推送至Azure App Service的部署方式。
*   **Azure Functions应用通过Zip包部署**: 生成Zip包供部署。
*   **Azure Automation Runbooks通过本地脚本部署/打包**: 通过本地PowerShell脚本或打包脚本交付。
*   **不使用CI/CD流水线**: 所有构建、打包和部署发起操作在本地进行。
*   **代码不上传至公共云端Git服务**: Monorepo主要用于内部版本控制。

**目标读者**: 项目开发团队成员，AI编程助手。

### 2. 项目整体目录结构

```plaintext
jcs-endpoint-monorepo/
├── .git/                     # Monorepo 主Git仓库
├── .vscode/                  # VS Code 工作区配置 (可选)
│   ├── launch.json
│   └── settings.json
├── apps/                     # 存放可独立部署的应用程序单元
│   ├── jcs-endpoint-nextjs/  # Next.js 门户工程
│   │   ├── .git/             # Next.js 应用的独立Git仓库 (用于本地Git部署到Azure)
│   │   ├── public/
│   │   ├── src/
│   │   │   ├── components/   # UI组件
│   │   │   ├── lib/          # 应用内部工具函数、类型等
│   │   │   ├── pages/        # 页面路由
│   │   │   └── ...
│   │   ├── .env.local        # 本地环境变量 (gitignore)
│   │   ├── .gitignore        # Next.js项目的.gitignore
│   │   ├── next.config.js
│   │   ├── package.json
│   │   └── tsconfig.json
│   └── jcs-backend-services/ # 后端服务 (Azure Functions)
│       └── functions/          # Azure Function App 项目根目录
│           ├── TaskOrchestratorFunc/
│           │   ├── index.ts
│           │   └── function.json
│           ├── StatusProcessorFunc/
│           │   └── ...
│           ├── (其他 Functions...)
│           ├── lib/              # Function App 内部共享的辅助代码
│           ├── host.json         # Function App 全局配置
│           ├── local.settings.json # 本地开发设置 (gitignore)
│           ├── package.json      # Function App 的依赖
│           └── tsconfig.json
├── automation/               # Azure Automation 资源
│   ├── runbooks/
│   │   ├── Export-Data.ps1
│   │   └── lib/              # Runbook辅助脚本
│   │       └── Common-Helper.ps1
│   ├── modules/              # 自定义PowerShell模块
│   │   └── JcsAutomationTools/
│   │       └── ...
├── docs/                     # 核心技术文档 (中文内容, 英文文件名)
│   ├── README.md             # 文档库总览
│   ├── architecture/
│   │   ├── system-architecture.md # 系统架构文档 (源文档的Markdown版本)
│   │   ├── adrs/                  # 架构决策记录
│   │   └── diagrams/              # 架构图 (Mermaid源文件或图片)
│   ├── apis/                   # API规范 (若有)
│   │   └── openapi.v1.yaml
│   ├── data-models/            # 数据模型详细说明
│   │   ├── task-table.md
│   │   ├── container-concurrency-status-table.md
│   │   └── lov-table.md
│   │   └── (其他表名.md)
│   ├── components/             # 各核心组件的详细说明
│   │   ├── apps-nextjs.md
│   │   ├── apps-backend-functions.md
│   │   ├── function-task-orchestrator.md
│   │   ├── function-status-processor.md
│   │   ├── function-task-cancellation.md
│   │   ├── function-runbook-timeout-monitor.md
│   │   ├── automation-runbook-export-data.md
│   │   ├── (其他Function和Runbook的详细文档)
│   │   └── login.md
│   │   └── (其他前端主要功能模块.md)
│   ├── guides/                 # 开发、部署等指南
│   │   ├── development-setup.md
│   │   ├── coding-standards.md
│   │   ├── deployment-procedures.md # 关键！包含各组件手动部署步骤
│   │   ├── functional-specification-migration-guide.md # 機能仕様書迁移指南
│   │   └── detailed-design-migration-guide.md      # 詳細設計書迁移指南
│   └── glossary.md             # 项目术语表
├── docs-delivery/            # 存放交付用的日文文档 (Word, Excel)
│   ├── 機能仕様書/
│   │   └── JCSエンドポイント資産とタスク管理システム機能仕様書_vX.X.docx
│   ├── 詳細設計書/
│   │   └── JCSエンドポイント資産とタスク管理システム詳細設計書_vX.X.xlsx
│   └── templates/            # (可选) Word/Excel 模板
├── scripts/                  # 本地辅助脚本
│   ├── build-and-package-functions.sh  # 构建并打包Azure Functions
│   ├── deploy-runbooks-locally.ps1     # 本地部署Runbooks (或打包)
│   └── (其他辅助脚本)
├── .gitignore                # Monorepo主Git仓库的.gitignore
├── package.json              # Monorepo根package.json (可选, 轻量级)
└── README.md                 # 项目总览README
```

### 3. Git 版本控制策略

*   **主Monorepo仓库 (`jcs-endpoint-monorepo/.git`)**:
    *   作为团队内部开发和版本控制的中心。
    *   所有项目代码 (`apps/`, `automation/`)、脚本 (`scripts/`)、文档 (`docs/`, `docs-delivery/`) 均在此仓库中进行版本管理。
    *   推荐使用 `main` (稳定版) 和 `develop` (开发版) 分支，并为特性开发创建功能分支。
    *   **`.gitignore` (主仓库)**:
        *   全局忽略：`node_modules/`, `*.env*`, `local.settings.json`, `dist/` (脚本输出目录), `*.zip` (打包产物), `*.log`。
        *   特别忽略Next.js应用的内部Git目录：`apps/jcs-endpoint-nextjs/.git/`

*   **Next.js应用的内部Git仓库 (`apps/jcs-endpoint-nextjs/.git`)**:
    *   **目的**: 仅用于Azure App Service的“本地Git部署”功能。
    *   **初始化**: 在 `apps/jcs-endpoint-nextjs/` 目录下执行 `git init`。
    *   **远程**: 添加Azure App Service的Git远程仓库地址。
    *   **内容**: 只包含Next.js应用本身的文件。其 `.gitignore` 文件应包含 `.next/`, `node_modules/` (如果Kudu负责构建)等。
    *   **工作流程**:
        1.  在主Monorepo中完成Next.js应用的开发和测试。
        2.  确保 `apps/jcs-endpoint-nextjs/` 目录下的代码是最新的。
        3.  `cd apps/jcs-endpoint-nextjs`
        4.  （可选，如果App Service不自行构建）运行构建命令：`pnpm build` (或 `npm run build`)。
        5.  提交变更到此内部Git仓库：`git add . && git commit -m "Deployment commit"`。
        6.  推送到Azure：`git push azure <branch>`。

### 4. 文档策略 (`docs/` 目录)

`docs/` 目录是项目知识的核心，为开发团队和AI编程助手提供一致且准确的信息来源。

*   **语言**: 所有Markdown文件的内容使用 **中文** 编写。
*   **文件名和目录名**: 使用 **英文**，遵循 `kebab-case` (小写字母加连字符)。
*   **术语**: 技术组件和服务名称优先使用官方英文全称（如 Azure Service Bus, Keycloak）。项目特定术语参考 `docs/glossary.md`。
*   **内容来源**: 主要来源于 `system-architecture.md`、`機能仕様書` 和 `詳細設計書`，并随项目进展持续更新。
*   **目标**: 作为生成日文交付文档 (`機能仕様書.docx`, `詳細設計書.xlsx`) 的主要信息源。

**各子目录详细说明：**

*   **`docs/README.md`**: 文档库的介绍、结构说明、如何使用本文档，以及对AI助手的提示（例如，如何引用特定文档）。
*   **`docs/architecture/`**:
    *   `system-architecture.md`: 核心架构文档，基于您提供的源文档进行演进和细化。包含设计哲学、目标、核心约束、整体架构图 (Mermaid)、核心流程图 (Mermaid)、各组件的高层职责描述。
    *   `adrs/`: 存放架构决策记录 (ADR)，记录重要的技术选型、设计选择及其理由。文件名如 `001-monorepo-structure.md`。
    *   `diagrams/`: 存放Mermaid源码文件 (`.md` 或 `.mermaid`) 或导出的图片 (PNG, SVG) 文件，如系统架构图、流程图、UI草图等。
*   **`docs/apis/`**:
    *   `openapi.v1.yaml`: 如果系统包含HTTP API（例如Next.js后端API或HTTP触发的Azure Functions），使用OpenAPI 3.x规范详细描述。这是AI理解API的关键。
*   **`docs/data-models/`**:
    *   为每个数据库表 (如 `task-table.md`, `container-concurrency-status-table.md`, `lov-table.md`) 创建独立的Markdown文件。
    *   内容包括：表注释、列名、数据类型、是否可空、主键/外键、默认值、检查约束、列注释、示例数据等。
    *   也适用于描述Service Bus消息的JSON结构等。
*   **`docs/components/`**:
    *   **这是AI编程和理解系统功能细节的核心区域。**
    *   **`apps-nextjs.md`**: Next.js门户应用的整体功能描述、主要页面模块、通用UI/UX原则、与Keycloak的集成方式、与后端服务的交互模式。
    *   **`apps-backend-functions.md`**: 整个Azure Function App (`jcs-backend-services/functions/`) 的概述，包括其角色、包含的主要Functions列表及其职责、`host.json`的关键配置、内部`lib/`目录的用途。
    *   **`function-[function-name].md`**: (例如 `function-task-orchestrator.md`, `function-status-processor.md`)
        *   **目的 (Mokuteki)**: 清晰描述该Function的作用。
        *   **触发器 (Trigger)**: 类型 (如Service Bus Queue) 和具体配置 (队列名)。
        *   **输入 (Input)**: 消息体结构、绑定参数等。
        *   **处理逻辑 (Processing Logic)**: 详细的步骤化描述，可辅以简单的流程图 (Mermaid) 或伪代码。**聚焦“做什么”，而非“如何实现”的底层代码细节。**
        *   **输出 (Output)**: 产生的结果，如数据库写入、发送到其他队列的消息、对存储的操作。
        *   **前提条件 (Preconditions)**: 执行该功能前必须满足的条件。
        *   **制约事项 (Constraints/Limitations)**: 功能的技术或业务限制。
        *   **注意事项 (Notes/Considerations)**: 其他重要说明。
        *   **错误处理 (Error Handling)**: 主要的异常情况及其处理方式。
    *   **`automation-runbook-[runbook-name].md`**: (例如 `automation-runbook-export-data.md`)
        *   类似Function的结构，描述Runbook的目的、输入参数、核心执行步骤（调用Docker工具、文件操作等）、依赖的Automation模块或资产、输出结果和错误处理。
    *   **`automation-module-[module-name].md`**: (例如 `automation-module-jcsautomationtools.md`) 如果自定义了PowerShell模块，描述其功能、提供的Cmdlet和用法。
*   **`docs/guides/`**:
    *   `development-setup.md`: 开发环境配置指南。
    *   `coding-standards.md`: 编码规范、命名约定、注释风格等。
    *   `testing-strategy.md`: 项目的测试方法和策略（即使是手动测试）。
    *   `deployment-procedures.md`: **极其重要。** 详细分步说明如何为每个组件（Next.js应用、Functions App、Runbooks）进行本地构建、打包、以及执行“本地Git部署”或准备交付给客户的Zip包的过程。包括所有命令和注意事项。
    *   `functional-specification-migration-guide.md`: 指导如何将日文 `機能仕様書` 内容映射到 `docs/` 目录。
    *   `detailed-design-migration-guide.md`: 指导如何将日文 `詳細設計書` (Excel) 内容映射到 `docs/` 目录。
*   **`glossary.md`**: 项目术语表，包含中英日对照（如果适用）和定义。

### 5. 从现有交付文档迁移到 `docs/`

*   **`機能仕様書` (functional_specifications_origin.md) 的迁移**:
    *   **概要/开发背景/目的**: 迁移到 `docs/architecture/system-architecture.md` 的引言和目标部分。
    *   **系统构成图 (図4.2)**: Mermaid代码迁移到 `docs/architecture/diagrams/system-architecture-diagram.md` (或类似文件名)，并在 `system-architecture.md` 中引用。
    *   **機能一覧 (表4.1)**: 迁移到 `docs/components/apps-nextjs.md` 作为功能列表，每个条目可以链接到更详细的组件文档。
    *   **各機能詳細 (ログイン, メイン画面, サーバ一覧等)**:
        *   每个機能详细内容（目的、前提条件、制限事項、注意事項、機能詳細、画面項目、エラー処理等）分别迁移到 `docs/components/` 下对应的Markdown文件。例如，`ログイン` 功能的详细信息写入 `docs/components/login.md`。
        *   **画面イメージ**: 截图保存到 `docs/architecture/diagrams/` 或 `docs/components/[component-name]/assets/`，并在Markdown中引用。
        *   **处理流程/手順**: 转换为中文的步骤描述或伪代码。
        *   **エラー処理 (表格)**: 转换为Markdown表格。
    *   **データベース設計**: 迁移到 `docs/data-models/` 下的对应表文件 (e.g., `user-table.md`, `task-table.md`)。
    *   **ストレージ設計, Azure Service Bus, Azure Functions, Azure Automation**: 这些章节的描述内容补充和完善 `docs/architecture/system-architecture.md` 中对应组件的描述，以及各个具体Function (`docs/components/function-*.md`) 和Runbook (`docs/components/automation-runbook-*.md`) 的文档。
    *   **セキュリティ (ユーザー認証)**: 认证流程和机制的核心描述放入 `docs/components/login.md` 和 `docs/architecture/security.md` (如果单独创建)。
    *   **その他 (技術構成, エラーリスト, 環境変数)**:
        *   技術構成: `docs/architecture/technology-stack.md` 或 `README.md`。
        *   エラーリスト: `docs/references/error-codes.md` 或分散到各组件文档。
        *   環境変数: `docs/guides/environment-variables.md` (仅列出变量名和描述，不含实际值)。

*   **`詳細設計書` (Excel/Google Sheets) 的迁移**:
    *   **目标**: 将Excel中的结构化信息提取并转换为Markdown格式，使其成为AI可读的上下文。
    *   **画面項目一覧**: 为每个主要页面或交互界面，在对应的 `docs/components/[screen-component-name].md` 文件中，使用Markdown表格描述UI元素（ID、名称、类型、主要行为、数据绑定等）。
    *   **处理逻辑细化**: 如果Excel中有比機能仕様書更详细的业务逻辑步骤或规则，将其提炼并用中文描述（或伪代码）补充到相应功能组件的 `docs/components/[component-name].md` 的 "处理逻辑" 或 "业务规则" 部分。
    *   **API接口定义**: 如果有内部API（例如Next.js后端调用Functions，或Functions之间调用），其详细定义（参数、请求/响应体示例）应迁移到 `docs/apis/openapi.v1.yaml`。
    *   **数据库访问细节**: 不直接复制SQL。而是确保 `docs/data-models/` 中的表定义足够详细，并在组件文档的逻辑描述中说明数据如何被查询或修改。
    *   **复杂流程图**: 如果Excel中有流程图，尝试用Mermaid在对应的组件文档中重绘，或截图保存在 `docs/architecture/diagrams/` 并引用。
    *   **参考**: `docs/guides/detailed-design-migration-guide.md` 将提供更具体的从Excel到Markdown的转换技巧。

### 6. 本地脚本 (`scripts/`)

*   **`build-and-package-functions.sh`**: 负责构建 (如TS编译) Azure Functions 应用，并将其打包成一个部署用的Zip文件。
*   **`deploy-runbooks-locally.ps1`**: (或 `package-runbooks-for-delivery.sh`)
    *   如果直接部署：连接到Azure，将 `automation/runbooks/` 和 `automation/modules/` 下的资源发布到指定的Azure Automation Account。
    *   如果打包交付：将 `automation/` 目录下的内容打包成Zip文件，供客户手动处理。
*   **(可选) `prepare-nextjs-for-deployment.sh`**: 辅助开发者执行 `apps/jcs-endpoint-nextjs/` 内部Git仓库的提交和推送到Azure的步骤。

### 7. AI编程助手的使用

*   **主要输入**: `docs/` 目录下的所有Markdown文件，特别是 `system-architecture.md`, `openapi.v1.yaml` (如果存在), 以及 `components/` 和 `data-models/` 下的具体文件。
*   **指令示例**:
    *   "根据 `docs/components/function-task-orchestrator.md` 和 `docs/data-models/task-table.md`，为TaskOrchestrator函数生成Node.js的数据库操作部分代码。"
    *   "请基于 `docs/components/apps-nextjs.md` 中关于登录页面的描述，生成React组件的初步骨架。"
    *   "将 `docs/components/automation-runbook-export-data.md` 中的处理逻辑，翻译成日文である調，用于機能設計書的更新。"

### 8. 交付文档的生成/更新

*   当需要生成或更新日文的 `機能仕様書.docx` 或 `詳細設計書.xlsx` 时：
    1.  以 `docs/` 目录下的中文Markdown文档为最新、最准确的信息源。
    2.  手动或借助AI（例如，要求AI将某段中文描述翻译成专业的日文である調，并按指定格式输出），将内容填充到Word/Excel模板中。
    3.  由熟悉日语和项目的人员进行最终审查和格式调整。
