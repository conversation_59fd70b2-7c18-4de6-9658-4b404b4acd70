describe("パフォーマンスと読み込み時間のテスト", () => {
  const validCredentials = {
    userId: "hitachi.hanako.ab",
    password: "changeit!@#",
    newPassword: "pass1234",
  };

  it("ログイン画面の読み込み時間をテスト", () => {
    cy.visit("/login");

    cy.window().should("have.property", "performance");

    cy.window().then((win) => {
      const perf = win.performance;

      expect(
        perf.timing.loadEventEnd - perf.timing.navigationStart,
      ).to.be.lessThan(3000);
    });
  });

  it("ログイン操作のレスポンス時間をテスト", () => {
    cy.visit("/login");

    cy.window().then((win) => {
      win.performance.mark("startLogin");
    });

    cy.get("#userId").type(validCredentials.userId);
    cy.get("#password").type(validCredentials.password);
    cy.get("button").contains("ログイン").click();

    cy.location("pathname").should("eq", "/dashboard/servers");
    // cy.get("nav").should("contain", "サーバ一覧");
    cy.window().then((win) => {
      win.performance.mark("endLogin");
      win.performance.measure("login", "startLogin", "endLogin");
      const measures = win.performance.getEntriesByName("login");

      const loginTime = measures[0].duration;

      expect(loginTime).to.be.lessThan(3000);
    });
  });

  describe("ログイン後の画面の読み込み時間をテスト", () => {
    beforeEach(() => {
      cy.visit("/login");
      cy.get("#userId").type(validCredentials.userId);
      cy.get("#password").type(validCredentials.password);
      cy.get("button").click();
      cy.get("nav").should("contain", "サーバ一覧");
    });

    it("サーバ一覧画面の読み込み時間をテスト", () => {
      cy.visit("/dashboard/servers");
      cy.window().then((win) => {
        win.performance.mark("startPage");
      });
      cy.get("nav").should("contain", "サーバ一覧");
      cy.get("table").find("tbody td:nth-child(3)").find("a").should("exist");
      cy.window().then((win) => {
        win.performance.mark("endPage");
        win.performance.measure("page", "startPage", "endPage");
        const measures = win.performance.getEntriesByName("page");

        const loginTime = measures[0].duration;

        expect(loginTime).to.be.lessThan(3000);
      });
    });

    it("操作ログ一覧画面の読み込み時間をテスト", () => {
      cy.visit("/dashboard/oplogs");
      cy.window().then((win) => {
        win.performance.mark("startPage");
      });
      cy.get("nav").should("contain", "操作ログ一覧");
      cy.get("table").find("tbody th").find("a").should("exist");
      cy.window().then((win) => {
        win.performance.mark("endPage");
        win.performance.measure("page", "startPage", "endPage");
        const measures = win.performance.getEntriesByName("page");

        const loginTime = measures[0].duration;

        expect(loginTime).to.be.lessThan(3000);
      });
    });

    it("製品媒体一覧画面の読み込み時間をテスト", () => {
      cy.visit("/dashboard/medias");
      cy.window().then((win) => {
        win.performance.mark("startPage");
      });
      cy.get("nav").should("contain", "製品媒体一覧");
      cy.get("table")
        .find("tbody td:nth-child(6) ,tbody td:nth-child(8)")
        .find("a")
        .should("exist");
      cy.window().then((win) => {
        win.performance.mark("endPage");
        win.performance.measure("page", "startPage", "endPage");
        const measures = win.performance.getEntriesByName("page");

        const loginTime = measures[0].duration;

        expect(loginTime).to.be.lessThan(3000);
      });
    });

    it("マニュアル一覧画面の読み込み時間をテスト", () => {
      cy.visit("/dashboard/manuals");
      cy.window().then((win) => {
        win.performance.mark("startPage");
      });
      cy.get("nav").should("contain", "マニュアル一覧");
      cy.get("table").find("tbody th").find("a").should("exist");
      cy.window().then((win) => {
        win.performance.mark("endPage");
        win.performance.measure("page", "startPage", "endPage");
        const measures = win.performance.getEntriesByName("page");

        const loginTime = measures[0].duration;

        expect(loginTime).to.be.lessThan(3000);
      });
    });

    it("提供ファイル一覧画面の読み込み時間をテスト", () => {
      cy.visit("/dashboard/provided-files");
      cy.window().then((win) => {
        win.performance.mark("startPage");
      });
      cy.get("nav").should("contain", "提供ファイル一覧");
      cy.get("table").find("tbody th").find("a").should("exist");
      cy.window().then((win) => {
        win.performance.mark("endPage");
        win.performance.measure("page", "startPage", "endPage");
        const measures = win.performance.getEntriesByName("page");

        const loginTime = measures[0].duration;

        expect(loginTime).to.be.lessThan(3000);
      });
    });

    it("サポート情報一覧画面の読み込み時間をテスト", () => {
      cy.visit("/dashboard/support-files");
      cy.window().then((win) => {
        win.performance.mark("startPage");
      });
      cy.get("nav").should("contain", "サポート情報一覧");
      cy.get("table").find("tbody td:nth-child(2)").find("a").should("exist");
      cy.window().then((win) => {
        win.performance.mark("endPage");
        win.performance.measure("page", "startPage", "endPage");
        const measures = win.performance.getEntriesByName("page");

        const loginTime = measures[0].duration;

        expect(loginTime).to.be.lessThan(3000);
      });
    });

    it("お知らせダイアログの読み込み時間をテスト", () => {
      cy.window().then((win) => {
        win.performance.mark("startPage");
      });
      cy.get("button").contains("お知らせ").click();
      cy.get(".font-semibold").contains("お知らせ");
      cy.get(".whitespace-pre-line").should("exist");
      cy.window().then((win) => {
        win.performance.mark("endPage");
        win.performance.measure("page", "startPage", "endPage");
        const measures = win.performance.getEntriesByName("page");

        const loginTime = measures[0].duration;

        expect(loginTime).to.be.lessThan(3000);
      });
    });

    it("ライセンス情報ダイアログの読み込み時間をテスト", () => {
      cy.window().then((win) => {
        win.performance.mark("startPage");
      });
      cy.get("button").contains("ライセンス").click();
      cy.contains("製品版");
      cy.window().then((win) => {
        win.performance.mark("endPage");
        win.performance.measure("page", "startPage", "endPage");
        const measures = win.performance.getEntriesByName("page");

        const loginTime = measures[0].duration;

        expect(loginTime).to.be.lessThan(3000);
      });
    });

    it("パスワード変更のレスポンス時間をテスト", () => {
      cy.window().then((win) => {
        win.performance.mark("startPage");
      });
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password").type(validCredentials.password);
      cy.get("#newPassword").type(validCredentials.newPassword);
      cy.get("#confirmPassword").type(validCredentials.newPassword);
      cy.get("#password-modal button").contains("OK").click();
      cy.contains("パスワードを変更しました。");
      cy.window().then((win) => {
        win.performance.mark("endPage");
        win.performance.measure("page", "startPage", "endPage");
        const measures = win.performance.getEntriesByName("page");

        const loginTime = measures[0].duration;

        expect(loginTime).to.be.lessThan(3000);
      });
      cy.get("#message-modal button").contains("OK").click();
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password").type(validCredentials.newPassword);
      cy.get("#newPassword").type(validCredentials.password);
      cy.get("#confirmPassword").type(validCredentials.password);
      cy.get("#password-modal button").contains("OK").click();
      cy.contains("パスワードを変更しました。");
    });
  });
});
