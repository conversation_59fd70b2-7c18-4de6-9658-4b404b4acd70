TN:
SF:index.ts
FNF:0
FNH:0
DA:8,0
DA:9,0
DA:10,0
DA:11,0
DA:12,0
DA:13,0
LF:6
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:RunbookMonitorFunc\RunbookMonitorFunc.ts
FN:62,RunbookMonitorFunc
FNF:1
FNH:0
FNDA:0,RunbookMonitorFunc
DA:15,0
DA:16,0
DA:17,0
DA:18,0
DA:21,0
DA:22,0
DA:23,0
DA:24,0
DA:25,0
DA:26,0
DA:28,0
DA:29,0
DA:33,0
DA:34,0
DA:37,0
DA:40,0
DA:62,0
DA:63,0
DA:65,0
DA:66,0
DA:69,0
DA:72,0
DA:77,0
DA:78,0
DA:79,0
DA:82,0
DA:86,0
DA:87,0
DA:89,0
DA:90,0
DA:91,0
DA:98,0
DA:99,0
DA:100,0
DA:102,0
DA:103,0
DA:104,0
DA:106,0
DA:108,0
DA:109,0
DA:110,0
DA:115,0
DA:117,0
DA:118,0
DA:119,0
DA:121,0
DA:122,0
DA:124,0
DA:125,0
DA:127,0
DA:130,0
DA:133,0
DA:134,0
DA:135,0
DA:138,0
DA:139,0
DA:140,0
DA:143,0
DA:144,0
DA:150,0
DA:151,0
DA:155,0
DA:160,0
DA:161,0
DA:172,0
DA:173,0
DA:174,0
DA:177,0
DA:181,0
DA:182,0
DA:190,0
DA:191,0
DA:199,0
DA:200,0
DA:207,0
DA:208,0
DA:216,0
DA:217,0
DA:219,0
DA:222,0
DA:225,0
DA:226,0
DA:231,0
DA:232,0
DA:239,0
DA:242,0
DA:247,0
LF:87
LH:0
BRDA:21,0,0,0
BRDA:21,0,1,0
BRDA:22,1,0,0
BRDA:22,1,1,0
BRDA:28,2,0,0
BRDA:28,3,0,0
BRDA:28,3,1,0
BRDA:28,3,2,0
BRDA:28,3,3,0
BRDA:77,4,0,0
BRDA:89,5,0,0
BRDA:106,6,0,0
BRDA:106,6,1,0
BRDA:119,7,0,0
BRDA:124,8,0,0
BRDA:124,8,1,0
BRDA:125,9,0,0
BRDA:125,9,1,0
BRDA:130,10,0,0
BRDA:130,10,1,0
BRDA:135,11,0,0
BRDA:135,11,1,0
BRDA:155,12,0,0
BRDA:172,13,0,0
BRDA:216,14,0,0
BRDA:216,14,1,0
BRF:26
BRH:0
end_of_record
TN:
SF:RunbookProcessorTimeoutFunc\RunbookProcessorTimeoutFunc.ts
FN:34,RunbookProcessorTimeoutFunc
FN:92,(anonymous_1)
FNF:2
FNH:0
FNDA:0,RunbookProcessorTimeoutFunc
FNDA:0,(anonymous_1)
DA:16,0
DA:17,0
DA:18,0
DA:19,0
DA:20,0
DA:34,0
DA:36,0
DA:38,0
DA:40,0
DA:43,0
DA:44,0
DA:45,0
DA:49,0
DA:50,0
DA:52,0
DA:53,0
DA:54,0
DA:57,0
DA:58,0
DA:61,0
DA:71,0
DA:73,0
DA:74,0
DA:77,0
DA:80,0
DA:82,0
DA:83,0
DA:91,0
DA:92,0
DA:96,0
DA:109,0
DA:111,0
DA:112,0
DA:117,0
DA:130,0
DA:132,0
DA:133,0
DA:136,0
DA:140,0
DA:144,0
DA:145,0
DA:146,0
DA:147,0
DA:148,0
DA:149,0
DA:152,0
DA:156,0
DA:160,0
DA:165,0
LF:49
LH:0
BRDA:43,0,0,0
BRDA:43,1,0,0
BRDA:43,1,1,0
BRDA:52,2,0,0
BRDA:52,3,0,0
BRDA:52,3,1,0
BRDA:71,4,0,0
BRDA:80,5,0,0
BRDA:109,6,0,0
BRDA:130,7,0,0
BRF:10
BRH:0
end_of_record
TN:
SF:TaskCancellationFunc\TaskCancellationFunc.ts
FN:31,TaskCancellationFunc
FNF:1
FNH:0
FNDA:0,TaskCancellationFunc
DA:15,0
DA:16,0
DA:17,0
DA:18,0
DA:31,0
DA:34,0
DA:36,0
DA:37,0
DA:38,0
DA:39,0
DA:43,0
DA:44,0
DA:46,0
DA:47,0
DA:48,0
DA:49,0
DA:52,0
DA:53,0
DA:56,0
DA:60,0
DA:61,0
DA:62,0
DA:66,0
DA:67,0
DA:68,0
DA:72,0
DA:83,0
DA:84,0
DA:85,0
DA:88,0
DA:91,0
DA:92,0
DA:97,0
LF:33
LH:0
BRDA:36,0,0,0
BRDA:36,1,0,0
BRDA:36,1,1,0
BRDA:46,2,0,0
BRDA:46,3,0,0
BRDA:46,3,1,0
BRDA:60,4,0,0
BRDA:66,5,0,0
BRDA:83,6,0,0
BRF:9
BRH:0
end_of_record
TN:
SF:TaskCancellationTimeoutFunc\TaskCancellationTimeoutFunc.ts
FN:30,TaskCancellationTimeoutFunc
FNF:1
FNH:0
FNDA:0,TaskCancellationTimeoutFunc
DA:14,0
DA:15,0
DA:16,0
DA:17,0
DA:30,0
DA:32,0
DA:34,0
DA:35,0
DA:38,0
DA:39,0
DA:40,0
DA:44,0
DA:45,0
DA:47,0
DA:48,0
DA:49,0
DA:52,0
DA:53,0
DA:56,0
DA:60,0
DA:61,0
DA:62,0
DA:66,0
DA:67,0
DA:68,0
DA:72,0
DA:84,0
DA:85,0
DA:86,0
DA:89,0
DA:91,0
DA:93,0
DA:98,0
LF:33
LH:0
BRDA:38,0,0,0
BRDA:38,1,0,0
BRDA:38,1,1,0
BRDA:47,2,0,0
BRDA:47,3,0,0
BRDA:47,3,1,0
BRDA:60,4,0,0
BRDA:66,5,0,0
BRDA:84,6,0,0
BRF:9
BRH:0
end_of_record
TN:
SF:TaskExecuteFunc\TaskExecuteFunc.ts
FN:42,updateTaskToError
FN:75,updateTaskToErrorAfterRunbookSubmitted
FN:121,TaskExecuteFunc
FN:418,(anonymous_3)
FN:440,(anonymous_4)
FN:525,(anonymous_5)
FN:534,(anonymous_6)
FNF:7
FNH:0
FNDA:0,updateTaskToError
FNDA:0,updateTaskToErrorAfterRunbookSubmitted
FNDA:0,TaskExecuteFunc
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
FNDA:0,(anonymous_6)
DA:16,0
DA:17,0
DA:18,0
DA:23,0
DA:24,0
DA:25,0
DA:49,0
DA:63,0
DA:64,0
DA:81,0
DA:95,0
DA:96,0
DA:121,0
DA:127,0
DA:128,0
DA:129,0
DA:130,0
DA:132,0
DA:135,0
DA:138,0
DA:139,0
DA:140,0
DA:144,0
DA:145,0
DA:147,0
DA:148,0
DA:149,0
DA:152,0
DA:153,0
DA:156,0
DA:157,0
DA:159,0
DA:160,0
DA:164,0
DA:165,0
DA:169,0
DA:177,0
DA:178,0
DA:184,0
DA:190,0
DA:193,0
DA:195,0
DA:196,0
DA:197,0
DA:199,0
DA:200,0
DA:202,0
DA:203,0
DA:205,0
DA:206,0
DA:212,0
DA:216,0
DA:217,0
DA:218,0
DA:219,0
DA:225,0
DA:230,0
DA:232,0
DA:235,0
DA:240,0
DA:241,0
DA:250,0
DA:253,0
DA:254,0
DA:260,0
DA:265,0
DA:266,0
DA:267,0
DA:273,0
DA:278,0
DA:279,0
DA:285,0
DA:289,0
DA:292,0
DA:293,0
DA:299,0
DA:305,0
DA:306,0
DA:318,0
DA:322,0
DA:323,0
DA:329,0
DA:332,0
DA:336,0
DA:337,0
DA:343,0
DA:348,0
DA:349,0
DA:350,0
DA:351,0
DA:352,0
DA:353,0
DA:354,0
DA:355,0
DA:356,0
DA:359,0
DA:362,0
DA:363,0
DA:364,0
DA:367,0
DA:368,0
DA:374,0
DA:380,0
DA:381,0
DA:382,0
DA:385,0
DA:389,0
DA:390,0
DA:391,0
DA:397,0
DA:408,0
DA:413,0
DA:416,0
DA:419,0
DA:420,0
DA:421,0
DA:422,0
DA:424,0
DA:440,0
DA:443,0
DA:446,0
DA:451,0
DA:453,0
DA:454,0
DA:456,0
DA:457,0
DA:459,0
DA:460,0
DA:464,0
DA:465,0
DA:470,0
DA:474,0
DA:480,0
DA:483,0
DA:489,0
DA:493,0
DA:494,0
DA:495,0
DA:498,0
DA:502,0
DA:505,0
DA:508,0
DA:512,0
DA:513,0
DA:526,0
DA:532,0
DA:533,0
DA:535,0
DA:536,0
DA:537,0
DA:538,0
DA:539,0
DA:540,0
DA:542,0
DA:549,0
DA:552,0
DA:554,0
DA:555,0
DA:559,0
DA:561,0
DA:568,0
DA:570,0
DA:577,0
DA:579,0
DA:586,0
DA:592,0
DA:600,0
DA:612,0
LF:168
LH:0
BRDA:63,0,0,0
BRDA:95,1,0,0
BRDA:138,2,0,0
BRDA:138,3,0,0
BRDA:138,3,1,0
BRDA:147,4,0,0
BRDA:147,5,0,0
BRDA:147,5,1,0
BRDA:157,6,0,0
BRDA:169,7,0,0
BRDA:170,8,0,0
BRDA:170,8,1,0
BRDA:170,8,2,0
BRDA:170,8,3,0
BRDA:170,8,4,0
BRDA:170,8,5,0
BRDA:193,9,0,0
BRDA:193,9,1,0
BRDA:193,9,2,0
BRDA:193,9,3,0
BRDA:217,10,0,0
BRDA:230,11,0,0
BRDA:250,12,0,0
BRDA:265,13,0,0
BRDA:289,14,0,0
BRDA:318,15,0,0
BRDA:359,16,0,0
BRDA:362,17,0,0
BRDA:362,17,1,0
BRDA:364,18,0,0
BRDA:408,19,0,0
BRDA:451,20,0,0
BRDA:451,20,1,0
BRDA:451,20,2,0
BRDA:451,20,3,0
BRDA:477,21,0,0
BRDA:477,21,1,0
BRDA:512,22,0,0
BRDA:512,23,0,0
BRDA:512,23,1,0
BRDA:512,23,2,0
BRDA:532,24,0,0
BRDA:532,25,0,0
BRDA:532,25,1,0
BRDA:552,26,0,0
BRDA:552,27,0,0
BRDA:552,27,1,0
BRDA:559,28,0,0
BRDA:559,28,1,0
BRDA:559,29,0,0
BRDA:559,29,1,0
BRDA:568,30,0,0
BRDA:568,30,1,0
BRDA:568,31,0,0
BRDA:568,31,1,0
BRDA:577,32,0,0
BRDA:577,32,1,0
BRDA:577,33,0,0
BRDA:577,33,1,0
BRDA:586,34,0,0
BRDA:586,34,1,0
BRDA:587,35,0,0
BRDA:587,35,1,0
BRDA:587,35,2,0
BRDA:587,35,3,0
BRDA:605,36,0,0
BRDA:605,36,1,0
BRF:67
BRH:0
end_of_record
TN:
SF:TaskExecuteTimeoutFunc\TaskExecuteTimeoutFunc.ts
FN:39,TaskExecuteTimeoutFunc
FNF:1
FNH:0
FNDA:0,TaskExecuteTimeoutFunc
DA:16,0
DA:17,0
DA:18,0
DA:19,0
DA:20,0
DA:39,0
DA:42,0
DA:44,0
DA:47,0
DA:48,0
DA:49,0
DA:53,0
DA:54,0
DA:56,0
DA:57,0
DA:58,0
DA:61,0
DA:62,0
DA:66,0
DA:67,0
DA:68,0
DA:70,0
DA:73,0
DA:74,0
DA:77,0
DA:82,0
DA:83,0
DA:87,0
DA:88,0
DA:89,0
DA:90,0
DA:91,0
DA:92,0
DA:95,0
DA:99,0
DA:100,0
DA:102,0
DA:103,0
DA:106,0
DA:110,0
DA:111,0
DA:124,0
DA:126,0
DA:128,0
DA:132,0
DA:136,0
DA:138,0
DA:142,0
DA:143,0
DA:155,0
DA:157,0
DA:159,0
DA:163,0
DA:168,0
DA:171,0
DA:172,0
DA:177,0
LF:57
LH:0
BRDA:47,0,0,0
BRDA:47,1,0,0
BRDA:47,1,1,0
BRDA:56,2,0,0
BRDA:56,3,0,0
BRDA:56,3,1,0
BRDA:70,4,0,0
BRDA:70,4,1,0
BRDA:100,5,0,0
BRDA:113,6,0,0
BRDA:113,6,1,0
BRDA:114,7,0,0
BRDA:114,7,1,0
BRDA:124,8,0,0
BRDA:124,8,1,0
BRDA:136,9,0,0
BRDA:136,9,1,0
BRDA:136,10,0,0
BRDA:136,10,1,0
BRDA:155,11,0,0
BRDA:155,11,1,0
BRDA:172,12,0,0
BRDA:172,12,1,0
BRF:23
BRH:0
end_of_record
TN:
SF:lib\azureClients.ts
FN:46,createBlobServiceClient
FN:55,createShareServiceClient
FN:67,authenticatedFetch
FN:93,createServiceBusClient
FN:108,createAutomationJob
FN:139,stopAutomationJob
FN:177,getAutomationJobStatus
FNF:7
FNH:7
FNDA:1,createBlobServiceClient
FNDA:1,createShareServiceClient
FNDA:9,authenticatedFetch
FNDA:1,createServiceBusClient
FNDA:4,createAutomationJob
FNDA:2,stopAutomationJob
FNDA:3,getAutomationJobStatus
DA:11,15
DA:12,15
DA:13,15
DA:14,15
DA:17,15
DA:18,15
DA:19,15
DA:20,15
DA:21,15
DA:22,15
DA:23,15
DA:26,15
DA:34,2
DA:46,15
DA:47,1
DA:55,15
DA:56,1
DA:68,9
DA:74,9
DA:75,9
DA:76,9
DA:77,8
DA:80,8
DA:82,8
DA:93,15
DA:94,1
DA:95,0
DA:97,1
DA:108,15
DA:114,4
DA:115,4
DA:123,4
DA:128,3
DA:129,1
DA:132,2
DA:139,15
DA:140,2
DA:142,2
DA:146,2
DA:147,1
DA:150,1
DA:177,15
DA:178,3
DA:180,3
DA:184,3
DA:186,1
DA:187,1
DA:192,2
DA:194,1
DA:200,1
LF:50
LH:49
BRDA:22,0,0,15
BRDA:22,0,1,0
BRDA:23,1,0,15
BRDA:23,1,1,0
BRDA:26,2,0,2
BRDA:27,3,0,15
BRDA:27,3,1,14
BRDA:27,3,2,13
BRDA:27,3,3,13
BRDA:27,3,4,13
BRDA:27,3,5,13
BRDA:67,4,0,0
BRDA:74,5,0,9
BRDA:94,6,0,0
BRDA:94,6,1,1
BRDA:128,7,0,1
BRDA:146,8,0,1
BRDA:184,9,0,1
BRDA:184,9,1,2
BRDA:192,10,0,1
BRDA:192,10,1,1
BRF:21
BRH:17
end_of_record
TN:
SF:lib\cleanup.ts
FN:25,cleanupOldTasks
FNF:1
FNH:0
FNDA:0,cleanupOldTasks
DA:13,0
DA:14,0
DA:15,0
DA:17,0
DA:18,0
DA:25,0
DA:29,0
DA:31,0
DA:34,0
DA:37,0
DA:40,0
DA:43,0
DA:48,0
DA:49,0
DA:52,0
DA:55,0
DA:60,0
DA:61,0
DA:76,0
DA:77,0
DA:78,0
DA:81,0
DA:84,0
DA:89,0
DA:90,0
DA:93,0
DA:98,0
DA:99,0
DA:101,0
DA:102,0
DA:103,0
DA:104,0
DA:105,0
DA:106,0
DA:107,0
DA:108,0
DA:109,0
DA:110,0
DA:115,0
DA:116,0
DA:120,0
DA:122,0
DA:123,0
DA:125,0
DA:128,0
DA:131,0
DA:134,0
DA:135,0
DA:136,0
DA:140,0
DA:145,0
DA:151,0
DA:152,0
DA:155,0
DA:156,0
DA:157,0
DA:159,0
DA:166,0
DA:167,0
DA:168,0
DA:170,0
LF:61
LH:0
BRDA:37,0,0,0
BRDA:37,0,1,0
BRDA:48,1,0,0
BRDA:76,2,0,0
BRDA:101,3,0,0
BRDA:102,4,0,0
BRDA:102,4,1,0
BRDA:105,5,0,0
BRDA:105,5,1,0
BRDA:108,6,0,0
BRDA:115,7,0,0
BRDA:115,7,1,0
BRDA:115,8,0,0
BRDA:115,8,1,0
BRDA:134,9,0,0
BRDA:134,9,1,0
BRDA:156,10,0,0
BRDA:167,11,0,0
BRF:18
BRH:0
end_of_record
TN:
SF:lib\constants.ts
FN:12,(anonymous_0)
FN:16,(anonymous_1)
FN:29,(anonymous_2)
FN:38,(anonymous_3)
FNF:4
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
DA:12,0
DA:16,0
DA:17,0
DA:18,0
DA:19,0
DA:20,0
DA:21,0
DA:22,0
DA:23,0
DA:29,0
DA:30,0
DA:31,0
DA:32,0
DA:38,0
DA:39,0
DA:45,0
DA:67,0
DA:88,0
DA:97,0
DA:105,0
LF:20
LH:0
BRDA:16,0,0,0
BRDA:16,0,1,0
BRDA:29,1,0,0
BRDA:29,1,1,0
BRDA:38,2,0,0
BRDA:38,2,1,0
BRDA:12,3,0,0
BRDA:12,3,1,0
BRF:8
BRH:0
end_of_record
TN:
SF:lib\prisma.ts
FNF:0
FNH:0
DA:12,0
DA:14,0
LF:2
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:lib\utils.ts
FN:17,formatTaskErrorMessage
FN:23,(anonymous_1)
FN:30,isPrismaError
FN:40,isAzureFilesError
FN:52,isAzureBlobError
FN:62,hasErrorCode
FN:76,deleteTaskWorkspaceDirectory
FN:104,deleteImportsDirectory
FN:134,deleteExportsDirectory
FNF:9
FNH:0
FNDA:0,formatTaskErrorMessage
FNDA:0,(anonymous_1)
FNDA:0,isPrismaError
FNDA:0,isAzureFilesError
FNDA:0,isAzureBlobError
FNDA:0,hasErrorCode
FNDA:0,deleteTaskWorkspaceDirectory
FNDA:0,deleteImportsDirectory
FNDA:0,deleteExportsDirectory
DA:17,0
DA:21,0
DA:22,0
DA:23,0
DA:30,0
DA:31,0
DA:40,0
DA:41,0
DA:52,0
DA:53,0
DA:62,0
DA:63,0
DA:76,0
DA:80,0
DA:82,0
DA:83,0
DA:84,0
DA:85,0
DA:88,0
DA:90,0
DA:91,0
DA:92,0
DA:93,0
DA:96,0
DA:97,0
DA:108,0
DA:110,0
DA:111,0
DA:112,0
DA:113,0
DA:114,0
DA:117,0
DA:118,0
DA:119,0
DA:120,0
DA:121,0
DA:123,0
DA:124,0
DA:127,0
DA:138,0
DA:140,0
DA:141,0
DA:142,0
DA:143,0
DA:144,0
DA:147,0
DA:148,0
DA:149,0
DA:150,0
DA:151,0
DA:152,0
DA:155,0
DA:156,0
DA:159,0
LF:54
LH:0
BRDA:17,0,0,0
BRDA:22,1,0,0
BRDA:22,1,1,0
BRDA:41,2,0,0
BRDA:41,2,1,0
BRDA:41,2,2,0
BRDA:41,2,3,0
BRDA:41,2,4,0
BRDA:41,2,5,0
BRDA:53,3,0,0
BRDA:53,3,1,0
BRDA:53,3,2,0
BRDA:53,3,3,0
BRDA:53,3,4,0
BRDA:63,4,0,0
BRDA:63,4,1,0
BRDA:63,4,2,0
BRDA:63,4,3,0
BRDA:83,5,0,0
BRDA:112,6,0,0
BRDA:119,7,0,0
BRDA:142,8,0,0
BRDA:149,9,0,0
BRF:23
BRH:0
end_of_record
