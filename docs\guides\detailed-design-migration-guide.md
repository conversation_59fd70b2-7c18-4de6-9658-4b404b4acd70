# 詳細設計書 (DDS) 内容迁移至 docs/ 目录指南

## 1. 目的

本指南旨在提供一套清晰的步骤和方法，用于：
1.  将项目现有的日文版《詳細設計書》(Detailed Design Specification, DDS，通常为Excel格式，称之为“源DDS”) 中的设计信息，系统地迁移并整合到项目 `docs/` 目录下的Markdown组件文档中。
2.  指导在完成初始迁移后，或为新功能直接编写Markdown版DDS时，应如何组织和撰写内容，以达到项目期望的质量标准，特别是满足“**DDS与代码一一对应**”的要求，使其能够被AI编程助手和开发团队有效利用。

**核心目标**：将源DDS中的实现细节，以及为满足“代码对应”所需补充的详细设计，准确、结构化地映射到 `docs/components/.../[component-name].md` (或其拆分出的子功能DDS文档) 文件的“**3. 技术设计与实现细节 (Technical Design & Implementation)**”部分，并确保与相关的API规范 (`docs/apis/openapi.v1.yaml` - 若有)、数据模型 (`docs/data-models/` 及Prisma Schema)以及统一定义文档 (`docs/definitions/`) 精确对应。

## 2. 准备工作

1.  **理解目标 `docs/` 目录结构**: 熟悉项目定义的 `docs/` 目录结构，特别是 `docs/components/` 下的模块化和可能的子目录结构 (如 `tasks/`)，以及 `docs/definitions/` 目录的用途。参考 `docs/guides/monorepo-structure-and-deployment-workflow.md` 和 `docs/guides/ai-collaboration-and-documentation-standards.md`。
2.  **获取源详细设计书 (Excel)**: 若存在，确保拥有最新版本的日文源DDS。
3.  **Markdown编辑器**: 如 VS Code。
4.  **相关规范与项目文档**:
    *   《AI协作与文档规范指南》 (`docs/guides/ai-collaboration-and-documentation-standards.md`)：特别是其中关于DDS粒度、图表规范、SSoT原则等内容。
    *   《機能仕様書 (FS) 内容迁移至 docs/ 目录指南》 (`docs/guides/functional-specification-migration-guide.md`)：了解FS到DDS的衔接。
    *   项目 `prisma/schema.prisma` 文件：数据模型的最终权威来源。
    *   `docs/definitions/lov-definitions.md`：值列表(LOV)的权威定义。
    *   `docs/definitions/error-messages.md`：错误消息的权威定义。
    *   `docs/definitions/glossary.md`：项目术语表。
    *   相关的组件代码（若已有原型或部分实现）。

## 3. 迁移与编写原则

*   **信息提炼、转换与深化**: 将源DDS (Excel) 中的结构化信息转换为Markdown。更重要的是，基于“与代码一一对应”的要求，对源信息进行**深化和补充**，确保DDS包含足够精确的实现细节。
*   **关注“如何精确实现”**: 详细描述组件的技术实现方案、内部逻辑流程、数据结构与交互契约、错误处理机制等，达到可直接指导编码的程度。
*   **语言规范**:
    *   `docs/` 目录下的Markdown文档内部描述性内容（如逻辑步骤、解释、备注、非UI的表格内容）均使用**简体中文**。
    *   所有直接面向最终用户的界面文本（如按钮标签、菜单项、表头、提示信息等）在文档中明确标识并使用**纯正、专业的日语**。这些日文文本的权威来源是 `docs/definitions/error-messages.md` (对于错误/提示消息) 或组件DDS中直接定义的UI元素文本。
    *   技术组件名称、服务名称、标准协议、API端点、数据库物理名、确切的函数/方法/变量名（当引用代码层面概念时）等标准技术术语优先使用其**官方英文全称或实际代码中的名称**。
*   **结构化与可读性**: 使用Markdown的特性。所有章节标题和子标题均使用中文。**避免在非代码块内容中使用不必要的行首缩进。**
*   **单一事实来源 (SSoT)**: `docs/` 目录下的Markdown文档是设计信息的唯一权威来源。
*   **与FS部分的衔接**: DDS的第3部分是对FS第1、2部分的细化和实现方案。
*   **明确具体，消除模糊**: DDS正文中应最大限度避免使用“例如”、“可能”等不确定描述。所有设计决策和规格参数均应明确。未定事宜标记`[TBD]`并推动明确。

## 4. 组件文档结构 (`docs/components/.../[component-name].md`)

每个主要功能模块或其拆分出的子功能都应有其独立的 `.md` 文件。推荐采用以下结构。源DDS的内容及为满足“代码对应”而补充的细节，主要填充到本模板的第3部分及其子章节。

```markdown
# 组件：[组件/功能中文名称] ([Component/Function English Name])

## 1. 概要 (Overview) - FS驱动
<!-- ... (内容参考FS迁移指南) ... -->

## 2. 功能规格 (Functional Specifications) - FS驱动
<!-- ... (内容参考FS迁移指南, 特别注意2.1 主要流程图应保持高层抽象) ... -->

---
*指导说明：以上第1和第2部分内容主要来源于《機能仕様書》。本指南主要关注以下“3. 技术设计与实现细节”部分的迁移和编写。*
---

## 3. 技术设计与实现细节 (Technical Design & Implementation) - DD驱动

### 3.1 技术栈与依赖 (Technology Stack & Dependencies)
<!-- (中文描述) 列出实现该组件所用的具体技术、框架、库版本。明确对外部服务、共享库、核心数据服务模块 (如 app/lib/data.ts)、Server Actions (如 app/lib/actions.ts)、特定UI组件 (如 app/ui/message-modal.tsx)、以及数据存储的依赖。强制链接到相关的OpenAPI定义 (若有)、数据模型文档 (`docs/data-models/`)、或共享类型定义 (`apps/jcs-endpoint-nextjs/app/lib/definitions.ts`)、以及系统定义文档 (`docs/definitions/`)。 -->

### 3.2 详细界面元素定义 (Detailed UI Element Definitions) - 若为UI组件或涉及UI交互
<!-- (中文描述)
    - 从源DDS的“画面項目定義”Sheet迁移基础信息。
    - **深化要求 (为满足代码对应)**:
        - 对每个UI控件，使用Markdown表格详细描述。
        - 表格列应包括: #, 控件内部逻辑名(中文), **界面显示文本(日文)**, 控件类型(HTML原生或自定义React组件名), 建议英文ID/React key, **数据来源/状态(明确绑定的前端state变量名或props名)**, **主要行为/事件(触发的事件处理器函数名)**, **校验规则/显示逻辑(客户端校验规则、条件显示逻辑)**, 格式/备注(中文)。
        - 若涉及动态内容 (如从 `docs/definitions/lov-definitions.md` 加载的选项)，需说明数据来源和转换逻辑。
-->

### 3.3 详细事件处理逻辑 (Detailed Event Handling) - 若为UI组件或涉及用户交互触发的逻辑
<!-- (中文描述)
    - 从源DDS的“イベント定義”Sheet迁移基础事件流。
    - **深化要求 (为满足代码对应)**:
        - 对每个用户交互事件或系统事件，创建中文子标题。
        - **触发条件 (Trigger)**: 清晰描述事件如何被触发。
        - **前端处理步骤 (Client-side Processing Flow)**: 详细描述事件处理器函数的内部逻辑步骤：
            1. 读取哪些props或state。
            2. 执行的参数校验逻辑。
            3. (若有)对其他内部辅助函数的调用。
            4. **构造调用Server Action或API的参数 (精确到字段名和预期数据类型)**。
            5. (若调用API Route) 明确调用的HTTP方法、端点路径，并链接到`docs/apis/openapi.v1.yaml`中的定义。
            6. **调用Server Action**: 明确调用的Server Action名称 (如 `createTaskAction`) 及其传递的参数 (例如 `FormData` 的构造或参数对象的结构)。
            7. **处理Server Action/API的响应**: 对成功和各种可预期的失败场景（基于Server Action/API的返回值结构，如 `CreateTaskActionResult`），描述客户端如何更新其内部state、向用户显示通知（引用 `docs/definitions/error-messages.md` 中的消息ID或消息键，前端获取对应日文文本）、以及执行其他UI更新（如关闭模态框、重定向、数据刷新等）。
-->

### 3.4 数据结构与API/Server Action交互 (Data Structures and API/Server Action Interaction)
<!-- (中文描述)
    - 从源DDS的“データ流れ”Sheet和“APIインターフェース詳細”Sheet迁移基础信息。
    - **深化要求 (为满足代码对应)**:
        - **前端核心状态管理**: 若组件有复杂的前端状态（特别是页面级组件管理其子组件或模态框状态），详细定义关键状态变量的结构、类型和用途。
        - **Server Action交互详情**:
            - **输入参数定义**: 为每个被调用的Server Action，清晰定义其接收的参数结构（例如，`CreateTaskActionParams` 类型定义，或 `FormData` 中所有字段的名称、预期类型、是否必需、用途）。
            - **返回值定义**: 清晰定义Server Action的返回值对象结构及其所有字段的类型和含义（例如，`CreateTaskActionResult` 类型定义，其错误消息部分应包含来自 `docs/definitions/error-messages.md` 的消息键）。
        - **(若使用API Routes) HTTP API交互详情**:
            - 明确调用的API端点、HTTP方法。强制链接到`docs/apis/openapi.v1.yaml`。
            - 若OpenAPI中未完全覆盖，可在此处补充关键请求/响应数据结构的摘要或引用共享类型。
        - **主要交互序列图 (Mermaid `sequenceDiagram`)**:
            - 绘制核心交互流程的序列图。**参与者必须是高层逻辑/物理单元**（如用户、前端应用、Next.js服务器、数据库服务器、外部服务平台）。
            - **严禁将内部函数或UI组件作为独立参与者。**
            - 消息应清晰表示请求/响应的意图和关键数据。
            - 对于较长的消息文本，使用 `<br>` 进行换行。
            - **图表在提交前必须经过渲染验证。**
-->

### 3.5 数据库设计与访问详情 (Database Design and Access Details - 主要通过 `app/lib/data.ts` 间接访问)
<!-- (中文描述)
    - 从源DDS的“テーブル定義”Sheet和“データベースアクセス詳細”Sheet迁移基础信息。
    - **深化要求 (为满足代码对应)**:
        - **相关表引用**: 列出此组件功能所依赖的所有数据库表，并强制链接到 `docs/data-models/` 下对应的表定义文件 (该文件应与Prisma Schema一致)。
        - **主要数据查询/变更逻辑 (由 `app/lib/data.ts` 中的函数实现)**:
            - 对每个由前端Server Component直接调用，或由Server Action间接调用的关键数据服务函数（例如 `fetchFilteredServers`, `createTaskInDb`）：
                - 描述其**职责、输入参数（类型和含义）、返回值（类型和含义）**。
                - 用中文概述其核心的Prisma查询或变更操作逻辑：涉及哪些表、主要的`where`条件、`select`的字段、`data`更新的内容、是否在事务(`prisma.$transaction`)中执行。
                - **避免直接粘贴Prisma Client代码**，除非是极其简短且关键的用于说明逻辑的片段。
-->

### 3.6 关键后端逻辑/算法 (Key Backend Logic/Algorithms - 主要在 `app/lib/actions.ts` 和 `app/lib/data.ts` 中)
<!-- (中文描述)
    - **深化要求 (为满足代码对应)**:
        - **Server Action详细处理流程**:
            - 对每个Server Action，采用结构化的中文描述（如编号列表、条件分支说明）其从接收参数到返回结果的完整内部执行流程。
            - 包括：输入参数的详细解析与服务端校验规则；用户身份/权限的验证逻辑；对`app/lib/data.ts`中数据服务函数的调用顺序和参数传递；对外部服务（如Azure Service Bus、Blob Storage）的调用逻辑和关键参数；错误捕获、处理（引用 `docs/definitions/error-messages.md` 中的消息键）及补偿逻辑（若有）；构造最终返回给客户端的`ActionResult`对象的逻辑。
            - (可选) 对于特别复杂的内部逻辑，可辅以Mermaid活动图或简单流程图。
        - **数据服务函数 (`app/lib/data.ts`) 的核心逻辑**:
            - 如果某些数据服务函数内部包含超越简单CRUD的复杂业务逻辑、数据转换或算法，在此处进行专门描述。
-->

### 3.7 错误处理详情 (Detailed Error Handling)
<!-- (中文描述)
    - 从源DDS的“エラー処理詳細”Sheet迁移基础信息。
    - **深化要求 (为满足代码对应)**:
        - 使用Markdown表格，针对每个可预期的错误场景（客户端校验失败、Server Action/API调用失败、Server Action内部逻辑失败、后端服务调用失败等）：
            - 列出**错误场景描述(中文)**。
            - **触发位置(前端/ServerAction/数据服务等)**。
            - **引用的消息ID/消息键 (来自 `docs/definitions/error-messages.md`)**。
            - **系统内部处理及日志记录建议(中文)** (例如，前端如何更新UI，Server Action如何记录错误，是否需要执行补偿逻辑)。
            - **日志级别(中文)** (如 INFO, WARN, ERROR, CRITICAL)。
        - **注意**: 用户可见的日文提示文本的SSoT在 `docs/definitions/error-messages.md`。本节只引用消息ID/键。
-->

### 3.8 配置项 (Configuration)
<!-- (中文描述)
    - **深化要求 (为满足代码对应)**:
        - 列出该组件功能（包括其调用的Server Action和数据服务）依赖的所有配置项。
        - **`LOV`表配置**: 清晰列出所有相关的`parentCode`及其下的`code`值 (参考 `docs/definitions/lov-definitions.md`)，并说明其用途 (例如，`TASK_TYPE.OPLOG_EXPORT` 用于任务类型判断，`OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN` 用于日期范围校验)。
        - **环境变量**: 列出相关的环境变量名称，简述其用途，并强调应参考全局的 `docs/guides/environment-variables.md` 获取详细信息和管理方法。
-->

### 3.9 注意事项与其他 (Notes/Miscellaneous)
<!-- (中文描述) 其他与具体实现相关的、未在上述章节覆盖的重要技术点、设计决策、性能考量、安全性增强措施等。 -->
```

## 5. 《詳細設計書》Excel各Sheet到Markdown的迁移指南 (初始迁移阶段)

本节主要指导如何将源DDS (Excel) 中的已有信息作为基础，迁移到上述Markdown组件文档结构的对应位置。在完成初始迁移后，必须根据第4节中各子章节的“深化要求”对内容进行补充和细化，以达到“与代码一一对应”的标准。

### 5.1. 画面項目定義 (Screen Item Definitions)
*   **源**: Excel中的“画面項目定義”Sheet。
*   **目标**: `docs/components/.../[screen-component-name].md` 内的 `3.2 详细界面元素定义` 章节。
*   **方法**:
    1.  为每个屏幕/主组件创建或打开其对应的Markdown文件。
    2.  参照第4节模板中的Markdown表格结构。将Excel中的“項目名”（日文UI标签）填入“界面显示文本(日文)”列。为控件创建“控件内部逻辑名(中文)”。Excel中“フォーマット/備考”列的非UI解释性内容用中文转录到“格式/备注”列；UI相关的提示则填入“界面显示文本(日文)”列或在备注中用日文注明。
    3.  **后续深化**: 必须补充“数据来源/状态(明确绑定的前端state变量名或props名)”和“主要行为/事件(触发的事件处理器函数名)”等列，以满足代码对应要求。

### 5.2. イベント定義 (Event Definitions)
*   **源**: Excel中的“イベント定義”Sheet。
*   **目标**: `docs/components/.../[screen-component-name].md` 内的 `3.3 详细事件处理逻辑` 章节。
*   **方法**:
    1.  对Excel中的每个Event No.，在Markdown中创建对应的中文子标题。
    2.  将其“DB情報取得”和“画面表示”等部分的日文描述，初步翻译并整理成中文的步骤。
    3.  **后续深化**: 必须按照4节中`3.3`的深化要求，详细描述前端处理步骤、状态更新、Server Action/API调用（含参数构造、响应处理、错误消息键引用）、UI更新等。

### 5.3. データ流れ (Data Flow) / APIインターフェース詳細 (Detailed API Interface)
*   **源**: Excel中的“データ流れ”Sheet和“APIインターフェース詳細”Sheet。
*   **目标**:
    *   序列图: `docs/components/.../[component-name].md` 内的 `3.4 数据结构与API交互` 章节，使用Mermaid `sequenceDiagram`。
    *   API接口定义: `docs/apis/openapi.v1.yaml` (若为HTTP API)。Server Action的输入输出定义在组件DDS的3.4节。
*   **方法**:
    1.  **序列图**: 理解Excel中的序列图逻辑，使用Mermaid语法在Markdown中重绘。**参与者必须是高层逻辑/物理单元，图表需预先验证。**
    2.  **API接口/Server Action**: 若源DDS有API描述，则迁移到OpenAPI或组件DDS的3.4节。
    3.  **后续深化**: 必须为所有Server Action明确定义输入参数和返回值结构（返回值中的错误消息部分应包含消息键）。序列图需准确反映前端与Server Action，以及Server Action与后端服务间的交互。

### 5.4. テーブル定義 (Table Definitions)
*   **源**: Excel中的“テーブル定義”Sheet。
*   **目标**: `docs/data-models/[table-name].md` (或直接参考并链接到 `prisma/schema.prisma`)。
*   **方法**:
    1.  优先以 `prisma/schema.prisma` 文件作为数据库表结构的SSoT。
    2.  `docs/data-models/[table-name].md` 文档可以用于提供对Prisma Schema中各表、字段的中文业务逻辑名、用途解释、重要约束说明等补充信息。表格列可包括：逻辑名(中文), (参考)日文论理名, 物理名(英文，来自Prisma), 数据类型(来自Prisma), 可空, PK/FK, 默认值, 约束/描述(中文)。
    3.  确保 `[table-name].md` 中的描述与 `prisma/schema.prisma` 保持一致。

### 5.5. エラー処理詳細 (Detailed Error Handling)
*   **源**: Excel中的“エラー処理詳細”Sheet。
*   **目标**: `docs/components/.../[component-name].md` 的 `3.7 错误处理详情` 章节。
*   **方法**:
    1.  将Excel中的错误场景迁移到Markdown表格的“错误场景描述(中文)”列。
    2.  **后续深化**: 必须按照4节中`3.7`的深化要求，补充错误触发位置、引用的消息ID/消息键（来自 `docs/definitions/error-messages.md`）、系统内部处理、日志级别等。**不再在此处重复定义日文错误消息文本。**

## 6. Server Actions 在详细设计中的体现

在使用 Next.js App Router 并采用 Server Actions 实现服务器端逻辑时，其详细设计应在组件文档 (`docs/components/.../[component-name].md` 或其拆分出的子功能文档) 的“3. 技术设计与实现细节”部分按如下方式核心体现：

*   **`3.3 详细事件处理逻辑 (Detailed Event Handling)`**:
    *   明确哪个用户交互事件触发了 Server Action 的调用。
    *   描述客户端如何构造参数 (例如 `FormData` 或对象) 传递给 Server Action。
    *   描述客户端如何处理 Server Action 的返回值 (`CreateTaskActionResult` 或类似结构)，包括成功和失败分支的UI更新及用户通知（引用消息键）。
*   **`3.4 数据结构与API/Server Action交互 (Data Structures and API Interaction)`**:
    *   **Server Action输入参数定义**: 提供Server Action接收参数的精确结构和类型定义 (例如TypeScript类型)。
    *   **Server Action返回值定义**: 提供Server Action返回结果对象的精确结构和类型定义（其中错误消息部分包含消息键）。
    *   **序列图**: 包含Server Action作为从前端到Next.js服务器的一个关键交互步骤。
*   **`3.6 关键后端逻辑/算法 (Key Backend Logic/Algorithms)`**:
    *   **这是描述Server Action内部实现的核心章节。** 详细描述Server Action从接收参数到返回结果的完整处理流程、业务规则、数据校验、对数据服务(`app/lib/data.ts`)的调用、与外部服务（Service Bus, Blob Storage）的交互、错误处理（引用消息键）等。

## 7. 从 `docs/` 生成/更新 《詳細設計書》(Excel) - 作为交付物

当需要生成符合传统日系IT行业规范的Excel版《詳細設計書》作为交付物时，应以`docs/`目录下的Markdown文档作为权威信息源。

### 7.1. 原则
*   **`docs/` 为单一事实来源**。
*   **人工审核与调整**: 最终的交付文档仍需人工审查，确保日语表达的准确性、专业性和格式。
*   **版本对应**: 交付文档的版本应与 `docs/` 中对应版本的内容保持一致。

### 7.2. 各Sheet的填充/更新方法 (摘要)
1.  **画面項目定義 Sheet**: 源自组件DDS `3.2`节表格。将Markdown中“界面显示文本(日文)”填入Excel“項目名”；“控件内部逻辑名(中文)”供翻译参考；“格式/备注(中文)”翻译为日文填入Excel“フォーマット/備考”。
2.  **イベント定義 Sheet**: 源自组件DDS `3.3`节。将Markdown中的中文步骤描述翻译为专业日文（である調），按Excel格式组织。
3.  **データ流れ Sheet / APIインターフェース詳細 Sheet**:
    *   **序列图**: 将Mermaid图导出为图片插入。根据图编写日语步骤说明。
    *   **API接口 (若有HTTP API)**: 参考`openapi.v1.yaml`，提取信息并翻译描述为日文。Server Action的契约则从组件DDS `3.4`节提取并翻译。
4.  **テーブル定義 Sheet**: 主要参考`prisma/schema.prisma`，并结合`docs/data-models/[table-name].md`中的中文逻辑名和描述，将其翻译为日文“论理名”和“備考”。
5.  **エラー処理詳細 Sheet**:
    *   **主要信息源**: `docs/definitions/error-messages.md`。
    *   将该文件中的“消息ID/代码”、“日文用户提示文本”等列，结合组件DDS `3.7`节中的“错误场景描述(中文)”（翻译为日文）和“内部处理逻辑/备注(中文)”（翻译为日文），填充到Excel的对应列。

### 7.3. 最终审查与交付
*   彻底审查生成的Excel《詳細設計書》，确保与`docs/`源信息一致。
*   明确版本号，归档到 `docs-delivery/詳細設計書/`。

## 8. 注意事项
*   **迭代与同步**: 设计变更优先更新`docs/`目录下的Markdown，再考虑同步到Excel交付物。
*   **文档拆分的应用**: 对于复杂组件，遵循`docs/guides/ai-collaboration-and-documentation-standards.md`中4.5.4节定义的文档拆分策略，将子功能的详细设计写入独立的Markdown文件。本指南的迁移和编写方法同样适用于这些拆分后的子文档。

通过本指南，旨在将源DDS (Excel) 中的信息有效地迁移并深化，或直接创建出结构化、精确详尽、易于维护且对AI友好的Markdown版DDS，并指导如何基于这些Markdown文档生成符合交付要求的《詳細設計書》。
