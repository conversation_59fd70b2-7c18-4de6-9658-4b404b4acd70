# 数据模型: 产品手册 (ProductManual)

*   **表名 (逻辑名)**: `ProductManual`
*   **对应UI界面**: 「マニュアル一覧」 (Manual List)
*   **主要用途**: 存储产品手册（如用户指南、操作手册、参考文档等）的详细信息，供用户在门户上查看和下载。

## 1. 字段定义

| 字段名 (Prisma/英文) | 数据类型 (SQL参考) | PK | FK | UQ | Nullable | Default  | 描述 (中文)                                                                       |
| :------------------- | :----------------- | :--- | :--- | :--- | :------- | :------- | :-------------------------------------------------------------------------------- |
| `id`                 | VARCHAR(36)        | ●    |      |      |          | `cuid()` | 主键。CUID格式，由系统自动生成。                                                      |
| `name`               | NVARCHAR(255)      |      |      |      |          |          | 手册的显示名称。例如："快速入门指南", "高级配置手册"。对应原「機能仕様書」中的`manualName`。       |
| `serialNo`           | VARCHAR(XX)        |      |      | ●    |          |          | 手册的唯一资料编号或文档编号。用于在系统中唯一标识一个手册。对应原「機能仕様書」中的`documentNumber`。 |
| `fileName`           | VARCHAR(255)       |      |      |      |          |          | 手册的实际文件名（通常是PDF或其他文档格式）。例如："QSG_v1.2.pdf"。                      |
| `size`               | INT                |      |      |      |          |          | 手册文件的大小，单位为字节 (Bytes)。界面显示时会转换为KB/MB/GB等易读单位。                |

*PK: Primary Key, FK: Foreign Key, UQ: Unique Key*
*XX表示具体长度，取决于实际的数据库Schema定义。*

## 2. 关系

*   **对 `PlanManual` (`planManuals`)**: 一对多关系 (`PlanManual[]`)。一个产品手册（由`serialNo`唯一确定）可以通过`PlanManual`表关联到多个契约计划，表示该手册可用于这些计划。

## 3. 唯一约束

*   `UNIQUE KEY (serialNo)` (Prisma Schema中已定义 `@unique`)，确保手册的资料编号在系统中是唯一的。

## 4. Azure Blob Storage 路径约定 (推测)

*   产品手册文件在Azure Blob Storage中的实际存储路径，通常遵循预定义规则动态构建，例如基于 `serialNo` 或 `fileName`。
*   表中不直接存储完整的Blob路径。

## 5. 索引 (示例)

*   `PRIMARY KEY (id)`
*   `UNIQUE KEY UQ_ProductManual_SerialNo (serialNo)` (业务唯一键)
*   `INDEX idx_productmanual_name (name)` (如果经常按手册名称搜索)
*   `INDEX idx_productmanual_filename (fileName)`