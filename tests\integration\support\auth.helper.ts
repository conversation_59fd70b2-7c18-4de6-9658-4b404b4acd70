/**
 * 认证辅助模块
 * 用于 Playwright 测试中快速注入登录 Cookie，跳过 UI 登录流程。
 * 基于 iron-session 和 Keycloak 认证机制实现。
 */

import { Page } from '@playwright/test';
import { SessionData, sessionOptions } from '../../../apps/jcs-endpoint-nextjs/app/lib/session';
import { sealData } from 'iron-session';

/**
 * 用户角色定义
 */
export enum UserRole {
  ADMIN = 'admin',
  STANDARD = 'standard',
  READONLY = 'readonly'
}

/**
 * 用户配置接口
 */
interface UserConfig {
  id: string;
  userId: string;
  licenseId: string;
  tz: string;
  refreshToken: string;
}

/**
 * 预定义的测试用户配置
 */
const userConfigs: Record<UserRole, UserConfig> = {
  [UserRole.ADMIN]: {
    id: 'admin-user-id',
    userId: 'admin',
    licenseId: 'test-license-id',
    tz: 'Asia/Tokyo',
    refreshToken: 'admin-refresh-token'
  },
  [UserRole.STANDARD]: {
    id: 'standard-user-id',
    userId: 'standard',
    licenseId: 'test-license-id',
    tz: 'Asia/Tokyo',
    refreshToken: 'standard-refresh-token'
  },
  [UserRole.READONLY]: {
    id: 'readonly-user-id',
    userId: 'readonly',
    licenseId: 'test-license-id',
    tz: 'Asia/Tokyo',
    refreshToken: 'readonly-refresh-token'
  }
};

/**
 * 创建认证会话 Cookie
 * @param userRole 用户角色
 * @param licenseId 可选的许可证ID，用于并发测试隔离
 * @returns 加密的会话 Cookie 值
 */
async function createSessionCookie(userRole: UserRole, licenseId?: string): Promise<string> {
  // 获取用户配置
  const userConfig = userConfigs[userRole];
  if (!userConfig) {
    throw new Error(`未知的用户角色: ${userRole}`);
  }

  // 创建会话数据 - 确保与实际应用的会话结构完全一致
  // 支持动态许可证ID以实现并发测试隔离
  const sessionData: SessionData = {
    user: {
      id: userConfig.id,
      userId: userConfig.userId,
      licenseId: licenseId || userConfig.licenseId, // 优先使用传入的许可证ID
      tz: userConfig.tz,
      refreshToken: userConfig.refreshToken
    }
  };

  console.log('Creating session data:', {
    hasUser: !!sessionData.user,
    userId: sessionData.user.userId,
    licenseId: sessionData.user.licenseId
  });

  // 使用真正的 iron-session 加密，与实际应用保持一致
  // 现在应用正确加载了测试环境的 .env 文件，使用相同的密码
  const password = sessionOptions.password;

  console.log('使用的密码长度:', password?.length || 0);

  const sealedData = await sealData(sessionData, {
    password: password,
    ttl: sessionOptions.cookieOptions?.maxAge || 3600
  });

  console.log('Sealed data length:', sealedData.length);
  return sealedData;
}

/**
 * 为指定页面注入认证状态
 * @param page Playwright 页面对象
 * @param options 登录选项，包括用户角色和可选的许可证ID
 */
export async function loginAs(page: Page, options: { role: UserRole | string; licenseId?: string }): Promise<void> {
  // 将字符串角色转换为枚举
  const role = typeof options.role === 'string' ? options.role as UserRole : options.role;

  // 创建加密的会话 Cookie
  const cookieValue = await createSessionCookie(role, options.licenseId);
  
  // 设置 Cookie 到浏览器
  // 计算Cookie过期时间（当前时间 + maxAge秒）
  const maxAge = sessionOptions.cookieOptions?.maxAge || 3600;
  const expiresTimestamp = Math.floor(Date.now() / 1000) + maxAge;

  await page.context().addCookies([
    {
      name: sessionOptions.cookieName || 'portal_keycloak_callback_session',
      value: cookieValue,
      url: 'http://localhost:3000', // 使用 URL，包含了域名和路径信息
      httpOnly: true,
      secure: false, // 测试环境使用 HTTP
      sameSite: 'Strict', // 与应用配置保持一致
      expires: expiresTimestamp // 设置正确的过期时间
    }
  ]);
}

/**
 * 清除认证状态
 * @param page Playwright 页面对象
 */
export async function logout(page: Page): Promise<void> {
  // 清除所有 Cookie
  await page.context().clearCookies();
}

/**
 * 检查用户是否已登录
 * @param page Playwright 页面对象
 * @returns 是否已登录
 */
export async function isLoggedIn(page: Page): Promise<boolean> {
  // 获取当前 Cookie
  const cookies = await page.context().cookies();
  const sessionCookie = cookies.find(cookie => 
    cookie.name === (sessionOptions.cookieName || 'portal_keycloak_callback_session')
  );
  
  return !!sessionCookie;
}

/**
 * 获取当前登录用户信息
 * @param page Playwright 页面对象
 * @returns 用户信息或 null（如果未登录）
 */
export async function getCurrentUser(page: Page): Promise<{ userId: string, role: string } | null> {
  // 执行客户端脚本获取用户信息
  // 注意：这依赖于应用程序在窗口对象上暴露用户信息
  const userInfo = await page.evaluate(() => {
    // 尝试从页面中获取用户信息
    // 这里假设应用在某处存储了用户信息
    // 实际实现可能需要根据应用的具体情况调整
    return (window as any).__USER_INFO__ || null;
  });
  
  return userInfo;
}