# 值列表定义 (LOV Definitions)

## 1. 引言

本文档旨在作为“JCS 端点资产与任务管理系统”项目中所有值列表 (List of Values, LOV) 及其具体值的权威定义来源。这些LOV配置存储于数据库的 `Lov` 表中（其表结构由 `prisma/schema.prisma` 文件定义），并被系统各模块广泛用于定义**应用内部的枚举类型、业务规则参数、以及用户界面上动态显示的标签或选项（特别是将内部状态码映射到用户可见的日文描述）**。

本文档的目标是为开发团队、测试团队以及AI编程助手提供一个清晰、准确、集中的参考，确保对各项配置和枚举值的理解和使用保持一致。当数据库 `Lov` 表内容或代码中引用的 `LOV Code` 发生变更时，本文档必须同步更新。**本文档内容严格遵循与客户确认的功能规格书（FS）中关于LOV的定义。**

**重要范围界定**:
*   **用户界面提示的消息文本键**: 例如错误消息、成功提示等，并非通过此 `LOV` 表管理，而是定义在 `docs/definitions/error-messages.md` 中，并在应用程序代码（例如 `apps/jcs-endpoint-nextjs/app/lib/definitions.ts`）中通过常量引用。
*   **基础设施相关的固定资源标识符**: 例如Azure Service Bus的队列名称等，这些是环境配置的一部分，通过**环境变量**进行管理，其详细定义参见 `docs/guides/environment-variables.md`。对于Azure Blob Storage的容器名称，尽管FS的LOV表格中列出了其逻辑定义，但项目的推荐实现方式仍是通过环境变量进行具体配置（详见`AZURE_STORAGE`部分的备注及`environment-variables.md`）。
*   **直接存储在业务表中的代码值**: 例如 `License.basicPlan` 字段存储的 `'STANDARD'`, `'LIGHT_A'`, `'LIGHT_B'` 等字符串代码，如果当前没有在UI上显示其对应日文名称的需求，则其定义和管理直接在应用程序代码层面通过常量进行，不一定需要在此LOV表中重复定义。

## 2. `Lov` 表核心字段回顾

`prisma/schema.prisma` 中定义的 `Lov` 模型（表）通常包含以下核心字段：

*   `code: String @unique`: LOV项的唯一代码，是程序中引用的主要标识符（例如，内部任务状态码）。
*   `name: String`: LOV项的名称，通常是其日文标签或描述，主要用于UI显示或内部参考。
*   `parentCode: String?`: 指向其父级LOV项的 `code`，用于组织LOV的层级和类别。顶层LOV此字段为空。
*   `value: String`: LOV项的实际值。对于状态等枚举，此字段可能与`name`相同或为空；对于配置参数，此字段存储具体参数值。
*   `isEnabled: Boolean @default(true)`: 指示此LOV项当前是否启用。

## 3. LOV 定义详情

以下按 `parentCode` 对项目中的主要LOV进行分组定义。

### 服务器类型 (`SERVER_TYPE`)

| # | code | name (中文名称) | value (值) | parentCode | isEnabled | 備考 (中文说明) |
|---|------|-------------|-----------|------------|-----------|-----------|
| 1 | SERVER_TYPE | 服务器类型 | - | null | true | 服务器类型定义的父级代码。 |
| 2 | SERVER_TYPE.GENERAL_MANAGER | 统括管理器 | JP1/ITDM2(統括マネージャ) | SERVER_TYPE | true | 代表JP1/ITDM2的统括管理器。 |
| 3 | SERVER_TYPE.RELAY_MANAGER | 中继管理器 | JP1/ITDM2(中継マネージャ) | SERVER_TYPE | true | 代表JP1/ITDM2的中继管理器。 |
| 4 | SERVER_TYPE.HIBUN_CONSOLE | 管理控制台 | 秘文(管理コンソール) | SERVER_TYPE | true | 代表秘文(HIBUN)的管理控制台。 |

### 许可证类型 (`LICENSE_TYPE`)

| # | code | name (中文名称) | value (值) | parentCode | isEnabled | 備考 (中文说明) |
|---|------|-------------|-----------|------------|-----------|-----------|
| 5 | LICENSE_TYPE | 许可证类型 | - | null | true | 许可证类型定义的父级代码。 |
| 6 | LICENSE_TYPE.PROD | 产品版 | 製品版 | LICENSE_TYPE | true | 代表正式的产品版许可证。 |
| 7 | LICENSE_TYPE.TRIAL | 评估版 | 評価版 | LICENSE_TYPE | true | 代表评估版或试用版许可证。 |

### 操作系统类型 (`OS_TYPE`)

| # | code | name (中文名称) | value (值) | parentCode | isEnabled | 備考 (中文说明) |
|---|------|-------------|-----------|------------|-----------|-----------|
| 8 | OS_TYPE | OS类型 | - | null | true | 操作系统类型定义的父级代码。 |
| 9 | OS_TYPE.WIN | Windows | Windows | OS_TYPE | true | 代表Windows操作系统。 |
| 10 | OS_TYPE.LINUX | Linux | Linux | OS_TYPE | true | 代表Linux操作系统。 |
| 11 | OS_TYPE.AIX | AIX | AIX | OS_TYPE | true | 代表AIX操作系统。 |
| 12 | OS_TYPE.SOLARIS | Solaris | Solaris | OS_TYPE | true | 代表Solaris操作系统。 |
| 13 | OS_TYPE.HPUX | HP-UX | HP-UX | OS_TYPE | true | 代表HP-UX操作系统。 |
| 14 | OS_TYPE.MACOS | Mac | Mac | OS_TYPE | true | 代表macOS操作系统。 |

### 账户锁定配置 (`LOCKOUT`)

| # | code | name (中文名称) | value (值) | parentCode | isEnabled | 備考 (中文说明) |
|---|------|-------------|-----------|------------|-----------|-----------|
| 15 | LOCKOUT | 账户锁定配置 | - | null | true | 用户账户锁定策略相关的配置父级代码。 |
| 16 | LOCKOUT.MAX_FAILED_TIMES | 锁定失败最大次数 | 10 | LOCKOUT | true | 登录失败达到此次数后账户将被锁定。 |
| 17 | LOCKOUT.TIME_SECONDS | 锁定时长 (秒) | 1800 | LOCKOUT | true | 账户锁定时长（秒）。例如1800秒等于30分钟。 |

### 支持信息重要度 (`SUPPORT_IMPORTANCE`)

| # | code | name (中文名称) | value (值) | parentCode | isEnabled | 備考 (中文说明) |
|---|------|-------------|-----------|------------|-----------|-----------|
| 18 | SUPPORT_IMPORTANCE | 支持信息重要度 | - | null | true | 支持信息重要度定义的父级代码。 |
| 19 | SUPPORT_IMPORTANCE.NONE | 无重要度 | - | SUPPORT_IMPORTANCE | true | 无特定重要度，例如预防性维护信息或使用注意事项。 |
| 20 | SUPPORT_IMPORTANCE.AAA | AAA级 | AAA | SUPPORT_IMPORTANCE | true | 最高重要级别：导致业务系统运营停止且发生频率高的问题。 |
| 21 | SUPPORT_IMPORTANCE.AA | AA级 | AA | SUPPORT_IMPORTANCE | true | 次高重要级别：可能导致业务系统运营停止的问题。 |
| 22 | SUPPORT_IMPORTANCE.A | A级 | A | SUPPORT_IMPORTANCE | true | 重要级别：业务系统运营停止的可能性较低，但仍有影响。 |
| 23 | SUPPORT_IMPORTANCE.B | B级 | B | SUPPORT_IMPORTANCE | true | 一般重要级别：对业务系统运营影响较小。 |
| 24 | SUPPORT_IMPORTANCE.C | C级 | C | SUPPORT_IMPORTANCE | true | 较低重要级别：对业务系统运营影响几乎没有。 |

### Azure 存储相关应用配置 (`AZURE_STORAGE`)
*(注意：以下条目严格按照FS文档中的LOV表格结构列出。对于容器名称，本项目的推荐实现方式是通过环境变量进行具体配置，详见 `docs/guides/environment-variables.md`。此处的LOV条目主要用于确保与客户约定的FS文档在定义层面保持一致。)*

| # | code | name (中文名称) | value (值) | parentCode | isEnabled | 備考 (中文说明) |
|---|------|-------------|-----------|------------|-----------|-----------|
| 28 | AZURE_STORAGE | Azure存储配置 | - | null | true | Azure Storage 服务相关应用配置的父级代码。 |
| 29 | AZURE_STORAGE.CONTAINER_OPLOGS | 操作日志容器逻辑名 | oplogs | AZURE_STORAGE | true | 【FS约定】操作日志文件的Blob容器逻辑名。**实际容器名通过环境变量 `AZURE_STORAGE_CONTAINER_OPLOGS` 配置。** |
| 30 | AZURE_STORAGE.CONTAINER_PRODUCT_MEDIAS | 产品媒体容器逻辑名 | product-medias | AZURE_STORAGE | true | 【FS约定】产品媒体文件的Blob容器逻辑名。**实际容器名通过环境变量 `AZURE_STORAGE_CONTAINER_PRODUCT_MEDIAS` 配置。** |
| 31 | AZURE_STORAGE.CONTAINER_PRODUCT_MANUALS | 手册容器逻辑名 | product-manuals | AZURE_STORAGE | true | 【FS约定】产品手册文件的Blob容器逻辑名。**实际容器名通过环境变量 `AZURE_STORAGE_CONTAINER_PRODUCT_MANUALS` 配置。** |
| 32 | AZURE_STORAGE.CONTAINER_PROVIDED_FILES | 提供文件容器逻辑名 | provided-files | AZURE_STORAGE | true | 【FS约定】提供文件的Blob容器逻辑名。**实际容器名通过环境变量 `AZURE_STORAGE_CONTAINER_PROVIDED_FILES` 配置。** |
| 33 | AZURE_STORAGE.CONTAINER_SUPPORT_FILES | 支持文件容器逻辑名 | support-files | AZURE_STORAGE | true | 【FS约定】支持信息附件的Blob容器逻辑名。**实际容器名通过环境变量 `AZURE_STORAGE_CONTAINER_SUPPORT_FILES` 配置。** |
| 34 | AZURE_STORAGE.CONTAINER_ASSETSFIELD_DEF | 管理项目定义容器逻辑名 | assetsfield-def | AZURE_STORAGE | true | 【FS约定】管理项目定义文件的Blob容器逻辑名。**实际容器名通过环境变量 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` 配置。** |
| 35 | AZURE_STORAGE.SAS_TTL_SECONDS | SAS有效期限 (秒) | 7200 | AZURE_STORAGE | true | 为Azure Blob Storage生成的共享访问签名 (SAS) Token 的默认有效时长（秒）。例如7200秒等于2小时。 |

### 操作日志相关配置 (`OPERATION_LOG_CONFIG`)

| # | code | name (中文名称) | value (值) | parentCode | isEnabled | 備考 (中文说明) |
|---|------|-------------|-----------|------------|-----------|-----------|
| 36 | OPERATION_LOG_CONFIG | 操作日志相关配置 | - | null | true | 操作日志功能相关配置的父级代码。 |
| 37 | OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN | 操作日志导出最大天数跨度 | 30 | OPERATION_LOG_CONFIG | true | 用户进行操作日志导出时，允许选择的最大日期范围跨度（天）。 |
| 38 | OPERATION_LOG_CONFIG.ALLOWED_PLANS | 允许导出的套餐列表 | - | OPERATION_LOG_CONFIG | true | 定义哪些基本契约套餐代码被允许执行“操作日志导出”功能的配置组的父级代码。 |
| 39 | OPERATION_LOG_CONFIG.ALLOWED_PLANS.OPLOG_ALLOW_STANDARD_PLAN | 允许导出: STANDARD套餐 | STANDARD | OPERATION_LOG_CONFIG.ALLOWED_PLANS | true | 允许拥有“STANDARD”基本契约套餐代码的许可证执行“操作日志导出”功能。 |
| 40 | OPERATION_LOG_CONFIG.ALLOWED_PLANS.OPLOG_ALLOW_LIGHT_B_PLAN | 允许导出: LIGHT_B套餐 | LIGHT_B | OPERATION_LOG_CONFIG.ALLOWED_PLANS | true | 允许拥有“LIGHT_B”基本契约套餐代码的许可证执行“操作日志导出”功能。 |

### 后台任务相关配置 (`TASK_CONFIG`)
*(注意: 由于在`OPERATION_LOG_CONFIG`下新增了条目，原`TASK_CONFIG`的起始行号顺延。)*

| # | code | name (中文名称) | value (值) | parentCode | isEnabled | 備考 (中文说明) |
|---|------|-------------|-----------|------------|-----------|-----------|
| 41 | TASK_CONFIG | 任务相关配置 | - | null | true | 后台任务通用配置的父级代码。 |
| 42 | TASK_CONFIG.MAX_RETENTION_COUNT | 服务器任务保留上限数 | 10 | TASK_CONFIG | true | 每个服务器在 `Task` 表中允许保留的最大任务记录数量。超出此数量时，最旧的符合清理条件（通常是已完成的）的任务记录及其关联文件将被自动删除。 |

### 后台任务类型 (`TASK_TYPE`)
*(起始行号顺延)*

| # | code | name (中文名称) | value (值) | parentCode | isEnabled | 備考 (中文说明) |
|---|------|-------------|-----------|------------|-----------|-----------|
| 43 | TASK_TYPE | 任务类型 | - | null | true | 后台任务类型定义的父级代码。 |
| 44 | TASK_TYPE.OPLOG_EXPORT | 操作日志导出 | 操作ログのエクスポート | TASK_TYPE | true | 代表操作日志导出任务。(`value`列保留日文是为了对应原FS中可能的UI显示，但`name`为中文) |
| 45 | TASK_TYPE.MGMT_ITEM_IMPORT | 管理项目定义导入 | 管理項目定義のインポート | TASK_TYPE | true | 代表管理项目定义导入任务。(`value`列保留日文是为了对应原FS中可能的UI显示，但`name`为中文) |
| 46 | TASK_TYPE.MGMT_ITEM_EXPORT | 管理项目定义导出 | 管理項目定義のエクスポート | TASK_TYPE | true | 代表管理项目定义导出任务。(`value`列保留日文是为了对应原FS中可能的UI显示，但`name`为中文) |

### 后台任务状态 (`TASK_STATUS`)
*(起始行号顺延)*

| # | code | name (中文名称) | value (值) | parentCode | isEnabled | 備考 (中文说明) |
|---|------|-------------|-----------|------------|-----------|-----------|
| 47 | TASK_STATUS | 任务状态 | - | null | true | 后台任务状态定义的父级代码。 |
| 48 | TASK_STATUS.QUEUED | 执行等待 | 実行待ち | TASK_STATUS | true | **内部状态**: 任务已提交到系统，等待`TaskExecuteFunc`处理。<br/>**界面显示**: 「実行待ち」。<br/>**用户可操作**: 可中止。 |
| 49 | TASK_STATUS.RUNBOOK_SUBMITTED | Runbook作业创建完成 | 実行中 | TASK_STATUS | true | **内部状态**: `TaskExecuteFunc`已成功将作业提交到Azure Automation，等待Runbook执行或`RunbookMonitorFunc`轮询。<br/>**界面显示**: 「実行中」。<br/>**用户可操作**: 不可中止。 |
| 50 | TASK_STATUS.RUNBOOK_PROCESSING | Runbook作业处理中 | 実行中 | TASK_STATUS | true | **内部状态**: `RunbookMonitorFunc`已从Azure Automation获取到作业的最终结果（成功/失败/超时），并将原始结果发送到`RunbookStatusQueue`等待`RunbookProcessorFunc`处理。<br/>**界面显示**: 「実行中」。<br/>**用户可操作**: 不可中止。 |
| 51 | TASK_STATUS.COMPLETED_SUCCESS | 正常结束 | 正常終了 | TASK_STATUS | true | **内部状态**: `RunbookProcessorFunc`已成功完成所有后处理（包括文件归档、资源清理、并发锁释放），任务圆满结束。<br/>**界面显示**: 「正常終了」。<br/>**用户可操作**: 不可中止。 |
| 52 | TASK_STATUS.COMPLETED_ERROR | 错误 | エラー | TASK_STATUS | true | **内部状态**: 任务在生命周期的任何阶段因错误而结束。<br/>**界面显示**: 「エラー」。<br/>**用户可操作**: 不可中止。 |
| 53 | TASK_STATUS.CANCELLED | 中止 | 中止 | TASK_STATUS | true | **内部状态**: 任务在`QUEUED`或`PENDING_CANCELLATION`状态时被`TaskCancellationFunc`成功取消（或`TaskCancellationTimeoutFunc`处理`PENDING_CANCELLATION`超时后标记为错误但外部仍可能理解为中止的一种）。<br/>**界面显示**: 「中止」（或「エラー」如果`TaskCancellationTimeoutFunc`超时标记为`COMPLETED_ERROR`）。<br/>**用户可操作**: 不可中止。 |
| 54 | TASK_STATUS.PENDING_CANCELLATION | 中止等待 | 中止待ち | TASK_STATUS | true | **内部状态**: 用户已请求取消处于`QUEUED`状态的任务，门户已更新任务状态为此，并已将取消请求发送到`TaskControlQueue`等待`TaskCancellationFunc`处理。<br/>**界面显示**: 「中止待ち」。<br/>**用户可操作**: 不可中止 (中止请求已在处理中)。 |