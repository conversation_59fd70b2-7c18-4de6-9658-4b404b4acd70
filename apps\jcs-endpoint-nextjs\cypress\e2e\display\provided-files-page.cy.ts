import planFixture from "../../fixtures/PlanProvidedFile.json";
import filesFixture from "../../fixtures/ProvidedFile.json";

describe("初期化表示のテスト", () => {
  describe("提供ファイル一覧画面", () => {
    const validCredentials = {
      userId: "hitachi.taro.aa",
      password: "changeit!@#",
    };
    const scrollBarWidth = 15;

    beforeEach(() => {
      cy.visit("/login");
      cy.get("#userId").type(validCredentials.userId);
      cy.get("#password").type(validCredentials.password);
      cy.get("button").click();
      cy.wait(2000);
      cy.visit("/dashboard/provided-files");
    });

    it("タイトルが正しく表示される", () => {
      cy.title().should("eq", "提供ファイル一覧");
      cy.get("nav").should("contain", "ファイル");
      cy.get("nav").should("contain", "提供ファイル一覧");
      cy.get("aside .from-blue-600").should("contain", "提供ファイル一覧");
    });

    it("アクションバーが正しく表示される", () => {
      cy.get(".bg-white.h-full input").should(
        "have.attr",
        "placeholder",
        "フィルター",
      );
      cy.get(".bg-white.h-full button span").should("contain", "search");
      cy.get(".bg-white.h-full button span").should("contain", "clear");
      cy.get("#page-left").should("have.prop", "tagName", "DIV");
      cy.get("#page-right").should("have.prop", "tagName", "A");
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .first()
        .should("have.prop", "tagName", "DIV");
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .first()
        .nextAll()
        .should("have.prop", "tagName", "A");
      cy.get(".bg-white.h-full label").should("contain", "行数/ページ:");
      cy.get(".bg-white.h-full select option:selected").then(
        (selectedOption) => {
          cy.wrap(selectedOption).invoke("text").should("include", "10");
          cy.wrap(selectedOption).invoke("val").should("eq", "10");
        },
      );
    });

    it("ページ数が正しく表示される", () => {
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .first()
        .should("have.prop", "tagName", "DIV")
        .invoke("text")
        .should("eq", "1");
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .first()
        .next()
        .should("have.prop", "tagName", "A")
        .invoke("text")
        .should("eq", "2");
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .first()
        .next()
        .next()
        .should("have.prop", "tagName", "A")
        .invoke("text")
        .should("eq", "3");
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .first()
        .next()
        .next()
        .next()
        .should("have.prop", "tagName", "DIV")
        .invoke("text")
        .should("eq", "...");
      const maxPage = Math.ceil(
        planFixture.PlanProvidedFile.filter((p) =>
          ["standard", "lighta"].includes(p.planId),
        ).length / 10,
      );
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .last()
        .should("have.prop", "tagName", "A")
        .invoke("text")
        .should("eq", `${maxPage}`);
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .last()
        .prev()
        .should("have.prop", "tagName", "A")
        .invoke("text")
        .should("eq", `${maxPage - 1}`);
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .last()
        .prev()
        .prev()
        .should("have.prop", "tagName", "DIV")
        .invoke("text")
        .should("eq", "...");
    });

    it("テーブルヘーダが正しく表示される", () => {
      cy.get("thead th span").should("contain", "ファイル名");
      cy.get("thead th span").should("contain", "説明");
      cy.get("thead th span")
        .should("contain", "最終更新日時")
        .next("img")
        .should("exist");
      cy.get("thead th span")
        .should("contain", "最終更新日時")
        .next("img")
        .should("not.have.class", "rotate-180");
      cy.get("thead th span").should("contain", "サイズ");
    });

    it("テーブルボディが正しく表示される", () => {
      cy.get("table")
        .find("tbody th, tbody td:nth-child(2), tbody td:nth-child(3)")
        .each((element) => {
          cy.wrap(element).should("have.css", "textAlign", "left");
        });
      cy.get("table")
        .find("tbody td:nth-child(4)")
        .each((element) => {
          cy.wrap(element).should("have.css", "textAlign", "right");
        });
      cy.get("table")
        .find("tbody th")
        .find("a")
        .should("exist")
        .should("have.attr", "target", "_blank");
      cy.get("table tbody tr").each(($row) => {
        const textInFirstColumn = $row.find("th").text();
        const rowData = filesFixture.ProvidedFile.find(
          (s) => s.name === textInFirstColumn,
        );
        expect(textInFirstColumn).to.eq(rowData?.name);

        // @ts-ignore
        const hrefValue = $row.find("th a").attr("href");
        expect(hrefValue).to.eq(`provided-files/${rowData?.fileName}`);
      });
    });

    it("最終更新日時列が降順で、ファイル名列が昇順で並んでいる", () => {
      cy.get("table tbody tr").then(($rows) => {
        const columnData = $rows
          .map((_, row) => {
            const releaseDate = new Date(
              // @ts-ignore
              row.querySelector("td:nth-child(3)").innerText,
            ).getTime();
            // @ts-ignore
            const name = row.querySelector("th").innerText;
            return { releaseDate, name };
          })
          .get();

        const sortedColumnData = [...columnData].sort((a, b) => {
          const releaseDateComparison = b.releaseDate - a.releaseDate;
          if (releaseDateComparison !== 0) {
            return releaseDateComparison;
          }
          return a.name.localeCompare(b.name);
        });

        expect(columnData).to.deep.equal(sortedColumnData);
      });
    });

    it("テーブルには横方向および縦方向のスクロールバーがない", () => {
      cy.get(".rounded-b-lg").within(() => {
        cy.get("table").then(($child) => {
          const parentClientWidth = $child.parent().width() || 0;
          const parentClientHeight = $child.parent().height() || 0;

          const childScrollWidth = $child[0].scrollWidth;
          const childScrollHeight = $child[0].scrollHeight;

          expect(childScrollWidth).to.be.equal(parentClientWidth);
          expect(childScrollHeight).to.be.equal(parentClientHeight);
        });
      });
    });
  });
});
