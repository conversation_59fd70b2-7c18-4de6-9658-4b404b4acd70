/**
 * @fileoverview タスク中止要求受付処理の単体テスト
 * @description クライアントからの特定タスクの中止要求を受け付け、対象タスクの現在のステータスを検証し、
 * 中止が可能であればデータベース内のタスクステータスを更新し、
 * 後続の非同期中止処理のためのメッセージをService Busへ送信する処理の正常系・異常系を検証する。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

// 模拟 portal-error
jest.mock("@/app/lib/portal-error", () => ({
  handleServerError: jest.fn((error) => {
    const { PORTAL_ERROR_MESSAGES } = require("@/app/lib/definitions");
    const { PrismaClientInitializationError } = require("@prisma/client/runtime/library");

    if (error instanceof PrismaClientInitializationError) {
      throw new Error(PORTAL_ERROR_MESSAGES.EMEC0006);
    }
    throw new Error(PORTAL_ERROR_MESSAGES.EMEC0007);
  }),
}));

// 模拟 prisma
jest.mock("@/app/lib/prisma", () => ({
  __esModule: true,
  default: {
    task: {
      findUnique: jest.fn(),
      update: jest.fn(),
      updateMany: jest.fn(),
    },
  },
}));

// 模拟 Azure Service Bus
jest.mock("@/app/lib/integrations/azure-service-bus", () => ({
  ServiceBusActions: {
    sendMessage: jest.fn(),
  },
}));

// 模拟 definitions.ts 中的 ENV 对象
jest.mock("@/app/lib/definitions", () => ({
  ...jest.requireActual("@/app/lib/definitions"),
  ENV: {
    ...jest.requireActual("@/app/lib/definitions").ENV,
    SERVICE_BUS_TASK_CONTROL_QUEUE_NAME: "task-control-queue-dev",
  },
}));

import { expect } from "@jest/globals";
import { cancelTask } from "@/app/lib/actions/tasks";
import { TASK_STATUS, PORTAL_ERROR_MESSAGES } from "@/app/lib/definitions";
import { getIronSession } from "iron-session";
import prisma from "@/app/lib/prisma";
import { ServiceBusActions } from "@/app/lib/integrations/azure-service-bus";

const prismaMock = prisma as jest.Mocked<typeof prisma>;

// iron-sessionのモック
jest.mock("iron-session", () => ({
  getIronSession: jest.fn(),
}));

// next/headersのモック
jest.mock("next/headers", () => ({
  cookies: jest.fn(),
}));

// next/cacheのモック
jest.mock("next/cache", () => ({
  revalidateTag: jest.fn(),
  unstable_cache: jest.fn((fn) => fn),
}));



describe("タスク中止機能", () => {
  const mockTask = {
    id: "task-1",
    taskName: "TestServer-操作ログのエクスポート-20240101100000",
    status: TASK_STATUS.QUEUED,
    taskType: "OPLOG_EXPORT",
    submittedAt: new Date("2024-01-01T09:59:00Z"),
    startedAt: new Date("2024-01-01T10:00:00Z"),
    endedAt: null,
    updatedAt: new Date("2024-01-01T10:00:00Z"),
    submittedByUserId: "<EMAIL>",
    targetVmName: "vm-test",
    targetContainerName: "container-test",
    targetServerName: "TestServer",
    targetHRWGroupName: "group-test",
    targetServerId: "server-1",
    parametersJson: "{}",
    resultMessage: null,
    errorMessage: null,
    errorCode: null,
    licenseId: "test-license-123",
    createdAt: new Date("2024-01-01T09:59:00Z"),
    outputBlobPath: null,
  };

  beforeEach(() => {
    jest.clearAllMocks();

    (getIronSession as jest.Mock).mockResolvedValue({
      user: {
        userId: "<EMAIL>",
        licenseId: "test-license-123",
        tz: "Asia/Tokyo",
      },
    });

    // Service Bus模拟设置
    (ServiceBusActions.sendMessage as jest.Mock).mockResolvedValue(undefined);
  });

  /**
   * 試験観点：実行待ち状態のタスクに対する正常な中止処理
   * 試験対象：requestTaskCancellation関数のQUEUED状態タスク処理
   * 試験手順：
   * 1. QUEUED状態のタスクデータをモック設定
   * 2. データベース更新処理を成功するようモック設定
   * 3. Service Bus送信処理を成功するようモック設定
   * 4. cancelTask関数を実行
   * 確認項目：
   * - タスクIDと最終更新日時を条件とした楽観的ロック制御でのupdateMany実行
   * - ステータスがPENDING_CANCELLATIONに更新されること
   * - Service Busへタスク中止メッセージが送信されること
   * - 成功応答としてタスク中止受付完了メッセージが返されること
   */
  it("正常系: 実行待ちタスクの中止処理", async () => {
    (prismaMock.task.findUnique as jest.Mock).mockResolvedValue(mockTask);
    (prismaMock.task.update as jest.Mock).mockResolvedValue({
      ...mockTask,
      status: TASK_STATUS.PENDING_CANCELLATION,
      updatedAt: new Date(),
    });
    (prismaMock.task.updateMany as jest.Mock).mockResolvedValue({ count: 1 });

    const result = await cancelTask("task-1");

    // 設計文書「06-タスク中止要求受付処理詳細.md」に基づく期待値
    // EMEC0026: "タスクの中止を受け付けました。タスクのステータスはタスク一覧画面で確認してください。\nタスク名：{0}"
    expect(result.success).toBe(true);
    expect(result.message).toContain("タスクの中止を受け付けました");
    expect(result.message).toContain("タスクのステータスはタスク一覧画面で確認してください");
    expect(result.message).toContain("TestServer-操作ログのエクスポート-20240101100000");

    // 設計文書に基づく楽観的ロック制御の確認
    // タスクIDが一致、及び最終更新日時がステップ2.で取得した日時と一致することを条件とする
    expect(prismaMock.task.updateMany).toHaveBeenCalledWith({
      where: {
        id: "task-1",
        status: TASK_STATUS.QUEUED,
        updatedAt: mockTask.updatedAt,
      },
      data: {
        status: TASK_STATUS.PENDING_CANCELLATION,
        updatedAt: expect.any(Date),
      },
    });

    // Service Busへのメッセージ送信確認
    expect(ServiceBusActions.sendMessage).toHaveBeenCalledWith(
      "task-control-queue-dev",
      { taskId: "task-1" }
    );
  });

  /**
   * 試験観点：存在しないタスクIDに対する中止要求のエラーハンドリング
   * 試験対象：requestTaskCancellation関数のタスク情報取得処理
   * 試験手順：
   * 1. データベースからnullが返されるようモック設定（タスク不存在を模擬）
   * 2. 存在しないタスクIDでcancelTask関数を実行
   * 3. エラー応答の内容を検証
   * 確認項目：
   * - 処理結果がfalseであること
   * - サーバ接続失敗を示すエラーメッセージが返されること
   * - データベース更新処理が実行されないこと
   * - Service Bus送信処理が実行されないこと
   */
  it("異常系: 存在しないタスクの中止処理", async () => {
    (prismaMock.task.findUnique as jest.Mock).mockResolvedValue(null);

    const result = await cancelTask("non-existent-task");

    // 設計文書に基づく期待値: EMEC0021
    expect(result.success).toBe(false);
    expect(result.message).toContain("サーバの接続に失敗したため、タスクを中止できませんでした");
  });

  /**
   * 試験観点：実行中状態のタスクに対する中止要求の拒否処理
   * 試験対象：requestTaskCancellation関数のタスクステータス分岐処理
   * 試験手順：
   * 1. RUNBOOK_SUBMITTED状態のタスクデータをモック設定
   * 2. cancelTask関数を実行
   * 3. 中止不可を示すエラー応答の内容を検証
   * 確認項目：
   * - 処理結果がfalseであること
   * - タスク中止不可を示すエラーメッセージが返されること
   * - データベース更新処理が実行されないこと
   * - Service Bus送信処理が実行されないこと
   */
  it("異常系: 中止不可能なステータスのタスク", async () => {
    const runningTask = {
      ...mockTask,
      status: TASK_STATUS.RUNBOOK_SUBMITTED,
    };
    (prismaMock.task.findUnique as jest.Mock).mockResolvedValue(runningTask);

    const result = await cancelTask("task-1");

    // 設計文書に基づく期待値: EMEC0023
    expect(result.success).toBe(false);
    expect(result.message).toContain("タスクの中止はできません");
    expect(result.message).toContain("他のユーザーによって中止操作が行われたか、すでに実行中か、もしくは実行が終了しています");
  });

  /**
   * 試験観点：他ユーザーが作成したタスクに対する中止要求の処理
   * 試験対象：requestTaskCancellation関数の権限制御処理
   * 試験手順：
   * 1. 他ユーザーが作成したタスクデータをモック設定
   * 2. cancelTask関数を実行
   * 3. エラー応答の内容を検証
   * 確認項目：
   * - 処理結果がfalseであること
   * - サーバ接続失敗を示すエラーメッセージが返されること
   * - データベース更新処理が実行されないこと
   * - Service Bus送信処理が実行されないこと
   */
  it("異常系: 他ユーザーのタスク中止試行", async () => {
    const otherUserTask = {
      ...mockTask,
      submittedByUserId: "<EMAIL>",
    };
    (prismaMock.task.findUnique as jest.Mock).mockResolvedValue(otherUserTask);

    const result = await cancelTask("task-1");

    // 設計文書に基づく期待値: 権限チェックは実装されていない可能性
    // 実際の実装では EMEC0021 が返される
    expect(result.success).toBe(false);
    expect(result.message).toContain("サーバの接続に失敗したため、タスクを中止できませんでした");
  });

  /**
   * 試験観点：既に中止済み状態のタスクに対する中止要求の拒否処理
   * 試験対象：requestTaskCancellation関数のタスクステータス分岐処理
   * 試験手順：
   * 1. CANCELLED状態のタスクデータをモック設定
   * 2. cancelTask関数を実行
   * 3. 中止不可を示すエラー応答の内容を検証
   * 確認項目：
   * - 処理結果がfalseであること
   * - タスク中止不可を示すエラーメッセージが返されること
   * - データベース更新処理が実行されないこと
   * - Service Bus送信処理が実行されないこと
   */
  it("境界条件: 既に中止済みのタスク", async () => {
    const cancelledTask = {
      ...mockTask,
      status: TASK_STATUS.CANCELLED,
    };
    (prismaMock.task.findUnique as jest.Mock).mockResolvedValue(cancelledTask);

    const result = await cancelTask("task-1");

    // 設計文書に基づく期待値: EMEC0023
    expect(result.success).toBe(false);
    expect(result.message).toContain("タスクの中止はできません");
    expect(result.message).toContain("他のユーザーによって中止操作が行われたか、すでに実行中か、もしくは実行が終了しています");
  });

  /**
   * 試験観点：データベース更新処理における例外発生時のエラーハンドリング
   * 試験対象：requestTaskCancellation関数の例外処理機能
   * 試験手順：
   * 1. タスク情報取得は成功するようモック設定
   * 2. データベース更新処理で例外が発生するようモック設定
   * 3. cancelTask関数を実行
   * 4. 例外処理によるエラー応答の内容を検証
   * 確認項目：
   * - 処理結果がfalseであること
   * - 予期しないエラーを示すエラーメッセージが返されること
   * - Service Bus送信処理が実行されないこと
   */
  it("異常系: データベース更新エラー処理", async () => {
    (prismaMock.task.findUnique as jest.Mock).mockResolvedValue(mockTask);
    (prismaMock.task.updateMany as jest.Mock).mockRejectedValue(new Error("Database connection failed"));

    const result = await cancelTask("task-1");

    // 設計文書に基づく期待値: EMEC0027
    expect(result.success).toBe(false);
    expect(result.message).toContain("サーバの接続に失敗したため、タスクを中止できませんでした");
  });

  /**
   * 試験観点：セッション情報不存在時のエラーハンドリング
   * 試験対象：requestTaskCancellation関数のセッション検証処理
   * 試験手順：
   * 1. 空のセッション情報をモック設定
   * 2. cancelTask関数を実行
   * 3. セッションエラー応答の内容を検証
   * 確認項目：
   * - 処理結果がfalseであること
   * - セッション不正を示すエラーメッセージが返されること
   * - データベース処理が実行されないこと
   * - Service Bus送信処理が実行されないこと
   */
  it("異常系: セッション情報が存在しない場合", async () => {
    (getIronSession as jest.Mock).mockResolvedValue({});

    const result = await cancelTask("task-1");

    expect(result.success).toBe(false);
    expect(result.message).toContain("中止");
  });

  /**
   * 試験観点：空のタスクIDに対する入力パラメータ検証
   * 試験対象：requestTaskCancellation関数の入力パラメータ検証処理
   * 試験手順：
   * 1. 空文字列のタスクIDでcancelTask関数を実行
   * 2. パラメータエラー応答の内容を検証
   * 確認項目：
   * - 処理結果がfalseであること
   * - パラメータ不正を示すエラーメッセージが返されること
   * - データベース処理が実行されないこと
   * - Service Bus送信処理が実行されないこと
   */
  it("異常系: taskIdが空の場合", async () => {
    const result = await cancelTask("");

    expect(result.success).toBe(false);
    expect(result.message).toContain("中止");
  });

  /**
   * 試験観点：異なるライセンスIDのタスクに対する中止要求の拒否処理
   * 試験対象：requestTaskCancellation関数の権限制御処理
   * 試験手順：
   * 1. 異なるライセンスIDを持つタスクデータをモック設定
   * 2. cancelTask関数を実行
   * 3. 権限エラー応答の内容を検証
   * 確認項目：
   * - 処理結果がfalseであること
   * - 権限不足を示すエラーメッセージが返されること
   * - データベース更新処理が実行されないこと
   * - Service Bus送信処理が実行されないこと
   */
  it("異常系: ライセンスIDが異なるタスクの中止試行", async () => {
    const differentLicenseTask = { ...mockTask, licenseId: "different-license" };
    (prismaMock.task.findUnique as jest.Mock).mockResolvedValue(differentLicenseTask);

    const result = await cancelTask("task-1");

    expect(result.success).toBe(false);
    expect(result.message).toContain("中止");
  });

  /**
   * 試験観点：楽観的ロック制御による更新失敗時のエラーハンドリング
   * 試験対象：requestTaskCancellation関数の楽観的ロック制御処理
   * 試験手順：
   * 1. タスク情報取得は成功するようモック設定
   * 2. データベース更新処理で更新件数0を返すようモック設定
   * 3. cancelTask関数を実行
   * 4. 楽観的ロック制御失敗エラー応答の内容を検証
   * 確認項目：
   * - 処理結果がfalseであること
   * - 楽観的ロック制御失敗を示すエラーメッセージが返されること
   * - Service Bus送信処理が実行されないこと
   */
  it("異常系: 楽観的ロック制御失敗", async () => {
    (prismaMock.task.findUnique as jest.Mock).mockResolvedValue(mockTask);
    (prismaMock.task.updateMany as jest.Mock).mockResolvedValue({ count: 0 });

    const result = await cancelTask("task-1");

    expect(result.success).toBe(false);
    expect(result.message).toContain("タスクの中止はできません");
  });

  /**
   * 試験観点：Service Bus送信失敗時の補償処理機能
   * 試験対象：requestTaskCancellation関数の補償処理機能
   * 試験手順：
   * 1. タスク情報取得とデータベース更新は成功するようモック設定
   * 2. Service Bus送信処理で例外が発生するようモック設定
   * 3. 補償処理のデータベース更新は成功するようモック設定
   * 4. cancelTask関数を実行
   * 5. 補償処理実行とエラー応答の内容を検証
   * 確認項目：
   * - 処理結果がfalseであること
   * - Service Bus送信失敗を示すエラーメッセージが返されること
   * - 補償処理でタスクステータスがQUEUEDに戻されること
   */
  it("異常系: Service Bus送信失敗時の補償処理", async () => {
    (prismaMock.task.findUnique as jest.Mock).mockResolvedValue(mockTask);
    (prismaMock.task.updateMany as jest.Mock).mockResolvedValue({ count: 1 });
    (ServiceBusActions.sendMessage as jest.Mock).mockRejectedValue(new Error("Service Bus failed"));
    (prismaMock.task.update as jest.Mock).mockResolvedValue(mockTask);

    const result = await cancelTask("task-1");

    // 設計文書に基づく期待値: EMEC0019
    expect(result.success).toBe(false);
    expect(result.message).toContain("サーバの接続に失敗したため、タスクを中止できませんでした");

    // 補償処理でタスクステータスがQUEUEDに戻されることを確認
    expect(prismaMock.task.update).toHaveBeenCalledWith({
      where: { id: "task-1" },
      data: { status: TASK_STATUS.QUEUED },
    });
  });

  /**
   * 試験観点：Service Bus送信失敗時の補償処理自体も失敗する場合のエラーハンドリング
   * 試験対象：requestTaskCancellation関数の補償処理例外処理機能
   * 試験手順：
   * 1. タスク情報取得とデータベース更新は成功するようモック設定
   * 2. Service Bus送信処理で例外が発生するようモック設定
   * 3. 補償処理のデータベース更新でも例外が発生するようモック設定
   * 4. cancelTask関数を実行
   * 5. 補償処理失敗時のエラー応答の内容を検証
   * 確認項目：
   * - 処理結果がfalseであること
   * - Service Bus送信失敗を示すエラーメッセージが返されること
   * - 補償処理失敗のログが記録されること
   */
  it("異常系: Service Bus送信失敗時の補償処理も失敗", async () => {
    (prismaMock.task.findUnique as jest.Mock).mockResolvedValue(mockTask);
    (prismaMock.task.updateMany as jest.Mock).mockResolvedValue({ count: 1 });
    (ServiceBusActions.sendMessage as jest.Mock).mockRejectedValue(new Error("Service Bus failed"));
    (prismaMock.task.update as jest.Mock).mockRejectedValue(new Error("Rollback failed"));

    const result = await cancelTask("task-1");

    // 設計文書に基づく期待値: EMEC0019（補償処理失敗でも同じエラーメッセージ）
    expect(result.success).toBe(false);
    expect(result.message).toContain("サーバの接続に失敗したため、タスクを中止できませんでした");
  });

});
