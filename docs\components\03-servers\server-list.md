# 组件：服务器列表主功能 (Server List Main Feature)

## 1. 概要 (Overview)

### 1.1 目的 (Purpose)
本组件为用户（通常是顾客系统管理员）提供一个集中管理和查看与资产分发服务相关的JP1/ITDM2（统括管理器、中継管理器）和秘文（管理控制台）服务器信息的界面。用户不仅可以浏览服务器的基本详情，还可以通过此界面发起针对特定服务器的后台管理任务，如管理项目定义的导入/导出以及操作日志的导出。同时，提供到各服务器原生管理界面的便捷访问入口。

### 1.2 用户故事/需求 (User Stories/Requirements)
*   作为一名顾客系统管理员，我希望能够在一个统一的视图中查看到我所管理的所有JP1/ITDM2和秘文服务器的关键信息（如名称、类型、状态），以便快速了解整体情况。
*   作为一名顾客系统管理员，我希望能够方便地从服务器列表直接跳转到任何一个服务器的独立管理控制台，以便进行更详细的配置和监控。
*   作为一名顾客系统管理员，我希望能够从服务器列表中针对特定的JP1/ITDM2服务器发起“管理项目定义导入”、“管理项目定义导出”和“操作日志导出”等任务，并且在执行前能得到清晰的确认。

### 1.3 高层依赖与交互 (High-Level Dependencies & Interactions)
*   **依赖于**:
    *   用户认证模块：确保用户已登录并具有访问权限，并能获取用户的许可证计划信息 (`License.basicPlan`)。
    *   数据服务模块 (`apps/jcs-endpoint-nextjs/app/lib/data.ts`)：用于从数据库获取服务器数据和相关的LOV数据（如服务器类型、任务类型、任务配置参数、以及操作日志导出权限相关的`OPERATION_LOG_CONFIG.ALLOWED_PLANS`配置等）。
    *   常量定义模块 (`apps/jcs-endpoint-nextjs/app/lib/definitions.ts`)：用于获取用户提示信息的消息键 (其值应与 [`错误消息定义`](../../definitions/error-messages.md) 中定义的 `Message Key` 一致)。
    *   Next.js App Router 的 `searchParams`：用于管理列表的筛选、排序和分页状态。
    *   通用UI组件 (位于 `apps/jcs-endpoint-nextjs/app/ui/`)：如搜索框、表格、分页控件、通用消息模态框 (`message-modal.tsx`)。
    *   特定任务参数模态框 (位于 `apps/jcs-endpoint-nextjs/app/ui/servers/modals/`)。
    *   Server Action [`createTaskAction` 组件设计文档](../actions/create-task-action.md)：作为所有后台任务创建请求的统一入口。
    *   环境变量：间接依赖于 `createTaskAction` 所需的基础设施配置（如数据库连接、Service Bus连接、Blob Storage连接及容器/队列名称等），详细定义参见 [`环境变量指南`](../../guides/environment-variables.md)。
    *   系统级定义文档 (位于 `docs/definitions/`):
        *   [`LOV值列表定义`](../../definitions/lov-definitions.md)
        *   [`错误消息定义`](../../definitions/error-messages.md)
        *   [`项目术语表`](../../definitions/glossary.md)
*   **交互**:
    *   用户通过UI控件（筛选框、排序表头、分页控件）与列表进行交互，这些操作通过更新URL `searchParams` 触发服务器组件重新获取数据并渲染。
    *   用户通过服务器列表行中的任务操作下拉菜单发起具体的后台任务。
        *   若任务需要参数（如操作日志导出、管理项目定义导入），本组件 (`ServerListPage`) 会先打开相应的特定任务参数输入模态框。用户在参数模态框中提交参数后，本组件会接着打开一个通用的二次确认模态框。**如果用户在最终确认模态框中选择取消，应能返回到参数输入模态框，并且之前输入或选择的参数应被保留。**
        *   若任务不需要参数（如管理项目定义导出），本组件会直接打开一个通用的二次确认模态框。
        *   用户在通用的二次确认模态框中最终确认后，本组件才调用 [`createTaskAction` Server Action](../actions/create-task-action.md)。**`createTaskAction` 成功后会将任务消息发送到队列，后续由一系列后端Azure Functions进行异步处理，主要包括：[`TaskExecuteFunc`](../backend-services-functions/function-task-execute.md) 负责任务的启动和Runbook提交，[`RunbookMonitorFunc`](../backend-services-functions/function-runbook-monitor.md) 负责监控Runbook执行状态，[`RunbookProcessorFunc`](../backend-services-functions/function-runbook-processor.md) 负责处理最终结果和资源清理。前端在 `createTaskAction` 调用成功后，不直接获取新任务ID，而是通过后续的任务列表刷新来查看新创建的任务及其状态。**
    *   具体任务（如操作日志导出、管理项目定义导入/导出）的详细参数输入界面和特定逻辑，由各自独立的组件设计文档定义，并通过本组件的UI入口进行调用和协调。
        *   [操作日志导出功能组件设计](./tasks/export-operation-log.md)
        *   [管理项目定义导入功能组件设计](./tasks/import-management-definition.md)
        *   [管理项目定义导出功能组件设计](./tasks/export-management-definition.md)

## 2. 功能规格 (Functional Specifications)

### 2.1 主要流程 (High-Level Flow)

#### 2.1.1 用户访问与浏览服务器列表
```mermaid
graph TD
    A["用户: 访问服务器列表页面<br>(/dashboard/servers)"] --> B["系统: (服务器端) <br/>获取用户会话及权限 (包括License.basicPlan)"];
    B -- 认证通过 --> C["系统: (服务器端) <br/>根据URL searchParams (或默认值)<br/>调用数据服务获取服务器列表数据<br/>及总页数, 并判断操作日志导出权限。<br/>(数据加载时显示骨架加载效果)"];
    C --> D["系统: (服务器端) <br/>渲染服务器列表页面<br/>(包含列表、搜索框、分页控件等，<br/>任务菜单项根据权限显隐)"];
    D --> E["用户: 查看服务器列表"];
    B -- 认证失败 --> F["系统: 重定向到登录页面"];
    C -- 数据获取失败 (例如 EMEC0006) --> G["系统: (服务器端)<br/>通过 Next.js 错误处理机制<br/>显示错误页面或信息"];
```

#### 2.1.2 用户执行列表操作 (筛选、排序、分页)
```mermaid
graph TD
    A["用户: 在服务器列表页面<br/>与筛选框、排序表头或分页控件进行交互"] --> B["客户端UI组件: 捕获用户操作<br/>(例如: 输入筛选词、点击排序列、选择页码/每页条数)"];
    B --> C["客户端UI组件: 更新浏览器URL<br/>(修改 searchParams 的 filter, sort, order, page, size 等参数)"];
    C --> D["Next.js App Router: 检测到URL变化，<br/>触发服务器列表页面组件 (page.tsx) 重新执行"];
    D --> E["系统: (服务器端) 根据新的 searchParams <br/>重新获取并过滤/排序/分页服务器数据，并重新判断操作日志导出权限。<br/>(数据加载时显示骨架加载效果)"];
    E --> F["系统: (服务器端) 重新渲染服务器列表<br/>及分页状态并返回给客户端"];
    F --> G["用户: 看到更新后的服务器列表"];
```

#### 2.1.3 用户发起后台任务 (通用入口与两阶段确认流程)
```mermaid
graph TD
    A["用户: 在服务器列表的某条服务器记录上<br/>点击“任务(タスク)”操作下拉菜单"] --> B["用户: 从菜单中选择一个具体的后台任务<br/>(例如: “操作日志导出”)"];
    B --> C["服务器列表页面 (ServerListPage Frontend): <br/>根据任务类型，决定下一步流程"];
    
    subgraph TaskRequiresParams ["若任务需要参数 (如操作日志导出, 管理项目定义导入)"]
        direction TB
        C --> CP1["ServerListPage: 打开特定任务参数模态框<br/>(例如 OperationLogExportModal)"];
        CP1 --> CP2["用户: 在参数模态框中提供必要参数并点击“提交”"];
        CP2 -- 参数通过客户端校验 --> CP3["特定任务参数模态框:<br/>将有效参数回调给 ServerListPage"];
        CP3 --> CC["ServerListPage"];
        CP2 -- 参数未通过客户端校验 --> CP_Error["特定任务参数模态框:<br/>在模态框内显示错误提示"];
        CP1 -- 用户取消或关闭参数模态框 --> EndCancel["操作取消"];
    end

    subgraph TaskNoParams ["若任务不需要参数 (如管理项目定义导出)"]
        direction TB
        C --> CNP1["ServerListPage"];
        CNP1 --> CC;
    end
    
    CC --> D["ServerListPage:<br/>打开通用二次确认模态框 (MessageModal)<br/>(填充任务类型、服务器名及已获取的参数)"];
    D -- 用户在 MessageModal 点击“OK/はい” --> E["ServerListPage (Frontend):<br/>调用 createTaskAction Server Action<br/>(传递 taskType, serverId 及任务特定参数)"];
    E --> F["Next.js 服务器: <br/>执行 createTaskAction 逻辑<br/>(参数校验、并发检查、权限再校验(针对操作日志导出)、创建任务记录、发送消息到队列)"];
    F -- 任务提交成功 (返回 messageId EMEC0025) --> G["ServerListPage (Frontend): <br/>接收成功响应，根据 messageId EMEC0025<br/>向用户显示任务已提交的日文提示。<br/>(关闭所有相关模态框)"];
    G --> H["用户: 可在“任务列表”等相关界面<br/>跟踪此后台任务的执行状态和最终结果。<br/>(前端不直接从createTaskAction获取taskId)"];
    F -- 任务提交失败 (返回具体 messageId) --> I["ServerListPage (Frontend): <br/>接收失败响应，根据返回的 messageId<br/>向用户显示相应的日文错误提示。<br/>(关闭所有相关模态框)"];
    D -- "用户在 MessageModal 点击“キャンセル”或关闭" --> EndCancelWithParams["操作取消。<br/>若之前有参数模态框，则返回参数模态框<br/>并保留用户已输入/选择的参数。<br/>否则，直接取消操作。"];

    CP_Error --> EndError[流程结束];
    I --> EndError;
    EndCancel --> X[流程结束];
    EndCancelWithParams --> X;
```

#### 2.1.4 访问服务器管理界面
1.  用户在列表中找到目标服务器。
2.  点击该服务器记录中“管理画面”列的链接。
3.  系统在新浏览器标签页中打开该服务器的`Server.url`所指向的地址。

### 2.2 业务规则 (Business Rules)
*   **数据显示**:
    *   界面上显示的服务器名称、类型、管理画面URL等信息来源于数据库的 `Server` 表 (通过 `app/lib/data.ts` 获取并缓存)。数据加载时，应显示**骨架加载效果**。
    *   服务器类型 (`Server.type`) 的显示值 (日文) 通过 `LOV` 表 (parentCode: `'SERVER_TYPE'`) 进行映射转换后显示。此转换在 `app/lib/data.ts#fetchCachedServers` 中完成。参考[`LOV值列表定义`](../../definitions/lov-definitions.md)。
*   **列表交互**:
    *   **筛选**: 用户可在搜索框输入关键字，系统将对服务器名称 (`Server.name`)、类型显示值 (日文)、管理画面URL (`Server.url`) 进行不区分大小写的包含匹配。筛选操作由 `app/lib/data.ts#fetchFilteredServers` 在内存中执行。
    *   **排序**: 用户可点击“サーバ名”、“種別”、“管理画面”列标题进行升序/降序排序。默认按“サーバ名”升序排列。排序操作由 `app/lib/data.ts#fetchFilteredServers` 在内存中执行。
    *   **分页**: 用户可通过分页控件和每页行数选择器控制列表显示。默认每页显示10条。分页操作由 `app/lib/data.ts#fetchFilteredServers` 在内存中执行。
*   **后台任务可用性**: 各种后台任务操作（如操作日志导出、管理项目定义导入/导出）的可用性取决于服务器类型 (Server.type) 以及用户的许可证计划 (`License.basicPlan`)。
    *   **“操作ログのエクスポート”任务**：
        1.  仅对服务器类型代码为 `SERVER_TYPE.GENERAL_MANAGER` (統括マネージャ) 或 `SERVER_TYPE.RELAY_MANAGER` (中継マネージャ) 的服务器考虑显示此任务选项。这些服务器类型代码定义在数据库 `Lov` 表中，其 `parentCode` 为 `SERVER_TYPE` (参考[`LOV值列表定义`](../../definitions/lov-definitions.md) - サーバ種別)。
        2.  用户的许可证计划 (`License.basicPlan`) 必须在 `LOV` 表中 `parentCode='OPERATION_LOG_CONFIG.ALLOWED_PLANS'` 定义的允许列表中。例如，如果 `OPERATION_LOG_CONFIG.ALLOWED_PLANS` 下有子条目的 `value` 为 `'STANDARD'`，则拥有 `'STANDARD'` 计划的用户才有权限。此权限判断由 `app/lib/data.ts` 中的 `checkOplogExportPermission` 函数实现，结果将用于控制UI显隐。
        3.  只有同时满足以上两个条件时，才在任务操作下拉菜单中显示“操作ログのエクスポート”选项。
    *   “管理項目定義のインポート”和“管理項目定義のエクスポート”任务：通常对所有JP1/ITDM2相关类型的服务器（`SERVER_TYPE.GENERAL_MANAGER` 或 `SERVER_TYPE.RELAY_MANAGER`）在任务操作下拉菜单中显示相应的任务选项。
    此逻辑由前端UI组件（任务操作下拉菜单）根据传入的 `Server.type`（服务器类型代码）和 `canExportOplog` (操作日志导出权限布尔值) 控制。
*   **并发控制**: (由 [`createTaskAction` Server Action](../actions/create-task-action.md) 进行并发检查，如果记录不存在则创建，如果存在且为IDLE则尝试获取锁，实际写锁由后端 `TaskExecuteFunc` 处理) 单个服务器（由其关联的Docker容器标识）在同一时间只能处理一个后台任务。若尝试为繁忙容器创建任务，`createTaskAction`将返回包含错误消息ID (`EMEC0022`) 的失败结果。

### 2.3 用户界面概述 (User Interface Overview)
*   **主页面路径**: `/dashboard/servers`
*   **核心UI组件 (位于 `apps/jcs-endpoint-nextjs/app/` 目录下)**:
    *   **页面容器 (`dashboard/servers/page.tsx`)**: 负责整体布局、数据获取的发起（包括调用 `checkOplogExportPermission` 获取操作日志导出权限）、以及协调管理各类任务参数模态框和通用二次确认模态框的显隐与交互。
    *   **搜索组件 (`ui/search.tsx`)**: 提供关键字输入，更新URL `searchParams.filter`。
    *   **服务器表格组件 (`ui/servers/table.tsx`)**: 负责展示服务器数据，包含可排序的表头 (`ui/thead.tsx`) 和表格行。每行包含服务器信息、管理画面链接以及任务操作下拉菜单。此组件接收 `canExportOplog` prop 以控制“操作ログのエクスポート”菜单项的显示。数据加载时，应显示**骨架加载效果**。
    *   **分页控件 (`ui/pagination.tsx`)**: 提供页码导航，更新URL `searchParams.page`。
    *   **每页行数选择器 (`ui/page-size.tsx`)**: 允许用户选择每页显示条数，更新URL `searchParams.size`。
    *   **会话刷新组件 (`ui/refreshToken.tsx`)**: 处理用户会话令牌的自动刷新。
    *   **任务操作下拉菜单 (在 `ui/servers/table.tsx` 中实现)**: 根据服务器类型和 `canExportOplog` 权限动态显示可用的后台任务选项。
    *   **各类任务参数模态框**: (由 `page.tsx` 根据选择的任务类型动态加载和管理)
        *   操作日志导出: [`ui/servers/modals/operation-log-export-modal.tsx`](./tasks/export-operation-log.md#321-操作日志导出参数对话框)
        *   管理项目定义导入: [`ui/servers/modals/management-definition-import-modal.tsx`](./tasks/import-management-definition.md#321-管理项目定义导入参数对话框)
    *   **通用二次确认模态框 (`ui/message-modal.tsx`)**: (由 `page.tsx` 调用) 用于所有任务在调用 `createTaskAction` 前的最终用户确认。其标题和消息文本会根据具体任务动态配置。
        *   操作日志导出任务的确认消息: 使用消息ID `EMEC0026` 对应的日文模板 “{0}の操作ログを{1}から{2}の期間でエクスポートします。\nよろしいですか？”。其中 `{0}` 由目标服务器名称 (`server.name`) 替换，`{1}` 由[操作日志导出功能组件设计](./tasks/export-operation-log.md)的参数模态框中用户选择的开始日期替换，`{2}` 由结束日期替换。
        *   管理项目定义导入任务的确认消息: “{サーバ名}の管理項目定義をインポートします。よろしいですか？”。其中 `{サーバ名}` 由目标服务器名称 (`server.name`) 替换。 (参考[管理项目定义导入功能组件设计](./tasks/import-management-definition.md)的1.3节)。
        *   管理项目定义导出任务的确认消息: “{サーバ名}の管理項目定義をエクスポートします。よろしいですか？”。其中 `{サーバ名}` 由目标服务器名称 (`server.name`) 替换。 (参考[管理项目定义导出功能组件设计](./tasks/export-management-definition.md)的1.3节)。

### 2.4 前提条件 (Preconditions)
*   用户已通过身份验证并成功登录。
*   `apps/jcs-endpoint-nextjs/app/lib/data.ts` 中的数据获取函数（包括 `checkOplogExportPermission`）已正确实现并能连接数据库。
*   相关的 `LOV` 表数据（特别是 `SERVER_TYPE`, `TASK_TYPE`, `OPERATION_LOG_CONFIG.ALLOWED_PLANS` 及特定任务所需的业务参数如 `OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN`）已在数据库中正确配置。参考[`LOV值列表定义`](../../definitions/lov-definitions.md)。
*   相关的**环境变量**（例如，数据库连接字符串、Service Bus连接字符串等，详见[`环境变量指南`](../../guides/environment-variables.md)）已正确设置。
*   用户提示信息的消息键已在 `apps/jcs-endpoint-nextjs/app/lib/definitions.ts` 中定义，其对应的日文文本在[`错误消息定义`](../../definitions/error-messages.md)中维护。
*   用户登录时，其 `License.basicPlan` 信息能被正确获取并传递给 `page.tsx`。

### 2.5 制约事项 (Constraints/Limitations)
*   浏览器兼容性: Microsoft Edge, Google Chrome (现代版本)。
*   语言: 界面仅支持日语。
*   列表数据量较大时，由于当前筛选、排序、分页主要在应用服务器内存中进行，性能可能会下降。当前假设单个许可证下的服务器数量在可接受范围内。

### 2.6 注意事项 (Notes/Considerations)
*   `RefreshToken` 组件的行为是每次服务器列表页面因 `searchParams` 变化而重新渲染时，都会尝试刷新用户会话令牌。
*   服务器列表的刷新主要依赖 Next.js App Router 的 `<Suspense>` 组件的 `key` 属性绑定到 `searchParams` 的组合值，以触发Server Component的重新获取和渲染。
*   **两阶段确认流程**: 所有任务的发起都包含一个最终的通用确认步骤。如果任务需要参数，则在参数输入之后进行最终确认。如果用户在最终确认阶段取消，应能返回参数输入阶段并保留已填参数。
*   **权限控制的执行点**: 操作日志导出权限的判断主要在服务器端 `page.tsx` 通过调用 `checkOplogExportPermission` 完成，并将结果传递给客户端UI组件用于渲染。`createTaskAction` Server Action 在后端也应再次执行此权限校验，作为安全防线。

### 2.7 错误处理概述 (Error Handling Overview)
*   **数据加载失败**: 若 `app/lib/data.ts` 中的数据获取函数（包括 `checkOplogExportPermission`）在服务器端执行时抛出异常，Next.js 的错误处理机制（例如，通过 `app/dashboard/error.tsx` 或根级别的 `global-error.tsx`）将捕获错误并向用户显示通用的错误页面或信息。其Message Key示例为 `EMEC0006` (数据库连接临时失败) 或其他适用ID。
*   **任务参数模态框内校验失败**: 各特定任务参数模态框（如 `OperationLogExportModal`）负责其内部的客户端参数校验，并在模态框内直接显示相应的错误提示 (例如 `EMEC0016`, `EMEC0017`, `EMEC0020`, `EMEC0024`)。
*   **任务发起失败 (createTaskAction 返回失败)**: 当调用 [`createTaskAction` Server Action](../actions/create-task-action.md) 失败时，`createTaskAction` 会返回不包含 `errorField` 的 `CreateTaskActionResult` 对象。`ServerListPage` 组件负责解析此结果，并根据 `messageId` (其定义在 `apps/jcs-endpoint-nextjs/app/lib/definitions.ts` 中，对应日文文本在 [`错误消息定义`](../../definitions/error-messages.md)) 通过通知服务向用户显示相应的日文错误提示信息。如果任务因权限不足（例如，`createTaskAction` 内部再次校验操作日志导出权限失败）而被拒绝，应返回明确的权限相关错误消息ID。
*   客户端交互错误应有适当的客户端日志记录，并可能由Next.js的错误边界处理。

### 2.8 相关功能参考 (Related Functional References)
*   [系统架构文档](../../architecture/system-architecture.md)
*   Server Action: [`createTaskAction` 组件设计文档](../actions/create-task-action.md)
*   具体任务的组件设计文档:
    *   [操作日志导出功能组件设计](./tasks/export-operation-log.md)
    *   [管理项目定义导入功能组件设计](./tasks/import-management-definition.md)
    *   [管理项目定义导出功能组件设计](./tasks/export-management-definition.md)
*   [任务列表组件设计](../../components/13-task-list.md)
*   数据模型:
    *   [`Server` 数据模型定义](../../data-models/server.md)
    *   [`Task` 数据模型定义](../../data-models/task.md)
    *   [`ContainerConcurrencyStatus` 数据模型定义](../../data-models/container-concurrency-status.md)
    *   [`License` 数据模型定义](../../data-models/license.md)
*   系统级定义:
    *   [`LOV值列表定义`](../../definitions/lov-definitions.md)
    *   [`错误消息定义`](../../definitions/error-messages.md)
    *   [`项目术语表`](../../definitions/glossary.md)
*   [`环境变量指南`](../../guides/environment-variables.md)
*   常量定义: `apps/jcs-endpoint-nextjs/app/lib/definitions.ts` (主要用于消息键)

---

## 3. 技术设计与实现细节 (Technical Design & Implementation)

### 3.1 技术栈与依赖 (Technology Stack & Dependencies)
*   **前端框架**: Next.js (v13.5.6 or later, 使用 App Router)。
*   **UI库/CSS**: Tailwind CSS (v3.x), Headless UI (可能用于模态框等)。
*   **前端状态管理**:
    *   URL查询参数 (`searchParams`): 作为服务器列表筛选、分页、排序状态的主要数据源和驱动力。
    *   React Hooks (`useState`, `useReducer`): 用于 `ServerListPage` (`page.tsx`) 内部的局部UI状态管理，核心是 `activeTaskContext` (或类似名称的状态，用于管理当前激活的任务类型、目标服务器、参数模态框的特定props、以及通用确认模态框的状态和参数)。
*   **数据请求与交互**:
    *   React Server Components (如 `page.tsx`, `ui/servers/table.tsx`): 在服务器端直接调用 `app/lib/data.ts` 中的数据获取函数。
    *   Server Actions (定义在 `app/lib/actions.ts`, 核心为 `createTaskAction`): 用于处理用户发起的后台任务创建请求。
*   **ORM**: Prisma (v4.16.2 or later) - 通过 `app/lib/data.ts` 间接使用，定义在 `app/lib/prisma.ts`。
*   **核心内部模块与工具 (位于 `apps/jcs-endpoint-nextjs/app/` 目录下)**:
    *   `lib/data.ts`: 包含 `fetchCachedServers`, `fetchFilteredServers`, `fetchServersPages` 等数据获取和缓存逻辑，以及访问 `LOV` 表的函数，`checkOplogExportPermission` 函数。
    *   `lib/actions.ts`: 包含 `createTaskAction` 等Server Actions。
    *   `lib/definitions.ts`: TypeScript核心类型定义 (如 `ServerDefinition`, `CreateTaskActionResult`) 以及项目中使用的常量 (包括消息键 `MessageKeys`，其对应日文文本在[`错误消息定义`](../../definitions/error-messages.md))。
    *   `lib/utils.ts`: 通用工具函数。
    *   `ui/`: 包含所有UI组件。

### 3.2 详细界面元素定义 (Detailed UI Element Definitions)

本节描述服务器列表页面 (`/dashboard/servers`) 及其相关模态框的核心UI元素。这些元素主要由 `page.tsx` 及其引用的 `ui/` 子组件构成。

#### 3.2.1 页头区域 (`page.tsx` 中布局)

| # | 控件内部逻辑名(中文) | 界面显示文本(日文) | 控件类型 (Type) | 建议英文ID/React key (ID) | 数据来源/状态 (Data Source/State) | 主要行为/事件 (Behavior/Event) | 校验规则/显示逻辑 (Validation/Display Logic) |
|---|-------------|------------|-------------|-----------------------|-----------------------------|--------------------------|--------------------------------------|
| 1 | 搜索组件 | (Placeholder: "サーバ名、種別、管理画面URLで検索") | `Search` (自定义组件 `ui/search.tsx`) | `serverListSearch` | 输入值由 `Search` 组件内部管理。 | 用户输入并提交搜索后 (例如回车或点击搜索图标)，组件通过 `next/navigation#useRouter` 更新URL的 `filter` 查询参数，并将 `page` 参数重置为 `1`。 | - |
| 2 | 分页与条数控制区 | - | `div` | `serverListPaginationControls` | `totalPages` (来自 `page.tsx` 计算结果，即 `ServerData.fetchServersPages` 的返回值) | - | 当 `totalPages > 0` 时显示。 |
| 2.1 | 分页组件 | (页码按钮, 上一页/下一页) | `Pagination` (自定义组件 `ui/pagination.tsx`) | `serverListPagination` | `totalPages` (作为prop从 `page.tsx` 传入) | 用户点击页码或导航按钮时，组件通过 `useRouter` 更新URL的 `page` 查询参数。 | - |
| 2.2 | 每页行数选择器 | (下拉列表，选项如 10, 20, 50) | `PageSize` (自定义组件 `ui/page-size.tsx`) | `serverListPageSize` | 选中值由 `PageSize` 组件内部管理，并反映到URL的 `size` 参数。其可选值列表和默认值在组件内部定义或通过props明确传入。 | 用户选择新的每页条目数后，组件通过 `useRouter` 更新URL的 `size` 查询参数，并将 `page` 参数重置为 `1`。 | 默认值为10。可选值列表通常为 `[10, 20, 30, 50, 100]`。 |

#### 3.2.2 主体列表区域 (`ui/servers/table.tsx`)

| # | 控件内部逻辑名(中文) | 界面显示文本(日文) | 控件类型 (Type) | 建议英文ID/React key (ID) | 数据来源/状态 (Data Source/State) | 主要行为/事件 (Behavior/Event) | 校验规则/显示逻辑 (Validation/Display Logic) (中文描述) |
|---|-------------|------------|-------------|-----------------------|-----------------------------|--------------------------|---------------------------------------------|
| 1 | 服务器表格 | - | `table` (HTML `<table>` 元素) | `serversTable` | `servers: ServerWithTypeLabel[]` (作为prop从 `page.tsx` 传入，其数据源为 `ServerData.fetchFilteredServers` 的返回值)<br/>`canExportOplog: boolean` (作为prop从 `page.tsx` 传入，表示当前用户是否有权执行操作日志导出) | - | 当 `servers` 数组为空且 `filter` 为空时，应显示“登録されているサーバがありません。”的日文提示。若 `filter` 非空且结果为空，则应显示“検索条件に一致するサーバが見つかりませんでした。”的日文提示。这些提示文本的Message Key应在[`错误消息定义`](../../definitions/error-messages.md)中定义 (例如 `EMEC_NO_SERVERS_REGISTERED`, `EMEC_NO_SERVERS_MATCH_FILTER`)。 |
| 1.1 | 表头 | "サーバ名"<br/>"種別"<br/>"管理画面" | `Thead` (自定义组件 `ui/thead.tsx`) | `serversTableThead` | `headers` prop: `[{ key: "name", label: "サーバ名" }, { key: "type", label: "種別" }, { key: "url", label: "管理画面" }]` (从 `table.tsx` 传入)。<br/>`defaultSort`, `defaultOrder` props (从 `page.tsx` 经 `table.tsx` 传入)。 | 用户点击可排序的表头列名时，`Thead` 组件通过 `useRouter` 更新URL的 `sort` (列key) 和 `order` (`asc`/`desc`) 查询参数。若点击当前已排序的列，则切换 `order`。 | 所有列 ("サーバ名", "種別", "管理画面") 均可排序。初始排序由 `page.tsx` 的 `sort` 和 `order` 状态决定。 |
| 1.2 | 表格行 (每行) | - | `tr` (HTML `<tr>` 元素) | `serverRow-${server.id}` | `server: ServerWithTypeLabel` (遍历 `servers` 数组中的每个对象) | - | - |
| 1.2.1 | 服务器名单元格 | `{server.name}` | `th` (HTML `<th>` 元素, `scope="row"`) | `serverNameCell-${server.id}` | `server.name` (来自 `ServerWithTypeLabel` 对象) | - | 直接显示。 |
| 1.2.2 | 类型单元格 | `{server.type}` (已转换为日文标签) | `td` (HTML `<td>` 元素) | `serverTypeCell-${server.id}` | `server.type` (来自 `ServerWithTypeLabel` 对象，已经是日文标签) | - | 直接显示。 |
| 1.2.3 | 管理画面链接单元格 | `{server.url}` | `td` (HTML `<td>` 元素，内含 `<a>` 标签) | `serverUrlCell-${server.id}` | `server.url` (来自 `ServerWithTypeLabel` 对象) | 用户点击链接时，浏览器在新标签页 (`target="_blank"`) 打开 `server.url`。 | 显示为可点击的超链接。链接文本为 `server.url`。 |
| 1.2.4 | 任务操作单元格 | (包含任务操作下拉菜单) | `td` (HTML `<td>` 元素) | `serverActionsCell-${server.id}` | `server: ServerWithTypeLabel` (用于判断可执行的任务类型)<br/>`canExportOplog: boolean` (从 `Table` 组件的props获取) | 用户点击下拉菜单按钮并选择具体任务项时，触发 `page.tsx` 中定义的 `handleOpenTaskInitiation(server, taskType)` 回调函数 (此函数将协调参数模态框和确认模态框的流程)。 | 仅当 `server.type` 为JP1/ITDM2相关类型 (其LOV Code为 `'SERVER_TYPE.GENERAL_MANAGER'` 或 `'SERVER_TYPE.RELAY_MANAGER'`, 定义于 [`LOV值列表定义`](../../definitions/lov-definitions.md)) 时，显示任务操作下拉菜单。此判断逻辑在 `table.tsx` 中实现。 |
| 1.2.4.1 | 任务操作下拉菜单 | "タスク" (按钮显示文本)<br/>菜单项：<br/>- "操作ログのエクスポート"<br/>- "管理項目定義のインポート"<br/>- "管理項目定義のエクスポート" | (自定义React组件，例如使用Headless UI Menu 构建，或一个简单的按钮触发展示自定义菜单) | taskActionDropdown-${server.id} | `server.type` (服务器类型代码，用于动态生成菜单项)。<br/>`canExportOplog: boolean` (从props获取)。<br/>各菜单项的文本来自数据库 Lov 表中 parentCode='TASK_TYPE' 的各任务的 name 字段 (日文)。菜单项关联的 taskType 值为其 code 字段。 | 当用户点击下拉菜单按钮时，显示包含可用任务的列表。用户选择某个菜单项后，该组件将调用 page.tsx 中传递过来的 onTaskSelect(taskType: string) 回调函数 (该回调内部会调用 handleOpenTaskDialog)，并将所选任务的 taskType (Lov Code) 作为参数传递。 | 仅当 server.type (服务器类型代码) 为JP1/ITDM2相关类型时，显示任务操作下拉菜单。具体菜单项的可见性如下：<br/>- **"操作ログのエクスポート"**: `server.type` が `SERVER_TYPE.GENERAL_MANAGER` または `SERVER_TYPE.RELAY_MANAGER` の場合、**且つ** `props.canExportOplog` 为 `true` の場合にのみ表示。<br/>- **"管理項目定義のインポート"**: `server.type` が `SERVER_TYPE.GENERAL_MANAGER` または `SERVER_TYPE.RELAY_MANAGER` の場合に表示。<br/>- **"管理項目定義のエクスポート"**: `server.type` が `SERVER_TYPE.GENERAL_MANAGER` または `SERVER_TYPE.RELAY_MANAGER` の場合に表示。<br/>(サーバ種別コードはデータベース Lov テーブル定義参照)。この表示制御ロジックは `table.tsx` 内のタスク操作メニューコンポーネントで実装される。 |

#### 3.2.3 任务参数与确认对话框 (由 `page.tsx` 协调管理)

`ServerListPage` (`page.tsx`) 组件负责根据用户从任务操作下拉菜单中选择的任务类型，协调以下模态框的显示和交互：

1.  **特定任务参数输入模态框** (若任务需要参数):
    *   **操作日志导出**:
        *   组件: `apps/jcs-endpoint-nextjs/app/ui/servers/modals/operation-log-export-modal.tsx`
        *   触发: 当用户选择 `'TASK_TYPE.OPLOG_EXPORT'` 时，`ServerListPage` 激活此模态框。
        *   传递的Props: 包括 `isOpen`, `serverName`, `maxExportDaysSpan` (从 `LOV` 获取，若获取失败则使用默认值如30天), `onSubmit` (指向 `ServerListPage` 的 `handleTaskParamsSubmitted` 回调), `onClose` (指向 `ServerListPage` 的 `handleCloseTaskModal`)。
        *   详细定义: 参考[操作日志导出功能组件设计](./tasks/export-operation-log.md#321-操作日志导出参数对话框)。
    *   **管理项目定义导入**:
        *   组件: `apps/jcs-endpoint-nextjs/app/ui/servers/modals/management-definition-import-modal.tsx`
        *   触发: 当用户选择 `'TASK_TYPE.MGMT_ITEM_IMPORT'` 时，`ServerListPage` 激活此模态框。
        *   传递的Props: 包括 `isOpen`, `serverName`, `allowedFileExtensions` (如 `['.csv']`), `onSubmit` (指向 `ServerListPage` 的 `handleTaskParamsSubmitted` 回调), `onClose` (指向 `ServerListPage` 的 `handleCloseTaskModal`)。
        *   详细定义: 参考[管理项目定义导入功能组件设计](./tasks/import-management-definition.md#321-管理项目定义导入参数对话框)。

2.  **通用二次确认模态框**:
    *   组件: `apps/jcs-endpoint-nextjs/app/ui/message-modal.tsx`
    *   触发时机:
        *   对于**不需要参数**的任务 (如 `'TASK_TYPE.MGMT_ITEM_EXPORT'`)：当用户从任务菜单选择后，`ServerListPage` 直接激活此通用确认模态框。
        *   对于**需要参数**的任务：当对应的特定任务参数模态框成功提交参数并回调给 `ServerListPage` 后，`ServerListPage` 激活此通用确认模态框。
    *   传递的Props (由 `ServerListPage` 根据当前任务上下文动态配置):
        *   `isOpen`: 控制显示。
        *   `title`: 确认对话框的标题 (日文)。例如：
            *   操作日志导出: "操作ログのエクスポート確認" (或直接使用任务类型名："操作ログのエクスポート")
            *   管理项目定义导入: "管理項目定義のインポート確認" (或直接使用任务类型名："管理項目定義のインポート")
            *   管理项目定义导出: "管理項目定義のエクスポート確認" (或直接使用任务类型名："管理項目定義のエクスポート")
        *   `message`: 确认信息文本 (日文)。
            *   操作日志导出任务时: 使用消息ID `EMEC0026` 对应的日文模板 “{0}の操作ログを{1}から{2}の期間でエクスポートします。\nよろしいですか？”。其中 `{0}` 由目标服务器名称 (`server.name`) 替换，`{1}` 由[操作日志导出功能组件设计](./tasks/export-operation-log.md)的参数模态框中用户选择的开始日期替换，`{2}` 由结束日期替换。
            *   管理项目定义导入任务时: “{サーバ名}の管理項目定義をインポートします。よろしいですか？”。其中 `{サーバ名}` 由目标服务器名称 (`server.name`) 替换。 (参考[管理项目定义导入功能组件设计](./tasks/import-management-definition.md)的1.3节)。
            *   管理项目定义导出任务时: “{サーバ名}の管理項目定義をエクスポートします。よろしいですか？”。其中 `{サーバ名}` 由目标服务器名称 (`server.name`) 替换。 (参考[管理项目定义导出功能组件设计](./tasks/export-management-definition.md)的1.3节)。
        *   `confirmText`: 确认按钮文本 (日文，例如 "エクスポート", "インポート", "OK")。
        *   `onConfirm`: 指向 `ServerListPage` 中一个**真正调用 `createTaskAction` Server Action** 的函数 (例如 `handleActualTaskSubmit`)，该函数会收集所有必需参数 (任务类型、服务器ID、以及从参数模态框或任务上下文获取的特定参数) 并构造 `FormData`。
        *   `onCancel`: 关闭此通用确认模态框。**如果此前打开的是参数输入模态框，则此取消操作应使界面返回到该参数输入模态框，并保留已输入/选择的参数。**
        *   `isProcessing`: 指示 `createTaskAction` 调用是否正在进行。
        *   `type`: 通常为 `'confirmation'` 或 `'info'`。

`ServerListPage` 通过其内部状态 (例如，一个统一的 `activeTaskContext` 对象，或分离的 `activeParamsModalConfig` 和 `activeConfirmModalConfig` 状态) 来管理当前应该显示哪个模态框，以及向这些模态框传递正确的 `props`。

### 3.3 详细事件处理逻辑 (Detailed Event Handling)

#### 3.3.1 初始化显示与数据加载
*   **触发条件**: 用户通过浏览器导航到 `/dashboard/servers` 页面。
*   **处理流程**:
    1.  Next.js App Router 渲染 `apps/jcs-endpoint-nextjs/app/dashboard/servers/page.tsx` (Server Component)。
    2.  `page.tsx` 在服务器端执行：
        a.  从传入的 `searchParams` 对象中解析 `filter`, `page`, `size`, `sort`, `order` 参数。若URL中无对应参数，则使用预设的默认值 (例如, `filter=""`, `page=1`, `size=10`, `sort="name"`, `order="asc"`）。这些值与 `page.tsx` 代码中的默认值一致。
        b.  调用 `await ServerData.fetchServersPages(filter, size, !searchParams?.page)` 获取总页数 (`totalPages`)，用于分页组件。`!searchParams?.page` 用于判断是否为初回加载（或清除了筛选条件），并触发缓存的更新。
        c.  调用 `await ServerData.checkOplogExportPermission(currentUserLicenseBasicPlan)` 获取当前用户是否有操作日志导出权限 (`canExportOplog`)。`currentUserLicenseBasicPlan` 从用户会话中获取。
        d.  渲染 `<Suspense>` 组件，其 `key` 属性绑定到 `filter`, `currentPage`, `size`, `sort`, `order` 的组合值，以确保在这些参数变化时 `<Suspense>` 内容能正确重新加载。
        e.  `<Suspense>` 的 `fallback` prop 设置为 `<TableSkeleton />`，用于在数据加载期间显示**骨架加载效果**。
        f.  `<Suspense>` 的子组件是 `<Table filter={filter} page={currentPage} size={size} sort={sort} order={order} canExportOplog={canExportOplog} />`。
    3.  `Table` 组件 (`app/ui/servers/table.tsx`) (Server Component) 在服务器端执行：
        a.  根据从 `page.tsx` 传入的 `filter`, `size`, `page`, `sort`, `order` props，调用 `await ServerData.fetchFilteredServers(...)` 获取当前页应显示的服务器列表数据。
        b.  渲染包含表头 (`Thead`) 和表格行的HTML。任务操作下拉菜单的“操作日志导出”项根据传入的 `canExportOplog` prop 控制显隐。
    4.  服务器将最终渲染好的HTML（或React Server Component (RSC) 载荷）发送给客户端浏览器。
*   **界面显示更新**: 客户端浏览器显示包含初始服务器列表、搜索框、分页控件（如果总页数大于0）等的完整页面。

#### 3.3.2 筛选框操作 (`ui/search.tsx` 组件逻辑)
*   **触发条件**: 用户在位于页面头部的搜索框 (`Search` 组件) 中输入文本后，执行搜索操作（例如，点击搜索图标或按回车键）。
*   **处理流程 (客户端)**:
    1.  `Search` 组件获取输入框中的当前文本值作为 `searchTerm`。
    2.  使用 `next/navigation` 的 `useRouter()` hook 获取的 `router` 对象，调用 `router.push()` 方法来更新浏览器的URL。
    3.  新的URL将包含更新后的 `filter` 查询参数 (值为 `encodeURIComponent(searchTerm)`)，并将 `page` 查询参数重置为 `1`，同时保留其他现有的查询参数（如 `size`, `sort`, `order`）。
        *   示例URL: `/dashboard/servers?filter=mykeyword&page=1&size=10&sort=name&order=asc`
*   **后续**: URL的改变会触发 `page.tsx` Server Component 的重新执行（返回步骤 3.3.1.2），从而加载和显示符合新筛选条件的数据。

#### 3.3.3 表头点击排序 (`ui/thead.tsx` 组件逻辑)
*   **触发条件**: 用户点击位于服务器表格 (`Table` 组件) 表头 (`Thead` 组件) 中的可排序列表头（例如“サーバ名”、“種別”或“管理画面”）。
*   **处理流程 (客户端)**:
    1.  `Thead` 组件获取被点击列的 `key` (例如 `"name"`)。
    2.  根据当前URL `searchParams` 中的 `sort` 和 `order` 值，判断新的排序状态：
        *   如果点击的是当前已排序的列（即 `key` 与当前 `sort` 参数相同），则切换 `order` 参数的方向 ( `asc` 变为 `desc`, `desc` 变为 `asc`)。
        *   如果点击的是新的未排序的列，则将 `sort` 参数设为新列的 `key`，并将 `order` 参数设为默认的升序 `'asc'`。
    3.  使用 `router.push()` 更新URL，包含新的 `sort` 和 `order` 查询参数，同时保留其他现有查询参数（如 `filter`, `page`, `size`）。
*   **后续**: URL的改变会触发 `page.tsx` Server Component 的重新执行，加载和显示按新条件排序的数据。

#### 3.3.4 分页操作 (`ui/pagination.tsx` 组件逻辑)
*   **触发条件**: 用户在分页控件 (`Pagination` 组件) 中点击页码按钮、上一页或下一页按钮。
*   **处理流程 (客户端)**:
    1.  `Pagination` 组件根据用户的点击计算出目标页码 `targetPage`。
    2.  使用 `router.push()` 更新URL，将 `page` 查询参数设为 `targetPage`，同时保留其他所有现有查询参数。
*   **后续**: URL的改变会触发 `page.tsx` Server Component 的重新执行，加载和显示目标页的数据。

#### 3.3.5 每页行数选择变化 (`ui/page-size.tsx` 组件逻辑)
*   **触发条件**: 用户在每页行数选择器 (`PageSize` 组件) 的下拉列表中选择一个新的行数值 (例如从10改为20)。
*   **处理流程 (客户端)**:
    1.  `PageSize` 组件获取用户选择的新的每页行数值 `newPageSize`。
    2.  使用 `router.push()` 更新URL，将 `size` 查询参数设为 `newPageSize`，并将 `page` 查询参数重置为 `1`（因为更改每页行数通常意味着从第一页开始重新分页），同时保留其他现有查询参数。
*   **后续**: URL的改变会触发 `page.tsx` Server Component 的重新执行，加载和显示按新行数分页的数据。

#### 3.3.6 管理画面超链接点击 (在 `ui/servers/table.tsx` 中渲染的 `<a>` 标签)
*   **触发条件**: 用户点击服务器表格某一行中的“管理画面”列的超链接。
*   **处理流程**: 此为标准的浏览器行为。浏览器将在新的标签页 (`target="_blank"`) 中打开该链接的 `href` 属性所指向的URL (即对应服务器记录的 `Server.url` 字段值)。此操作不涉及Next.js的路由或Server Component的重新渲染。

#### 3.3.7 任务操作下拉菜单项选择及后续处理 (在 `page.tsx` 中定义回调)

此部分详细描述了当用户从服务器列表的任务操作下拉菜单中选择一项具体任务后，前端（主要是 `page.tsx` 客户端逻辑部分）所执行的协调处理流程，包括参数输入（若需要）和最终确认。

`page.tsx` 中应定义以下核心客户端函数来管理此流程 (函数名和具体实现可能调整，此处为逻辑示意)：

##### 1. `handleOpenTaskInitiation(server: ServerDefinition, taskType: string)` - 任务发起入口

*   **触发位置**: 当用户在 `ui/servers/table.tsx` 的任务操作下拉菜单中选择一个任务项时，该表格组件会调用此函数，并传入选定的 `server` 对象和任务类型 `taskType` (LOV Code)。
*   **核心处理流程 (`page.tsx` 内部)**:
    1.  **根据 `taskType` 判断是否需要参数输入模态框**:
        *   **若任务需要参数** (例如 `'TASK_TYPE.OPLOG_EXPORT'`, `'TASK_TYPE.MGMT_ITEM_IMPORT'`):
            a.  准备该特定任务参数模态框所需的 `props`。这可能包括：
                *   `isOpen: true`
                *   `serverName: server.name`
                *   对于操作日志导出，还需从 `LOV` 获取并传入 `maxExportDaysSpan` (若LOV获取失败，则使用系统默认值，例如30天)。
                *   一个关键的 `onSubmit: (taskSpecificParams) => handleTaskParamsSubmitted(server, taskType, taskSpecificParams)` 回调函数。
                *   一个 `onClose: () => handleCloseTaskModal('params')` 回调函数。
            b.  更新 `page.tsx` 的状态 (例如 `activeTaskContext.paramsModalConfig`) 以显示并配置相应的特定任务参数模态框 (如 `OperationLogExportModal` 或 `ManagementDefinitionImportModal`)。
        *   **若任务不需要参数** (例如 `'TASK_TYPE.MGMT_ITEM_EXPORT'`):
            a.  直接调用 `handleOpenConfirmModal(server, taskType, null)` (或类似函数)，跳过参数输入，直接进入通用二次确认流程。
*   **界面显示更新**: 根据逻辑，相应的参数模态框或通用确认模态框将被显示。

##### 2. `handleTaskParamsSubmitted(server: ServerDefinition, taskType: string, taskSpecificParams: any)` - 参数模态框提交后的处理

*   **触发位置**: 当某个特定任务参数模态框（如 `OperationLogExportModal`）内部的表单校验通过，并且用户点击了其主提交按钮时，该模态框会调用其通过 `props.onSubmit` 接收到的此函数，并传入收集到的 `taskSpecificParams`。
*   **核心处理流程 (`page.tsx` 内部)**:
    1.  首先，调用 `handleCloseTaskModal('params')` 关闭当前的参数输入模态框。
    2.  然后，调用 `handleOpenConfirmModal(server, taskType, taskSpecificParams)` 打开通用的二次确认模态框，并将从参数模态框收集到的 `taskSpecificParams` 透传过去。

##### 3. `handleOpenConfirmModal(server: ServerDefinition, taskType: string, taskSpecificParams: any | null)` - 打开通用二次确认模态框

*   **触发位置**:
    *   由 `handleOpenTaskInitiation` 直接调用（对于不需要参数的任务）。
    *   由 `handleTaskParamsSubmitted` 调用（对于需要参数的任务，在参数提交后）。
*   **核心处理流程 (`page.tsx` 内部)**:
    1.  根据 `taskType` 和 `server.name` (以及 `taskSpecificParams` 中的日期或文件名等，如果适用)，构造通用确认模态框所需的 `title` 和 `message`。具体消息文本参考本文档2.3节。
    2.  准备通用确认模态框的 `onConfirm` 回调，使其指向 `handleActualTaskSubmit(server, taskType, taskSpecificParams)`。
    3.  准备 `onCancel` 回调，使其指向 `handleCloseTaskModal('confirm', server, taskType, taskSpecificParams)` (传递参数以便在取消时能恢复参数模态框)。
    4.  更新 `page.tsx` 的状态 (例如 `activeTaskContext.confirmModalConfig`) 以显示并配置通用消息模态框 (`app/ui/message-modal.tsx`)。

##### 4. `handleActualTaskSubmit(server: ServerDefinition, taskType: string, taskSpecificParams: any | null)` - 最终调用 Server Action

*   **触发位置**: 当用户在通用二次确认模态框中点击了确认按钮时，该模态框的 `onConfirm` prop (指向此函数) 被调用。
*   **核心处理流程 (`page.tsx` 内部)**:
    1.  设置提交状态 `activeTaskContext.isSubmittingTask = true`。
    2.  构造 `FormData` 对象：
        a.  添加通用参数: `formData.append('taskType', taskType);` 和 `formData.append('serverId', server.id);`
        b.  如果 `taskSpecificParams` 存在，则根据 `taskType` 将其内容添加到 `formData` (例如，`exportStartDate`, `importFile`, `originalFileName` 等)。
    3.  异步调用 `await createTaskAction(undefined, formData)`。
    4.  处理 `createTaskAction` 返回的 `CreateTaskActionResult`：
        *   **若成功 (`result.success === true`)**: 调用 `handleCloseTaskModal('all')` 关闭所有相关模态框。使用通知服务显示成功消息 (基于 `result.messageId`，例如 `EMEC0025`)。
        *   **若失败 (`result.success === false`)**: 调用 `handleCloseTaskModal('all')` 关闭所有相关模态框。使用通知服务显示错误消息 (基于 `result.messageId`)。
    5.  最终设置 `activeTaskContext.isSubmittingTask = false`。

##### 5. `handleCloseTaskModal(modalType: 'params' | 'confirm' | 'all', server?: ServerDefinition, taskType?: string, taskSpecificParams?: any)` - 关闭模态框

*   **触发位置**: 由各模态框的取消/关闭按钮回调，或在任务提交成功/失败后按需调用。
*   **核心处理流程 (`page.tsx` 内部)**:
    *   如果 `modalType` 是 `'confirm'` 且用户是从最终确认模态框点击“取消”，并且 `taskType` 指示这是一个需要参数的任务 (例如，`taskType` 为 `'TASK_TYPE.OPLOG_EXPORT'` 或 `'TASK_TYPE.MGMT_ITEM_IMPORT'`)，则：
        1.  关闭通用确认模态框 (通过更新 `activeTaskContext.confirmModalConfig.isOpen = false`)。
        2.  重新打开对应的特定任务参数模态框，并**使用之前暂存的 `taskSpecificParams` (如果存在) 来恢复用户已输入/选择的参数**。
    *   否则 (例如，从参数模态框直接取消，或最终确认后提交，或从不需要参数任务的确认模态框取消)：
        根据 `modalType` 更新 `page.tsx` 的状态 (例如 `activeTaskContext`)，将对应模态框的 `isOpen` 设为 `false`，并清除相关上下文。如果 `modalType` 是 `'all'`，则关闭所有当前打开的任务相关模态框。

#### 3.3.8 `RefreshToken` 组件的副作用逻辑 (`ui/refreshToken.tsx`)
*   **触发条件**: `RefreshToken` 组件在 `page.tsx` 中被渲染，并且其 `key` 属性（一个随机数）在 `page.tsx` 因 `searchParams` 变化而重新渲染时会改变，导致 `RefreshToken` 组件重新挂载并执行其 `useEffect` 钩子。
*   **处理流程 (客户端 `refreshToken.tsx` 内部)**:
    1.  `useEffect` 内部的异步函数 `fetchSession` 被调用。
    2.  向 `/api/refreshToken` 发起 `POST` HTTP请求，尝试刷新用户会话令牌。
    3.  根据响应状态码和内容进行处理：
        *   若响应状态为 `401` (未授权)，表示会话已无效或刷新失败，则调用 `router.push("/login")` 将用户重定向到登录页面。
        *   若响应状态非 `401`，但刷新操作本身失败（例如，响应 `ok` 为 `false` 或响应体中的状态指示失败），则向 `/api/logout` 发起POST请求以清除客户端会话，然后调用 `router.push("/login?messageId=SESSION_REFRESH_FAILED")` (messageId应为[`错误消息定义`](../../definitions/error-messages.md)中定义的 `EMECXXXX_SESSION_REFRESH_FAILED` 或类似ID) 重定向到登录页并提示错误。
        *   若刷新成功，则不执行任何导航操作，用户会话得到延长。
    4.  使用 `try...catch` 块捕获 `fetchSession` 过程中的任何未预料的异常，并在发生异常时也尝试登出并重定向到登录页。
*   **界面显示更新**: `RefreshToken` 组件本身不渲染任何可见UI (`return null`)。其副作用是可能发生的页面重定向（到登录页）。

### 3.4 数据结构与API/Server Action交互 (Data Structures and API/Server Action Interaction)

本章节描述了服务器列表页面组件 (`ServerListPage`) 主要管理的数据结构，以及其与后台任务创建核心逻辑 Server Action (`createTaskAction`) 的交互方式。

#### 3.4.1 前端核心状态管理 (Frontend Core State Management - 在 `ServerListPage` (`page.tsx`) 中)

服务器列表页面的用户界面和交互行为依赖以下关键状态：

*   **URL查询参数 (`searchParams`)**: 驱动列表的筛选、分页和排序。
    *   `filter?: string`
    *   `page?: string`
    *   `size?: string`
    *   `sort?: "name" | "type" | "url"`
    *   `order?: "asc" | "desc"`

*   **`ServerListPage` 组件局部状态 (示例结构，具体实现可能使用 `useState` 或 `useReducer`)**:
    *   `activeTaskContext: {`
        *   `paramsModal: { isOpen: boolean; type: string | null; server: ServerDefinition | null; props?: any; currentParams?: any; }` // 管理特定任务参数模态框，`currentParams` 用于暂存用户输入
        *   `confirmModal: { isOpen: boolean; taskType: string | null; server: ServerDefinition | null; params?: any; title: string; message: React.ReactNode; onConfirm: () => void; }` // 管理通用确认模态框
        *   `isSubmittingTask: boolean;` // 标记当前是否有任务正在提交
    *   `}`
    *(此状态结构用于统一管理当前激活的任务类型、目标服务器、传递给各模态框的特定属性、以及任务提交状态。)*
*   `canExportOplog: boolean` (由服务器端 `page.tsx` 通过调用 `checkOplogExportPermission` 获取，并作为prop传递给客户端的 `Table` 组件)

#### 3.4.2 数据获取流程 (Data Fetching Flow - 通过 `app/lib/data.ts`)

服务器列表的展示数据通过 `apps/jcs-endpoint-nextjs/app/lib/data.ts` 中的数据获取函数进行加载，主要包括：
*   `fetchCachedServers()`: 获取并缓存当前许可证下的所有服务器基础信息（含类型标签转换）。
*   `fetchServersPages()`: 基于筛选条件计算总页数。
*   `fetchFilteredServers()`: 获取当前页应显示的、经过筛选和排序的服务器数据。
*   `checkOplogExportPermission(userLicenseBasicPlan: string | undefined | null): Promise<boolean>` (位于 `app/lib/data.ts` 模块):
    *   输入：当前用户的 `License.basicPlan` 代码。
    *   职责：根据用户当前的许可证计划代码 (`userLicenseBasicPlan`) 和 `LOV` 表中 `parentCode='OPERATION_LOG_CONFIG.ALLOWED_PLANS'` 的配置（具体为检查 `LOV.value` 字段是否与 `userLicenseBasicPlan` 匹配且 `isEnabled` 为 `true`），判断用户是否有权执行操作日志导出。
    *   数据来源/前置条件：调用此函数前，`userLicenseBasicPlan` 必须已从用户会话中获取。`page.tsx` (服务器端组件) 负责获取此计划代码并调用此函数，以获取权限判断结果，并将此结果作为 prop (`canExportOplog`) 传递给客户端渲染的 `Table` 组件。
    *   返回：布尔值，指示用户是否有权限。若查询数据库失败或发生其他错误，应安全地返回 `false` (无权限)。

上述函数的具体实现和详细的数据处理逻辑，请参考 `app/lib/data.ts` 内的相应定义。

#### 3.4.3 与Server Action: `createTaskAction` 的交互 (Interaction with `createTaskAction` Server Action)

所有从服务器列表发起的后台任务创建请求，均通过调用统一的 Server Action [`createTaskAction` 组件设计文档](../../components/actions/create-task-action.md) 来处理。该 Server Action 的通用接口定义、内部核心处理流程框架、以及特定任务类型逻辑的扩展点，详见其独立的组件设计文档。

`ServerListPage` 与 `createTaskAction` 的交互遵循以下模式：

1.  **参数构造与调用**:
    *   用户在前端UI（经过特定任务参数模态框（若有）和通用的二次确认模态框）中最终确认发起任务后，`ServerListPage` 中的事件处理器 (例如 `handleActualTaskSubmit`) 会构造一个 `FormData` 对象。
    *   此 `FormData` 对象包含**通用参数**：`taskType` (任务类型的LOV Code) 和 `serverId` (目标服务器ID)。
    *   同时，特定任务所需的额外参数（如日期范围、上传的文件对象等，这些参数已在之前的模态框流程中收集并暂存）也会被一并添加到此 `FormData` 中。
    *   随后，`ServerListPage` 调用 `await createTaskAction(undefined, formData)`。

2.  **响应处理**:
    *   `createTaskAction` 返回一个 [`CreateTaskActionResult` 对象](../../components/actions/create-task-action.md#13-返回值-createtaskactionresult)。
    *   `ServerListPage` 根据返回结果中的 `success` 字段判断任务提交是否被后端接受：
        *   若成功 (`success: true`)，则通常会关闭所有相关模态框，并根据 `messageId` (`EMEC0025`) 向用户显示成功的提示信息。
        *   若失败 (`success: false`)，则根据 `messageId` 向用户显示相应的错误提示。

#### 3.4.4 与API Route: `/api/refreshToken` 和 `/api/logout` 的交互

这两个API Route主要由全局会话管理组件 (`app/ui/refreshToken.tsx`) 调用，用于处理用户会话令牌的刷新和登出。`ServerListPage` 间接受其影响，因为它们关系到用户在当前页面的登录状态和操作权限。其详细接口契约请参考项目[API规范文档 (`docs/apis/openapi.v1.yaml`)](../../apis/openapi.v1.yaml)（若存在）。

#### 3.4.5 服务器列表与任务创建交互概览 (Mermaid序列图 - 包含两阶段确认)

```mermaid
sequenceDiagram
    actor User as 用户
    participant ServerListPage as 服务器列表页面 (门户前端)
    participant NextJsAppServer as Next.js 应用服务器 (后端)
    participant Database as 数据库 (Task表等)
    participant MessageQueue as 消息队列 (TaskInputQueue)

    User->>ServerListPage: 1. 在服务器列表选择某服务器的任务
    ServerListPage->>User: 2. (若需参数) 显示任务参数模态框<br/>(若无需参数) 直接显示通用确认模态框
    User->>ServerListPage: 3. (若有参数模态框) 提供参数并提交<br/>或 (若直接确认) 点击确认
    ServerListPage->>User: 4. (若有参数模态框且参数有效) 显示通用确认模态框
    User->>ServerListPage: 5. 在通用确认模态框中点击“OK/はい”
    ServerListPage->>NextJsAppServer: 6. 发起后台任务创建请求 (HTTP POST)<br/>(调用 createTaskAction Server Action,<br/>携带taskType, serverId, 及任务特定参数)
    activate NextJsAppServer
    NextJsAppServer->>NextJsAppServer: 7. (在createTaskAction内) 执行通用前置校验<br/>(用户会话,权限(含操作日志导出权限再校验),服务器配置,并发检查, <br/> 若并发记录不存在则创建并初始化为IDLE) <br/>及特定任务逻辑(参数校验,文件上传等)
    alt 前置校验或特定任务逻辑失败
        NextJsAppServer-->>ServerListPage: 8a. 返回错误响应 (含messageId)
    else 前置校验和特定任务逻辑成功
        NextJsAppServer->>Database: 8b. (在createTaskAction内) 执行核心数据库事务<br/>(含任务保留策略, 创建Task记录)
        activate Database
        Database-->>NextJsAppServer: 9b. 数据库事务结果
        deactivate Database
        alt 数据库事务失败 (或并发锁获取失败)
            NextJsAppServer-->>ServerListPage: 10a. 返回DB错误响应 (含messageId)
        else 数据库事务成功
            NextJsAppServer->>MessageQueue: 10b. (在createTaskAction内) 发送任务消息 (含taskId)
            activate MessageQueue
            MessageQueue-->>NextJsAppServer: 11b. 消息发送结果
            deactivate MessageQueue
            alt 消息发送失败
                NextJsAppServer-->>ServerListPage: 12a. 返回队列错误响应 (含messageId)
            else 消息发送成功
                NextJsAppServer->>NextJsAppServer: 12b. (在createTaskAction内) 调用 revalidatePath
                NextJsAppServer-->>ServerListPage: 13b. 返回成功响应 (含messageId)
            end
        end
    end
    deactivate NextJsAppServer
    
    ServerListPage->>User: 14. 根据Server Action响应结果<br/>显示成功或失败提示 (基于messageId)
```

### 3.5 数据库设计与访问详情 (Database Design and Access Details - 通过 `app/lib/data.ts`)

#### 3.5.1 相关表引用
*   [`Server` 数据模型定义](../../data-models/server.md)
*   [`LOV值列表定义`](../../definitions/lov-definitions.md) (特别是 `SERVER_TYPE` 和 `OPERATION_LOG_CONFIG.ALLOWED_PLANS`)
*   [`License` 数据模型定义](../../data-models/license.md) (间接通过用户会话获取 `basicPlan`)
*   (任务操作相关的表如 `Task`, `ContainerConcurrencyStatus` 则在 [`createTaskAction` Server Action](../../components/actions/create-task-action.md) 及其调用的数据服务中涉及，本组件不直接操作这些表，而是通过 `createTaskAction` 间接影响。)

#### 3.5.2 主要数据查询逻辑 (由 `app/lib/data.ts` 实现，供 `page.tsx` 和 `table.tsx` 调用)
*   **`fetchCachedServers(licenseId: string)`**:
    *   通过 `prisma.server.findMany({ where: { licenseId: currentUserLicenseId } })` 获取指定许可证下的所有服务器记录。
    *   通过 `prisma.lov.findMany({ where: { parentCode: 'SERVER_TYPE' } })` 获取所有服务器类型的LOV定义。
    *   在应用层将 `Server.type` 的code值转换为用户可见的日文标签，并缓存结果。
*   **`fetchServersPages(filter: string, size: number, refreshCache: boolean)`**:
    *   调用 `fetchCachedServers` 获取全量数据。
    *   在应用服务器内存中根据 `filter` 条件进行筛选（匹配 `Server.name`, `Server.type` 的日文标签, `Server.url`）。
    *   根据筛选后结果计算总页数。
*   **`fetchFilteredServers(filter: string, size: number, page: number, sort: string, order: "asc" | "desc")`**:
    *   调用 `fetchCachedServers` 获取全量数据。
    *   在应用服务器内存中执行筛选、排序（使用 `localeCompare` 实现不区分大小写的字符串比较）和分页（使用 `slice`）。
*   `checkOplogExportPermission(userLicenseBasicPlan: string | undefined | null): Promise<boolean>` (位于 `app/lib/data.ts` 模块):
    *   输入：当前用户的 `License.basicPlan` 代码。
    *   职责：根据用户当前的许可证计划代码 (`userLicenseBasicPlan`) 和 `LOV` 表中 `parentCode='OPERATION_LOG_CONFIG.ALLOWED_PLANS'` 的配置（具体为检查 `LOV.value` 字段是否与 `userLicenseBasicPlan` 匹配且 `isEnabled` 为 `true`），判断用户是否有权执行操作日志导出。
    *   返回：布尔值，指示用户是否有权限。若查询数据库失败或发生其他错误，应安全地返回 `false` (无权限)。

### 3.6 关键后端逻辑/算法 (Key Backend Logic/Algorithms)

服务器列表组件 (`page.tsx`, `table.tsx`) 本身主要负责前端展现和用户交互，其核心的“后端”逻辑体现在以下两个方面：

1.  **数据获取逻辑 (在 `app/lib/data.ts` 中)**:
    *   **缓存策略**: 使用 Next.js 的 `unstable_cache` API，通过 `licenseId` 作为缓存键的一部分，实现按许可证缓存服务器列表。通过 `revalidateTag` 机制支持按需刷新缓存。
    *   **内存筛选/排序/分页**: 如3.5.2节所述，在从缓存获取（或首次从数据库加载）全量服务器数据后，在应用服务器端的JavaScript代码中进行这些操作。这对于当前“数据量不会太大”的场景是合适的。
    *   **权限判断**: `checkOplogExportPermission` 函数直接查询 `Lov` 表进行权限判断。
2.  **任务创建请求的委派 (通过调用 [`createTaskAction` Server Action](../../components/actions/create-task-action.md))**:
    *   `ServerListPage` 将用户发起的任务创建请求（包含通用参数和任务特定参数）完全委派给 [`createTaskAction` Server Action](../../components/actions/create-task-action.md) 进行处理。
    *   `createTaskAction` 内部包含了真正的后端业务逻辑，如参数的深度校验、与数据库的事务性交互（创建`Task`记录，管理`ContainerConcurrencyStatus`记录）、以及将任务消息发送到Azure Service Bus等。这部分详细逻辑在其独立的组件设计文档 [`createTaskAction` 组件设计文档](../../components/actions/create-task-action.md) 中描述。`createTaskAction` 在处理操作日志导出任务时，也需要再次执行操作日志导出权限的校验。

### 3.7 错误处理详情 (Detailed Error Handling)

| 错误场景描述 (中文) | 触发位置 (前端/ServerAction调用) | 返回给客户端的 `CreateTaskActionResult` (若适用) 或客户端处理方式 | 用户提示信息 (日文界面文本) (基于 `messageId`，其日文文本定义在[`错误消息定义`](../../definitions/error-messages.md)) | 系统内部处理及日志记录建议 (中文) |
|-------------|--------------------------|-------------------------------------------------|------------------------------------------------------------------------------------------|--------------------|
| 服务器列表数据加载失败 (例如数据库连接问题) | `page.tsx` / `table.tsx` (服务器端) | (服务器端异常) | (由 `error.tsx` 或 `global-error.tsx` 显示通用错误页面) Message Key: `EMEC0006` (数据库连接临时失败) | Next.js App Router的错误边界捕获异常。应在 `app/lib/data.ts` 中对数据库查询等操作进行`try...catch`，记录详细错误到服务器日志，并向上抛出或返回可识别的错误状态。 |
| 用户未登录或会话失效，尝试访问服务器列表页面 | `page.tsx` (服务器端会话检查) | (服务器端重定向) | (用户被重定向到登录页) | `page.tsx` 在获取数据前应检查用户会话。若无效，直接重定向到登录页。 |
| 调用 `createTaskAction` 时发生网络错误或其他客户端异常 | `page.tsx` (客户端) | (客户端捕获的 `Error` 对象) | Message Key: `EMEC0007` (サーバに一時的に接続できません。...) 或其他通用网络错误ID。 | 在 `handleActualTaskSubmit` 中调用 `await createTaskAction()` 时使用 `try...catch` 块捕获。向用户显示通用通信错误，记录错误到浏览器控制台。 |
| `createTaskAction` 返回失败结果 (例如参数错误、容器繁忙、队列错误、权限不足等) | `page.tsx` (客户端，处理Server Action响应) | `result.success === false`，包含  `message` | `result.message` 可作为消息文本 | `page.tsx` 根据 `CreateTaskActionResult` 中的错误信息向用户显示具体或通用的日文错误提示。由于返回值不再包含 `errorField`，前端不再高亮特定字段。 |
| `RefreshToken` 组件刷新会话失败 | `ui/refreshToken.tsx` (客户端) | (客户端处理) | (用户被重定向到登录页，URL可能带有错误消息ID，例如 `SESSION_REFRESH_FAILED`，其定义在[`错误消息定义`](../../definitions/error-messages.md)中) | `refreshToken.tsx` 内部逻辑处理，尝试登出并重定向。记录相关错误到浏览器控制台。 |

### 3.8 配置项 (Configuration)
*   **URL查询参数**:
    *   `filter`
    *   `page` (默认值: 1)
    *   `size` (默认值: 10，可选值参考 `ui/page-size.tsx` 实现)
    *   `sort` (默认值: `"name"`)
    *   `order` (默认值: `"asc"`)
*   **`LOV` 表 (其权威定义参见 [`LOV值列表定义`](../../definitions/lov-definitions.md))**:
    *   `SERVER_TYPE`: 用于将 `Server.type` code 映射为日文显示名称。
    *   `TASK_TYPE`: 用于标识从服务器列表发起的不同后台任务类型及其日文名称 (用于任务操作菜单和确认消息)。
    *   `OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN`: (由 `page.tsx` 获取并传递给操作日志导出模态框) 操作日志导出的最大允许天数跨度。
    *   `OPERATION_LOG_CONFIG.ALLOWED_PLANS`: (及其子条目) 定义了哪些许可证基本契约计划代码 (`License.basicPlan`) 被允许执行“操作日志导出”功能。此配置由 `app/lib/data.ts#checkOplogExportPermission` 和 `createTaskAction` 使用。
    *   相关消息ID (例如 `EMEC0026`，其日文文本在[`错误消息定义`](../../definitions/error-messages.md)中定义，并由 `page.tsx` 在构造通用确认模态框消息时，根据具体任务动态填充占位符)。
*   **常量定义 (`apps/jcs-endpoint-nextjs/app/lib/definitions.ts`)**:
    *   包含所有用户提示信息对应的 `MessageKeys` (例如 `MessageKeys.TASK_SUBMIT_SUCCESS` (`EMEC0025`), `MessageKeys.TASK_CONTAINER_BUSY` (`EMEC0022`) 等)，这些由 `createTaskAction` 的 `messageId` 字段返回，`page.tsx` 负责根据这些键名从[`错误消息定义`](../../definitions/error-messages.md)获取并显示对应的日文消息文本。
*   **环境变量 (参考 [`环境变量指南`](../../guides/environment-variables.md))**:
    *   `DATABASE_URL` (由 Prisma ORM 在 `app/lib/data.ts` 中使用)
    *   `APP_CACHE_TTL_SECONDS` (由 `app/lib/data.ts` 中的 `unstable_cache` 使用)
    *   间接依赖于 [`createTaskAction` Server Action](../../components/actions/create-task-action.md) 所需的环境变量（如Service Bus连接、Blob Storage连接等）。

### 3.9 注意事项与其他 (Notes/Miscellaneous)
*   **内存中数据处理的性能考量**: 当前设计中，服务器列表的筛选、排序和分页主要在 `app/lib/data.ts` 中对从数据库获取的全量数据（按许可证）进行内存操作。这对于服务器数量不大的情况是高效的。如果预计单个客户的服务器数量可能非常庞大（例如数千台以上），未来可能需要考虑将这些操作下推到数据库层面执行，以优化性能和内存消耗。
*   **任务操作UI的一致性**: 通过引入通用的两阶段确认流程（参数输入（若有）-> 通用确认 -> 提交），确保所有后台任务从服务器列表发起的体验在核心交互逻辑上保持一致。
*   **`RefreshToken` 组件的触发时机**: `refreshToken.tsx` 的 `key` 属性绑定到一个随机数，该随机数在 `page.tsx` 每次因 `searchParams` 变化而重新渲染时都会改变。这意味着 `RefreshToken` 组件会在每次列表视图刷新（例如筛选、排序、分页）时都重新挂载并执行其 `useEffect` 中的会话刷新逻辑。这确保了用户在进行列表操作时会话的活跃性，但如果刷新操作本身有一定开销，可能需要评估此策略的性能影响。
*   **状态管理复杂度**: `page.tsx` 需要管理参数模态框和确认模态框的状态以及它们之间的数据传递，状态管理逻辑会相对复杂。建议使用清晰的状态变量命名和状态更新函数。
