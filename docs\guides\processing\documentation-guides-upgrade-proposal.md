## 指南升级建议：整合服务器列表功能簇文档迭代经验

**目的**: 本升级建议旨在将本次“服务器列表”功能簇核心技术文档迭代过程中积累的关键经验、遇到的典型问题、以及达成的解决方案和最佳实践，系统性地反馈并整合到现有的《機能仕様書 (FS) 内容迁移至 docs/ 目录指南》和《詳細設計書 (DDS) 内容迁移至 docs/ 目录指南》中。通过对这两个核心指南的升级，期望能提升未来新功能设计文档编写的质量、效率和一致性。

**核心原则**: 确保指南的更新能够更好地指导团队（包括AI助手）创建出真正符合项目SSoT（单一事实来源）原则、职责清晰、接口明确、易于理解和维护、并能有效指导“与代码一一对应”的核心技术文档。

---

### 一、 对《機能仕様書 (FS) 内容迁移至 docs/ 目录指南》的升级建议

**当前《FS迁移指南》的核心目标**: 指导将日文FS内容准确迁移到 `docs/components/[component-name].md` 的第1部分（概要）和第2部分（功能规格）。

**基于本次迭代经验，建议在《FS迁移指南》中强化或补充以下内容：**

1.  **强调“高层抽象”在FS部分的图表中的重要性 (针对2.1 主要流程)**:
    *   **收获/问题**: 在迭代中，我们明确了FS部分的流程图（如 `server-list.md` 的 `2.1.x` 各图）应保持高度抽象，聚焦用户与系统的主要交互步骤，避免过早暴露内部组件或实现细节。
    *   **升级建议**: 在指南中增加一节，专门强调FS流程图（特别是使用Mermaid绘制时）的抽象层级要求。明确指出参与者通常仅为“用户”和“系统”（或非常高层的逻辑模块），消息应描述业务意图而非技术调用。可以提供一个“好的FS流程图”和“过于详细的FS流程图（应避免）”的对比示例。

2.  **明确FS部分与后续“技术设计与实现细节”部分的边界与衔接**:
    *   **收获/问题**: 需要清晰界定哪些信息属于FS层面，哪些应推迟到详细设计层面。
    *   **升级建议**: 在指南的引言或总结部分，再次强调FS部分（组件文档的1、2部分）的核心是描述“做什么”（What）和“为什么做”（Why），而“怎么做”（How）的细节（如具体API调用、数据库表结构、详细的UI控件属性、Server Action内部逻辑等）**必须**留到第3部分“技术设计与实现细节”中描述。可以在模板说明中更清晰地标示这一点。

3.  **对“用户界面概述 (UI Overview)”的粒度控制 (针对2.3)**:
    *   **收获/问题**: FS层面的UI概述应避免陷入具体控件的技术定义。
    *   **升级建议**: 强化指南中对 `2.3 用户界面概述` 的编写指导，明确此部分应侧重于：
        *   **高层界面布局草图或概念图**的引用（如果FS提供）。
        *   **主要交互区域和用户核心操作路径**的文字描述。
        *   **关键输入/输出信息的业务概念**，而非具体的HTML元素或前端状态变量。
        *   明确指出详细的UI元素定义（如精确到ID、数据绑定、事件处理器名称）属于详细设计范畴。

4.  **链接到相关FS级别文档的规范 (针对2.8 相关功能参考)**:
    *   **收获/问题**: 此部分应专注于功能层面的关联。
    *   **升级建议**: 明确 `2.8 相关功能参考` 中列出的应主要是其他组件的FS部分（即其组件设计文档的第1、2部分链接）或更高层级的架构文档，以帮助理解当前组件在整体**用户可见功能**网络中的位置和依赖。避免在此处过早链接到纯技术实现细节的文档（如具体的数据模型文件或API定义文件）。

---

### 二、 对《詳細設計書 (DDS) 内容迁移至 docs/ 目录指南》的升级建议

**当前《DDS迁移指南》的核心目标**: 指导将日文详细设计书（Excel）或从零开始编写的内容，准确、详尽地填充到 `docs/components/[component-name].md` 的第3部分“技术设计与实现细节”，并满足“与代码一一对应”的要求。

**基于本次迭代经验，建议在《DDS迁移指南》中强化或补充以下内容：**

1.  **彻底澄清“内部核心技术文档”与“交付用《詳細設計書》”的概念 (针对全文)**:
    *   **收获/问题**: 这是本次迭代中反复出现的核心概念混淆点。AI助手（甚至有时人类成员也可能）会将正在编写的 `docs/` 目录下的Markdown详细设计文档错误地等同于最终交付给客户的、特定格式（如Excel/Word）的《詳細設計書》(DDS)。
    *   **升级建议**:
        *   在指南的**引言和核心原则**部分，必须**加重强调和清晰定义**：我们编写的 `docs/` 目录下的组件设计文档的第3部分，是项目的**单一事实来源 (SSoT)**，是**内部核心技术文档**，其目标是精确指导开发（与代码一一对应）。它**不是**交付文档本身，而是**生成**交付用《詳細設計書》的**权威信息源**。
        *   在指南中，当指代 `docs/` 目录下的这部分内容时，应统一使用“**组件技术实现文档**”、“**核心详细设计文档**”或“**组件设计文档的第3部分**”等中性且准确的表述，**严格避免直接使用“DDS”这个缩写来指代这些Markdown文件本身**，以防混淆。仅在讨论最终交付物时才使用“交付用《詳細設計書》(Excel/Word)”这样的全称。

2.  **强化Markdown列表格式规范，特别是避免深层嵌套和错误缩进 (针对3.x各子章节，尤其是3.3, 3.4, 3.6)**:
    *   **收获/问题**: 这是本次迭代中导致最多返工和困扰的技术性问题。过度和错误的列表嵌套（尤其是在描述复杂条件分支和多步骤流程时）严重影响了文档的可读性和渲染正确性。
    *   **升级建议**:
        *   在指南中新增一个**专门的小节或附录**，详细阐述“**如何在Markdown中清晰表述复杂逻辑流程和条件分支**”。
        *   **明确禁止超过两到三层的深层列表嵌套。**
        *   **提供并推广替代方案**：
            *   **使用子标题 (例如 `#####`)** 将一个复杂步骤（如 `handleConfirmTaskSubmission`）分解为多个逻辑上独立的、更易管理的小节（例如，将其中的响应处理逻辑提升为一个独立的子标题引导的描述块）。
            *   在子标题下，优先使用**清晰的段落和逻辑引导词**（首先、然后、如果...则...、此外、最后等）来描述顺序步骤和条件分支。
            *   如果在一个段落中确实需要列举少量并列事项，才使用**简单的单层无序或有序列表**（标准数字 `1.` `2.` 或星号 `*` 标记）。
            *   **强调正确的缩进规则**：列表项内容（包括其下的段落或单层子列表）必须有相对于其直接父列表项（或页面边距，如果是顶层列表）的正确且一致的缩进。**严禁为表达逻辑从属关系而对普通段落进行不必要的、可能导致被误认为代码块的行首空格缩进。**
        *   **提供“错误/难以阅读的嵌套列表”与“推荐的清晰表达方式（使用子标题和段落）”的对比示例。**

3.  **进一步明确“与代码一一对应”的具体要求 (针对3.x各子章节)**:
    *   **收获/问题**: 虽然有此目标，但具体到每个设计方面（UI、Server Action、数据交互等）如何才算“对应”，仍需要更细化的指导。
    *   **升级建议**: 在指南中针对 `3.2 详细界面元素定义`, `3.3 详细事件处理逻辑`, `3.4 数据结构与API/Server Action交互`, `3.5 数据库设计与访问详情`, `3.6 关键后端逻辑/算法` 等核心章节，分别**更具体地列出为达到“与代码一一对应”所必须包含的信息点和描述深度**。例如：
        *   `3.2`: 明确要求表格中必须包含前端状态变量名或props名与UI控件的绑定关系，以及触发的事件处理器函数名。
        *   `3.3`: 明确要求对事件处理器函数的内部逻辑步骤进行结构化描述。
        *   `3.4`: 强调Server Action的输入参数（`FormData`字段或参数对象）和返回值（`CreateTaskActionResult`结构）必须有精确定义。
        *   `3.6`: 强调对Server Action内部流程（包括参数校验、外部服务调用、数据库事务、错误处理、补偿逻辑等）的每一步都需要清晰描述。

4.  **消除“可选”描述，强调设计决策的明确性 (针对全文)**:
    *   **收获/问题**: 文档中不应存在指导性模糊的“可选”内容。
    *   **升级建议**: 在指南中增加一条核心原则：“**所有设计决策必须明确。**” 指导编写者在遇到不确定点时，应通过讨论或研究将其明确化，而不是在文档中保留“可选”、“可能”等描述。如果某个行为确实依赖于特定条件，则必须清晰描述这些条件。

5.  **配置项管理与引用的规范 (针对3.8 配置项)**:
    *   **收获/问题**: 对`LOV`配置的引用可能不准确，或引入了不存在的配置。
    *   **升级建议**:
        *   **强调严格基于已提供的`LOV列表`进行引用。**
        *   如果逻辑上需要新的配置项，应在文档中明确指出这是一个“**待新增的配置项**”，并描述其期望的`code`, `name`, `value`和用途，**而不是假设其已存在**。
        *   对于像“临时上传文件的Blob容器名”这类可能在多个地方用到的配置，应推荐在`LOV`中定义一个统一的`code`（例如，`AZURE_STORAGE.CONTAINER_TEMP_UPLOADS`），而不是在每个组件设计文档中各自假设一个名称。

6.  **Mermaid图表规范的再次强调与细化 (针对3.4等章节)**:
    *   **收获/问题**: 图表消息过长、参与者定义不当等问题反复出现。
    *   **升级建议**: 在指南中关于Mermaid图表的部分，除了已有的规范外，可以：
        *   **提供更具体的“好”与“坏”的序列图示例**，特别是在参与者选择和消息简洁性方面。
        *   再次强调AI助手有责任在输出图表前进行渲染验证。

7.  **任务记录保留策略等全局业务规则的体现方式**:
    *   **收获/问题**: 像任务记录保留这样的全局规则，其执行逻辑主要在一个核心组件（如`createTaskAction`）中实现，但其影响会遍及多个具体任务。
    *   **升级建议**: 指南中可以建议：
        *   这类全局规则的**详细执行逻辑**应在其主要负责实现的组件（如`createTaskAction.md`）的“技术设计与实现细节”中详细描述。
        *   其他受影响的组件设计文档（如各个具体任务的文档），应在其“业务规则”或“注意事项”中**简要提及此规则对其产生的影响**（例如，任务记录和关联文件会被自动清理），并**链接**到那个详细描述执行逻辑的文档。这样既保证了信息共享，又避免了在多处重复描述复杂逻辑。

通过将这些从实践中总结出来的经验和教训融入到核心的《FS迁移指南》和《DDS迁移指南》中，我们可以为未来所有新功能的文档编写工作提供更强大、更精确、更具操作性的理论支持和方法论指导，从而持续提升整个项目技术文档的质量和一致性。
