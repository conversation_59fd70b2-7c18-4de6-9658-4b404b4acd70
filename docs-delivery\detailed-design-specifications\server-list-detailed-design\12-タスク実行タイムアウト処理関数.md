## タスク実行タイムアウト処理関数 (TaskExecuteTimeoutFunc) 詳細設計

### 概要

#### 責務
本関数TaskExecuteTimeoutFuncは、タスク実行関数TaskExecuteFuncの実行が例外やタイムアウトによって3回（TaskInputQueueの最大配信数）まで失敗し、元々TaskInputQueueにあったメッセージが破棄され、Azure Service BusによってTaskInputQueueのDLQ（Dead-Letter Queue）へ配信された場合に起動されるAzure Functionsの関数である。本関数の主な責務は以下の通りである。
1.  TaskInputQueueのDLQメッセージを解析し、関連するタスクIDを特定する。
2.  Azure Automationのジョブ取得APIを呼び出して、タスク実行関数によってジョブが作成されたか確認する。作成された場合はタスク実行関数の処理が正常に完了したと見なして、本関数を終了する。
3.  Azure Files上のタスク作業ディレクトリの削除を試みる。
4.  タスクテーブルから入力のタスクIDで検索し、該当するタスク情報を取得する。
5.  コンテナ実行状態テーブルから対象コンテナの タスク実行状態（status） を'IDLE' に、使用中のタスクID（currentTaskId） をnull に更新する。
6.  対象タスクの現在のステータスを確認する。PENDING_CANCELLATIONまたはCANCELLEDの場合はステップ7.のタスクレコード更新をスキップ。
7.  Taskテーブルから対象タスクのステータスをCOMPLETED_ERRORに、エラーコードとタスク詳細を EMET0005および対応するエラーメッセージに更新する。

#### トリガー
Azure Service Bus - TaskInputQueue のDLQメッセージ。

#### 主要入力
*   TaskInputQueue のDLQから受信する、元のタスク実行要求メッセージ。メッセージ構造はタスク実行関数TaskExecuteFuncの主要入力と同様。
*   Azure Automation API: APIを呼び出してtaskId=ジョブ名に対応するジョブが作成されたか確認する。
*   Azure SQL Database のTaskテーブルからの読み取り。

#### 主要出力
*   Azure SQL Database のTaskテーブル、ContainerConcurrencyStatusテーブルのレコード更新。
*   Azure Files上のタスク作業ディレクトリ (`TaskWorkspaces/<TaskID>/`) の削除。

※DLQの名称は、TaskInputQueueの名称（環境変数 `SERVICE_BUS_TASK_INPUT_QUEUE_NAME` で指定）に接尾辞 `/$DeadLetterQueue` を付加したものとなる。
※Azure Filesへのアクセスには環境変数 AZURE_STORAGE_CONNECTION_STRING で指定される接続文字列が使用される。
※データベース接続には環境変数 MSSQL_PRISMA_URL で指定される接続文字列が使用される。
※Azure Automationアカウントの名称は環境変数 AZURE_AUTOMATION_ACCOUNT_NAME により指定される。
※サブスクリプションIDは環境変数 SUBSCRIPTION_ID により指定される。
※リソースグループ名は環境変数 RESOURCE_GROUP_NAME により指定される。

### 主要処理フロー

```mermaid
graph TD
    A["開始: TaskInputQueue の<br/>DLQメッセージ受信"] --> B["メッセージ解析・<br/>taskId取得"];
    B -- "不足/不正" --> ErrorLog["エラーログ記録"];
    B -- "正常" --> JobGet["automation Job Get API呼び出し、ジョブ取得"];
    JobGet -- "呼び出し失敗" -->  ErrorLog;
    JobGet -- "ジョブあり" --> Log["補償不要のログ記録"];
    JobGet -- "ジョブなし" --> F_FilesClean["Azure Files上の<br/>作業ディレクトリ削除試行<br/>(TaskWorkspaces/{taskId}/)"];
    F_FilesClean -- "削除失敗" --> FA_FilesClean["ログ記録(処理継続)"];
        FA_FilesClean --> C["タスクテーブルよりタスク情報取得<br/>(taskId使用)"];
    F_FilesClean -- "削除成功" --> C;
    C -- "取得失敗/taskレコードなし" --> CA["エラーログ記録"];
    C -- "Task取得成功" --> ContainerUpdate["コンテナ実行状態テーブルから対象コンテナをstatus='IDLE'、currentTaskId=nullで更新"];
    ContainerUpdate -- "DB更新失敗/0件" --> ContainerUpdateFail["ログ記録(処理継続)"];
        ContainerUpdateFail --> D{"タスクの現在のステータス<br/>(Task.status)がPENDING_CANCELLATION<br/>またはCANCELLED"};
    ContainerUpdate -- "DB更新成功" --> D;
    D -- "はい" --> CompleteLog["完了ログ記録"];
    D -- "いいえ" --> E["タスク情報をCOMPLETED_ERROR、EMET0005に更新"];
    E -- "DB更新失敗/0件" --> EA["ログ記録(処理継続)"];
        EA --> CompleteLog;
    E -- "DB更新成功" --> CompleteLog;

    ErrorLog --> Z["終了(メッセージACK)"];
    CA --> Z;
    Log --> Z;
    CompleteLog --> Z;
```
#### 共通処理フロー詳細

1.  Azure Service Bus の TaskInputQueue/$DeadLetterQueue からメッセージを受信し、解析する。受信したメッセージ内容（特にtaskId）をログに出力する。
2.  メッセージ内の基本パラメータ（taskId）の存在と値が空であるかを確認する。
    *   情報が不足/不正の場合：エラー詳細をログに出力して、処理を終了する。
3.  Azure Automationのジョブ取得APIを呼び出して、タスク実行関数によってジョブが作成されたか確認する。
    *   taskId=jobName を使用して、Azure Automationのジョブ取得APIを呼び出してジョブの存在を問い合わせる。下記のリクエストを構築してAPIを呼び出す。
    *   ジョブが存在しない場合はHTTPステータスコードが404のレスポンスが返却される。この場合はステップ4.に進める。
    *   ジョブが存在している場合はHTTPステータスコードが200（OK）のレスポンスが返却される。この場合は補償不要のログを出力して、処理を終了する。
    *   API の呼び出しに失敗した場合は、HTTPステータスコードが200（OK）または404以外のレスポンスが返却される。この場合はエラー詳細をログに記録した後、処理を終了する。

    リクエスト
    URI：GET https://management.azure.com/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Automation/automationAccounts/{automationAccountName}/jobs/{jobName}?api-version=2023-11-01
    ※{subscriptionId}には環境変数SUBSCRIPTION_IDで指定されたサブスクリプションID、{resourceGroupName}には環境変数RESOURCE_GROUP_NAMEで指定されたリソースグループ名、{automationAccountName}には環境変数AZURE_AUTOMATION_ACCOUNT_NAMEで指定されたAutomationのアカウント名、{jobName}にはtaskIdを設定する（タスク実行関数ではtaskId=jobNameでジョブを作成した）。
    ※Request Bodyは必要なし。

    サンプルレスポンス
    Status code: 200
    ```json
    {
      "id": "/subscriptions/********-3ed7-4a72-a187-0c8ab644ddab/resourceGroups/mygroup/providers/Microsoft.Automation/automationAccounts/ContoseAutomationAccount/jobs/jobName",
      "name": "foo",
      "type": "Microsoft.Automation/AutomationAccounts/Jobs",
      "properties": {
        "jobId": "5b8a3960-e8ab-45f6-bec6-567df8467d1a",
        "runbook": {
          "name": "TestRunbook"
        },
        "provisioningState": "Processing",
        "creationTime": "2018-02-01T05:53:30.243+00:00",
        "endTime": null,
        "exception": null,
        "lastModifiedTime": "2018-02-01T05:53:30.243+00:00",
        "lastStatusModifiedTime": "2018-02-01T05:53:30.243+00:00",
        "startTime": null,
        "status": "New",
        "statusDetails": "None",
        "parameters": {
          "tag01": "value01",
          "tag02": "value02"
        },
        "runOn": ""
      }
    }
    ```
4.  Azure Files上のタスク作業ディレクトリ（`TaskWorkspaces/<taskId>/`）の削除を試みる。
    *   削除に失敗した場合はエラーをログに記録するが、後続処理は継続する。
5.  taskId を使用して Task テーブルを検索し、タスクの情報を取得する。
    *   DB読み取り失敗、またはタスクレコードが存在しない場合：エラー詳細をログに出力して、処理を終了する。
6.  コンテナ実行状態ContainerConcurrencyStatus テーブルの対象コンテナレコードを更新する。条件：対象VM名と対象コンテナ名がステップ5.で取得した情報と一致し、ステータスがBUSYで、使用中のタスクID（currentTaskId）が入力のtaskIdと一致する。
    *   対象コンテナの タスク実行状態（status） を'IDLE' に、使用中のタスクID（currentTaskId） をnull に更新する。
    *   更新失敗または更新件数が0件の場合はエラーをログに記録するが、後続処理は継続する。
7.  タスクのステータスがPENDING_CANCELLATIONまたはCANCELLEDであるかチェックする。
    *   PENDING_CANCELLATIONまたはCANCELLEDの場合：ステップ8.をスキップして、ステップ9.に進める。
    *   上記でない場合：ステップ8.に進める。
8.  タスクTask テーブルの該当タスクレコードを更新する。条件：IDが入力のtaskIdと一致し、最終更新日時がステップ5.で取得した最終更新日時と一致する。
    *   該当タスクのstatusをCOMPLETED_ERRORに、errorCodeをEMET0005に、タスク詳細（resultMessage）を「タスクの完了が確認できないため、システムによってタスクを中止しました。タスクが操作ログのエクスポートの場合、エクスポートするログの期間を短くして再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMET0005)」に更新する。
    *   更新失敗または更新件数が0件の場合はエラーをログに記録するが、後続処理は継続する。
9.  処理完了のログを出力して、処理を終了する。（メッセージACK）。

### 主要なデータ及び外部サービスとの対話詳細

#### Azure Service Bus (TaskInputQueue/$DeadLetterQueue) からのメッセージ受信
*   本Functionは TaskInputQueue のDLQメッセージをトリガーとする。
*   メッセージ内容は元のタスク実行要求メッセージ。
*   DLQの最大配信数は1に設定されているため、タスク実行タイムアウト関数は一回だけ起動される。タスク実行タイムアウト関数が処理成功した場合メッセージはACKされキューから削除される。タスク実行タイムアウト関数が例外やタイムアウトにより処理失敗した場合メッセージはDLQに残り続けて、システム管理者による手動削除が必要となる。

#### データベース との対話
*   **Task テーブル**:
    *   taskId をキーに読み取り、タスクに関する情報を取得する。
    *   status を COMPLETED_ERROR に、errorCode と resultMessage を該当のエラーコードとエラーメッセージ内容に更新する。
*   **ContainerConcurrencyStatus テーブル**:
    *   対象コンテナのレコードを status = 'IDLE', currentTaskId = null で更新する。

#### Azure Files との対話
*   タスク作業ディレクトリのパス (`TaskWorkspaces/<taskId>/`) を特定し、そのディレクトリ全体の削除を試行する。

#### Azure Automation API との対話
*   ジョブ取得APIを呼び出して、タスク実行関数がRunbookジョブを作成したかどうか確認する。

### エラー処理メカニズム

| エラーシナリオ | 関連エラーコード | 対処・補償ロジック |
|:---|:---|:---|
| メッセージ基本パラメータ確認エラー (`taskId`不足/不正) | - | エラーログ記録。処理終了。（メッセージACK） |
| Azure Automation API呼び出し失敗 | - | エラーログ記録。処理終了。（メッセージACK） |
| 対象タスク情報 (`Task`レコード) がデータベースに存在しない/DB読み取り失敗 | - | エラーログ記録。処理終了。（メッセージACK） |
| Azure Files作業ディレクトリ削除失敗 | - | ログ記録して、処理を継続。 |
| DB更新失敗、更新件数が0件 | - | ログ記録して、処理を継続。 |
| 予期せぬ内部エラー | - | エラーログ記録。処理終了。（メッセージACK） |

*   **タイムアウトについて**
    Azure Functionsがタスク実行タイムアウト関数の実行時間を監視し、host.jsonのfunctionTimeoutプロパティで設定されたタイムアウト時間（5分）になっても関数がまだ終了していない場合、Azure Functionsは関数の実行を強制的に中止する。
*   **リトライについて**
    DLQの最大配信数は1に設定されているため、タスク実行タイムアウト関数は一回だけ実行される。例外やタイムアウトにより処理失敗の場合でもリトライは行わない。この場合はシステム管理者による人的確認と対処が必要となる。