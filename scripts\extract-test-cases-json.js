#!/usr/bin/env node

/**
 * テストケース抽出スクリプト - JSON形式出力
 *
 * テストファイルからテストケース情報を抽出し、構造化されたJSON形式のレポートを生成する
 * 構造：プロジェクト名 -> 大項目 -> 小項目 -> テストケースリスト
 */

const fs = require('fs');
const path = require('path');

// プロジェクト設定
const PROJECTS = [
  {
    name: 'jcs-endpoint-nextjs',
    displayName: 'ポータル',
    path: 'apps/jcs-endpoint-nextjs/__tests__',
    type: 'frontend'
  },
  {
    name: 'jcs-backend-services-standard',
    displayName: 'タスク Function App',
    path: 'apps/jcs-backend-services-standard/__tests__',
    type: 'backend'
  },
  {
    name: 'jcs-backend-services-long-running',
    displayName: 'Runbook ジョブ Function App',
    path: 'apps/jcs-backend-services-long-running/__tests__',
    type: 'backend'
  }
];

// 必須の日本語キーワード
const REQUIRED_KEYWORDS = [
  '試験観点：',
  '試験対象：',
  '試験手順：',
  '確認項目：'
];

/**
 * ディレクトリ下のすべてのテストファイルを再帰的に取得
 */
function getTestFiles(dir) {
  const files = [];

  if (!fs.existsSync(dir)) {
    console.warn(`⚠️  ディレクトリが存在しません: ${dir}`);
    return files;
  }
  
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      files.push(...getTestFiles(fullPath));
    } else if (item.endsWith('.test.ts') || item.endsWith('.test.tsx')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * テストファイルを解析し、テストケースを抽出
 */
function parseTestFile(filePath, projectInfo) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const fileCases = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // it( または test( で始まる行を検索
    if (line.match(/^\s*(it|test)\s*\(/)) {
      // テスト名を抽出
      const testNameMatch = line.match(/^\s*(it|test)\s*\(\s*["'`]([^"'`]+)["'`]/);
      const testName = testNameMatch ? testNameMatch[2] : '不明なテスト名';
      
      // 上方向にコメントブロックを検索
      let commentStart = -1;
      let commentEnd = -1;

      for (let j = i - 1; j >= 0; j--) {
        const prevLine = lines[j].trim();

        if (prevLine === '*/') {
          commentEnd = j;
        } else if (prevLine.startsWith('/**')) {
          commentStart = j;
          break;
        } else if (prevLine === '' || prevLine.startsWith('*')) {
          continue;
        } else {
          break;
        }
      }

      // コメント内容を抽出
      let commentInfo = {
        試験観点: '',
        試験対象: '',
        試験手順: '',
        確認項目: ''
      };
      
      if (commentStart >= 0 && commentEnd >= 0) {
        const commentLines = lines.slice(commentStart, commentEnd + 1);
        const commentText = commentLines.join('\n');

        // キーワード内容を解析
        for (const keyword of REQUIRED_KEYWORDS) {
          const keywordName = keyword.replace('：', '');
          const keywordRegex = new RegExp(
            `${keyword}\\s*([\\s\\S]*?)(?=\\n\\s*\\*\\s*(?:${REQUIRED_KEYWORDS.map(k => k.replace('：', '：')).join('|')})|\\n\\s*\\*/|$)`,
            'g'
          );
          const match = keywordRegex.exec(commentText);
          if (match) {
            // 改行文字を保持し、フォーマットをクリーンアップ
            let content = match[1]
              .replace(/\n\s*\*\s*/g, '\n')  // 改行を保持、* プレフィックスを削除
              .replace(/^\s*\*\s*/, '')      // 先頭の * を削除
              .trim();

            commentInfo[keywordName] = content;
          }
        }
      }
      
      // 分類を決定
      const relativePath = path.relative(projectInfo.path, filePath);
      const pathParts = relativePath.replace(/\\/g, '/').split('/');

      // 大項目：第一階層ディレクトリ
      let majorCategory = pathParts[0] || 'root';
      if (majorCategory === '.') majorCategory = 'root';

      // 小項目：ファイル名（.test.ts/.test.tsx拡張子を除去）
      const fileName = path.basename(filePath);
      let minorCategory = fileName.replace(/\.test\.(ts|tsx)$/, '');
      
      const testCase = {
        テストケース名: testName,
        試験観点: commentInfo.試験観点,
        試験対象: commentInfo.試験対象,
        試験手順: commentInfo.試験手順,
        確認項目: commentInfo.確認項目,
        _metadata: {
          filePath: filePath.replace(/\\/g, '/'),
          lineNumber: i + 1,
          majorCategory,
          minorCategory
        }
      };
      
      fileCases.push(testCase);
    }
  }
  
  return fileCases;
}

/**
 * テストケースを3階層構造に整理
 */
function organizeTestCases(allTestCases) {
  const organized = {};

  // プロジェクト別にグループ化
  for (const project of PROJECTS) {
    const projectCases = allTestCases.filter(tc =>
      tc._metadata.filePath.includes(project.path.replace(/\\/g, '/'))
    );

    if (projectCases.length === 0) continue;
    
    organized[project.name] = {
      displayName: project.displayName,
      type: project.type,
      categories: {}
    };
    
    // 大項目別にグループ化
    const majorCategories = {};
    projectCases.forEach(tc => {
      const major = tc._metadata.majorCategory;
      if (!majorCategories[major]) {
        majorCategories[major] = [];
      }
      majorCategories[major].push(tc);
    });

    // 小項目別にグループ化
    for (const [majorName, majorCases] of Object.entries(majorCategories)) {
      organized[project.name].categories[majorName] = {
        displayName: majorName,
        subCategories: {}
      };

      const minorCategories = {};
      majorCases.forEach(tc => {
        const minor = tc._metadata.minorCategory;
        if (!minorCategories[minor]) {
          minorCategories[minor] = [];
        }
        minorCategories[minor].push(tc);
      });

      for (const [minorName, minorCases] of Object.entries(minorCategories)) {
        // _metadata フィールドを削除
        const cleanCases = minorCases.map(tc => {
          const { _metadata, ...cleanCase } = tc;
          return cleanCase;
        });

        organized[project.name].categories[majorName].subCategories[minorName] = {
          displayName: minorName,
          testCases: cleanCases
        };
      }
    }
  }
  
  return organized;
}

/**
 * 統計情報を生成
 */
function generateStatistics(organized) {
  const stats = {
    totalProjects: 0,
    totalMajorCategories: 0,
    totalMinorCategories: 0,
    totalTestCases: 0,
    byProject: {}
  };
  
  for (const [projectName, projectData] of Object.entries(organized)) {
    stats.totalProjects++;
    
    let projectMajorCount = 0;
    let projectMinorCount = 0;
    let projectTestCount = 0;
    
    for (const [majorName, majorData] of Object.entries(projectData.categories)) {
      projectMajorCount++;
      stats.totalMajorCategories++;
      
      for (const [minorName, minorData] of Object.entries(majorData.subCategories)) {
        projectMinorCount++;
        stats.totalMinorCategories++;
        
        projectTestCount += minorData.testCases.length;
        stats.totalTestCases += minorData.testCases.length;
      }
    }
    
    stats.byProject[projectName] = {
      displayName: projectData.displayName,
      majorCategories: projectMajorCount,
      minorCategories: projectMinorCount,
      testCases: projectTestCount
    };
  }
  
  return stats;
}

/**
 * メイン関数
 */
function main() {
  console.log('🔍 テストケース抽出開始 (JSON形式)...\n');

  const allTestCases = [];

  // 各プロジェクトを処理
  for (const project of PROJECTS) {
    console.log(`📂 プロジェクト処理中: ${project.displayName}`);

    const testFiles = getTestFiles(project.path);
    console.log(`   ${testFiles.length}個のテストファイルを発見`);

    for (const filePath of testFiles) {
      const fileCases = parseTestFile(filePath, project);
      console.log(`   ${path.basename(filePath)}: ${fileCases.length}個のテストケース`);
      allTestCases.push(...fileCases);
    }

    console.log('');
  }
  
  // 3階層構造に整理
  console.log('📊 テストケース構造を整理中...');
  const organized = organizeTestCases(allTestCases);

  // 統計情報を生成
  const statistics = generateStatistics(organized);

  // 最終出力を作成
  const output = {
    metadata: {
      generatedAt: new Date().toISOString(),
      generatedBy: 'extract-test-cases-json.js',
      description: 'JCS Endpoint Monorepo テストケース抽出結果',
      structure: 'プロジェクト名 -> 大項目 -> 小項目 -> テストケースリスト'
    },
    statistics,
    testCases: organized
  };

  // JSONファイルを保存
  const outputPath = 'docs-delivery/unit-test-report/test-cases.json';
  fs.writeFileSync(outputPath, JSON.stringify(output, null, 2), 'utf8');

  console.log(`✅ JSONレポートが生成されました: ${outputPath}`);
  console.log(`📊 統計情報:`);
  console.log(`   - プロジェクト数: ${statistics.totalProjects}`);
  console.log(`   - 大項目数: ${statistics.totalMajorCategories}`);
  console.log(`   - 小項目数: ${statistics.totalMinorCategories}`);
  console.log(`   - テストケース総数: ${statistics.totalTestCases}`);

  // プロジェクト詳細を表示
  console.log('\n📋 各プロジェクト詳細:');
  for (const [projectName, projectStats] of Object.entries(statistics.byProject)) {
    console.log(`   ${projectStats.displayName}:`);
    console.log(`     - 大項目: ${projectStats.majorCategories}`);
    console.log(`     - 小項目: ${projectStats.minorCategories}`);
    console.log(`     - テストケース: ${projectStats.testCases}`);
  }
}

// スクリプト実行
if (require.main === module) {
  main();
}
