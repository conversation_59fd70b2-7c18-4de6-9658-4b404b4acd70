# 组件：任务中止超时处理函数 (Task Cancellation Timeout Function)

## 1. 概要 (Overview)

### 1.1. 目的 (Purpose)
本Azure Function (`TaskCancellationTimeoutFunc`) 旨在处理那些因为[`TaskCancellationFunc`](./function-task-cancellation.md)执行超时或其他持久性错误而未能成功处理、最终进入`TaskControlQueue`对应死信队列 (DLQ) 的任务中止请求消息。其主要职责是根据任务的当前状态，对数据库中的`Task`表进行补偿性更新或记录，确保系统状态的最终一致性和可追溯性。

### 1.2. 范围 (Scope)
本文档详细描述`TaskCancellationTimeoutFunc`的技术设计，包括其触发机制、核心处理逻辑、与数据库的交互、错误处理机制以及相关的配置项。

### 1.3. 组件类型 (Component Type)
*   Azure Function (Node.js/TypeScript 运行时)
*   触发器: Azure Service Bus Queue Trigger (监听 `TaskControlQueue` 的 Dead-Letter Queue)

### 1.4. 名词定义 (Glossary References)
*   **TaskControlQueue DLQ**: `TaskControlQueue`的死信队列，存储无法被`TaskCancellationFunc`正常处理的消息。
*   其他相关术语请参考项目核心术语表 `docs/definitions/glossary.md`。
*   本文档中所有用户可见的错误和提示信息，均引用自项目核心错误消息定义文档 `docs/definitions/error-messages.md`。
*   本文档中所有环境变量的定义，均参考项目核心环境变量指南 `docs/guides/environment-variables.md`。
*   任务状态码常量定义在 `app/lib/definitions.ts`。

## 2. 功能规格 (Functional Specifications)

### 2.1. 主要流程 (Main Process Flow)

```mermaid
graph TD
    A["TaskControlQueue DLQ接收到中止消息<br/>(taskId, 来自原TaskCancellationFunc超时)"] --> B{TaskCancellationTimeoutFunc被触发};
    B --> C["1.解析消息, 获取taskId"];
    C --> D["2.查询Task表获取任务当前状态<br/>(使用taskId)"];
    D --> E{任务当前状态是?};
    E -- "PENDING_CANCELLATION" --> F["2.1 更新Task状态为CANCELLED<br/>更新resultDetails为EMET0004<br/>记录原TaskCancellationFunc超时"];
    E -- "RUNBOOK_SUBMITTED 或<br/>RUNBOOK_PROCESSING" --> G["2.2 更新Task的resultDetails为EMET0006<br/>(状态不变更)<br/>记录原TaskCancellationFunc超时"];
    E -- "COMPLETED_SUCCESS,<br/>COMPLETED_ERROR, 或<br/>CANCELLED" --> H["2.3 不做任何处理 (幂等)<br/>记录原TaskCancellationFunc超时及当前状态"];
    E -- "QUEUED (理论上极少见)" --> I["2.4 更新Task状态为CANCELLED<br/>更新resultDetails为EMET0004<br/>记录原TaskCancellationFunc超时"];
    E -- "其他/未知状态" --> J["2.5 记录严重错误及原超时信息"];
    F --> K["3.记录成功处理日志"];
    G --> K;
    H --> K;
    I --> K;
    J --> Z["结束处理 (含错误记录)"];
    K --> Z;
```
**图 2.1: TaskCancellationTimeoutFunc 主要处理流程图**

### 2.2. 功能点描述 (Functional Points)
1.  **DLQ消息触发**: 函数由`TaskControlQueue`的DLQ中的新消息触发。消息体与原`TaskControlQueue`消息相同，主要包含`taskId`。
2.  **超时日志记录**: 首先，记录一条明确的日志，表明这是一个由[`TaskCancellationFunc`](./function-task-cancellation.md)超时导致的消息，并包含原始消息的关键信息（如`taskId`）。
3.  **任务状态校验与补偿更新**:
    *   根据`taskId`从数据库`Task`表查询该任务的当前`status`和`resultDetails`。
    *   如果任务的当前`status`为 `TASK_STATUS_PENDING_CANCELLATION_CODE` ("PENDING_CANCELLATION"):
        *   将`Task.status`更新为 `TASK_STATUS_CANCELLED_CODE` ("CANCELLED")。
        *   将`Task.resultDetails`更新为消息键 `EMET0004` 对应的日文消息 ("ユーザーによってタスクが中止されました。")。
        *   更新`Task.endedAt`为当前时间。
        *   在日志中强调这是超时补偿操作。
    *   如果任务的当前`status`为 `TASK_STATUS_RUNBOOK_SUBMITTED_CODE` ("RUNBOOK_SUBMITTED") 或 `TASK_STATUS_RUNBOOK_PROCESSING_CODE` ("RUNBOOK_PROCESSING"):
        *   **不修改**`Task.status`。
        *   将`Task.resultDetails`更新为消息键 `EMET0006` 对应的日文消息 ("タスクの中止がタイムアウトしました。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMET0006)")。
        *   在日志中强调这是超时补偿操作。
    *   如果任务的当前`status`为 `TASK_STATUS_COMPLETED_SUCCESS_CODE` ("COMPLETED_SUCCESS"), `TASK_STATUS_COMPLETED_ERROR_CODE` ("COMPLETED_ERROR"), 或 `TASK_STATUS_CANCELLED_CODE` ("CANCELLED"):
        *   不执行任何数据库更新操作（幂等处理）。记录相应的参考日志，注明原始中止请求已因超时而通过本函数处理，但任务此前已达终态。
    *   如果任务的当前`status`为 `TASK_STATUS_QUEUED_CODE` ("QUEUED") (此情况在DLQ场景下理论上更为罕见，除非`TaskCancellationFunc`在首次读取任务状态前就超时):
        *   将`Task.status`更新为 `TASK_STATUS_CANCELLED_CODE` ("CANCELLED")。
        *   将`Task.resultDetails`更新为消息键 `EMET0004` 对应的日文消息。
        *   更新`Task.endedAt`为当前时间。
        *   在日志中强调这是超时补偿操作。
    *   对于其他未预期的任务状态，记录严重错误日志，包含`taskId`和当前状态，以及这是一个超时处理的上下文。通常不进行状态变更，以避免引入更多不确定性。
4.  **日志记录**: 对所有接收的DLQ消息、执行的数据库操作、遇到的错误以及最终处理结果进行详细且带有“超时补偿”上下文的日志记录。

### 2.3. 业务规则 (Business Rules)
*   本函数是对`TaskCancellationFunc`未能成功处理（主要是由于执行超时）的中止请求的最终补偿和状态修正。
*   其目标是尽可能使`Task`记录的状态反映出中止尝试的结果，即使用户体验到的是超时。

### 2.4. 前提条件 (Preconditions)
*   Azure Service Bus (`TaskControlQueue`的DLQ) 正常运行。
*   Azure SQL Database (`Task`表) 正常运行且可访问。
*   相关的`LOV`定义和错误消息定义已在系统中正确配置。

### 2.5. 制约事项 (Constraints)
*   本函数同样不负责实际停止正在运行的Runbook作业。
*   由于是处理DLQ消息，通常意味着原始处理已遇到问题，本函数应侧重于数据状态的最终修正和日志记录，而非复杂的业务逻辑重试。

### 2.6. 注意事项 (Notes)
*   本函数的设计也应保证幂等性。
*   如果本函数自身执行也发生严重错误或超时，消息可能会保留在DLQ中（取决于Service Bus的DLQ配置），此时需要人工介入分析和处理。

---

## 3. 技术设计与实现细节 (Technical Design & Implementation)

### 3.1. 技术栈与依赖 (Technology Stack & Dependencies)
*   **运行时**: Azure Functions (Node.js/TypeScript)。
*   **触发器**: Azure Service Bus Queue Trigger (监听`TaskControlQueue`的DLQ)。
    *   DLQ名称: 通常是主队列名称后附加 `/$DeadLetterQueue`。具体配置在`function.json`的绑定中。
*   **数据库交互**: Prisma ORM (通过 `app/lib/data.ts` 或直接使用 `app/lib/prisma.ts`)。
*   **消息队列SDK**: `@azure/service-bus` (主要用于消息属性的读取，如`deliveryCount`等，如果需要)。
*   **日志**: `app/lib/logger.ts`。
*   **核心定义与常量**: `app/lib/definitions.ts`。

### 3.2. 详细界面元素定义 (Detailed UI Element Definitions)
*   本组件为后端Azure Function，无直接用户界面。

### 3.3. 详细事件处理逻辑 (Detailed Event Handling)
*   本组件由Service Bus DLQ消息触发，其核心处理逻辑见3.6节。

### 3.4. 数据结构与API/Server Action交互 (Data Structures and API/Server Action Interaction)

#### 3.4.1. 输入消息结构 (from `TaskControlQueue/$DeadLetterQueue`)
消息体与原`TaskControlQueue`消息相同，预期包含：
```typescript
{
  "taskId": "cuid-or-uuid-of-the-task-to-cancel"
}
```
此外，Service Bus消息本身会包含死信原因等元数据，可在Function绑定中或通过SDK访问，用于增强日志记录。

#### 3.4.2. 与数据库的交互 (`Task` 表)
详见3.5节。

#### 3.4.3. 序列图 (Mermaid `sequenceDiagram`)

```mermaid
sequenceDiagram
    participant DLQ as "Azure Service Bus (TaskControlQueue/$DeadLetterQueue)"
    participant TimeoutFunc as "TaskCancellationTimeoutFunc (Azure Function)"
    participant Database as "Azure SQL Database (Task表)"

    DLQ->>TimeoutFunc: DLQ消息: { taskId: "task123", DeadLetterReason: "TTLExpired" }
    activate TimeoutFunc
    TimeoutFunc->>TimeoutFunc: 1. 解析消息，获取 taskId<br/>   记录日志: "处理DLQ消息 (原TaskCancellationFunc超时)"
    TimeoutFunc->>Database: 2. (内部通过app/lib/data) Prisma: task.findUnique({ where: { id: "task123" } })
    activate Database
    Database-->>TimeoutFunc: Task对象 (e.g., status: 'PENDING_CANCELLATION')
    deactivate Database

    alt 任务状态为 PENDING_CANCELLATION
        TimeoutFunc->>Database: 2.1 (内部通过app/lib/data) Prisma: task.update(...)<br/>   data: { status: 'CANCELLED', resultDetails: "EMET0004", endedAt: now() }
        activate Database
        Database-->>TimeoutFunc: 更新成功
        deactivate Database
    else G["任务状态为 RUNBOOK_SUBMITTED 或 RUNBOOK_PROCESSING"]
        TimeoutFunc->>Database: 2.2 (内部通过app/lib/data) Prisma: task.update(...)<br/>   data: { resultDetails: "EMET0006" }
        activate Database
        Database-->>TimeoutFunc: 更新成功
        deactivate Database
    else H["任务已结束或状态为 QUEUED"]
        TimeoutFunc->>TimeoutFunc: 2.3 / 2.4 相应处理 (可能更新DB或仅记录日志)
    else J["其他/未知状态"]
        TimeoutFunc->>TimeoutFunc: 2.5 记录严重错误
    end
    TimeoutFunc->>TimeoutFunc: 3. 记录处理完成日志
    deactivate TimeoutFunc
```

### 3.5. 数据库设计与访问详情 (Database Design and Access Details)

#### 3.5.1. 相关表引用
*   **`Task` 表**: 主要目标表。

#### 3.5.2. 主要数据查询/变更逻辑

1.  **查询任务当前状态**:
    *   同`TaskCancellationFunc`：`prisma.task.findUnique({ where: { id: message.taskId } })`。
2.  **更新任务状态和结果详情 (补偿逻辑)**:
    *   **当原状态为PENDING_CANCELLATION**:
        ```typescript
        prisma.task.update({
          where: { id: message.taskId },
          data: {
            status: TASK_STATUS_CANCELLED_CODE,
            resultDetails: getErrorMessageByKey(PORTAL_ERROR_MESSAGES_KEYS.EMET0004),
            endedAt: new Date(),
          },
        });
        ```
    *   **当原状态为RUNBOOK_SUBMITTED / RUNBOOK_PROCESSING**:
        ```typescript
        prisma.task.update({
          where: { id: message.taskId },
          data: {
            resultDetails: getErrorMessageByKey(PORTAL_ERROR_MESSAGES_KEYS.EMET0006),
          },
        });
        ```
*   **事务管理**: 与`TaskCancellationFunc`类似，单个消息的处理是独立的。

### 3.6. 关键后端逻辑/算法 (Key Backend Logic/Algorithms)

核心逻辑与`TaskCancellationFunc`类似，是一个基于任务当前状态的条件判断，但其更新的`resultDetails`内容和日志记录的上下文会有所不同，以反映这是超时补偿操作。

1.  提取`taskId`。记录DLQ处理上下文。
2.  获取任务记录。处理任务未找到或DB错误。
3.  根据`task.status`执行条件分支:
    *   **`PENDING_CANCELLATION`**: 更新状态为`CANCELLED`，`resultDetails`为`EMET0004`，记录超时补偿。
    *   **`RUNBOOK_SUBMITTED` 或 `RUNBOOK_PROCESSING`**: `resultDetails`更新为`EMET0006`，状态不变，记录超时补偿。
    *   **`COMPLETED_SUCCESS`, `COMPLETED_ERROR`, `CANCELLED`**: 仅记录日志（已终态，超时补偿未执行变更）。
    *   **`QUEUED`**: 更新状态为`CANCELLED`，`resultDetails`为`EMET0004`，记录超时补偿。
    *   **Default**: 记录严重错误。
4.  全局错误捕获。

### 3.7. 错误处理详情 (Detailed Error Handling)

| #  | 错误场景描述 (中文) | 触发位置 | 引用的消息ID/消息键 (用户可见部分) | 系统内部处理及日志记录建议 (中文) | 日志级别 |
|----|-----------------|----------|------------------------------------|---------------------------------|-----------|
| 1  | DLQ消息格式无效或`taskId`缺失 | Function触发，消息解析阶段 | (内部错误) | 记录错误，包含原始DLQ消息属性。 | ERROR |
| 2  | 根据`taskId`未找到任务 (DLQ处理时) | 调用`ServerData.getTaskById`后 | (内部错误) | 记录错误，包含`taskId`和DLQ上下文。 | ERROR |
| 3  | 数据库更新失败 (DLQ处理时) | Prisma调用时 | (内部错误) | 记录详细DB错误和DLQ上下文。如果此Function也失败，消息可能永久留在DLQ或按Service Bus策略处理，需人工关注。 | CRITICAL |
| 4  | 本Function执行超时 (极罕见) | Azure Functions运行时 | (内部错误) | 平台记录超时。需人工排查DLQ。 | CRITICAL |

### 3.8. 配置项 (Configuration)

#### 3.8.1. 值列表 (LOV) 定义 (源自 `docs/definitions/lov-definitions.md`)
*   `TASK_STATUS` (父级代码): 使用其下定义的内部状态码常量。

#### 3.8.2. 环境变量 (源自 `docs/guides/environment-variables.md`)
*   `SERVICE_BUS_TASK_CONTROL_QUEUE_NAME`: 用于在`function.json`中正确配置DLQ的触发器绑定 (通常为 `${SERVICE_BUS_TASK_CONTROL_QUEUE_NAME}/$DeadLetterQueue`)。
*   `MSSQL_PRISMA_URL`
*   `LOG_LEVEL`
*   `FUNCTION_TIMEOUT_SECONDS`

#### 3.8.3. 错误与提示消息 (源自 `docs/definitions/error-messages.md`)
本函数主要使用以下消息键更新`Task.resultDetails`:
*   `EMET0004`: "ユーザーによってタスクが中止されました。"
*   `EMET0006`: "タスクの中止がタイムアウトしました。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMET0006)"

### 3.9. 注意事项与其他 (Notes/Miscellaneous)
*   **日志明确区分**: 所有由本函数产生的日志，都应清晰标记是来自`TaskCancellationTimeoutFunc`的超时补偿处理，并包含原始消息的相关信息（如`DeadLetterReason`, `DeliveryCount`等Service Bus提供的属性），以便于区分和排查[`TaskCancellationFunc`](./function-task-cancellation.md)原始的超时问题。
*   **警报**: 对于DLQ中有消息并由本函数处理的情况，应考虑设置监控和告警，因为这通常指示了主处理流程([`TaskCancellationFunc`](./function-task-cancellation.md))存在问题（如性能瓶颈、依赖服务缓慢、代码bug等）。
