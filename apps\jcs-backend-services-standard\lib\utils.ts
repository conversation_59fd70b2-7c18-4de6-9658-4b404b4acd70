/**
 * @fileoverview 共通ユーティリティ関数
 * @description
 * エラーメッセージテンプレート処理、各種エラー判定機能、
 * Azure Files操作に関する共通ユーティリティ関数を提供する。
 * TaskWorkspacesディレクトリの固定構造に基づく非再帰的削除機能を含む。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { ShareDirectoryClient } from "@azure/storage-file-share";
import { InvocationContext } from "@azure/functions";

/**
 * エラーメッセージテンプレートをパラメータで置換するユーティリティ関数
 */
export function formatTaskErrorMessage(code: string, params: string[] = []): string {
  // AppConstants をここでインポート（循環参照を避けるため必要に応じて引数化も検討）
  // ここでは直接 import
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const { AppConstants } = require("./constants");
  const template = AppConstants.TASK_ERROR_MESSAGE[code as keyof typeof AppConstants.TASK_ERROR_MESSAGE] || "";
  return params.reduce((msg, val, idx) => msg.replace(`{${idx}}`, val), template);
}

/**
 * Prismaエラーかどうかを判定する関数
 * portal-error.ts の実装を参考にした優雅な判定方法
 */
export function isPrismaError(error: Error): boolean {
  return error.name.includes("PrismaClient");
}



/**
 * Azure Files関連エラーかどうかを判定する関数
 * Files操作エラーの場合はEMET0002を使用する
 */
export function isAzureFilesError(error: Error): boolean {
  return error.name.includes("RestError") &&
         (!!error.message && error.message.includes("Share")) ||
         error.name.includes("ShareError") ||
         error.name.includes("FileError") ||
         error.name.includes("DirectoryError");
}

/**
 * Azure Blob Storage関連エラーかどうかを判定する関数
 * Blob操作エラーの場合はEMET0003を使用する
 */
export function isAzureBlobError(error: Error): boolean {
  return error.name.includes("RestError") &&
         (!!error.message && error.message.includes("Blob")) ||
         error.name.includes("BlobError") ||
         error.name.includes("ContainerError");
}

/**
 * カスタムエラーコードを持つエラーかどうかを判定する関数
 */
export function hasErrorCode(error: unknown, code: string): boolean {
  return error instanceof Error &&
         "code" in error &&
         typeof (error as any).code === "string" &&
         (error as any).code === code;
}

/**
 * タスク作業ディレクトリを削除する
 *
 * @param taskDirectoryClient - 削除対象のタスクディレクトリクライアント
 * @param context - Azure Functions実行コンテキスト
 * @returns Promise<void>
 */
export async function deleteTaskWorkspaceDirectory(
  taskDirectoryClient: ShareDirectoryClient,
  context: InvocationContext
): Promise<void> {
  try {
    // ディレクトリが存在するかチェック
    const exists = await taskDirectoryClient.exists();
    if (!exists) {
      context.log(`[deleteTaskWorkspaceDirectory] タスクディレクトリが存在しません: ${taskDirectoryClient.path}`);
      return;
    }

    context.log(`[deleteTaskWorkspaceDirectory] タスクディレクトリ削除開始: ${taskDirectoryClient.path}`);

    await deleteImportsDirectory(taskDirectoryClient, context);
    await deleteExportsDirectory(taskDirectoryClient, context);
    await taskDirectoryClient.deleteIfExists();
    context.log(`[deleteTaskWorkspaceDirectory] タスクディレクトリ削除完了: ${taskDirectoryClient.path}`);

  } catch (error: any) {
    context.error(`[deleteTaskWorkspaceDirectory] タスクディレクトリ削除失敗: ${taskDirectoryClient.path}, エラー: ${error.message}`);
    throw error;
  }
}

/**
 * imports/ ディレクトリとその内容を削除する
 */
async function deleteImportsDirectory(
  taskDirectoryClient: ShareDirectoryClient,
  context: InvocationContext
): Promise<void> {
  const importsDirectoryClient = taskDirectoryClient.getDirectoryClient("imports");

  try {
    const importsExists = await importsDirectoryClient.exists();
    if (!importsExists) {
      context.log(`[deleteTaskWorkspaceDirectory] imports/ディレクトリが存在しません`);
      return;
    }

    const assetsFieldDefFileClient = importsDirectoryClient.getFileClient("assetsfield_def.csv");
    const fileExists = await assetsFieldDefFileClient.exists();
    if (fileExists) {
      await assetsFieldDefFileClient.delete();
      context.log(`[deleteTaskWorkspaceDirectory] ファイル削除: imports/assetsfield_def.csv`);
    }
    await importsDirectoryClient.deleteIfExists();
    context.log(`[deleteTaskWorkspaceDirectory] ディレクトリ削除: imports/`);

  } catch (error: any) {
    context.log(`[deleteTaskWorkspaceDirectory] imports/ディレクトリ削除中にエラー: ${error.message}`);
  }
}

/**
 * exports/ ディレクトリとその内容を削除する
 */
async function deleteExportsDirectory(
  taskDirectoryClient: ShareDirectoryClient,
  context: InvocationContext
): Promise<void> {
  const exportsDirectoryClient = taskDirectoryClient.getDirectoryClient("exports");

  try {
    const exportsExists = await exportsDirectoryClient.exists();
    if (!exportsExists) {
      context.log(`[deleteTaskWorkspaceDirectory] exports/ディレクトリが存在しません`);
      return;
    }

    const items = exportsDirectoryClient.listFilesAndDirectories();
    for await (const item of items) {
      if (item.kind === "file") {
        const fileClient = exportsDirectoryClient.getFileClient(item.name);
        await fileClient.deleteIfExists();
        context.log(`[deleteTaskWorkspaceDirectory] ファイル削除: exports/${item.name}`);
      }
    }
    await exportsDirectoryClient.deleteIfExists();
    context.log(`[deleteTaskWorkspaceDirectory] ディレクトリ削除: exports/`);

  } catch (error: any) {
    context.log(`[deleteTaskWorkspaceDirectory] exports/ディレクトリ削除中にエラー: ${error.message}`);
  }
}
