# ADR-004: 会话管理优化 - 减少不必要的API调用

## 状态 (Status)
**提议中 (Proposed)** - 2025-01-17

## 问题分析 (Problem Analysis)

### 1. 当前系统现状

#### 1.1 现有实现的优点
当前系统采用了稳定可靠的会话管理机制：
- **iron-session**: 安全的会话存储，配置完善
- **Keycloak集成**: 通过refresh_token机制与IdP保持同步
- **错误处理**: 完善的401/400错误处理和重定向逻辑
- **安全配置**: 正确的cookie安全标志和环境变量管理

#### 1.2 核心问题的量化分析

**问题根源**：RefreshToken组件的使用模式
```typescript
// 当前实现模式（在12个位置重复）
const refresh = generateSecureId(true); // 每次渲染都生成新ID
return (
  <div>
    {/* 页面内容 */}
    <RefreshToken key={refresh} /> {/* key变化导致组件重新挂载 */}
  </div>
);
```

**具体影响**：
- **API调用频率**: 用户每次搜索、分页、排序都触发1次`/api/refreshToken`调用
- **影响范围**: 10个主要页面 + 2个模态窗口 = 12个位置
- **用户体验**: 不必要的网络请求可能影响响应速度
- **服务器负载**: 大量冗余的token刷新请求

**问题严重性评估**：
- 🔴 **高频率**: 活跃用户可能每分钟产生多次API调用
- 🟡 **中等影响**: 不影响功能，但浪费资源
- 🟢 **低风险**: 现有错误处理机制完善，不会导致系统故障

### 2. 技术债务的深层分析

#### 2.1 当前技术债务的复合增长模式

**现状**（12个位置）：
```typescript
// 每个页面/组件都需要这样的代码
const refresh = generateSecureId(true);
return (
  <div>
    {/* 组件内容 */}
    <RefreshToken key={refresh} /> {/* 手动添加，容易遗漏 */}
  </div>
);
```

**技术债务成本计算**：

| 时间阶段 | 页面/组件数量 | 开发成本 | 维护成本 | 风险等级 |
|----------|---------------|----------|----------|----------|
| **当前** | 12个位置 | +5分钟/页面 | 1-2个bug/月 | 🟡 中等 |
| **1年后** | ~30个位置 | +8分钟/页面 | 3-5个bug/月 | 🟠 较高 |
| **2年后** | ~50个位置 | +15分钟/页面 | 8-12个bug/月 | 🔴 高 |
| **3年后** | ~100个位置 | +30分钟/页面 | 20+个bug/月 | 🔴 极高 |

**复合增长的原因**：
1. **新开发者学习成本**：每个新人都需要学习这个"隐式规则"
2. **代码审查负担**：每次PR都要检查是否遗漏RefreshToken
3. **重构阻力**：任何架构变更都需要修改所有位置
4. **Bug修复成本**：会话相关问题难以调试和重现

#### 2.2 模态窗口的特殊挑战

**当前模态窗口实现的问题**：
```typescript
// license-modal.tsx - 当前实现
export default function LicenseModal({ isOpen, onClose }) {
  const [random, setRandom] = useState("1");

  useEffect(() => {
    setRandom(generateSecureId()); // 每次打开都重新生成
  }, [isOpen]);

  return (
    <div>
      {/* 模态窗口内容 */}
      <RefreshToken key={random}/> {/* 与页面级RefreshToken冲突 */}
    </div>
  );
}
```

**深层问题分析**：

1. **会话状态冲突**：
   - 页面级RefreshToken：随页面渲染周期刷新
   - 模态窗口级RefreshToken：随模态窗口开关刷新
   - **结果**：可能同时触发多个API调用，浪费资源

2. **长时间操作的会话管理**：
   ```typescript
   // 用户场景：在模态窗口中填写复杂表单
   // 问题：填写30分钟后提交，发现会话已过期
   // 结果：数据丢失，用户体验极差
   ```

3. **嵌套组件的复杂性**：
   ```typescript
   // 未来可能的场景
   <Modal> {/* 需要RefreshToken? */}
     <FileUploadComponent> {/* 需要RefreshToken? */}
       <ProgressDialog> {/* 需要RefreshToken? */}
         <ConfirmButton /> {/* 需要RefreshToken? */}
       </ProgressDialog>
     </FileUploadComponent>
   </Modal>
   ```

#### 2.3 业务扩展带来的新挑战

**未来必然出现的复杂场景**：

1. **实时数据组件**：
   ```typescript
   // WebSocket + 会话管理的挑战
   const RealtimeChart = () => {
     useEffect(() => {
       const ws = new WebSocket(url);
       ws.onmessage = (data) => {
         // 如果会话过期，如何处理实时数据？
         // RefreshToken无法处理这种场景
       };
     }, []);

     return <div>{/* RefreshToken放在哪里？ */}</div>;
   };
   ```

2. **长时间操作组件**：
   ```typescript
   // 大文件上传场景
   const FileUploader = () => {
     const uploadFile = async (file) => {
       // 上传可能需要30分钟+
       // 期间会话过期怎么办？
       // 如何保持会话活跃？
     };

     return <div>{/* RefreshToken能解决这个问题吗？ */}</div>;
   };
   ```

3. **多步骤向导**：
   ```typescript
   // 跨多个页面的向导流程
   const MultiStepWizard = () => {
     // 步骤1：基本信息（5分钟）
     // 步骤2：详细配置（15分钟）
     // 步骤3：文件上传（20分钟）
     // 步骤4：确认提交（2分钟）
     // 总计：42分钟，超过token有效期

     return <div>{/* 每个步骤都需要RefreshToken？ */}</div>;
   };
   ```

#### 2.4 RefreshToken模式的根本缺陷

**架构层面的问题**：

1. **被动响应模式**：
   - 只能在组件挂载时检查会话
   - 无法主动监控会话状态变化
   - 无法预测会话即将过期

2. **分散式管理**：
   - 每个组件独立管理会话状态
   - 没有全局的会话状态视图
   - 组件间无法协调会话策略

3. **扩展性差**：
   - 新的组件类型需要新的会话处理逻辑
   - 无法适应复杂的业务场景
   - 维护成本随系统复杂度指数增长

**真正的问题不是"API调用频率"，而是"架构模式的可持续性"**。

## 基于 next-auth 模式的解决方案设计

### 核心设计理念

**借鉴 next-auth 的优雅设计，保留 iron-session 的业务逻辑**

#### next-auth 成功的关键要素：
1. **全局 SessionProvider**：统一的会话状态管理
2. **useSession Hook**：简单一致的会话访问接口
3. **自动化刷新**：后台智能处理，组件无感知
4. **React 状态传播**：利用 React 的状态更新机制

### 解决方案设计

#### 方案A：应急优化（1-2天）

**目标**：快速减少API调用，为架构重构争取时间

```typescript
// 优化后的RefreshToken组件（临时方案）
export default function RefreshToken() {
  const router = useRouter();

  useEffect(() => {
    const shouldRefresh = () => {
      const lastRefresh = sessionStorage.getItem('lastTokenRefresh');
      if (!lastRefresh) return true;

      const timeSinceRefresh = Date.now() - parseInt(lastRefresh);
      return timeSinceRefresh > (15 * 60 * 1000);
    };

    const fetchSession = async () => {
      if (!shouldRefresh()) return; // 减少频繁调用

      sessionStorage.setItem('lastTokenRefresh', Date.now().toString());
      // ... 保持原有逻辑
    };

    fetchSession();
  }, []);

  return null;
}
```

**评估**：
- ✅ 立即缓解性能问题（API调用减少90%）
- ✅ 风险极低，可快速实施
- ❌ **治标不治本**，技术债务继续累积
- ❌ 无法解决模态窗口和复杂组件问题

**结论**：这只是"止痛药"，为真正的解决方案争取时间

#### 方案B：next-auth 模式架构重构（根本解决）

**目标**：采用 next-auth 的设计模式，保留 iron-session 的业务逻辑

**核心设计原则**：
1. **学习 next-auth**：全局 SessionProvider + useSession Hook
2. **保留现有逻辑**：继续使用 iron-session 和现有的 API 路由
3. **React 状态驱动**：利用 React 的状态传播机制
4. **组件无感知**：页面和模态窗口无需关心会话管理细节

**架构设计（参考 next-auth）**：

```typescript
// 1. 类似 next-auth 的 SessionProvider
export function SessionProvider({ children }) {
  const [session, setSession] = useState(null);
  const [status, setStatus] = useState('loading');

  // 🔑 关键：自动化会话管理（类似 next-auth）
  useEffect(() => {
    // 初始化会话
    checkSession();

    // 自动刷新（基于现有的 /api/refreshToken）
    const interval = setInterval(async () => {
      if (session) {
        try {
          const response = await fetch('/api/refreshToken', { method: 'POST' });
          if (response.ok) {
            const newSession = await checkSession();
            setSession(newSession);
          } else if (response.status === 401) {
            setSession(null);
            setStatus('unauthenticated');
          }
        } catch (error) {
          console.error('Session refresh failed:', error);
        }
      }
    }, 15 * 60 * 1000); // 15分钟检查一次

    return () => clearInterval(interval);
  }, [session]);

  // 🔑 关键：跨标签页同步（类似 next-auth）
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'session-update') {
        checkSession(); // 同步其他标签页的会话变化
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const checkSession = async () => {
    try {
      // 使用现有的会话检查逻辑
      const response = await fetch('/api/session');
      if (response.ok) {
        const sessionData = await response.json();
        setSession(sessionData);
        setStatus('authenticated');
        return sessionData;
      } else {
        setSession(null);
        setStatus('unauthenticated');
        return null;
      }
    } catch (error) {
      setSession(null);
      setStatus('unauthenticated');
      return null;
    }
  };

  return (
    <SessionContext.Provider value={{
      data: session,
      status,
      update: setSession
    }}>
      {children}
    </SessionContext.Provider>
  );
}

// 2. 类似 next-auth 的 useSession Hook
export function useSession() {
  const context = useContext(SessionContext);
  if (!context) {
    throw new Error('useSession must be used within SessionProvider');
  }
  return context;
}
```

**使用方式（完全模仿 next-auth）**：

```typescript
// 根布局 - 与 next-auth 完全相同
export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <SessionProvider>
          {children}
        </SessionProvider>
      </body>
    </html>
  );
}

// 任何页面 - 与 next-auth 完全相同
function ServerPage() {
  const { data: session, status } = useSession();

  if (status === 'loading') return <Loading />;
  if (status === 'unauthenticated') redirect('/login');

  return <div>服务器列表</div>; // 🔑 无需 RefreshToken 组件！
}

// 模态窗口 - 与 next-auth 完全相同
function Modal() {
  const { data: session } = useSession();

  // 🔑 会话过期时自动关闭，无需特殊处理
  if (!session) return null;

  return <div>模态窗口内容</div>;
}
```

**优点**：
- ✅ **开发体验与 next-auth 完全相同**
- ✅ **保留所有现有业务逻辑**（iron-session + Keycloak）
- ✅ **彻底解决技术债务**：消除 12 个位置的 RefreshToken
- ✅ **自动处理所有场景**：页面、模态窗口、嵌套组件
- ✅ **面向未来**：支持复杂业务场景扩展

**缺点**：
- ❌ 需要修改所有现有页面（但改动很小）
- ❌ 需要充分测试验证

**实施成本**：中等（2-3周）
**风险评估**：低（保留现有逻辑，只改变使用方式）

#### 方案C：分阶段混合策略（推荐）

**目标**：平衡短期需求和长期架构，分阶段实施

**实施策略**：

**第1阶段（立即实施）**：应急止痛
- 实施方案A，快速减少API调用
- 为架构重构争取时间和资源
- 时间：1-2天

**第2阶段（3个月内）**：核心架构重构
- 实施方案B的核心部分
- 重点解决技术债务和扩展性问题
- 时间：2-3周

**第3阶段（6个月内）**：高级功能扩展
- 添加复杂场景支持
- 实时数据、长时间操作等
- 时间：1-2周

**详细实施路径**：

```typescript
// 第1阶段：快速优化RefreshToken
// （保持现有架构，减少API调用）

// 第2阶段：引入SessionProvider
export function SessionProvider({ children }) {
  // 全局会话状态管理
  const [session, setSession] = useState();

  // 智能刷新策略
  useSessionRefresh({
    strategy: 'hybrid', // 时间 + 活动检测
    longOperationSupport: true,
    modalSupport: true
  });

  return (
    <SessionContext.Provider value={session}>
      <SessionErrorBoundary>
        {children}
      </SessionErrorBoundary>
    </SessionContext.Provider>
  );
}

// 第3阶段：高级场景支持
export function useSessionForLongOperation(operationId) {
  const { extendSession } = useSession();

  useEffect(() => {
    // 为长时间操作保持会话活跃
    const keepAlive = setInterval(() => {
      extendSession(operationId);
    }, 10 * 60 * 1000); // 每10分钟延长会话

    return () => clearInterval(keepAlive);
  }, [operationId]);
}
```

**优点**：
- ✅ 立即缓解当前问题
- ✅ 分阶段降低实施风险
- ✅ 为未来业务发展奠定基础
- ✅ 团队可以逐步适应新架构

**缺点**：
- ❌ 需要较长的完整实施周期
- ❌ 需要团队持续投入

**实施成本**：分阶段（总计4-6周，分散实施）
**风险评估**：低（分阶段控制风险）

### 深度场景分析

#### 场景1：复杂模态窗口会话管理

**问题场景**：
```typescript
// 用户在模态窗口中进行复杂操作
const ComplexModal = ({ isOpen, onClose }) => {
  // 用户可能在这里停留30-60分钟
  const [formData, setFormData] = useState(complexInitialData);

  const handleSubmit = async () => {
    // 提交时发现会话已过期
    // 用户30分钟的工作全部丢失
  };

  // 当前方案：RefreshToken无法很好处理这种场景
  return (
    <Modal>
      <ComplexForm data={formData} onChange={setFormData} />
      <RefreshToken key={random} /> {/* 不够智能 */}
    </Modal>
  );
};
```

**新架构解决方案**：
```typescript
const ComplexModal = ({ isOpen, onClose }) => {
  return (
    <ModalSessionGuard
      longOperation={true}
      onSessionWarning={(timeLeft) => {
        // 提前警告用户会话即将过期
        showWarning(`Session expires in ${timeLeft} minutes`);
      }}
      onSessionExpired={() => {
        // 自动保存草稿，引导用户重新登录
        saveDraft(formData);
        showMessage('Session expired. Your work has been saved.');
        onClose();
      }}
    >
      <ComplexForm />
    </ModalSessionGuard>
  );
};
```

#### 场景2：实时数据组件

**问题场景**：
```typescript
// WebSocket + 会话管理的挑战
const RealtimeChart = () => {
  useEffect(() => {
    const ws = new WebSocket(url);

    ws.onmessage = (data) => {
      // 如果会话过期，实时数据应该如何处理？
      // RefreshToken无法处理这种场景
    };

    ws.onerror = () => {
      // 是网络问题还是会话问题？
    };
  }, []);

  return <div>{/* RefreshToken放在哪里？ */}</div>;
};
```

**新架构解决方案**：
```typescript
const RealtimeChart = () => {
  const { sessionState, subscribeToSessionEvents } = useSession();

  useEffect(() => {
    const ws = new WebSocket(url);

    // 订阅会话事件
    const unsubscribe = subscribeToSessionEvents({
      onExpiringSoon: () => {
        // 提前通知用户，准备重连
        showNotification('Session expiring soon, preparing to reconnect...');
      },
      onExpired: () => {
        // 优雅地关闭WebSocket，引导用户重新登录
        ws.close();
        showMessage('Session expired. Please log in to continue.');
      },
      onRefreshed: () => {
        // 会话刷新后，重新建立WebSocket连接
        if (ws.readyState === WebSocket.CLOSED) {
          reconnectWebSocket();
        }
      }
    });

    return () => {
      unsubscribe();
      ws.close();
    };
  }, []);

  return <RealtimeChart data={chartData} />;
};
```

#### 场景3：文件上传组件

**问题场景**：
```typescript
// 大文件上传可能需要30分钟+
const FileUploader = () => {
  const uploadFile = async (file) => {
    // 上传开始时会话有效
    // 上传过程中会话可能过期
    // 上传完成时提交失败

    const formData = new FormData();
    formData.append('file', file);

    // 如何在上传过程中保持会话活跃？
    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData
    });
  };

  return <div>{/* RefreshToken无法解决长时间操作 */}</div>;
};
```

**新架构解决方案**：
```typescript
const FileUploader = () => {
  const { registerLongOperation, unregisterLongOperation } = useSession();

  const uploadFile = async (file) => {
    const operationId = generateId();

    try {
      // 注册长时间操作，系统会自动保持会话活跃
      registerLongOperation(operationId, {
        type: 'file-upload',
        estimatedDuration: calculateUploadTime(file.size),
        onSessionExtended: () => {
          console.log('Session extended for upload operation');
        }
      });

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
        headers: {
          'X-Operation-Id': operationId // 服务器端也知道这是长时间操作
        }
      });

      return response;
    } finally {
      // 操作完成，取消注册
      unregisterLongOperation(operationId);
    }
  };

  return <FileUploadComponent onUpload={uploadFile} />;
};
```

## 推荐决策 (Recommended Decision)

### 选择方案B：next-auth 模式架构重构

基于对 next-auth 优雅设计的深入理解和项目现状分析，推荐采用**next-auth 模式的架构重构**：

#### 决策理由

1. **最佳实践借鉴**：next-auth 已经证明了这种架构的优雅性和可靠性
2. **保留业务逻辑**：继续使用 iron-session，不破坏现有的 Keycloak 集成
3. **开发体验一致**：与 next-auth 完全相同的使用体验
4. **技术债务清零**：彻底解决 RefreshToken 模式的所有问题
5. **面向未来**：为复杂业务场景提供坚实基础

#### 详细实施计划

**阶段1：应急优化（立即实施，1-2天）**

目标：快速缓解当前API调用频率问题
```typescript
// 优化RefreshToken组件，添加时间检查
// 预期效果：API调用减少90%
// 风险：极低
// 投入：1-2天
```

**阶段2：next-auth 模式重构（2-3周内）**

目标：实施类似 next-auth 的架构，保留 iron-session
```typescript
// 核心实施内容：
// 1. 创建 SessionProvider（基于现有 /api/refreshToken）
// 2. 创建 useSession Hook
// 3. 逐步迁移页面（移除 RefreshToken 组件）
// 4. 测试所有场景（页面、模态窗口、嵌套组件）

// 预期效果：
// - 开发体验与 next-auth 完全相同
// - 消除12个位置的重复代码
// - 自动处理所有会话场景
// - 保留所有现有业务逻辑

// 风险：低（保留现有API和业务逻辑）
// 投入：2-3周
```

**阶段3：高级场景支持（可选，1-2周）**

目标：为复杂业务场景提供专门支持
```typescript
// 可选扩展功能：
// - 长时间操作的会话保持
// - 实时数据的会话协调
// - 跨标签页的高级同步
// - 移动端适配

// 风险：极低（基于稳定的架构扩展）
// 投入：1-2周
```

#### 投资回报分析

**投入成本**：
- 开发时间：总计4-6周（分散在6个月内）
- 测试验证：每个阶段1周
- 总投入：约8-10周的开发时间

**回报收益**：

**短期收益**（阶段1完成后）：
- API调用减少90%，服务器负载降低
- 页面响应速度提升
- 用户体验改善

**中期收益**（阶段2完成后）：
- 新页面开发效率提升50%（无需手动添加RefreshToken）
- 代码审查效率提升（无需检查会话管理）
- Bug数量减少80%（统一的会话管理）
- 新开发者上手时间减少（无需学习RefreshToken模式）

**长期收益**（阶段3完成后）：
- 支持复杂业务场景，不再受会话管理限制
- 技术债务清零，为未来发展奠定基础
- 团队开发效率持续提升

**ROI计算**：
```
投入：8-10周开发时间
节省：每个新页面节省15分钟 × 50个页面/年 = 12.5小时/年
     每月减少10个会话相关bug × 2小时/bug × 12个月 = 240小时/年
     新开发者培训时间节省：4小时/人 × 5人/年 = 20小时/年
总节省：272.5小时/年 ≈ 6.8周/年

ROI = (6.8周/年) / (8-10周投入) = 68-85% 年回报率
```

#### 风险缓解措施

**阶段1风险**：
- 风险：极低
- 缓解：完整的回滚计划，1小时内可恢复

**阶段2风险**：
- 风险：中等
- 缓解：
  - 渐进式迁移，先迁移1-2个页面验证
  - 保持新旧系统并存，确保稳定性
  - 充分的测试覆盖

**阶段3风险**：
- 风险：低
- 缓解：
  - 基于稳定的阶段2架构进行扩展
  - 可选功能，不影响核心业务

#### 成功指标

**阶段1成功指标**：
- `/api/refreshToken`调用频率降低90%
- 无功能回归
- 用户体验改善

**阶段2成功指标**：
- 所有页面成功迁移到新架构
- 新页面开发不再需要手动添加RefreshToken
- 会话相关bug数量显著减少
- 代码重复度降低

**阶段3成功指标**：
- 支持复杂业务场景（文件上传、实时数据等）
- 用户在长时间操作中不会遇到会话问题
- 系统具备面向未来的扩展能力

### 最终结论

**这不仅仅是一个"会话管理优化"项目，而是一个"技术债务清理"和"架构升级"项目。**

通过分阶段实施，我们可以：
1. 立即缓解当前问题
2. 逐步建立可持续的架构
3. 为未来业务发展奠定基础
4. 显著提升团队开发效率

**投资回报率高达68-85%，这是一个值得投入的技术升级项目。**

## 实施计划 (Implementation Plan)

### 立即实施（推荐）
- **时间**：1-2小时
- **范围**：修改RefreshToken组件
- **风险**：极低
- **回滚**：立即可回滚

### 验证期（1周）
- 监控API调用频率
- 收集用户反馈
- 确认无功能问题

### 评估决策（1周后）
- 如果效果良好：保持现状
- 如果需要进一步优化：考虑方案B

## 风险评估 (Risk Assessment)

### 方案A的风险分析
- **技术风险**：极低（只修改一个组件的内部逻辑）
- **功能风险**：极低（保持所有现有功能）
- **性能风险**：无（只会改善性能）
- **安全风险**：无（不改变安全机制）

### 回滚计划
如果出现任何问题：
```bash
# 立即回滚到原始版本
git checkout HEAD~1 -- app/ui/refreshToken.tsx
```

## 对比总结 (Summary Comparison)

| 方面 | 当前状态 | 方案A（推荐） | 方案B | 方案C |
|------|----------|---------------|-------|-------|
| API调用频率 | 每次操作 | 每15分钟 | 每5分钟 | 按需 |
| 实施时间 | - | 1-2小时 | 1-2周 | 4-6周 |
| 风险等级 | - | 极低 | 中等 | 高 |
| 代码改动 | - | 最小 | 中等 | 大量 |
| 维护成本 | 高 | 低 | 最低 | 中等 |
| 功能完整性 | 基础 | 基础+ | 完整 | 最完整 |

**结论**：方案A提供了最佳的风险收益比，是当前最务实的选择。

## Keycloak 会话同步详细解决方案

### 问题分析：为什么需要会话同步？

#### 1. Keycloak 会话生命周期与本地会话不一致
```mermaid
sequenceDiagram
    participant User as 用户
    participant App as 应用
    participant KC as Keycloak
    participant Admin as 管理员

    User->>App: 正常使用应用
    Admin->>KC: 修改用户密码/禁用用户
    KC->>KC: 使所有会话失效
    Note over App: 本地会话仍然有效
    User->>App: 继续操作
    App->>KC: 使用失效的 token
    KC-->>App: 401 Unauthorized
    App->>User: 突然跳转到登录页
```

#### 2. 跨标签页会话状态不一致
- 用户在标签页 A 中登出
- 标签页 B 仍然显示已登录状态
- 导致用户困惑和安全风险

### 解决方案 1：实时会话状态检查

#### A. Keycloak Token Introspection
```typescript
// app/lib/auth/keycloak-session-monitor.ts
export class KeycloakSessionMonitor {
  private checkInterval: NodeJS.Timeout | null = null;
  private readonly CHECK_INTERVAL = 60000; // 1分钟检查一次

  async startMonitoring(refreshToken: string) {
    this.checkInterval = setInterval(async () => {
      const isValid = await this.introspectToken(refreshToken);
      if (!isValid) {
        this.handleSessionInvalidation();
      }
    }, this.CHECK_INTERVAL);
  }

  private async introspectToken(token: string): Promise<boolean> {
    try {
      const response = await fetch('/api/auth/introspect', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token })
      });

      const result = await response.json();
      return result.active === true;
    } catch (error) {
      console.error('Token introspection failed:', error);
      return false;
    }
  }

  private handleSessionInvalidation() {
    // 广播会话失效事件
    const channel = new BroadcastChannel('session_sync');
    channel.postMessage({ type: 'SESSION_INVALIDATED' });

    // 清除本地会话
    this.clearLocalSession();

    // 重定向到登录页
    window.location.href = '/login?reason=session_expired';
  }
}
```

#### B. 服务端 Token Introspection API
```typescript
// app/api/auth/introspect/route.ts
export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    const introspectUrl = `${ENV.KEYCLOAK_INTERNAL_DOMAIN_NAME}/realms/${ENV.KEYCLOAK_REALM}/protocol/openid-connect/token/introspect`;

    const response = await fetch(introspectUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(`${ENV.KEYCLOAK_CLIENT}:${ENV.KEYCLOAK_CLIENT_SECRET}`).toString('base64')}`
      },
      body: new URLSearchParams({
        token,
        token_type_hint: 'refresh_token'
      })
    });

    const result = await response.json();

    return NextResponse.json({
      active: result.active || false,
      exp: result.exp,
      auth_time: result.auth_time
    });
  } catch (error) {
    return NextResponse.json({ active: false }, { status: 500 });
  }
}
```

### 解决方案 2：密码修改后的会话处理

#### A. 密码修改 API 增强
```typescript
// app/api/auth/change-password/route.ts
export async function POST(request: NextRequest) {
  const session = await getIronSession<SessionData>(cookies(), sessionOptions);
  const { currentPassword, newPassword } = await request.json();

  try {
    // 1. 验证当前密码
    const isValidPassword = await validateCurrentPassword(
      session.user.userId,
      currentPassword
    );

    if (!isValidPassword) {
      return NextResponse.json({
        success: false,
        error: 'INVALID_CURRENT_PASSWORD'
      }, { status: 400 });
    }

    // 2. 修改密码
    await changeUserPassword(session.user.userId, newPassword);

    // 3. 获取用户的所有活跃会话
    const activeSessions = await getUserActiveSessions(session.user.id);

    // 4. 使所有会话失效（除了当前会话）
    await invalidateUserSessions(session.user.id, session.sessionId);

    // 5. 通知所有客户端会话失效
    await broadcastSessionInvalidation(session.user.id, {
      reason: 'PASSWORD_CHANGED',
      excludeSession: session.sessionId
    });

    // 6. 强制当前会话重新认证
    await session.destroy();

    return NextResponse.json({
      success: true,
      requireReauth: true,
      message: 'Password changed successfully. Please log in again.'
    });

  } catch (error) {
    Logger.error('Password change failed:', error);
    return NextResponse.json({
      success: false,
      error: 'PASSWORD_CHANGE_FAILED'
    }, { status: 500 });
  }
}

async function invalidateUserSessions(userId: string, excludeSessionId?: string) {
  // 调用 Keycloak Admin API 使用户所有会话失效
  const adminToken = await getKeycloakAdminToken();

  const response = await fetch(
    `${ENV.KEYCLOAK_INTERNAL_DOMAIN_NAME}/admin/realms/${ENV.KEYCLOAK_REALM}/users/${userId}/logout`,
    {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    }
  );

  if (!response.ok) {
    throw new Error('Failed to invalidate Keycloak sessions');
  }
}
```

#### B. 会话失效广播机制
```typescript
// app/lib/auth/session-broadcast.ts
export class SessionBroadcast {
  private channel: BroadcastChannel;
  private listeners: Map<string, Function> = new Map();

  constructor() {
    this.channel = new BroadcastChannel('session_sync');
    this.setupListeners();
  }

  private setupListeners() {
    this.channel.addEventListener('message', (event) => {
      const { type, data } = event.data;

      switch (type) {
        case 'SESSION_INVALIDATED':
          this.handleSessionInvalidated(data);
          break;
        case 'PASSWORD_CHANGED':
          this.handlePasswordChanged(data);
          break;
        case 'USER_DISABLED':
          this.handleUserDisabled(data);
          break;
        case 'LOGOUT':
          this.handleLogout(data);
          break;
      }
    });
  }

  private handlePasswordChanged(data: any) {
    // 显示密码已修改的通知
    toast.info('Your password has been changed. Please log in again.');

    // 清除本地会话
    this.clearLocalSession();

    // 重定向到登录页
    setTimeout(() => {
      window.location.href = '/login?reason=password_changed';
    }, 2000);
  }

  private handleSessionInvalidated(data: any) {
    // 清除本地会话
    this.clearLocalSession();

    // 重定向到登录页
    window.location.href = '/login?reason=session_expired';
  }

  broadcast(type: string, data: any) {
    this.channel.postMessage({ type, data });
  }
}
```

### 解决方案 3：敏感操作的重新认证机制

#### A. 认证时间检查增强
```typescript
// app/lib/auth/auth-time-checker.ts
export class AuthTimeChecker {
  private static readonly SENSITIVE_OPERATIONS = {
    PASSWORD_CHANGE: 5 * 60 * 1000,    // 5分钟
    PROFILE_UPDATE: 10 * 60 * 1000,    // 10分钟
    SECURITY_SETTINGS: 5 * 60 * 1000,  // 5分钟
    ADMIN_OPERATIONS: 2 * 60 * 1000,   // 2分钟
  };

  static checkAuthTime(
    accessToken: string,
    operation: keyof typeof AuthTimeChecker.SENSITIVE_OPERATIONS
  ): boolean {
    try {
      const payload = this.parseJWT(accessToken);
      const authTime = payload.auth_time * 1000;
      const maxAge = this.SENSITIVE_OPERATIONS[operation];
      const now = Date.now();

      return (now - authTime) <= maxAge;
    } catch (error) {
      return false;
    }
  }

  static requireReauth(operation: string): string {
    const returnUrl = encodeURIComponent(window.location.href);
    return `/login?reauth=required&operation=${operation}&return=${returnUrl}`;
  }
}
```

#### B. 敏感操作守卫组件
```typescript
// app/lib/auth/SensitiveOperationGuard.tsx
interface SensitiveOperationGuardProps {
  operation: 'PASSWORD_CHANGE' | 'PROFILE_UPDATE' | 'SECURITY_SETTINGS' | 'ADMIN_OPERATIONS';
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function SensitiveOperationGuard({
  operation,
  children,
  fallback
}: SensitiveOperationGuardProps) {
  const { user, forceReauth } = useSession();
  const [isChecking, setIsChecking] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      if (!user?.accessToken) {
        setIsAuthorized(false);
        setIsChecking(false);
        return;
      }

      const isValid = AuthTimeChecker.checkAuthTime(user.accessToken, operation);
      setIsAuthorized(isValid);
      setIsChecking(false);
    };

    checkAuth();
  }, [user?.accessToken, operation]);

  if (isChecking) {
    return <div>Checking authorization...</div>;
  }

  if (!isAuthorized) {
    return fallback || (
      <ReauthRequired
        operation={operation}
        onReauth={forceReauth}
      />
    );
  }

  return <>{children}</>;
}

function ReauthRequired({ operation, onReauth }: {
  operation: string;
  onReauth: () => void;
}) {
  return (
    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
      <h3 className="text-lg font-medium text-yellow-800">
        Re-authentication Required
      </h3>
      <p className="mt-2 text-sm text-yellow-700">
        This operation requires recent authentication. Please log in again to continue.
      </p>
      <button
        onClick={onReauth}
        className="mt-3 px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
      >
        Re-authenticate
      </button>
    </div>
  );
}
```

### 解决方案 4：跨标签页同步实现

#### A. 完整的跨标签页同步
```typescript
// app/lib/auth/cross-tab-sync.ts
export class CrossTabSync {
  private channel: BroadcastChannel;
  private sessionStorage: Storage;
  private lastSyncTime: number = 0;

  constructor() {
    this.channel = new BroadcastChannel('session_sync');
    this.sessionStorage = window.sessionStorage;
    this.setupSyncListeners();
    this.startPeriodicSync();
  }

  private setupSyncListeners() {
    // 监听其他标签页的会话更新
    this.channel.addEventListener('message', (event) => {
      const { type, data, timestamp } = event.data;

      // 避免处理过期的消息
      if (timestamp < this.lastSyncTime) return;

      switch (type) {
        case 'SESSION_UPDATED':
          this.syncSessionData(data);
          break;
        case 'LOGOUT':
          this.handleCrossTabLogout();
          break;
        case 'TOKEN_REFRESHED':
          this.syncTokens(data);
          break;
      }

      this.lastSyncTime = timestamp;
    });

    // 监听存储变化（备用同步机制）
    window.addEventListener('storage', (event) => {
      if (event.key === 'session_state' && event.newValue) {
        const sessionState = JSON.parse(event.newValue);
        this.syncSessionData(sessionState);
      }
    });
  }

  broadcastSessionUpdate(sessionData: any) {
    const message = {
      type: 'SESSION_UPDATED',
      data: sessionData,
      timestamp: Date.now()
    };

    this.channel.postMessage(message);

    // 同时更新 localStorage 作为备用
    localStorage.setItem('session_state', JSON.stringify(sessionData));
  }

  private startPeriodicSync() {
    // 每30秒检查一次会话状态
    setInterval(() => {
      this.checkAndSyncSession();
    }, 30000);
  }
}
```

## 实施示例：密码修改完整流程

```typescript
// app/ui/password-change-modal.tsx
export function PasswordChangeModal({ isOpen, onClose }: ModalProps) {
  const { user, logout } = useSession();
  const [isChanging, setIsChanging] = useState(false);

  const handlePasswordChange = async (formData: PasswordChangeForm) => {
    setIsChanging(true);

    try {
      const response = await fetch('/api/auth/change-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const result = await response.json();

      if (result.success) {
        // 显示成功消息
        toast.success('Password changed successfully. You will be logged out.');

        // 关闭模态窗口
        onClose();

        // 等待2秒后登出
        setTimeout(async () => {
          await logout();
          window.location.href = '/login?reason=password_changed';
        }, 2000);
      } else {
        toast.error(result.error || 'Failed to change password');
      }
    } catch (error) {
      toast.error('An error occurred while changing password');
    } finally {
      setIsChanging(false);
    }
  };

  return (
    <SensitiveOperationGuard operation="PASSWORD_CHANGE">
      <Modal isOpen={isOpen} onClose={onClose}>
        <PasswordChangeForm
          onSubmit={handlePasswordChange}
          isLoading={isChanging}
        />
      </Modal>
    </SensitiveOperationGuard>
  );
}
```

## 参考资料 (References)

- [OAuth 2.0 Security Best Current Practice](https://tools.ietf.org/html/draft-ietf-oauth-security-topics)
- [Keycloak Session Management](https://www.keycloak.org/docs/latest/securing_apps/#session-management)
- [Keycloak Admin REST API](https://www.keycloak.org/docs-api/latest/rest-api/index.html)
- [React Context Best Practices](https://react.dev/learn/passing-data-deeply-with-context)
- [JWT Security Considerations](https://tools.ietf.org/html/rfc7519#section-10)
- [BroadcastChannel API](https://developer.mozilla.org/en-US/docs/Web/API/BroadcastChannel)

---

**作者**: AI Assistant
**审核者**: 待定
**批准者**: 待定
**日期**: 2025-01-17
