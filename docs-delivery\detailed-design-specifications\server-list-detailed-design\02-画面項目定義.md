## 画面項目定義

### ヘッダ部情報

| # | 項目名 | 種別 | 桁数/バイト数 | 必須 | 文字種 | ソート順 | 論理DBからの入力<br/>論理テーブル名 | 初期表示 | フォーマット/備考 |
|:---|:---|:---|:---|:---:|:---:|:---:|:---|:---|:---|
| 1 | サーバ一覧 | h1 | - | - | - | - | - | 「管理>サーバ一覧」 | - |
| 2 | 更新 | button | - | - | - | - | - | 活性 | - |
| 3 | フィルターの入力 | input | - | - | - | - | - | ブランク | 最大は全角100文字を入力できる |
| 4 | フィルターの検索 | button | - | - | - | - | - | 活性 | - |
| 5 | フィルターのクリア | button | - | - | - | - | - | 活性 | - |
| 6 | 改ページのエリア | div | - | - | - | - | - | - | - |
| 7 | 前のページ | button | - | - | - | - | - | 活性 | - |
| 8 | 次のページ | button | - | - | - | - | - | 活性 | - |
| 9 | 指定のページ | button | - | - | - | - | - | 活性 | - |
| 10 | 行数/ページ: | label | - | - | - | - | - | 「行数/ページ:」 | - |
| 11 | 行数/ページの選択項目 | dropdown menu | - | - | - | - | - | 活性 | 選択肢は「10」「30」「50」固定 |

### ボディ部情報

| # | 項目名 | 種別 | 桁数/バイト数 | 必須 | 文字種 | ソート順 | 論理DBからの入力<br/>論理テーブル名 | 初期表示 | フォーマット/備考 |
|:---|:---|:---|:---|:---:|:---:|:---:|:---|:---|:---|
| 1 | サーバ一覧エリア | table | - | - | - | - | - | - | - |
| 2 | サーバ名 | label | - | - | - | - | - | サーバ名 | - |
| 3 | サーバ名ソート | botton | - | - | - | - | - | 表示 | ディフォルトソート：ソートアイコンを昇順のアイコンにする。<br/>「サーバ」テーブルの「サーバ名」を昇順・降順にソートする。<br/>※辞書順(A→Z,0→9の順番) |
| 4 | サーバ名 | label | - | - | - | - | サーバ<br/>サーバ名 | DBに従う | そのまま表示 |
| 5 | 種別 | label | - | - | - | - | - | 種別 | - |
| 6 | 種別ソート | botton | - | - | - | - | - | 非表示 | 「サーバ」テーブルの「種別」を昇順・降順にソートする。<br/>※辞書順(A→Z,0→9の順番) |
| 7 | 種別 | label | - | - | - | - | サーバ<br/>種別 | DBに従う | 種別を下記のキーとして、値を画面に表示する。<br/>GENERAL_MANAGER：JP1/ITDM2(統括マネージャ)<br/>RELAY_MANAGER：JP1/ITDM2(中継マネージャ)<br/>HIBUN_CONSOLE：秘文(管理コンソール) |
| 8 | 管理画面 | label | - | - | - | - | - | 管理画面 | - |
| 9 | 管理画面ソート | botton | - | - | - | - | - | 非表示 | 「サーバ」テーブルの「リンク」を昇順・降順にソートする。<br/>※辞書順(A→Z,0→9の順番) |
| 10 | 管理画面 | link | - | - | - | - | サーバ<br/>リンク | DBに従う | 「フォーマット」<br/>そのまま表示<br/>「ハイパーリンク」<br/>リンクをハイパーリンクとして表示する |
| 11 | スクロールバー | scroll | - | - | - | - | - | 表示条件に従う | サーバ一覧の表示範囲内、該当ページのサーバリストを表示でき場合、非表示とする。その以外、表示とする。 |
| 12 | タスク | label | - | - | - | - | - | タスク | - |
| 13 | タスクを選択 | dropdown menu | - | - | - | - | ドロップダウンメニュー | DBに従う | 種別が「JP1/ITDM2(統括マネージャ)」/「JP1/ITDM2(中継マネージャ)」の場合だけ表示する。 |
| 14 | 操作ログのエクスポート | sub-menu | - | - | - | - | サブメニュー | DBに従う | 種別が「JP1/ITDM2(統括マネージャ)」/「JP1/ITDM2(中継マネージャ)」の場合だけ表示する。<br/>契約(License)テーブルの「基本契約プランコード(BasicPlan)」列の値はSTANDARD / LIGHT_A / LIGHT_Bのいずれかに設定される。ユーザーの契約の基本契約プランコードがSTANDARDかLIGHT_Bの場合は[操作ログのエクスポート]メニューを表示する。LIGHT_Aの場合は[操作ログのエクスポート]メニューを表示しない。 |
| 15 | 管理項目定義のインポート | sub-menu | - | - | - | - | サブメニュー | DBに従う | 種別が「JP1/ITDM2(統括マネージャ)」/「JP1/ITDM2(中継マネージャ)」の場合だけ表示する。 |
| 16 | 管理項目定義のエクスポート | sub-menu | - | - | - | - | サブメニュー | DBに従う | 種別が「JP1/ITDM2(統括マネージャ)」/「JP1/ITDM2(中継マネージャ)」の場合だけ表示する。 |

### 管理項目定義のインポート

| # | 項目名 | 種別 | 桁数/バイト数 | 必須 | 文字種 | ソート順 | 論理DBからの入力<br/>論理テーブル名 | 初期表示 | フォーマット/備考 |
|:---|:---|:---|:---|:---:|:---:|:---:|:---|:---|:---|
| 1 | 管理項目定義のインポート | h1 | - | - | - | - | - | 「管理項目定義のインポート」 | - |
| 2 | × | button | - | - | - | - | - | 活性 | 管理項目定義のインポートのポップアップ画面を閉じる |
| 3 | 画面記述 | label | - | - | - | - | - | 「管理項目定義のCSVファイルを指定してください。」 | - |
| 4 | ファイルラベル | label | - | - | - | - | - | 「ファイル:」 | - |
| 5 | ファイルの入力ボックス | input | | | | | | 「ファイルを選択してください」 | 編集不可。左クリックで表示するファイル選択ダイアログ（CSVファイルだけが選択できる）でファイルを選択して、選択したファイルのパスを表示する。ファイル選択ダイアログの初期パスはデスクトップとなる。<br/>未入力時の背景のヒントメッセージ：「ファイルを選択してください」 |
| 6 | 必須ラベル | label | - | - | - | - | - | 「*」 | 必須入力項目の前に表示。赤文字。 |
| 7 | エラーメッセージ | label | - | - | - | - | - | 非表示 | - |
| 8 | ヒント文字 | label | - | - | - | - | - | 「* 必須入力」 | - |
| 9 | OK | button | - | - | - | - | - | 活性 | 管理項目定義のインポートのポップアップ画面を閉じて、管理項目定義のインポートの確認画面を表示する |
| 10 | キャンセル | button | - | - | - | - | - | 活性 | 管理項目定義のインポートのポップアップ画面を閉じる |

### 操作ログのエクスポート

| # | 項目名 | 種別 | 桁数/バイト数 | 必須 | 文字種 | ソート順 | 論理DBからの入力<br/>論理テーブル名 | 初期表示 | フォーマット/備考 |
|:---|:---|:---|:---|:---:|:---:|:---:|:---|:---|:---|
| 1 | 操作ログのエクスポート | h1 | - | - | - | - | - | 「操作ログのエクスポート」 | - |
| 2 | × | button | - | - | - | - | - | 活性 | 操作ログのエクスポートのポップアップ画面を閉じる |
| 3 | 画面記述 | label | - | - | - | - | - | 「エクスポートする期間を{0}日間以内で指定してください。」 | {0}：指定可能な最大日数。値の一覧（Lov）テーブルのOPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPANから取得。 |
| 4 | 開始日ラベル | label | - | - | - | - | - | 「開始日:」 | - |
| 5 | 終了日ラベル | label | - | - | - | - | - | 「終了日:」 | - |
| 6 | 開始日の入力ボックス | input | - | - | - | - | - | 「YYYY / MM / DD」 | 編集不可。左クリックで表示するカレンダーメニューで開始日を選択する。カレンダーメニューの初期値はクリックした日の日付となる。<br/>未入力時の背景のヒントメッセージ：「YYYY / MM / DD」<br/>選択された日付はYYYY / MM / DDフォーマットで表示される。 |
| 7 | 終了日の入力ボックス | input | - | - | - | - | - | 「YYYY / MM / DD」 | 編集不可。左クリックで表示するカレンダーメニューで終了日を選択する。カレンダーメニューの初期値はクリックした日の日付となる。<br/>未入力時の背景のヒントメッセージ：「YYYY / MM / DD」<br/>選択された日付はYYYY / MM / DDフォーマットで表示される。 |
| 8 | 必須ラベル | label | - | - | - | - | - | 「*」 | 必須入力項目の前に表示。赤文字。 |
| 9 | エラーメッセージ | label | - | - | - | - | - | 非表示 | - |
| 10 | ヒント文字 | label | - | - | - | - | - | 「* 必須入力」 | - |
| 11 | OK | button | - | - | - | - | - | 活性 | 操作ログのエクスポートのポップアップ画面を閉じて、操作ログのエクスポートの確認画面を表示する |
| 12 | キャンセル | button | - | - | - | - | - | 活性 | 操作ログのエクスポートのポップアップ画面を閉じる |

### 管理項目定義のエクスポート【確認画面】

| # | 項目名 | 種別 | 桁数/バイト数 | 必須 | 文字種 | ソート順 | 論理DBからの入力<br/>論理テーブル名 | 初期表示 | フォーマット/備考 |
|:---|:---|:---|:---|:---:|:---:|:---:|:---|:---|:---|
| 1 | 管理項目定義のエクスポート | h1 | - | - | - | - | - | 「管理項目定義のエクスポート」 | - |
| 2 | × | button | - | - | - | - | - | 活性 | 管理項目定義のエクスポートタスクの実行メッセージを送信しないで、確認画面を閉じる |
| 3 | 画面記述 | label | - | - | - | - | - | 「{0}の管理項目定義をエクスポートします。<br/>よろしいですか？」 | {0}：対象サーバ名 |
| 4 | OK | button | - | - | - | - | - | 活性 | 管理項目定義のエクスポートタスクの実行メッセージを送信して、確認画面を閉じる |
| 5 | キャンセル | button | - | - | - | - | - | 活性 | 管理項目定義のエクスポートタスクの実行メッセージを送信しないで、確認画面を閉じる |

### 操作ログのエクスポート【確認画面】

| # | 項目名 | 種別 | 桁数/バイト数 | 必須 | 文字種 | ソート順 | 論理DBからの入力<br/>論理テーブル名 | 初期表示 | フォーマット/備考 |
|:---|:---|:---|:---|:---:|:---:|:---:|:---|:---|:---|
| 1 | 操作ログのエクスポート | h1 | - | - | - | - | - | 「操作ログのエクスポート」 | - |
| 2 | × | button | - | - | - | - | - | 活性 | 操作ログのエクスポートタスクの実行メッセージを送信しないで、確認画面を閉じて、入力された値はそのままで前の操作ログのエクスポート画面を表示する |
| 3 | 画面記述 | label | - | - | - | - | - | 「{0}の操作ログをエクスポートします。<br/>よろしいですか？」 | {0}：対象サーバ名 |
| 4 | OK | button | - | - | - | - | - | 活性 | 操作ログのエクスポートタスクの実行メッセージを送信して、確認画面を閉じる |
| 5 | キャンセル | button | - | - | - | - | - | 活性 | 操作ログのエクスポートタスクの実行メッセージを送信しないで、確認画面を閉じて、入力された値はそのままで前の操作ログのエクスポート画面を表示する |

### 管理項目定義のインポート【確認画面】

| # | 項目名 | 種別 | 桁数/バイト数 | 必須 | 文字種 | ソート順 | 論理DBからの入力<br/>論理テーブル名 | 初期表示 | フォーマット/備考 |
|:---|:---|:---|:---|:---:|:---:|:---:|:---|:---|:---|
| 1 | 管理項目定義のインポート | h1 | - | - | - | - | - | 「管理項目定義のインポート」 | - |
| 2 | × | button | - | - | - | - | - | 活性 | 管理項目定義のインポートタスクの実行メッセージを送信しないで、確認画面を閉じて、入力された値はそのままで前の管理項目定義のインポート画面を表示する |
| 3 | 画面記述 | label | - | - | - | - | - | 「{0}の管理項目定義をインポートします。<br/>よろしいですか？」 | {0}：対象サーバ名 |
| 4 | OK | button | - | - | - | - | - | 活性 | 管理項目定義のインポートタスクの実行メッセージを送信して、確認画面を閉じる |
| 5 | キャンセル | button | - | - | - | - | - | 活性 | 管理項目定義のインポートタスクの実行メッセージを送信しないで、確認画面を閉じて、入力された値はそのままで前の管理項目定義のインポート画面を表示する |