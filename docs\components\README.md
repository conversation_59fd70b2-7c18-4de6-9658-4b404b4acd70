# 功能模块与核心组件列表

本文档列出了 JCS 端点资产与任务管理系统的所有主要面向用户的功能模块以及核心的后端服务/逻辑组件。每个条目都有其对应的详细设计文档。

## 功能列表 (面向用户)

| # | 功能名称 | 功能概述 | 详细文档 |
|---|----------|----------|----------|
| 1 | 登录 | 用户登录到门户系统，开始使用服务。 | [登录](./01-login.md) |
| 2 | 主界面 | 显示导航栏、侧边栏和面包屑导航。 | [主界面](./02-main-screen.md) |
| 3 | 服务器列表 | 显示资产分发管理信息的超链接。支持个别导入/导出各服务器的管理项目定义，以及按指定期间导出操作日志。 | [服务器列表](./03-servers/server-list.md) |
| 4 | 通知信息显示 | 显示系统管理员发送给用户的消息。 | [通知信息](./04-notification.md) |
| 5 | 登出 | 从门户系统登出，结束服务使用，返回登录等待状态。 | [登出](./05-logout.md) |
| 6 | 密码修改 | 用户修改自己的密码。 | [密码修改](./06-password-change.md) |
| 7 | 许可证信息 | 显示用户所签约的许可证信息。 | [许可证信息](./07-license-info.md) |
| 8 | 操作日志列表 | 用户查看本服务操作日志功能中记录的操作日志。 | [操作日志列表](./08-operation-log-list.md) |
| 9 | 产品媒体列表 | 用户查看可用的产品媒体。 | [产品媒体列表](./09-product-media-list.md) |
| 10 | 手册列表 | 用户查看可用的手册。 | [手册列表](./10-manual-list.md) |
| 11 | 提供文件列表 | 用户查看提供的文件。 | [提供文件列表](./11-provided-files-list.md) |
| 12 | 支持信息列表 | 用户查看支持信息。 | [支持信息列表](./12-support-info-list.md) |
| 13 | 任务列表 | 用户查看操作日志导出、管理项目定义导入/导出的执行结果。可以中止等待执行的任务。 | [任务列表](./13-task-list.md) |

## 核心后端服务与逻辑组件

| # | 组件名称 | 组件概述 | 详细文档 |
|---|----------|----------|----------|
| B1 | `createTaskAction` Server Action | 统一处理所有后台任务创建请求的服务器端逻辑。 | [createTaskAction设计文档](./actions/create-task-action.md) |
| B2 | `TaskExecuteFunc` | (Azure Function) 处理新任务，获取并发锁，准备环境，提交Runbook。 | [TaskExecuteFunc设计文档](./backend-services-functions/function-task-execute.md) |
| B3 | `RunbookMonitorFunc` | (Azure Function) 定时监控Runbook作业，获取结果或检测超时。 | [RunbookMonitorFunc设计文档](./backend-services-functions/function-runbook-monitor.md) |
| B4 | `RunbookProcessorFunc` | (Azure Function) 处理Runbook作业的最终结果，归档文件，更新DB，释放资源。 | [RunbookProcessorFunc设计文档](./backend-services-functions/function-runbook-processor.md) |
| B5 | `TaskCancellationFunc` | (Azure Function) 处理任务取消请求。 | [TaskCancellationFunc设计文档](./backend-services-functions/function-task-cancellation.md) |
| B6 | `TaskExecuteTimeoutFunc` | (Azure Function) 处理`TaskExecuteFunc`执行超时后的补偿。 | [TaskExecuteTimeoutFunc设计文档](./backend-services-functions/function-task-execute-timeout.md) |
| B7 | `TaskCancellationTimeoutFunc` | (Azure Function) 处理`TaskCancellationFunc`执行超时后的补偿。 | [TaskCancellationTimeoutFunc设计文档](./backend-services-functions/function-task-cancellation-timeout.md) |
| B8 | `RunbookProcessorTimeoutFunc` | (Azure Function) 处理`RunbookProcessorFunc`执行超时后的补偿。 | [RunbookProcessorTimeoutFunc设计文档](./backend-services-functions/function-runbook-processor-timeout.md) |

## 功能模块与组件说明

每个功能模块或核心组件的详细文档包含以下内容：
- 目的与范围
- 功能规格 / 核心职责
- 用户界面概述 (若适用) / 触发机制
- 业务规则 / 处理流程
- 错误处理
- 技术实现细节 (包括依赖、接口、数据结构、算法、配置项等)

## 相关文档

- [系统架构](../architecture/system-architecture.md)
- [API 规范](../apis/openapi.v1.yaml) (若适用)
- [数据模型](../data-models/) (包含各核心数据表的详细定义)
- [项目定义与规范](../definitions/) (包含术语表、LOV定义、错误消息、环境变量指南等)
- [开发与部署指南](../guides/) (包含Monorepo结构、AI协作规范等)