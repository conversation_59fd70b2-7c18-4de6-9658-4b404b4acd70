# 组件：密码修改 (Password Change)

## 1. 概要 (Overview)

### 1.1 目的 (Purpose)
本组件为“JCS 端点资产与任务管理系统”的已登录用户提供修改自身登录密码的功能。通过与外部身份提供商Keycloak集成，用户可以在验证当前密码和一次性密码（OTP）后，设置一个新的符合安全策略的密码，以增强账户安全性或在密码可能泄露时进行更新。

### 1.2 用户故事/需求 (User Stories/Requirements)
- 作为一名已登录的用户，我希望能够在我需要的时候（例如，定期更换密码或怀疑密码泄露时）修改我的登录密码。
- 作为一名已登录的用户，我希望在修改密码时，系统能验证我当前的密码，以确保是我本人在操作。
- 作为一名已登录的用户，我希望在设置新密码时，系统能强制执行一定的密码复杂度规则（如长度、字符类型组合），以帮助我设置一个强密码。
- 作为一名已登录的用户，我希望新密码不能与我当前使用的密码或我的用户ID相同。
- 作为一名已登录的用户，我希望在修改密码的最后一步，需要输入我移动设备上的一次性密码（OTP）进行最终确认，以增加操作的安全性。
- 作为一名用户，如果我输入的信息有误（如当前密码错误、新密码不符合规则、OTP错误），系统应能给出清晰的错误提示。

### 1.3 高层依赖与交互 (High-Level Dependencies & Interactions)
- **用户认证与会话**: 用户必须处于已登录状态才能访问此功能，依赖当前有效的用户会话。
- **身份提供商 (IdP)**: 密码修改的核心逻辑和策略强制执行依赖 **Keycloak** 服务。用户输入的新密码将通过Keycloak的机制进行更新。当前密码验证和OTP验证也由Keycloak处理。
- **主界面 (Main Screen)**: “密码修改”功能的入口点通常位于[主界面](./02-main-screen.md)的全局导航栏上。
- **后端API (Next.js API Routes / Keycloak Direct Interaction)**:
    - 前端界面收集用户输入的当前密码、新密码和OTP。
    - 这些信息或者通过门户应用的后端API转发给Keycloak进行处理，或者前端使用 `keycloak-js` 库直接与Keycloak的相关端点交互（具体实现方式取决于项目的Keycloak集成策略）。
- **登录功能 (Login)**: 密码修改成功后，用户可能需要重新登录（取决于Keycloak的策略，`fs.md` 提及“密码修改成功后返回登录画面”，暗示可能需要重新登录）。

## 2. 功能规格 (Functional Specifications)

### 2.1 主要流程 (High-Level Flow)
1.  用户在门户系统的主界面导航栏点击“パスワード変更 (密码修改)”按钮。
2.  系统弹出一个“密码修改”模态对话框。
3.  对话框中显示当前登录用户的用户ID（通常为只读）。
4.  用户在对话框中输入以下信息：
    *   **現在のパスワード (当前密码)**
    *   **新しいパスワード (新密码)**
    *   **新しいパスワード（確認） (新密码确认)**
    *   **ワンタイムコード (一次性密码)**：从其已绑定的移动认证应用获取。
5.  用户填写完毕后，点击对话框中的“OK” (或“変更 (修改)”) 按钮。
6.  系统（通过Keycloak）处理密码修改请求：
    a.  验证“当前密码”是否正确。
    b.  验证“新密码”是否与“新密码确认”一致。
    c.  验证“新密码”是否符合已定义的密码策略（长度、字符集、复杂度、与用户ID不同、与当前密码不同）。
    d.  验证“一次性密码 (OTP)”是否有效。
7.  处理结果：
    *   **若所有验证通过**:
        i.  Keycloak更新用户的密码。
        ii. 系统向用户显示密码修改成功的提示（如 `fs.md` EMEC0008: “パスワードを変更しました。”）。
        iii.关闭密码修改对话框。
        iv. 根据 `fs.md` 的描述 (“密码修改成功后返回登录画面”)，用户当前会话可能失效，并被重定向到[登录界面](./01-login.md)要求重新登录。
    *   **若任一验证失败**:
        i.  系统在密码修改对话框内显示相应的错误提示信息（参考 `2.7 错误处理概述`）。
        ii. 用户密码保持不变。
        iii.对话框保持打开状态，允许用户修正输入并重试。
8.  用户也可以在任何时候点击对话框的“キャンセル (取消)”按钮或关闭图标“X”来放弃密码修改并关闭对话框。

```mermaid
sequenceDiagram
    participant User as 👤 用户 (Browser)
    participant MainScreen as 🖥️ 主界面
    participant PwdChangeModal as 🔑 密码修改弹窗
    participant Keycloak as 🔐 Keycloak (IdP)
    participant LoginScreen as 🚪 登录界面

    User->>MainScreen: 点击导航栏“パスワード変更”按钮
    MainScreen->>PwdChangeModal: 打开密码修改弹窗 (显示用户ID)
    User->>PwdChangeModal: 输入当前密码, 新密码, 确认新密码, OTP
    User->>PwdChangeModal: 点击 "OK" 按钮

    PwdChangeModal->>Keycloak: (通过前端JS或后端API) 发送密码修改请求 (含所有输入)
    Keycloak->>Keycloak: 校验当前密码, 新密码策略, OTP
    alt 验证失败 (任一项)
        Keycloak-->>PwdChangeModal: 返回错误信息
        PwdChangeModal->>User: 显示错误提示 (如: 当前密码错误, 新密码不合规, OTP无效)
    else 所有验证成功
        Keycloak->>Keycloak: 更新用户密码
        Keycloak-->>PwdChangeModal: 返回成功响应
        PwdChangeModal->>User: 显示 "パスワードを変更しました。" 提示
        PwdChangeModal-->>MainScreen: 关闭弹窗
        MainScreen-->>LoginScreen: (可能)重定向至登录界面 (会话失效)
    end
```

### 2.2 业务规则 (Business Rules)
-   **访问前提**: 用户必须处于已登录状态才能访问密码修改功能。
-   **当前密码验证**: 必须正确输入当前使用的密码才能进行后续修改。
-   **新密码与确认密码一致性**: “新密码”和“新密码（确认）”输入框的内容必须完全一致。
-   **新密码策略** (由Keycloak强制执行，`fs.md`描述的是对Keycloak的配置要求)：
    *   **长度**: 8至128个字符。
    *   **字符集**: 允许半角英文字母（大小写敏感）、数字，以及 `fs.md` 中列出的特定符号和半角空格。
    *   **复杂度**: 必须包含至少两种不同类型的字符（例如，字母和数字的组合）。
    *   **与用户ID不同**: 新密码不能与当前用户的用户ID相同。
    *   **与当前密码不同**: 新密码必须与当前正在使用的密码不同。
-   **一次性密码 (OTP) 验证**:
    *   必须输入由用户已绑定的移动认证应用生成的有效6位半角数字OTP。
    *   OTP的长度固定为6位，此为Keycloak针对本项目（考虑到Microsoft Authenticator的兼容性）的配置。
-   **修改成功后的行为**: 密码修改成功后，用户会收到成功提示。根据 `fs.md` 的描述，系统会将用户导航回登录界面，这通常意味着当前会话会立即失效，要求用户使用新密码重新登录。

### 2.3 用户界面概述 (User Interface Overview)

-   **入口点**:
    *   主界面导航栏右侧的“パスワード変更 (密码修改)”按钮。
-   **密码修改对话框 (模态)**:
    *   **触发**: 点击“パスワード変更”按钮后弹出。
    *   **标题**: 可能为“パスワードの変更 (修改密码)”或类似文本。
    *   **输入字段**:
        1.  **ユーザーID (用户ID)**: 只读文本，显示当前登录用户的ID。
        2.  **現在のパスワード (当前密码)**: 密码输入框。
        3.  **新しいパスワード (新密码)**: 密码输入框。
        4.  **新しいパスワード（確認） (新密码确认)**: 密码输入框。
        5.  **ワンタイムコード (一次性密码)**: 普通文本输入框，限制输入6位数字。
    *   **操作按钮**:
        *   “OK” (或 “変更する (修改)”)：用于提交密码修改请求。
        *   “キャンセル (取消)”：用于放弃修改并关闭对话框。
    *   **关闭操作**: 通常对话框右上角也会提供标准的“X”关闭图标，其行为应等同于点击“キャンセル (取消)”按钮。
    *   **错误提示区域**: 用于在输入验证失败或处理失败时显示错误信息。
-   **界面草图**: (参考 `fs.md` 图4.6.6 (1) 作为密码修改对话框的高层概念)
    *   `![密码修改对话框示意图](./assets/password-change-modal-sketch.png)` (假设存在此图)

### 2.4 前提条件 (Preconditions)
-   用户必须已成功通过[登录功能](./01-login.md)完成身份验证，并拥有有效的活动会话。
-   用户的账户已在Keycloak中正确配置并启用了OTP多因素认证。
-   Keycloak服务必须正常运行，并且门户应用能够与其进行通信（直接或通过后端API）。

### 2.5 制约事项 (Constraints/Limitations)
-   **浏览器兼容性**: 仅正式支持 Microsoft Edge 和 Google Chrome 浏览器。
-   **语言支持**: 界面显示语言仅支持日语。
-   **密码策略强制性**: 用户设置的新密码必须严格遵守Keycloak中配置的所有密码策略规则，无法绕过。
-   **OTP依赖**: 用户必须能够访问其已绑定的移动认证应用以获取OTP，否则无法完成密码修改。

### 2.6 注意事项 (Notes/Considerations)
-   密码输入框应使用适当的类型（`type="password"`）以隐藏输入内容。
-   出于安全考虑，错误提示信息应尽量通用，避免泄露过多关于验证失败的具体细节（例如，不应明确提示“当前密码不正确”和“用户ID不存在”之间的区别，尽管`fs.md`中部分错误提示EMEC0003是具体的）。Keycloak自身的错误提示行为是主导。
-   如果密码修改成功后强制用户重新登录，应确保用户得到明确的成功提示，并平滑地过渡到登录页面。

### 2.7 错误处理概述 (Error Handling Overview)
(以下错误信息主要参考 `fs.md` 中 EMECxxxx 错误码和Keycloak标准行为)
-   **当前密码不正确**: 对话框内提示错误（如 `fs.md` EMEC0003 或Keycloak的“Invalid credentials”类消息）。
-   **新密码与确认密码不匹配**: 对话框内提示错误（如 `fs.md` EMEC0002: “確認用パスワードが正しくありません。”）。
-   **新密码不符合策略**:
    *   长度不足/超长: 提示错误 (如 `fs.md` EMEC0009)。
    *   复杂度不足 (种类不够): 提示错误 (如 `fs.md` EMEC0010)。
    *   与用户ID相同: 提示错误 (如 `fs.md` EMEC0011)。
    *   与当前密码相同: 提示错误 (如 `fs.md` EMEC0012)。
    *   (Keycloak可能合并这些为统一的“新密码不符合策略”消息)。
-   **一次性密码 (OTP) 错误/无效**: 对话框内提示OTP错误（如 `fs.md` EMEC0014 或 EMEC0015，或Keycloak的“Invalid OTP”类消息）。
-   **后端处理失败/Keycloak通信失败**: 若提交后，后端处理或与Keycloak通信发生不可恢复的错误，对话框内应提示通用错误（如 `fs.md` EMEC0013: “パスワード変更に失敗しました。”或 EMEC0007），并记录详细技术日志。

### 2.8 相关功能参考 (Related Functional References)
*   **主要入口**: [主界面 (Main Screen)](./02-main-screen.md) - 导航栏上的“パスワード変更”按钮是访问本功能的主要途径。
*   **认证关联**: [登录 (Login)](./01-login.md) - 用户的登录身份决定了其密码修改权限。
*   **系统整体架构**: [系统架构](../../architecture/system-architecture.md) - 描述了密码修改功能的数据流和与后端服务的交互。
*   **核心数据模型**: [用户数据模型](../../data-models/User.md) - 定义了用户密码的存储和验证结构。
*   **源功能规格**: [功能规格书](../../docs-delivery/functional-specifications/fs.md) - 本组件功能规格的原始日文描述，特别是其“4.6 パスワード変更”章节。
