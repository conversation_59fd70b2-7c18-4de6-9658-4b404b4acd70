/**
 * @file route.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2024, 2025, Hitachi Solutions, Ltd.
 */

import { NotificationType } from "@/app/lib/definitions";
import { handleApiError } from "@/app/lib/portal-error";
import prisma from "@/app/lib/prisma";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

// システムお知らせの GET リクエストの処理
export async function GET(req: Request) {
  try {
    // サーバーセッションを取得
    const session = await getIronSession<SessionData>(cookies(), sessionOptions);

    // お知らせを最新順で取得
    const notifications = await prisma.notification.findMany({
      where: {
        OR: [
          { type: NotificationType.SYSTEM }, // システムお知らせ
          {
            AND: [
              { userId: session!.user.userId! },
              { type: NotificationType.USER },
            ],
          }, // ユーザーに関連するお知らせ
          {
            AND: [
              {
                licenseId: session.user.licenseId,
              },
              { type: NotificationType.LICENSE },
            ],
          }, // ライセンスに関連するライセンスのお知らせ
          {
            AND: [
              {
                plan: {
                  licensePlans: {
                    some: {
                      license: { licenseId: session!.user.licenseId! },
                    },
                  },
                },
              },
              { type: NotificationType.PLAN },
            ],
          }, // プランに関連するライセンスのプランのお知らせ
        ],
      },
      orderBy: {
        publishedAt: "desc",
      },
    });

    // 取得したお知らせをJSONレスポンスで返す
    return NextResponse.json(notifications);
  } catch (error) {
    // エラーが発生した場合はエラーハンドリング関数に委譲
    return handleApiError(error);
  }
}
