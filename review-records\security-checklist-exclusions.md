# JCS Endpoint Project - セキュリティチェックリスト除外項目一覧

**作成日**: 2025年1月9日  
**対象プロジェクト**: JCS エンドポイント資産とタスク管理システム  
**技術スタック**: Next.js、Azure Functions (Node.js/TypeScript)、Prisma ORM、Azure Blob Storage

---

## 概要

本文書は、汎用セキュリティ開発チェックリスト（全93項目）から、JCS Endpoint Projectの適用チェックリスト（79項目）を作成する際に除外された**80項目**の詳細な除外理由を記載する。

**除外項目数**: 80項目
**適用項目数**: 79項目
**除外率**: 50.3%

**注**: 汎用チェックリストには159項目が含まれており、そのうち79項目がJCSプロジェクトに適用され、80項目が技術的理由により除外されている。

---

## 除外項目詳細一覧

### 1. データチェック機能関連の除外項目 (5項目)

#### **1-15** (汎用版): SSI（Server Side Include）使用時の入力チェック
**原文**: "SSI（Server Side Include）を使用する場合、入力文字のチェックを許可する文字のパターン（ホワイトリスト）で行い、それ以外は拒否して処理を中止する。"
**除外理由**:
- JCS Endpointプロジェクトでは**SSI（Server Side Include）を使用していない**
- Next.jsフレームワークを使用しており、SSIは技術スタックに含まれない
- 代替技術: Next.jsのサーバーサイドレンダリング（SSR）とAPI Routes

#### **1-20** (汎用版): LDAP使用時の入力文字チェック
**原文**: "LDAPを使用する場合、入力文字のチェックは許可する文字のパターン（ホワイトリスト）を作成して行い、それ以外は拒否するか、特殊文字のエスケープ処理を行ってからLDAPクエリを発行する。"
**除外理由**:
- JCS Endpointプロジェクトでは**LDAPを使用していない**
- 認証はKeycloakを使用しており、LDAPディレクトリサービスは使用しない
- 代替技術: Keycloak OAuth2/OpenID Connect認証

#### **1-24** (汎用版): バインド機構が使用できない場合のストアドプロシジャ実装
**原文**: "(バインド機構が使用できない場合) DBMSアクセスをすべてストアドプロシジャ呼び出しで実装する。"
**除外理由**:
- **Prisma ORMを使用**しており、バインド機構は標準で提供される
- ストアドプロシジャを使用する必要がない
- 代替技術: Prisma ORMのタイプセーフなクエリビルダー

#### **1-26** (汎用版): XMLデータベース使用時のXPathクエリ対策
**原文**: "XMLデータベースを使用する場合、入力文字のチェックを許可する文字のパターン（ホワイトリスト）で行うか、特殊文字をエスケープ処理してからXPathクエリを発行する。"
**除外理由**:
- JCS Endpointプロジェクトでは**XMLデータベースを使用していない**
- PostgreSQLリレーショナルデータベースを使用
- 代替技術: Prisma ORM + PostgreSQL

#### **1-28** (汎用版): メール送信時のヘッダインジェクション対策
**原文**: "問い合わせフォーム等から入力される文字を埋め込んでメールを作成する場合、メールヘッダ（To, Cc, Bcc, Subjectなど）の値は固定化するか、改行コードの入力を禁止する。Webアプリケーションの実行環境や言語に用意されているメール送信用APIを使用する。"
**除外理由**:
- JCS Endpointプロジェクトでは**メール送信機能を実装していない**
- 通知機能はシステム内通知のみで、外部メール送信は行わない
- 代替技術: システム内通知機能

### 2. メモリ管理関連の除外項目 (1項目)

#### **7-1** (汎用版): C/C++/Java/.NET向けメモリ不足例外処理
**原文**: "メモリ不足例外（OutOfMemoryException等）が発生した場合の処理を実装する。"
**除外理由**:
- JCS Endpointプロジェクトでは**Node.js/TypeScriptを使用**
- C/C++、Java、.NETは技術スタックに含まれない
- Node.jsはガベージコレクション機能により自動メモリ管理
- 代替技術: Node.jsの自動メモリ管理とAzure App Serviceのリソース監視

### 3. ファイル送受信機能関連の除外項目 (1項目)

#### **8-3** (汎用版): アップロードファイルのデータベース保存
**原文**: "アップロードされたファイルをデータベースに保存する場合、ファイルサイズの上限を設定し、上限を超えるファイルは受け付けない。"
**除外理由**:
- JCS Endpointプロジェクトでは**Azure Blob Storageを使用**してファイル保存
- データベースにバイナリデータを保存する設計ではない
- ファイルサイズ制限はBlob Storageレベルで実装
- 代替技術: Azure Blob Storage + メタデータのDB管理

### 4. 情報漏洩防止対策関連の除外項目 (1項目)

#### **9-4** (汎用版): メモリロック（mlock, VirtualLock）使用
**原文**: "機密情報を格納したメモリ領域は、スワップアウトされないようにメモリロック（mlock, VirtualLock）を使用する。"
**除外理由**:
- **Node.js環境では該当APIが利用できない**
- クラウド環境（Azure）での実行のため、低レベルメモリ制御は不要
- Azure App Serviceでは仮想メモリ管理はプラットフォームが担当
- 代替技術: Node.jsのメモリ管理機能とAzure PaaSのセキュリティ機能

### 5. プラットフォーム固有の除外項目 (32項目)

#### **11-1** ～ **11-14** (汎用版): Windows固有のセキュリティ対策（14項目）
**主な除外項目**:
- **11-1**: IIS/ISAPI使用時のセキュリティ設定
- **11-2**: 名前付きパイプのアクセス権設定
- **11-3**: CreateProcess使用時のセキュリティ記述子設定
- **11-4**: レジストリアクセス権の適切な設定
- **11-5** ～ **11-14**: その他Windows API固有の対策

**除外理由**:
- JCS Endpointプロジェクトは**クラウドネイティブアプリケーション**
- Azure App ServiceとAzure Functionsで実行され、Windows固有の機能は使用しない
- IIS/ISAPI、名前付きパイプ、CreateProcess等のWindows APIは使用しない
- 代替技術: Azure PaaS環境のセキュリティ機能

#### **12-1** ～ **12-18** (汎用版): UNIX/Linux固有のセキュリティ対策（18項目）
**主な除外項目**:
- **12-1**: fork使用時の適切なプロセス管理
- **12-2**: execve使用時のセキュリティ対策
- **12-3**: setuid/setgid使用時の権限管理
- **12-4**: 一時ファイル作成時のumask設定
- **12-5** ～ **12-18**: その他UNIX/Linux固有の対策

**除外理由**:
- JCS Endpointプロジェクトは**Azure PaaS環境で実行**
- fork、execve、setuid等のUNIX/Linux固有のシステムコールは使用しない
- 一時ファイル作成もAzure Blob Storageで管理
- 代替技術: Azure PaaS環境のセキュリティ機能

### 6. 言語固有の除外項目 (29項目)

#### **13-1** ～ **13-16** (汎用版): C/C++固有のセキュリティ対策（16項目）
**主な除外項目**:
- **13-1**: バッファオーバーフロー対策（境界チェック）
- **13-2**: フォーマット文字列攻撃対策
- **13-3**: strcpy、sprintf等の危険な関数の使用禁止
- **13-4**: 整数オーバーフロー対策
- **13-5** ～ **13-16**: その他C/C++固有のメモリ管理対策

**除外理由**:
- JCS Endpointプロジェクトでは**C/C++を使用していない**
- バッファオーバーフロー、フォーマット文字列攻撃等はNode.js/TypeScriptでは該当しない
- 代替技術: TypeScriptの型安全性とNode.jsのメモリ安全性

#### **14-1** ～ **14-7** (汎用版): Java固有のセキュリティ対策（7項目）
**主な除外項目**:
- **14-1**: セキュリティポリシーファイルでの権限管理
- **14-2**: AllPermission等の大きな特権の制限
- **14-3**: シリアル化オブジェクトのアクセス権設定
- **14-4**: シリアル化通信の暗号化
- **14-5** ～ **14-7**: その他Java固有の対策

**除外理由**:
- JCS Endpointプロジェクトでは**Javaを使用していない**
- セキュリティポリシーファイル、シリアル化等はJava固有の機能
- 代替技術: Node.js/TypeScriptのセキュリティ機能

#### **15-1** (汎用版): ASP.NET固有のVIEWSTATE暗号化（1項目）
**除外項目**:
- **15-1**: VIEWSTATEの暗号化設定（EnableViewStateMAC="true"）

**除外理由**:
- JCS Endpointプロジェクトでは**ASP.NETを使用していない**
- Next.jsフレームワークを使用
- 代替技術: Next.jsのセッション管理とCSRF対策

#### **16-1** ～ **16-2** (汎用版): ASP（Active Server Pages）固有の対策（2項目）
**除外項目**:
- **16-1**: FileSystemObjectでのServer.MapPath使用
- **16-2**: Requestオブジェクトのコレクション指定

**除外理由**:
- JCS Endpointプロジェクトでは**ASPを使用していない**
- FileSystemObject、Requestオブジェクト等はASP固有の機能
- 代替技術: Next.jsのファイルシステムAPIとリクエスト処理

#### **17-1** ～ **17-3** (汎用版): Perl固有のセキュリティ対策（3項目）
**除外項目**:
- **17-1**: sysopenの使用（openではなく）
- **17-2**: シェルコマンド混入防止
- **17-3**: Taintモードの使用

**除外理由**:
- JCS Endpointプロジェクトでは**Perlを使用していない**
- sysopen、Taintモード等はPerl固有の機能
- 代替技術: Node.js/TypeScriptのファイル操作とバリデーション

### 7. インストーラ関連の除外項目 (4項目)

#### **18-1** ～ **18-4** (汎用版): インストーラ作成時のセキュリティ対策（4項目）
**除外項目**:
- **18-1**: インストール時のディレクトリ・ファイルアクセス権設定
- **18-2**: インストール後の一時ファイル削除
- **18-3**: 機密ファイルのアクセス権設定
- **18-4**: 既存設定の勝手な変更禁止

**除外理由**:
- JCS Endpointプロジェクトは**クラウドネイティブWebアプリケーション**
- インストーラを作成・配布する必要がない
- Azure App ServiceとAzure Functionsにデプロイ
- 代替技術: Azure DevOpsによるCI/CDパイプライン

### 8. コンパイル関連の除外項目 (1項目)

#### **19-3** (汎用版): メモリクリア命令削除の防止
**除外項目**:
- **19-3**: コンパイル時最適化によるmemset等の削除防止

**除外理由**:
- **Node.js/TypeScriptはインタープリター言語**
- コンパイル時最適化によるメモリクリア命令削除は発生しない
- 代替技術: Node.jsのガベージコレクション機能

### 9. Webページ設計関連の除外項目 (3項目)

#### **20-2** (汎用版): FRAMEやIFRAMEの利用制限
**除外項目**:
- **20-2**: FRAME/IFRAMEの利用制限（特にTLSページでの禁止）

**除外理由**:
- JCS Endpointプロジェクトでは**FRAMEやIFRAMEを使用していない**
- Next.jsのSPA（Single Page Application）アーキテクチャを採用
- 代替技術: Next.jsのルーティングとコンポーネント設計

#### **20-3** (汎用版): HTTPSページでのHTTPコンテンツ混在防止
**除外項目**:
- **20-3**: httpsページにhttpコンテンツを混在させない

**除外理由**:
- JCS Endpointプロジェクトでは**全通信がHTTPS**
- Azure App Serviceで強制HTTPS設定
- 混在コンテンツの問題は発生しない
- 代替技術: Azure App ServiceのHTTPS強制機能

#### **20-5** (汎用版): URLリダイレクトの制限
**除外項目**:
- **20-5**: 外部パラメータを利用したURLリダイレクトの禁止

**除外理由**:
- JCS Endpointプロジェクトでは**外部パラメータによるリダイレクトを実装していない**
- 認証リダイレクトはKeycloakが管理
- 代替技術: Keycloakの安全なリダイレクト機能

### 10. クライアント全体設計関連の除外項目 (2項目)

#### **22-1** ～ **22-2** (汎用版): Internet Explorer固有のActiveX対策（2項目）
**除外項目**:
- **22-1**: ActiveXコントロールの署名と設定強要の禁止
- **22-2**: ダウンロードプログラムの署名と設定強要の禁止

**除外理由**:
- JCS Endpointプロジェクトでは**ActiveXコントロールを使用していない**
- モダンブラウザ対応のWebアプリケーション
- Internet Explorer固有の機能は使用しない
- 代替技術: モダンWeb標準API

---

## 除外項目の技術分類

| 技術分野 | 除外項目数 | 主な理由 |
|----------|------------|----------|
| **プラットフォーム固有** | 32項目 | Windows/UNIX/Linux固有機能の不使用 |
| **言語固有** | 29項目 | C/C++/Java/ASP.NET/Perl等の不使用 |
| **データチェック機能** | 5項目 | SSI/LDAP/XML DB/メール送信等の不使用 |
| **インストーラ関連** | 4項目 | クラウドネイティブ設計による不要化 |
| **Webページ設計** | 3項目 | モダンSPAアーキテクチャによる不要化 |
| **クライアント設計** | 2項目 | ActiveX等レガシー技術の不使用 |
| **メモリ管理** | 1項目 | Node.js自動メモリ管理による不要化 |
| **ファイル送受信** | 1項目 | Azure Blob Storage使用による不要化 |
| **情報漏洩防止** | 1項目 | クラウド環境での低レベル制御不要 |
| **コンパイル関連** | 1項目 | インタープリター言語による不要化 |
| **その他** | 1項目 | 技術スタック差異による不要化 |

**合計**: 80項目

---

## 結論

JCS Endpoint Projectでは、モダンなクラウドネイティブ技術スタック（Next.js + Azure PaaS）を採用することにより、レガシー技術やプラットフォーム固有の多くのセキュリティ対策が不要となっている。

### 除外の妥当性

除外された**80項目**は、すべて以下の明確な技術的根拠に基づく適切な判断である：

1. **技術スタック不適合** (61項目): C/C++、Java、Windows/UNIX固有機能等の不使用
2. **アーキテクチャ差異** (11項目): クラウドネイティブ設計による従来型対策の不要化
3. **機能不実装** (8項目): メール送信、インストーラ等の未実装機能

### セキュリティレベルへの影響

これらの除外は、プロジェクトのセキュリティレベルを低下させるものではない。むしろ：

- **集中的対策**: 適用可能な79項目に集中することで、より効果的なセキュリティ対策の実施が可能
- **モダン技術の活用**: Next.js、TypeScript、Azure PaaSの組み込みセキュリティ機能を最大限活用
- **適切なリスク管理**: プロジェクト固有のリスクに焦点を当てた実用的なセキュリティ対策

### 継続的改善

本除外リストは、技術スタックやアーキテクチャの変更に応じて定期的に見直しを行い、常に最適なセキュリティ対策の実施を確保する。

---

**文書管理**:
- 作成者: AIセキュリティ監査アシスタント
- 承認者: プロジェクトセキュリティ責任者
- 次回見直し: 2025年7月9日（6ヶ月後）
