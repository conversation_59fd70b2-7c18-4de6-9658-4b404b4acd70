/**
 * @file portal-error.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

import { NextResponse } from "next/server";
import Logger from "./logger";
import { PORTAL_ERROR_MESSAGES } from "./definitions";

/**
 * ポータルエラークラス
 */
class PortalError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "PortalError";
  }
}

export default PortalError;

// Prismaエラーかどうかを判定する関数
export function isPrismaError(error: Error) {
  return error.name.includes("PrismaClient");
}

/**
 * APIエラーをハンドリングする関数
 * セキュリティ強化のため、本番環境では機密情報を含む可能性のあるスタックトレースを除外してログ記録する
 * @param {any} error - 処理対象のエラーオブジェクト
 * @returns {NextResponse} 統一されたエラーレスポンス
 */
export function handleApiError(error: any) {
  // 本番環境では機密情報の漏洩を防ぐため、エラーログから敏感な情報を除外
  if (process.env.NODE_ENV === 'production') {
    // 本番環境では安全な情報のみをログ記録
    Logger.error({
      message: error.name || 'UnknownError',
      code: (error as any).code || 'UNKNOWN',
    });
  } else {
    // 開発環境では詳細なエラー情報をログ記録
    Logger.error({ message: error.message, stack: error.stack });
  }

  // Prismaエラーの場合はエラーレスポンスを返す
  if (isPrismaError(error)) {
    return NextResponse.json(
      { error: PORTAL_ERROR_MESSAGES.EMEC0006 },
      { status: 500 },
    );
  }

  // それ以外のエラーの場合もエラーレスポンスを返す
  return NextResponse.json(
    { error: PORTAL_ERROR_MESSAGES.EMEC0007 },
    { status: 500 },
  );
}

/**
 * サーバーエラーをハンドリングする関数
 * セキュリティ強化のため、本番環境では機密情報を含む可能性のあるスタックトレースを除外してログ記録する
 * @param {any} error - 処理対象のエラーオブジェクト
 * @throws {Error} 統一されたエラーメッセージを含むエラー
 */
export function handleServerError(error: any) {
  // 本番環境では機密情報の漏洩を防ぐため、エラーログから敏感な情報を除外
  if (process.env.NODE_ENV === 'production') {
    // 本番環境では安全な情報のみをログ記録
    Logger.error({
      message: error.name || 'UnknownError',
      code: (error as any).code || 'UNKNOWN',
    });
  } else {
    // 開発環境では詳細なエラー情報をログ記録
    Logger.error({ message: error.message, stack: error.stack });
  }

  // Prismaエラーの場合はエラーをスロー
  if (isPrismaError(error)) {
    throw new Error(PORTAL_ERROR_MESSAGES.EMEC0006);
  }

  // それ以外のエラーの場合もエラーをスロー
  throw new Error(PORTAL_ERROR_MESSAGES.EMEC0007);
}
