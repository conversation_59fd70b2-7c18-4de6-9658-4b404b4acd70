/**
 * @fileoverview 日本語データ生成ユーティリティ
 * @description 真実味のある日本語ビジネスデータを生成するためのヘルパー関数群
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import businessNames from '../data/business-names.json';

/**
 * 日本語データ生成クラス
 * 実際のビジネスシーンで使用される日本語の名称やデータを生成する
 */
export class JapaneseFaker {
  /**
   * ランダムな会社名を生成する
   * @returns 日本の実在企業風の会社名
   */
  static company(): string {
    return this.randomFromArray(businessNames.companies);
  }

  /**
   * ランダムな部署名を生成する
   * @returns 日本企業の一般的な部署名
   */
  static department(): string {
    return this.randomFromArray(businessNames.departments);
  }

  /**
   * ランダムなサーバー名を生成する
   * @returns 日本語のサーバー名
   */
  static serverName(): string {
    const baseName = this.randomFromArray(businessNames.serverNames);
    const number = Math.floor(Math.random() * 99) + 1;
    return `${baseName}${number.toString().padStart(2, '0')}`;
  }

  /**
   * ランダムな拠点名を生成する
   * @returns 日本の地域名を含む拠点名
   */
  static location(): string {
    return this.randomFromArray(businessNames.locations);
  }

  /**
   * ランダムなシステム名を生成する
   * @returns 日本企業で使用される一般的なシステム名
   */
  static systemName(): string {
    return this.randomFromArray(businessNames.systemNames);
  }

  /**
   * ランダムなタスク名を生成する
   * @returns 日本語のタスク名
   */
  static taskName(): string {
    return this.randomFromArray(businessNames.taskNames);
  }

  /**
   * ランダムな製品名を生成する
   * @returns 日本の製品名
   */
  static productName(): string {
    return this.randomFromArray(businessNames.productNames);
  }

  /**
   * ランダムな通知タイプを生成する
   * @returns 日本語の通知タイプ
   */
  static notificationType(): string {
    return this.randomFromArray(businessNames.notificationTypes);
  }

  /**
   * 日本語の人名を生成する
   * @returns 日本人の姓名
   */
  static personName(): string {
    const surnames = [
      '田中', '佐藤', '鈴木', '高橋', '渡辺', '伊藤', '山本', '中村', '小林', '加藤',
      '吉田', '山田', '佐々木', '山口', '松本', '井上', '木村', '林', '清水', '山崎',
      '森', '池田', '橋本', '阿部', '石川', '斎藤', '前田', '藤田', '後藤', '小川'
    ];
    
    const givenNames = [
      '太郎', '次郎', '三郎', '花子', '美咲', '健太', '翔太', '大輔', '智子', '由美',
      '裕子', '直樹', '和也', '真一', '博之', '恵子', '美穂', '雅子', '隆', '誠',
      '明', '正', '勇', '進', '清', '茂', '実', '豊', '武', '昭'
    ];

    const surname = this.randomFromArray(surnames);
    const givenName = this.randomFromArray(givenNames);
    return `${surname} ${givenName}`;
  }

  /**
   * 日本語のメールアドレスを生成する
   * @param domain ドメイン名（オプション）
   * @returns 日本語ローマ字のメールアドレス
   */
  static email(domain: string = 'example.com'): string {
    const prefixes = [
      'tanaka', 'sato', 'suzuki', 'takahashi', 'watanabe', 'ito', 'yamamoto',
      'nakamura', 'kobayashi', 'kato', 'yoshida', 'yamada', 'sasaki', 'yamaguchi',
      'matsumoto', 'inoue', 'kimura', 'hayashi', 'shimizu', 'yamazaki'
    ];
    
    const prefix = this.randomFromArray(prefixes);
    const number = Math.floor(Math.random() * 999) + 1;
    return `${prefix}${number}@${domain}`;
  }

  /**
   * 日本の郵便番号を生成する
   * @returns 日本の郵便番号形式（XXX-XXXX）
   */
  static postalCode(): string {
    const first = Math.floor(Math.random() * 900) + 100;
    const second = Math.floor(Math.random() * 9000) + 1000;
    return `${first}-${second}`;
  }

  /**
   * 日本の住所を生成する
   * @returns 日本の住所
   */
  static address(): string {
    const prefectures = [
      '東京都', '大阪府', '神奈川県', '愛知県', '埼玉県', '千葉県', '兵庫県', '北海道',
      '福岡県', '静岡県', '茨城県', '広島県', '京都府', '宮城県', '新潟県', '長野県',
      '岐阜県', '栃木県', '群馬県', '岡山県', '福島県', '三重県', '熊本県', '鹿児島県'
    ];
    
    const cities = [
      '中央区', '港区', '新宿区', '渋谷区', '豊島区', '品川区', '目黒区', '大田区',
      '世田谷区', '中野区', '杉並区', '練馬区', '板橋区', '北区', '荒川区', '足立区',
      '葛飾区', '江戸川区', '千代田区', '文京区', '台東区', '墨田区', '江東区'
    ];
    
    const prefecture = this.randomFromArray(prefectures);
    const city = this.randomFromArray(cities);
    const block = Math.floor(Math.random() * 20) + 1;
    const building = Math.floor(Math.random() * 50) + 1;
    
    return `${prefecture}${city}${block}-${building}`;
  }

  /**
   * 日本の電話番号を生成する
   * @returns 日本の電話番号形式
   */
  static phoneNumber(): string {
    const areaCodes = ['03', '06', '052', '011', '092', '022', '045', '048', '043'];
    const areaCode = this.randomFromArray(areaCodes);
    const number1 = Math.floor(Math.random() * 9000) + 1000;
    const number2 = Math.floor(Math.random() * 9000) + 1000;
    return `${areaCode}-${number1}-${number2}`;
  }

  /**
   * 日本語の説明文を生成する
   * @param length 文の長さ（短い、中程度、長い）
   * @returns 日本語の説明文
   */
  static description(length: 'short' | 'medium' | 'long' = 'medium'): string {
    const shortDescriptions = [
      'システムの定期メンテナンスを実施します。',
      'セキュリティパッチの適用を行います。',
      'データベースの最適化処理を実行します。',
      'ログファイルの圧縮とアーカイブを行います。',
      'バックアップの整合性チェックを実施します。'
    ];
    
    const mediumDescriptions = [
      'システムの安定稼働を維持するため、定期的なメンテナンス作業を実施いたします。作業中は一時的にサービスが停止する場合があります。',
      'セキュリティ強化のため、最新のセキュリティパッチを適用いたします。適用後はシステムの再起動が必要となります。',
      'データベースのパフォーマンス向上を目的として、インデックスの再構築とテーブルの最適化を実行いたします。',
      'ディスク容量の確保とシステム性能の維持のため、古いログファイルの圧縮とアーカイブ処理を実行いたします。',
      'データの整合性と可用性を確保するため、バックアップファイルの検証とリストア試験を実施いたします。'
    ];
    
    const longDescriptions = [
      'システムの継続的な安定稼働と性能維持を目的として、包括的なメンテナンス作業を実施いたします。本作業では、サーバーのハードウェア点検、ソフトウェアの更新、データベースの最適化、セキュリティ設定の見直しを行います。作業期間中は段階的にサービスを停止いたしますが、業務への影響を最小限に抑えるよう配慮いたします。',
      '情報セキュリティの強化と脆弱性対策の一環として、システム全体のセキュリティパッチ適用作業を実施いたします。対象となるのは、オペレーティングシステム、ミドルウェア、アプリケーションソフトウェアです。適用後は各システムの動作確認を行い、正常性を確認してからサービスを再開いたします。',
      'データベースシステムの性能向上と安定性確保を目的として、総合的な最適化作業を実行いたします。具体的には、テーブルの統計情報更新、インデックスの再構築、不要データの削除、パーティション設定の見直しを行います。これにより、クエリの実行速度向上とシステム全体のレスポンス改善が期待されます。'
    ];
    
    switch (length) {
      case 'short':
        return this.randomFromArray(shortDescriptions);
      case 'medium':
        return this.randomFromArray(mediumDescriptions);
      case 'long':
        return this.randomFromArray(longDescriptions);
      default:
        return this.randomFromArray(mediumDescriptions);
    }
  }

  /**
   * 配列からランダムな要素を選択する
   * @param array 選択元の配列
   * @returns ランダムに選択された要素
   */
  static randomFromArray<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }

  /**
   * 指定された範囲の整数を生成する
   * @param min 最小値
   * @param max 最大値
   * @returns ランダムな整数
   */
  static randomInt(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * 指定された確率でtrueを返す
   * @param probability 確率（0-1）
   * @returns ランダムなブール値
   */
  static randomBoolean(probability: number = 0.5): boolean {
    return Math.random() < probability;
  }

  /**
   * 過去の日付を生成する
   * @param daysAgo 何日前まで
   * @returns 過去の日付
   */
  static pastDate(daysAgo: number = 365): Date {
    const now = new Date();
    const pastTime = now.getTime() - (Math.random() * daysAgo * 24 * 60 * 60 * 1000);
    return new Date(pastTime);
  }

  /**
   * 未来の日付を生成する
   * @param daysAhead 何日後まで
   * @returns 未来の日付
   */
  static futureDate(daysAhead: number = 365): Date {
    const now = new Date();
    const futureTime = now.getTime() + (Math.random() * daysAhead * 24 * 60 * 60 * 1000);
    return new Date(futureTime);
  }
}
