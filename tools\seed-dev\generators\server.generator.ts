/**
 * @fileoverview サーバーデータ生成器
 * @description 開発環境用の大量のサーバーデータを生成する
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { PrismaClient } from '@prisma/client';
import { BaseGenerator } from './base.generator';

/**
 * サーバーデータ生成器クラス
 * 様々なタイプのサーバーデータを大量に生成する
 */
export class ServerGenerator extends BaseGenerator {
  private readonly SERVER_COUNT = 200; // 生成するサーバー数
  private readonly BATCH_SIZE = 20;

  constructor(prisma: PrismaClient) {
    super(prisma, 'ServerGenerator');
  }

  /**
   * 生成器名を取得する
   * @returns 生成器名
   */
  getGeneratorName(): string {
    return 'サーバーデータ';
  }

  /**
   * 生成予定のデータ数を取得する
   * @returns 生成予定数
   */
  getExpectedCount(): number {
    return this.SERVER_COUNT;
  }

  /**
   * 既存のサーバーデータをクリーンアップする
   * 関連するタスクデータも削除する
   */
  async cleanup(): Promise<void> {
    try {
      // 関連するタスクデータを先に削除
      await this.prisma.task.deleteMany({});
      
      // サーバーデータを削除
      const deleteResult = await this.prisma.server.deleteMany({});
      console.log(`既存サーバーデータを削除しました (${deleteResult.count}件)`);
    } catch (error) {
      console.error('サーバーデータのクリーンアップに失敗しました', error);
      throw error;
    }
  }

  /**
   * サーバーデータを生成する
   * 様々なタイプのサーバーを大量に生成する
   * @returns 生成されたデータの数
   */
  async generate(): Promise<number> {
    try {
      // 有効なライセンスを取得
      const licenses = await this.getActiveLicenses();
      if (licenses.length === 0) {
        throw new Error('有効なライセンスが見つかりません。先にライセンスデータを生成してください。');
      }

      // サーバータイプのLOVデータを取得
      const serverTypes = await this.getServerTypes();
      if (serverTypes.length === 0) {
        throw new Error('サーバータイプのLOVデータが見つかりません。先にLOVデータを生成してください。');
      }

      console.log(`${licenses.length}個のライセンス、${serverTypes.length}種類のサーバータイプを使用します`);

      // バッチでサーバーデータを生成
      return await this.generateInBatches(
        this.SERVER_COUNT,
        this.BATCH_SIZE,
        async (startIndex: number, count: number) => {
          return await this.generateServerBatch(startIndex, count, licenses, serverTypes);
        }
      );
    } catch (error) {
      console.error('サーバーデータ生成中にエラーが発生しました', error);
      throw error;
    }
  }

  /**
   * サーバーデータのバッチを生成する
   * @param startIndex 開始インデックス
   * @param count 生成数
   * @param licenses 利用可能なライセンス
   * @param serverTypes サーバータイプ
   * @returns 生成されたサーバーデータ
   */
  private async generateServerBatch(
    startIndex: number,
    count: number,
    licenses: any[],
    serverTypes: any[]
  ): Promise<any[]> {
    const servers = [];

    for (let i = 0; i < count; i++) {
      const serverIndex = startIndex + i + 1;
      const license = licenses[Math.floor(Math.random() * licenses.length)];
      const serverType = serverTypes[Math.floor(Math.random() * serverTypes.length)];
      
      const server = {
        name: this.generateServerName(serverIndex, serverType.value),
        type: serverType.code,
        url: this.generateServerUrl(serverIndex, serverType.code),
        hrwGroupName: this.generateHrwGroupName(),
        licenseId: license.licenseId,
        azureVmName: this.generateAzureVmName(serverIndex),
        dockerContainerName: this.generateDockerContainerName(serverIndex, serverType.code),
      };

      servers.push(server);
    }

    // バッチでデータベースに挿入
    await this.prisma.server.createMany({
      data: servers,
    });

    return servers;
  }

  /**
   * サーバー名を生成する
   * @param index サーバーインデックス
   * @param serverTypeValue サーバータイプの値
   * @returns サーバー名
   */
  private generateServerName(index: number, serverTypeValue: string): string {
    const location = this.faker.location();
    const serverNumber = index.toString().padStart(3, '0');
    
    // サーバータイプに応じた命名
    if (serverTypeValue.includes('統括')) {
      return `${location}-統括管理-${serverNumber}`;
    } else if (serverTypeValue.includes('中継')) {
      return `${location}-中継管理-${serverNumber}`;
    } else if (serverTypeValue.includes('秘文')) {
      return `${location}-秘文管理-${serverNumber}`;
    } else {
      return `${location}-${this.faker.serverName()}-${serverNumber}`;
    }
  }

  /**
   * サーバーURLを生成する
   * @param index サーバーインデックス
   * @param serverType サーバータイプ
   * @returns サーバーURL
   */
  private generateServerUrl(index: number, serverType: string): string {
    const baseIp = '192.168.';
    const subnet = Math.floor(index / 254) + 1;
    const host = (index % 254) + 1;
    const ip = `${baseIp}${subnet}.${host}`;
    
    // サーバータイプに応じたポート
    let port: number;
    if (serverType.includes('GENERAL_MANAGER')) {
      port = 8080;
    } else if (serverType.includes('RELAY_MANAGER')) {
      port = 8081;
    } else if (serverType.includes('HIBUN_CONSOLE')) {
      port = 8443;
    } else {
      port = 8080 + (index % 10);
    }
    
    return `https://${ip}:${port}`;
  }

  /**
   * HRWグループ名を生成する
   * @returns HRWグループ名またはnull
   */
  private generateHrwGroupName(): string | null {
    if (this.faker.randomBoolean(0.7)) { // 70%の確率でHRWグループを設定
      const department = this.faker.department();
      const groupNumber = this.faker.randomInt(1, 10);
      return `${department}-グループ${groupNumber}`;
    }
    return null;
  }

  /**
   * Azure VM名を生成する
   * @param index サーバーインデックス
   * @returns Azure VM名またはnull
   */
  private generateAzureVmName(index: number): string | null {
    if (this.faker.randomBoolean(0.6)) { // 60%の確率でAzure VMを設定
      const vmPrefix = 'vm-jcs';
      const environment = this.faker.randomBoolean(0.8) ? 'dev' : 'test';
      const vmNumber = index.toString().padStart(3, '0');
      return `${vmPrefix}-${environment}-${vmNumber}`;
    }
    return null;
  }

  /**
   * Dockerコンテナ名を生成する
   * @param index サーバーインデックス
   * @param serverType サーバータイプ
   * @returns Dockerコンテナ名またはnull
   */
  private generateDockerContainerName(index: number, serverType: string): string | null {
    if (this.faker.randomBoolean(0.4)) { // 40%の確率でDockerコンテナを設定
      let containerType: string;
      
      if (serverType.includes('GENERAL_MANAGER')) {
        containerType = 'general-mgr';
      } else if (serverType.includes('RELAY_MANAGER')) {
        containerType = 'relay-mgr';
      } else if (serverType.includes('HIBUN_CONSOLE')) {
        containerType = 'hibun-console';
      } else {
        containerType = 'app-server';
      }
      
      const containerNumber = index.toString().padStart(3, '0');
      return `jcs-${containerType}-${containerNumber}`;
    }
    return null;
  }

  /**
   * 有効なライセンスを取得する
   * @returns 有効なライセンスの配列
   */
  private async getActiveLicenses(): Promise<any[]> {
    return await this.prisma.license.findMany({
      where: {
        isDisabled: false,
        expiredAt: { gt: new Date() },
      },
      select: {
        licenseId: true,
        type: true,
        maxClients: true,
      },
    });
  }

  /**
   * サーバータイプのLOVデータを取得する
   * @returns サーバータイプの配列
   */
  private async getServerTypes(): Promise<any[]> {
    return await this.prisma.lov.findMany({
      where: {
        parentCode: 'SERVER_TYPE',
        isEnabled: true,
      },
      select: {
        code: true,
        name: true,
        value: true,
      },
    });
  }

  /**
   * サーバーの統計情報を取得する
   * @returns 統計情報
   */
  async getStatistics(): Promise<{
    totalCount: number;
    byType: Record<string, number>;
    withAzureVm: number;
    withDockerContainer: number;
    withHrwGroup: number;
    byLicense: Record<string, number>;
  }> {
    const [
      totalCount,
      servers,
      withAzureVm,
      withDockerContainer,
      withHrwGroup,
    ] = await Promise.all([
      this.prisma.server.count(),
      this.prisma.server.findMany({
        select: {
          type: true,
          licenseId: true,
        },
      }),
      this.prisma.server.count({ where: { azureVmName: { not: null } } }),
      this.prisma.server.count({ where: { dockerContainerName: { not: null } } }),
      this.prisma.server.count({ where: { hrwGroupName: { not: null } } }),
    ]);

    // タイプ別の集計
    const byType: Record<string, number> = {};
    const byLicense: Record<string, number> = {};

    servers.forEach(server => {
      byType[server.type] = (byType[server.type] || 0) + 1;
      byLicense[server.licenseId] = (byLicense[server.licenseId] || 0) + 1;
    });

    return {
      totalCount,
      byType,
      withAzureVm,
      withDockerContainer,
      withHrwGroup,
      byLicense,
    };
  }
}
