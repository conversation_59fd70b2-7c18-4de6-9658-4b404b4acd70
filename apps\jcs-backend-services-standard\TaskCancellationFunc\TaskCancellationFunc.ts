/**
 * @fileoverview タスク中止関数 (TaskCancellationFunc)
 * @description
 * Azure Service BusのTaskControlQueueからタスク中止要求メッセージを受信し、
 * PENDING_CANCELLATION状態のタスクをCANCELLED状態に更新する関数。
 * 楽観ロック制御により安全に状態更新を行い、エラー時はリトライ機構を活用する。
 *
 * @trigger Azure Service Bus - TaskControlQueue キューメッセージ
 * @input TaskControlQueue から受信するJSON形式のメッセージ（{ taskId: 文字列 }）
 * @output Azure SQL Database (Task) のレコード更新
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { app, InvocationContext } from "@azure/functions";
import { prisma } from "../lib/prisma";
import { AppConstants } from "../lib/constants";
import { formatTaskErrorMessage } from "../lib/utils";

/**
 * TaskCancellationFunc - タスク中止要求の処理
 *
 * 処理ステップ:
 * 1. TaskControlQueueメッセージ受信・解析、taskId検証
 * 2. タスク情報取得・ステータス確認（PENDING_CANCELLATION）
 * 3. 楽観ロック制御によるタスクステータス更新（CANCELLED）
 *
 * エラー処理:
 * taskId不正・タスク存在なし・ステータス不正・DB更新失敗時は例外throw（リトライ）。
 */
export async function TaskCancellationFunc(message: unknown, context: InvocationContext): Promise<void> {
  let taskId: string | undefined;

  try {
    // メッセージ基本校验
    if (!message || typeof message !== 'object') {
      const errorMsg = "[TaskCancellationFunc] メッセージが不正です。";
      context.error(errorMsg);
      throw new Error(errorMsg);
    }

    // taskId 抽出・校验
    const messageBody = message as { taskId?: string };
    const extractedTaskId = messageBody.taskId;

    if (!extractedTaskId || typeof extractedTaskId !== "string") {
      const errorMsg = "[TaskCancellationFunc] taskIdが不正です。";
      context.error(errorMsg);
      throw new Error(errorMsg);
    }

    taskId = extractedTaskId;
    context.log(`[TaskCancellationFunc] 受信taskId: ${taskId}`);

    // 2. データベースからタスク情報取得
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      select: { status: true, updatedAt: true }
    });
    if (!task) {
      context.error(`[TaskCancellationFunc] 中止対象のタスク(ID: ${taskId})がデータベースに存在しない。`);
      throw new Error("中止対象タスクが存在しない");
    }

    // 3. タスク状態確認（PENDING_CANCELLATIONのみ処理）
    if (task.status !== AppConstants.TaskStatus.PendingCancellation) {
      context.error(`[TaskCancellationFunc] タスクID ${taskId} のステータスが PENDING_CANCELLATION ではない (現在: ${task.status})。処理を中断する。`);
      throw new Error(`タスクステータスが PENDING_CANCELLATION ではない: ${task.status}`);
    }

    // 4. PENDING_CANCELLATION → CANCELLED へ更新（楽観ロック制御）
    const updateResult = await prisma.task.updateMany({
      where: {
        id: taskId,
        updatedAt: task.updatedAt
      },
      data: {
        status: AppConstants.TaskStatus.Cancelled,
        resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0004),
      },
    });

    if (updateResult.count === 0) {
      context.error(`[TaskCancellationFunc] タスクID ${taskId} の楽観ロック失敗。他プロセスによりタスクが変更されたため処理を中断する。`);
      throw new Error("楽観ロック失敗：タスクが他プロセスにより変更された");
    }

    context.log(`[TaskCancellationFunc] タスクID ${taskId} をCANCELLED/EMET0004で更新完了。`);
  } catch (error) {
    // 重大な例外はエラーログ出力し再スロー
    context.error(`[TaskCancellationFunc] タスクID ${taskId} の処理中にエラー発生:`, error);
    throw error;
  }
}

// FunctionとService Busキューのバインド設定
app.serviceBusQueue("TaskCancellationFunc", {
  connection: "AZURE_SERVICEBUS_NAMESPACE_HOSTNAME",
  queueName: "%SERVICE_BUS_TASK_CONTROL_QUEUE_NAME%",
  handler: TaskCancellationFunc,
}); 