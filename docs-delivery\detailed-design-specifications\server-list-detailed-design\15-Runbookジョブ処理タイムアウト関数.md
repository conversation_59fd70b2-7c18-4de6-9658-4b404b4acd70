## Runbookジョブ処理タイムアウト関数 (RunbookProcessorTimeoutFunc) 詳細設計

### 概要

#### 責務
本関数RunbookProcessorTimeoutFuncは、Runbookジョブ処理関数RunbookProcessorFuncの実行が例外やタイムアウトによって3回（RunbookStatusQueueの最大配信数）まで失敗し、元々RunbookStatusQueueにあったメッセージが破棄され、Azure Service BusによってRunbookStatusQueueのDLQ（Dead-Letter Queue）へ配信された場合に起動されるAzure Functionsの関数である。本関数の主な責務は以下の通りである。

1.  DLQメッセージ（元のRunbookStatusQueueメッセージ）を解析し、タスクIDを取得する。
2.  Taskテーブルからタスクの関連情報（現在のstatus、対象VM名、対象コンテナ名等）を取得する。
3.  タスクのステータスがRUNBOOK_PROCESSINGか判定する。RUNBOOK_PROCESSINGでない場合処理を終了する。
4.  コンテナ実行状態テーブルから対象コンテナのステータスをIDLEに更新する。
5.  タスクテーブルから対象タスクのステータスをCOMPLETED_ERRORに更新し、タスク詳細をタイムアウトのエラーメッセージに更新する。
6.  Azure Files上のタスク作業ディレクトリをクリーンアップ（削除）する。

#### トリガー
Azure Service Bus - RunbookStatusQueue のDLQメッセージ。

#### 主要入力
*   RunbookStatusQueue のDLQから受信する、元のRunbookジョブ実行結果メッセージ。メッセージ構造はRunbookジョブ監視関数RunbookMonitorFuncの出力メッセージ仕様（本設計書「Runbookジョブ監視関数」参照）と同様。
*   Azure SQL Database のTaskテーブルからの読み取り。

#### 主要出力
*   Azure SQL Database のTaskテーブル、ContainerConcurrencyStatusテーブルのレコード更新。
*   Azure Files上のタスク作業ディレクトリ (TaskWorkspaces/<taskID>/) の削除試行。

※DLQの名称は、RunbookStatusQueueの名称（環境変数 SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME で指定）に接尾辞 /$DeadLetterQueue を付加したものとなる。
※Azure Filesへのアクセスには環境変数 AZURE_STORAGE_CONNECTION_STRING で指定される接続文字列が使用される。
※データベース接続には環境変数 MSSQL_PRISMA_URL で指定される接続文字列が使用される。

### 主要処理フロー

```mermaid
graph TD
    A["開始: RunbookStatusQueue<br/>のDLQメッセージ受信"] --> B["メッセージ解析・<br/>taskId特定"];
    B -- "不正/特定不可" --> BA["ログ記録、処理終了"];
    B -- "正常" --> C["Taskテーブル参照<br/>(現在のstatus, VM名,<br/>コンテナ名取得)"];
    C -- "Task存在なし/取得失敗" --> BA;
    C -- "取得成功" --> D{"Task.statusが<br/>RUNBOOK_PROCESSINGか判定"};
    D -- "RUNBOOK_PROCESSINGでない" --> BA;
    D -- "RUNBOOK_PROCESSING" --> G_TxStart["DBトランザクション開始"];
    
    G_TxStart --> H_LockRelease["コンテナ実行状態テーブルから<br/>コンテナのステータスをIDLEに更新"];
    H_LockRelease -- "DB更新成功" --> I_TaskUpdate;

    I_TaskUpdate["Taskテーブルからタスクを<br/>COMPLETED_ERRORに更新<br/>(errorCode: EMET0005)"];
    I_TaskUpdate -- "DB更新失敗" --> J_TxRollback["Txロールバック、<br/>エラーログ記録(処理継続)"];
    H_LockRelease -- "DB更新失敗" --> J_TxRollback;
    I_TaskUpdate -- "DB更新成功" --> K_TxCommit["Txコミット"];

    J_TxRollback --> M_FileCleanup;
    K_TxCommit --> M_FileCleanup;

    M_FileCleanup["Azure Files上の<br/>作業ディレクトリ削除試行<br/>(TaskWorkspaces/taskId/)"];
    M_FileCleanup -- "削除失敗" --> N_LogFail["エラーログ記録"];
    N_LogFail --> L_End["終了"];
    M_FileCleanup -- "削除成功" --> L_End;
```

#### 共通処理フロー詳細

1.  Azure Service Bus の RunbookStatusQueue/$DeadLetterQueue からメッセージを受信し、解析する。メッセージから taskId を特定する。
    *   taskId が特定できない場合、エラーをログに記録し処理を終了する。
2.  taskId を使用して Task テーブルを検索し、タスクの関連情報（status、対象VM名、対象コンテナ名）を取得する。
    *   タスクが存在しない、または取得失敗の場合はエラーをログに記録し処理を終了する。
3.  Task.statusがRUNBOOK_PROCESSINGか判定する。RUNBOOK_PROCESSINGの場合は処理を続行する。
    *   RUNBOOK_PROCESSINGでない場合はログに記録し処理を終了する。
4.  データベーストランザクションを開始する。
5.  コンテナ実行状態ContainerConcurrencyStatus テーブルから対象コンテナを status = IDLE, currentTaskId = NULL で更新する。条件：対象VM名 = ステップ2.で取得した対象VM名、対象コンテナ名 = ステップ2.で取得した対象コンテナ名、ステータス（status） = BUSY、使用中のタスクID（currentTaskId） = 入力パラメータのtaskId
    *   更新失敗または更新した件数が0件の場合はトランザクションをロールバックする。エラー詳細をログに記録し、処理を継続する。
6.  Task テーブルの該当タスクを status = COMPLETED_ERROR, endedAt = 現在の日時（UTC協定世界時）, resultMessage = EMET0005 (タイムアウト)のエラーメッセージ, errorCode = EMET0005 で更新する。更新条件：ID = 入力パラメータのtaskId、最終更新日時 = ステップ2.で取得した最終更新日時
    *   更新失敗または更新した件数が0件の場合はトランザクションをロールバックする。エラー詳細をログに記録し、処理を継続する。
7.  データベーストランザクションをコミットする。
8.  Azure Files上のタスク作業ディレクトリ（TaskWorkspaces/<taskId>/）の削除を試みる。
    *   削除に失敗した場合はエラー詳細をログに記録し、処理を継続する。
9.  正常終了のログを記録し、処理を終了する。（メッセージACK）

### 主要なデータ及び外部サービスとの対話詳細

#### Azure Service Bus (RunbookStatusQueue/$DeadLetterQueue) からのメッセージ受信
*   本Functionは RunbookStatusQueue のDLQメッセージをトリガーとする。
*   メッセージ内容は元のRunbookジョブ実行結果メッセージ。
*   DLQの最大配信数は1に設定されているため、Runbookジョブ処理タイムアウト関数は一回だけ起動される。Runbookジョブ処理タイムアウト関数が処理成功した場合メッセージはACKされキューから削除される。Runbookジョブ処理タイムアウト関数が例外やタイムアウトにより処理失敗した場合メッセージはDLQに残り続けて、システム管理者による手動削除が必要となる。

#### データベース (Task, ContainerConcurrencyStatus) との対話
*   **Task テーブル**:
    *   taskId で読み取り、現在のstatusなどの関連情報を取得。
    *   タスクレコードを最終的に更新する（status, endedAt, errorCode, resultMessage等）。
*   **ContainerConcurrencyStatus テーブル**:
    *   対象コンテナの status を IDLE に、currentTaskId を NULL に更新する。

#### Azure Files との対話
*   タスク作業ディレクトリ（`TaskWorkspaces/<taskId>/`） 全体を削除する。

### エラー処理メカニズム

| エラーシナリオ | 関連エラーコード | 対処・補償ロジック |
|:---|:---|:---|
| 受信メッセージ解析失敗/必須パラメータ不足/不正 | - | エラーログ記録。処理終了。（メッセージACK） |
| 対象タスク情報 (`Task`レコード) がデータベースに存在しない/DB読み取り失敗 | - | エラーログ記録。処理終了。 （メッセージACK） |
| Task.statusがRUNBOOK_PROCESSINGでない | - | エラーログ記録。処理終了。 （メッセージACK） |
| DB更新失敗/更新件数が0件 | - | トランザクションをロールバックする。エラーログ記録。処理を続行する。 |
| Azure Files作業ディレクトリ削除失敗 | - | エラーログ記録。処理を続行する。 |
| 予期せぬ内部エラー | - | エラーログ記録。処理終了。（メッセージACK） |

*   **タイムアウトについて**
    Azure FunctionsがRunbookジョブ処理タイムアウト関数の実行時間を監視し、host.jsonのfunctionTimeoutプロパティで設定されたタイムアウト時間（5分）になっても関数がまだ終了していない場合、Azure Functionsは関数の実行を強制的に中止する。
*   **リトライについて**
    DLQの最大配信数は1に設定されているため、Runbookジョブ処理タイムアウト関数は一回だけ実行される。例外やタイムアウトにより処理失敗の場合でもリトライは行わない。この場合はシステム管理者による人的確認と対処が必要となる。