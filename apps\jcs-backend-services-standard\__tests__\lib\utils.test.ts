/**
 * @file utils.test.ts
 * @description
 * lib/utils.ts の単体テストファイルです。
 * Jest を用いて、ユーティリティ関数の主要な分岐、例外処理、ファイル削除ロジックの動作を検証します。
 *
 * 【設計意図】
 * - deleteTaskWorkspaceDirectory関数の全分岐・例外処理の網羅的なテストを自動化。
 * - formatTaskErrorMessage関数のエラーメッセージ生成ロジックの検証。
 * - Azure Files操作の外部依存をモックし、異常系も含めて堅牢性を担保。
 * - 100%のコードカバレッジを目指し、全ての分岐・例外処理を網羅的に検証。
 *
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import {
  deleteTaskWorkspaceDirectory,
  formatTaskErrorMessage,
  isPrismaError,
  isAzureFilesError,
  isAzureBlobError,
  hasErrorCode
} from "../../lib/utils";
import { ShareDirectoryClient, ShareFileClient } from "@azure/storage-file-share";

// Azure Storage File Share のモック
jest.mock("@azure/storage-file-share");

/**
 * @fileoverview utils関数の単体テスト。
 * @description lib/utils.tsの主要分岐・例外・ファイル削除処理を網羅的に検証する。
 * 試験観点：ユーティリティ関数の正常系・異常系・ファイル削除分岐網羅性、外部依存のモックによる堅牢性検証。
 * 試験対象：lib/utils.ts（deleteTaskWorkspaceDirectory、formatTaskErrorMessage関数）。
 */
describe("lib/utils 単体テスト", () => {
  let context: any;
  let mockTaskDirectoryClient: jest.Mocked<ShareDirectoryClient>;
  let mockImportsDirectoryClient: jest.Mocked<ShareDirectoryClient>;
  let mockExportsDirectoryClient: jest.Mocked<ShareDirectoryClient>;
  let mockFileClient: jest.Mocked<ShareFileClient>;

  beforeEach(() => {
    /**
     * Azure Functions の InvocationContext をモックし、必須フィールドを全て補完
     */
    context = {
      invocationId: "test-invoke",
      functionName: "utils-test",
      extraInputs: [],
      extraOutputs: [],
      bindingData: {},
      traceContext: {},
      log: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
    };

    // ShareDirectoryClient のモック
    mockTaskDirectoryClient = {
      path: "TaskWorkspaces/task1",
      exists: jest.fn(),
      deleteIfExists: jest.fn(),
      getDirectoryClient: jest.fn(),
      getFileClient: jest.fn(),
    } as any;

    mockImportsDirectoryClient = {
      exists: jest.fn(),
      deleteIfExists: jest.fn(),
      getFileClient: jest.fn(),
    } as any;

    mockExportsDirectoryClient = {
      exists: jest.fn(),
      deleteIfExists: jest.fn(),
      getFileClient: jest.fn(),
      listFilesAndDirectories: jest.fn(),
    } as any;

    mockFileClient = {
      exists: jest.fn(),
      delete: jest.fn(),
      deleteIfExists: jest.fn(),
    } as any;

    jest.clearAllMocks();
  });

  describe("formatTaskErrorMessage", () => {
    /**
     * 試験観点：正常系メッセージ生成の検証。
     * 試験対象：formatTaskErrorMessage関数の正常系。
     * 試験手順：
     * 1. 有効なエラーコードでメッセージを生成する場合をテスト。
     * 確認項目：
     * - 正しいエラーメッセージが返されること。
     */
    it("有効なエラーコード: 正常メッセージ生成", () => {
      const result = formatTaskErrorMessage("EMET0005");
      expect(result).toContain("完了が確認できない");
      expect(result).toContain("EMET0005");
    });

    /**
     * 試験観点：異常系メッセージ生成の検証。
     * 試験対象：formatTaskErrorMessage関数の異常系。
     * 試験手順：
     * 1. 無効なエラーコードでメッセージを生成する場合をテスト。
     * 確認項目：
     * - 空文字列が返されること。
     */
    it("無効なエラーコード: 空文字列返却", () => {
      const result = formatTaskErrorMessage("INVALID_CODE");
      expect(result).toBe("");
    });

    /**
     * 試験観点：パラメータ置換機能の検証。
     * 試験対象：formatTaskErrorMessage関数のパラメータ置換。
     * 試験手順：
     * 1. パラメータ付きでメッセージを生成する場合をテスト。
     * 確認項目：
     * - パラメータが正しく置換されること。
     */
    it("パラメータ付き: 正常置換", () => {
      const result = formatTaskErrorMessage("EMET0001", ["testParam"]);
      expect(typeof result).toBe("string");
    });

    /**
     * 試験観点：デフォルトパラメータ機能の検証。
     * 試験対象：formatTaskErrorMessage関数のデフォルトパラメータ。
     * 試験手順：
     * 1. パラメータなしでメッセージを生成する場合をテスト。
     * 確認項目：
     * - デフォルトの空配列が使用されること。
     */
    it("パラメータなし: デフォルト空配列", () => {
      const result = formatTaskErrorMessage("EMET0005");
      expect(typeof result).toBe("string");
      expect(result.length).toBeGreaterThan(0);
    });
  });

  describe("isPrismaError", () => {
    /**
     * 試験観点：Prismaエラー判定の正常系検証。
     * 試験対象：isPrismaError関数の正常系。
     * 試験手順：
     * 1. PrismaClientエラーの場合をテスト。
     * 確認項目：
     * - trueが返されること。
     */
    it("PrismaClientエラー: true返却", () => {
      const error = new Error("Database error");
      error.name = "PrismaClientKnownRequestError";
      expect(isPrismaError(error)).toBe(true);
    });

    /**
     * 試験観点：Prismaエラー判定の異常系検証。
     * 試験対象：isPrismaError関数の異常系。
     * 試験手順：
     * 1. 通常のエラーの場合をテスト。
     * 確認項目：
     * - falseが返されること。
     */
    it("通常エラー: false返却", () => {
      const error = new Error("Normal error");
      expect(isPrismaError(error)).toBe(false);
    });
  });

  describe("isAzureFilesError", () => {
    /**
     * 試験観点：Azure Filesエラー判定の正常系検証。
     * 試験対象：isAzureFilesError関数の正常系。
     * 試験手順：
     * 1. Azure Files RestErrorの場合をテスト。
     * 確認項目：
     * - trueが返されること。
     */
    it("Azure Files RestError: true返却", () => {
      const error = new Error("Share not found");
      error.name = "RestError";
      expect(isAzureFilesError(error)).toBe(true);
    });

    /**
     * 試験観点：Azure Filesエラー判定の正常系検証。
     * 試験対象：isAzureFilesError関数の正常系。
     * 試験手順：
     * 1. ShareErrorの場合をテスト。
     * 確認項目：
     * - trueが返されること。
     */
    it("ShareError: true返却", () => {
      const error = new Error("Share error");
      error.name = "ShareError";
      expect(isAzureFilesError(error)).toBe(true);
    });

    /**
     * 試験観点：Azure Filesエラー判定の異常系検証。
     * 試験対象：isAzureFilesError関数の異常系。
     * 試験手順：
     * 1. 通常のエラーの場合をテスト。
     * 確認項目：
     * - falseが返されること。
     */
    it("通常エラー: false返却", () => {
      const error = new Error("Normal error");
      expect(isAzureFilesError(error)).toBe(false);
    });
  });

  describe("isAzureBlobError", () => {
    /**
     * 試験観点：Azure Blobエラー判定の正常系検証。
     * 試験対象：isAzureBlobError関数の正常系。
     * 試験手順：
     * 1. Azure Blob RestErrorの場合をテスト。
     * 確認項目：
     * - trueが返されること。
     */
    it("Azure Blob RestError: true返却", () => {
      const error = new Error("Blob not found");
      error.name = "RestError";
      expect(isAzureBlobError(error)).toBe(true);
    });

    /**
     * 試験観点：Azure Blobエラー判定の異常系検証。
     * 試験対象：isAzureBlobError関数の異常系。
     * 試験手順：
     * 1. 通常のエラーの場合をテスト。
     * 確認項目：
     * - falseが返されること。
     */
    it("通常エラー: false返却", () => {
      const error = new Error("Normal error");
      expect(isAzureBlobError(error)).toBe(false);
    });
  });

  describe("hasErrorCode", () => {
    /**
     * 試験観点：エラーコード判定の正常系検証。
     * 試験対象：hasErrorCode関数の正常系。
     * 試験手順：
     * 1. 指定されたエラーコードを持つエラーの場合をテスト。
     * 確認項目：
     * - trueが返されること。
     */
    it("指定エラーコード存在: true返却", () => {
      const error = new Error("Test error") as any;
      error.code = "TEST_CODE";
      expect(hasErrorCode(error, "TEST_CODE")).toBe(true);
    });

    /**
     * 試験観点：エラーコード判定の異常系検証。
     * 試験対象：hasErrorCode関数の異常系。
     * 試験手順：
     * 1. 異なるエラーコードを持つエラーの場合をテスト。
     * 確認項目：
     * - falseが返されること。
     */
    it("異なるエラーコード: false返却", () => {
      const error = new Error("Test error") as any;
      error.code = "OTHER_CODE";
      expect(hasErrorCode(error, "TEST_CODE")).toBe(false);
    });

    /**
     * 試験観点：エラーコード判定の異常系検証。
     * 試験対象：hasErrorCode関数の異常系。
     * 試験手順：
     * 1. エラーコードを持たないエラーの場合をテスト。
     * 確認項目：
     * - falseが返されること。
     */
    it("エラーコードなし: false返却", () => {
      const error = new Error("Test error");
      expect(hasErrorCode(error, "TEST_CODE")).toBe(false);
    });
  });

  describe("deleteTaskWorkspaceDirectory", () => {
    /**
     * 試験観点：ディレクトリ不存在分岐の検証。
     * 試験対象：deleteTaskWorkspaceDirectory関数のディレクトリ不存在分岐。
     * 試験手順：
     * 1. タスクディレクトリが存在しない場合をテスト。
     * 確認項目：
     * - context.logに"存在しません"が出力されること。
     * - 処理が正常終了すること。
     */
    it("タスクディレクトリ不存在: 正常終了", async () => {
      mockTaskDirectoryClient.exists.mockResolvedValue(false);
      
      await deleteTaskWorkspaceDirectory(mockTaskDirectoryClient, context);
      
      expect(context.log).toHaveBeenCalledWith(expect.stringContaining("存在しません"));
      expect(mockTaskDirectoryClient.deleteIfExists).not.toHaveBeenCalled();
    });

    /**
     * 試験観点：importsディレクトリ不存在分岐の検証。
     * 試験対象：deleteTaskWorkspaceDirectory関数のimports削除分岐。
     * 試験手順：
     * 1. importsディレクトリが存在しない場合をテスト。
     * 確認項目：
     * - context.logに"imports/ディレクトリが存在しません"が出力されること。
     * - 処理が継続されること。
     */
    it("importsディレクトリ不存在: 処理継続", async () => {
      mockTaskDirectoryClient.exists.mockResolvedValue(true);
      mockTaskDirectoryClient.getDirectoryClient
        .mockReturnValueOnce(mockImportsDirectoryClient)
        .mockReturnValueOnce(mockExportsDirectoryClient);
      mockImportsDirectoryClient.exists.mockResolvedValue(false);
      mockExportsDirectoryClient.exists.mockResolvedValue(false);

      await deleteTaskWorkspaceDirectory(mockTaskDirectoryClient, context);

      expect(context.log).toHaveBeenCalledWith(expect.stringContaining("imports/ディレクトリが存在しません"));
      expect(context.log).toHaveBeenCalledWith(expect.stringContaining("exports/ディレクトリが存在しません"));
    });

    /**
     * 試験観点：importsファイル削除分岐の検証。
     * 試験対象：deleteTaskWorkspaceDirectory関数のimportsファイル削除分岐。
     * 試験手順：
     * 1. assetsfield_def.csvファイルが存在する場合をテスト。
     * 確認項目：
     * - ファイルが削除されること。
     * - context.logに"ファイル削除: imports/assetsfield_def.csv"が出力されること。
     */
    it("importsファイル存在: ファイル削除", async () => {
      mockTaskDirectoryClient.exists.mockResolvedValue(true);
      mockTaskDirectoryClient.getDirectoryClient
        .mockReturnValueOnce(mockImportsDirectoryClient)
        .mockReturnValueOnce(mockExportsDirectoryClient);
      mockImportsDirectoryClient.exists.mockResolvedValue(true);
      mockImportsDirectoryClient.getFileClient.mockReturnValue(mockFileClient);
      mockFileClient.exists.mockResolvedValue(true);
      mockExportsDirectoryClient.exists.mockResolvedValue(false);

      await deleteTaskWorkspaceDirectory(mockTaskDirectoryClient, context);

      expect(mockFileClient.delete).toHaveBeenCalled();
      expect(context.log).toHaveBeenCalledWith(expect.stringContaining("ファイル削除: imports/assetsfield_def.csv"));
    });

    /**
     * 試験観点：exportsファイル削除分岐の検証。
     * 試験対象：deleteTaskWorkspaceDirectory関数のexportsファイル削除分岐。
     * 試験手順：
     * 1. exportsディレクトリにファイルが存在する場合をテスト。
     * 確認項目：
     * - ファイルが削除されること。
     * - context.logに"ファイル削除: exports/filename"が出力されること。
     */
    it("exportsファイル存在: ファイル削除", async () => {
      mockTaskDirectoryClient.exists.mockResolvedValue(true);
      mockTaskDirectoryClient.getDirectoryClient
        .mockReturnValueOnce(mockImportsDirectoryClient)
        .mockReturnValueOnce(mockExportsDirectoryClient);
      mockImportsDirectoryClient.exists.mockResolvedValue(false);
      mockExportsDirectoryClient.exists.mockResolvedValue(true);

      // exportsディレクトリ内のファイルをモック
      const mockIterator = {
        [Symbol.asyncIterator]: async function* () {
          yield { kind: "file", name: "result.csv" };
        }
      };
      mockExportsDirectoryClient.listFilesAndDirectories.mockReturnValue(mockIterator as any);
      mockExportsDirectoryClient.getFileClient.mockReturnValue(mockFileClient);

      await deleteTaskWorkspaceDirectory(mockTaskDirectoryClient, context);

      expect(mockFileClient.deleteIfExists).toHaveBeenCalled();
      expect(context.log).toHaveBeenCalledWith(expect.stringContaining("ファイル削除: exports/result.csv"));
    });

    /**
     * 試験観点：imports削除例外分岐の検証。
     * 試験対象：deleteTaskWorkspaceDirectory関数のimports削除例外処理分岐。
     * 試験手順：
     * 1. imports削除で例外が発生する場合をテスト。
     * 確認項目：
     * - context.logにエラーメッセージが出力されること。
     * - 処理が継続されること（例外は再スローされない）。
     */
    it("imports削除例外: エラーログ記録・処理継続", async () => {
      const error = new Error("imports削除失敗");
      mockTaskDirectoryClient.exists.mockResolvedValue(true);
      mockTaskDirectoryClient.getDirectoryClient
        .mockReturnValueOnce(mockImportsDirectoryClient)
        .mockReturnValueOnce(mockExportsDirectoryClient);
      mockImportsDirectoryClient.exists.mockRejectedValue(error);
      mockExportsDirectoryClient.exists.mockResolvedValue(false);

      // 例外は再スローされず、処理が継続される
      await deleteTaskWorkspaceDirectory(mockTaskDirectoryClient, context);
      expect(context.log).toHaveBeenCalledWith(expect.stringContaining("imports/ディレクトリ削除中にエラー"));
    });

    /**
     * 試験観点：exports削除例外分岐の検証。
     * 試験対象：deleteTaskWorkspaceDirectory関数のexports削除例外処理分岐。
     * 試験手順：
     * 1. exports削除で例外が発生する場合をテスト。
     * 確認項目：
     * - context.logにエラーメッセージが出力されること。
     * - 処理が継続されること（例外は再スローされない）。
     */
    it("exports削除例外: エラーログ記録・処理継続", async () => {
      const error = new Error("exports削除失敗");
      mockTaskDirectoryClient.exists.mockResolvedValue(true);
      mockTaskDirectoryClient.getDirectoryClient
        .mockReturnValueOnce(mockImportsDirectoryClient)
        .mockReturnValueOnce(mockExportsDirectoryClient);
      mockImportsDirectoryClient.exists.mockResolvedValue(false);
      mockExportsDirectoryClient.exists.mockRejectedValue(error);

      // 例外は再スローされず、処理が継続される
      await deleteTaskWorkspaceDirectory(mockTaskDirectoryClient, context);
      expect(context.log).toHaveBeenCalledWith(expect.stringContaining("exports/ディレクトリ削除中にエラー"));
    });

    /**
     * 試験観点：メイン削除例外分岐の検証。
     * 試験対象：deleteTaskWorkspaceDirectory関数のメイン例外処理分岐。
     * 試験手順：
     * 1. メイン処理で例外が発生する場合をテスト。
     * 確認項目：
     * - context.errorにエラーメッセージが出力されること。
     * - 例外が再スローされること。
     */
    it("メイン削除例外: エラーログ記録・例外再スロー", async () => {
      const error = new Error("メイン削除失敗");
      mockTaskDirectoryClient.exists.mockRejectedValue(error);

      await expect(deleteTaskWorkspaceDirectory(mockTaskDirectoryClient, context)).rejects.toThrow("メイン削除失敗");
      expect(context.error).toHaveBeenCalledWith(expect.stringContaining("削除失敗"));
    });

    /**
     * 試験観点：正常系フローの網羅的検証。
     * 試験対象：deleteTaskWorkspaceDirectory関数の正常系フロー。
     * 試験手順：
     * 1. 正常系の完全なフローをテスト。
     * 確認項目：
     * - 全ての処理が正常に実行されること。
     * - 適切なログが出力されること。
     */
    it("正常系: 完全削除フロー", async () => {
      mockTaskDirectoryClient.exists.mockResolvedValue(true);
      mockTaskDirectoryClient.getDirectoryClient
        .mockReturnValueOnce(mockImportsDirectoryClient)
        .mockReturnValueOnce(mockExportsDirectoryClient);
      mockImportsDirectoryClient.exists.mockResolvedValue(true);
      mockImportsDirectoryClient.getFileClient.mockReturnValue(mockFileClient);
      mockFileClient.exists.mockResolvedValue(true);
      mockExportsDirectoryClient.exists.mockResolvedValue(true);

      const mockIterator = {
        [Symbol.asyncIterator]: async function* () {
          yield { kind: "file", name: "output.txt" };
        }
      };
      mockExportsDirectoryClient.listFilesAndDirectories.mockReturnValue(mockIterator as any);
      mockExportsDirectoryClient.getFileClient.mockReturnValue(mockFileClient);

      await deleteTaskWorkspaceDirectory(mockTaskDirectoryClient, context);

      expect(context.log).toHaveBeenCalledWith(expect.stringContaining("削除開始"));
      expect(context.log).toHaveBeenCalledWith(expect.stringContaining("削除完了"));
      expect(mockTaskDirectoryClient.deleteIfExists).toHaveBeenCalled();
    });
  });
});
