describe("初期化表示のテスト", () => {
  describe("ログアウトダイアログ", () => {
    const validCredentials = {
      userId: "hitachi.taro.aa",
      password: "changeit!@#",
    };

    it("確認用メッセージが表示される", () => {
      cy.visit("/login");
      cy.get("#userId").type(validCredentials.userId);
      cy.get("#password").type(validCredentials.password);
      cy.get("button").contains("ログイン").click();
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("ログアウト").click();
      cy.get(
        "#logout-modal > .w-full > .relative > .flex-row-reverse > button.bg-gradient-dark",
      ).click();
      cy.contains("ログアウトします。よろしいですか？");
    });
  });
});
