/**
 * @fileoverview 提供ファイルデータ生成器
 * @description 開発環境用の大量の提供ファイルデータを生成する
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { PrismaClient } from '@prisma/client';
import { BaseGenerator } from './base.generator';

/**
 * 提供ファイルデータ生成器クラス
 * 様々な種類の提供ファイルデータを大量に生成する
 */
export class ProvidedFileGenerator extends BaseGenerator {
  private readonly FILE_COUNT = 80; // 生成するファイル数
  private readonly BATCH_SIZE = 10;

  constructor(prisma: PrismaClient) {
    super(prisma, 'ProvidedFileGenerator');
  }

  /**
   * 生成器名を取得する
   * @returns 生成器名
   */
  getGeneratorName(): string {
    return '提供ファイルデータ';
  }

  /**
   * 生成予定のデータ数を取得する
   * @returns 生成予定数
   */
  getExpectedCount(): number {
    return this.FILE_COUNT;
  }

  /**
   * 既存の提供ファイルデータをクリーンアップする
   * 関連するプラン提供ファイルも削除する
   */
  async cleanup(): Promise<void> {
    try {
      // 関連するプラン提供ファイルを先に削除
      await this.prisma.planProvidedFile.deleteMany({});
      
      // 提供ファイルデータを削除
      const deleteResult = await this.prisma.providedFile.deleteMany({});
      console.log(`既存提供ファイルデータを削除しました (${deleteResult.count}件)`);
    } catch (error) {
      console.error('提供ファイルデータのクリーンアップに失敗しました', error);
      throw error;
    }
  }

  /**
   * 提供ファイルデータを生成する
   * 様々な種類の提供ファイルを大量に生成する
   * @returns 生成されたデータの数
   */
  async generate(): Promise<number> {
    try {
      // バッチで提供ファイルデータを生成
      return await this.generateInBatches(
        this.FILE_COUNT,
        this.BATCH_SIZE,
        async (startIndex: number, count: number) => {
          return await this.generateFileBatch(startIndex, count);
        }
      );
    } catch (error) {
      console.error('提供ファイルデータ生成中にエラーが発生しました', error);
      throw error;
    }
  }

  /**
   * 提供ファイルデータのバッチを生成する
   * @param startIndex 開始インデックス
   * @param count 生成数
   * @returns 生成された提供ファイルデータ
   */
  private async generateFileBatch(
    startIndex: number,
    count: number
  ): Promise<any[]> {
    const files = [];

    for (let i = 0; i < count; i++) {
      const fileIndex = startIndex + i + 1;
      const fileInfo = this.generateFileInfo(fileIndex);
      
      const file = {
        name: `${fileInfo.name}_${fileIndex.toString().padStart(3, '0')}`, // 一意性を保証
        description: fileInfo.description,
        updatedAt: this.generateUpdateDate(),
        size: this.generateFileSize(),
        fileName: fileInfo.fileName,
      };

      files.push(file);
    }

    // バッチでデータベースに挿入
    await this.prisma.providedFile.createMany({
      data: files,
    });

    return files;
  }

  /**
   * ファイル情報を生成する
   * @param index ファイルインデックス
   * @returns ファイル情報
   */
  private generateFileInfo(index: number): {
    name: string;
    description: string;
    fileName: string;
  } {
    const fileTypes = [
      {
        name: 'セキュリティパッチ',
        description: 'システムのセキュリティ脆弱性を修正するためのパッチファイルです。',
        prefix: 'security_patch'
      },
      {
        name: 'システム更新プログラム',
        description: 'システムの機能追加や不具合修正を含む更新プログラムです。',
        prefix: 'system_update'
      },
      {
        name: 'ドライバーファイル',
        description: 'ハードウェアデバイス用のドライバーファイルです。',
        prefix: 'driver'
      },
      {
        name: '設定テンプレート',
        description: 'システム設定用のテンプレートファイルです。',
        prefix: 'config_template'
      },
      {
        name: 'スクリプトファイル',
        description: '自動化処理用のスクリプトファイルです。',
        prefix: 'script'
      },
      {
        name: 'データベーススキーマ',
        description: 'データベース構造定義ファイルです。',
        prefix: 'db_schema'
      },
      {
        name: 'ライセンスファイル',
        description: 'ソフトウェアライセンス情報ファイルです。',
        prefix: 'license'
      },
      {
        name: '証明書ファイル',
        description: 'SSL/TLS証明書ファイルです。',
        prefix: 'certificate'
      },
      {
        name: 'バックアップツール',
        description: 'データバックアップ用のツールファイルです。',
        prefix: 'backup_tool'
      },
      {
        name: '監視設定ファイル',
        description: 'システム監視用の設定ファイルです。',
        prefix: 'monitor_config'
      },
      {
        name: 'ログ解析ツール',
        description: 'ログファイル解析用のツールです。',
        prefix: 'log_analyzer'
      },
      {
        name: 'パフォーマンス調整ツール',
        description: 'システムパフォーマンス調整用のツールです。',
        prefix: 'perf_tuning'
      },
      {
        name: 'ネットワーク設定ファイル',
        description: 'ネットワーク接続設定用のファイルです。',
        prefix: 'network_config'
      },
      {
        name: 'ユーザー管理ツール',
        description: 'ユーザーアカウント管理用のツールです。',
        prefix: 'user_mgmt'
      },
      {
        name: 'レポート生成ツール',
        description: 'システムレポート生成用のツールです。',
        prefix: 'report_gen'
      }
    ];

    const fileType = fileTypes[index % fileTypes.length];
    const version = this.generateVersion();
    const extensions = ['exe', 'msi', 'zip', 'tar.gz', 'deb', 'rpm', 'pkg', 'dmg'];
    const extension = this.faker.randomFromArray(extensions);
    
    return {
      name: `${fileType.name} v${version}`,
      description: `${fileType.description} バージョン ${version} の提供ファイルです。`,
      fileName: `${fileType.prefix}_v${version.replace(/\./g, '_')}.${extension}`,
    };
  }

  /**
   * バージョン番号を生成する
   * @returns バージョン番号
   */
  private generateVersion(): string {
    const major = this.faker.randomInt(1, 5);
    const minor = this.faker.randomInt(0, 9);
    const patch = this.faker.randomInt(0, 20);
    
    if (this.faker.randomBoolean(0.3)) {
      // 30%の確率でパッチバージョンも含める
      return `${major}.${minor}.${patch}`;
    } else {
      return `${major}.${minor}`;
    }
  }

  /**
   * 更新日時を生成する
   * @returns 更新日時
   */
  private generateUpdateDate(): Date {
    // 過去2年以内のランダムな日時
    const now = new Date();
    const twoYearsAgo = new Date(now.getFullYear() - 2, now.getMonth(), now.getDate());
    const randomTime = twoYearsAgo.getTime() + Math.random() * (now.getTime() - twoYearsAgo.getTime());
    return new Date(randomTime);
  }

  /**
   * ファイルサイズを生成する（バイト単位）
   * @returns ファイルサイズ
   */
  private generateFileSize(): number {
    // 1MB から 500MB の範囲でランダム生成
    const minSize = 1024 * 1024; // 1MB
    const maxSize = 500 * 1024 * 1024; // 500MB
    
    // 重み付き分布（小さいファイルが多い）
    const random = Math.random();
    if (random < 0.5) {
      // 50%: 1MB - 10MB
      return this.faker.randomInt(minSize, 10 * 1024 * 1024);
    } else if (random < 0.8) {
      // 30%: 10MB - 50MB
      return this.faker.randomInt(10 * 1024 * 1024, 50 * 1024 * 1024);
    } else {
      // 20%: 50MB - 500MB
      return this.faker.randomInt(50 * 1024 * 1024, maxSize);
    }
  }

  /**
   * 提供ファイルの統計情報を取得する
   * @returns 統計情報
   */
  async getStatistics(): Promise<{
    totalCount: number;
    totalSize: number;
    averageSize: number;
    byExtension: Record<string, number>;
    recentUpdates: number;
    oldFiles: number;
    largestFile: any;
    smallestFile: any;
  }> {
    const [
      totalCount,
      files,
      recentUpdates,
      oldFiles,
      largestFile,
      smallestFile,
    ] = await Promise.all([
      this.prisma.providedFile.count(),
      this.prisma.providedFile.findMany({
        select: {
          fileName: true,
          size: true,
          updatedAt: true,
        },
      }),
      this.prisma.providedFile.count({
        where: {
          updatedAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }, // 過去30日
        },
      }),
      this.prisma.providedFile.count({
        where: {
          updatedAt: { lt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000) }, // 1年以上前
        },
      }),
      this.prisma.providedFile.findFirst({
        orderBy: { size: 'desc' },
        select: { name: true, size: true, fileName: true },
      }),
      this.prisma.providedFile.findFirst({
        orderBy: { size: 'asc' },
        select: { name: true, size: true, fileName: true },
      }),
    ]);

    // 拡張子別の集計
    const byExtension: Record<string, number> = {};
    let totalSize = 0;

    files.forEach(file => {
      totalSize += file.size;
      const extension = file.fileName.split('.').pop()?.toLowerCase() || 'unknown';
      byExtension[extension] = (byExtension[extension] || 0) + 1;
    });

    const averageSize = totalCount > 0 ? Math.round(totalSize / totalCount) : 0;

    return {
      totalCount,
      totalSize,
      averageSize,
      byExtension,
      recentUpdates,
      oldFiles,
      largestFile,
      smallestFile,
    };
  }

  /**
   * 配列からランダムな要素を選択する
   * @param array 選択元の配列
   * @returns ランダムに選択された要素
   */
  private randomFromArray<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }
}
