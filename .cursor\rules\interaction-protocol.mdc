---
description: 
globs: 
alwaysApply: true
---
### ✅ Task: Interactive Task Loop with User Feedback

1. **Main Workflow**:

   * Perform your assigned tasks.

   * Run:

     ```python
     import readline; python -c 'p = input("请输入一个任务："); print(p)'
     ```
   * The terminal should be opened in the chat window itself.

   * Read the user's input.

   * Based on the input, perform the next set of tasks.

   * Repeat the process.

2. **Exit Condition**:

   * If the user enters `"stop"` when prompted, exit the loop and terminate the process.