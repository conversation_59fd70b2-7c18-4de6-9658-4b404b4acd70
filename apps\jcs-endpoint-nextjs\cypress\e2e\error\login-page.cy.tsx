describe("エラーハンドリングの一般的なテスト", () => {
  describe("ログイン画面", () => {
    it("未入力のままフォームを送信", () => {
      cy.visit("/login");
      cy.get("button").contains("ログイン").click();

      cy.get("input.border-red-500").should("have.length", 2);
      // cy.get(".transform-gpu").should("have.class", "hidden");
      cy.contains("ユーザIDもしくはパスワードに誤りがあります。").should(
        "be.visible",
      );
    });

    it("文字数が不足しているユーザーIDでのログイン", () => {
      cy.visit("/login");
      cy.get("#userId").type("invalid");
      cy.get("#password").type("invalidpassword");
      cy.get("button").contains("ログイン").click();

      cy.get("#userId.border-red-500").should("have.length", 1);
      // cy.get(".transform-gpu").should("have.class", "hidden");
      cy.contains("ユーザIDもしくはパスワードに誤りがあります。").should(
        "be.visible",
      );
    });

    it("文字数が不足しているパスワードでのログイン", () => {
      cy.visit("/login");
      cy.get("#userId").type("invaliduser");
      cy.get("#password").type("invalid");
      cy.get("button").contains("ログイン").click();

      cy.get("#password.border-red-500").should("have.length", 1);
      // cy.get(".transform-gpu").should("have.class", "hidden");
      cy.contains("ユーザIDもしくはパスワードに誤りがあります。").should(
        "be.visible",
      );
    });

    it("文字数が超過しているユーザーIDでのログイン", () => {
      cy.visit("/login");
      cy.get("#userId")
        .type("123456789012345678901234567890123456789012345678901")
        .should(
          "have.value",
          "12345678901234567890123456789012345678901234567890",
        );
      cy.get("#password").type("invalidpassword");
      cy.get("button").contains("ログイン").click();

      cy.get("#userId.border-red-500").should("have.length", 0);
      // cy.get(".transform-gpu").should("have.class", "hidden");
      cy.contains("ユーザIDもしくはパスワードに誤りがあります。").should(
        "be.visible",
      );
    });

    it("文字数が超過しているパスワードでのログイン", () => {
      cy.visit("/login");
      cy.get("#userId").type("invaliduser");
      cy.get("#password")
        .type(
          "123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789",
        )
        .should(
          "have.value",
          "12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678",
        );
      cy.get("button").contains("ログイン").click();

      cy.get("#password.border-red-500").should("have.length", 0);
      cy.get(".transform-gpu").should("have.class", "hidden");
    });

    it("大文字が含まれるユーザーIDでのログイン", () => {
      cy.visit("/login");
      cy.get("#userId").type("USER1234");
      cy.get("#password").type("invalidpassword");
      cy.get("button").contains("ログイン").click();

      cy.get("#userId.border-red-500").should("have.length", 1);
      // cy.get(".transform-gpu").should("have.class", "hidden");
      cy.contains("ユーザIDもしくはパスワードに誤りがあります。").should(
        "be.visible",
      );
    });

    it("記号「-」が含まれるユーザーIDでのログイン", () => {
      cy.visit("/login");
      cy.get("#userId").type("user-123");
      cy.get("#password").type("invalidpassword");
      cy.get("button").contains("ログイン").click();

      cy.get("#userId.border-red-500").should("have.length", 1);
      // cy.get(".transform-gpu").should("have.class", "hidden");
      cy.contains("ユーザIDもしくはパスワードに誤りがあります。").should(
        "be.visible",
      );
    });

    it("全角文字が含まれるユーザーIDでのログイン", () => {
      cy.visit("/login");
      cy.get("#userId").type("validuser漢字");
      cy.get("#password").type("invalidpassword");
      cy.get("button").contains("ログイン").click();

      cy.get("#userId.border-red-500").should("have.length", 1);
      // cy.get(".transform-gpu").should("have.class", "hidden");
      cy.contains("ユーザIDもしくはパスワードに誤りがあります。").should(
        "be.visible",
      );
    });

    it("全角文字が含まれるパスワードでのログイン", () => {
      cy.visit("/login");
      cy.get("#userId").type("validuser");
      cy.get("#password").type("invalidpassword漢字");
      cy.get("button").contains("ログイン").click();

      cy.get("#password.border-red-500").should("have.length", 1);
      // cy.get(".transform-gpu").should("have.class", "hidden");
      cy.contains("ユーザIDもしくはパスワードに誤りがあります。").should(
        "be.visible",
      );
    });

    it("無効なユーザーIDとパスワードでのログイン", () => {
      cy.visit("/login");
      cy.get("#userId").type("invaliduser");
      cy.get("#password").type("invalidpassword");
      cy.get("button").contains("ログイン").click();

      cy.contains("ユーザIDもしくはパスワードに誤りがあります。").should(
        "be.visible",
      );
    });

    it("環境無効のユーザーIDと無効なパスワードでのログイン", () => {
      cy.visit("/login");
      cy.get("#userId").type("wsst.taro.aa");
      cy.get("#password").type("invalidpassword");
      cy.get("button").contains("ログイン").click();

      cy.contains("現在ポータルは利用できません。").should("be.visible");
    });

    it("環境無効のユーザーIDと有効なパスワードでのログイン", () => {
      cy.visit("/login");
      cy.get("#userId").type("wsst.taro.aa");
      cy.get("#password").type("changeit!@#");
      cy.get("button").contains("ログイン").click();

      cy.contains("現在ポータルは利用できません。").should("be.visible");
    });

    it("10回続けて誤ったパスワードでのログイン後で、正しいパスワードを入力してログインする", () => {
      cy.visit("/login");
      cy.get("#userId").type("hitachi.hanako.ab");
      cy.get("#password").type("invalidpassword");
      cy.get("button").contains("ログイン").click();
      cy.wait(1000);
      cy.get("button").contains("ログイン").click();
      cy.wait(1000);
      cy.get("button").contains("ログイン").click();
      cy.wait(1000);
      cy.get("button").contains("ログイン").click();
      cy.wait(1000);
      cy.get("button").contains("ログイン").click();
      cy.wait(1000);
      cy.get("button").contains("ログイン").click();
      cy.wait(1000);
      cy.get("button").contains("ログイン").click();
      cy.wait(1000);
      cy.get("button").contains("ログイン").click();
      cy.wait(1000);
      cy.get("button").contains("ログイン").click();
      cy.wait(1000);
      cy.get("button").contains("ログイン").click();
      cy.wait(1000);
      cy.get("#password").clear().type("changeit!@#");
      cy.get("button").contains("ログイン").click();
      cy.wait(1000);

      cy.contains(
        "このユーザーIDはロックアウトされているため、ログインできません。",
      ).should("be.visible");
    });

    it("サーバーエラーが発生した場合、デフォルトメッセージを表示", () => {
      cy.intercept("POST", "/api/auth/callback/credentials", {
        statusCode: 500,
        body: "Server Error",
        headers: {
          "content-type": "text/plain",
        },
      });
      cy.visit("/login");
      cy.get("#userId").type("validuser");
      cy.get("#password").type("validpassword");
      cy.get("button").contains("ログイン").click();

      cy.contains("ユーザIDもしくはパスワードに誤りがあります。").should(
        "be.visible",
      );
    });

    it("お知らせはサーバーエラーが発生した場合", () => {
      cy.intercept("GET", "/api/notifications/system", {
        statusCode: 500,
        body: "Server Error",
        headers: {
          "content-type": "text/plain",
        },
      });
      cy.visit("/login");

      cy.contains("お知らせはありません。");
    });
  });
});
