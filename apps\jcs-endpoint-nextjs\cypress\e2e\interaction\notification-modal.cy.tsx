describe("画面操作のテスト", () => {
  describe("お知らせダイアログ", () => {
    const validCredentials = {
      userId: "hitachi.taro.aa",
      password: "changeit!@#",
    };

    beforeEach(() => {
      cy.visit("/login");
      cy.get("#userId").type(validCredentials.userId);
      cy.get("#password").type(validCredentials.password);
      cy.get("button").click();
    });

    it("閉じるボタンをクリックすると、ダイアログが閉じられる", () => {
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("お知らせ").click();
      cy.get("#notification-modal button").contains("閉じる").click();
      cy.get("#notification-modal").should("have.class", "hidden");
    });

    it("✖アイコンをクリックすると、ダイアログが閉じられる", () => {
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("お知らせ").click();
      cy.get("#notification-modal button svg").click();
      cy.get("#notification-modal").should("have.class", "hidden");
    });
  });
});
