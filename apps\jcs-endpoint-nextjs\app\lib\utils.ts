/**
 * @file utils.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

import { Lov } from "@prisma/client";
import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";
import { DATE_FORMATS } from "./definitions";

dayjs.extend(utc);
dayjs.extend(timezone);
/**
 * ページネーションを生成する関数
 * @param currentPage 現在のページ
 * @param totalPages ページ総数
 * @returns 表示文字列配列
 */
export const generatePagination = (currentPage: number, totalPages: number) => {
  // もしページ総数が7以下なら、省略記号なしで全てのページを表示
  if (totalPages <= 7) {
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }

  // もし現在のページが最初の3ページの中にあれば、最初の3ページ、省略記号、そして最後の2ページを表示
  if (currentPage <= 3) {
    return [1, 2, 3, "...", totalPages - 1, totalPages];
  }

  // もし現在のページが最後の3ページの中にあれば、最初の2ページ、省略記号、そして最後の3ページを表示
  if (currentPage >= totalPages - 2) {
    return [1, 2, "...", totalPages - 2, totalPages - 1, totalPages];
  }

  // もし現在のページが中間にあれば、最初のページ、省略記号、現在のページとその前後のページ、
  // 別の省略記号、そして最後のページを表示
  return [
    1,
    "...",
    currentPage - 1,
    currentPage,
    currentPage + 1,
    "...",
    totalPages,
  ];
};

/**
 * 値をラベルに変換する関数
 * @param value 値
 * @param lovs LOV配列
 * @returns ラベル
 */
export const castValueToLabel = (value: string, lovs: Lov[]) => {
  // 指定された値に対応するラベルのペアを検索
  const lovPair = lovs.find((lov) => lov.code === value);

  // 該当する場合はラベルを返す
  return lovPair?.value;
};

/**
 * バイト数をフォーマットする関数
 * @param bytes 実際のバイト数
 * @returns 人間が読みやすい文字列
 */
export function formatBytes(bytes: number): string {
  const k = 1024;
  const sizes = ["KB", "MB", "GB"];
  const removeTrailingZeros = (value: string): string => {
    return value.replace(/\.?0+$/, "");
  };

  if (bytes < k * k) {
    return removeTrailingZeros((bytes / k).toFixed(2)) + " " + sizes[0];
  } else if (bytes < k * k * k) {
    return removeTrailingZeros((bytes / (k * k)).toFixed(2)) + " " + sizes[1];
  } else {
    return (
      removeTrailingZeros((bytes / (k * k * k)).toFixed(2)) + " " + sizes[2]
    );
  }
}

/**
 * 時間をフォーマットする関数
 * @param date 実際の時間
 * @param tz 時間帯
 * @returns 「YYYY/MM/DD hh:mm:ss」形式文字列
 */
export function formatDate(date: Date, tz: string): string {
  return dayjs(date).tz(tz).format("YYYY/MM/DD HH:mm:ss");
}

/**
 * YYYY-MM-DD形式の日付をYYYY / MM / DD形式に変換
 * @param {string} dateStr - 変換対象の日付文字列（YYYY-MM-DD形式）
 * @returns {string} 変換後の日付文字列（YYYY / MM / DD形式）
 */
export function formatDateToSlashWithSpaces(dateStr: string): string {
  if (!dateStr) return "";
  return dayjs(dateStr).format(DATE_FORMATS.DISPLAY_WITH_SPACES);
}

/**
 * 現在時刻をユーザーのタイムゾーンでYYYYMMDDHHmmss形式にフォーマットする関数
 * @param tz ユーザーのタイムゾーン
 * @returns YYYYMMDDHHmmss形式の文字列
 */
export function formatTimestampForTaskName(tz: string): string {
  return dayjs().tz(tz).format("YYYYMMDDHHmmss");
}

/**
 * YYYY-MM-DD形式の日付文字列を厳密に検証する関数
 * @param dateString 検証する日付文字列
 * @returns 有効な場合はtrue、無効な場合はfalse
 */
export function isValidDateFormat(dateString: string): boolean {
  // YYYY-MM-DD形式の正規表現
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;

  if (!dateRegex.test(dateString)) {
    return false;
  }

  // 実際の日付として有効かチェック
  const date = new Date(dateString + 'T00:00:00.000Z');
  const isoString = date.toISOString().split('T')[0];

  return isoString === dateString;
}

/**
 * データを取得するfetcher関数
 * @param url リモートアドレス
 * @returns 取得したデータ
 */
export const fetcher = async (url: string) => {
  // URLにリクエストを送信し、レスポンスを取得
  const response: Response = await fetch(url);

  // レスポンスのデータをJSON形式で取得
  const data = await response.json();

  // 取得したデータを返す
  return data;
};

/**
 * メッセージをフォーマットする関数
 * 
 * @param template - メッセージテンプレート（例："{0}が正しくありません。"）
 * @param args - 置換する値の配列
 * @returns フォーマットされたメッセージ
 * 
 * @example
 * ```typescript
 * formatMessage("{0}が{1}です。", ["パスワード", "無効"]);
 * // => "パスワードが無効です。"
 * ```
 */
export function formatMessage(template: string, args: string[]): string {
  return template.replace(/\{(\d+)\}/g, (match, index) => {
    return args[parseInt(index)] ?? match;
  });
}

/**
 * セキュアな一意識別子を生成する関数
 *
 * MR-01セキュリティ要件に準拠し、Math.random()を使用せずに
 * 暗号学的に安全な乱数を生成する。環境に応じて最適な方法を選択する。
 *
 * @param isServerSide - サーバーサイド環境かどうか（デフォルト: false）
 * @returns セキュアな一意識別子文字列
 *
 * @example
 * ```typescript
 * // クライアントサイドでの使用
 * const clientId = generateSecureId();
 *
 * // サーバーサイドでの使用
 * const serverId = generateSecureId(true);
 * ```
 */
export function generateSecureId(isServerSide: boolean = false): string {
  if (isServerSide) {
    // サーバーサイド: Node.js crypto.randomUUID()を使用
    try {
      const { randomUUID } = require('crypto');
      return randomUUID();
    } catch (error) {
      // フォールバック: タイムスタンプベース
      return `server-${Date.now()}-${process.hrtime.bigint()}`;
    }
  } else {
    // クライアントサイド: ブラウザのWeb Crypto APIを使用
    if (typeof window !== 'undefined' && window.crypto) {
      if (window.crypto.randomUUID) {
        // モダンブラウザ: Web Crypto API UUID
        return window.crypto.randomUUID();
      } else if (window.crypto.getRandomValues) {
        // 古いブラウザ: Web Crypto API getRandomValues
        const array = new Uint32Array(4);
        window.crypto.getRandomValues(array);
        return Array.from(array, dec => dec.toString(16)).join('');
      }
    }

    // 最終フォールバック: タイムスタンプベース（Math.random()は使用しない）
    return `fallback-${Date.now()}-${performance.now()}`;
  }
}
