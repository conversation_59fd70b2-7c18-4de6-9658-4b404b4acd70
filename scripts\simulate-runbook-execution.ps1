# simulate-runbook-execution.ps1
#
# このスクリプトは、Azure Automationで実行されるRunbookの動作をシミュレートします。
# RunbookProcessorFuncの統合テスト用に、実際のRunbookの代わりに使用できます。
#
# 使用方法:
# .\simulate-runbook-execution.ps1 -taskId "task-123" -targetContainerName "container-name" [-exportStartDate "2025-01-01"] [-exportEndDate "2025-01-31"] [-importedFileBlobPath "path/to/file"]
#
# 例:
# .\simulate-runbook-execution.ps1 -taskId "task-123" -targetContainerName "container1" -exportStartDate "2025-01-01" -exportEndDate "2025-01-31"
# .\simulate-runbook-execution.ps1 -taskId "task-456" -targetContainerName "container2"
# .\simulate-runbook-execution.ps1 -taskId "task-789" -targetContainerName "container3" -importedFileBlobPath "license123/imports/task-789/assetsfield_def.csv"

param (
    [Parameter(Mandatory=$true)]
    [string]$taskId,
    
    [Parameter(Mandatory=$true)]
    [string]$targetContainerName,
    
    [Parameter(Mandatory=$false)]
    [string]$exportStartDate,
    
    [Parameter(Mandatory=$false)]
    [string]$exportEndDate,
    
    [Parameter(Mandatory=$false)]
    [string]$importedFileBlobPath
)

# 挂载的Azure Files路径（Z盘）
$baseWorkspacePath = "Z:\"

# タスクIDごとの作業ディレクトリパス
$taskWorkspacePath = Join-Path -Path $baseWorkspacePath -ChildPath $taskId

# exportsディレクトリパス
$exportsPath = Join-Path -Path $taskWorkspacePath -ChildPath "exports"

# importsディレクトリパス
$importsPath = Join-Path -Path $taskWorkspacePath -ChildPath "imports"

# タスク種別を推定する関数
function DetermineTaskType {
    if ($exportStartDate -and $exportEndDate) {
        return "OPLOG_EXPORT"
    } elseif ($importedFileBlobPath) {
        return "MGMT_ITEM_IMPORT"
    } else {
        return "MGMT_ITEM_EXPORT"
    }
}

# ディレクトリ構造を作成
function CreateDirectoryStructure {
    Write-Host "作業ディレクトリ構造を作成中..."
    
    # TaskWorkspaces/<TaskId>/ ディレクトリを作成
    if (-not (Test-Path -Path $taskWorkspacePath)) {
        New-Item -Path $taskWorkspacePath -ItemType Directory -Force | Out-Null
    }
    
    # TaskWorkspaces/<TaskId>/exports/ ディレクトリを作成
    if (-not (Test-Path -Path $exportsPath)) {
        New-Item -Path $exportsPath -ItemType Directory -Force | Out-Null
    }
    
    # TaskWorkspaces/<TaskId>/imports/ ディレクトリを作成
    if (-not (Test-Path -Path $importsPath)) {
        New-Item -Path $importsPath -ItemType Directory -Force | Out-Null
    }
    
    Write-Host "ディレクトリ構造の作成が完了しました。"
}

# 操作ログエクスポートRunbookをシミュレート
function SimulateOplogExportRunbook {
    Write-Host "操作ログエクスポートRunbookを実行中..."
    Write-Host "  タスクID: $taskId"
    Write-Host "  対象コンテナ: $targetContainerName"
    Write-Host "  エクスポート期間: $exportStartDate から $exportEndDate"
    
    # 処理時間をシミュレート
    $processingTime = Get-Random -Minimum 2 -Maximum 6
    Write-Host "  処理中... ($processingTime 秒)"
    Start-Sleep -Seconds $processingTime
    
    # 複数のZIPファイルを生成（exportoplog_*.zip）
    $fileCount = Get-Random -Minimum 1 -Maximum 4  # 1～3個のファイルをランダムに生成
    
    for ($i = 1; $i -le $fileCount; $i++) {
        $zipFileName = "exportoplog_$i.zip"
        $zipFilePath = Join-Path -Path $exportsPath -ChildPath $zipFileName
        
        # ZIPファイルの内容を生成（ダミーファイルを含むZIP）
        $tempDir = Join-Path -Path $env:TEMP -ChildPath ([System.Guid]::NewGuid().ToString())
        New-Item -Path $tempDir -ItemType Directory -Force | Out-Null
        
        # ダミーのログファイルを作成
        $dummyLogPath = Join-Path -Path $tempDir -ChildPath "operation_log_$i.log"
        $logContent = @"
===== 操作ログ サンプル $i =====
タスクID: $taskId
コンテナ名: $targetContainerName
エクスポート期間: $exportStartDate から $exportEndDate
タイムスタンプ: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
ユーザー: user$i
アクション: サーバー設定変更
詳細: CPU設定を2コアから4コアに変更
ステータス: 成功
"@
        Set-Content -Path $dummyLogPath -Value $logContent -Encoding UTF8
        
        # ZIPファイルを作成
        Compress-Archive -Path "$tempDir\*" -DestinationPath $zipFilePath -Force
        
        # 一時ディレクトリを削除
        Remove-Item -Path $tempDir -Recurse -Force
        
        Write-Host "  - $zipFileName を作成しました。"
    }
    
    Write-Host "操作ログエクスポートRunbookの実行が完了しました。"
}

# 管理項目定義エクスポートRunbookをシミュレート
function SimulateMgmtItemExportRunbook {
    Write-Host "管理項目定義エクスポートRunbookを実行中..."
    Write-Host "  タスクID: $taskId"
    Write-Host "  対象コンテナ: $targetContainerName"
    
    # 処理時間をシミュレート
    $processingTime = Get-Random -Minimum 2 -Maximum 5
    Write-Host "  処理中... ($processingTime 秒)"
    Start-Sleep -Seconds $processingTime
    
    # 固定ファイル名 assetsfield_def.csv を生成
    $csvFilePath = Join-Path -Path $exportsPath -ChildPath "assetsfield_def.csv"
    
    # CSVファイルの内容を生成
    $csvContent = @"
"項目ID","項目名","データ型","必須","デフォルト値","備考"
"server_id","サーバーID","string","true","","サーバーの一意識別子"
"server_name","サーバー名","string","true","","サーバーの表示名"
"ip_address","IPアドレス","string","true","","サーバーのIPアドレス"
"os_type","OS種別","string","true","Windows","Windows/Linux"
"cpu_cores","CPUコア数","number","true","2","物理CPUコア数"
"memory_gb","メモリ容量(GB)","number","true","8","物理メモリ容量"
"disk_gb","ディスク容量(GB)","number","true","100","ディスク総容量"
"status","ステータス","string","true","active","active/inactive/maintenance"
"created_at","作成日時","datetime","true","","YYYY-MM-DD HH:MM:SS形式"
"updated_at","更新日時","datetime","true","","YYYY-MM-DD HH:MM:SS形式"
"container_name","コンテナ名","string","true","$targetContainerName","タスク実行コンテナ名"
"task_id","タスクID","string","true","$taskId","実行タスクID"
"@
    
    Set-Content -Path $csvFilePath -Value $csvContent -Encoding UTF8
    
    Write-Host "  - assetsfield_def.csv を作成しました。"
    Write-Host "管理項目定義エクスポートRunbookの実行が完了しました。"
}

# 管理項目定義インポートRunbookをシミュレート
function SimulateMgmtItemImportRunbook {
    Write-Host "管理項目定義インポートRunbookを実行中..."
    Write-Host "  タスクID: $taskId"
    Write-Host "  対象コンテナ: $targetContainerName"
    Write-Host "  インポートファイルパス: $importedFileBlobPath"
    
    # インポートファイルの存在確認
    $importFilePath = Join-Path -Path $importsPath -ChildPath "assetsfield_def.csv"
    
    if (-not (Test-Path -Path $importFilePath)) {
        # インポートファイルが存在しない場合、エラーファイルを作成
        $errorFilePath = Join-Path -Path $exportsPath -ChildPath "errordetail.txt"
        $errorContent = "インポートファイル assetsfield_def.csv が見つかりません。"
        Set-Content -Path $errorFilePath -Value $errorContent -Encoding UTF8
        
        Write-Host "  エラー: $errorContent"
        Write-Host "  - errordetail.txt を作成しました。"
        Write-Error $errorContent
        exit 1
    }
    
    # 処理時間をシミュレート
    $processingTime = Get-Random -Minimum 3 -Maximum 7
    Write-Host "  処理中... ($processingTime 秒)"
    Start-Sleep -Seconds $processingTime
    
    # 成功を示すマーカーファイルを作成
    $markerFilePath = Join-Path -Path $exportsPath -ChildPath "import_success_marker.txt"
    
    $markerContent = @"
管理項目定義インポート成功
タスクID: $taskId
コンテナ名: $targetContainerName
インポートファイルパス: $importedFileBlobPath
処理日時: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
"@
    
    Set-Content -Path $markerFilePath -Value $markerContent -Encoding UTF8
    
    Write-Host "  - インポート成功マーカーファイルを作成しました。"
    Write-Host "管理項目定義インポートRunbookの実行が完了しました。"
}

# メイン処理
try {
    # タスク種別を推定
    $taskType = DetermineTaskType
    
    # パラメータ情報を表示
    Write-Host "===== Runbook実行シミュレーション開始 ====="
    Write-Host "タスクID: $taskId"
    Write-Host "推定タスク種別: $taskType"
    Write-Host "対象コンテナ: $targetContainerName"
    
    if ($taskType -eq "OPLOG_EXPORT") {
        Write-Host "エクスポート開始日: $exportStartDate"
        Write-Host "エクスポート終了日: $exportEndDate"
    } elseif ($taskType -eq "MGMT_ITEM_IMPORT") {
        Write-Host "インポートファイルパス: $importedFileBlobPath"
    }
    
    Write-Host ""
    
    # ディレクトリ構造を作成
    CreateDirectoryStructure
    
    # タスク種別に応じた処理
    switch ($taskType) {
        "OPLOG_EXPORT" {
            if ([string]::IsNullOrEmpty($exportStartDate) -or [string]::IsNullOrEmpty($exportEndDate)) {
                Write-Error "操作ログエクスポートには、exportStartDateとexportEndDateが必要です。"
                exit 1
            }
            SimulateOplogExportRunbook
        }
        "MGMT_ITEM_EXPORT" {
            SimulateMgmtItemExportRunbook
        }
        "MGMT_ITEM_IMPORT" {
            SimulateMgmtItemImportRunbook
        }
    }
    
    Write-Host ""
    Write-Host "===== Runbook実行シミュレーション完了 ====="
    Write-Host "作業ディレクトリ: $taskWorkspacePath"
}
catch {
    Write-Error "エラーが発生しました: $_"
    
    # エラー詳細ファイルを作成
    $errorFilePath = Join-Path -Path $exportsPath -ChildPath "errordetail.txt"
    $errorContent = "Runbook実行中にエラーが発生しました: $_"
    Set-Content -Path $errorFilePath -Value $errorContent -Encoding UTF8
    
    Write-Host "  - errordetail.txt を作成しました。"
    exit 1
}