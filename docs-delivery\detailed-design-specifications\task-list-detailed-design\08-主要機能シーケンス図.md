### 3.8 主要機能シーケンス図

本章では、タスク一覧機能における主要な操作シーケンスを示す。図中のエラー応答に示されるメッセージIDは、本設計書「エラーメッセージ一覧」で定義される。

#### 3.8.1 タスク一覧 データ表示シーケンス図

本図は、ユーザーがタスク一覧画面にアクセスした際、または画面上でフィルタリング、ページネーション、ソート等の操作を行った際の、基本的なデータ表示フローの概要を示す。本機能では、パフォーマンス向上のため、関連タスク全件を一度取得しキャッシュに格納後、メモリ上でフィルタリング等の処理を行う。

```mermaid
sequenceDiagram
    title タスク一覧 データ表示シーケンス図

    actor User as ユーザー
    participant Frontend as フロントエンド
    participant Backend as バックエンド
    participant Database as データベース

    User->>Frontend: 1. タスク一覧画面へアクセス<br/>(または操作によりURLパラメータ変更)
    activate Frontend
    
    Note over Backend: URLパラメータがNext.js App Routerを<br/>介してサーバコンポーネントへ渡される
    Frontend->>Backend: 2. タスク一覧データ要求
    activate Backend
    Backend->>Backend: 3. URLパラメータ解析
    Backend->>Database: 4. 条件に基づきタスク情報取得
    activate Database
    Database-->>Backend: 5. タスク情報返却
    deactivate Database
    Backend->>Backend: 6. ステータス、タスク種別等を日本語表示へ変換（Lov参照）
    Backend-->>Frontend: 7. 描画用タスク一覧データ返却
    deactivate Backend
    Frontend->>User: 8. タスク一覧情報を画面に表示
    deactivate Frontend
```

#### 3.8.2 タスク中止要求シーケンス図

本図は、ユーザーがタスク一覧画面から「実行待ち」状態のタスクに対して「中止」操作を行う際の主要なインタラクションを示す。

```mermaid
sequenceDiagram
    title タスク中止要求受付処理シーケンス図

    actor User as ユーザー
    participant Frontend as フロントエンド
    participant Backend as バックエンド
    participant Database as データベース
    participant MessageQueue as Azure Service Busキュー

    User->>Frontend: 特定タスクの「中止」ボタンクリック
    activate Frontend
    Frontend->>User: 中止確認モーダル表示
    User->>Frontend: 中止確認モーダルで「OK」クリック
    Frontend->>Backend: タスク中止要求<br/>(ServerAction: requestTaskCancellation)<br/>(パラメータ: taskId)
    activate Backend
    Backend->>Backend: 入力パラメータ検証
    alt 検証失敗
        Backend-->>Frontend: エラー応答(success: false, message: EMEC0021)
    else 検証成功
        Backend->>Database: Taskテーブルから対象タスク情報取得
        activate Database
        Database-->>Backend: タスク情報返却
        deactivate Database
        alt 取得失敗
            Backend-->>Frontend: エラー応答(success: false, message: EMEC0021)
        else 取得成功
            alt タスクステータスがQUEUED
                Backend->>Database: タスクステータスをPENDING_CANCELLATIONへ更新
                activate Database
                Database-->>Backend: DB更新結果
                deactivate Database
                alt DB更新失敗
                    Backend-->>Frontend: エラー応答(success: false, message: EMEC0021)
                else DB更新0件
                    Backend-->>Frontend: エラー応答(success: false, message: EMEC0023)
                else DB更新成功
                    Backend->>MessageQueue: 中止要求メッセージ送信<br/>(TaskControlQueueへ)
                    activate MessageQueue
                    MessageQueue-->>Backend: 送信結果
                    deactivate MessageQueue
                    alt メッセージ送信失敗
                        Backend->>Database: タスクステータスをQUEUEDへ更新
                        activate Database
                        Database-->>Backend: DB更新結果
                        deactivate Database
                        Backend-->>Frontend: エラー応答(success: false, message: EMEC0019)
                    else メッセージ送信成功
                        Backend-->>Frontend: 成功応答(success: true, message: EMEC0026)
                    end
                end
            else タスクステータスがQUEUED以外
                Backend-->>Frontend: エラー応答(success: false, message: EMEC0023)
            end
            
        end
    end
    deactivate Backend

    Frontend->>User: 応答に基づき通知モーダル表示
    alt 成功応答 (EMEC0026)
        Frontend->>Frontend: 一覧画面更新 (router.refresh)
    end
    deactivate Frontend
```

#### 3.8.3 管理項目定義エクスポートタスク成果物ダウンロードシーケンス図

本図は、ユーザーが「正常終了」した「管理項目定義のエクスポート」タスクの「ダウンロード」リンクをクリックした際の、ファイルダウンロード処理の概要を示す。

```mermaid
sequenceDiagram
    title 管理項目定義エクスポートタスク成果物ダウンロードシーケンス図

    actor User as ユーザー
    participant Frontend as フロントエンド
    participant Backend as バックエンド (API Route)
    participant BlobStorage as Blobストレージ

    User->>Frontend: 「ダウンロード」リンククリック<br/>(対象タスクのtaskId含むURL)
    activate Frontend
    Frontend->>Backend: ファイルダウンロード要求<br/>(HTTP GET /dashboard/tasks/{taskId}/download)
    
    activate Backend
        Backend->>Backend: 環境変数、ライセンスID、タスクIDでBlobパス取得
        Backend->>BlobStorage: SAS URL生成要求
        activate BlobStorage
        BlobStorage-->>Backend: SAS URL返却
        deactivate BlobStorage
        Backend-->>Frontend: HTTPリダイレクト (Location: SAS URL)
        deactivate Backend

        Frontend->>BlobStorage: (リダイレクトに基づき) ファイル取得要求 (SAS URL)
        activate BlobStorage
        BlobStorage-->>Frontend: ファイルデータ (assetsfield_def.csv)
        deactivate BlobStorage
        Frontend->>User: ファイルダウンロード開始
    deactivate Frontend
```
