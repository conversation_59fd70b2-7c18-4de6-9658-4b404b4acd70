# 数据模型: 用户 (User)

*   **源表名 (原规范)**: `[ユーザー] (User)`
*   **主要用途**: (02-20版本前) 存储门户用户的基本信息、认证相关信息及状态。 (02-30版本后) 此表不再存储实际数据，用户信息由 Keycloak 管理，此表仅为兼容性目的存在。

## 字段定义 (02-20 版本前)

| 字段名 (日文推测/英文) | 中文名           | 数据类型      | 约束/备注                                                                   |
| :----------------------- | :--------------- | :------------ | :-------------------------------------------------------------------------- |
| `userId`                 | 用户ID           | VARCHAR(50)   | 主键。8-50位小写半角英数字和点号。                                             |
| `passwordHash`           | 密码哈希         | VARCHAR(255)  | 加密存储的用户密码。                                                          |
| `contractId` / `licenseId` | 契约ID/许可证ID  | VARCHAR(XX)   | 用户关联的契约或许可证ID。                                                    |
| `passwordLastUpdated`    | 密码最后更新时间 | DATETIME      |                                                                             |
| `loginFailureCount`      | 登录失败次数     | INT           |                                                                             |
| `lockoutEndTimestamp`    | 锁定结束时间     | DATETIME      | 账户锁定时，记录解锁时间。                                                      |
| `createdAt`              | 创建时间         | DATETIME      | 记录创建时间 (推测)。                                                         |
| `updatedAt`              | 更新时间         | DATETIME      | 记录最后更新时间 (推测)。                                                       |

**重要说明 (根据 `func-spec.md` 5.1.1):**

*   **【02-20まで】(到02-20版本为止)**: 此表用于存储用户认证信息。
    *   一个许可证可以关联多个用户。
    *   一个用户可以关联多个通知。
    *   锁定结束时间的更新逻辑：
        1.  登录失败次数达到最大时，更新此时间为当前时间。
        2.  锁定时间超过最大允许时间且用户成功登录时，清除此时间。
*   **【02-30以降】(02-30版本以后)**:
    *   **此表不再存储数据 (データを格納しない)。**
    *   此表的存在仅为与02-20版本的兼容性。
    *   用户信息（用户ID、密码、MFA设置等）完全由 **Keycloak** 进行存储和管理。
    *   服务运营者在02-30版本后新增用户时，应在 Keycloak 中注册，不应向此表写入数据。
    *   02-20版本前存储在此表的用户数据，应由服务运营者迁移到 Keycloak，并删除此表中的信息，以防止用户数据多重管理。
    *   门户程序在02-30版本后，不对此表进行任何操作（增删改查）。用户信息从 Keycloak 获取。如果需要使用此表信息作为外键关联其他表，应使用从 Keycloak 获取的 `userId` 和 `licenseId`。

## 关系 (02-20 版本前)

*   **`License`**: 多对一 (一个 `User` 属于一个 `License`，一个 `License` 可以有多个 `User`)。
*   **`Notification`**: 一对多 (一个 `User` 可以有多个特定于用户的 `Notification`)。

## 索引示例 (02-20 版本前)

*   主键: `userId`
*   外键/索引: `contractId` / `licenseId`