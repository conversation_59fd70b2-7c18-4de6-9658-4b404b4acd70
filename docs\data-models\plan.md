# 数据模型: 契约计划信息 (Plan)

*   **表名 (逻辑名)**: `Plan`
*   **对应UI界面**: N/A (通常在用户管理、计费或服务开通等后台管理界面使用，用户不直接操作此表)
*   **主要用途**: 存储客户可以订阅的各种契约计划（或称服务套餐）的基本信息。

## 1. 字段定义

| 字段名 (Prisma/英文) | 数据类型 (SQL参考) | PK | FK | UQ | Nullable | Default  | 描述 (中文)                                                     |
| :------------------- | :----------------- | :--- | :--- | :--- | :------- | :------- | :-------------------------------------------------------------- |
| `id`                 | VARCHAR(36)        | ●    |      |      |          | `cuid()` | 主键。CUID格式，由系统自动生成。                                    |
| `name`               | NVARCHAR(255)      |      |      |      |          |          | 契约计划的名称。例如："标准版", "高级版", "企业版"。对应原「機能仕様書」中的`planName`。 |
| `planId`             | VARCHAR(XX)        |      |      | ●    |          |          | 契约计划的业务ID。在系统中唯一标识一个契约计划，用于业务逻辑处理。          |

*PK: Primary Key, FK: Foreign Key, UQ: Unique Key*
*XX表示具体长度，取决于实际的数据库Schema定义。*

## 2. 关系

*   **对 `PlanProduct` (`planProducts`)**: 一对多关系 (`PlanProduct[]`)。一个契约计划可以包含多种可用的产品媒体。
*   **对 `PlanManual` (`planManuals`)**: 一对多关系 (`PlanManual[]`)。一个契约计划可以包含多种可用的产品手册。
*   **对 `PlanProvidedFile` (`PlanProvidedFiles`)**: 一对多关系 (`PlanProvidedFile[]`)。一个契约计划可以包含多种可用的提供文件。
*   **对 `Notification` (`notifications`)**: 一对多关系 (`Notification[]`)。一个契约计划可以关联多条特定于此计划的通知。
*   **对 `PlanSupport` (`planSupports`)**: 一对多关系 (`PlanSupport[]`)。一个契约计划可以包含多种可用的支持信息。
*   **对 `LicensePlan` (`licensePlans`)**: 一对多关系 (`LicensePlan[]`)。用于实现 `Plan` 与 `License` 之间的多对多关联。一个契约计划可以通过 `LicensePlan` 表关联到多个许可证。

## 3. 索引 (示例)

*   `PRIMARY KEY (id)`
*   `UNIQUE KEY (planId)` (确保契约计划业务ID的唯一性，Prisma Schema已定义)
*   `INDEX idx_plan_name (name)` (如果经常按计划名称查询)