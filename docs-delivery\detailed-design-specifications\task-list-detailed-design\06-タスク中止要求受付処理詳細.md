## タスク中止要求受付処理詳細 (requestTaskCancellation Server Action)

本章では、ポータル画面のタスク一覧から発行されるバックグラウンドタスクの中止要求を処理するサーバサイドロジックである Server Actionの requestTaskCancellation の詳細な仕様について記述する。

### 概要

#### 責務
requestTaskCancellationは、クライアントからの特定タスクの中止要求を受け付け、対象タスクの現在のステータスを検証し、中止が可能であればデータベース内のタスクステータスを更新し、後続の非同期中止処理（TaskCancellationFuncによるステータス変更）のためのメッセージをService Busへ送信する。主な責務は以下の通りである。

1.  入力パラメータ（中止対象のタスクID）を解析・検証する。
2.  データベースから対象タスクの現在のステータスを取得する。
3.  タスクの現在のステータスに基づき、中止処理の可否を判断する。
    ・「実行待ち」(QUEUED) の場合：タスクステータスを「中止待ち」(PENDING_CANCELLATION) に更新し、中止要求メッセージを TaskControlQueue へ送信する。受付完了の応答を返す。
    ・「実行待ち」(QUEUED) 以外の場合：中止不可である旨の応答を返す。
4.  処理結果（成功、失敗、対応するメッセージ）をクライアントへ返却する。詳細な技術的エラー情報は、サーバサイドのログシステムに記録される。

#### 入力パラメータ
本サーバアクションは、クライアントから以下の入力パラメータを引数として受け取る。

| パラメータ名 | データ型 | 説明 |
|:---|:---|:---|
| taskId | 文字列 | 中止対象のタスクの一意なID (Task.id)。 |

#### 返り値
本サーバアクションは、Promiseオブジェクトを返却し、そのPromiseは処理结果を含む以下の構造のオブジェクト (TaskActionResult) で解決（resolve）される。

| フィールド名 | データ型 | 説明 |
|:---|:---|:---|
| success | bool | タスク中止要求がサーバサイドで正常に受け付けられ、必要なステータス更新とメッセージ送信が行われたかを示す。trueは受付完了で、falseはタスクステータスの不適合や本サーバアクションの処理中のエラーなどで受付失敗したことを示す。 |
| message | 文字列 | 実際にユーザーへ表示するメッセージ内容。 |

### 共通処理フローとロジック

以下に、requestTaskCancellation がリクエストを受信した後の共通処理フローを示す。

```mermaid
graph TD
    A["開始: Server Action (requestTaskCancellation)"] --> B["ステップ1: 入力パラメータ検証<br/>(taskId)"];
    B -- "検証失敗" --> ZFailInput["エラー応答返却<br/>(messageKey: EMEC0021 - 汎用)"];
    B -- "検証成功" --> C["ステップ2: ユーザーセッション検証"];
    C -- "検証失敗" --> ZFailAuth["エラー応答返却<br/>(messageKey: EMEC0021)"];
    C -- "検証成功" --> D["ステップ3: 対象タスク情報取得<br/>(DBよりtaskId使用)"];
    D -- "タスク取得失敗<br/>(DBエラーまたはタスク不存在)" --> ZFailDbRead["エラー応答返却<br/>(messageKey: EMEC0021)"];
    D -- "タスク取得成功" --> E["ステップ4: タスク操作権限検証<br/>(契約ID一致確認)"];
    E -- "権限なし" --> ZFailPermission["エラー応答返却<br/>(messageKey: EMEC0021)"];
    E -- "権限あり" --> F{"ステップ5: タスク状態分岐処理"};
    F -- "状態: QUEUED" --> G["ステップ5.1: Task状態をPENDING_CANCELLATIONへ更新<br/>(DB更新)"];
    G -- "DB更新失敗" --> ZFailDbUpdate["エラー応答返却<br/>(messageKey: EMEC0021)"];
    G -- "DB更新成功" --> H["ステップ5.1.1: 中止メッセージを<br/>TaskControlQueueへ送信"];
    H -- "送信失敗" --> ZFailQueue["エラー応答返却<br/>(messageKey: EMEC0019)<br/>※Task状態ロールバック試行"];
    H -- "送信成功" --> I_SuccessQueued["成功応答返却<br/>(messageKey: EMEC0026,<br/>updatedTaskStatus: PENDING_CANCELLATION)"];
    F -- "状態: PENDING_CANCELLATION<br/>または CANCELLED" --> J_SuccessAlready["成功応答返却 (冪等)<br/>(messageKey: EMEC0026)"];
    F -- "状態: その他<br/>(RUNBOOK_SUBMITTED,<br/>RUNBOOK_PROCESSING,<br/>COMPLETED_SUCCESS,<br/>COMPLETED_ERROR等)" --> K_FailCannotCancel["エラー応答返却<br/>(messageKey: EMEC0023)"];
    F -- "状態: 不明/予期せぬ値" --> L_FailUnknownState["エラー応答返却<br/>(messageKey: EMEC0019 - 汎用)"];

    ZFailInput --> X["終了"];
    ZFailAuth --> X;
    ZFailDbRead --> X;
    ZFailPermission --> X;
    ZFailDbUpdate --> X;
    ZFailQueue --> X;
    I_SuccessQueued --> X;
    J_SuccessAlready --> X;
    K_FailCannotCancel --> X;
    L_FailUnknownState --> X;
```

**■ 共通処理フロー詳細**

1.  **入力パラメータ検証:**
    入力パラメータの taskId が存在しているか、空でないか確認する。検証失敗時は、{ success: false, message:EMEC0021「サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」（{0}は「中止」で置換）}を返却する。エラー詳細をログに出力して、処理を終了する。

2.  **対象タスク情報取得**
    taskId を使用して、データベースの Task テーブルから該当するタスクのステータス、タスク名、最終更新日時を取得する。
    ・タスクレコードが存在しない、またはデータベース読み取り時にエラーが発生した場合は、{ success: false, message:EMEC0021「サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」（{0}は「中止」で置換）}を返却する。エラー詳細をログに出力して、処理を終了する。

3.  **タスクステータス分岐処理**
    取得した Task.status に基づき、以下の処理を行う。

    ・ステータスが QUEUED (実行待ち) の場合
    1.  データベースの該当 Task レコードの status を PENDING_CANCELLATION (中止待ち) に更新する。タスクIDが一致、及び最終更新日時がステップ2.で取得した日時と一致することを条件とする。
        *   データベース更新時にエラーが発生した場合は、{ success: false, message:EMEC0021「サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」（{0}は「中止」で置換）}を返却する。エラー詳細をログに出力して、処理を終了する。
        *   更新した件数が0件の場合は、{ success: false, message:EMEC0023「タスクの中止はできません。タスクは他のユーザーによって中止操作が行われたか、すでに実行中か、もしくは実行が終了しています。」}を返却する。エラー詳細をログに出力して、処理を終了する。
    2.  Service Bus の TaskControlQueue (環境変数 SERVICE_BUS_TASK_CONTROL_QUEUE_NAME で指定) へ、{ taskId: 入力パラメータのタスクID } を含むメッセージを送信する。
        *   メッセージ送信時にエラーが発生した場合は、データベースの該当 Task レコードの status を QUEUED (実行待ち) に更新する（戻す）。{ success: false, message:EMEC0019「サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0019)」（{0}は「中止」で置換）}を返却する。エラー詳細をログに出力して、処理を終了する。
    3.  上記処理が全て成功した場合、{ success: true, message:EMEC0026「タスクの中止を受け付けました。タスクのステータスはタスク一覧画面で確認してください。\nタスク名：{0}」（{0}はステップ2.で取得したタスク名Task.taskNameで置換）}を返却する。

    ・ステータスが QUEUED (実行待ち) 以外の場合
    タスクは他のユーザーによって先に中止操作が行われたか、既に実行開始したか、または終了（正常/エラー問わず）したかのため、データベースの更新とService Busへの送信は行わず、{ success: false, message:EMEC0023「タスクの中止はできません。タスクは他のユーザーによって中止操作が行われたか、すでに実行中か、もしくは実行が終了しています。」}を返却する。

### エラー処理メカニズム

*   各処理ステップでの業務ロジックエラー時または処理成功時は、処理結果のsuccessフィールドと対応するメッセージ内容のmessageフィールドが含まれるTaskActionResultオブジェクトを構築し返却する。
*   予期せぬ内部例外発生時は、{ success: false, message:EMEC0027「サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0027)」（{0}は「中止」で置換）}を返却する。