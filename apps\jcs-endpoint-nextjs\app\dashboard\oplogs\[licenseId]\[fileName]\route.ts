/**
 * @file route.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2024, 2025, Hitachi Solutions, Ltd.
 */

import { BlobActions } from "@/app/lib/integrations/azure-blob";
import { ServerDataLov } from "@/app/lib/data/lov";
import {
  LOV_CODE_AZURE_STORAGE_CONTAINER_OPLOGS,
  PORTAL_ERROR_MESSAGES
} from "@/app/lib/definitions";
import Logger from "@/app/lib/logger";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export async function GET(
  request: Request,
  { params }: { params: { licenseId: string; name: string; fileName: string } },
) {
  let blobUrlWithSAS;
  const { licenseId, name, fileName } = params;

  try {
    const session = await getIronSession<SessionData>(cookies(), sessionOptions);

    if (!session || session.user.licenseId !== licenseId) {
      Logger.error({
        message: `ディレクトリ[/${licenseId}]へのアクセス権がありません。`,
      });

      return new Response(PORTAL_ERROR_MESSAGES.EMEC0007, {
        status: 500,
      });
    }

    const containerLov = await ServerDataLov.fetchLov(
      LOV_CODE_AZURE_STORAGE_CONTAINER_OPLOGS,
    );

    if (!containerLov) {
      Logger.error({
        message: `LOVでコンテナ設定[${LOV_CODE_AZURE_STORAGE_CONTAINER_OPLOGS}]が見つかりません。`,
      });

      return new Response(PORTAL_ERROR_MESSAGES.EMEC0007, { status: 500 });
    }

    blobUrlWithSAS = await BlobActions.generateBlobUrlWithSAS(
      containerLov.value,
      [licenseId, name, fileName],
    );
  } catch (error: any) {
    Logger.error({ message: error.message, stack: error.stack });

    return new Response(PORTAL_ERROR_MESSAGES.EMEC0007, {
      status: 500,
    });
  }

  // SAS トークンを含む Blob URL へのリダイレクト
  redirect(blobUrlWithSAS);
}
