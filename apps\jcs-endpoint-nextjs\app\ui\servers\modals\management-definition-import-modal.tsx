/**
 * @fileoverview 管理項目定義のインポート用パラメータ入力モーダルコンポーネント
 * @description
 * 管理項目定義のインポート選択時の処理フロー：
 *   1. パラメータ入力モーダル表示：対象サーバ名、許可ファイル拡張子（.csv）の表示
 *   2. パラメータ入力と検証：CSVファイルの選択、クライアントサイド検証
 *   3. 最終確認モーダル表示：選択ファイルの確認
 *
 * バリデーション規則：
 *   - ファイルの必須入力チェック：未選択時「ファイルを指定してください。」（EMEC0016）
 *   - ファイル形式チェック：CSV以外の場合「無効なファイル形式です。CSVファイルを指定してください。」（EMEC0017）
 *   - エラー時は該当入力ボックスを赤枠表示
 *
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  PORTAL_ERROR_MESSAGES,
  FILE_VALIDATION,
  FORM_FIELD_NAMES,
} from "@/app/lib/definitions";
import { formatMessage } from "@/app/lib/utils";
import { Tooltip, TooltipInterface } from "flowbite";
import { MdUploadFile } from "react-icons/md";

/**
 * 管理項目定義インポートモーダルのプロパティ型定義である。
 * @property {boolean} isOpen - モーダルの表示・非表示制御である。
 * @property {(params: { file: File; originalFileName: string }) => void} onSubmit - バリデーション成功時のコールバックである。
 * @property {() => void} onClose - モーダルを閉じるコールバックである。
 */
interface ManagementDefinitionImportModalProps {
  isOpen: boolean;
  initialValues?: { file: File; originalFileName: string };
  onSubmit: (params: { file: File; originalFileName: string }) => void;
  onClose: () => void;
}

/**
 * 管理項目定義のインポート用パラメータ入力モーダルコンポーネントである。
 * ファイル選択、バリデーション、ツールチップ表示、送信処理を行う。
 * operation-log-export-modal.tsxと同じUI・バリデーション・ツールチップ構造を持つ。
 * @param {ManagementDefinitionImportModalProps} props - モーダルのプロパティである。
 * @returns {JSX.Element|null} モーダルUIまたは非表示(null)を返す。
 */
const ManagementDefinitionImportModal: React.FC<
  ManagementDefinitionImportModalProps
> = ({ isOpen, initialValues, onSubmit, onClose }) => {
  // ファイル選択状態
  const [selectedFile, setSelectedFile] = useState<File | null>(
    initialValues?.file || null,
  );
  // エラーメッセージ
  const [fileError, setFileError] = useState<string>("");
  // Tooltipインスタンス
  const [fileTooltip, setFileTooltip] = useState<
    TooltipInterface | undefined
  >();
  const [isClient, setIsClient] = useState(false);
  // ファイルinputのrefである。カスタムUIからinputクリックを実現するために利用する。
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // 初期値が変更された時に状態を更新
  useEffect(() => {
    if (initialValues) {
      setSelectedFile(initialValues.file || null);
    } else {
      // initialValuesがnullまたはundefinedの場合は状態をクリア
      setSelectedFile(null);
    }
  }, [initialValues]);

  /**
   * ファイル選択欄のツールチップ初期化処理である。
   * 既存インスタンスがあればhide、新規なら生成する。
   * エラーが既に存在する場合は初期化直後にshow。
   */
  const initTooltip = useCallback(() => {
    if (fileTooltip) {
      fileTooltip.hide();
    } else {
      // Tooltipのターゲットをアップロードアイコンボタンにし、右側に表示する
      setFileTooltip(
        new Tooltip(
          document.getElementById("importDefFileTooltip"),
          document.getElementById("importDefFileButton"),
          { placement: "right", triggerType: "none" },
          { id: "importDefFileTooltip", override: true },
        ) as TooltipInterface,
      );
    }
  }, [fileTooltip]);

  /**
   * フォーム初期化処理である。
   * エラー状態のみクリア、ファイル選択状態はuseEffectで処理する。
   */
  const initForm = useCallback(() => {
    // エラー状態をクリア
    setFileError("");

    // 初期値がある場合は設定、ない場合は空にする
    if (initialValues?.file) {
      setSelectedFile(initialValues.file);
    } else {
      setSelectedFile(null);
      const $file = document.getElementById(
        "importDefFileInput",
      ) as HTMLInputElement;
      if ($file) $file.value = "";
    }
  }, [initialValues]);

  useEffect(() => {
    if (isOpen) {
      initTooltip();
      initForm();
    } else {
      // モーダル閉じる時にtooltipをクリア
      setFileTooltip(undefined);
    }
  }, [isOpen, initTooltip, initForm]);

  /**
   * ファイルバリデーション処理
   * 拡張子チェック・MIMEタイプチェック・ファイルサイズチェックを行い、エラー時はツールチップを即時表示する
   * セキュリティ強化のため、ファイル拡張子とMIMEタイプの両方を検証し、悪意のあるファイルのアップロードを防ぐ
   * @returns {boolean} バリデーション成功時trueを返す
   */
  const validate = (): boolean => {
    if (!selectedFile) {
      return false;
    }

    const fileName = selectedFile.name;

    // ファイル拡張子チェック（定数を使用してセキュリティ強化）
    const hasValidExtension = FILE_VALIDATION.CSV.ALLOWED_EXTENSIONS.some(
      (ext) => fileName.toLowerCase().endsWith(ext),
    );
    if (!hasValidExtension) {
      setFileError(PORTAL_ERROR_MESSAGES.EMEC0017);
      fileTooltip?.show();
      return false;
    }

    // MIMEタイプチェック（定数を使用してセキュリティ強化）
    if (
      selectedFile.type &&
      !FILE_VALIDATION.CSV.ALLOWED_MIME_TYPES.includes(selectedFile.type as any)
    ) {
      setFileError(PORTAL_ERROR_MESSAGES.EMEC0017);
      fileTooltip?.show();
      return false;
    }

    // ファイルサイズチェック（DoS攻撃防止のため、定数を使用してセキュリティ強化）
    if (selectedFile.size > FILE_VALIDATION.CSV.MAX_FILE_SIZE) {
      setFileError(PORTAL_ERROR_MESSAGES.EMEC0028);
      fileTooltip?.show();
      return false;
    }

    return true;
  };

  /**
   * ファイル選択時の処理である。
   * ファイル選択時はエラーをリセットし、選択ファイルをstateに格納する。
   * @param {React.ChangeEvent<HTMLInputElement>} e - ファイルinputのchangeイベント
   */
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFileError("");
    fileTooltip?.hide();
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    } else {
      setSelectedFile(null);
    }
  };

  /**
   * フォーム送信処理である。
   * バリデーション失敗時はツールチップを必ず表示し、ユーザーに即時エラーを通知する。
   * @param {React.FormEvent<HTMLFormElement>} e - フォーム送信イベント
   */
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // 必須入力チェック
    if (!selectedFile) {
      setFileError(
        formatMessage(PORTAL_ERROR_MESSAGES.EMEC0016, [
          FORM_FIELD_NAMES.MGMT_ITEM_CSV_FILE,
        ]),
      );
      fileTooltip?.show();
      return;
    }

    // 詳細バリデーション
    if (!validate()) {
      return;
    }

    onSubmit({ file: selectedFile, originalFileName: selectedFile.name });
  };

  if (!isOpen) return null;

  // ・遮蔽レイヤー、ツールチップ、モーダル本体の3層構造である。
  // ・ファイル入力欄はカスタムUI（ラベル+必須マーク+擬似入力欄+右側ボタン）で横並び。
  // ・右側ボタンはreact-iconsのMdUploadFileを使用し、デザインに近い。
  // ・エラー時は擬似入力欄の枠色が赤色になる。

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 z-40" />
      {isClient && (
        <div
          id="importDefFileTooltip"
          role="tooltip"
          className="max-w-md break-words whitespace-pre-line tooltip invisible fixed z-[9999] inline-block rounded-lg bg-red-700 px-3 py-2 text-sm font-medium text-white opacity-0 shadow-sm transition-opacity duration-300"
        >
          {fileError}
          <div className="tooltip-arrow" data-popper-arrow></div>
        </div>
      )}
      <div className="fixed left-1/2 top-1/2 z-50 -translate-x-1/2 -translate-y-1/2 w-full max-w-lg max-h-full font-body">
        <form
          className="relative rounded shadow bg-gray-600"
          onSubmit={handleSubmit}
        >
          <div className="flex items-center justify-between p-4 border-b rounded-t bg-gradient-header">
            <h3 className="text-lg font-semibold text-white">
              管理項目定義のインポート
            </h3>
            <button
              type="button"
              className="text-gray-400 bg-transparent rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center hover:bg-gray-600 hover:text-white"
              onClick={onClose}
            >
              <svg
                className="w-3 h-3"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 14 14"
              >
                <path
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                />
              </svg>
              <span className="sr-only">Close modal</span>
            </button>
          </div>
          <div className="px-8 py-4 bg-gray-100 text-base font-medium">
            <div className="text-sm font-normal">
              管理項目定義のCSVファイルを指定してください。
            </div>
          </div>
          <div className="px-8 py-4 space-y-4 bg-white text-base font-medium">
            <div className="flex items-center justify-between overflow-hidden">
              <div className="flex-1 min-w-0 flex items-center justify-between">
                <label
                  htmlFor="importDefFileInput"
                  className="block text-sm font-medium text-gray-900"
                >
                  ファイル：
                </label>
                <span className="text-red-500 me-2">*</span>
              </div>
              <div className="flex items-center min-w-0" style={{ flex: 2 }}>
                <input
                  id="importDefFileInput"
                  type="file"
                  accept=".csv"
                  className="absolute opacity-0 pointer-events-none"
                  onChange={handleFileChange}
                  ref={fileInputRef}
                />
                <div
                  id="importDefFileDisplay"
                  className={[
                    "w-0 flex-1 rounded-l border border-gray-300 bg-gray-50 p-2 text-sm text-gray-900 shadow-inner cursor-pointer",
                    "overflow-hidden text-ellipsis whitespace-nowrap",
                    fileError ? "border-red-500" : "",
                  ].join(" ")}
                  onClick={() => fileInputRef.current?.click()}
                  tabIndex={0}
                  role="button"
                  aria-label="ファイルを選択してください"
                >
                  {selectedFile
                    ? selectedFile.name
                    : "ファイルを選択してください"}
                </div>
                <button
                  id="importDefFileButton"
                  type="button"
                  className="rounded-r bg-gray-200 border-t border-b border-r border-gray-300 px-3 py-2 flex items-center hover:bg-gray-300 focus:outline-none"
                  onClick={() => fileInputRef.current?.click()}
                  tabIndex={-1}
                  aria-label="ファイル選択"
                >
                  <MdUploadFile className="w-5 h-5 text-gray-600" />
                </button>
              </div>
            </div>
          </div>
          <div className="flex items-center justify-between p-4 border-t rounded-b bg-gradient-header">
            <div className="text-sm font-normal text-white">* 必須入力</div>
            <div className="flex flex-row-reverse items-center">
              <button
                type="button"
                onClick={onClose}
                className="w-28 ms-3 rounded bg-gradient-blue px-3 py-2 text-center text-xs font-medium text-white drop-shadow-blue shadow-inner hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
              >
                キャンセル
              </button>
              <button
                type="submit"
                className="w-28 rounded bg-gradient-dark px-3 py-2 text-center text-xs font-medium text-white drop-shadow-dark shadow-dark hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
              >
                OK
              </button>
            </div>
          </div>
        </form>
      </div>
    </>
  );
};

export default ManagementDefinitionImportModal;
