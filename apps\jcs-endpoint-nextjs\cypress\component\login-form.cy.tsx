import Form from "@/app/ui/login-form";
import React from "react";

Cypress.on("uncaught:exception", () => {
  return false;
});

describe("ログインフォームのテスト", () => {
  describe("初期表示", () => {
    it("２つタイトルが存在する", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // タイトルを確認
      cy.get("h1:first").should("have.text", "JP1 Cloud Service");
      cy.get("h1:last").should("have.text", "エンドポイント管理");
    });

    it("「パスワード」入力欄は、type 属性が password の入力タイプである", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // 「パスワード」入力欄のtype属性を確認
      cy.get("#password").should("have.attr", "type", "password");
    });

    it("「ログイン」ボタンは、クリック可能な状態にある", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // 「ログイン」ボタンの存在、クリック可能性、およびスタイルを確認
      cy.get("button").contains("ログイン");
      cy.get("button").should("be.enabled");
      cy.get("button").should("have.class", "hover:opacity-80");
    });

    it("ログインの試行前には、エラーメッセージが表示されないことを確認する", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // エラーメッセージが表示されないことを確認
      cy.get(".hidden").should("exist");
      // cy.contains("ユーザーIDもしくはパスワードに誤りがあります。").should(
      //   "not.exist",
      // );
    });

    it("「ユーザーID」入力欄は、デフォルトで空の値である", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // 「ユーザーID」入力欄のデフォルトの値、無効状態を確認
      cy.get("#userId").should("have.value", "");
      // cy.get("#userId").should(
      //   "have.css",
      //   "border-top-color",
      //   "rgb(239, 68, 68)",
      // );
    });

    it("「パスワード」入力欄は、デフォルトで空の値であある", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // 「パスワード」入力欄のデフォルトの値、無効状態を確認
      cy.get("#password").should("have.value", "");
      // cy.get("#password").should(
      //   "have.css",
      //   "border-top-color",
      //   "rgb(239, 68, 68)",
      // );
    });

    it("「ユーザーID」入力欄の上に「ユーザーID」というラベルが表示されている", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // 「ユーザーID」入力欄の上に「ユーザーID」というラベルが表示されていることを確認
      cy.get("label").contains("ユーザーID");
    });

    it("「パスワード」入力欄の上に「パスワード」というラベルが表示されている", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // 「パスワード」入力欄の上に「パスワード」というラベルが表示されていることを確認
      cy.get("label").contains("パスワード");
    });
  });

  describe("タブオーダ", () => {
    it("タブオーダは「ユーザーID」⇒「パスワード」⇒「ログイン」の順である", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // タブオーダが正しい順序であることを確認
      cy.get("#userId").tab().should("be.focused");
      cy.get("#password").tab().should("be.focused");
      cy.get("button").tab().should("be.focused");
    });
  });

  describe("項目チェック", () => {
    it("「ユーザーID」入力欄は必須である", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      cy.get("button").click();
      cy.get("#userId").should("have.class", "border-red-500");
    });

    it("「ユーザーID」入力欄は、全角文字は使用できない", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // 半角英数字以外の文字を入力して無効な状態を確認
      cy.get("#userId")
        .type("山田山田山田山田")
        .should("have.value", "山田山田山田山田");
      cy.get("button").click();
      cy.get("#userId").should("have.class", "border-red-500");
    });

    it("「ユーザーID」入力欄は、大英文字は使用できない", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // 半角英数字以外の文字を入力して無効な状態を確認
      cy.get("#userId").type("AAAAaaaa").should("have.value", "AAAAaaaa");
      cy.get("button").click();
      cy.get("#userId").should("have.class", "border-red-500");
    });

    it("「ユーザーID」入力欄は、記号「.」以外のは使用できない", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // 半角英数字以外の文字を入力して無効な状態を確認
      cy.get("#userId").type("AAAAaaa!").should("have.value", "AAAAaaa!");
      cy.get("button").click();
      cy.get("#userId").should("have.class", "border-red-500");
    });

    it("「ユーザーID」入力欄に8文字未満の文字列を入力すると、入力欄は無効な状態になり", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // 8文字未満の文字列を入力して無効な状態を確認
      cy.get("#userId").focus();
      cy.get("#userId").type("dl1234").should("have.value", "dl1234");
      cy.get("button").click();
      cy.get("#userId").should("have.class", "border-red-500");
    });

    it("「ユーザーID」入力欄に50文字を超える文字列を入力すると、入力欄は無効な状態になり", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // 50文字を超える文字列を入力して無効な状態でないことを確認
      cy.get("#userId")
        .type("123456789012345678901234567890123456789012345678901")
        .should(
          "have.value",
          "12345678901234567890123456789012345678901234567890",
        );
    });

    it("「パスワード」入力欄は必須である", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      cy.get("button").click();
      cy.get("#password").should("have.class", "border-red-500");
    });

    it("「パスワード」入力欄は、全角文字は使用できない", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // 半角英数字以外の文字を入力して無効な状態を確認
      cy.get("#password")
        .type("山田山田山田山田")
        .should("have.value", "山田山田山田山田");
      cy.get("button").click();
      cy.get("#password").should("have.class", "border-red-500");
    });

    it("「パスワード」入力欄に8文字未満の文字列を入力すると、入力欄は無効な状態になり", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // 8文字未満の文字列を入力して無効な状態を確認
      cy.get("#password").focus();
      cy.get("#password").type("dl1234").should("have.value", "dl1234");
      cy.get("button").click();
      cy.get("#password").should("have.class", "border-red-500");
    });

    it("「パスワード」入力欄に128文字を超える文字列を入力すると、入力欄は無効な状態になり", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // 20文字を超える文字列を入力して無効な状態でないことを確認
      cy.get("#password")
        .type(
          "123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789",
        )
        .should(
          "have.value",
          "12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678",
        );
    });
  });

  describe("ログイン動作確認", () => {
    it("ユーザーIDとパスワードが一致しない場合、ログインが失敗し、エラーメッセージが表示されることを確認する", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // ログイン失敗のモックデータを設定
      cy.intercept("GET", "/api/auth/providers", {
        statusCode: 200,
        body: {
          credentials: {
            id: "credentials",
            name: "Credentials",
            type: "credentials",
            signinUrl: "http://localhost:3000/api/auth/signin/credentials",
            callbackUrl: "http://localhost:3000/api/auth/callback/credentials",
          },
        },
      });
      cy.intercept("GET", "/api/auth/csrf", {
        statusCode: 200,
        body: {
          csrfToken: "dumy-token",
        },
      });
      cy.intercept("POST", "/api/auth/callback/credentials", {
        statusCode: 401,
        body: {
          url: "http://localhost:3000/api/auth/error?error=%E3%83%A6%E3%83%BC%E3%82%B6ID%E3%82%82%E3%81%97%E3%81%8F%E3%81%AF%E3%83%91%E3%82%B9%E3%83%AF%E3%83%BC%E3%83%89%E3%81%AB%E8%AA%A4%E3%82%8A%E3%81%8C%E3%81%82%E3%82%8A%E3%81%BE%E3%81%99%E3%80%82",
        },
      });
      // ユーザーIDとパスワードを入力してログインボタンをクリック
      cy.get("#userId").type("user1234");
      cy.get("#password").type("pass1234");
      cy.get("button").click();
      cy.wait(1000);
      // エラーメッセージが表示されることを確認
      cy.contains("ユーザIDもしくはパスワードに誤りがあります。");
    });

    it("ユーザーIDとパスワードが一致する場合、ログインが成功し、ページが遷移されることを確認する", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // ログイン成功のモックデータを設定
      cy.intercept("GET", "/api/auth/providers", {
        statusCode: 200,
        body: {
          credentials: {
            id: "credentials",
            name: "Credentials",
            type: "credentials",
            signinUrl: "http://localhost:3000/api/auth/signin/credentials",
            callbackUrl: "http://localhost:3000/api/auth/callback/credentials",
          },
        },
      });
      cy.intercept("GET", "/api/auth/csrf", {
        statusCode: 200,
        body: {
          csrfToken: "dumy-token",
        },
      });
      cy.intercept("POST", "/api/auth/callback/credentials", {
        statusCode: 200,
        body: {
          url: "http://localhost:3000",
        },
      });
      // ユーザーIDとパスワードを入力してログインボタンをクリック
      cy.get("#userId").type("user1234");
      cy.get("#password").type("pass1234");
      cy.get("button").click();
      cy.wait(1000);
      // ページが遷移されることを確認
      cy.get("@push").should("be.called");
    });

    it("ログイン時にネットワークエラーが発生した場合、デフォルトのエラーメッセージが表示されることを確認する", () => {
      // フォームコンポーネントをマウント
      cy.nextMount(<Form />);
      // ネットワークエラーのモックデータを設定
      cy.intercept("GET", "/api/auth/providers", {
        statusCode: 200,
        body: {
          credentials: {
            id: "credentials",
            name: "Credentials",
            type: "credentials",
            signinUrl: "http://localhost:3000/api/auth/signin/credentials",
            callbackUrl: "http://localhost:3000/api/auth/callback/credentials",
          },
        },
      });
      cy.intercept("GET", "/api/auth/csrf", {
        statusCode: 200,
        body: {
          csrfToken: "dumy-token",
        },
      });
      // ユーザーIDとパスワードを入力してログインボタンをクリック
      cy.get("#userId").type("user1234");
      cy.get("#password").type("pass1234");
      cy.get("button").click();
      cy.wait(1000);
      // デフォルトのエラーメッセージが表示されることを確認
      cy.contains("ユーザIDもしくはパスワードに誤りがあります。");
    });
  });
});
