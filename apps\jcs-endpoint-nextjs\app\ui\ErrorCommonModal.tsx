/**
 * @file ErrorCommonModal.tsx
 * @description
 * 汎用的な情報・エラーメッセージ表示モーダルを提供するクライアントコンポーネントです。
 * FlowbiteライブラリのModalコンポーネントをReactのライフサイクル（useEffect, useRef）と統合し、
 * 宣言的なUI操作（propsによる表示制御）を実現します。
 * 親コンポーネントから渡される `isOpen` プロパティに応じてモーダルの表示・非表示を制御し、
 * `title`, `message`, `error` の内容を動的に表示します。
 * エラーメッセージがある場合はエラーアイコンを、それ以外は情報アイコンを表示します。
 *
 * 主な機能：
 * - Flowbite Modalインスタンスのライフサイクル管理。
 * - props（isOpen）に基づいた、モーダルのプログラム的な表示/非表示制御。
 * - 情報メッセージとエラーメッセージの表示切り替え。
 * - 閉じるボタン押下時に親コンポーネントへ通知するコールバック機能（onClose）。
 *
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

"use client";

import { Modal } from "flowbite";
import type { ModalOptions, ModalInterface } from "flowbite";
import { ModalProps } from "../lib/definitions";
import { useEffect, useRef } from "react";

/**
 * 汎用メッセージモーダルのプロパティ定義
 * @interface ErrorCommonModalProps
 * @extends ModalProps
 * @param {string} title - モーダルのタイトル
 * @param {string} [message] - 表示する情報メッセージ（HTML許容）
 * @param {string} [error] - 表示するエラーメッセージ（HTML許容）。これが存在する場合、エラーアイコンが表示される。
 * @param {boolean} isOpen - モーダルが開いているかどうかを制御するフラグ
 */
interface ErrorCommonModalProps extends ModalProps {
  title: string;
  message?: string;
  error?: string;
  isOpen: boolean;
}

export default function ErrorCommonModal({
  title,
  message,
  error,
  onClose,
  isOpen,
}: ErrorCommonModalProps) {
  // 1. Refsの定義
  // modalRef: モーダルのDOM要素への参照。Flowbiteが操作する対象となります。
  const modalRef = useRef<HTMLDivElement>(null);
  // modalInstance: FlowbiteのModalインスタンスへの参照。コンポーネントのライフサイクルを通じてインスタンスを保持します。
  const modalInstance = useRef<ModalInterface | null>(null);

  // 2. Flowbite Modalインスタンスの初期化と破棄
  // このuseEffectはコンポーネントがマウントされた時に一度だけ実行されます。
  useEffect(() => {
    // DOM要素がまだレンダリングされていない場合は何もしません。
    if (!modalRef.current) return;

    // Flowbiteモーダルの設定オプション
    const modalOptions: ModalOptions = {
      placement: "center", // 画面中央に表示
      backdrop: "static", // 背景をクリックしても閉じない
      closable: true, // Escキーや閉じるボタンで閉じられるようにする
    };

    // FlowbiteのModalクラスをインスタンス化し、useRefに格納します。
    modalInstance.current = new Modal(modalRef.current, modalOptions);

    // クリーンアップ関数：コンポーネントがアンマウントされる時に実行されます。
    // これにより、メモリリークや意図しない動作を防ぎます。
    return () => {
      if (modalInstance.current) {
        modalInstance.current.destroy(); // Flowbiteインスタンスを破棄
        modalInstance.current = null;
      }
    };
  }, []); // 空の依存配列は、このエフェクトがマウント時に一度だけ実行されることを保証します。

  // 3. モーダルの表示/非表示の制御
  // このuseEffectは、親から渡される `isOpen` プロパティが変更されるたびに実行されます。
  useEffect(() => {
    // Flowbiteインスタンスがまだ準備できていない場合は何もしません。
    if (!modalInstance.current) return;

    // isOpenフラグに応じて、FlowbiteのAPIを呼び出してモーダルを制御します。
    if (isOpen) {
      modalInstance.current.show();
    } else {
      modalInstance.current.hide();
    }
  }, [isOpen]); // 依存配列にisOpenを指定し、この値の変更を監視します。

  /** モーダルを閉じるためのコールバック関数 */
  const closeModal = () => {
    // 親コンポーネントから渡されたonClose関数が存在すれば、それを呼び出します。
    // これにより、親コンポーネントはモーダルが閉じられたことを検知できます。
    onClose && onClose();
  };

  // 4. レンダリングの条件分岐
  // モーダルが閉じてる、または表示すべき内容（message, error）が何もない場合、
  // コンポーネントは何もレンダリングしません（nullを返します）。
  // これにより、不要なDOM要素が生成されるのを防ぎます。
  if (!isOpen || (!message && !error)) return null;

  return (
    // Flowbiteが要求するモーダルの基本HTML構造
    <div
      ref={modalRef}
      id="message-modal"
      tabIndex={-1}
      className="flex overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full"
    >
      <div className="relative w-full max-w-md max-h-full">
        <div className="relative rounded shadow bg-gray-600">
          {/* ヘッダー：タイトルと閉じるボタン */}
          <div className="flex items-center justify-between p-4 border-b rounded-t bg-gradient-header">
            <h3 className="text-lg font-semibold text-white">{title}</h3>
            <button
              type="button"
              className="text-gray-400 bg-transparent rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center hover:bg-gray-600 hover:text-white"
              onClick={closeModal}
            >
              <svg className="w-3 h-3" /* ... */>
                <path /* ... */ />
              </svg>
              <span className="sr-only">Close modal</span>
            </button>
          </div>
          {/* ボディ：アイコンとメッセージ */}
          <div className="px-8 py-4 space-y-4 bg-white text-base font-medium">
            <div className="flex">
              {/* errorプロパティの有無で表示するアイコンを切り替えます。 */}
              <img
                src={error ? "/dialogerror_32.png" : "/dialoginfo_32.png"}
                className="w-8 h-8 me-2 inline-block"
                alt={error ? "Error" : "Information"}
              />
              {/* メッセージ表示：dangerouslySetInnerHTMLを使い、メッセージ内の改行（\n）やHTMLタグを解釈して表示します。 */}
              <span
                className="whitespace-pre-wrap break-words"
                dangerouslySetInnerHTML={{
                  __html: message || error || "",
                }}
              />
            </div>
          </div>
          {/* フッター：閉じるボタン */}
          <div className="flex flex-row-reverse items-center p-4 border-t rounded-b bg-gradient-header">
            <button
              onClick={closeModal}
              type="button"
              className="w-28 rounded bg-gradient-dark px-3 py-2 text-center text-xs font-medium text-white 
              drop-shadow-dark shadow-dark hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
            >
              閉じる
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
