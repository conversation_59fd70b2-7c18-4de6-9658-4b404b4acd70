# E2E 测试环境设计文档

## 概述

本设计文档详细说明了如何为 jcs-endpoint-monorepo 项目建立一个完整、可靠的本地端到端（E2E）测试环境。该环境将支持三个核心应用程序（Next.js 前端、标准 Azure Functions、长时运行 Azure Functions）的集成测试，并提供适当的环境隔离、数据管理和认证处理能力。

## 架构

### 整体架构

测试环境架构包括以下主要组件：

1. **测试执行层**：Playwright 测试框架，负责执行测试用例和断言
2. **应用层**：三个核心应用（Next.js、标准 Functions、长时运行 Functions）
3. **配置层**：环境变量和配置文件，用于环境隔离
4. **数据层**：测试数据管理和数据库交互
5. **辅助工具层**：启动脚本、认证辅助函数等

```mermaid
graph TD
    A[Playwright 测试框架] --> B[测试用例]
    B --> C[测试辅助函数]
    B --> D[Next.js 前端应用]
    B --> E[标准 Azure Functions]
    B --> F[长时运行 Azure Functions]
    D --> G[数据库]
    E --> G
    F --> G
    H[环境配置] --> D
    H --> E
    H --> F
    I[启动脚本] --> D
    I --> E
    I --> F
```

### 组件交互

1. **启动流程**：
   - 启动脚本并行启动所有三个应用
   - 每个应用使用特定的测试环境配置
   - 应用之间通过预配置的端点相互发现

2. **测试执行流程**：
   - Playwright 测试用例访问 Next.js 前端
   - 前端与后端 Functions 交互
   - 测试断言验证整个流程的正确性

3. **关闭流程**：
   - 启动脚本捕获终止信号
   - 清理所有启动的进程
   - 确保没有孤立进程残留

## 组件和接口

### 1. 目录结构

```
jcs-endpoint-monorepo/
├── apps/
│   ├── jcs-endpoint-nextjs/
│   │   ├── .env.local          # 本地开发环境配置
│   │   └── .env.test           # 测试专用环境配置
│   ├── jcs-backend-services-standard/
│   │   └── local.settings.json # 包含测试配置
│   └── jcs-backend-services-long-running/
│       └── local.settings.json # 包含测试配置
├── scripts/
│   └── start-local-env.sh      # 一键启动脚本
└── tests/
    └── integration/            # 集成测试工作区
        ├── specs/              # 测试用例
        │   └── server-list.spec.ts
        ├── support/            # 测试辅助模块
        │   └── auth.helper.ts  # 认证辅助模块
        ├── package.json        # 测试依赖
        ├── playwright.config.ts # Playwright 配置
        └── tsconfig.json       # TypeScript 配置
```

### 2. 核心组件

#### 2.1 启动脚本 (`scripts/start-local-env.sh`)

负责并行启动所有应用，并在退出时清理进程。

**接口**：
- 输入：无命令行参数
- 输出：控制台日志，显示启动状态
- 行为：启动三个应用，等待用户中断，然后清理

#### 2.2 环境配置文件

**Next.js 环境文件 (`.env.test`)**：
- 包含测试专用的数据库连接、API 端点等
- 与开发环境 (`.env.local`) 分离

**Azure Functions 配置 (`local.settings.json`)**：
- 包含测试专用的连接字符串和配置
- 支持与测试数据库的连接

#### 2.3 认证辅助模块 (`tests/integration/support/auth.helper.ts`)

提供跳过 UI 登录流程的认证功能。

**接口**：
```typescript
async function loginAs(page: Page, options: { role: string }): Promise<void>
```

#### 2.4 测试用例 (`tests/integration/specs/*.spec.ts`)

包含实际的测试场景和断言。

**接口**：
- 使用 Playwright 的 `test` 和 `expect` API
- 包含 `beforeEach` 和 `afterEach` 钩子进行数据设置和清理

## 数据模型

### 1. 测试数据模型

测试将使用以下数据模型：

#### 1.1 服务器测试数据

```typescript
interface TestServer {
  name: string;       // 服务器名称，包含时间戳确保唯一性
  type: string;       // 服务器类型，如 'GENERAL_MANAGER'
  url: string;        // 管理界面 URL
  licenseId: string;  // 许可证 ID
}
```

### 2. 数据库交互

测试将通过 Prisma 客户端与数据库交互：

```typescript
// 创建测试数据
const server = await prisma.server.create({ data: testServer });

// 清理测试数据
await prisma.server.deleteMany({ where: { name: { contains: 'e2e-test' } } });
```

## 错误处理

### 1. 环境启动错误

- 启动脚本将检测应用启动失败
- 提供清晰的错误消息和可能的解决方案
- 确保即使部分应用启动失败，也能正确清理所有进程

### 2. 测试执行错误

- 使用 Playwright 的重试机制处理偶发性失败
- 捕获并记录详细的错误信息和跟踪数据
- 在测试失败时生成详细的 HTML 报告

### 3. 数据清理错误

- 即使测试失败，也确保 `afterEach` 钩子执行数据清理
- 使用 `try/catch` 块确保清理操作不会中断测试流程
- 记录清理失败的情况，以便后续手动干预

## 测试策略

### 1. 测试范围

- **功能测试**：验证核心功能正常工作
- **集成测试**：验证多个组件协同工作
- **UI 测试**：验证用户界面元素正确显示

### 2. 测试隔离

- 每个测试用例在开始前清理相关测试数据
- 使用唯一标识符（如时间戳）避免测试数据冲突
- 测试完成后删除所有创建的测试数据

### 3. 认证策略

- 使用辅助函数直接注入认证状态，跳过 UI 登录
- 支持不同用户角色的认证
- 确保认证状态在整个测试会话中持续存在

### 4. 执行策略

- 使用单一命令启动测试环境
- 自动生成详细的测试报告
- 捕获失败测试的跟踪信息用于调试

## 实现考量

### 1. 启动模式选择

对于集成测试环境，我们选择以下启动模式：

1. **Next.js 应用**：使用 `npm run dev` 而非 `npm start`，原因是：
   - 测试环境需要快速启动，无需预先构建
   - 开发模式提供更详细的错误信息，便于调试测试失败
   - 集成测试主要关注功能正确性，而非生产性能

2. **Azure Functions**：使用 `func start`，原因是：
   - 这是本地开发 Functions 的标准方式
   - 提供了与 Azure 环境类似的执行环境
   - 支持实时日志输出，便于调试

### 2. 环境隔离方案

为确保测试环境与开发环境隔离，我们将：

1. **Next.js 应用**：
   - 创建专用的 `.env.test` 文件
   - 在启动脚本中使用 `NODE_ENV=test` 确保加载正确的环境变量

2. **Azure Functions**：
   - 使用环境变量覆盖 `local.settings.json` 中的设置
   - 为测试创建专用的数据库连接字符串

### 3. 性能考量

- 并行启动所有应用以减少等待时间
- 使用直接注入认证状态而非 UI 登录以加快测试执行
- 配置适当的超时时间，平衡测试稳定性和执行速度

## 安全考量

1. **环境文件保护**：
   - 确保所有包含敏感信息的环境文件被添加到 `.gitignore`
   - 提供示例环境文件（如 `.env.test.example`）不含实际凭据

2. **测试数据隔离**：
   - 使用专用的测试数据库或架构
   - 确保测试数据不会影响生产或开发数据

3. **凭据处理**：
   - 避免在测试代码中硬编码凭据
   - 使用环境变量或配置文件存储敏感信息