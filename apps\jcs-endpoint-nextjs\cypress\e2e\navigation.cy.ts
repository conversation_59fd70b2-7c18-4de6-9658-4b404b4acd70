describe("画面間のナビゲーションテスト", () => {
  const validCredentials = {
    userId: "hitachi.taro.aa",
    password: "changeit!@#",
  };

  it("ログイン画面からサーバ一覧画面へのナビゲーション", () => {
    cy.visit("/login");

    cy.get("#userId").type(validCredentials.userId);
    cy.get("#password").type(validCredentials.password);
    cy.get("button").contains("ログイン").click();
    cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/servers");
    cy.title().should("eq", "サーバ一覧");
  });

  it("メイン画面からログイン画面へのナビゲーション", () => {
    cy.visit("/login");

    cy.get("#userId").type(validCredentials.userId);
    cy.get("#password").type(validCredentials.password);
    cy.get("button").contains("ログイン").click();
    cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/servers");
    cy.title().should("eq", "サーバ一覧");
    cy.get("button").contains("ログアウト").click();
    cy.get("#logout-modal button").contains("OK").click();
    cy.url().should("eq", Cypress.config().baseUrl + "/login");
  });

  it("サーバ一覧画面から操作ログ一覧画面へのナビゲーション", () => {
    cy.visit("/login");

    cy.get("#userId").type(validCredentials.userId);
    cy.get("#password").type(validCredentials.password);
    cy.get("button").contains("ログイン").click();
    cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/servers");
    cy.get("aside #file li:nth-child(1) a").click();
    cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/oplogs");
    cy.title().should("eq", "操作ログ一覧");
  });

  it("サーバ一覧画面から製品媒体一覧画面へのナビゲーション", () => {
    cy.visit("/login");

    cy.get("#userId").type(validCredentials.userId);
    cy.get("#password").type(validCredentials.password);
    cy.get("button").contains("ログイン").click();
    cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/servers");
    cy.get("aside #file li:nth-child(2) a").click();
    cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/medias");
    cy.title().should("eq", "製品媒体一覧");
  });

  it("サーバ一覧画面からマニュアル一覧画面へのナビゲーション", () => {
    cy.visit("/login");

    cy.get("#userId").type(validCredentials.userId);
    cy.get("#password").type(validCredentials.password);
    cy.get("button").contains("ログイン").click();
    cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/servers");
    cy.get("aside #file li:nth-child(3) a").click();
    cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/manuals");
    cy.title().should("eq", "マニュアル一覧");
  });

  it("サーバ一覧画面から提供ファイル一覧画面へのナビゲーション", () => {
    cy.visit("/login");

    cy.get("#userId").type(validCredentials.userId);
    cy.get("#password").type(validCredentials.password);
    cy.get("button").contains("ログイン").click();
    cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/servers");
    cy.get("aside #file li:nth-child(4) a").click();
    cy.url().should(
      "eq",
      Cypress.config().baseUrl + "/dashboard/provided-files",
    );
    cy.title().should("eq", "提供ファイル一覧");
  });

  it("サーバ一覧画面からサポート情報一覧画面へのナビゲーション", () => {
    cy.visit("/login");

    cy.get("#userId").type(validCredentials.userId);
    cy.get("#password").type(validCredentials.password);
    cy.get("button").contains("ログイン").click();
    cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/servers");
    cy.get("aside #file li:nth-child(5) a").click();
    cy.url().should(
      "eq",
      Cypress.config().baseUrl + "/dashboard/support-files",
    );
    cy.title().should("eq", "サポート情報一覧");
  });

  it("外部リンクをクリックして新しいタブで開くことを確認", () => {
    const modifiedText = "https://example.com";

    cy.visit("/login");
    cy.get("#userId").type(validCredentials.userId);
    cy.get("#password").type(validCredentials.password);
    cy.get("button").contains("ログイン").click();
    cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/servers");
    cy.title().should("eq", "サーバ一覧");
    cy.intercept("GET", modifiedText, (req) => {
      expect(true).to.eq(true);
    });
    cy.get("tbody tr:first-of-type a")
      .invoke("text")
      .then((text) => {
        cy.get("tbody tr:first-of-type a").invoke("attr", "href", modifiedText);
        cy.get("tbody tr:first-of-type a")
          .invoke("removeAttr", "target")
          .click();
      });
  });
});
