### イベント定義

本画面における主要なイベント処理について記述する。

#### イベントNo 1：初期表示

ユーザーがブラウザでタスク一覧画面のURL (`/dashboard/tasks`) へアクセスすると、システムはタスク一覧表示に必要な初期データを取得し、画面をレンダリングしてユーザーに提示する。

処理詳細は以下の通りである。

1.  **ページ初期化**
    Next.js App Router は、サーバサイドでページコンポーネント（apps/jcs-endpoint-nextjs/app/dashboard/tasks/page.tsx）の実行を開始する。
    URLに検索パラメータ（フィルター条件、ページ番号、1ページあたりの表示行数、ソートキー、ソート順）が存在しない場合、
    ページコンポーネントは以下の初期条件を設定する。

| 項目名 | 設定内容 |
|:---|:---|
| フィルター（filter） | - |
| ページ（page） | 1 |
| 行数/ページ（size） | 10 |
| ソートキー（sort） | 開始日時 (startedAt) |
| ソートオーダー（order） | 降順 (desc) |

2.  **データ取得と編集**
    本画面では、複数の項目に対する横断的なフィルター機能（キーワード検索）を提供するため、またデータ総量が限定的である想定から、現在のログインユーザーの契約IDに紐づくタスク情報の全件を取得しキャッシュする仕様とする。
    キャッシュされたタスクデータがない場合、ページコンポーネントは、契約IDが現在のログインユーザーの契約IDと一致するタスクレコードの全件をデータベースのTaskテーブルから取得する。取得したデータに対し、ステータスおよびタスク種別の日本語名称への変換（本設計書「参照値一覧- タスクステータス」および「参照値一覧 - タスク種別」を参照）、日時の変換（DBから取得したUTC協定世界時の時間をログインしているユーザーのタイムゾーンで変換）とフォーマット（YYYY/MM/DD hh:mm:ss）を行う。その後タスクデータをキャッシュし、キャッシュされた全項目は文字列に変換される。
    キャッシュされたタスクデータに対し、指定されたフィルター条件、ソート条件、ページネーション条件に基づいて、下記のメモリ内での絞り込み、並び替え、ページ分割処理を行い、編集したタスク一覧データおよび総ページ数をページコンポーネントへ返却する。
    *   絞り込み：タスク名、ステータス（日本語名）、開始日時、終了日時、サーバ名、タスク種別（日本語名）、実行ユーザー名、以上の項目について、filterの文字列がキャッシュされたいずれかの項目の文字列に含まれているタスクデータを選出する。英語の大文字小文字を区別しない。
    *   並び替え：第一ソートキーsortとソート順orderによってデータをソートする。第一ソートキーが「タスク名」でない場合は固定で第二ソートキーとして「タスク名」(taskName) の昇順 (asc) を設定する。第一ソートキーが「タスク名」の場合は第二ソートキーを設定しない。
    *   ページ分割処理：先頭の ( page - 1 ) * size 件をスキップして、size件のデータを取得する。

    データ取得処理の実行中は、画面にスケルトンローディング（テーブルの仮の骨組み）を表示する。

3.  **画面レンダリングと表示**
    ページコンポーネントは、取得したデータに基づき画面各項目をサーバサイドでレンダリングする。レンダリング結果はクライアントブラウザへ送信され、画面が表示される。
    各行の「タスク詳細」列の内容は、タスクのステータスおよび種別に応じて動的に表示される（詳細は本設計書「画面項目定義 - タスク一覧」を参照）。
    ページネーションコントロールは、取得した総ページ数に基づき適切に初期化される（「前のページ」ボタンは非活性、「次のページ」ボタンは総ページ数に応じて活性/非活性）。
    データ取得処理中にエラーが発生した場合（例：データベース接続不可）、Next.js のエラー処理部品により、適切なエラーメッセージが表示される。

#### イベントNo 2：フィルター検索実行

ユーザーがフィルター入力ボックスにキーワードを入力し、「検索」ボタンを押下すると、入力されたキーワードに基づきタスク一覧が再表示される。

1.  **入力受付**
    ユーザーがフィルター入力ボックスに検索キーワードを入力する。

2.  **検索実行**
    ユーザーが虫眼鏡の検索ボタンを押下すると、ページコンポーネントは以下の処理を実行する。
    1.  フィルター入力ボックスから現在の入力値を取得する。
    2.  URLの検索パラメータを更新する。具体的には、filterパラメータを入力値に設定し、pageパラメータを1にリセットする。他の検索パラメータ（size, sort, order）は維持する。
    3.  更新された検索パラメータを持つ新しいURLへ画面を遷移させる。これにより、初期表示イベント（イベントNo.1）と同様のデータ取得および画面レンダリング処理が再実行され、フィルター条件に合致するタスクのデータが表示される。

#### イベントNo 3：フィルタークリア

ユーザーがフィルターのクリアボタンを押下すると、フィルター入力ボックスの内容がクリアされ、フィルター条件が解除されて、タスク一覧が再表示される。

1.  **クリア実行**
    ユーザーがフィルターのクリアボタンを押下すると、ページコンポーネントは以下の処理を実行する。
    1.  フィルター入力ボックスの内容を空にする。
    2.  URLの検索パラメータを更新する。具体的には、filterパラメータを削除し、pageパラメータを1にリセットする。他の検索パラメータ（size, sort, order）は維持する。
    3.  更新された検索パラメータを持つ新しいURLへ画面を遷移させる。これにより、初期表示イベント（イベントNo.1）と同様のデータ取得および画面レンダリング処理が再実行され、フィルターが解除されたタスクのデータが表示される。

#### イベントNo 4：一覧更新

ユーザーが画面右上の更新ボタンを押下すると、タスク一覧が最新の情報に更新される。

1.  **更新実行**
    ユーザーが更新ボタンを押下すると、ページコンポーネントはNext.jsのrouter.refresh()機能を呼び出す。

2.  **データ再取得と表示**
    router.refresh()の呼び出しにより、タスクのデータキャッシュは無効化され、初期表示イベント（イベントNo.1）と同様のデータ取得および画面レンダリング処理が実行される。この際、現在のフィルター条件、ソート条件、ページネーション条件は維持される。

#### イベントNo 5：ソート順変更

ユーザーがソート可能な列のヘッダーをクリックすると、その列を基準にタスク一覧が昇順または降順に並び替えられて再表示される。

1.  **ソート実行**
    ユーザーがソート可能な列ヘッダー（本設計書「画面項目定義 - タスク一覧」参照）をクリックすると、ページコンポーネントは以下の処理を実行する。
    1.  クリックされた列のキー（例：taskName, status等）を取得する。
    2.  現在の第一ソートキーsortおよびソート順orderに基づき、新しい第一ソートキーおよびソート順を決定する。
        *   クリックされた列が現在の第一ソートキーでない場合、その列を新しい第一ソートキーとし、ソート順を昇順 (asc) に設定する。
        *   クリックされた列が現在の第一ソートキーである場合、ソート順を現在の逆（昇順なら降順、降順なら昇順）に切り替える。
    3.  URLの検索パラメータを更新する。sortパラメータを新しい第一ソートキーに、orderパラメータを新しいソート順に設定し、pageパラメータを1にリセットする。他の検索パラメータ（filter, size）は維持する。
    4.  更新された検索パラメータを持つ新しいURLへ画面を遷移させる。これにより、初期表示イベント（イベントNo.1）と同様のデータ取得および画面レンダリング処理が再実行され、並び替えられたタスクデータが表示される。クリックされた列のヘッダーには、現在のソート状態を示すアイコン（▲▼）が表示される。

#### イベントNo 6：ページ変更

ユーザーがページネーションコントロールを操作すると、指定されたページでタスク一覧が再表示される。

1.  **ページ変更**
    ユーザーがページネーションコントロールの前のページボタン、次のページボタン、または特定のページ番号リンクをクリックすると、ページコンポーネントは以下の処理を実行する。
    1.  クリックされた操作に基づき、新しい表示ページ番号を取得する。
    2.  URLの検索パラメータを更新する。pageパラメータを新しいページ番号に設定する。他の検索パラメータ（filter, size, sort, order）は維持する。
    3.  更新された検索パラメータを持つ新しいURLへ画面を遷移させる。これにより、初期表示イベント（イベントNo.1）と同様のデータ取得および画面レンダリング処理が再実行され、指定されたページのタスクデータが表示される。

#### イベントNo 7：表示件数変更

ユーザーが1ページあたりの表示件数選択ドロップダウンリストを操作すると、選択された件数でタスク一覧が再表示される。

1.  **表示件数変更**
    ユーザーが「行数/ページ:」ドロップダウンリストから新しい表示件数（10/30/50）を選択すると、ページコンポーネントは以下の処理を実行する。
    1.  選択された新しい表示件数を取得する。
    2.  URLの検索パラメータを更新する。sizeパラメータを新しい表示件数に設定し、pageパラメータを1にリセットする。他の検索パラメータ（filter, sort, order）は維持する。
    3.  更新された検索パラメータを持つ新しいURLへ画面を遷移させる。これにより、初期表示イベント（イベントNo.1）と同様のデータ取得および画面レンダリング処理が再実行され、指定された表示件数のタスクデータが表示される。

#### イベントNo 8：タスク中止操作

ユーザーが「実行待ち」ステータスのタスクに対して「中止」ボタンを押下すると、タスクの中止要求処理が開始される。

1.  **中止確認モーダル表示**
    「タスク詳細」列に表示されている「中止する」ボタンがクリックされると、ページコンポーネントは以下の処理を実行する。
    1.  クリックされたタスクのID およびタスク名 (taskName) を取得する。
    2.  汎用メッセージモーダルダイアログ（本設計書「画面項目定義 - 確認画面」参照）を表示する準備を行う。タイトルを「確認」とし、確認メッセージとして「タスクを中止してもよろしいですか？\nタスク名: {taskName}」（{taskName}は取得したタスク名で置換）を設定する。
    3.  中止確認モーダルダイアログを表示する。

2.  **中止要求受付処理**
    ユーザーが中止確認モーダルダイアログの「OK」ボタンを押下すると、ページコンポーネントは以下の処理を実行する。
    1.  中止確認モーダルダイアログを閉じる。
    2.  ローディング状態（UI操作を一時的に遮断する）を開始する。
    3.  Server ActionのrequestTaskCancellationを呼び出す。入力パラメータとして、対象タスクのIDを渡す。このServer Actionの詳細は本設計書「タスク中止要求受付処理詳細」を参照。
    4.  requestTaskCancellationからの応答 (TaskCancellationActionResult) を受信する。
    5.  ローディング状態を終了する。
    6.  応答 (TaskCancellationActionResult) の success フィールドおよび message フィールドに基づいた結果をユーザーに通知する。
        *   success が true の場合: タイトルを「情報」に、メッセージ内容を messageフィールドの内容（EMEC0026「タスクの中止を受け付けました。タスクのステータスはタスク一覧画面で確認してください。\nタスク名：{0}」、{0}は対象タスク名で置換）に設定した汎用メッセージモーダルダイアログ（本設計書「画面項目定義 - 受付完了画面」参照）をユーザーに表示する。その後、イベントNo.4（一覧更新）と同様にタスク一覧を更新する。
        *   success が false の場合: タイトルを「エラー」に、メッセージ内容を messageフィールドの内容（例：タスクがすでに実行し始めた場合は EMEC0023「タスクの中止はできません。タスクは他のユーザーによって中止操作が行われたか、すでに実行中か、もしくは実行が終了しています。」）に設定した汎用エラーメッセージモーダルダイアログ（本設計書「画面項目定義 - エラー画面」参照）をユーザーに表示する。

    ユーザーが中止確認モーダルダイアログの「キャンセル」ボタンまたは×ボタンを押下すると、ページコンポーネントはモーダルダイアログを閉じ、中止要求受付処理は実行されない。

#### イベントNo 9：「エラー詳細を表示」リンククリック

ユーザーが「エラー」ステータスのタスクに対して「エラー詳細を表示」リンクをクリックすると、タスクの実行エラーに関する詳細情報が表示される。

1.  **エラー詳細モーダル表示**
    「タスク詳細」列に表示されている「エラー詳細を表示」リンクがクリックされると、ページコンポーネントは以下の処理を実行する。
    1.  クリックされたタスクの resultMessage フィールド（エラーメッセージ本文）を取得する。
    2.  汎用エラーメッセージモーダルダイアログ（本設計書「画面項目定義 - エラー画面」参照）を表示する準備を行う。タイトルを「エラー詳細」とし、メッセージ本文として取得した resultMessage フィールドの内容を設定する。
    3.  モーダルダイアログを表示する。

2.  **モーダルクローズ**
    ユーザーがエラー詳細モーダルダイアログの「閉じる」ボタンまたは×ボタンを押下すると、ページコンポーネントはモーダルダイアログを閉じる。

#### イベントNo 10：「ダウンロード」リンククリック

管理項目定義のエクスポートかつ「正常終了」ステータスのタスクに対して、ユーザーが「ダウンロード」リンクをクリックすると、対応する成果物ファイルのダウンロードが開始される。

1.  **ダウンロード実行処理**
    「タスク詳細」列に表示されている「ダウンロード」リンクがクリックされると、ページコンポーネント（実際にはHTMLの<a>タグの標準動作）は、リンクのhref属性に設定されたURL（/dashboard/tasks/{taskId}/download）へ遷移を開始する。/dashboard/tasks/{taskId}/download を処理するNext.jsのAPI Routeは、対象タスクでエクスポートされた管理項目定義ファイルがAzure Blob Storageにおけるパスを、環境変数AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEFと{licenseID}/exports/{taskID}/assetsfield_def.csvの形式で組み立てて取得した後、セキュアなダウンロード用SAS URLを生成し、ブラウザをそのSAS URLへリダイレクトさせる。これにより、ファイルのダウンロードが開始される。ダウンロードされるファイル名は固定で assetsfield_def.csv となる。