# 组件：操作日志列表 (Operation Log List)

## 1. 概要 (Overview)

### 1.1 目的 (Purpose)
本组件为资产分发管理服务的用户提供一个界面，用于查看和下载其有权访问的操作日志文件。这些日志文件可能来源于两种途径：一是通过“服务器列表”界面的“操作日志导出”后台任务功能按需生成并导出的日志；二是由系统管理员通过其他方式（如手动上传）记录到系统中的操作日志。用户可以通过此界面对日志进行搜索、筛选，并下载所需的日志文件进行审计或分析。

### 1.2 用户故事/需求 (User Stories/Requirements)
- 作为一名顾客系统管理员，我希望能够在一个集中的位置查看所有与我账户相关的操作日志文件列表，以便于管理和追溯。
- 作为一名顾客系统管理员，我希望能根据日志文件名中包含的服务器名、日期或其他关键字快速搜索和筛选操作日志，以快速定位到我需要的文件。
- 作为一名顾客系统管理员，我希望能方便地从列表中下载任何一个操作日志文件到本地进行详细分析。
- 作为一名顾客系统管理员，我希望了解每个日志文件的基本元数据，如注册（上传/生成）时间、文件大小和保管期限（如果适用）。

### 1.3 高层依赖与交互 (High-Level Dependencies & Interactions)
- **用户认证与授权**: 本组件的访问及可查看的日志范围，依赖于通过Keycloak和Next.js后端实现的用户认证和授权机制（例如，基于用户的契约ID进行数据隔离）。
- **数据来源**:
    - 日志元数据（日志名、注册日期、大小、保管期限、关联的TaskID等）主要从Azure SQL Database的 `OperationLog` 表获取。
    - 部分日志（通过任务导出的）与 `Task` 表相关联。
- **文件存储与下载**:
    - 操作日志文件实际存储在Azure Blob Storage的特定容器（如 `oplogs`）中，并按契约ID等规则组织。
    - 用户下载日志文件时，前端通过后端API获取一个安全的、有时限的共享访问签名 (SAS) 链接指向Blob Storage中的文件。
- **任务关联**: 对于通过“服务器列表”的“操作日志导出”任务生成的日志，其元数据会与对应的任务记录关联。这些任务的状态可在[任务列表](./13-task-list.md)组件中跟踪。
- **导航**: 本组件通常作为登录后主界面的一个主要功能区域，用户从主导航访问。

## 2. 功能规格 (Functional Specifications)

### 2.1 主要流程 (High-Level Flow)
1.  用户成功登录门户系统后，通过侧边导航栏访问“操作日志列表”菜单项。
2.  系统加载并向用户展示其有权访问的操作日志文件列表，包含日志名、注册日期、大小、保管期限等信息。
3.  用户可以使用界面提供的筛选框输入关键字，点击搜索或按回车后，列表将根据匹配结果刷新。
4.  用户可以点击列表表头对日志进行排序。
5.  用户可以点击特定日志文件对应的“日志名”（作为下载链接）来下载该文件。
    ```mermaid
    sequenceDiagram
        participant User as 👤 用户
        participant LogListScreen as 🖥️ 操作日志列表界面
        participant NextJsApiRoutes as 🌐 Next.js API Routes
        participant OperationLogTable as 💾 OperationLog 表 (SQL DB)
        participant AzureBlobStorage as 📦 Azure Blob Storage

        User->>LogListScreen: 打开操作日志列表
        LogListScreen->>NextJsApiRoutes: 请求日志列表数据 (GET /api/operation-logs)
        NextJsApiRoutes->>OperationLogTable: 查询用户可见的日志元数据
        OperationLogTable-->>NextJsApiRoutes: 返回日志元数据列表
        NextJsApiRoutes-->>LogListScreen: 显示日志列表

        alt 用户下载日志
            User->>LogListScreen: 点击日志A的下载链接
            LogListScreen->>NextJsApiRoutes: 请求日志A的下载URL (GET /api/operation-logs/A/download-url)
            NextJsApiRoutes->>AzureBlobStorage: 为日志A生成SAS Token
            AzureBlobStorage-->>NextJsApiRoutes: 返回带SAS Token的URL
            NextJsApiRoutes-->>LogListScreen: 返回安全的下载URL
            LogListScreen->>User: (浏览器)开始下载文件
        end
    ```
6.  用户可以使用分页控件浏览更多的日志记录。
7.  用户可以点击界面右上角的“更新”图标按钮，手动刷新整个日志列表。

### 2.2 业务规则 (Business Rules)
-   **日志可见性**: 用户只能查看与其契约ID相关联的操作日志。
-   **日志命名规范**:
    *   通过后台任务（Task）导出的操作日志：文件名格式为 `{サーバ名}_oplog_{開始年月日YYYYMMDD}-{終了年月日YYYYMMDD}_task_{TaskID}.zip`。若一次任务生成多个文件，则文件名末尾可能追加序号。
    *   其他方式记录的操作日志（如手动上传）：文件名格式为 `oplog_{開始年月日YYYYMMDD}-{終了年月日YYYYMMDD}_{シーケンス番号}.zip`。
-   **保管期限**:
    *   通过任务导出的操作日志，其保留策略与关联的 `Task` 记录一致（例如，随任务记录的删除而被删除，通常基于任务保留数量上限）。其在“操作日志列表”界面显示的“保管期限”可能反映其关联任务的清理策略，或不适用而显示为“-”。
    *   其他方式记录的操作日志，其“保管期限”字段（YYYY/MM/DD hh:mm:ss）表示用户可下载此日志的截止时间。过了此期限，日志可能被系统自动清理。
-   **文件存储路径**: 操作日志文件存储在Azure Blob Storage的 `oplogs` 容器下，按契约ID分子目录组织。例如：`oplogs/{licenseId}/{日志文件名}`。
-   **下载机制**: 用户下载日志文件时，后端API为目标Blob生成一个具有读取权限且有时间限制（默认为2小时，可由`LOV`表 `AZURE_STORAGE.SAS_TTL_SECONDS` 配置）的共享访问签名 (SAS) Token，并将其附加到Blob URL后提供给前端。
-   **默认排序**: 列表默认按“注册日時”降序排列（最新的日志在前）。

### 2.3 用户界面概述 (User Interface Overview)

-   **界面草图/核心区域**: (参考 `fs.md` 图4.8.6 (1) 作为高层概念)
    1.  **筛选区**: 位于列表上方，包含一个文本输入框用于输入筛选关键字，以及搜索和清除筛选的图标按钮。
    2.  **操作日志列表表格**:
        *   列：ログ名 (日志名，兼作下载链接)、登録日時 (注册日期)、サイズ (大小)、保管期限 (保管期限)。
        *   每行代表一个操作日志文件。
    3.  **分页控件**: 位于列表下方，用于导航浏览多页日志。
    4.  **更新按钮**: 通常位于列表右上角，用于手动刷新列表。
-   **主要交互点**:
    *   用户在筛选框中输入文本，点击搜索图标（或按回车），列表根据所有列内容进行模糊匹配并刷新。
    *   用户点击清除筛选图标，清除筛选条件，列表恢复显示所有日志。
    *   用户点击可排序的列标题（如“ログ名”、“登録日時”、“サイズ”、“保管期限”），列表按该列升序或降序重新排列。
    *   用户点击“ログ名”列中的文件名链接，浏览器开始下载对应的日志文件。
    *   用户使用分页控件选择每页显示数量、跳转到特定页面或前后翻页。
    *   用户点击“更新”按钮，重新从后端加载日志列表。
-   **画面項目 (Screen Items - High Level)**:
    *   输入：筛选关键字。
    *   显示：日志文件名 (可下载)、日志注册日期和时间、日志文件大小（如 KB, MB）、日志保管截止日期和时间。
    *   用户可操作：筛选、排序、分页、下载日志文件。

### 2.4 前提条件 (Preconditions)
-   用户必须已通过身份验证并成功登录到门户系统。
-   后端用于获取操作日志列表元数据和生成下载链接的API端点必须可用。
-   Azure SQL Database 中的 `OperationLog` 表必须包含准确的日志元数据。
-   Azure Blob Storage 中的 `oplogs` 容器必须存在，并且包含用户有权访问的日志文件。

### 2.5 制约事项 (Constraints/Limitations)
-   列表的加载和筛选性能可能受限于日志记录的总数和数据库查询效率。
-   文件下载速度取决于用户的网络连接状况以及Azure Blob Storage的即时性能。
-   界面显示的“保管期限”仅为参考，实际的文件保留和清理策略由后端逻辑和配置决定。

### 2.6 注意事项 (Notes/Considerations)
-   所有显示的日期和时间（注册日期、保管期限）应根据用户的浏览器时区进行格式化和显示（遵循 `fs.md` 描述：“日付はブラウザのタイムゾーンで表示する”）。
-   对于非常大的日志文件，下载可能需要较长时间，界面最好能提供某种反馈或允许后台下载（尽管当前FS未明确要求）。
-   应考虑对筛选功能进行性能优化，例如通过后端API支持基于特定字段的精确筛选，而非仅前端模糊匹配所有列。

### 2.7 错误处理概述 (Error Handling Overview)
-   **列表加载失败**: 如果后端API调用失败或无法连接数据库导致无法获取日志列表，界面应向用户显示通用的错误提示信息（例如，“操作日志列表加载失败，请稍后重试。”），并记录详细技术错误。
-   **文件下载失败**:
    *   如果请求下载链接时后端API出错（如无法生成SAS Token），应提示用户“无法获取下载链接，请重试。”
    *   如果生成的下载链接无效（例如，SAS Token过期、文件在Blob Storage中不存在或无权限访问），用户点击下载后浏览器层面会报错或下载失败。理想情况下，后端在生成链接前应尽可能验证文件存在性。
-   **筛选/排序/分页操作失败**: 如果这些操作依赖后端API且API调用失败，也应给出通用错误提示。

### 2.8 相关功能参考 (Related Functional References)
*   **日志生成源头之一**: [服务器列表](./03-server-list.md) - 用户可能通过此功能的"操作日志导出"任务来生成一部分操作日志。
*   **关联的任务管理**: [任务列表](./13-task-list.md) - "操作日志导出"任务的执行状态、成功与否、以及可能的错误信息在此处跟踪。
*   **系统整体架构**: [系统架构](../../architecture/system-architecture.md) - 描述了操作日志数据如何从生成（任务或手动上传）到存储（Azure Blob Storage, `OperationLog`表），再到本组件呈现和下载的整体流程。
*   **核心数据模型**: [操作日志表结构](../../data-models/OperationLog.md) - 定义了本界面显示的操作日志元数据的主要来源表结构。
*   **配置参考**: [系统配置表](../../data-models/Lov.md) - 可能包含与SAS Token有效期 (`AZURE_STORAGE.SAS_TTL_SECONDS`) 等相关的配置。
*   **源功能规格**: [功能规格书](../../docs-delivery/functional-specifications/fs.md) - 本组件功能规格的原始日文描述，特别是其"4.8 操作ログ一覧"章节。

## 3. 技术设计与实现细节 (Technical Design & Implementation) - DD驱动
<!-- 此部分详细内容主要来源于《詳細設計書》的各个Sheet页 -->

### 3.1 技术栈与依赖 (Technology Stack & Dependencies)
*   **前端框架**: Next.js - 用于构建和管理轻量级网站。
*   **CSS框架**: Tailwind CSS - 用于简单高效地设置样式。
*   **ORM**: Prisma - 用于数据库操作和查询。
*   **数据存储**: Azure SQL Database
    *   主要交互表: `OperationLog` (参见 `../../data-models/operation-log.md`), `Lov` (参见 `../../data-models/lov.md`)
*   **后端API**: (源DDS未明确定义具体的Next.js API Routes，但前端事件处理逻辑暗示了对后端数据获取的需求，这部分通常通过Prisma Client在API路由中实现)

### 3.2 详细界面元素定义 (Detailed UI Element Definitions)

#### 3.2.1 页头区域 (Header Section)

| # | 控件中文名称     | 控件类型 (Type) | 建议英文ID (ID)      | 主要行为/事件 (Behavior/Event) | 校验规则/显示逻辑 (Validation/Display Logic) | 格式/备注 (Format/Notes)                     |
|---|------------------|-----------------|----------------------|--------------------------------|-------------------------------------------------|----------------------------------------------|
| 1 | 过滤器输入框     | `input`         | `filterInput`        | `onFilterInputChange`          | -                                               | 初始为空白，最大可输入100个全角字符            |
| 2 | 过滤器搜索按钮   | `button`        | `filterSearchButton` | `onFilterSearchClick`          | -                                               | 初始状态：激活                               |
| 3 | 过滤器清除按钮   | `button`        | `filterClearButton`  | `onFilterClearClick`           | -                                               | 初始状态：激活                               |
| 4 | 分页控件区域     | `div`           | `paginationArea`     | -                              | -                                               | -                                            |
| 5 | 上一页按钮       | `button`        | `prevPageButton`     | `onPrevPageClick`              | 列表页数为1时非激活，否则激活                     | 初始状态：非激活                             |
| 6 | 下一页按钮       | `button`        | `nextPageButton`     | `onNextPageClick`              | 列表页数大于等于1时激活，否则非激活               | 根据显示条件决定                               |
| 7 | 跳转指定页按钮   | `button`        | `goToPageButton`     | `onGoToPageClick`              | 列表页数大于等于1时激活，否则非激活               | 根据显示条件决定 (通常结合输入框或下拉选择)    |
| 8 | 每页行数标签     | `label`         | `pageSizeLabel`      | -                              | -                                               | "行数/页:"                                   |
| 9 | 每页行数选择     | `select` (或一组 `button`) | `pageSizeSelect`     | `onPageSizeChange`             | -                                               | 初始状态：激活，选项固定为 "10, 30, 50"        |

#### 3.2.2 操作日志列表区域 (Operation Log List Area)

| # | 控件中文名称     | 控件类型 (Type) | 建议英文ID (ID)         | 主要行为/事件 (Behavior/Event) | 校验规则/显示逻辑 (Validation/Display Logic) | 格式/备注 (Format/Notes)                                                                                                         |
|---|------------------|-----------------|-------------------------|--------------------------------|-------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------|
| 1 | 操作日志列表     | `table`         | `operationLogTable`     | -                              | -                                               | 初始状态：激活                                                                                                                     |
| 2 | "日志名"列标题   | `label`         | `logNameHeaderLabel`    | `onSortByLogName`              | -                                               | "日志名"                                                                                                                         |
| 3 | 日志名排序按钮   | `button`        | `logNameSortButton`     | `onSortByLogName`              | -                                               | 初始状态：不显示排序图标 (或默认不排序)                                                                                              |
| 4 | 日志名 (链接)    | `link`          | `logNameLink` (每行)    | `onLogNameLinkClick`           | -                                               | 从数据库获取，格式: `oplog_YYYYMMDD-yyyymmdd_NNN.zip` (YYYYMMDD为开始日, yyyymmdd为结束日, NNN为序号001-999)。显示为超链接。 |
| 5 | "注册日期"列标题 | `label`         | `createdAtHeaderLabel`  | `onSortByCreatedAt`            | -                                               | "注册日期"                                                                                                                         |
| 6 | 注册日期排序按钮 | `button`        | `createdAtSortButton`   | `onSortByCreatedAt`            | -                                               | 初始状态：显示，默认降序排序图标                                                                                                   |
| 7 | 注册日期 (文本)  | `label`         | `createdAtLabel` (每行) | -                              | -                                               | 从数据库获取，格式: "YYYY/MM/DD hh:mm:ss"，根据浏览器时区显示。                                                                     |
| 8 | "保管期限"列标题 | `label`         | `retentionAtHeaderLabel`| `onSortByRetentionAt`          | -                                               | "保管期限"                                                                                                                         |
| 9 | 保管期限排序按钮 | `button`        | `retentionAtSortButton` | `onSortByRetentionAt`          | -                                               | 初始状态：显示，默认降序排序图标 (DDS原文如此，但通常一个列表只有一个默认排序列)                                                       |
| 10| 保管期限 (文本)  | `label`         | `retentionAtLabel` (每行)| -                              | -                                               | 从数据库获取，格式: "YYYY/MM/DD hh:mm:ss"，根据浏览器时区显示。                                                                     |
| 11| "大小"列标题     | `label`         | `sizeHeaderLabel`       | `onSortBySize`                 | -                                               | "大小"                                                                                                                           |
| 12| 大小排序按钮     | `button`        | `sizeSortButton`        | `onSortBySize`                 | -                                               | 初始状态：不显示排序图标                                                                                                             |
| 13| 大小 (文本)      | `label`         | `sizeLabel` (每行)      | -                              | -                                               | 从数据库获取，格式: "xxx KB", "xxx MB" 或 "xxx GB"。                                                                                |
| 14| 滚动条           | `scroll`        | `logListScrollbar`      | -                              | 当列表内容超出显示范围时显示，否则不显示。          | -                                                                                                                                |


### 3.3 详细事件处理逻辑 (Detailed Event Handling)

#### 事件1: 初始化显示 (Initial Display)
*   **触发条件 (Trigger):** 页面组件加载完成。
*   **处理流程 (Processing Flow):**
    1.  **数据获取:**
        *   设置筛选条件 (filter) 为空字符串 (`''`)。
        *   设置当前页码 (page) 为 `1`。
        *   设置每页行数 (pageSize) 为 `10`。
        *   设置排序键 (sortKey) 为 `createdAt` (注册日期)。
        *   设置排序顺序 (sortOrder) 为 `desc` (降顺)。
        *   调用后端API (例如 `GET /api/operation-logs`)，传递上述参数获取操作日志数据。
    2.  **界面显示:**
        *   根据“3.2 详细界面元素定义”渲染界面。
        *   将获取到的日志数据显示在操作日志列表表格中。
        *   更新分页控件状态（如总页数、当前页、上一页/下一页按钮的激活状态）。

#### 事件2: 更新按钮按下 (按下“更新”图标按钮，源DDS中未明确定义此按钮，但FS中有提及，此处补充)
*   **触发条件 (Trigger):** 用户点击“更新”图标按钮。
*   **处理流程 (Processing Flow):**
    1.  **数据获取:**
        *   保持当前筛选条件不变。
        *   设置当前页码 (page) 为 `1` (或保持当前页，取决于产品设计)。
        *   设置每页行数 (pageSize) 为当前的设定值 (例如 `10`)。
        *   重置排序条件为默认排序 (例如，`sortKey: 'createdAt'`, `sortOrder: 'desc'`)。
        *   调用后端API获取操作日志数据。
    2.  **界面显示:**
        *   刷新操作日志列表表格以显示新获取的数据。
        *   更新分页控件状态。

#### 事件3: 在过滤器中输入文字 (Filter Input Change)
*   **触发条件 (Trigger):** 用户在“过滤器输入框”中输入或删除文字。
*   **处理流程 (Processing Flow):**
    1.  **界面显示:**
        *   实时更新“过滤器输入框”中显示的文字。
        *   (注: 此事件通常不直接触发数据获取，而是等待用户点击搜索按钮或按回车键。)

#### 事件4: 点击过滤器搜索按钮 (Filter Search Button Click)
*   **触发条件 (Trigger):** 用户点击“过滤器搜索按钮”或在输入框中按回车。
*   **处理流程 (Processing Flow):**
    1.  **数据获取:**
        *   获取“过滤器输入框”中的当前文字作为筛选条件 (filter)。
        *   设置当前页码 (page) 为 `1`。
        *   保持当前每页行数 (pageSize) 和排序条件 (sortKey, sortOrder) 不变 (或重置为默认排序，取决于产品设计)。
        *   调用后端API获取符合筛选条件的日志数据。
    2.  **界面显示:**
        *   刷新操作日志列表表格以显示新获取的数据。
        *   更新分页控件状态。

#### 事件5: 点击过滤器清除按钮 (Filter Clear Button Click)
*   **触发条件 (Trigger):** 用户点击“过滤器清除按钮”。
*   **处理流程 (Processing Flow):**
    1.  **数据获取:**
        *   将筛选条件 (filter) 设置为空字符串 (`''`)。
        *   设置当前页码 (page) 为 `1`。
        *   保持当前每页行数 (pageSize) 和排序条件 (sortKey, sortOrder) 不变 (或重置为默认排序)。
        *   调用后端API获取操作日志数据。
    2.  **界面显示:**
        *   清空“过滤器输入框”中的文字。
        *   刷新操作日志列表表格以显示新获取的数据。
        *   更新分页控件状态。

#### 事件6: 点击上一页按钮 (Previous Page Button Click)
*   **触发条件 (Trigger):** 用户点击“上一页按钮”。
*   **处理流程 (Processing Flow):**
    1.  **数据获取:**
        *   保持当前筛选条件 (filter)、每页行数 (pageSize) 和排序条件 (sortKey, sortOrder) 不变。
        *   将当前页码 (page) 减 `1`。
        *   调用后端API获取新页码的日志数据。
    2.  **界面显示:**
        *   刷新操作日志列表表格以显示新获取的数据。
        *   更新分页控件状态：
            *   “下一页按钮”变为激活状态。
            *   如果新的当前页码为 `1`，“上一页按钮”变为非激活状态；否则保持激活。

#### 事件7: 点击下一页按钮 (Next Page Button Click)
*   **触发条件 (Trigger):** 用户点击“下一页按钮”。
*   **处理流程 (Processing Flow):**
    1.  **数据获取:**
        *   保持当前筛选条件 (filter)、每页行数 (pageSize) 和排序条件 (sortKey, sortOrder) 不变。
        *   将当前页码 (page) 加 `1`。
        *   调用后端API获取新页码的日志数据。
    2.  **界面显示:**
        *   刷新操作日志列表表格以显示新获取的数据。
        *   更新分页控件状态：
            *   “上一页按钮”变为激活状态。
            *   如果新的当前页码等于总页数，“下一页按钮”变为非激活状态；否则保持激活。

#### 事件8: 点击跳转指定页按钮 (Go To Specific Page Button Click)
*   **触发条件 (Trigger):** 用户通过某种方式（如输入页码后点击确认，或从页码下拉列表中选择）指定了要跳转的页码。
*   **处理流程 (Processing Flow):**
    1.  **数据获取:**
        *   保持当前筛选条件 (filter)、每页行数 (pageSize) 和排序条件 (sortKey, sortOrder) 不变。
        *   将当前页码 (page) 设置为用户指定的页码。
        *   调用后端API获取指定页码的日志数据。
    2.  **界面显示:**
        *   刷新操作日志列表表格以显示新获取的数据。
        *   更新分页控件状态（“上一页”和“下一页”按钮的激活状态，根据新页码和总页数判断）。

#### 事件9: 选择每页行数 (Page Size Selection Change)
*   **触发条件 (Trigger):** 用户从“每页行数选择”控件中选择了新的行数值 (如10, 30, 50)。
*   **处理流程 (Processing Flow):**
    1.  **数据获取:**
        *   保持当前筛选条件 (filter) 和排序条件 (sortKey, sortOrder) 不变。
        *   将每页行数 (pageSize) 更新为用户选择的新值。
        *   将当前页码 (page) 设置为 `1`。
        *   调用后端API获取日志数据。
    2.  **界面显示:**
        *   刷新操作日志列表表格以显示新获取的数据。
        *   更新分页控件状态：
            *   当前页码显示为 `1`。
            *   “上一页按钮”变为非激活状态。
            *   “下一页按钮”的激活状态根据新的总页数和当前页码（1）判断。

#### 事件10: 点击日志名链接 (Log Name Link Click)
*   **触发条件 (Trigger):** 用户点击“操作日志列表”表格中某一行的“日志名”链接。
*   **处理流程 (Processing Flow):**
    1.  **界面显示 (或触发浏览器行为):**
        *   获取该日志文件对应的下载URL (通常通过调用后端API，后端API会生成包含SAS Token的Azure Blob Storage链接)。
        *   浏览器开始下载该日志文件。
        *   (详细机制参考本文档 `2.2 业务规则` 中的下载机制描述)

#### 事件11: 点击列标题进行排序 (Column Header Click for Sorting)
*   **触发条件 (Trigger):** 用户点击可排序列表头（如“日志名”、“注册日期”、“保管期限”、“大小”）。
*   **处理流程 (Processing Flow):**
    1.  **数据获取:**
        *   获取被点击列对应的物理字段名 (例如，`name`, `createdAt`, `retentionAt`, `size`) 作为新的排序键 (sortKey)。
        *   **判断排序顺序 (sortOrder):**
            *   如果新的排序键与当前的排序键相同，则切换排序顺序 (asc <-> desc)。
            *   如果新的排序键与当前的排序键不同，则将排序顺序设置为 `asc` (升序)。
        *   保持当前筛选条件 (filter) 和每页行数 (pageSize) 不变。
        *   将当前页码 (page) 设置为 `1` (或保持当前页，取决于产品设计)。
        *   调用后端API获取按新排序条件排序的日志数据。
    2.  **界面显示:**
        *   刷新操作日志列表表格以显示新获取的数据。
        *   更新被点击列标题旁的排序图标，以反映当前的排序键和排序顺序。
        *   清除其他列的排序图标（如果之前有）。
        *   更新分页控件状态。

### 3.4 数据结构与API交互 (Data Structures and API Interaction)

#### 3.4.1 主要数据流
```mermaid
sequenceDiagram
    participant User as 用户
    participant OperationLogListUI as 操作日志列表界面 (Next.js)
    participant NextJsApi as Next.js API路由 (/api/operation-logs)
    participant PrismaClient as Prisma Client (in API Route)
    participant Database as Azure SQL Database (OperationLog, Lov表)
    participant AzureBlob as Azure Blob Storage    User->>OperationLogListUI: 发生界面事件
    Note right of User: 如: 初始化, 搜索,<br>排序, 分页, 下载
    OperationLogListUI->>NextJsApi: 发起HTTP请求
    Note right of OperationLogListUI: GET /api/operation-logs<br>GET /api/operation-logs/{logId}/download-url
    Note right of NextJsApi: 请求参数:<br>filter: 筛选关键字<br>page: 当前页码<br>pageSize: 每页行数<br>sortKey, sortOrder: 排序<br>logId: 下载时使用
    NextJsApi->>PrismaClient: 调用Prisma执行数据库查询/操作
    PrismaClient->>Database: 执行SQL语句 (SELECT, etc.)
    Database-->>PrismaClient: 返回查询结果
    PrismaClient-->>NextJsApi: 返回处理后的数据

    alt 获取日志列表
        NextJsApi-->>OperationLogListUI: 返回日志列表数据 (JSON)
        OperationLogListUI->>User: 更新并显示列表
    end

    alt 请求下载链接
        NextJsApi->>AzureBlob: (需要时)为指定日志文件生成SAS Token
        AzureBlob-->>NextJsApi: 返回含SAS Token的URL
        NextJsApi-->>OperationLogListUI: 返回安全的下载URL (JSON)
        OperationLogListUI->>User: 触发浏览器下载
    end
```

#### 3.4.2 关键数据结构
*   **操作日志列表项 (前端显示用)**:
    ```typescript
    interface OperationLogListItem {
      id: string; // OperationLog.id
      logName: string; // OperationLog.name
      createdAt: string; // 格式化后的 OperationLog.createdAt
      retentionAt: string; // 格式化后的 OperationLog.retentionAt
      size: string; // 格式化后的 OperationLog.size
      downloadUrl?: string; // 临时生成的下载链接 (可选, 点击时获取)
    }
    ```
*   **API请求参数 (获取列表)**: (参见 `openapi.v1.yaml` 中 `/api/operation-logs` GET 请求的参数定义)
*   **API响应 (获取列表)**: (参见 `openapi.v1.yaml` 中 `/api/operation-logs` GET 响应的schema定义，通常包含 `data: OperationLogListItem[]` 和 `totalItems: number`)

### 3.5 数据库设计与访问详情 (Database Design and Access Details)

*   **相关表 (Related Tables):**
    *   `OperationLog`: [操作日志表定义](../../data-models/operation-log.md) - 存储操作日志的核心元数据。
    *   `Lov`: [值列表(LOV)表定义](../../data-models/lov.md) - 可能用于存储与操作日志相关的配置信息（如SAS Token有效期，虽然DDS未直接体现）。

*   **主要查询逻辑 (Main Query Logic):**
    操作日志的获取主要通过Prisma Client在Next.js的API路由中实现。以下是源DDS中提供的Prisma查询示例，用于根据筛选、分页和排序条件获取操作日志列表：

    ```typescript
    // 假设在Next.js API路由处理函数中
    // const prisma = new PrismaClient(); // Prisma客户端实例
    // const { filterValue, page, pageSize, sortKey, sortOrder, currentUser } = req.query; // 或 req.body

    const operationLogs = await prisma.operationLog.findMany({
        where: {
            licenseId: currentUser.licenseId, // 基于当前用户的契约ID进行数据隔离
            OR: [ // 简单的多字段模糊匹配 (实际应用中可能更复杂或精确)
                {
                    name: { contains: filterValue }, // 筛选日志文件名
                },
                // DDS示例中包含对日期和大小字段的contains查询，这对于datetime和int类型通常不适用
                // 更合适的可能是范围查询或特定格式的字符串匹配。
                // 以下仅为示例，实际应根据需求调整：
                // {
                //     createdAt: { gte: startDate, lte: endDate } // 如果filterValue是日期范围
                // },
                // {
                //     size: { equals: parseInt(filterValue) } // 如果filterValue是精确大小
                // }
            ]
        },
        skip: (Number(page) - 1) * Number(pageSize), // 分页：跳过的记录数
        take: Number(pageSize),                     // 分页：每页获取的记录数
        orderBy: {
            [sortKey]: sortOrder // 动态排序
        }
    });

    const totalLogs = await prisma.operationLog.count({
        where: {
            licenseId: currentUser.licenseId,
            OR: [
                { name: { contains: filterValue } },
                // ... (与findMany相同的筛选条件)
            ]
        }
    });

    // 返回 { data: operationLogs, totalItems: totalLogs }
    ```
    **说明:**
    *   上述Prisma查询展示了如何根据动态参数（筛选值、分页信息、排序字段和顺序）从`OperationLog`表中检索数据。
    *   通过`licenseId`确保用户只能看到其有权访问的日志。
    *   `OR`条件用于实现简单的跨字段文本筛选。在实际应用中，筛选逻辑可能需要更精细化，例如，支持针对特定字段的精确匹配、日期范围筛选等。源DDS中的`createdAt: { contains: filterValue }`等对于日期和数字类型的字段可能不是最佳实践，通常会替换为范围查询 (`gte`, `lte`) 或精确匹配 (`equals`)。
    *   `skip` 和 `take` 用于实现分页。
    *   `orderBy` 允许根据指定的字段和顺序进行排序。
    *   同时需要执行一个 `count` 查询以获取满足筛选条件的总记录数，用于前端分页控件。

### 3.6 关键后端逻辑/算法 (Key Backend Logic/Algorithms) - 若适用
本组件主要涉及前端界面交互和后端的数据查询与文件下载链接生成。
*   **日志列表获取逻辑**: 已在 "3.5 数据库设计与访问详情" 中通过Prisma查询示例描述。核心是通过API接收前端的筛选、排序、分页参数，然后构造数据库查询。
*   **SAS Token生成逻辑 (用于文件下载)**:
    1.  后端API接收到下载特定日志文件的请求 (包含日志ID或文件名)。
    2.  验证用户是否有权访问该日志文件 (通常基于`licenseId`)。
    3.  查询`OperationLog`表获取该日志在Azure Blob Storage中的`fileName` (或完整的Blob路径)。
    4.  使用Azure Storage SDK，针对目标Blob生成一个具有只读权限、预设有效期的服务SAS Token。
        *   SAS Token的有效期通过系统配置（如 `LOV` 表中的 `AZURE_STORAGE.SAS_TTL_SECONDS`，默认为2小时）控制。
    5.  将生成的SAS Token附加到Blob的URL后，返回给前端。

### 3.7 错误处理详情 (Detailed Error Handling)
源DDS未提供详细的错误代码表。以下为根据功能推断的常见错误场景及建议处理方式：

| 错误场景描述                     | 用户提示信息 (建议中文)          | 系统处理方式 (建议)                                                                  | 日志级别 (建议) |
|----------------------------------|--------------------------------|--------------------------------------------------------------------------------------|-----------------|
| 获取操作日志列表失败 (API异常)   | "获取日志列表失败，请稍后重试。"    | 后端记录详细错误 (API错误、数据库连接错误等)。前端显示友好提示。                                | Error           |
| 筛选/排序/分页时API请求失败      | "操作失败，请稍后重试。"          | 后端记录错误。前端显示友好提示。                                                                 | Error           |
| 请求下载链接失败 (API异常)       | "无法获取下载链接，请重试。"      | 后端记录错误 (如SAS Token生成失败)。前端显示友好提示。                                       | Error           |
| 下载链接指向的文件不存在或无权限 | (通常由浏览器层面提示下载失败) | 后端在生成SAS Token前应尽可能校验文件存在性和用户权限。若校验失败，API应返回明确错误。                     | Warn/Error      |
| 无符合条件的日志记录             | "未找到符合条件的操作日志。"      | 列表区域显示空状态提示。                                                                     | Info            |
| 输入的筛选条件格式无效           | "筛选条件格式不正确。" (若有校验) | 前端进行输入校验，提示用户修正。                                                                 | Warn            |

### 3.8 配置项 (Configuration)
源DDS未明确列出与此功能直接相关的配置项。但根据系统架构和FS描述，可能涉及以下间接配置：
*   `AZURE_STORAGE_CONNECTION_STRING`: (敏感信息, 存于Key Vault) Azure Blob Storage连接字符串，用于后端生成SAS Token。
*   `DATABASE_URL`: (敏感信息, 存于Key Vault) Azure SQL Database 连接字符串，用于Prisma Client访问数据库。
*   `LOV:AZURE_STORAGE.SAS_TTL_SECONDS`: (存储于`Lov`表) SAS Token的默认有效时间（秒），例如 `7200` (2小时)。
*   `LOV:PAGINATION.DEFAULT_PAGE_SIZE`: (存储于`Lov`表) 列表默认的每页显示行数，例如 `10`。
*   `LOV:PAGINATION.PAGE_SIZE_OPTIONS`: (存储于`Lov`表) 每页行数的可选值列表，例如 `10,30,50`。

(具体配置项及其管理请参考 `docs/guides/environment-variables.md` 和 `docs/data-models/lov.md`)

### 3.9 注意事项与其他 (Notes/Miscellaneous)
*   **日期时间显示**: 所有在界面上显示的日期和时间（注册日期、保管期限）均需根据用户浏览器的本地时区进行格式化显示，如 "YYYY/MM/DD hh:mm:ss"。
*   **文件大小显示**: 文件大小应以易读的单位（KB, MB, GB）显示，并保留适当的小数位数。
*   **性能考虑**:
    *   对于大量的操作日志记录，数据库查询（特别是带复杂筛选条件的）性能至关重要。应确保`OperationLog`表在`licenseId`, `name`, `createdAt`, `retentionAt`等常用查询和排序列上有合适的索引。
    *   前端列表渲染大量数据时，应考虑使用虚拟滚动或更优化的渲染策略以避免性能问题。
*   **安全性**:
    *   确保SAS Token的权限被限制为最小所需（通常是只读），且有效期不宜过长。
    *   所有后端API调用都应经过严格的身份认证和授权检查。
*   **筛选逻辑的改进**: 源DDS中对日期和大小字段使用`contains`进行筛选可能不准确。实际实现中，应考虑为日期提供范围选择器，为大小提供更精确的比较操作。
