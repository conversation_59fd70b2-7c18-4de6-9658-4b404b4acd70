/**
 * @fileoverview Azure Storage・Automationの認証済みクライアントを提供するモジュール
 * @description
 * Azure Storage・Automation等のクライアント初期化・認証・呼び出しを一元管理するモジュール。
 * 環境変数から設定値を取得し、Blob/File/Automation RESTの認証済みクライアントを提供。
 * Azureリソースへのアクセス・認証・呼び出しの共通処理を集約し、各Functionからの利用を簡素化。
 * 環境変数の検証・認証トークン自動付与・エンドポイントの一元管理で、運用性・保守性を向上。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */
import { BlobServiceClient } from "@azure/storage-blob";
import { ShareServiceClient } from "@azure/storage-file-share";
import { DefaultAzureCredential } from "@azure/identity";
import { ServiceBusClient } from "@azure/service-bus";

// 1. 環境変数から設定値を取得する
const azureFilesConnectionString = process.env.AZURE_STORAGE_FILES_CONNECTION_STRING;
const azureBlobConnectionString = process.env.AZURE_STORAGE_BLOB_CONNECTION_STRING;
const automationAccount = process.env.AZURE_AUTOMATION_ACCOUNT_NAME;
const subscriptionId = process.env.SUBSCRIPTION_ID;
const resourceGroupName = process.env.RESOURCE_GROUP_NAME;
const fullyQualifiedNamespace = process.env.AZURE_SERVICEBUS_NAMESPACE_HOSTNAME__fullyQualifiedNamespace || process.env.AZURE_SERVICEBUS_NAMESPACE_HOSTNAME;
const azureManagementBaseUrl = process.env.AZURE_MANAGEMENT_BASE_URL || "https://management.azure.com";

// 2. 重要な環境変数が設定されているか実行時にチェックする
if (
  !azureFilesConnectionString ||
  !azureBlobConnectionString ||
  !automationAccount ||
  !subscriptionId ||
  !resourceGroupName ||
  !fullyQualifiedNamespace
) {
  throw new Error(
    "必要な環境変数が設定されていないため、処理を継続できない。次の変数を確認すること：" +
    "AZURE_STORAGE_FILES_CONNECTION_STRING, AZURE_STORAGE_BLOB_CONNECTION_STRING, AZURE_AUTOMATION_ACCOUNT_NAME, " +
    "SUBSCRIPTION_ID, RESOURCE_GROUP_NAME, AZURE_SERVICEBUS_NAMESPACE_HOSTNAME__fullyQualifiedNamespace"
  );
}

/**
 * Azure Blob Storageクライアントを作成するファクトリー関数
 * 並行実行時の安全性を確保するため、リクエスト毎に新しいインスタンスを生成
 * @returns BlobServiceClientインスタンス
 */
export function createBlobServiceClient(): BlobServiceClient {
  return BlobServiceClient.fromConnectionString(azureBlobConnectionString!);
}

/**
 * Azure Files共有クライアントを作成するファクトリー関数
 * 並行実行時の安全性を確保するため、リクエスト毎に新しいインスタンスを生成
 * @returns ShareServiceClientインスタンス
 */
export function createShareServiceClient(): ShareServiceClient {
  return ShareServiceClient.fromConnectionString(azureFilesConnectionString!);
}

/**
 * Azure Automation管理用の認証済みfetchリクエストを実行する関数
 * 並行実行時の安全性を確保するため、リクエスト毎に新しいクレデンシャルを使用
 * 本番環境ではAzure托管標識を使用し、テスト環境では認証をスキップする
 * @param url リクエストURL（相対パス）
 * @param options fetchオプション
 * @returns fetchレスポンス
 */
async function authenticatedFetch(url: string, options: RequestInit = {}): Promise<Response> {
  let headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...options.headers as Record<string, string>,
  };

  // 本番環境の場合はAzure托管標識による認証を実行
  if (azureManagementBaseUrl.startsWith('https://')) {
    const credential = new DefaultAzureCredential();
    const { token } = await credential.getToken(`${azureManagementBaseUrl}/.default`);
    headers['Authorization'] = `Bearer ${token}`;
  }

  const fullUrl = `${azureManagementBaseUrl}${url}`;

  return fetch(fullUrl, {
    ...options,
    headers,
  });
}

/**
 * Service Busクライアントを作成するファクトリー関数
 * 並行実行時の安全性を確保するため、リクエスト毎に新しいインスタンスを生成
 * @returns ServiceBusClientインスタンス
 */
export function createServiceBusClient(): ServiceBusClient {
  if (fullyQualifiedNamespace!.startsWith("Endpoint")) {
    return new ServiceBusClient(fullyQualifiedNamespace!);
  } else {
    return new ServiceBusClient(fullyQualifiedNamespace!, new DefaultAzureCredential());
  }
}

/**
 * 指定された情報でAzure Automationジョブを作成する関数
 * @param jobName ジョブの一意な名前 (通常はtaskId)
 * @param runbookName 実行するRunbookの名前
 * @param parameters Runbookに渡すパラメータ
 * @param runOn ジョブを実行するHybrid Worker Groupの名前
 */
export async function createAutomationJob(
  jobName: string,
  runbookName: string,
  parameters: any,
  runOn: string
) {
  const url = `/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}/providers/Microsoft.Automation/automationAccounts/${automationAccount}/jobs/${jobName}?api-version=2023-11-01`;
  const body = {
    properties: {
      runbook: { name: runbookName },
      parameters,
      runOn,
    },
  };

  const response = await authenticatedFetch(url, {
    method: 'PUT',
    body: JSON.stringify(body),
  });

  if (!response.ok) {
    throw new Error(`Azure Automation job creation failed: ${response.status} ${response.statusText}`);
  }

  return response;
}

/**
 * 指定されたAzure Automationジョブを停止する関数
 * @param jobName 停止するジョブの名前
 */
export async function stopAutomationJob(jobName: string) {
  const url = `/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}/providers/Microsoft.Automation/automationAccounts/${automationAccount}/jobs/${jobName}/stop?api-version=2023-11-01`;

  const response = await authenticatedFetch(url, {
    method: 'POST',
  });

  if (!response.ok) {
    throw new Error(`Azure Automation job stop failed: ${response.status} ${response.statusText}`);
  }

  return response;
}

/**
 * Azure Automationジョブ取得結果を表すインターフェース
 */
export interface AutomationJobStatusResult {
  /** ジョブが存在する場合のジョブデータ */
  jobData?: any;
  /** HTTPステータスコード */
  statusCode: number;
  /** ジョブの存在状況 */
  exists: boolean;
}

/**
 * 指定されたAzure Automationジョブの状態を取得する関数
 *
 * 設計仕様に基づき、以下の3つの状況を明確に区別する：
 * 1. ジョブが存在している場合 - HTTP 200 (OK)
 * 2. ジョブが存在しない場合 - HTTP 404
 * 3. API呼び出し失敗 - HTTP 200/404以外
 *
 * @param jobName 状態を取得するジョブの名前
 * @returns AutomationJobStatusResult オブジェクト
 * @throws HTTP 200/404以外のステータスコードの場合のみ例外をスロー
 */
export async function getAutomationJobStatus(jobName: string): Promise<AutomationJobStatusResult> {
  const url = `/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}/providers/Microsoft.Automation/automationAccounts/${automationAccount}/jobs/${jobName}?api-version=2023-11-01`;

  const response = await authenticatedFetch(url, {
    method: 'GET',
  });

  if (response.status === 200) {
    // ジョブが存在している場合はHTTPステータスコードが200（OK）のレスポンスが返却される
    const jobData = await response.json();
    return {
      jobData,
      statusCode: 200,
      exists: true,
    };
  } else if (response.status === 404) {
    // ジョブが存在しない場合はHTTPステータスコードが404のレスポンスが返却される
    return {
      statusCode: 404,
      exists: false,
    };
  } else {
    // API の呼び出しに失敗した場合は、HTTPステータスコードが200（OK）または404以外のレスポンスが返却される
    throw new Error(`Azure Automationジョブ状態取得に失敗しました: ${response.status} ${response.statusText}`);
  }
}
