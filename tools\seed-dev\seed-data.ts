/**
 * @fileoverview 简化版开发环境种子数据生成スクリプト
 * @description JCS端点管理システムの開発環境用種子データを生成する
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

// 环境变量加载
import * as dotenv from 'dotenv';
dotenv.config();

import { getPrismaClient, connectDatabase, disconnectDatabase, testDatabaseConnection, getDatabaseStats } from './utils/database-simple';

// データ生成器のインポート
import { LovGenerator } from './generators/lov.generator';
import { LicenseGenerator } from './generators/license.generator';
import { ServerGenerator } from './generators/server.generator';
import { TaskGenerator } from './generators/task.generator';
import { ProductManualGenerator } from './generators/product-manual.generator';
import { ProductMediaGenerator } from './generators/product-media.generator';
import { OperationLogGenerator } from './generators/operation-log.generator';
import { ProvidedFileGenerator } from './generators/provided-file.generator';
import { SupportFileGenerator } from './generators/support-file.generator';
import { PlanGenerator } from './generators/plan.generator';

/**
 * 環境の妥当性をチェックする
 */
function validateEnvironment(): void {
  const dbUrl = process.env.MSSQL_PRISMA_URL;
  
  if (!dbUrl) {
    throw new Error('環境変数 MSSQL_PRISMA_URL が設定されていません');
  }

  if (!dbUrl.includes('database=dev')) {
    throw new Error('このツールは開発環境(database=dev)でのみ実行できます');
  }

  console.log('✅ 環境チェックが完了しました (開発環境)');
}

/**
 * 全データをクリーンアップする
 */
async function cleanupAllData(): Promise<void> {
  console.log('🧹 データクリーンアップを開始します');

  const prisma = getPrismaClient();

  try {
    // 外部キー制約を考慮した削除順序
    await prisma.planSupport.deleteMany({});
    await prisma.planProvidedFile.deleteMany({});
    await prisma.planManual.deleteMany({});
    await prisma.planProduct.deleteMany({});
    await prisma.licensePlan.deleteMany({});
    await prisma.operationLog.deleteMany({});
    await prisma.task.deleteMany({});
    await prisma.server.deleteMany({});
    await prisma.notification.deleteMany({});
    await prisma.license.deleteMany({});
    await prisma.plan.deleteMany({});
    await prisma.productMedia.deleteMany({});
    await prisma.productManual.deleteMany({});
    await prisma.providedFile.deleteMany({});
    await prisma.supportFile.deleteMany({});
    await prisma.lov.deleteMany({});

    console.log('✅ データクリーンアップが完了しました');
  } catch (error) {
    console.error('❌ データクリーンアップに失敗しました:', error);
    throw error;
  }
}

/**
 * 統計情報を表示する
 */
async function showStats(): Promise<void> {
  try {
    const stats = await getDatabaseStats();
    console.log('=== データベース統計情報 ===');
    console.log(`ライセンス: ${stats.licenseCount}件`);
    console.log(`サーバー: ${stats.serverCount}件`);
    console.log(`タスク: ${stats.taskCount}件`);
    console.log(`LOV: ${stats.lovCount}件`);
    console.log(`プラン: ${stats.planCount}件`);
    console.log(`製品マニュアル: ${stats.productManualCount}件`);
    console.log(`製品媒体: ${stats.productMediaCount}件`);
    console.log(`提供ファイル: ${stats.providedFileCount}件`);
    console.log(`サポートファイル: ${stats.supportFileCount}件`);
    console.log(`操作ログ: ${stats.operationLogCount}件`);
    console.log(`取得時刻: ${stats.timestamp}`);
    console.log('========================');
  } catch (error) {
    console.error('❌ 統計情報の取得に失敗しました:', error);
  }
}

/**
 * メイン実行関数
 */
async function main(): Promise<void> {
  try {
    console.log('🌱 JCS開発環境種子データ生成を開始します');
    
    // 環境チェック
    validateEnvironment();
    
    // データベース接続
    await connectDatabase();
    const isConnected = await testDatabaseConnection();
    if (!isConnected) {
      throw new Error('データベース接続に失敗しました');
    }
    
    // クリーンアップ
    await cleanupAllData();
    
    // 生成前の統計情報
    console.log('=== 生成前のデータベース状態 ===');
    await showStats();
    
    // データ生成器を順序立てて実行（依存関係を考慮した順序）
    const prisma = getPrismaClient();
    const generators = [
      new LovGenerator(prisma),              // 1. LOVデータ（他の生成器が参照）
      new LicenseGenerator(prisma),          // 2. ライセンス（サーバーが参照）
      new ProductManualGenerator(prisma),    // 3. 製品マニュアル（プランが参照）
      new ProductMediaGenerator(prisma),     // 4. 製品媒体（プランが参照）
      new ProvidedFileGenerator(prisma),     // 5. 提供ファイル（プランが参照）
      new SupportFileGenerator(prisma),      // 6. サポートファイル（プランが参照）
      new PlanGenerator(prisma),             // 7. プラン（上記データを参照）
      new ServerGenerator(prisma),           // 8. サーバー（ライセンスを参照）
      new TaskGenerator(prisma),             // 9. タスク（サーバーを参照）
      new OperationLogGenerator(prisma),     // 10. 操作ログ（ライセンス・タスクを参照）
    ];
    
    let totalGenerated = 0;
    const startTime = Date.now();
    
    for (const generator of generators) {
      console.log(`📊 ${generator.getGeneratorName()}の生成を開始します (予定: ${generator.getExpectedCount()}件)`);
      
      const generatorStartTime = Date.now();
      const count = await generator.execute();
      const duration = Date.now() - generatorStartTime;
      
      totalGenerated += count;
      console.log(`✅ ${generator.getGeneratorName()}の生成が完了しました (${count}件, ${duration}ms)`);
    }
    
    // 生成後の統計情報
    console.log('=== 生成後のデータベース状態 ===');
    await showStats();
    
    const totalTime = Date.now() - startTime;
    
    // 結果サマリー
    console.log('');
    console.log('🎉 ===== 種子データ生成完了 =====');
    console.log(`📊 総生成件数: ${totalGenerated.toLocaleString()}件`);
    console.log(`⏱️  総実行時間: ${(totalTime / 1000).toFixed(2)}秒`);
    console.log('✅ 開発環境の準備が完了しました！');
    console.log('');
    
  } catch (error) {
    console.error('❌ 実行中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await disconnectDatabase();
  }
}

// スクリプトとして直接実行された場合のみmainを実行
if (require.main === module) {
  main();
}
