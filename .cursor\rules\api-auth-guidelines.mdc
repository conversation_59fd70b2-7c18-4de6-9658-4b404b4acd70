---
description: 
globs: 
alwaysApply: false
---
# API Authentication and Validation Guidelines (for apps/jcs-endpoint-nextjs)

This document outlines the mandatory rules and procedures for creating and modifying API endpoints within the `apps/jcs-endpoint-nextjs/app/api/` directory, specifically concerning authentication, authorization, and data validation. Adherence to these guidelines is critical for maintaining security and predictable API behavior.

**Primary References & Context:**
*   Existing API routes in `app/api/` (e.g., `login/route.ts`, `callback/route.ts`, `passwords/[id]/route.ts`, `refreshToken/route.ts`).
*   Session management logic in `app/lib/session.ts` (using Iron Session).
*   Authentication-related environment variables defined in `app/lib/definitions.ts` (e.g., `ENV.KEYCLOAK_*`).
*   User and session type definitions in `app/lib/session.ts` (`SessionData`).
*   Error handling utilities from `app/lib/portal-error.ts`.
*   Relevant component design documents in the Monorepo's `docs/components/` that describe API interactions and security requirements.

## Core Principles

1.  **Authentication is Mandatory for Protected Resources:** All API endpoints that handle sensitive data or perform restricted actions **MUST** be protected. Unauthenticated access should only be permitted for public data (e.g., system notifications via `app/api/notifications/system/route.ts`) or specific authentication lifecycle endpoints (login, callback, logout).
2.  **Session-Based Protection (Iron Session):**
    *   User authentication state is managed server-side using **Iron Session**, configured in `app/lib/session.ts`.
    *   Protected API routes **MUST** retrieve the current session using `getIronSession<SessionData>(cookies(), sessionOptions)`.
    *   The presence and validity of `session.user.userId` (and potentially `session.user.licenseId` or other relevant fields) **MUST** be checked to confirm an authenticated and authorized user.
    *   If authentication fails, a `401 Unauthorized` response (or appropriate error) **MUST** be returned.
3.  **Keycloak Integration Workflow:**
    *   Authentication is federated to **Keycloak**.
    *   `app/api/login/route.ts`: Initiates the Keycloak OIDC authorization code flow by redirecting the user to Keycloak.
    *   `app/api/callback/route.ts`: Handles the callback from Keycloak. It exchanges the authorization code for tokens, decodes the JWT access token, verifies the associated license (`ServerData.checkLicense`), creates an audit log event, and establishes the Iron Session for the user, storing necessary user details and the refresh token.
    *   `app/api/logout/route.ts`: Handles user logout. This includes attempting to invalidate the Keycloak session (via Keycloak Admin API if applicable, using the access token obtained via refresh token flow) and **MUST** destroy the local Iron Session using `session.destroy()`.
    *   `app/api/refreshToken/route.ts`: Handles refreshing Keycloak access tokens using the `refresh_token` stored in the Iron Session. The new tokens (access and refresh) **MUST** be saved back into the session, and the session's `maxAge` should be updated.
4.  **Authorization Checks:**
    *   Beyond basic authentication, if an API endpoint requires more granular authorization (e.g., based on user roles, license type, or specific permissions for a resource), these checks **MUST** be implemented after successful authentication.
    *   Example: `app/api/oplogs/[licenseId]/[fileName]/route.ts` checks if `session.user.licenseId` matches the requested `licenseId`.
5.  **Input Validation:**
    *   All incoming request data (URL parameters, query parameters, request body) **MUST** be rigorously validated before processing.
    *   Utilize predefined regex patterns from `app/lib/definitions.ts` (e.g., `atLeastTwoKindsRegex` for passwords, `IsNumber`).
    *   For data types and presence, perform explicit checks.
    *   If validation fails, the API **MUST** return an appropriate HTTP error status (typically `400 Bad Request`) with a clear error message, preferably using keys from `PORTAL_ERROR_MESSAGES` in `app/lib/definitions.ts`.
6.  **Audit Logging:**
    *   Critical authentication events (login success, login failure, logout) **MUST** be logged using `ServerAction.createAuditEvent()` as demonstrated in `app/api/audit-login-logs/route.ts` and `app/api/callback/route.ts`.

## Mandatory Implementation Patterns & Examples

1.  **Protecting an API Route:**
    ```typescript
    // File: app/api/some-protected-resource/route.ts
    import { getIronSession } from "iron-session";
    import { cookies } from "next/headers";
    import { NextRequest, NextResponse } from "next/server";
    import { SessionData, sessionOptions } from "@/app/lib/session";
    import { handleApiError } from "@/app/lib/portal-error";
    import { PORTAL_ERROR_MESSAGES } from "@/app/lib/definitions";

    export async function GET(req: NextRequest) {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);

      if (!session.user || !session.user.userId) {
        return NextResponse.json({ error: PORTAL_ERROR_MESSAGES.EMEC0005 }, { status: 401 }); // Or a more specific "Unauthorized" message
      }

      // User is authenticated, proceed with authorization and logic
      // const userId = session.user.userId;
      // const licenseId = session.user.licenseId;

      try {
        // ... your logic for the protected resource ...
        // const data = await fetchDataForUser(userId, licenseId);
        return NextResponse.json({ message: "Success", data: {} /* data */ });
      } catch (error) {
        return handleApiError(error); // Logs error and returns standard server error response
      }
    }
    ```

2.  **Keycloak Token Exchange (Example from `api/callback/route.ts`):**
    *   Construct token URL: `${ENV.KEYCLOAK_INTERNAL_DOMAIN_NAME}/realms/${ENV.KEYCLOAK_REALM}/protocol/openid-connect/token`.
    *   Parameters: `grant_type`, `code`, `client_id`, `client_secret`, `redirect_uri`.
    *   Headers: `Content-Type: application/x-www-form-urlencoded`. (Note: `Authorization: Bearer ${basicAuth}` was seen but Keycloak token endpoint typically uses client credentials in body or Basic Auth for confidential clients. Follow existing pattern.)
    *   Decode JWT: Use `jwt.decode()` from `jsonwebtoken`.

3.  **Password Change (`app/api/passwords/[id]/route.ts`):**
    *   This route exemplifies a multi-step process involving Keycloak:
        1.  Validate new password against complexity rules (e.g., length, character types, not same as user ID, not same as current password).
        2.  Verify current password and OTP by attempting a `grant_type: 'password'` token exchange with Keycloak.
        3.  If successful, use the obtained `access_token` to call Keycloak's Admin API (`/admin/realms/.../reset-password`) to update the password.
    *   AI **MUST** follow this established pattern for any operations requiring verification against Keycloak credentials and subsequent administrative actions on Keycloak.

## Security Considerations

*   **Sensitive Data in Session:** Minimize data in `SessionData`. `refreshToken` is highly sensitive; ensure `sessionOptions` in `app/lib/session.ts` use a strong `password`, `httpOnly: true`, and `secure: true` (for production environments).
*   **Environment Variables:** All Keycloak credentials (`clientSecret`, domain names, etc.) and other secrets **MUST** be managed via environment variables (accessed via `ENV` object from `app/lib/definitions.ts`) and **NEVER** hardcoded.
*   **Error Message Verbosity:** While logging should be detailed, error messages returned to the client should be generic enough not to reveal internal system details or vulnerabilities (e.g., use `PORTAL_ERROR_MESSAGES`).
*   **CSRF Protection:** Next.js API Routes, when used with methods like POST/PUT/DELETE from traditional HTML forms without JavaScript, might be vulnerable if not explicitly protected. However, for client-side SPA-like interactions using `fetch`, and especially if Server Actions are preferred for mutations, the risk profile changes. Iron Session's `sameSite: "lax"` helps. Server Actions have built-in CSRF. If creating traditional form submission APIs, evaluate CSRF needs.

---

**NOTE TO CURSOR:**
1.  When creating or modifying API routes in `app/api/`, authentication via Iron Session is the default expectation for any route not explicitly public.
2.  Strictly adhere to the established patterns for interacting with Keycloak as seen in existing authentication-related API routes (`login`, `callback`, `logout`, `refreshToken`, `passwords`).
3.  Input validation for all request parameters and body content is mandatory. Use predefined constants and error messages from `app/lib/definitions.ts` where applicable.
4.  Always use `handleApiError` from `app/lib/portal-error.ts` for consistent server-side error responses in API routes.
5.  Refer to relevant API design specifications in the Monorepo's `docs/apis/` or `docs/components/` directories.


---