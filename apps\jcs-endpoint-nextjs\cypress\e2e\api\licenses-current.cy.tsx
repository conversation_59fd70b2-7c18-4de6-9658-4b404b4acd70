describe("API エンドポイントの可用性のテスト", () => {
  describe("API - ライセンス情報取得", () => {
    const validCredentials = {
      userId: "hitachi.taro.aa",
      password: "changeit!@#",
    };

    describe("セッションが存在しない場合", () => {
      it("セッションが存在しない場合、エラーが返される", () => {
        cy.request({
          method: "GET",
          url: "/api/licenses/current",
          failOnStatusCode: false,
        }).then((response) => {
          expect(response.status).to.eq(401);
        });
      });
    });

    describe("セッションが存在する場合", () => {
      // @ts-ignore
      let cookies;

      before(() => {
        Cypress.Cookies.debug(true);
        cy.visit("/login");
        cy.get("#userId").type(validCredentials.userId);
        cy.get("#password").type(validCredentials.password);
        cy.get("button").click();

        cy.wait(3000);
        cy.getCookies()
          .should("have.length.gt", 0)
          .then((cookiesArray) => {
            cookies = cookiesArray;
          });
      });

      it("ライセンス情報が正常に取得できる", () => {
        cy.request({
          method: "GET",
          url: "/api/licenses/current",
          headers: {
            // @ts-ignore
            Cookie: `${cookies[0].name}=${cookies[0].value}`,
          },
        }).then((response) => {
          expect(response.status).to.eq(200);

          expect(response.body).to.be.a("object");

          expect(response.body.id).to.be.a("string");
          expect(response.body.type).to.be.a("string");
          expect(response.body.expiredAt).to.be.a("string");
          expect(response.body.maxClients).to.be.a("number");
          expect(response.body.typeLov).to.be.a("object");
        });
      });
    });
  });
});
