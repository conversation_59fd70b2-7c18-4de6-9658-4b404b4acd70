# 📊 JCS Endpoint Monorepo 単体テストレポート

このディレクトリには、JCS Endpoint Monorepoプロジェクトの完全な単体テストケースデータとレポートが含まれています。

---

## 📁 ファイル説明

### `test-cases.json`
**形式**: JSON構造化データ
**内容**: 全417個のテストケースの完全な情報
**構造**: 3階層構造

```
プロジェクト名 (3個)
├── 大項目 (12個)
    └── 小項目 (27個)
        └── テストケースリスト (417個)
```

### `UT動作確認チェックリスト_template.xlsx`
**形式**: Excel模板文件
**用途**: 客户提供的测试报告模板
**包含**: 表紙、目次、统计信息等基础工作表

### `UT動作確認チェックリスト_generated.xlsx`
**形式**: 生成的完整测试报告
**内容**: 基于test-cases.json数据填充的完整报告
**包含**: 所有测试用例的详细信息和统计数据

### JSON構造詳細

#### ルートレベル構造
```json
{
  "metadata": {
    "generatedAt": "2025-07-16T05:37:49.532Z",
    "generatedBy": "extract-test-cases-json.js",
    "description": "JCS Endpoint Monorepo テストケース抽出結果",
    "structure": "プロジェクト名 -> 大項目 -> 小項目 -> テストケースリスト"
  },
  "statistics": {
    "totalProjects": 3,
    "totalMajorCategories": 12,
    "totalMinorCategories": 27,
    "totalTestCases": 417,
    "byProject": { ... }
  },
  "testCases": { ... }
}
```

#### プロジェクトレベル構造
```json
"testCases": {
  "jcs-endpoint-nextjs": {
    "displayName": "ポータル",
    "type": "frontend",
    "categories": { ... }
  },
  "jcs-backend-services-standard": {
    "displayName": "タスク Function App",
    "type": "backend",
    "categories": { ... }
  },
  "jcs-backend-services-long-running": {
    "displayName": "Runbook ジョブ Function App",
    "type": "backend",
    "categories": { ... }
  }
}
```

#### 大項目レベル構造
```json
"categories": {
  "app": {
    "displayName": "app",
    "subCategories": { ... }
  },
  "lib": {
    "displayName": "lib",
    "subCategories": { ... }
  },
  "ui": {
    "displayName": "ui",
    "subCategories": { ... }
  }
}
```

#### 小項目レベル構造
```json
"subCategories": {
  "route": {
    "displayName": "route",
    "testCases": [ ... ]
  },
  "servers": {
    "displayName": "servers",
    "testCases": [ ... ]
  }
}
```

#### テストケース構造
各テストケースには以下のフィールドが含まれます：
```json
{
  "テストケース名": "指定されたフィルタ条件とページサイズに基づいてページ数を計算する",
  "試験観点": "ページ数計算機能の動作確認",
  "試験対象": "fetchServersPages関数のページ数計算ロジック",
  "試験手順": "1. 指定されたフィルタ条件とページサイズでfetchServersPagesを呼び出し",
  "確認項目": "- 正しいページ数が計算されること"
}
```

**フィールド説明**：
- `テストケース名`: テストケース名称
- `試験観点`: テスト観点/目的
- `試験対象`: テスト対象のオブジェクト/関数
- `試験手順`: テスト手順（改行文字を保持）
- `確認項目`: 確認項目（改行文字を保持）

#### 完全な構造例
```json
{
  "metadata": { ... },
  "statistics": { ... },
  "testCases": {
    "jcs-endpoint-nextjs": {
      "displayName": "ポータル",
      "type": "frontend",
      "categories": {
        "lib": {
          "displayName": "lib",
          "subCategories": {
            "servers": {
              "displayName": "servers",
              "testCases": [
                {
                  "テストケース名": "指定されたフィルタ条件とページサイズに基づいてページ数を計算する",
                  "試験観点": "ページ数計算機能の動作確認",
                  "試験対象": "fetchServersPages関数のページ数計算ロジック",
                  "試験手順": "1. 指定されたフィルタ条件とページサイズでfetchServersPagesを呼び出し",
                  "確認項目": "- 正しいページ数が計算されること"
                }
              ]
            }
          }
        }
      }
    }
  }
}
```

### アクセス方法
特定のテストケースにアクセスする場合：
```javascript
// 例：ポータルプロジェクトのlibカテゴリのserversテストケースを取得
const testCases = data.testCases["jcs-endpoint-nextjs"].categories["lib"].subCategories["servers"].testCases;
```

---

## 📊 統計情報

| プロジェクト | 大項目数 | 小項目数 | テストケース数 |
|-------------|---------|---------|---------------|
| **ポータル** | 3 | 14 | 185 |
| **タスク Function App** | 7 | 9 | 161 |
| **Runbook ジョブ Function App** | 2 | 4 | 71 |
| **合計** | **12** | **27** | **417** |

---

## 🔄 データ更新

### データ再生成
```bash
# プロジェクトルートディレクトリで実行
node scripts/extract-test-cases-json.js
```

### データソース
- `__tests__` ディレクトリから全テストファイルを自動スキャン
- JSDocコメント内の日本語キーワードを解析
- Jest実行結果との整合性を検証

---

## 📋 客户报告生成

### 快速生成
```bash
# 生成完整的客户测试报告
npm run generate-customer-report
```

### 生成内容
基于 `test-cases.json` 数据和客户模板生成完整的Excel测试报告：

#### 工作表结构
- **表紙**: 封面页（保持原模板格式）
- **目次**: 自动更新的目录页
- **1.試験観点・テスト結果集計シート**: 统计信息汇总
- **2.1, 2.2, 2.3**: フロントエンド各大項目详细测试用例
- **3.1 ~ 3.7**: バックエンド各大項目详细测试用例  
- **4.1, 4.2**: Runbookジョブ各大項目详细测试用例

#### 章节映射
| 项目 | 章节 | 标题 | 测试用例数 |
|------|------|------|-----------|
| jcs-endpoint-nextjs | 2章 | フロントエンド | 185个 |
| jcs-backend-services-standard | 3章 | バックエンド | 161个 |
| jcs-backend-services-long-running | 4章 | Runbookジョブ | 71个 |

### 预览和配置
```bash
# 查看生成预览信息
node scripts/example-customer-report.js
```

### 详细说明
参考 `客户报告生成指南.md` 获取完整的使用说明和配置选项。

---

## 📋 分類ルール

### プロジェクト名
- `jcs-endpoint-nextjs`: ポータル（フロントエンドNext.jsアプリケーション）
- `jcs-backend-services-standard`: タスク Function App（標準Azure Functions バックエンドサービス）
- `jcs-backend-services-long-running`: Runbook ジョブ Function App（長時間実行Azure Functions バックエンドサービス）

### 大項目
テストファイルの第一階層ディレクトリに基づく：
- `app`: アプリケーションルーティングとAPIテスト
- `lib`: ライブラリ関数とユーティリティテスト
- `ui`: UIコンポーネントテスト
- など...

### 小項目
テストファイル名に基づく（`.test.ts`/`.test.tsx`拡張子を除去）：
- `route`: ルーティングテスト
- `servers`: サーバー関連テスト
- `tasks`: タスク関連テスト
- など...

---

## 🎯 品質保証

### コメント規範
- ✅ **100%準拠**: 全テストケースがdev-guide.md規範に準拠
- ✅ **JSDoc形式**: 標準の `/** ... */` 形式を使用
- ✅ **必須キーワード**: 4つの必須日本語キーワードを含む
- ✅ **正しい順序**: キーワードが正しい順序で配置

### データ完全性
- ✅ **完全カバレッジ**: 全417個のテストケースを含む
- ✅ **形式統一**: 統一されたデータ構造と形式
- ✅ **改行保持**: 元のコメント内の改行文字を保持
- ✅ **検証済み**: Jest実行結果と100%一致

---

## 💡 使用推奨事項

### 開発チーム
- 既存テストケースのコメント形式を参考にする
- 確立された分類と命名規範に従う
- 統一された日本語キーワードを使用する

### 品質保証
- 定期的にチェックスクリプトを実行してコメント規範を検証
- データを使用してテストカバレッジ分析を実施
- テストケース数と品質トレンドを監視

### ドキュメント保守
- 新しいテストファイル追加後にデータを再生成
- 分類構造の一貫性を保持
- 統計情報を適時更新

---

**最終更新**: 2025年7月16日
**データバージョン**: v1.0
**生成ツール**: `scripts/extract-test-cases-json.js`
