/**
 * @file password-modal.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2024, 2025, Hitachi Solutions, Ltd.
 */

"use client";

import clsx from "clsx";
import { Tooltip, TooltipInterface } from "flowbite";
import { useRouter } from "next/navigation";
import { FormEvent, useEffect, useState } from "react";
import {
  atLeastTwoKindsRegex,
  IsNumber,
  LoginFormPasswordPattern, ModalProps, PORTAL_ERROR_MESSAGES
} from "../lib/definitions";
import RefreshToken from "./refreshToken";
import Spinner from "./spinner";

interface PasswordModalProps extends ModalProps {
  id: string;
  userId: string;
}

// パスワード変更ダイアログコンポーネント
export default function PasswordModal({
  id,
  userId,
  isOpen,
  onClose,
  onOK,
  onError,
}: PasswordModalProps) {
  const [passwordTooltip, setPasswordTooltip] = useState<TooltipInterface>();
  const [newPasswordTooltip, setNewPasswordTooltip] =
    useState<TooltipInterface>();
  const [confirmPasswordTooltip, setConfirmPasswordTooltip] =
    useState<TooltipInterface>();
  const [oneTimePasswordTooltip, setOneTimePasswordTooltip] =
    useState<TooltipInterface>();
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [random, setRandom] = useState(1);
  const router = useRouter(); // ルーターのインスタンス取得
  const closeModal = () => {
    onClose && onClose();
  };
  const initTooltip = () => {
    if (passwordTooltip) {
      passwordTooltip.hide();
    } else {
      setPasswordTooltip(
        new Tooltip(
          document.getElementById("passwordTooltip"),
          document.getElementById("password"),
          {
            placement: "right",
            triggerType: "none",
          },
          {
            id: "passwordTooltip",
            override: true,
          },
        ),
      );
    }

    if (newPasswordTooltip) {
      newPasswordTooltip.hide();
    } else {
      setNewPasswordTooltip(
        new Tooltip(
          document.getElementById("newPasswordTooltip"),
          document.getElementById("newPassword"),
          {
            placement: "right",
            triggerType: "none",
          },
          {
            id: "newPasswordTooltip",
            override: true,
          },
        ),
      );
    }

    if (confirmPasswordTooltip) {
      confirmPasswordTooltip.hide();
    } else {
      setConfirmPasswordTooltip(
        new Tooltip(
          document.getElementById("confirmPasswordTooltip"),
          document.getElementById("confirmPassword"),
          {
            placement: "right",
            triggerType: "none",
          },
          {
            id: "confirmPasswordTooltip",
            override: true,
          },
        ),
      );
    }

    if (oneTimePasswordTooltip) {
      oneTimePasswordTooltip.hide();
    } else {
      setOneTimePasswordTooltip(
        new Tooltip(
          document.getElementById("oneTimePasswordTooltip"),
          document.getElementById("oneTimePassword"),
          {
            placement: "right",
            triggerType: "none",
          },
          {
            id: "oneTimePasswordTooltip",
            override: true,
          },
        ),
      );
    }
  };
  const initForm = () => {
    const $password = document.getElementById("password") as HTMLInputElement;
    const $newPassword = document.getElementById(
      "newPassword",
    ) as HTMLInputElement;
    const $confirmPassword = document.getElementById(
      "confirmPassword",
    ) as HTMLInputElement;
    const $oneTimePassword = document.getElementById(
      "oneTimePassword",
    ) as HTMLInputElement;

    setError("");
    setLoading(false);
    setRandom(Math.floor(Math.random() * Math.random() * 101));
    $password.value = "";
    $newPassword.value = "";
    $confirmPassword.value = "";
    $oneTimePassword.value = "";
    $password.classList.remove("border-red-500");
    $newPassword.classList.remove("border-red-500");
    $confirmPassword.classList.remove("border-red-500");
    $oneTimePassword.classList.remove("border-red-500");
  };
  useEffect(() => {
    initTooltip();
    initForm();
  }, [isOpen]);
  const updatePassword = async (password: string, newPassword: string, oneTimePassword: string) => {
    try {
      // ログアウト時の監査ログを記録
      const response = await fetch(`/api/passwords/${id}`, {
        method: "PUT", // POSTリクエスト
        headers: {
          "Content-Type": "application/json", // JSON形式のヘッダー
        },
        body: JSON.stringify({
          password,
          newPassword,
          oneTimePassword,
        }),
      });

      // レスポンスのデータを処理
      const res = await response.json();

      if (response.status === 403) {
        const $password = document.getElementById(
          "password",
        ) as HTMLInputElement;
        const $oneTimePassword = document.getElementById(
          "oneTimePassword",
        ) as HTMLInputElement;

        setError(res.error);
        passwordTooltip?.show();
        $password.classList.add("border-red-500");
        $oneTimePassword.classList.add("border-red-500");
      } else if (response.status === 400) {
        const $newPassword = document.getElementById(
          "newPassword",
        ) as HTMLInputElement;
        setError(res.error);
        newPasswordTooltip?.show();
        $newPassword.classList.add("border-red-500");
      } else if (response.status === 401) {
        onClose && onClose();
        router.push("/login");
      } else if (response.status === 200) {
        onOK && onOK(res.message);
      } else {
        onError && onError(res.error);
      }
    } catch (error) {
      // エラーを捕捉・処理
      console.error("Error:", error);
      onError && onError(PORTAL_ERROR_MESSAGES.EMEC0007);
    } finally {
      setLoading(false);
    }
  };
  const submit = async (e: FormEvent<HTMLFormElement>) => {
    let isError;
    const passwordValue: string = e.currentTarget.password.value;
    const newPasswordValue: string = e.currentTarget.newPassword.value;
    const confirmPasswordValue: string = e.currentTarget.confirmPassword.value;
    const oneTimePasswordValue: string = e.currentTarget.oneTimePassword.value;

    e.preventDefault(); // フォームのデフォルトの送信アクションを無効化

    e.currentTarget.password.classList.toggle("border-red-500", !passwordValue);
    e.currentTarget.newPassword.classList.toggle(
      "border-red-500",
      !newPasswordValue,
    );
    e.currentTarget.confirmPassword.classList.toggle(
      "border-red-500",
      !confirmPasswordValue,
    );
    e.currentTarget.oneTimePassword.classList.toggle(
      "border-red-500",
      !oneTimePasswordValue,
    );

    if (!passwordValue || !newPasswordValue || !confirmPasswordValue || !oneTimePasswordValue) {
      return;
    }

    if (
      passwordValue.length < 8 ||
      passwordValue.length > 128 ||
      !LoginFormPasswordPattern.test(passwordValue)
    ) {
      setError(PORTAL_ERROR_MESSAGES.EMEC0003);
      passwordTooltip?.show();
      e.currentTarget.password.classList.add("border-red-500");
      isError = true;
    } else if (newPasswordValue.length < 8 || newPasswordValue.length > 128) {
      setError(PORTAL_ERROR_MESSAGES.EMEC0009);
      newPasswordTooltip?.show();
      e.currentTarget.newPassword.classList.add("border-red-500");
      isError = true;
    } else if (!atLeastTwoKindsRegex.test(newPasswordValue)) {
      setError(PORTAL_ERROR_MESSAGES.EMEC0010);
      newPasswordTooltip?.show();
      e.currentTarget.newPassword.classList.add("border-red-500");
      isError = true;
    } else if (userId === newPasswordValue) {
      setError(PORTAL_ERROR_MESSAGES.EMEC0011);
      newPasswordTooltip?.show();
      e.currentTarget.newPassword.classList.add("border-red-500");
      isError = true;
    } else if (passwordValue === newPasswordValue) {
      setError(PORTAL_ERROR_MESSAGES.EMEC0012);
      newPasswordTooltip?.show();
      e.currentTarget.newPassword.classList.add("border-red-500");
      isError = true;
    } else if (confirmPasswordValue !== newPasswordValue) {
      setError(PORTAL_ERROR_MESSAGES.EMEC0002);
      confirmPasswordTooltip?.show();
      e.currentTarget.confirmPassword.classList.add("border-red-500");
      isError = true;
    } else if (oneTimePasswordValue.length !== 6 ||
      !IsNumber.test(oneTimePasswordValue)) {
        setError(PORTAL_ERROR_MESSAGES.EMEC0014);
        oneTimePasswordTooltip?.show();
        e.currentTarget.oneTimePassword.classList.add("border-red-500");
        isError = true;
    }

    if (isError) {
      return;
    }

    setLoading(true); // ローディング状態をtrueに設定
    await updatePassword(passwordValue, newPasswordValue, oneTimePasswordValue);
  };

  return (
    <div
      id="password-modal"
      tabIndex={-1}
      className="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full"
    >
      <div
        id="passwordTooltip"
        role="tooltip"
        className="w-48 md:w-48 xl:w-60 tooltip invisible absolute z-10 inline-block rounded-lg bg-red-700 px-3 py-2 text-sm font-medium text-white opacity-0 shadow-sm transition-opacity duration-300"
      >
        {error}
        <div className="tooltip-arrow" data-popper-arrow></div>
      </div>
      <div
        id="newPasswordTooltip"
        role="tooltip"
        className="w-48 md:w-48 xl:w-60 tooltip invisible absolute z-10 inline-block rounded-lg bg-red-700 px-3 py-2 text-sm font-medium text-white opacity-0 shadow-sm transition-opacity duration-300"
      >
        {error}
        <div className="tooltip-arrow" data-popper-arrow></div>
      </div>
      <div
        id="confirmPasswordTooltip"
        role="tooltip"
        className="w-48 md:w-48 xl:w-60 tooltip invisible absolute z-10 inline-block rounded-lg bg-red-700 px-3 py-2 text-sm font-medium text-white opacity-0 shadow-sm transition-opacity duration-300"
      >
        {error}
        <div className="tooltip-arrow" data-popper-arrow></div>
      </div>
      <div
        id="oneTimePasswordTooltip"
        role="tooltip"
        className="w-48 md:w-48 xl:w-60 tooltip invisible absolute z-10 inline-block rounded-lg bg-red-700 px-3 py-2 text-sm font-medium text-white opacity-0 shadow-sm transition-opacity duration-300"
      >
        {error}
        <div className="tooltip-arrow" data-popper-arrow></div>
      </div>
      <div className="relative w-full max-w-lg max-h-full">
        <form className="relative rounded shadow bg-gray-600" onSubmit={submit}>
          <div className="flex items-center justify-between p-4 border-b rounded-t bg-gradient-header">
            <h3 className="text-lg font-semibold text-white">パスワード変更</h3>
            <button
              type="button"
              className="text-gray-400 bg-transparent rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center hover:bg-gray-600 hover:text-white"
              onClick={closeModal}
            >
              <svg
                className="w-3 h-3"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 14 14"
              >
                <path
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                />
              </svg>
              <span className="sr-only">Close modal</span>
            </button>
          </div>
          <div className="px-8 py-4 bg-gray-100 text-base font-medium">
            <div className="text-sm font-normal">
              ログインユーザーのパスワードを変更できます。
            </div>
          </div>
          <div className="px-8 py-4 space-y-4 bg-white text-base font-medium">
            <div className="flex flex-col gap-4">
              <div className="flex items-center justify-between">
                <div className="w-60 block">
                  <label
                    htmlFor="userId"
                    className="block text-sm font-medium text-gray-900"
                  >
                    ユーザーID:
                  </label>
                </div>
                <label className="flex-1 text-sm font-medium text-gray-900">
                  {userId}
                </label>
              </div>
              <div className="flex items-center justify-between">
                <div className="w-60 flex items-center justify-between">
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium text-gray-900"
                  >
                    現在のパスワード:
                  </label>
                  <label className="text-red-500 me-2">*</label>
                </div>
                <input
                  type="password"
                  id="password"
                  // minLength={8}
                  maxLength={128}
                  className="flex-1 rounded border border-gray-300 bg-gray-50 p-2 text-sm text-gray-900 focus:border-gray-300 focus:ring-gray-300"
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="w-60 flex items-center justify-between">
                  <label
                    htmlFor="newPassword"
                    className="block text-sm font-medium text-gray-900"
                  >
                    新しいパスワード:
                  </label>
                  <label className="text-red-500 me-2">*</label>
                </div>
                <input
                  type="password"
                  id="newPassword"
                  // minLength={8}
                  maxLength={128}
                  className="flex-1 rounded border border-gray-300 bg-gray-50 p-2 text-sm text-gray-900 focus:border-gray-300 focus:ring-gray-300"
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="w-60 flex items-center justify-between">
                  <label
                    htmlFor="confirmPassword"
                    className="block text-sm font-medium text-gray-900"
                  >
                    新しいパスワード（確認）:
                  </label>
                  <label className="text-red-500 me-2">*</label>
                </div>
                <input
                  type="password"
                  id="confirmPassword"
                  // minLength={8}
                  maxLength={128}
                  className="flex-1 rounded border border-gray-300 bg-gray-50 p-2 text-sm text-gray-900 focus:border-gray-300 focus:ring-gray-300"
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="w-60 flex items-center justify-between">
                  <label
                    htmlFor="oneTimePassword"
                    className="block text-sm font-medium text-gray-900"
                  >
                    ワンタイムコード:
                  </label>
                  <label className="text-red-500 me-2">*</label>
                </div>
                <input
                  type="input"
                  id="oneTimePassword"
                  maxLength={6}
                  className="flex-1 rounded border border-gray-300 bg-gray-50 p-2 text-sm text-gray-900 focus:border-gray-300 focus:ring-gray-300"
                />
              </div>
            </div>
          </div>
          <div className="flex items-center justify-between p-4 border-t rounded-b bg-gradient-header">
            <div className="text-sm font-normal text-white">* 必須入力</div>
            <div className="flex flex-row-reverse items-center">
              <button
                onClick={closeModal}
                type="button"
                className="w-28 ms-3 rounded bg-gradient-blue px-3 py-2 text-center text-xs font-medium text-white 
              drop-shadow-blue shadow-inner hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
              >
                キャンセル
              </button>
              <button
                type="submit"
                className="w-28 rounded bg-gradient-dark px-3 py-2 text-center text-xs font-medium text-white 
              drop-shadow-dark shadow-dark hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
              >
                <Spinner
                  className={clsx("inline-block mr-2", { hidden: !loading })}
                />
                OK
              </button>
            </div>
          </div>
        </form>
      </div>
      <RefreshToken key={random} />
    </div>
  );
}
