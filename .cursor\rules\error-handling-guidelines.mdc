---
description: 
globs: 
alwaysApply: true
---
# Error Handling Guidelines (for apps/jcs-endpoint-nextjs)

This document outlines standard procedures for handling and reporting errors within the `apps/jcs-endpoint-nextjs` application. Consistent error handling improves user experience, aids debugging, and maintains application stability.

**Primary References & Tools:**
*   `app/lib/portal-error.ts`: Contains `PortalError` class, `isPrismaError` utility, and centralized handlers `handleApiError` and `handleServerError`.
*   `app/lib/logger.ts`: Used for logging all significant errors (via `Logger.error()`).
*   `app/global-error.tsx`: The root error boundary for the entire application.
*   `app/dashboard/error.tsx`: A specific error boundary for routes under `/dashboard`.
*   Next.js App Router documentation on Error Handling (Error Boundaries, `error.tsx`, `global-error.tsx`).
*   Predefined user-facing error messages in `app/lib/definitions.ts` (as `PORTAL_ERROR_MESSAGES`).
*   Detailed design documents in the Monorepo's `docs/components/` which may specify error states and messages for particular features.

## Core Principles

1.  **Catch and Handle Specific Errors:** Whenever practical, catch specific error types (e.g., `PrismaClientKnownRequestError` from Prisma, custom `PortalError`) rather than generic `Error` to allow for more targeted responses or recovery logic.
2.  **User-Friendly Messages (SSoT: `definitions.ts`):**
    *   For errors displayed to the user (either via API responses or UI components), **MUST** use the predefined error messages from `PORTAL_ERROR_MESSAGES` in `app/lib/definitions.ts` by their keys (e.g., `PORTAL_ERROR_MESSAGES.EMEC0007`). This ensures consistency and ease of localization/management.
    *   Avoid exposing raw technical error details (like stack traces or sensitive system information) directly in user-facing UI or API responses intended for end-users.
3.  **Comprehensive Server-Side Logging:** All caught errors on the server-side (API Routes, Server Actions, `lib` functions) **MUST** be logged with sufficient context (original error message, stack trace, relevant identifiers like `userId`, operation name) using `Logger.error()` from `app/lib/logger.ts`. The utilities in `app/lib/portal-error.ts` already incorporate this.
4.  **Graceful Degradation & Error Boundaries:**
    *   Design features so that if a non-critical part fails (e.g., loading an optional widget), the main functionality of the page remains accessible if possible.
    *   Next.js App Router uses `error.tsx` files as error boundaries. `app/dashboard/error.tsx` handles UI errors within the dashboard layout, and `app/global-error.tsx` is the last resort for the entire application. These components should render a user-friendly error message (using `PORTAL_ERROR_MESSAGES`) and provide a `reset` function to attempt recovery.
5.  **Server-Side Centralized Error Handling (`app/lib/portal-error.ts`):**
    *   **`handleApiError(error: any): NextResponse`**: **MUST** be used as the primary catch-all error handler in API Routes (`app/api/.../route.ts`). It logs the error via `Logger.error()` and returns a standardized JSON error response (typically HTTP 500 with a generic message, or a specific message for Prisma errors, using `PORTAL_ERROR_MESSAGES`).
    *   **`handleServerError(error: any): never`**: **MUST** be used in server-side data fetching functions (e.g., within `app/lib/data.ts`) or Server Actions where the intent is to **throw an error** that will be caught by the nearest Next.js error boundary (`error.tsx` or `global-error.tsx`). It logs the error and re-throws a new `Error` with a user-friendly message from `PORTAL_ERROR_MESSAGES`.
6.  **Client-Side Error Handling:**
    *   **SWR Hooks:** Client components using SWR hooks (from `app/hooks/`) **MUST** check the `error` object returned by the hook for fetch/network errors. They also need to inspect the `data` payload for application-specific errors if the API returns them within a successful HTTP response (e.g., `data.error`). Display appropriate UI feedback.
    *   **Server Action Return Values:** Client components invoking Server Actions (especially those using `useFormState`) **MUST** handle the `success: false` state and any `message` or `errors` fields in the object returned by the Server Action to display feedback to the user.
    *   **General Try-Catch:** For other client-side operations that might fail (e.g., complex UI logic, browser API interactions), use `try-catch` blocks and provide user feedback. Avoid letting unhandled errors break the UI.

## Specific Error Handling Scenarios

1.  **Prisma Errors (Server-Side):**
    *   Identified using `isPrismaError(error)` from `app/lib/portal-error.ts`.
    *   `handleApiError` and `handleServerError` provide specific user-facing messages (e.g., `PORTAL_ERROR_MESSAGES.EMEC0006` for database connectivity issues).
2.  **Input Validation Errors:**
    *   **API Routes & Server Actions:** If input validation fails, **MUST** return an HTTP `400 Bad Request` status. The response body should contain a clear error message (from `PORTAL_ERROR_MESSAGES`) or a structured error object detailing field-specific issues.
        ```typescript
        // Example in an API Route or Server Action
        // if (!isValidInput(input)) {
        //   return NextResponse.json({ error: PORTAL_ERROR_MESSAGES.EMEC00XX_YOUR_VALIDATION_ERROR }, { status: 400 });
        // }
        ```
    *   **Client-Side Forms:** Display validation errors inline, next to the respective form fields, guiding the user to correct them.
3.  **Authentication/Authorization Errors (API Routes/Server Actions):**
    *   If authentication fails (e.g., invalid session), **MUST** return HTTP `401 Unauthorized`.
    *   If authorization fails (e.g., user authenticated but lacks permission), **MUST** return HTTP `403 Forbidden`.
    *   Use appropriate messages from `PORTAL_ERROR_MESSAGES`.

---

**NOTE TO CURSOR:**
1.  When implementing new server-side logic (API Routes, Server Actions, `lib` functions), always wrap operations prone to failure in `try-catch` blocks.
2.  For API Routes, use `handleApiError` as the final catch block to ensure consistent JSON error responses and logging.
3.  For Server Actions or `lib` functions intended to propagate errors to Next.js error boundaries, use `handleServerError` in catch blocks.
4.  All user-facing error messages **MUST** be sourced from `PORTAL_ERROR_MESSAGES` in `app/lib/definitions.ts`.
5.  In client components, diligently check for and handle error states from SWR hooks and Server Action responses, providing clear UI feedback.
6.  Consult `docs/components/` for feature-specific error handling requirements and user messages defined in the detailed design.


---