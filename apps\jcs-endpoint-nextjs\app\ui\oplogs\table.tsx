/**
 * @file table.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

import { ServerDataOplogs } from "@/app/lib/data/oplogs";
import Thead from "../thead";

// 操作ログテーブルコンポーネント
export default async function OplogsTable({
  filter,
  page,
  size,
  sort,
  order,
}: {
  filter: string;
  page: number;
  size: number;
  sort: "name" | "createdAt" | "retentionAt" | "size";
  order: "asc" | "desc";
}) {
  const oplogs = await ServerDataOplogs.fetchFilteredOplogs(
    filter,
    size,
    page,
    sort,
    order,
  );

  return (
    <table className="w-full text-left text-sm text-gray-500">
      <Thead
        headers={[
          { key: "name", label: "ログ名" },
          { key: "createdAt", label: "登録日時" },
          { key: "retentionAt", label: "保管期限" },
          { key: "size", label: "サイズ" },
        ]}
        defaultOrder="createdAt"
        defaultSort="desc"
      />
      <tbody>
        {oplogs?.length !== 0 ? (
          oplogs!.map((oplog) => (
            <tr
              key={oplog.id}
              className="border-b odd:bg-white even:bg-gray-50"
            >
              <th
                scope="row"
                className="border-r whitespace-nowrap px-6 py-4 font-medium"
              >
                <a
                  target="_blank"
                  href={`oplogs/${oplog.licenseId}/${oplog.fileName}`}
                  className="font-medium text-blue-600 hover:underline"
                >
                  {oplog.name}
                </a>
              </th>
              <td className="border-r px-6 py-4">{oplog.createdAt}</td>
              <td className="border-r px-6 py-4">
                {oplog.retentionAt && oplog.retentionAt}
              </td>
              <td className="px-6 py-4 text-right">{oplog.formattedSize}</td>
            </tr>
          ))
        ) : (
          <tr>
            <td>
              <div className="p-4"></div>
            </td>
          </tr>
        )}
      </tbody>
    </table>
  );
}
