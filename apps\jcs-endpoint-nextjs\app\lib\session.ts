/**
 * @fileoverview Iron Sessionを使用したセッション管理の設定と型定義
 * @description ユーザー認証状態の管理とセッションデータの型安全性を提供する
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { SessionOptions } from "iron-session";
import { ENV } from "./definitions";

export interface SessionData {
    user: {
        id: string,
        userId: string,
        licenseId: string,
        tz: string,
        refreshToken: string,
    },
}

export const defaultSession: SessionData = {
    user: {
        id: "",
        userId: "",
        licenseId: "",
        tz: "",
        refreshToken: "",
    },
};

/**
 * Iron Sessionの設定オプション
 * セッション暗号化とCookieのセキュリティ設定を定義する
 */
export const sessionOptions: SessionOptions = {
    // 環境変数からセッション暗号化キーを取得（セキュリティ強化）
    password: ENV.SESSION_SECRET,
    cookieName: "portal_keycloak_callback_session",
    cookieOptions: {
        // 本番環境ではHTTPS必須、開発環境では柔軟に対応
        secure: process.env.NODE_ENV === "production",
        httpOnly: true,  // クライアント・スクリプトによるクッキーへのアクセスの防止
        sameSite: "strict", // CSRF攻撃の防止（Keycloak認証との互換性確認済み）
        maxAge: ENV.JWT_MAX_AGE_SECONDS,
    },
};
