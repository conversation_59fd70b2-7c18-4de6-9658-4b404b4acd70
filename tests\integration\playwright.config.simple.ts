import { defineConfig, devices } from '@playwright/test';
import * as path from 'path';

/**
 * @fileoverview 简化的 Playwright 配置文件（用于验证）
 * 
 * 本配置文件用于验证测试环境，不使用 webServer 自动启动服务。
 * 适用于手动启动服务后进行测试验证的场景。
 * 
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */
export default defineConfig({
  testDir: './specs',
  
  // 🚀 基础配置
  fullyParallel: false, // 数据库操作测试需要串行执行
  forbidOnly: !!process.env.CI,
  
  // 🔄 重试策略
  retries: process.env.CI ? 2 : 1,
  workers: process.env.CI ? 1 : 2, // 减少并发数以避免冲突
  
  // ⏱️ 超时配置
  timeout: 60 * 1000, // 60秒测试超时
  expect: {
    timeout: 10 * 1000, // 10秒断言超时
  },
  
  // 📊 简化的报告配置
  reporter: [
    ['html', { 
      outputFolder: '../test-results/html-report',
      open: 'never',
    }],
    ['line'], // 控制台实时输出
  ],
  
  // 📁 输出目录
  outputDir: '../test-results/test-output',
  
  // 🔧 全局测试配置
  use: {
    baseURL: 'http://localhost:3000',
    
    // 🎬 追踪和调试
    trace: 'retain-on-failure',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    
    // 🌐 浏览器配置
    headless: !!process.env.CI,
    viewport: { width: 1280, height: 720 },
    
    // 🚀 性能优化
    actionTimeout: 15 * 1000, // 15秒操作超时
    navigationTimeout: 30 * 1000, // 30秒导航超时
    
    // 🔐 安全和网络配置
    ignoreHTTPSErrors: true,
    bypassCSP: true,
  },

  // 🖥️ 浏览器配置
  projects: [
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        launchOptions: {
          args: [
            '--disable-web-security',
            '--no-sandbox',
          ],
        },
      },
    },
  ],

  // 🧹 简化的全局钩子（不进行复杂的环境验证）
  globalSetup: require.resolve('./support/simple-global-setup.ts'),
  globalTeardown: require.resolve('./support/global-teardown.ts'),
});
