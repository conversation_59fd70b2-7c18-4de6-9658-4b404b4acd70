---
description: 
globs: 
alwaysApply: true
---
# Data Fetching & Caching Guidelines (for apps/jcs-endpoint-nextjs)

This document outlines best practices for fetching, caching, and revalidating data within the `apps/jcs-endpoint-nextjs` application. Proper data handling is essential for performance, consistency, and user experience.

**Primary References:**
*   `app/lib/data.ts` (especially the `ServerData` class and usage of `unstable_cache`, `revalidateTag`).
*   `app/hooks/` directory for client-side data fetching with SWR (e.g., `use-license.ts`).
*   Next.js App Router documentation on Data Fetching and Caching.
*   Relevant component design documents in `docs/components/` describing data requirements.

## Core Principles

1.  **Server-Centric Data Fetching for Initial Load:** Whenever possible, fetch data on the server (using Server Components, Route Handlers, or Server Actions) for the initial page load. This improves performance and SEO.
2.  **Client-Side Fetching for Dynamic Updates:** Use SWR (via custom hooks in `app/hooks/`) for client-side data fetching that needs to be dynamic, respond to user interactions, or revalidate in the background.
3.  **Caching on the Server:**
    *   The `ServerData` class in `app/lib/data.ts` utilizes Next.js's `unstable_cache` API for caching the results of data fetching functions (e.g., Prisma queries).
    *   Cache keys are defined (e.g., `PORTAL_CACHE_KEY_SERVERS`) and often include dynamic parts like `licenseId` or `tz` to ensure cache granularity.
    *   Cache revalidation is managed by `ENV.APP_CACHE_TTL_SECONDS` and explicit `revalidateTag` calls.
4.  **Cache Invalidation/Revalidation:**
    *   When data is mutated (created, updated, deleted) via Server Actions or API Routes, relevant server-side caches **MUST** be revalidated using `revalidateTag()`.
    *   For client-side data fetched via SWR, use `mutate()` function provided by SWR hooks to revalidate or optimistically update data.
5.  **Data Transformation and Formatting:**
    *   Data transformations (e.g., formatting dates with `formatDate`, sizes with `formatBytes`, casting LOV codes to labels with `castValueToLabel` from `app/lib/utils.ts`) should ideally happen as close to the source as possible within the `ServerData` methods or directly in components if specific to view logic.

## Server-Side Data Fetching (`app/lib/data.ts`)

*   **`ServerData` Class:** This class encapsulates most server-side data fetching logic that interacts with Prisma.
*   **`unstable_cache`:** Methods like `fetchCachedLovList`, `fetchCachedServers`, etc., wrap Prisma queries with `unstable_cache` to memoize results.
    *   **Cache Keys and Tags:** Pay close attention to the cache keys and tags (e.g., `[PORTAL_CACHE_KEY_SERVERS]`, `tags: [\`${PORTAL_CACHE_KEY_SERVERS}-\${licenseId}\`]` ). Tags allow for granular revalidation.
*   **Pagination Logic:** Methods like `fetchServersPages` and `fetchFilteredServers` handle pagination logic on top of cached/filtered data.
*   **Session Data for Filtering:** Many `ServerData` methods use `getIronSession` to retrieve `licenseId` or `tz` from the session to fetch user-specific data.
*   **Error Handling:** Server-side data fetching errors are handled by `handleServerError` from `app/lib/portal-error.ts`.

**Example: Fetching cached data and revalidating**
```typescript
// In app/lib/data.ts
// private static fetchCachedServers(licenseId: string) {
//   return unstable_cache(
//     async (licenseId: string) => { /* ... fetch from prisma ... */ },
//     [PORTAL_CACHE_KEY_SERVERS], // Cache key array
//     {
//       tags: [`${PORTAL_CACHE_KEY_SERVERS}-${licenseId}`], // Tags for revalidation
//       revalidate: ENV.APP_CACHE_TTL_SECONDS, // Time-based revalidation
//     },
//   )(licenseId);
// }

// When some action modifies server data, in a Server Action or API route:
// import { revalidateTag } from "next/cache";
// import { PORTAL_CACHE_KEY_SERVERS } from "./definitions"; // Adjust path as necessary
// import { SessionData, sessionOptions } from "./session";   // Adjust path as necessary
// import { cookies } from "next/headers";
// import prisma from "./prisma"; // Adjust path as necessary
//
// async function exampleUpdateServerAction() {
//   // ... (logic to update or create a server using prisma) ...
//   // For example: await prisma.server.update(...);
//
//   // const session = await getIronSession<SessionData>(cookies(), sessionOptions);
//   // if (session.user && session.user.licenseId) {
//   //   revalidateTag(`${PORTAL_CACHE_KEY_SERVERS}-${session.user.licenseId}`);
//   // }
// }
```

## Client-Side Data Fetching (SWR in `app/hooks/`)

*   Custom hooks like `useLicense` (`app/hooks/use-license.ts`) encapsulate SWR logic.
*   A generic `fetcher` function (usually defined in `app/lib/utils.ts` or within the hook itself) is used to call Next.js API Routes.
*   **Usage Example:**
```tsx
// In a client component
// "use client"; // Ensure this directive for client components
// import useLicense from "@/app/hooks/use-license";
// import Spinner from "@/app/ui/spinner"; // Assuming a spinner component exists
//
// interface MyLicenseComponentProps {
//   onError?: (message: string) => void; // Callback for handling errors
// }
//
// function MyLicenseComponent({ onError }: MyLicenseComponentProps) {
//   const { data, error: fetchError, isLoading, mutate } = useLicense();
//
//   if (isLoading) {
//     return <Spinner />;
//   }
//   if (fetchError) {
//      onError?.("Network error encountered while loading license.");
//      return <p>Error loading license information. Please try again.</p>;
//   }
//   // Check for application-specific errors potentially returned in the 'data' payload
//   if (data && data.error) { // Assuming 'data' object might contain an 'error' field from the API
//      onError?.(data.error); // Pass the specific error message
//      return <p>{data.error}</p>;
//   }
//
//   const licenseDetails = data; // If no errors, 'data' is the license information
//
//   return (
//     <div>
//       {licenseDetails ? (
//         <p>License Type: {licenseDetails.type}</p>
//       ) : (
//         <p>No license data available.</p>
//       )}
//       {/* <button onClick={() => mutate()}>Refresh License</button> */}
//     </div>
//   );
// }
```
*   **Error Handling:** SWR hooks return an `error` object for network or fetch-related errors. Application-specific errors (e.g., validation errors from the API) might be part of the `data` payload and **MUST** be explicitly checked and handled by the consuming component.

## Data Revalidation Strategies

*   **On-Demand Revalidation (Server):** Use `revalidateTag()` from `next/cache` immediately after successful data mutations in Server Actions or API Routes. The tag used **MUST** match the tag defined in the corresponding `unstable_cache` call.
*   **Time-Based Revalidation (Server):** `unstable_cache` uses the `revalidate` option (e.g., `ENV.APP_CACHE_TTL_SECONDS`) for automatic time-based cache invalidation.
*   **SWR Revalidation (Client):** SWR provides automatic revalidation on events like window focus, network reconnection, and on an interval. Use the `mutate()` function returned by SWR hooks for manual revalidation or optimistic updates from client-side interactions.

---

**NOTE TO CURSOR:** When implementing features requiring data:
1.  Prioritize fetching initial data for page loads via Server Components, leveraging methods from the `ServerData` class in `app/lib/data.ts` which incorporate `unstable_cache`.
2.  For dynamic client-side updates, polling, or data that changes frequently post-initial load, create or use existing SWR-based custom hooks in `app/hooks/`.
3.  When adding data mutation logic (in Server Actions or API Routes), it is **mandatory** to ensure that all relevant server-side caches (identified by tags) are revalidated using `revalidateTag()`.
4.  When creating new cached data functions in `ServerData`, clearly define appropriate cache keys (from `app/lib/definitions.ts`) and tags.

---
