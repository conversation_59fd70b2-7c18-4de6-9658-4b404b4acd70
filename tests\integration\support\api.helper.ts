import { APIRequestContext, expect } from '@playwright/test';

/**
 * 🌐 API 测试辅助器 - 业界最佳实践
 * 
 * 功能：
 * - 统一的 API 请求管理
 * - 自动化认证处理
 * - 响应验证和断言
 * - 错误处理和重试
 * - 性能监控
 */
export class ApiHelper {
  private request: APIRequestContext;
  private baseURL: string;
  private authToken?: string;

  constructor(request: APIRequestContext) {
    this.request = request;
    this.baseURL = process.env.API_BASE_URL || 'http://localhost:3000';
  }

  /**
   * 🔐 设置认证令牌
   */
  setAuthToken(token: string): void {
    this.authToken = token;
  }

  /**
   * 🔐 获取认证头
   */
  private getAuthHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }

    return headers;
  }

  /**
   * 🚀 GET 请求
   */
  async get(endpoint: string, options: ApiRequestOptions = {}): Promise<ApiResponse> {
    const startTime = Date.now();
    
    try {
      const response = await this.request.get(`${this.baseURL}${endpoint}`, {
        headers: { ...this.getAuthHeaders(), ...options.headers },
        params: options.params,
      });

      const responseTime = Date.now() - startTime;
      const data = await this.parseResponse(response);

      return {
        status: response.status(),
        data,
        headers: response.headers(),
        responseTime,
      };
    } catch (error) {
      console.error(`❌ GET ${endpoint} 失败:`, error);
      throw error;
    }
  }

  /**
   * 📤 POST 请求
   */
  async post(endpoint: string, data?: any, options: ApiRequestOptions = {}): Promise<ApiResponse> {
    const startTime = Date.now();
    
    try {
      const response = await this.request.post(`${this.baseURL}${endpoint}`, {
        headers: { ...this.getAuthHeaders(), ...options.headers },
        data: data ? JSON.stringify(data) : undefined,
      });

      const responseTime = Date.now() - startTime;
      const responseData = await this.parseResponse(response);

      return {
        status: response.status(),
        data: responseData,
        headers: response.headers(),
        responseTime,
      };
    } catch (error) {
      console.error(`❌ POST ${endpoint} 失败:`, error);
      throw error;
    }
  }

  /**
   * 🔄 PUT 请求
   */
  async put(endpoint: string, data?: any, options: ApiRequestOptions = {}): Promise<ApiResponse> {
    const startTime = Date.now();
    
    try {
      const response = await this.request.put(`${this.baseURL}${endpoint}`, {
        headers: { ...this.getAuthHeaders(), ...options.headers },
        data: data ? JSON.stringify(data) : undefined,
      });

      const responseTime = Date.now() - startTime;
      const responseData = await this.parseResponse(response);

      return {
        status: response.status(),
        data: responseData,
        headers: response.headers(),
        responseTime,
      };
    } catch (error) {
      console.error(`❌ PUT ${endpoint} 失败:`, error);
      throw error;
    }
  }

  /**
   * 🗑️ DELETE 请求
   */
  async delete(endpoint: string, options: ApiRequestOptions = {}): Promise<ApiResponse> {
    const startTime = Date.now();
    
    try {
      const response = await this.request.delete(`${this.baseURL}${endpoint}`, {
        headers: { ...this.getAuthHeaders(), ...options.headers },
      });

      const responseTime = Date.now() - startTime;
      const data = await this.parseResponse(response);

      return {
        status: response.status(),
        data,
        headers: response.headers(),
        responseTime,
      };
    } catch (error) {
      console.error(`❌ DELETE ${endpoint} 失败:`, error);
      throw error;
    }
  }

  /**
   * 📊 解析响应
   */
  private async parseResponse(response: any): Promise<any> {
    const contentType = response.headers()['content-type'] || '';
    
    if (contentType.includes('application/json')) {
      try {
        return await response.json();
      } catch {
        return null;
      }
    } else if (contentType.includes('text/')) {
      return await response.text();
    } else {
      return await response.body();
    }
  }

  /**
   * ✅ 验证响应状态
   */
  expectStatus(response: ApiResponse, expectedStatus: number): void {
    expect(response.status).toBe(expectedStatus);
  }

  /**
   * ✅ 验证响应数据结构
   */
  expectResponseSchema(response: ApiResponse, schema: any): void {
    expect(response.data).toMatchObject(schema);
  }

  /**
   * ⚡ 验证响应时间
   */
  expectResponseTime(response: ApiResponse, maxTime: number): void {
    expect(response.responseTime).toBeLessThan(maxTime);
  }

  /**
   * 🔄 重试请求
   */
  async retryRequest(
    requestFn: () => Promise<ApiResponse>,
    maxRetries = 3,
    delay = 1000
  ): Promise<ApiResponse> {
    let lastError: Error;
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await requestFn();
      } catch (error) {
        lastError = error as Error;
        
        if (i < maxRetries) {
          console.log(`🔄 请求失败，${delay}ms 后重试 (${i + 1}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, delay));
          delay *= 2; // 指数退避
        }
      }
    }
    
    throw lastError!;
  }

  /**
   * 🎯 等待 API 可用
   */
  async waitForApiAvailable(endpoint = '/api/health', timeout = 30000): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const response = await this.get(endpoint);
        if (response.status === 200) {
          console.log('✅ API 已可用');
          return;
        }
      } catch {
        // API 还未可用，继续等待
      }
      
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    throw new Error(`API 等待超时 (${timeout}ms)`);
  }

  /**
   * 📊 批量请求
   */
  async batchRequests(requests: (() => Promise<ApiResponse>)[]): Promise<ApiResponse[]> {
    const startTime = Date.now();
    
    try {
      const responses = await Promise.all(requests.map(req => req()));
      const totalTime = Date.now() - startTime;
      
      console.log(`📊 批量请求完成: ${requests.length} 个请求，总耗时 ${totalTime}ms`);
      
      return responses;
    } catch (error) {
      console.error('❌ 批量请求失败:', error);
      throw error;
    }
  }
}

// 类型定义
export interface ApiRequestOptions {
  headers?: Record<string, string>;
  params?: Record<string, string>;
}

export interface ApiResponse {
  status: number;
  data: any;
  headers: Record<string, string>;
  responseTime: number;
}

// 常用的 API 端点
export const ApiEndpoints = {
  // 认证相关
  AUTH: {
    LOGIN: '/api/auth/login',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
  },
  
  // 管理项目相关
  MANAGEMENT_ITEMS: {
    LIST: '/api/management-items',
    CREATE: '/api/management-items',
    GET: (id: string) => `/api/management-items/${id}`,
    UPDATE: (id: string) => `/api/management-items/${id}`,
    DELETE: (id: string) => `/api/management-items/${id}`,
    EXPORT: '/api/management-items/export',
  },
  
  // 任务相关
  TASKS: {
    LIST: '/api/tasks',
    CREATE: '/api/tasks',
    GET: (id: string) => `/api/tasks/${id}`,
    EXECUTE: (id: string) => `/api/tasks/${id}/execute`,
    STATUS: (id: string) => `/api/tasks/${id}/status`,
  },
  
  // 健康检查
  HEALTH: '/api/health',
} as const;
