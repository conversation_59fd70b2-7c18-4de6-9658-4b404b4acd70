#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function showUsageExamples() {
    console.log('📋 Excel测试报告生成工具使用指南\n');
    
    console.log('🚀 可用命令:');
    console.log('='.repeat(60));
    console.log('npm run generate-test-excel      # 从测试代码自动生成详细报告');
    console.log('npm run create-test-template     # 创建手动填写用的模板');
    console.log('npm run test-excel-help          # 显示此帮助信息');
    console.log('');
    console.log('🆕 客户格式命令:');
    console.log('npm run analyze-customer-template # 分析客户提供的Excel模板');
    console.log('npm run create-customer-template  # 创建客户格式的模板');
    console.log('npm run generate-customer-report  # 生成客户格式的测试报告');
    
    console.log('\n📊 生成的Excel文件特点:');
    console.log('='.repeat(60));
    console.log('✅ 标准格式报告 (generate-test-excel):');
    console.log('  - 概要工作表: 显示各项目统计信息');
    console.log('  - 项目工作表: 按项目分类的详细测试用例');
    console.log('  - 自动提取: 从JSDoc注释提取结构化信息');
    console.log('  - 文件分组: 按测试文件分组显示');
    
    console.log('\n✅ 客户格式报告 (generate-customer-report):');
    console.log('  - 表紙: 项目封面页');
    console.log('  - 目次: 自动生成的目录');
    console.log('  - 试验观点・测试结果集计: 统计汇总表');
    console.log('  - API对象一览: API测试对象列表');
    console.log('  - 各模块测试表: 按UT動作確認チェックリスト格式');
    
    console.log('\n✅ 手动模板 (create-test-template):');
    console.log('  - 标准格式: 统一的测试用例记录格式');
    console.log('  - 示例数据: 包含填写示例');
    console.log('  - 统计功能: 自动计算测试进度');
    console.log('  - 日语优化: 使用メイリオ字体，支持日语显示');
    
    console.log('\n💡 推荐的测试注释格式:');
    console.log('='.repeat(60));
    console.log(`/**
 * 試験観点: 正常なデータ処理の確認
 * 試験対象: ユーザー登録機能
 * 試験手順: 
 *   1. 有効なユーザーデータを準備する
 *   2. 登録APIを呼び出す
 *   3. レスポンスを確認する
 * 確認項目: 
 *   - ステータスコード200が返されること
 *   - ユーザーIDが正しく生成されること
 */
it('正常系：有効なデータでユーザー登録が成功する', async () => {
  // 测试代码
});`);
    
    console.log('\n📁 项目状态检查:');
    console.log('='.repeat(60));
    
    // 检查测试文件
    const testDirs = ['tests', 'test', '__tests__', 'src', 'apps'];
    let totalTestFiles = 0;
    
    testDirs.forEach(dir => {
        if (fs.existsSync(dir)) {
            const files = scanForTestFiles(dir);
            if (files.length > 0) {
                console.log(`📂 ${dir}: 找到 ${files.length} 个测试文件`);
                totalTestFiles += files.length;
            }
        }
    });
    
    if (totalTestFiles === 0) {
        console.log('⚠️  未找到测试文件，请确保测试文件以 .test.ts, .test.tsx, .test.js, .test.jsx 结尾');
    } else {
        console.log(`✅ 总计找到 ${totalTestFiles} 个测试文件`);
    }
    
    // 检查客户模板文件
    const customerTemplate = path.join(__dirname, '..', 'docs-delivery', 'unit-test-report', 'UT動作確認チェックリスト.xlsx');
    if (fs.existsSync(customerTemplate)) {
        console.log('✅ 找到客户模板文件: UT動作確認チェックリスト.xlsx');
    } else {
        console.log('⚠️  未找到客户模板文件，将使用默认格式');
    }
    
    // 检查输出目录
    const outputDir = path.join(__dirname, '..', 'output');
    if (!fs.existsSync(outputDir)) {
        console.log('📁 将创建输出目录: output/');
    } else {
        console.log('📁 输出目录已存在: output/');
    }
    
    // 检查模板目录
    const templatesDir = path.join(__dirname, '..', 'templates');
    if (!fs.existsSync(templatesDir)) {
        console.log('📁 将创建模板目录: templates/');
    } else {
        console.log('📁 模板目录已存在: templates/');
    }
    
    console.log('\n🎯 快速开始:');
    console.log('='.repeat(60));
    console.log('1. 确保已安装依赖: npm install');
    console.log('2. 分析客户模板: npm run analyze-customer-template');
    console.log('3. 生成客户格式报告: npm run generate-customer-report');
    console.log('4. 或生成标准报告: npm run generate-test-excel');
    console.log('5. 或创建手动模板: npm run create-test-template');
    console.log('6. 查看生成的Excel文件在 output/ 或 templates/ 目录中');
    
    console.log('\n📝 注意事项:');
    console.log('='.repeat(60));
    console.log('- 客户格式报告完全基于UT動作確認チェックリスト.xlsx的格式');
    console.log('- 确保测试文件包含适当的JSDoc注释以获得最佳结果');
    console.log('- 生成的Excel文件使用日语标题和格式');
    console.log('- 支持TypeScript和JavaScript测试文件');
    console.log('- 自动检测Jest、Mocha等测试框架的语法');
    console.log('- 客户格式包含表紙、目次、集计表等完整结构');
    
    console.log('\n🔄 工作流程建议:');
    console.log('='.repeat(60));
    console.log('1. 首先运行 analyze-customer-template 了解客户格式');
    console.log('2. 在测试代码中添加适当的JSDoc注释');
    console.log('3. 运行 generate-customer-report 生成最终报告');
    console.log('4. 根据需要手动调整Excel文件内容');
}

function scanForTestFiles(dir) {
    const testFiles = [];
    
    function scanDirectory(currentDir) {
        if (!fs.existsSync(currentDir)) return;
        
        const items = fs.readdirSync(currentDir);
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
                scanDirectory(fullPath);
            } else if (stat.isFile() && /\.test\.(ts|tsx|js|jsx)$/.test(item)) {
                testFiles.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return testFiles;
}

// 如果直接运行此脚本
if (require.main === module) {
    showUsageExamples();
}

module.exports = { showUsageExamples };