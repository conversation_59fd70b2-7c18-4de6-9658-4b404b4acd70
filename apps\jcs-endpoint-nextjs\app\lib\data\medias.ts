import prisma from "@/app/lib/prisma";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { revalidateTag, unstable_cache } from "next/cache";
import { cookies } from "next/headers";
import {
  ENV,
  OS_TYPE,
  PORTAL_CACHE_KEY_MEDIAS
} from "../definitions";
import { LogFunctionSignature } from "../logger";
import { handleServerError } from "../portal-error";
import { castValueToLabel, formatBytes } from "../utils";

/**
 * 媒体（ProductMedia）関連のデータ操作を行う静的クラスです。
 * 媒体のキャッシュ取得、ページ数計算、フィルタ・ソート処理を担当します。
 */
export class ServerDataMedias {
  /**
   * 指定されたライセンスIDに紐づく媒体一覧をキャッシュ付きで取得します。
   * キャッシュキーとタグにライセンスIDを含めることで、正確なキャッシュ無効化を実現します。
   *
   * @param {string} licenseId - ライセンスID
   * @returns {Promise<any[]>} 媒体情報配列
   */
  static async fetchCachedMedias(licenseId: string) {
    const cachedFn = unstable_cache(
      async () => {
        const plansWithLicense = await prisma.license.findUnique({
          where: { licenseId },
          select: {
            licensePlans: { select: { planId: true } },
          },
        });
        const planProducts = await prisma.planProduct.findMany({
          where: {
            planId: {
              in: plansWithLicense!.licensePlans.map((plan) => plan.planId),
            },
          },
          distinct: ["productCode", "version"],
          include: { productMedia: true },
        });
        const osTypes = await prisma.lov.findMany({
          where: { parentCode: OS_TYPE },
        });
        return planProducts.map((planProduct) => {
          const { productMedia } = planProduct;
          return {
            ...productMedia,
            bigMediaSize: Number(productMedia.bigMediaSize || 0),
            mediaSize:
              Number(productMedia.bigMediaSize || 0) > 0
                ? Number(productMedia.bigMediaSize)
                : productMedia.mediaSize,
            os: castValueToLabel(productMedia.os, osTypes) || "",
            formattedMediaSize: formatBytes(
              Number(productMedia.bigMediaSize || 0) > 0
                ? Number(productMedia.bigMediaSize)
                : productMedia.mediaSize
            ),
            formattedDocumentSize: formatBytes(productMedia.documentSize),
          };
        });
      },
      [`${PORTAL_CACHE_KEY_MEDIAS}-${licenseId}`],
      {
        revalidate: ENV.APP_CACHE_TTL_SECONDS,
        tags: [`${PORTAL_CACHE_KEY_MEDIAS}-${licenseId}`],
      },
    );
    return await cachedFn();
  }

  /**
   * 媒体一覧のページ数を取得します。
   *
   * @param {string} filter - フィルタ文字列
   * @param {number} size - 1ページあたりの件数
   * @param {boolean} refresh - キャッシュ強制リフレッシュ有無
   * @returns {Promise<number>} ページ数
   */
  @LogFunctionSignature()
  static async fetchProductMediaPages(filter: string, size: number, refresh: boolean) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);
      if (refresh) {
        revalidateTag(`${PORTAL_CACHE_KEY_MEDIAS}-${session!.user.licenseId}`);
      }
      const cachedMedias = await this.fetchCachedMedias(session!.user.licenseId);
      if (cachedMedias) {
        if (filter) {
          const filteredMedias = cachedMedias.filter(
            (media) =>
              media.name.toLowerCase().includes(filter.toLowerCase()) ||
              media.productCode.toLowerCase().includes(filter.toLowerCase()) ||
              media.version.toLowerCase().includes(filter.toLowerCase()) ||
              media.os.toLowerCase().includes(filter.toLowerCase()) ||
              media.releasedAt.toLowerCase().includes(filter.toLowerCase()) ||
              media.mediaName.toLowerCase().includes(filter.toLowerCase()) ||
              media.formattedMediaSize.toLowerCase().includes(filter.toLowerCase()) ||
              media.documentName.toLowerCase().includes(filter.toLowerCase()) ||
              media.formattedDocumentSize.toLowerCase().includes(filter.toLowerCase()),
          );
          return Math.ceil(Number(filteredMedias.length) / size);
        } else {
          return Math.ceil(Number(cachedMedias.length) / size);
        }
      } else {
        return 0;
      }
    } catch (error) {
      handleServerError(error);
    }
  }

  /**
   * フィルタ・ソート・ページング済みの媒体一覧を取得します。
   *
   * @param {string} filter - フィルタ文字列
   * @param {number} size - 1ページあたりの件数
   * @param {number} page - ページ番号
   * @param {string} sort - ソートキー
   * @param {"asc"|"desc"} order - ソート順
   * @param {string} [preferSort] - 優先ソートキー
   * @returns {Promise<any[]>} 媒体情報配列
   */
  @LogFunctionSignature()
  static async fetchFilteredProductMedias(
    filter: string,
    size: number,
    page: number,
    sort:
      | "name"
      | "productCode"
      | "version"
      | "os"
      | "releasedAt"
      | "mediaName"
      | "mediaSize"
      | "documentName"
      | "documentSize",
    order: "asc" | "desc",
    preferSort?:
      | "name"
      | "productCode"
      | "version"
      | "os"
      | "releasedAt"
      | "mediaName"
      | "mediaSize"
      | "documentName"
      | "documentSize",
  ) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);
      const cachedMedias = await this.fetchCachedMedias(session!.user.licenseId);
      if (cachedMedias) {
        let filteredMedias = cachedMedias;
        if (filter) {
          filteredMedias = cachedMedias.filter(
            (media) =>
              media.name.toLowerCase().includes(filter.toLowerCase()) ||
              media.productCode.toLowerCase().includes(filter.toLowerCase()) ||
              media.version.toLowerCase().includes(filter.toLowerCase()) ||
              media.os.toLowerCase().includes(filter.toLowerCase()) ||
              media.releasedAt.toLowerCase().includes(filter.toLowerCase()) ||
              media.mediaName.toLowerCase().includes(filter.toLowerCase()) ||
              media.formattedMediaSize.toLowerCase().includes(filter.toLowerCase()) ||
              media.documentName.toLowerCase().includes(filter.toLowerCase()) ||
              media.formattedDocumentSize.toLowerCase().includes(filter.toLowerCase()),
          );
        }
        if (sort) {
          filteredMedias.sort((a, b) => {
            if (sort === "mediaSize" || sort === "documentSize") {
              if (order === "asc") {
                return a[sort] - b[sort];
              } else {
                return b[sort] - a[sort];
              }
            } else {
              const aValue = a[sort].toLowerCase();
              const bValue = b[sort].toLowerCase();
              if (!preferSort || sort === preferSort) {
                if (order === "asc") {
                  return aValue.localeCompare(bValue);
                } else {
                  return bValue.localeCompare(aValue);
                }
              } else if (
                preferSort !== "mediaSize" &&
                preferSort !== "documentSize"
              ) {
                let firstComparison;
                if (order === "asc") {
                  firstComparison = aValue.localeCompare(bValue);
                } else {
                  firstComparison = bValue.localeCompare(aValue);
                }
                if (firstComparison !== 0) {
                  return firstComparison;
                }
                return a[preferSort].localeCompare(b[preferSort]);
              } else {
                return 0;
              }
            }
          });
        }
        const startIndex = (page - 1) * size;
        const endIndex = startIndex + size;
        const paginatedMedias = filteredMedias.slice(startIndex, endIndex);
        return paginatedMedias;
      } else {
        return [];
      }
    } catch (error) {
      handleServerError(error);
    }
  }
} 