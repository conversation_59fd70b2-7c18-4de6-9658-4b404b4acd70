## タスク受付処理詳細 (createTaskAction Server Action)

本章では、サーバ一覧画面から発行される全てのバックグラウンドタスク（操作ログのエクスポート、管理項目定義のインポート・エクスポート）の実行要求を統一的に処理するサーバサイドロジックであるcreateTaskAction サーバアクション の詳細な仕様について記述する。

### 概要

#### 責務
createTaskAction サーバアクション は、クライアントからのタスク実行要求を受け付けて検証し、必要な前処理（ファイルアップロード等）を行い、タスク情報をデータベースへ登録し、後続処理のためのメッセージをAzure Service Busへ送信する。主な責務は以下の通りである。

1.  入力パラメータ（タスク種別、対象サーバID、タスク固有パラメータ）を解析・検証する。
2.  対象コンテナのタスク実行状態（ステータス）を確認し、BUSYの場合はタスク実行要求を拒否する。
3.  タスクID・タスク名を生成し、タスク種別に応じた固有のパラメータ検証およびファイル処理（管理項目定義インポート時のAzure Blob Storageへの一時アップロード）を行う。
4.  DBトランザクション開始：コンテナレコードが存在しない場合はコンテナレコードを作成する。タスク関連情報（タスクID、タスク名、対象サーバ、パラメータ等）をデータベースへ登録する。
5.  Service Busの指定キュー（TaskInputQueue）へタスク実行要求のメッセージを送信する。
6.  送信成功の場合、DBトランザクションをコミットし、関連パスのキャッシュを無効化して、フロント側へ処理成功のレスポンスを返却する。
7.  受け付け処理失敗時は必要なエラー処理（トランザクションのロールバック、管理項目定義のインポートタスクの場合はAzure Blob Storageへアップロードした一時ファイルの削除）を行った後、フロント側へエラーのレスポンスを返却する。詳細な技術的エラー情報は、サーバサイドのログシステムに記録される。

#### 入力パラメータ (FormData 経由)
本サーバアクションは、クライアントから FormData オブジェクトを介してパラメータを受信する。

**共通パラメータ (全てのタスク種別で必須)**

| パラメータ名 | データ型 | 説明 | FormData受信時の値の例 |
|:---|:---|:---|:---|
| taskType | 文字列 | タスクの種別コード。値の一覧（Lov）テーブルのTASK_TYPEで定義される。 | "TASK_TYPE.OPLOG_EXPORT" |
| serverId | 文字列 | 対象サーバのID (Server.id)。 | "clxqm5s2c000108l3g0r1h2f4" |

**特定タスク種別パラメータ：**
・操作ログのエクスポート (taskType が TASK_TYPE.OPLOG_EXPORT の場合)

| パラメータ名 | データ型 | 説明 | FormData受信時の値の例 |
|:---|:---|:---|:---|
| exportStartDate | 文字列 | 操作ログのエクスポート開始日。"YYYY-MM-DD"形式。 | "2023-01-01" |
| exportEndDate | 文字列 | 操作ログのエクスポート終了日。"YYYY-MM-DD"形式。 | "2023-01-31" |

・管理項目定義のインポート (taskType が TASK_TYPE.MGMT_ITEM_IMPORT の場合)

| パラメータ名 | データ型 | 説明 | FormData受信時の値の例 |
|:---|:---|:---|:---|
| importFile | File オブジェクト | ユーザーがアップロードした管理項目定義CSVファイルのオブジェクト。(FormDataを介してFileオブジェクトとして直接送信される) | (実際の File オブジェクト) |
| originalFileName | 文字列 | アップロードされたファイルの元の名称。Fileオブジェクトのname属性から取得される。サーバサイドでは、この名称をログ記録、監査追跡、およびエラーフィードバックのコンテキストとして使用する。 | "my_definitions.csv" |

・管理項目定義のエクスポート (taskType が TASK_TYPE.MGMT_ITEM_EXPORT の場合)
このタスク種別に固有の追加パラメータは無い。

#### 返り値 (TaskActionResult)
本サーバアクションは、Promiseオブジェクトを返却し、そのPromiseは処理结果を含む以下の構造のオブジェクト (TaskActionResult) で解決（resolve）される。

| パラメータ名 | データ型 | 説明 |
|:---|:---|:---|
| success | bool | タスク作成要求がバックエンドで正常に受け付けられ、メッセージキューへ送信されたかを示す。trueは成功、falseは本サーバアクションの処理中にエラーが発生し失敗したことを示す。 |
| message | 文字列 | 実際にユーザーへ表示するメッセージ内容。 |

### 共通処理フローとロジック

以下に、createTaskActionがリクエストを受信した後の共通処理フローを示す。各タスク種別固有の処理分岐については後述する。

```mermaid
graph TD
    A["開始: Server Action<br/>(createTaskAction)"] --> B["共通パラメータ解析<br/>(taskType, serverId)"];
    B -- "検証失敗" --> ZFailCommonParam["エラー応答返却<br/>( success: false, message: EMEC0021「サーバの接続に失敗したため...」)"];
    B -- "検証成功" --> D["Serverテーブルからサーバ実行構成取得"];
    D -- "取得失敗/構成不備" --> ZFailConfig["エラー応答返却<br/>( success: false, message: EMEC0021「サーバの接続に失敗したため...」)"];
    D -- "取得成功" --> F_PrecheckConcurrency["ContainerConcurrencyStatusテーブルからコンテナ実行状態確認"];
    F_PrecheckConcurrency -- "取得失敗/構成不備" --> FB["エラー応答返却<br/>( success: false, message: EMEC0021「サーバの接続に失敗したため...」)"];
    F_PrecheckConcurrency -- "ステータス 'BUSY'" --> ZFailConcurrency["エラー応答返却<br/>( success: false, message: EMEC0022「{0}に対するタスクを実行中のため...」{0}:対象サーバ名 )"];
    F_PrecheckConcurrency -- "ステータス 'IDLE' または<br/>レコードが存在しない" --> C["タスクID、タスク名生成"];
    C --> E["タスク種別固有ロジック実行<br/>( パラメータ構築、ファイル処理等 )"];
    E -- "処理失敗" --> ZFailTaskSpecific["エラー応答返却<br/>( success: false, message: 対応したエラーメッセージ)"];
    E -- "処理成功" --> F_Transaction["DBトランザクション開始"];
    F_Transaction -- "コンテナレコード存在しない" --> F_Container["コンテナレコード作成"];
    F_Container -- "作成失敗" --> ZDbError["Txロールバック、<br/>インポートならアップロードした一時ファイルを削除<br/>、エラー応答返却<br/>( success: false, message: EMEC0021「サーバの接続に失敗したため...」)"];
    F_Container -- "作成成功" --> F_Task["タスクレコード作成"];
    F_Transaction -- "コンテナレコード存在する" --> F_Task;
    F_Task -- "作成失敗" --> ZDbError;
    F_Task -- "作成成功" --> G_Commit["DBトランザクション<br/>コミット"];
    G_Commit --> G_SendMessage["タスクメッセージ構築<br/>Service Busへ送信"];
    G_SendMessage -- "送信失敗" --> ZQueueError["インポートならアップロードした一時ファイルを削除、タスクレコード削除、<br/>エラー応答返却<br/>( success: false, message: EMEC0019「サーバの接続に失敗したため...」)"];
    G_SendMessage -- "送信成功" --> H_Revalidate["関連パスの<br/>キャッシュ無効化"];
    H_Revalidate --> M_Success["成功応答返却<br/>( success: true, message: EMEC0025「タスクの実行を受け付けました...」)"];
    
    ZFailCommonParam --> X["終了"];
    ZFailConfig --> X;
    FB --> X;
    ZFailConcurrency --> X;
    ZFailTaskSpecific --> X;
    ZDbError --> X;
    ZQueueError --> X;
    M_Success --> X;
```

**■ 共通処理フロー詳細**

1.  共通パラメータ解析 (taskType, serverId):
    FormData から taskType と serverId を抽出する。これらのパラメータの存在と taskType の有効性（データベース 値の一覧（Lov） テーブルのタスク種別定義TASK_TYPEに基づく）、serverId の形式を検証する。
    *   検証失敗時は、{ success: false, message:EMEC0021「サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」（{0}は「開始」で置換）}を返却する。エラー詳細をログに出力して、処理を終了する。

2.  サーバ実行構成取得:
    serverId に基づき Server テーブルからサーバ情報（サーバ名 Server.name、Dockerコンテナ名 Server.dockerContainerName、Azure VM名 Server.azureVmName、HRWグループ名Server.hrwGroupName）を取得する。
    *   DBからの取得失敗、またはタスク実行に必要な構成情報（dockerContainerName, azureVmName 等）が不足/不正している場合：{ success: false, message:EMEC0021「サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」（{0}は「開始」で置換）}を返却する。エラー詳細をログに出力して、処理を終了する。

3.  コンテナ実行状態確認：
    Azure VM名Server.azureVmName および Dockerコンテナ名Server.dockerContainerName を複合キーとして使用し、コンテナ実行状態（ContainerConcurrencyStatus） テーブルから対象コンテナのステータスを確認する。IDLE またはレコードが存在しない場合は処理を続行する。
    *   DBからの取得失敗、または必要な情報（コンテナのステータス）が不足/不正している場合：{ success: false, message:EMEC0021「サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」（{0}は「開始」で置換）}を返却する。エラー詳細をログに出力して、処理を終了する。
    *   コンテナのステータスが BUSY の場合は、{ success: false, message:EMEC0022「{0}に対するタスクを実行中のため実行できません。\n実行中のタスクが完了してから再度実行してください。」（{0}は対象サーバ名Server.nameで置換）}を返却する。エラー詳細をログに出力して、処理を終了する。

4.  タスクID、タスク名生成:
    タスクID（taskId）はUUIDとして生成する。
    タスク名（taskName）は、{ServerName}-{タスク種別}-{YYYYMMDDHHmmss}の形式で生成する。{ServerName}は対象サーバ名 Server.name、{タスク種別}は値の一覧（Lov） テーブルのタスク種別定義TASK_TYPEに基づいた日本語名、{YYYYMMDDHHmmss}はログインしているユーザーのタイムゾーンに応じた現在の日時で置き換える。

5.  タスク種別固有ロジック実行 (パラメータ構築、ファイル処理等):
    taskType に応じた固有のパラメータ検証やファイル処理を実行する。詳細は本章「特定タスク種別処理ロジック」で記述する。

6.  DBトランザクション開始

7.  レコード作成
    トランザクション内で以下を実行する。
    *   ContainerConcurrencyStatusレコード作成：ステップ3.でコンテナ実行状態（ContainerConcurrencyStatus） テーブルにレコードが存在しない場合は、ステップ2.で取得したAzure VM名およびDockerコンテナ名を複合主キーとし、ステータス（status）がIDLE、使用中のタスクID（currentTaskId）がNULLの新規レコードを挿入する。存在する場合はスキップ。
    *   Task レコード作成: 生成した taskId を主キーとし、タスク名（taskName）、初期ステータス TASK_STATUS.QUEUED、ステップ5.で構築されたTask.parametersJson用特定パラメータ等の情報を含む新規レコードをTask テーブルに挿入する。 
    *   レコード作成に失敗した場合：トランザクションをロールバックする。管理項目定義のインポートタスクの場合はステップ5.でアップロードした一時ファイルを削除する。{ success: false, message:EMEC0021「サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0O21)」（{0}は「開始」で置換）}を返却する。エラー詳細をログに出力して、処理を終了する。

8.  DBトランザクションをコミットする。

9.  タスクメッセージ構築・Service Bus (TaskInputQueue) へ送信:
    タスクIDを含むタスク実行要求メッセージを、環境変数 SERVICE_BUS_TASK_INPUT_QUEUE_NAME で指定されるService Busキュー (TaskInputQueue) へ送信する。
    *   送信に失敗した場合：管理項目定義のインポートタスクの場合はステップ5.でアップロードした一時ファイルを削除する。ステップ7.で作成されたタスクレコードを削除する。{ success: false, message:EMEC0019「サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0019)」（{0}は「開始」で置換）}を返却する。エラー詳細をログに出力して、処理を終了する。

10. 関連パスのキャッシュ無効化 (revalidatePath):
    タスク受付成功後、影響を受ける画面（タスク一覧画面/dashboard/tasks、操作ログエクスポート時は 操作ログ一覧画面/dashboard/operation-logs も）のキャッシュを無効化する。こうすることで、該当画面に遷移する時は最新のデータを取得し直す。

11. 成功応答返却:
    { success: true, message: EMEC0025「タスクの実行を受け付けました。タスクのステータスはタスク一覧画面で確認してください。\n対象サーバ：{0}\nタスク種別：{1}」（{0}は対象サーバ名、{1}はタスク種別の日本語名で置換）} を返却し、処理成功のログを記録する。

### 特定タスク種別処理ロジック

createTaskAction の共通処理フローにおけるステップ5.のタスク種別固有ロジックは、タスク種別taskType に応じて以下の処理を実行する。

#### 操作ログのエクスポート (TASK_TYPE.OPLOG_EXPORT) タスクの特定処理

1.  固有入力パラメータの抽出とサーバサイド検証:
    FormData から exportStartDate (YYYY-MM-DD形式), exportEndDate (YYYY-MM-DD形式) を抽出する。以下の検証を行う。
    *   両日付の存在と形式。失敗時は{ success: false, message: EMEC0016「{0}を指定してください。」（{0}:該当する日付フィールド名）}を返す。
    *   終了日 >= 開始日であること。違反時は{ success: false, message: EMEC0024「終了日は開始日以降の日付を指定してください。」}を返す。
    *   期間の日数がデータベース 値の一覧（Lov） テーブルの OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN の値（最大日数）以下であること。超過時は{ success: false, message: EMEC0020「{0} 日を超える期間が指定されました。{0} 日以内の期間を指定して再度実行してください。」（{0}は最大日数で置換）}を返す。

2.  Task.parametersJson用特定パラメータの構築:
    { "exportStartDate": YYYY-MM-DD形式の開始日文字列, "exportEndDate": YYYY-MM-DD形式の終了日文字列 }

#### 管理項目定義のインポート (TASK_TYPE.MGMT_ITEM_IMPORT) タスクの特定処理

1.  固有入力パラメータの抽出とサーバサイド検証:
    FormData から importFile (File オブジェクト) と originalFileName (文字列) を抽出する。以下の検証を行う。
    *   importFile の存在。未指定時は{ success: false, message: EMEC0016「{0}を指定してください。」（{0}:管理項目定義のCSVファイル）}を返す。
    *   importFile のMIMEタイプが text/csv であること。違反時は{ success: false, message: EMEC0017「無効なファイル形式です。CSVファイルを指定してください。」}を返す。

2.  Azure Blob Storageへの一時ファイルアップロード:
    入力パラメータのFileオブジェクトを、環境変数 AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF（デフォルトで「assetsfield-def」）で指定されるコンテナへ、パス {currentUser.licenseId}/imports/{生成したtaskId}/assetsfield_def.csv、固定ファイル名 assetsfield_def.csv でアップロードする。失敗時は{ success: false, message: EMEC0018「ファイルのアップロードに失敗しました。時間をおいてから再度実行してください。」}を返す。

3.  Task.parametersJson用特定パラメータの構築:
    { "importedFileBlobPath": "{currentUser.licenseId}/imports/{生成したtaskId}/assetsfield_def.csv", "originalFileName": 元のファイル名 }

#### 管理項目定義のエクスポート (TASK_TYPE.MGMT_ITEM_EXPORT) タスクの特定処理

1.  固有入力パラメータの抽出とサーバサイド検証:
    なし

2.  Task.parametersJson用特定パラメータの構築:
    なし

### エラー処理メカニズム

*   予期せぬ内部例外発生時は、{ success: false, message:EMEC0027「サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0027)」（{0}は「開始」で置換）}を返却する。
*   管理項目定義のインポートタスクの場合、すでにAzure Blob Storageへ一時ファイルをアップロードした後で受け付け処理にエラーが発生する時はアップロードしたファイルを削除する。
*   DBトランザクション内にエラーが発生する時はトランザクションをロールバックする。
*   各処理ステップでの業務ロジックエラー時または処理成功時は、処理結果のsuccessフィールドと対応するメッセージ内容のmessageフィールドが含まれるCreateTaskActionResultオブジェクトを構築し返却する。