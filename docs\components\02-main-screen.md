# 组件：主界面 (Main Screen)

## 1. 概要 (Overview)

### 1.1 目的 (Purpose)
本组件是用户成功登录“JCS 端点资产与任务管理系统”后进入的核心界面框架。它提供了一致性的导航结构，包括页面顶部的全局导航栏、左侧的业务功能侧边栏以及内容区域上方的面包屑导航。主界面的目的是作为用户访问系统各项功能的中心枢纽，确保用户能够清晰、高效地定位和操作所需功能。

### 1.2 用户故事/需求 (User Stories/Requirements)
- 作为一名已登录的用户，我希望看到一个清晰、一致的界面布局，包含易于理解的导航元素，以便我能快速开始工作。
- 作为一名已登录的用户，我希望在页面顶部始终能看到通用的操作入口，如我的用户信息、通知提醒、密码修改和安全登出系统的选项。
- 作为一名已登录的用户，我希望在页面左侧看到一个根据我的权限动态显示的侧边栏菜单，它能引导我访问核心的业务功能模块，如服务器管理、任务列表、文件查阅等。
- 作为一名已登录的用户，我希望在浏览系统不同功能页面时，能通过面包屑导航清晰地了解我当前在系统层级结构中的位置，并能快速返回到上级页面。
- 作为一名已登录的用户，我希望侧边栏的一级菜单（如“管理”、“文件”）可以展开和折叠，以便我能更好地组织和查看菜单项。

### 1.3 高层依赖与交互 (High-Level Dependencies & Interactions)
- **用户认证与会话**: 本组件的展现和内容（特别是侧边栏菜单项）依赖于用户的认证状态和通过[登录功能](./01-login.md)建立的有效会话。用户的权限信息将决定侧边栏菜单的可见性。
- **路由与内容加载**:
    - 当用户通过导航栏或侧边栏选择某个功能时，主界面框架负责加载并显示对应功能组件的内容到主内容区域。这通常通过前端路由机制实现。
    - 面包屑导航需要根据当前的路由状态动态生成。
- **通用功能调用**: 导航栏中的“密码修改”、“登出”等按钮会触发相应的独立功能组件或认证流程。
- **通知服务 (概念性)**: 导航栏的“通知”按钮暗示可能存在一个通知服务，用于向用户推送系统消息或告警（尽管`fs.md`对此细节描述不多）。
- **许可证信息**: 导航栏的“ライセンス (许可证)”按钮会触发显示用户当前许可证相关信息的功能。

## 2. 功能规格 (Functional Specifications)

### 2.1 主要流程 (High-Level Flow)
1.  用户成功登录系统后，系统加载并显示主界面框架。
2.  主界面框架根据用户的权限信息，动态构建并显示导航栏和侧边栏的菜单项。
3.  面包屑导航初始化，通常显示当前页面的顶级路径（例如，“首页”或默认加载的功能模块名称）。
4.  用户通过点击导航栏的通用功能按钮（如“密码修改”、“登出”），或侧边栏的业务功能菜单项，或面包屑导航的链接，来访问系统的不同部分。
5.  当用户导航到新的功能页面时：
    *   主内容区域更新，显示新选定功能组件的界面。
    *   面包屑导航路径相应更新，反映新的当前位置。
    *   侧边栏中对应菜单项可能高亮显示，指示当前活动的功能模块。

```mermaid
graph TD
    A[用户成功登录] --> B[加载主界面框架];
    B --> C[获取用户权限信息];
    C --> D[动态生成导航栏内容<br/>(用户信息, 通知, 密码修改, 登出等)];
    C --> E[动态生成侧边栏菜单<br/>(根据权限显示: 管理, 文件等一级菜单及其子项)];
    B --> F[初始化面包屑导航<br/>(显示当前页面路径)];
    D & E & F --> G{用户与导航元素交互};
    G -- 点击导航栏通用功能 --> H[执行通用功能<br/>(如: 弹出密码修改对话框, 执行登出流程)];
    G -- 点击侧边栏菜单项 --> I[加载对应功能组件到主内容区];
    I --> J[更新面包屑导航路径];
    G -- 点击面包屑导航链接 --> K[导航到面包屑指定层级页面];
    K --> J;
```

### 2.2 业务规则 (Business Rules)
-   **导航元素持久性**: 导航栏和侧边栏作为主界面的固定组成部分，在用户已登录状态下应持续显示（除非特定功能要求全屏或无导航模式，但`fs.md`未提及此情况）。
-   **权限驱动菜单**: 侧边栏中显示的菜单项（一级和二级）必须严格根据当前登录用户的权限动态生成。用户不应看到其无权访问的功能入口。
-   **面包屑导航逻辑**:
    *   面包屑应准确反映用户当前在系统功能层级结构中的位置。
    *   每个面包屑节点（除最后一个表示当前页面的节点外）应可点击，并能导航用户返回到对应的上级页面。
-   **默认加载页面**: 用户登录后，主界面应加载一个默认的功能页面到主内容区（例如，“服务器列表”页面）。
-   **一级菜单交互**: 侧边栏的一级菜单（如“管理”、“文件”）作为可展开/折叠的父节点，点击它们会切换其下二级菜单的显示状态，自身不直接导航到特定页面。
-   **二级菜单交互**: 点击侧边栏的二级菜单项会直接导航到对应的功能页面。
-   **导航栏功能**:
    *   “当前用户”区域显示当前登录用户的ID。
    *   “お知らせ (通知)”按钮用于访问系统通知信息（具体通知功能详见[お知らせ情報表示](./04-notification-display.md)）。
    *   “パスワード変更 (密码修改)”按钮用于访问[密码修改](./06-password-change.md)功能。
    *   “ログアウト (登出)”按钮用于启动[登出](./05-logout.md)流程。
    *   “ライセンス (许可证)”按钮用于访问[许可证信息](./07-license-info.md)功能。
-   **“更新”图标按钮行为** (当存在于具体功能页面的工具栏时，如服务器列表)：
    *   保持当前页面的筛选条件不变。
    *   列表排序状态重置为该页面的默认排序。
    *   分页状态重置（当前页码设为1，每页显示数量恢复为默认值）。
    *   重新从后端获取并显示最新的数据。

### 2.3 用户界面概述 (User Interface Overview)

-   **界面草图/核心区域**: (参考 `fs.md` 图4.2.6 (1) 作为高层概念)
    1.  **导航栏 (Navigation Bar)**: 固定在页面最顶部。
        *   左侧：可能显示应用Logo或名称。
        *   右侧：包含当前登录用户ID显示、お知らせ(通知)按钮、パスワード変更(密码修改)按钮、ログアウト(登出)按钮、ライセンス(许可证)按钮。
    2.  **侧边栏 (Sidebar)**: 固定在页面左侧。
        *   包含可展开/折叠的一级菜单项（如“管理”、“ファイル(文件)”）。
        *   一级菜单下包含二级菜单项（如“サーバ一覧”、“タスク一覧”等，具体项根据 `fs.md` 表4.1中“ポータルシステム画面の機能一覧”并结合`fs.md`主界面章节的菜单结构）。
            | 一级菜单 | 二级菜单         |
            | -------- | ---------------- |
            | 管理     | サーバ一覧     |
            |          | タスク一覧     |
            | ファイル | 操作ログ一覧   |
            |          | 製品媒体一覧   |
            |          | マニュアル一覧 |
            |          | 提供ファイル一覧 |
            |          | サポート情報一覧 |
    3.  **面包屑导航 (Breadcrumb Navigation)**: 位于导航栏下方，主内容区域的上方。显示从首页到当前页面的路径，如 “ホーム > 管理 > サーバ一覧”。
    4.  **主内容区域 (Main Content Area)**: 占据界面剩余的主要空间，用于显示用户当前选定功能模块的具体内容。
-   **主要交互点**:
    *   用户点击导航栏右侧的各项按钮，执行相应通用操作。
    *   用户点击侧边栏的一级菜单项，展开或折叠其下的二级菜单。
    *   用户点击侧边栏的二级菜单项，主内容区域加载并显示对应功能页面，面包屑更新。
    *   用户点击面包屑导航中的上级链接，主内容区域加载并显示对应上级页面，面包屑更新。
-   **画面項目 (Screen Items - High Level)**:
    *   显示：导航链接、当前用户信息、面包屑路径、当前加载的功能模块内容。
    *   用户可操作：点击所有导航元素。

### 2.4 前提条件 (Preconditions)
-   用户必须已成功通过[登录功能](./01-login.md)完成身份验证，并且拥有有效的用户会话。
-   后端API必须能够提供构建侧边栏菜单所需的权限信息（或前端能够根据用户角色静态/动态构建）。
-   前端路由机制已正确配置，能够根据用户的导航操作加载相应的功能组件。

### 2.5 制约事项 (Constraints/Limitations)
-   **浏览器兼容性**: 仅正式支持 Microsoft Edge 和 Google Chrome 浏览器。
-   **语言支持**: 界面显示语言仅支持日语。
-   **布局固定性**: 导航栏和侧边栏的位置是固定的，不允许用户自定义调整。
-   **响应式设计**: 虽然主内容区域宽度自适应，但FS未明确要求对极小屏幕（如移动设备）的完全响应式布局，主要针对桌面浏览器体验。

### 2.6 注意事项 (Notes/Considerations)
-   侧边栏菜单项的文本和顺序应与 `fs.md` 中定义的保持一致。
-   当主内容区域加载新的功能模块时，应有清晰的加载状态指示（如果加载时间较长）。
-   面包屑导航的生成逻辑需要准确处理多层级路径。
-   对于用户无权限访问的菜单项，应直接不在侧边栏中显示，而不是显示为禁用状态。

### 2.7 错误处理概述 (Error Handling Overview)
-   **功能模块加载失败**: 如果用户点击侧边栏菜单尝试加载某个功能模块，但该模块因故（如JavaScript错误、API数据获取失败）无法正常显示，主内容区域应显示一个用户友好的错误提示（例如，“无法加载[功能名称]，请稍后重试或联系管理员。”），而不是空白页或崩溃。详细错误应记录到控制台或日志系统。
-   **导航逻辑错误**: 如果因路由配置问题或程序错误导致点击导航元素无法正确跳转，应尽可能保持用户在当前稳定页面，并记录错误。
-   **权限校验失败 (理论上不应发生)**: 如果用户通过某种非正常途径尝试访问其无权限的URL，应用应有全局的权限校验机制（通常在路由层面或API层面）来阻止访问并显示权限不足页面或重定向到登录页。主界面本身侧重于不展示无权限入口。

### 2.8 相关功能参考 (Related Functional References)
*   **入口**: [登录 (Login)](./01-login.md) - 用户通过此功能进入主界面。
*   **核心导航目标**:
  *   [服务器列表 (Server List)](./03-server-list.md) - 管理服务器信息。
  *   [任务列表 (Task List)](./task-list.md) - 管理任务执行。
  *   [操作日志列表 (Operation Log List)](./operation-log.md) - 查看系统操作记录。
  *   [产品媒体列表 (Product Media List)](./product-media.md) - 管理产品媒体文件。
  *   [マニュアル一覧 (Manual List)](./manual.md) - 查看系统手册。
  *   [提供ファイル一覧 (Provided Files)](./provided-files.md) - 查看可下载文件。
  *   [サポート情報一覧 (Support Information)](./support-info.md) - 查看支持信息。
*   **导航栏功能**:
  *   [お知らせ情報表示 (Notifications)](./04-notification-display.md) - 系统通知功能。
  *   [密码修改 (Password Change)](./06-password-change.md) - 修改账户密码。
  *   [登出 (Logout)](./05-logout.md) - 退出系统。
  *   [许可证信息 (License)](./07-license-info.md) - 查看许可证信息。
*   **系统整体架构**: [系统架构文档](../../architecture/system-architecture.md) - 主界面在系统架构中的定位。
*   **原始规格**: 参考 `fs.md` 的 4.2 节和表 4.1 的功能列表。
