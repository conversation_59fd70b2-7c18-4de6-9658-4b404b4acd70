# 数据模型: 值列表 (Lov)

*   **表名 (逻辑名)**: `Lov`
*   **对应UI界面**: N/A (主要由系统内部用于获取配置、枚举及下拉列表选项)
*   **主要用途**: 存储系统中使用的各类枚举值、配置参数和下拉列表选项，提供统一的管理和引用方式。支持层级结构。

## 1. 字段定义

| 字段名 (Prisma/英文) | 数据类型 (SQL参考) | PK | FK | UQ | Nullable | Default | 描述 (中文) |
|-----------------|--------------|----|----|----|----------|---------|---------|
| `id` | VARCHAR(36) | ● | - | - | - | `cuid()` | 主键。CUID格式，由系统自动生成。 |
| `code` | VARCHAR(100) | - | - | ● | - | - | LOV条目的唯一代码。例如："SERVER_TYPE", "SERVER_TYPE.GENERAL_MANAGER", "OPERATION_LOG_CONFIG.ALLOWED_PLANS.OPLOG_ALLOW_STANDARD_PLAN"。 |
| `name` | NVARCHAR(255) | - | - | - | - | - | LOV条目的显示名称，通常用于UI展示。例如："サーバタイプ", "统括マネージャ", "允许导出: STANDARD套餐"。 |
| `parentCode` | VARCHAR(100) | - | ● | - | Yes | - | 外键，引用父级LOV条目的`code`。用于实现层级结构的LOV。如果是一级条目，则此字段为NULL。 |
| `value` | NVARCHAR(MAX) | - | - | - | - | - | LOV条目的实际值。根据`code`的上下文，可以是配置值、用于业务逻辑判断的内部值等。例如："JP1/ITDM2(統括マネージャ)", "QUEUED", "STANDARD"。 |
| `isEnabled` | BOOLEAN | - | - | - | - | `true` | 指示此LOV条目当前是否可用/启用。默认为 `true`。 |

*PK: Primary Key, FK: Foreign Key, UQ: Unique Key*

## 2. 关系

*   **自关联 (父级 `parent`)**: 可选的多对一关系。通过 `parentCode` 字段关联到本表（`Lov`）的 `code` 唯一键，用于表示层级关系中的父条目。`onDelete: NoAction, onUpdate: NoAction` 表示数据库层面不执行级联操作。
*   **自关联 (子级 `children`)**: 一对多关系 (`children Lov[]`)。一个LOV条目可以拥有多个子条目。

## 3. 层级结构示例

`Lov`表通过`parentCode`支持层级数据。例如，系统可以通过这种结构管理特定功能的执行权限与不同许可证套餐的关联：

*   一级条目 (功能配置的根): 
    *   `code`="OPERATION_LOG_CONFIG", 
    *   `name`="操作日志相关配置", 
    *   `parentCode`=NULL
*   二级条目 (特定权限配置组的父级): 
    *   `code`="OPERATION_LOG_CONFIG.ALLOWED_PLANS", 
    *   `name`="允许导出的套餐列表", 
    *   `parentCode`="OPERATION_LOG_CONFIG"
    *   三级条目 (具体的套餐权限): 
        *   `code`="OPERATION_LOG_CONFIG.ALLOWED_PLANS.OPLOG_ALLOW_STANDARD_PLAN", 
        *   `name`="允许导出: STANDARD套餐", 
        *   `parentCode`="OPERATION_LOG_CONFIG.ALLOWED_PLANS", 
        *   `value`="STANDARD"
    *   三级条目 (具体的套餐权限): 
        *   `code`="OPERATION_LOG_CONFIG.ALLOWED_PLANS.OPLOG_ALLOW_LIGHT_B_PLAN", 
        *   `name`="允许导出: LIGHT_B套餐", 
        *   `parentCode`="OPERATION_LOG_CONFIG.ALLOWED_PLANS", 
        *   `value`="LIGHT_B"

另一个简单的二级结构示例，用于定义任务状态及其界面显示文本：

*   一级条目: `code`="TASK_STATUS", `name`="任务状态", `parentCode`=NULL
    *   二级条目: `code`="TASK_STATUS.QUEUED", `name`="执行等待", `parentCode`="TASK_STATUS", `value`="実行待ち"
    *   二级条目: `code`="TASK_STATUS.COMPLETED_SUCCESS", `name`="正常结束", `parentCode`="TASK_STATUS", `value`="正常終了"

## 4. 索引 (示例)

*   `PRIMARY KEY (id)`
*   `UNIQUE KEY (code)` (确保代码唯一，Prisma Schema已定义)
*   `INDEX idx_lov_parentcode (parentCode)` (支持快速查找子条目)
*   `INDEX idx_lov_name (name)` (如果经常按名称搜索)
*   `INDEX idx_lov_isenabled (isEnabled)`