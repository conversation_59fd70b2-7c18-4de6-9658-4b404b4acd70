/**
 * @fileoverview サポートファイルデータ生成器
 * @description 開発環境用の大量のサポートファイルデータを生成する
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { PrismaClient } from '@prisma/client';
import { BaseGenerator } from './base.generator';

/**
 * サポートファイルデータ生成器クラス
 * 様々な重要度のサポートファイルデータを大量に生成する
 */
export class SupportFileGenerator extends BaseGenerator {
  private readonly FILE_COUNT = 120; // 生成するファイル数
  private readonly BATCH_SIZE = 12;

  constructor(prisma: PrismaClient) {
    super(prisma, 'SupportFileGenerator');
  }

  /**
   * 生成器名を取得する
   * @returns 生成器名
   */
  getGeneratorName(): string {
    return 'サポートファイルデータ';
  }

  /**
   * 生成予定のデータ数を取得する
   * @returns 生成予定数
   */
  getExpectedCount(): number {
    return this.FILE_COUNT;
  }

  /**
   * 既存のサポートファイルデータをクリーンアップする
   * 関連するプランサポートも削除する
   */
  async cleanup(): Promise<void> {
    try {
      // 関連するプランサポートを先に削除
      await this.prisma.planSupport.deleteMany({});
      
      // サポートファイルデータを削除
      const deleteResult = await this.prisma.supportFile.deleteMany({});
      console.log(`既存サポートファイルデータを削除しました (${deleteResult.count}件)`);
    } catch (error) {
      console.error('サポートファイルデータのクリーンアップに失敗しました', error);
      throw error;
    }
  }

  /**
   * サポートファイルデータを生成する
   * 様々な重要度のサポートファイルを大量に生成する
   * @returns 生成されたデータの数
   */
  async generate(): Promise<number> {
    try {
      // 重要度のLOVデータを取得
      const importanceLevels = await this.getImportanceLevels();
      if (importanceLevels.length === 0) {
        this.logger.warn('重要度のLOVデータが見つかりません。デフォルト値を使用します。');
      }

      console.log(`${importanceLevels.length}種類の重要度を使用します`);

      // バッチでサポートファイルデータを生成
      return await this.generateInBatches(
        this.FILE_COUNT,
        this.BATCH_SIZE,
        async (startIndex: number, count: number) => {
          return await this.generateFileBatch(startIndex, count, importanceLevels);
        }
      );
    } catch (error) {
      console.error('サポートファイルデータ生成中にエラーが発生しました', error);
      throw error;
    }
  }

  /**
   * サポートファイルデータのバッチを生成する
   * @param startIndex 開始インデックス
   * @param count 生成数
   * @param importanceLevels 重要度レベル
   * @returns 生成されたサポートファイルデータ
   */
  private async generateFileBatch(
    startIndex: number,
    count: number,
    importanceLevels: any[]
  ): Promise<any[]> {
    const files = [];

    for (let i = 0; i < count; i++) {
      const fileIndex = startIndex + i + 1;
      const fileInfo = this.generateFileInfo(fileIndex);
      const importance = this.selectImportance(importanceLevels);
      
      const file = {
        title: fileInfo.title,
        productName: fileInfo.productName,
        importance: importance.code,
        publishedAt: this.generatePublishDate(),
        updatedAt: this.generateUpdateDate(),
        fileName: fileInfo.fileName,
      };

      files.push(file);
    }

    // バッチでデータベースに挿入
    await this.prisma.supportFile.createMany({
      data: files,
    });

    return files;
  }

  /**
   * ファイル情報を生成する
   * @param index ファイルインデックス
   * @returns ファイル情報
   */
  private generateFileInfo(index: number): {
    title: string;
    productName: string;
    fileName: string;
  } {
    const supportTypes = [
      '脆弱性対応情報',
      'セキュリティアップデート',
      '不具合修正パッチ',
      '機能追加アップデート',
      'パフォーマンス改善',
      '互換性情報',
      'インストール手順',
      'トラブルシューティング',
      '設定変更手順',
      'バックアップ手順',
      'リストア手順',
      'メンテナンス情報',
      '運用ガイドライン',
      'ベストプラクティス',
      'FAQ集'
    ];

    const productNames = [
      'JP1/統括マネージャ',
      'JP1/中継マネージャ',
      '秘文/管理コンソール',
      'JP1/ログ管理',
      'JP1/監視システム',
      'JP1/バックアップ',
      'JP1/セキュリティ管理',
      'JP1/ネットワーク管理',
      'JP1/システム運用',
      'JP1/性能管理',
      'JP1/構成管理',
      'JP1/変更管理',
      'JP1/問題管理',
      'JP1/資産管理',
      'JP1/パッチ管理'
    ];

    const supportType = this.faker.randomFromArray(supportTypes);
    const productName = this.faker.randomFromArray(productNames);
    const issueNumber = `SUP-${index.toString().padStart(4, '0')}`;
    
    const title = `${productName} ${supportType} (${issueNumber})`;
    const fileName = this.generateFileName(supportType, issueNumber);

    return {
      title,
      productName,
      fileName,
    };
  }

  /**
   * ファイル名を生成する
   * @param supportType サポートタイプ
   * @param issueNumber 問題番号
   * @returns ファイル名
   */
  private generateFileName(supportType: string, issueNumber: string): string {
    const extensions = ['pdf', 'docx', 'txt', 'html', 'zip'];
    const extension = this.faker.randomFromArray(extensions);
    
    // 日本語ファイル名の場合もある
    if (this.faker.randomBoolean(0.4)) {
      const jpFileNames = [
        '脆弱性対応手順書',
        'セキュリティ更新手順',
        '不具合修正手順書',
        '機能追加ガイド',
        'パフォーマンス改善手順',
        '互換性確認資料',
        'インストール手順書',
        'トラブル対応手順',
        '設定変更ガイド',
        'バックアップ手順書',
        'リストア手順書',
        'メンテナンス手順',
        '運用手順書',
        'ベストプラクティス集',
        'よくある質問集'
      ];
      const jpFileName = this.faker.randomFromArray(jpFileNames);
      return `${jpFileName}_${issueNumber}.${extension}`;
    } else {
      const enFileNames = [
        'vulnerability_fix',
        'security_update',
        'bug_fix',
        'feature_update',
        'performance_improvement',
        'compatibility_info',
        'installation_guide',
        'troubleshooting',
        'configuration_guide',
        'backup_procedure',
        'restore_procedure',
        'maintenance_info',
        'operation_guide',
        'best_practices',
        'faq'
      ];
      const enFileName = this.faker.randomFromArray(enFileNames);
      return `${enFileName}_${issueNumber}.${extension}`;
    }
  }

  /**
   * 重要度を選択する
   * @param importanceLevels 重要度レベル
   * @returns 選択された重要度
   */
  private selectImportance(importanceLevels: any[]): any {
    if (importanceLevels.length === 0) {
      // デフォルト重要度
      const defaultImportance = ['AAA', 'AA', 'A', 'B', 'C', '-'];
      const weights = [5, 10, 20, 30, 25, 10]; // AAAが最も少なく、Bが最も多い
      return { code: this.weightedRandomChoice(defaultImportance, weights) };
    }

    // 重み付き選択（高重要度は少なく、低重要度は多く）
    const weights = importanceLevels.map(level => {
      if (level.code.includes('AAA')) return 5;
      if (level.code.includes('AA')) return 10;
      if (level.code.includes('A')) return 20;
      if (level.code.includes('B')) return 30;
      if (level.code.includes('C')) return 25;
      return 10; // NONE
    });

    return this.weightedRandomChoice(importanceLevels, weights);
  }

  /**
   * 公開日を生成する
   * @returns 公開日（YYYY-MM-DD形式）
   */
  private generatePublishDate(): string {
    const startDate = new Date('2022-01-01');
    const endDate = new Date();
    const randomDate = new Date(
      startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime())
    );
    return randomDate.toISOString().split('T')[0];
  }

  /**
   * 更新日を生成する
   * @returns 更新日（YYYY-MM-DD形式）
   */
  private generateUpdateDate(): string {
    const startDate = new Date('2022-01-01');
    const endDate = new Date();
    const randomDate = new Date(
      startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime())
    );
    return randomDate.toISOString().split('T')[0];
  }

  /**
   * 重要度レベルのLOVデータを取得する
   * @returns 重要度レベルの配列
   */
  private async getImportanceLevels(): Promise<any[]> {
    return await this.prisma.lov.findMany({
      where: {
        parentCode: 'SUPPORT_IMPORTANCE',
        isEnabled: true,
      },
      select: {
        code: true,
        name: true,
        value: true,
      },
    });
  }

  /**
   * サポートファイルの統計情報を取得する
   * @returns 統計情報
   */
  async getStatistics(): Promise<{
    totalCount: number;
    byImportance: Record<string, number>;
    byProduct: Record<string, number>;
    byExtension: Record<string, number>;
    recentFiles: number;
    oldFiles: number;
  }> {
    const [
      totalCount,
      files,
      recentFiles,
      oldFiles,
    ] = await Promise.all([
      this.prisma.supportFile.count(),
      this.prisma.supportFile.findMany({
        select: {
          importance: true,
          productName: true,
          fileName: true,
          publishedAt: true,
        },
      }),
      this.prisma.supportFile.count({
        where: {
          publishedAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] },
        },
      }),
      this.prisma.supportFile.count({
        where: {
          publishedAt: { lt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] },
        },
      }),
    ]);

    // 各種集計
    const byImportance: Record<string, number> = {};
    const byProduct: Record<string, number> = {};
    const byExtension: Record<string, number> = {};

    files.forEach(file => {
      byImportance[file.importance] = (byImportance[file.importance] || 0) + 1;
      byProduct[file.productName] = (byProduct[file.productName] || 0) + 1;
      
      const extension = file.fileName.split('.').pop()?.toLowerCase() || 'unknown';
      byExtension[extension] = (byExtension[extension] || 0) + 1;
    });

    return {
      totalCount,
      byImportance,
      byProduct,
      byExtension,
      recentFiles,
      oldFiles,
    };
  }

  /**
   * 配列からランダムな要素を選択する
   * @param array 選択元の配列
   * @returns ランダムに選択された要素
   */
  private randomFromArray<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }
}
