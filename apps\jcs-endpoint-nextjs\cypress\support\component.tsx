// ***********************************************************
// This example support/component.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Alternatively you can use CommonJS syntax:
// require('./commands')

import { mount } from 'cypress/react18'
import { AppRouterContext, AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';
import { HeadManagerContext } from 'next/dist/shared/lib/head-manager-context.shared-runtime';
import 'cypress-plugin-tab';
import "cypress-real-events";
// import '@cypress/code-coverage/support'

//Ensure global styles are loaded
import '/styles/globals.css';

// Augment the Cypress namespace to include type definitions for
// your custom command.
// Alternatively, can be defined in cypress/support/component.d.ts
// with a <reference path="./component" /> at the top of your spec.
declare global {
  namespace Cypress {
    interface Chainable {
      mount: typeof mount
    }
  }
}

Cypress.Commands.add('mount', mount)

Cypress.Commands.add('nextMount', (component, options: any) => {
  const createRouter = (params: Partial<AppRouterInstance>) => ({
    back: cy.spy().as('back'),
    forward: cy.spy().as('forward'),
    prefetch: cy.stub().as('prefetch').resolves(),
    push: cy.spy().as('push'),
    replace: cy.spy().as('replace'),
    refresh: cy.spy().as('refresh'),
    ...params,
  })

  const router = createRouter(options?.router as AppRouterInstance)

  const createHeadManager = (params: any) => ({
    updateHead: cy.stub().as('head:updateHead'),
    mountedInstances: new Set(),
    updateScripts: cy.stub().as('head:updateScripts'),
    scripts: new Set(),
    getIsSsr: () => false,
    appDir: false,
    nonce: '_',
    ...params
  })

  const headManager = createHeadManager(options?.head || {})

  return mount(
    <HeadManagerContext.Provider value={headManager}>
      <AppRouterContext.Provider value={router}>
        {component}
      </AppRouterContext.Provider>
    </HeadManagerContext.Provider>,
    options
  )
})

// Example use:
// cy.mount(<MyComponent />)
