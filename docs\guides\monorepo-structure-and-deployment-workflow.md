# Monorepo项目实施指南

## 1. 引言与目标

本指南为 "JCS 端点资产与任务管理系统" 项目提供了一套在特定约束条件下实施Monorepo（单一代码仓库）的策略和方法。主要目标是建立一个清晰、可维护、可测试的项目结构，支持开发团队的协作，并为AI编程助手提供有效的上下文信息。

**核心约束：**

*   **无外部共享代码包 (`packages/`)**: 代码复用限制在各应用或自动化单元内部。
*   **Next.js应用通过本地Git部署**: 保持现有的本地Git推送至Azure App Service的部署方式。
*   **Azure Functions应用通过命令行工具部署**: 使用Azure Functions Core Tools进行构建和发布。
*   **Azure Automation Runbooks通过本地脚本部署/打包**: 通过本地PowerShell脚本或打包脚本交付。
*   **不使用CI/CD流水线**: 所有构建、打包和部署发起操作在本地进行。
*   **代码不上传至公共云端Git服务**: Monorepo主要用于内部版本控制。

**目标读者**: 项目开发团队成员，AI编程助手。

## 2. 项目整体目录结构

```plaintext
jcs-endpoint-monorepo/
├── .git/                     # Monorepo 主Git仓库
├── .vscode/                  # VS Code 工作区配置 (可选)
├── apps/                     # 存放可独立部署的应用程序单元
│   ├── jcs-endpoint-nextjs/  # Next.js 门户工程
│   │   ├── .git/             # 独立的Git仓库 (用于本地Git部署到Azure)
│   │   └── ... (内部结构不变)
│   ├── jcs-backend-services-standard/      # [新] 标准超时 (5分钟) 的Function App
│   │   ├── TaskExecuteFunc/
│   │   │   ├── TaskExecuteFunc.ts          # 函数源代码 (v4模型, 无function.json)
│   │   │   └── TaskExecuteFunc.test.ts     # [新] 与源代码并列的Jest测试文件
│   │   ├── TaskCancellationFunc/
│   │   │   ├── TaskCancellationFunc.ts
│   │   │   └── TaskCancellationFunc.test.ts  # [新]
│   │   ├── ... (其他所有函数都遵循此模式)
│   │   ├── lib/                            # 此App内部的共享代码
│   │   ├── jest.config.js                  # [新] Jest的配置文件
│   │   ├── host.json                       # 关键：配置 "functionTimeout": "00:05:00"
│   │   ├── local.settings.json
│   │   ├── package.json                    # [更新] 包含jest依赖和测试脚本
│   │   └── tsconfig.json
│   │
│   └── jcs-backend-services-long-running/  # [新] 长时间运行 (30分钟) 的Function App
│       ├── RunbookProcessorFunc/
│       │   ├── RunbookProcessorFunc.ts
│       │   └── RunbookProcessorFunc.test.ts  # [新]
│       ├── lib/
│       ├── jest.config.js                  # [新]
│       ├── host.json                       # 关键：配置 "functionTimeout": "00:30:00"
│       ├── local.settings.json
│       ├── package.json                    # [更新]
│       └── tsconfig.json
├── automation/               # Azure Automation 资源
│   └── ... (内部结构不变)
├── docs/                     # 核心技术文档
│   └── ... (内部结构不变)
├── docs-delivery/            # 存放交付用的日文文档
│   └── ... (内部结构不变)
├── scripts/                  # 本地辅助脚本
│   ├── deploy-functions.sh   # [新] 用于部署两个Function App的脚本
│   └── ... (其他辅助脚本)
├── .gitignore                # Monorepo主Git仓库的.gitignore
├── package.json              # Monorepo根package.json
└── README.md                 # 项目总览README
```

## 3. Git 版本控制策略

*   **主Monorepo仓库 (`jcs-endpoint-monorepo/.git`)**:
    *   作为团队内部开发和版本控制的中心。
    *   **`.gitignore` (主仓库)**:
        *   全局忽略：`node_modules/`, `*.env*`, `local.settings.json`, `dist/` (编译输出目录), `coverage/` ([新] Jest覆盖率报告目录), `*.zip`, `*.log`。
        *   Next.js特定忽略: `apps/jcs-endpoint-nextjs/.next/`。
        *   特别忽略Next.js应用的内部Git目录：`apps/jcs-endpoint-nextjs/.git/`。

*   **Next.js应用的内部Git仓库 (`apps/jcs-endpoint-nextjs/.git`)**:
    *   **目的**: 仅用于Azure App Service的“本地Git部署”功能。
    *   **工作流程**: 在主Monorepo中完成开发和提交。部署时，进入 `apps/jcs-endpoint-nextjs` 目录，在此内部Git仓库中提交变更，并推送到Azure。

## 4. 文档策略 (`docs/` 目录)

`docs/` 目录是项目知识的核心，是开发团队和AI编程助手的单一事实来源 (SSoT)。

*   **语言**: 所有Markdown文件内容使用 **简体中文**。
*   **文件名和目录名**: 使用 **英文**，遵循 `kebab-case`。
*   **内容来源**: 源于系统分析、架构设计，并随项目进展持续更新。是生成日文交付文档的主要信息源。
*   **后端服务文档**: 所有后端Azure Functions的详细设计文档，无论其最终部署到哪个Function App，在逻辑上都统一存放在 `docs/components/backend-services-functions/` 目录下进行管理，以便于从业务逻辑角度进行查阅。

## 5. 部署与测试工作流详解 (Azure Functions)

### 5.1. 概述

采用**本地为源，云端为目标**的策略。我们本地`apps/`目录下的两个Function App项目是权威代码源，部署过程就是将本地编译好的、通过测试的代码发布到Azure上预先创建好的对应Function App服务实例中。

### 5.2. 步骤1: 基础设施准备 (一次性)

首次部署或创建新环境时，需要在Azure上准备好接收代码的“容器”。

1.  **创建两个Function App资源**:
    *   **标准应用**: `jcs-backend-services-standard` 的宿主。可使用**Consumption Plan (消费计划)**。创建后，务必在其 **配置 -> 函数运行时设置** 中将**超时时间**设置为 **5分钟**。
    *   **长时运行应用**: `jcs-backend-services-long-running` 的宿主。**必须使用 Premium Plan 或更高级的计划**，因为消费计划无法支持超过10分钟的超时。创建后，务必将其**超时时间**设置为 **30分钟**。

2.  **命名规范**: 本地目录名 (`jcs-backend-services-standard`) 与Azure资源名 (`jcs-func-std-prod-japaneast-001`) **不必相同**。Azure资源名需全球唯一，推荐采用结构化命名法，如：`<项目简称>-<资源类型>-<环境>-<区域简称>-<序号>`。

### 5.3. 步骤2: 本地开发与单元测试 (重复性)

这是开发过程中的核心循环，旨在保证代码质量。

1.  **编写/修改代码**: 在对应的函数目录中修改 `*.ts` 源代码文件。
2.  **编写/更新测试**: 同时，在同一个目录中编写或更新 `*.test.ts` 文件，确保测试用例覆盖了新的逻辑或变更。
3.  **运行单元测试**:
    *   进入目标Function App的目录，例如 `cd apps/jcs-backend-services-standard`。
    *   运行 `npm test` 命令。
4.  **确认结果**: 确保所有测试用例都通过。在所有测试通过之前，不应进行部署。

### 5.4. 步骤3: 代码部署 (重复性)

当本地代码开发完成并通过所有单元测试后，执行部署。

1.  **安装工具**: 确保本地已安装 Azure CLI (`az`) 和 Azure Functions Core Tools (`func`)。
2.  **登录Azure**: 在终端运行 `az login`。
3.  **执行部署脚本**: 运行项目 `scripts/` 目录下的部署脚本（见第6节示例）。该脚本会自动为两个Function App分别执行编译和发布操作。

## 6. 本地脚本 (`scripts/` 与 NPM Scripts)

### 6.1. Azure Functions 部署脚本

为了统一和简化部署流程，项目提供一个部署脚本。

**`scripts/deploy-functions.sh` 示例:**
```bash
#!/bin/bash
# 部署 JCS Backend Azure Functions 的辅助脚本

# --- 配置 ---
# 请替换为您在Azure上创建的Function App的实际名称
STANDARD_APP_NAME="jcs-func-std-prod-japaneast-001"
LONG_RUNNING_APP_NAME="jcs-func-long-prod-japaneast-001"

# --- 脚本主体 ---
set -e # 任何命令失败则立即退出

# 获取脚本所在目录，以便计算相对路径
SCRIPT_DIR=$(cd -- "$(dirname -- "${BASH_SOURCE}")" &> /dev/null && pwd)
APPS_DIR=$(realpath "$SCRIPT_DIR/../apps")

echo "====================================================="
echo "Deploying Standard Functions App (5-min timeout)"
echo "====================================================="
cd "$APPS_DIR/jcs-backend-services-standard"
echo "Current directory: $(pwd)"

echo "--> Installing dependencies and building project..."
npm install && npm run build

echo "--> Publishing to Azure Function App: $STANDARD_APP_NAME"
# 使用 --no-build 标志，因为我们已经手动构建过了，这能提供更好的控制
func azure functionapp publish "$STANDARD_APP_NAME" --no-build

echo ""
echo "====================================================="
echo "Deploying Long-Running Functions App (30-min timeout)"
echo "====================================================="
cd "$APPS_DIR/jcs-backend-services-long-running"
echo "Current directory: $(pwd)"

echo "--> Installing dependencies and building project..."
npm install && npm run build

echo "--> Publishing to Azure Function App: $LONG_RUNNING_APP_NAME"
func azure functionapp publish "$LONG_RUNNING_APP_NAME" --no-build

echo ""
echo "====================================================="
echo "Deployment completed successfully!"
echo "====================================================="
```

### 6.2. Azure Functions 测试脚本 (NPM Scripts)

单元测试的执行由每个Function App项目内部的`package.json`文件定义。

*   **`npm test`**: 运行一次所有测试用例。
*   **`npm run test:watch`**: 启动Jest的观察者模式，在文件发生变化时自动重新运行相关测试。
*   **`npm run test:coverage`**: 运行测试并生成代码覆盖率报告，输出到`coverage/`目录。

### 6.3. Azure Automation Runbooks 部署脚本

`deploy-runbooks-locally.ps1`: 包含连接到目标Azure Automation Account，并将 `automation/runbooks/` 下的资源发布或更新的PowerShell脚本。

## 7. AI编程助手的使用

*   **主要输入源**: `docs/` 目录下的所有Markdown文档，`prisma/schema.prisma`，以及 `apps/` 目录下各应用的源代码（包括 `*.ts` 和 `*.test.ts` 文件）。
*   **指令清晰化**:
    *   **当指令涉及后端服务时，请明确指出Function的具体名称**，例如：“请为 `TaskExecuteFunc` 函数 (`apps/jcs-backend-services-standard/TaskExecuteFunc/TaskExecuteFunc.ts`) 增加...逻辑，并为其在 `TaskExecuteFunc.test.ts` 中补充相应的单元测试用例。”
    *   AI助手能够根据函数名和新的目录结构，精确地定位和修改源代码与测试代码。

## 8. 交付文档的生成/更新

此流程保持不变。当需要生成或更新日文的《機能仕様書》或《詳細設計書》时：
1.  **以 `docs/` 目录下的中文Markdown文档作为最新、最权威的信息源 (SSoT)。**
2.  手动或借助AI辅助，将所需内容从Markdown文档中提取、转换并填充到Word/Excel模板中。
3.  所有AI辅助生成的日文内容，必须经过人工的**彻底审查和校对**。
