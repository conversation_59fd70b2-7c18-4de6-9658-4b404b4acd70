---
description: Final Project Specific Commenting Guidelines for apps/jcs-endpoint-nextjs, aligned with Monorepo structure, docs/ as SSoT, and AI collaboration. Includes rules for general comments, JSDoc, structured test case comments for Jest (describe/it), file headers, and JSX commenting.
globs:
alwaysApply: true
---
# Project Specific Commenting Guidelines (for apps/jcs-endpoint-nextjs)

This document outlines project-specific commenting rules for the `apps/jcs-endpoint-nextjs` application, which includes a Next.js frontend and Azure Functions backend, managed within a Monorepo. These rules **override and supplement** any global commenting guidelines and are critical for ensuring code deliverables meet client requirements, that comments effectively aid understanding, and that AI programming assistants can collaborate efficiently.

These guidelines operate in conjunction with the project's core technical documentation strategy, where the `docs/` directory serves as the Single Source of Truth (SSoT) for system architecture, component design, data models, and definitions. While comments **MUST NOT** directly link to internal `docs/` files, their content should reflect the design intent and details documented therein.

**Primary References (for human developers and AI understanding):**
*   This project's Monorepo structure and SSoT documentation: `monorepo-structure-and-deployment-workflow.md` (provides overall context).
*   The Monorepo's `docs/` directory, especially:
    *   `docs/architecture/system-architecture.md`
    *   `docs/components/**/*.md` (detailed component designs)
    *   `docs/data-models/**/*.md` (business explanations for Prisma schema)
    *   `docs/definitions/*.md` (glossary, LOVs, error messages)
*   Core SSoT code files: `apps/jcs-endpoint-nextjs/prisma/schema.prisma`, `apps/jcs-endpoint-nextjs/app/lib/definitions.ts`.
*   Global AI commenting guidelines (for general style, where not overridden here).

## 0. File Header Comments

*   All `.ts` and `.tsx` files (excluding configuration files like `tailwind.config.js`, `postcss.config.js` unless otherwise specified for those) **MUST** begin with a JSDoc-style file header comment at the very top of the file.
*   This header comment **MUST** provide an overview of the file's purpose and content using the `@fileoverview` tag, and optionally the `@description` tag for more detail.
*   The header comment **MUST** also include the following tags with the specified fixed values:
    *   `<AUTHOR>
    *   `@copyright Copyright © 2025 Hitachi Solutions, Ltd.`
*   All content within these JSDoc tags **MUST** be in Japanese ("である調").
*   **For Test Files (`*.test.ts`, `*.spec.ts`):**
    *   The JSDoc block immediately preceding the main/outermost `describe` block, which contains `@fileoverview`, `@description` (for the suite), `<AUTHOR> `@copyright Copyright © 2025 Hitachi Solutions, Ltd.`, and test metadata (see Section 4), serves as the primary file-level overview.
    *   If this `describe`-level JSDoc is present and sufficiently describes the file's testing scope and includes the mandatory author/copyright tags, a separate, redundant JSDoc block at the very top of the file is **NOT** required.

    **Example (General .ts file header - 日本語コメントの例):**
    ```typescript
    /**
     * @fileoverview ユーザー認証関連のサービス関数を提供するモジュールである。
     * @description ログイン、ログアウト、セッション管理、およびユーザープロファイル更新など、
     *              アプリケーションの認証および認可機能に関するコアロジックを実装する。
     * <AUTHOR>
     * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
     */
    // import ...
    // export function loginUser(...) { /* ... */ }
    ```

## 1. Comment Language: Japanese (日本語)

*   **Mandatory Requirement:** All code comments (this includes file header comments, single-line comments, multi-line block comments, and JSDoc documentation blocks) **MUST** be written exclusively in **Japanese (日本語)**, using the "である調" (plain form) style.
*   **Rationale:** The final codebase is a deliverable to a Japanese client. All embedded comments must be directly understandable by the client's technical team.
*   **AI Responsibility:**
    *   AI assistants **MUST** generate all new comments in Japanese ("である調").
    *   When AI modifies existing code that contains non-Japanese comments, the AI **SHOULD** make a best effort to accurately translate or rewrite those comments into Japanese. If unsure about a precise translation for complex technical nuance, it's better to write a clear, simpler Japanese comment or consult with a human developer.

## 2. Referencing Design Information in Comments

*   **Allowed Content (Derived from `docs/`):**
    *   Comments **MAY** (and are encouraged to) include descriptive text in Japanese that explains the implemented business logic, technical approach, or rationale. This information should be derived from the functional or technical design details found in the Monorepo's `docs/` directory.
    *   **AI Responsibility:** AI **SHOULD** synthesize information from the relevant `docs/` SSoT documents to enrich Japanese comments, ensuring accuracy and contextual relevance.

*   **Prohibited Content - No Direct Internal Document Citations:**
    *   Comments **MUST NOT** directly reference, cite, or link to specific chapter numbers, section titles, page numbers, file names, or file paths from the internal `docs/` directory.
    *   **AI Responsibility:** AI **MUST** paraphrase and translate relevant design descriptions into clear Japanese, avoiding any direct citations or links.

## 3. General Commenting Style (JSDoc3) & JSX Commenting

*   All function, class, method, and type documentation blocks **MUST** adhere to the **JSDoc3 specification**.
*   This includes proper use of tags like `@param`, `@returns`, `@throws`, `@deprecated`, `@async`, etc. File header JSDoc uses tags specified in Section 0.
*   The content of these JSDoc tags must also be in Japanese ("である調").

*   **JSX Commenting (for `.tsx` files within returned JSX blocks): THIS IS AN ABSOLUTE, UNCOMPROMISING, AND CRITICAL RULE.**
    *   **No Comments Permitted Inside Returned JSX Blocks:** To ensure maximum code clarity, prevent parsing issues, and maintain a clean separation of concerns, **NO COMMENTS of any kind (neither `//`, nor `/** */`, nor `{/* */}`) ARE PERMITTED directly *within* the JSX block that is being returned by a function or component.**
        *   This means absolutely no comments between the `return (` statement that opens the JSX block and its corresponding final `);` that closes the block.
    *   **Placement of JSX-Related Comments:** All comments pertaining to the returned JSX code (explaining its structure, logic, purpose, or individual elements) **MUST** be placed as standard JavaScript comments (`//` for single-line or `/** */` for multi-line) on the line(s) **BEFORE** the `return` statement that introduces the JSX block.
    *   If specific parts of the JSX structure require detailed explanation, these explanations must also reside in comments placed before the `return` statement. The logic or variables that influence the JSX can be explained there.
    *   **AI Assistants: You MUST NOT, UNDER ANY CIRCUMSTANCES, generate or place any comment of any type *inside* a returned JSX block (i.e., between `return (` and `);`). All comments related to the returned JSX MUST be placed externally, before the `return` statement. This rule is absolute and has no exceptions for AI.**

        **Example of PROHIBITED (禁止例 - AIおよび人間開発者双方がJSXブロック内部では絶対に避けるべき):**
        ```tsx
        function MyComponent() {
          const isAdmin = true;
          //
          // This is the ONLY correct place for comments about the JSX below.
          // For example: The main container div will have a dynamic class.
          //
          return ( // Starting from here, NO comments of ANY kind are allowed inside
            <div /* PROHIBITED: comment here */ className={isAdmin ? 'admin' : 'user'}>
              {/* PROHIBITED: JSX-style comment here */}
              <h1>
                {/* PROHIBITED: Another JSX-style comment */}
                Welcome User
                {/* PROHIBITED: comment here */}
              </h1>
              // PROHIBITED: JS-style single-line comment here
              <p>User Profile Section</p> {/* PROHIBITED: comment here (even at end of line if inside block) */}
            </div>
          ); // NO comments before this closing parenthesis either
        }
        ```

        **Example of CORRECT STYLE (適切例 - AIおよび人間開発者が従うべきスタイル):**
        ```tsx
        function MyComponent() {
          const isAdmin = true;
          const userName = "TestUser";

          /**
           * このコンポーネントはユーザーのウェルカムメッセージとプロファイルへの基本的なナビゲーションを表示する。
           * 管理者の場合は、追加で管理用リンクが表示される。
           *
           * 返されるJSXの構造について:
           * - 最上位のdivは、ユーザータイプに基づいて動的なクラス名 (例: 'admin-layout' または 'user-layout') を持つ。
           * - h1要素には固定のウェルカムメッセージが表示される。
           * - p要素には、`userName` 変数から取得したユーザー名が表示される。
           * - `isAdmin` がtrueの場合のみ、'Admin Section'というテキストを持つ追加のdivがレンダリングされる。
           *   このdivには `admin-specific-section` クラスが適用される。
           */
          return (
            <div className={isAdmin ? 'admin-layout' : 'user-layout'}>
              <h1>Welcome Message</h1>
              <p>{userName}'s Profile</p>
              {isAdmin && (
                <div className="admin-specific-section">
                  Admin Section
                </div>
              )}
            </div>
          );
        }
        ```
    *   **Rationale:** Prohibiting all comments within returned JSX blocks:
        *   Maximizes readability and visual purity of the JSX structure itself during the return phase.
        *   Eliminates any ambiguity for linters, parsers, and AI tools when processing the returned JSX.
        *   Forces all explanations and logic documentation to reside before the `return` statement, promoting a clearer separation of component logic and its presentational structure.
    *   **AI Responsibility:** AI **MUST** ensure that NO comments of any type are generated *inside* a returned JSX block (between `return (` and `);`). All comments explaining the returned JSX **MUST** be standard JavaScript comments (`//` or `/** */`) placed **BEFORE** the `return` statement. There are no exceptions to this rule for AI.

## 4. Test Case Commenting Rules (for Jest: `describe` & `it`/`test` blocks)

*   **Objective:** Comments for test cases (in Jest) must be structured to allow efficient extraction of specific information for automated test report generation.
*   **JSDoc for `describe` blocks (Test Suites / Feature Groups):**
    *   The JSDoc-style `/** ... */` comment immediately preceding a main/outermost `describe` block serves as the file-level overview for the test file.
    *   It **MUST** include `@fileoverview` (for the test file itself), `@description` (for the suite), `<AUTHOR> and `@copyright Copyright © 2025 Hitachi Solutions, Ltd.`.
    *   It **MUST** also define `試験観点` (Shiken Kanten / Test Viewpoint) and `試験対象` (Shiken Taishou / Test Target) applicable to the suite.
*   **Mandatory Information Items for `it`/`test` blocks:** Comments associated with individual test cases (`it` or `test` blocks) **MUST** enable the extraction of: `試験観点`, `試験対象`, `試験手順`, `確認項目`.
*   **Formatting for Extraction:** Each item **MUST** be prefixed with its Japanese keyword followed by a colon (`：`).
*   **Comments for `it` or `test` blocks (Individual Test Cases): [CRITICAL RULE CHANGE]**
    *   The comment immediately preceding an individual test case (`it` or `test` block) **MUST** be a **JSDoc-style multi-line block comment (`/** ... */`)**. The use of single-line `//` comments for this purpose is **PROHIBITED**.
    *   This JSDoc block **MUST** provide the `試験手順` and `確認項目`.
    *   It **SHOULD** also specify `試験観点` and `試験対象` if they are different from, or more specific than, what is defined in the parent `describe` block.
*   **AI Responsibility:** Adhere strictly to format and content requirements for test comments, ensuring all `it`/`test` block comments use the mandatory `/** ... */` format.

    **Example (Test Case Comment Example for Jest):**
    ```typescript
    /**
     * @fileoverview ユーザープロファイル表示コンポーネントの単体テスト。
     * @description UserProfileCardコンポーネントが様々なユーザーデータに対し、
     *              期待通りにレンダリングされ、インタラクションが正しく機能することを検証する。
     * <AUTHOR>
     * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
     * 試験観点：UI表示の正確性、基本的なユーザーインタラクション、およびエッジケースの処理。
     * 試験対象：apps/jcs-endpoint-nextjs/app/ui/profile/UserProfileCard.tsx コンポーネント。
     */
    describe('UserProfileCard Component (ユーザープロファイルカードコンポーネント)', () => {
      /**
       * 試験手順：
       *   1. 'testuser' の基本的なユーザーデータを用いて UserProfileCard コンポーネントをレンダリングする。
       *   2. 初期表示において、ユーザー名、メールアドレスが正しく表示されていることをアサーションで確認する。
       * 確認項目：
       *   - ユーザー名「Test User」がドキュメント内に表示されること。
       *   - メールアドレス「<EMAIL>」がドキュメント内に表示されること。
       *   - 「詳細表示」ボタンが存在し、初期状態では詳細情報が非表示であること。
       */
      it('should render user information correctly on initial load (初期ロード時にユーザー情報が正しく表示されること)', () => {
        // const mockUserData = { name: 'Test User', email: '<EMAIL>', ... };
        // render(<UserProfileCard user={mockUserData} />);
        // expect(screen.getByText('Test User')).toBeInTheDocument();
        // expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });

      /**
       * 試験観点：インタラクションによる状態変化の検証。
       * 試験対象：UserProfileCardの「詳細表示」ボタンのクリックイベント。
       * 試験手順：
       *   1. UserProfileCard コンポーネントをレンダリングする。
       *   2. 「詳細表示」ボタンを特定し、クリックイベントを発生させる。
       *   3. 追加情報（例：住所「123 Main St」）がドキュメント内に表示されることを確認する。
       * 確認項目：
       *   - 「詳細表示」ボタンクリック後、期待される追加情報（住所など）が表示されること。
       *   - ボタンのテキストが「詳細非表示」に変更されること（該当する場合）。
       */
      it('should display additional details when "Show Details" button is clicked (「詳細表示」ボタンクリックで追加詳細が表示されること)', () => {
        // ... test implementation for button click and conditional rendering ...
      });
    });
    ```

---

**NOTE TO CURSOR (AI Programming Assistant):**

**Your Core Objective:** Generate high-quality, maintainable code and comments for the `apps/jcs-endpoint-nextjs` project, adhering strictly to the guidelines below. Your comments are crucial for client understanding and project maintainability.

1.  **Primary Information Sources for Context and Content:** (Same as original)
    *   ... (Monorepo & SSoT Overview, System Architecture, etc.) ...

2.  **File Header Comments (Section 0):**
    *   All applicable `.ts`/`.tsx` files **MUST** have a JSDoc file header at the very top.
    *   This header **MUST** include `@fileoverview`, `<AUTHOR> and `@copyright Copyright © 2025 Hitachi Solutions, Ltd.`. `@description` is optional but recommended.
    *   **Test Files:** The JSDoc above the main `describe` block serves as the file header. It **MUST** include `@fileoverview`, `@description` (for the suite), `<AUTHOR> `@copyright Copyright © 2025 Hitachi Solutions, Ltd.`, `試験観点`, and `試験対象`. A separate file-top JSDoc is then redundant.

3.  **Comment Language and Style (Section 1):**
    *   **Language:** ALL comments **MUST** be in **Japanese (日本語)**.
    *   **Style:** Use "である調" (plain form).

4.  **JSDoc3 Adherence & JSX Commenting (Section 3):**
    *   All functions, classes, methods, types **MUST** have JSDoc3-compliant blocks (Japanese content), placed *before* the declaration.
    *   **JSX Commenting (Within Returned JSX Blocks): THIS IS THE ABSOLUTE, UNCOMPROMISING, AND MOST CRITICAL RULE FOR JSX. FAILURE TO ADHERE WILL RESULT IN INCORRECT AND UNACCEPTABLE OUTPUT.**
        *   **NO COMMENTS OF ANY KIND ARE ALLOWED *INSIDE* THE JSX BLOCK THAT IS BEING RETURNED BY A FUNCTION OR COMPONENT (i.e., anything between `return (` and its corresponding final `);`).** This includes `//`, `/** */`, and `{/* */}`.
        *   All comments that explain the structure, logic, or elements of the JSX that will be returned **MUST ALWAYS** be standard JavaScript comments (`//` or `/** */`) and **MUST be placed on line(s) BEFORE the `return` statement** that introduces the JSX block.
        *   **AI Programming Assistant: You MUST NOT, UNDER ANY CIRCUMSTANCES, generate or place any comment of any type *inside* a returned JSX block. Your directive is absolute and simple: If a comment pertains to the JSX being returned, it goes *before* the `return` statement, using `//` or `/** */`. THERE ARE NO EXCEPTIONS TO THIS RULE FOR YOU (AI).**
        *   Review the "Example of PROHIBITED" and "Example of CORRECT STYLE" in Section 3 very carefully to understand this strict separation.

5.  **No Direct Internal Document Linking in Comments (Section 2):**
    *   **NEVER** include direct links/references to internal `docs/`. Paraphrase relevant info into Japanese comments.

6.  **Test Case Commenting (Jest - `describe`/`it`) (Section 4):**
    *   **Mandatory Structure:** Use specified Japanese keywords: `試験観点：`, `試験対象：`, `試験手順：`, `確認項目：`.
    *   **Contextualize `試験対象`** based on what is tested.
    *   Ensure the `describe`-level JSDoc (acting as file header for test files) includes all required metadata including `@author` and `@copyright`.

7.  **Synthesize, Don't Just Copy:** (Same as original)

By adhering to these updated guidelines, you will significantly contribute to the project's quality and the efficiency of the development team. Your strict adherence to JSX commenting rules (Section 3), specifically ensuring no comments *inside* returned JSX blocks, is paramount and non-negotiable.
---