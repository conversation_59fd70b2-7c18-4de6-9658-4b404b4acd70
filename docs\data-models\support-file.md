# 数据模型: 支持信息 (SupportFile)

*   **表名 (逻辑名)**: `SupportFile`
*   **对应UI界面**: 「サポート情報一覧」 (Support Information List)
*   **主要用途**: 存储提供给用户的支持信息，例如障害回避/预防信息、产品更新通告、重要提醒等。用户可以在门户上查看这些信息，并可能下载相关的附件。

## 1. 字段定义

| 字段名 (Prisma/英文) | 数据类型 (SQL参考) | PK | FK | UQ | Nullable | Default          | 描述 (中文)                                                                                                                                   |
| :------------------- | :----------------- | :--- | :--- | :--- | :------- | :--------------- | :-------------------------------------------------------------------------------------------------------------------------------------------- |
| `id`                 | VARCHAR(36)        | ●    |      |      |          | `cuid()`         | 主键。CUID格式，由系统自动生成。                                                                                                                |
| `serialNo`           | INT                |      |      | ●    |          | `autoincrement()`| 支持信息的唯一序列号或通番。在数据库中是自增的整数值，用于唯一标识和排序。                                                                                       |
| `title`              | NVARCHAR(255)      |      |      |      |          |                  | 支持信息的标题。                                                                                                                                |
| `productName`        | NVARCHAR(255)      |      |      |      |          |                  | 此支持信息相关的产品名称或影响范围。例如："Product A", "All Products"。对应原「機能仕様書」中的`productAffected`。                                      |
| `importance`         | VARCHAR(50)        |      |      |      |          |                  | 支持信息的重要性级别。例如："AAA", "HIGH", "MEDIUM", "LOW"。其值可能来自`Lov`表。对应原「機能仕様書」中的`importanceLevel`。                                  |
| `publishedAt`        | VARCHAR(XX)        |      |      |      |          |                  | 支持信息首次公开发布的日期/时间。**注意：Prisma Schema中此字段类型为String，实际存储格式和排序需关注。** 对应原「機能仕様書」中的`publishDate`。                               |
| `updatedAt`          | VARCHAR(XX)        |      |      |      |          |                  | 支持信息内容最后更新的日期/时间。**注意：Prisma Schema中此字段类型为String，实际存储格式和排序需关注。** 对应原「機能仕様書」中的`lastUpdatedDate`。                                |
| `fileName`           | VARCHAR(255)       |      |      |      |          |                  | 如果此支持信息有关联的附件（例如详细文档、补丁文件等），则此字段存储该附件的文件名。如果无附件，则可能为空或特定占位符。                                                           |

*PK: Primary Key, FK: Foreign Key, UQ: Unique Key*
*XX表示具体长度，取决于实际的数据库Schema定义。*

## 2. 关系

*   **对 `PlanSupport` (`planSupports`)**: 一对多关系 (`PlanSupport[]`)。一个支持信息记录（由`serialNo`唯一确定）可以通过`PlanSupport`表关联到多个契约计划，表示该支持信息适用于这些计划。

## 3. 唯一约束

*   `UNIQUE KEY (serialNo)` (Prisma Schema中已定义 `@unique @default(autoincrement())`)，确保支持信息的序列号在系统中是唯一的，并且是自增的。

## 4. Azure Blob Storage 路径约定 (推测)

*   支持信息关联的附件文件（如果存在）在Azure Blob Storage中的实际存储路径，通常遵循预定义规则动态构建，例如基于 `serialNo` 或 `fileName`。
*   表中不直接存储完整的Blob路径。

## 5. 索引 (示例)

*   `PRIMARY KEY (id)`
*   `UNIQUE KEY UQ_SupportFile_SerialNo (serialNo)` (业务唯一键，自增)
*   `INDEX idx_supportfile_title (title)`
*   `INDEX idx_supportfile_productname (productName)`
*   `INDEX idx_supportfile_importance (importance)`
*   `INDEX idx_supportfile_publishedat (publishedAt DESC)` (注意：基于字符串的排序)
*   `INDEX idx_supportfile_updatedat (updatedAt DESC)` (注意：基于字符串的排序)