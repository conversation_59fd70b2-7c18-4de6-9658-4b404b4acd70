/**
 * Playwright 全局设置脚本
 * 在所有测试开始前执行一次
 */
import { FullConfig } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';
import { initializeLovSeedData, validateLovData } from './support/lov-data.helper';

/**
 * 全局设置函数
 * @param config Playwright 配置
 */
async function globalSetup(config: FullConfig) {
  console.log('🚀 开始 E2E 测试环境全局设置...');

  // 1. 确保测试结果目录存在
  const testResultsDir = path.join(__dirname, '..', 'test-results');
  const htmlReportDir = path.join(testResultsDir, 'html-report');
  const testOutputDir = path.join(testResultsDir, 'test-output');
  const harDir = path.join(testResultsDir, 'har');

  ensureDirectoryExists(testResultsDir);
  ensureDirectoryExists(htmlReportDir);
  ensureDirectoryExists(testOutputDir);
  ensureDirectoryExists(harDir);

  console.log('📁 测试目录结构准备完成');

  // 2. 初始化 LOV 种子数据
  try {
    await initializeLovSeedData();

    // 验证 LOV 数据是否正确初始化
    const isValid = await validateLovData();
    if (!isValid) {
      throw new Error('LOV 数据验证失败');
    }

    console.log('🌱 LOV 种子数据初始化并验证完成');
  } catch (error) {
    console.error('❌ LOV 种子数据初始化失败:', error);
    throw error;
  }

  console.log('🧪 测试环境准备就绪');
  console.log(`📊 测试报告将保存在: ${htmlReportDir}`);
  console.log(`🔍 测试输出将保存在: ${testOutputDir}`);
}

/**
 * 确保目录存在，如果不存在则创建
 * @param dirPath 目录路径
 */
function ensureDirectoryExists(dirPath: string) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

export default globalSetup;