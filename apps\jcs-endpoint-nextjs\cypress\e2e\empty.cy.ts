describe("空データ表示のテスト", () => {
  describe("ログイン前の場合", () => {
    it("データがない場合、ログイン画面での表示", () => {
      cy.visit("/login");
      cy.get("footer .font-semibold").contains("お知らせ");
      cy.contains("お知らせはありません。");
      cy.get(".whitespace-pre-line").should("have.length", 0);
    });
  });

  describe("ログイン済みの場合", () => {
    describe("データがない場合", () => {
      const end2023jpCredentials = {
        userId: "end2023jp.hanako.ab",
        password: "changeit!@#",
        licenseId: "end2023jp",
      };

      beforeEach(() => {
        cy.visit("/login");
        cy.get("#userId").type(end2023jpCredentials.userId);
        cy.get("#password").type(end2023jpCredentials.password);
        cy.get("button").click();
        cy.title().should("eq", "サーバ一覧");
      });

      it("お知らせダイアログでの表示", () => {
        cy.get("button").contains("お知らせ").click();
        cy.get("#notification-modal .font-semibold").contains("お知らせ");
        cy.contains("お知らせはありません。");
        cy.get(".whitespace-pre-line").should("have.length", 0);
      });

      it("サーバ一覧画面での表示", () => {
        cy.get("nav").should("contain", "サーバ一覧");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9").should(
          "have.length",
          0,
        );
        cy.get("tbody tr").should("have.length", 1);
      });

      it("操作ログ一覧画面での表示", () => {
        cy.visit("/dashboard/oplogs");
        cy.get("nav").should("contain", "操作ログ一覧");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9").should(
          "have.length",
          0,
        );
        cy.get("tbody tr").should("have.length", 1);
      });

      it("製品媒体一覧画面での表示", () => {
        cy.visit("/dashboard/medias");
        cy.get("nav").should("contain", "製品媒体一覧");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9").should(
          "have.length",
          0,
        );
        cy.get("tbody tr").should("have.length", 1);
      });

      it("マニュアル一覧画面での表示", () => {
        cy.visit("/dashboard/manuals");
        cy.get("nav").should("contain", "マニュアル一覧");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9").should(
          "have.length",
          0,
        );
        cy.get("tbody tr").should("have.length", 1);
      });

      it("提供ファイル一覧画面での表示", () => {
        cy.visit("/dashboard/provided-files");
        cy.get("nav").should("contain", "提供ファイル一覧");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9").should(
          "have.length",
          0,
        );
        cy.get("tbody tr").should("have.length", 1);
      });

      it("サポート情報一覧画面での表示", () => {
        cy.visit("/dashboard/support-files");
        cy.get("nav").should("contain", "サポート情報一覧");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9").should(
          "have.length",
          0,
        );
        cy.get("tbody tr").should("have.length", 1);
      });
    });

    describe("フィルタリングの結果が空の場合", () => {
      const validCredentials = {
        userId: "hitachi.taro.aa",
        password: "changeit!@#",
      };
      const filterEmpty = "filterEmpty";

      beforeEach(() => {
        cy.visit("/login");
        cy.get("#userId").type(validCredentials.userId);
        cy.get("#password").type(validCredentials.password);
        cy.get("button").click();
        cy.title().should("eq", "サーバ一覧");
      });

      it("サーバ一覧画面での表示", () => {
        cy.get("nav").should("contain", "サーバ一覧");
        cy.get("tbody tr").should("have.length", 10);
        cy.get(".bg-white.h-full input").type(filterEmpty);
        cy.get(".bg-white.h-full button").first().click();
        cy.get("tbody tr").should("have.length", 1);
      });

      it("操作ログ一覧画面での表示", () => {
        cy.visit("/dashboard/oplogs");
        cy.get("nav").should("contain", "操作ログ一覧");
        cy.get("tbody tr").should("have.length", 10);
        cy.get(".bg-white.h-full input").type(filterEmpty);
        cy.get(".bg-white.h-full button").first().click();
        cy.get("tbody tr").should("have.length", 1);
      });

      it("製品媒体一覧画面での表示", () => {
        cy.visit("/dashboard/medias");
        cy.get("nav").should("contain", "製品媒体一覧");
        cy.get("tbody tr").should("have.length", 10);
        cy.get(".bg-white.h-full input").type(filterEmpty);
        cy.get(".bg-white.h-full button").first().click();
        cy.get("tbody tr").should("have.length", 1);
      });

      it("マニュアル一覧画面での表示", () => {
        cy.visit("/dashboard/manuals");
        cy.get("nav").should("contain", "マニュアル一覧");
        cy.get("tbody tr").should("have.length", 10);
        cy.get(".bg-white.h-full input").type(filterEmpty);
        cy.get(".bg-white.h-full button").first().click();
        cy.get("tbody tr").should("have.length", 1);
      });

      it("提供ファイル一覧画面での表示", () => {
        cy.visit("/dashboard/provided-files");
        cy.get("nav").should("contain", "提供ファイル一覧");
        cy.get("tbody tr").should("have.length", 10);
        cy.get(".bg-white.h-full input").type(filterEmpty);
        cy.get(".bg-white.h-full button").first().click();
        cy.get("tbody tr").should("have.length", 1);
      });

      it("サポート情報一覧画面での表示", () => {
        cy.visit("/dashboard/support-files");
        cy.get("nav").should("contain", "サポート情報一覧");
        cy.get("tbody tr").should("have.length", 10);
        cy.get(".bg-white.h-full input").type(filterEmpty);
        cy.get(".bg-white.h-full button").first().click();
        cy.get("tbody tr").should("have.length", 1);
      });
    });
  });
});
