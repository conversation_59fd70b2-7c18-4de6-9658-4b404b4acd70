/**
 * @fileoverview アプリケーション全体で使用する定数を一元管理するモジュール
 * @description
 * アプリケーション全体で使用する定数を一元管理するモジュール。
 * タスク状態・タイプ・設定・Runbook名・エラーコード等、全機能横断で参照される値を集約。
 * 定数の一元管理により、値の変更・追加・参照の一貫性と保守性を担保。
 * ビジネスロジックや外部連携で必要な値を明示的に定義し、マジックナンバー・ハードコーディングを排除。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */
// アプリケーション全体で使用する定数を一元管理する名前空間
export namespace AppConstants {
  /**
   * タスクの全ステータスを定義します。
   */
  export enum TaskStatus {
    Queued = "TASK_STATUS.QUEUED",
    RunbookSubmitted = "TASK_STATUS.RUNBOOK_SUBMITTED",
    RunbookProcessing = "TASK_STATUS.RUNBOOK_PROCESSING",
    CompletedSuccess = "TASK_STATUS.COMPLETED_SUCCESS",
    CompletedError = "TASK_STATUS.COMPLETED_ERROR",
    Cancelled = "TASK_STATUS.CANCELLED",
    PendingCancellation = "TASK_STATUS.PENDING_CANCELLATION",
  }

  /**
   * タスクの全タイプを定義します。
   */
  export enum TaskType {
    MgmtItemImport = "TASK_TYPE.MGMT_ITEM_IMPORT",
    MgmtItemExport = "TASK_TYPE.MGMT_ITEM_EXPORT",
    OpLogExport = "TASK_TYPE.OPLOG_EXPORT",
  }

  /**
   * タスク設定関連の定数を定義します。
   */
  export enum TaskConfig {
    maxRetentionCount = "TASK_CONFIG.MAX_RETENTION_COUNT",
  }

  /**
   * エラーコード定数（keyもvalueもEMETxxxxのみ）
   */
  export const ERROR_CODES = {
    EMET0001: "EMET0001",
    EMET0002: "EMET0002",
    EMET0003: "EMET0003",
    EMET0004: "EMET0004",
    EMET0005: "EMET0005",
    EMET0006: "EMET0006",
    EMET0007: "EMET0007",
    EMET0008: "EMET0008",
    EMET0009: "EMET0009",
    EMET0010: "EMET0010",
    EMET0011: "EMET0011",
    EMET0012: "EMET0012",
    EMET0013: "EMET0013",
    EMET0014: "EMET0014",
    EMET0015: "EMET0015",
  } as const;

  /**
   * タスク実行・補償処理で使用するエラーメッセージテンプレート定数である。
   * 各メッセージID（EMETxxxx）は機能仕様書に準拠し、
   * DBへのエラー記録やユーザー向け表示に利用する。
   */
  export const TASK_ERROR_MESSAGE = {
    EMET0001: "{0}に対するタスクを実行中のため実行できません。<br/>実行中のタスクが完了してから再度実行してください。",
    EMET0002: "タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0002)",
    EMET0003: "タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0003)",
    EMET0004: "ユーザーによってタスクが中止されました。",
    EMET0005: "タスクの完了が確認できないため、システムによってタスクを中止しました。タスクが操作ログのエクスポートの場合、エクスポートするログの期間を短くして再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMET0005)",
    EMET0006: "タスクの中止がタイムアウトしました。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMET0006)",
    EMET0007: "タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0007)",
    EMET0008: "タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0008)",
    EMET0009: "タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009)",
    EMET0010: "システムによりタスクの実行を中止しました。タスクの実行中にサービスのメンテナンスが開始された可能性があります。サービスのメンテナンス終了後に再度実行してください。",
    EMET0011: "タスクの実行に失敗しました。<br/>エラー詳細：<br/>{0}",
    EMET0012: "タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0012)",
    EMET0013: "タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0013)",
    EMET0014: "操作ログ一覧画面で操作ログファイルをダウンロードしてください。ログ名は{タスク名}_{連番}です。",
    EMET0015: "エクスポートファイルが見つかりませんでした。サポートサービスにお問い合わせください。(EMET0015)",
  } as const;

  /**
   * Azure Files関連の定数を定義します。
   */
  export const AZURE_FILES = {
    TASK_WORKSPACES_SHARE: "TaskWorkspaces",
    IMPORTS_DIRECTORY: "imports",
    EXPORTS_DIRECTORY: "exports",
  } as const;

  /**
   * Azure Blob Storage関連の定数を定義します。
   */
  export const AZURE_BLOB_PATHS = {
    IMPORTS_PREFIX: "imports",
    EXPORTS_PREFIX: "exports",
  } as const;

  /**
   * ファイル名関連の定数を定義します。
   */
  export const FILE_NAMES = {
    ASSETSFIELD_DEF_CSV: "assetsfield_def.csv",
    ERROR_DETAIL_TXT: "errordetail.txt",
    EXPORT_OPLOG_PREFIX: "exportoplog_",
    EXPORT_OPLOG_SUFFIX: ".zip",
  } as const;
}