/**
 * @fileoverview タスク関連のサーバーアクションを提供するモジュール
 * @description
 * createTaskAction Server Action - サーバ一覧画面から発行される全てのバックグラウンドタスク
 * （操作ログのエクスポート、管理項目定義のインポート・エクスポート）の実行要求を統一的に処理する
 * サーバサイドロジック。
 *
 * 主な責務：
 * 1. 入力パラメータ（タスク種別、対象サーバID、タスク固有パラメータ）を解析・検証
 * 2. 対象コンテナのタスク実行状態（ステータス）を確認し、BUSYの場合はタスク実行要求を拒否
 * 3. タスクID・タスク名を生成し、タスク種別に応じた固有のパラメータ検証およびファイル処理を実行
 * 4. DBトランザクション開始：コンテナレコード・タスクレコードをデータベースへ登録
 * 5. Service Busの指定キュー（TaskInputQueue）へタスク実行要求のメッセージを送信
 * 6. 送信成功の場合、DBトランザクションをコミットし、関連パスのキャッシュを無効化
 * 7. 受け付け処理失敗時は必要なエラー処理（トランザクションのロールバック、一時ファイルの削除）を実行
 *
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

"use server";

import Logger, { LogFunctionSignature } from "@/app/lib/logger";
import prisma from "@/app/lib/prisma";
import { v4 as uuidv4 } from "uuid";
import {
  TaskActionResult,
  ENV,
  PORTAL_ERROR_MESSAGES,
  TASK_ACTION_NAME,
  TASK_STATUS,
  TASK_TYPE,
  CONTAINER_STATUS,
  FILE_VALIDATION,
  PORTAL_CACHE_KEY_TASKS,
  FORM_FIELD_NAMES,
  AZURE_BLOB_PATHS,
  FILE_NAMES,
} from "../definitions";
import { ServerDataServers } from "../data/servers";
import { ServerDataLov } from "../data/lov";
import { ServerDataTasks } from "../data/tasks";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import { SessionData, sessionOptions } from "../session";
import { revalidatePath } from "next/cache";
import { formatMessage, formatTimestampForTaskName, isValidDateFormat } from "../utils";
import { revalidateTag } from "next/cache";
import { BlobActions } from "../integrations/azure-blob";
import { ServiceBusActions } from "../integrations/azure-service-bus";



/**
 * タスク関連のサーバーアクションを提供するクラス
 *
 * サーバ一覧画面から発行される全てのバックグラウンドタスクの作成要求を統一的に処理し、
 * 厳格なパラメータ検証・権限制御・DBトランザクション・ServiceBus連携・補償処理を実施する。
 */
class TaskActions {
  /**
   * createTaskAction Server Action
   *
   * クライアントからのタスク実行要求を受け付けて検証し、必要な前処理（ファイルアップロード等）を行い、
   * タスク情報をデータベースへ登録し、後続処理のためのメッセージをAzure Service Busへ送信する。
   *
   * 入力パラメータ (FormData 経由):
   * 共通パラメータ (全てのタスク種別で必須):
   * - taskType: タスクの種別コード（値の一覧テーブルのTASK_TYPEで定義）
   * - serverId: 対象サーバのID (Server.id)
   *
   * 特定タスク種別パラメータ:
   * - 操作ログのエクスポート: exportStartDate (YYYY-MM-DD形式), exportEndDate (YYYY-MM-DD形式)
   * - 管理項目定義のインポート: importFile (File オブジェクト), originalFileName (文字列)
   * - 管理項目定義のエクスポート: 追加パラメータなし
   *
   * 返り値 (TaskActionResult):
   * - success: タスク作成要求がバックエンドで正常に受け付けられ、メッセージキューへ送信されたかを示す
   * - message: 実際にユーザーへ表示するメッセージ内容
   *
   * @param {FormData} formData クライアントから送信されたフォームデータ
   * @returns {Promise<TaskActionResult>} タスク受付処理の結果
   */
  @LogFunctionSignature()
  static async createTaskAction(formData: FormData): Promise<TaskActionResult> {
    try {
      Logger.info({
        message: "createTaskAction invoked",
        formData: formDataToObject(formData),
      });
      let uploadedBlobPath: string | undefined = undefined;

      // 共通パラメータ解析 (taskType, serverId)
      // FormData から taskType と serverId を抽出し、存在と taskType の有効性を検証
      const taskType = formData.get("taskType") as string;
      const serverId = formData.get("serverId") as string;
      if (!taskType) {
        return {
          success: false,
          message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0021, [TASK_ACTION_NAME.START]),
        };
      }
      if (!serverId) {
        return {
          success: false,
          message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0021, [TASK_ACTION_NAME.START]),
        };
      }
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);
      if (!session.user?.userId || !session.user.licenseId) {
        Logger.error({
          message: "ユーザーセッションが見つからないか、無効です。",
          source: "createTaskAction",
        });
        return {
          success: false,
          message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0021, [TASK_ACTION_NAME.START]),
        };
      }

      // サーバ実行構成取得
      // serverId に基づき Server テーブルからサーバ情報を取得し、タスク実行に必要な構成情報を検証
      let serverDetails: any;
      try {
        serverDetails = await ServerDataServers.getServerDetailsForTask(serverId);
      } catch (error) {
        // DBからの取得失敗の場合：EMEC0021を返却する。エラー詳細をログに出力して、処理を終了する。
        Logger.error({
          message: "サーバ実行構成の取得に失敗しました",
          error,
          serverId,
          licenseId: session.user.licenseId,
          source: "createTaskAction",
        });
        return {
          success: false,
          message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0021, [TASK_ACTION_NAME.START]),
        };
      }
      if (!serverDetails) {
        Logger.error({
          message: `ライセンスID ${session.user.licenseId} に対して、サーバーID ${serverId} が見つかりません。`,
          source: "createTaskAction",
          serverId,
          licenseId: session.user.licenseId,
        });
        return {
          success: false,
          message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0021, [TASK_ACTION_NAME.START]),
        };
      }
      if (!serverDetails.azureVmName || !serverDetails.dockerContainerName || !serverDetails.hrwGroupName) {
        Logger.error({
          message: `サーバー ${serverId} はタスク実行用に構成されていません。`,
          source: "createTaskAction",
          serverDetails,
        });
        return {
          success: false,
          message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0021, [TASK_ACTION_NAME.START]),
        };
      }

      // コンテナ実行状態確認（設計文書ステップ3）
      // Azure VM名およびDockerコンテナ名を複合キーとして使用し、コンテナ実行状態テーブルから対象コンテナのステータスを確認
      // IDLE またはレコードが存在しない場合は処理を続行する
      let containerStatus: string | null | undefined;
      try {
        containerStatus = await ServerDataTasks.getContainerStatus(
          serverDetails.azureVmName!,
          serverDetails.dockerContainerName!,
        );
      } catch (error) {
        // DBからの取得失敗、または必要な情報（コンテナのステータス）が不足/不正している場合
        Logger.error({
          message: "コンテナ実行状態の取得に失敗しました",
          error,
          azureVmName: serverDetails.azureVmName,
          dockerContainerName: serverDetails.dockerContainerName,
          source: "createTaskAction",
        });
        return {
          success: false,
          message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0021, [TASK_ACTION_NAME.START]),
        };
      }

      // コンテナのステータスが BUSY の場合は処理を拒否
      if (containerStatus === CONTAINER_STATUS.BUSY) {
        return {
          success: false,
          message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0022, [serverDetails.name]),
        };
      }

      // コンテナのステータスが IDLE 以外の未知の文字列の場合は処理を拒否
      if (containerStatus !== null && containerStatus !== CONTAINER_STATUS.IDLE && containerStatus !== CONTAINER_STATUS.BUSY) {
        Logger.error({
          message: "コンテナステータスが未知の値のため処理を中断",
          containerStatus,
          expectedStatuses: [CONTAINER_STATUS.IDLE, CONTAINER_STATUS.BUSY, null],
          azureVmName: serverDetails.azureVmName,
          dockerContainerName: serverDetails.dockerContainerName,
          source: "createTaskAction",
        });
        return {
          success: false,
          message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0021, [TASK_ACTION_NAME.START]),
        };
      }

      // タスクID、タスク名生成（設計文書ステップ4）
      // タスクID (taskId)はUUIDとして生成
      // タスク名(taskName)は「{ServerName}-{タスク種別}-{YYYYMMDDHHmmss}」の形式で生成
      const taskId = uuidv4();

      // ユーザーのタイムゾーンに応じた現在の日時でタイムスタンプを生成
      const ymdhms = formatTimestampForTaskName(session.user.tz);

      // タスク種別の日本語名をLOVテーブルから取得
      const taskTypeLov = await ServerDataLov.fetchLov(taskType);
      const taskTypeName = taskTypeLov?.value || taskType;

      const taskName = `${serverDetails.name}-${taskTypeName}-${ymdhms}`;

      // タスク種別固有ロジック実行 (パラメータ構築、ファイル処理等)
      let taskSpecificParamsForDb: any = {};
      switch (taskType) {
        case TASK_TYPE.OPLOG_EXPORT: {
          // 操作ログのエクスポート (TASK_TYPE.OPLOG_EXPORT) タスクの特定処理
          // 固有入力パラメータの抽出とサーバサイド検証
          const exportStartDate = formData.get("exportStartDate") as string;
          if (!exportStartDate) {
            return {
              success: false,
              message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0016, [FORM_FIELD_NAMES.START_DATE]),
            };
          }
          // YYYY-MM-DD形式の厳密な検証
          if (!isValidDateFormat(exportStartDate)) {
            return {
              success: false,
              message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0016, [FORM_FIELD_NAMES.START_DATE]),
            };
          }
          const exportEndDate = formData.get("exportEndDate") as string;
          if (!exportEndDate) {
            return {
              success: false,
              message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0016, [FORM_FIELD_NAMES.END_DATE]),
            };
          }
          // YYYY-MM-DD形式の厳密な検証
          if (!isValidDateFormat(exportEndDate)) {
            return {
              success: false,
              message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0016, [FORM_FIELD_NAMES.END_DATE]),
            };
          }
          // 期間の日数がデータベース 値の一覧(Lov) テーブルの OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN の値（最大日数）以下であることを検証
          let maxExportDaysSpan: number | undefined = undefined;
          const lov = await ServerDataLov.fetchLov("OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN");
          if (lov && !isNaN(Number(lov.value))) {
            maxExportDaysSpan = Number(lov.value);
          }
          if (!maxExportDaysSpan) {
            return {
              success: false,
              message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0021, [TASK_ACTION_NAME.START]),
            };
          }
          const start = new Date(exportStartDate);
          const end = new Date(exportEndDate);
          // 終了日 >= 開始日であることを検証
          if (end < start) {
            return {
              success: false,
              message: PORTAL_ERROR_MESSAGES.EMEC0024,
            };
          }
          const diffDays = Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
          if (diffDays > maxExportDaysSpan) {
            return {
              success: false,
              message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0020, [maxExportDaysSpan.toString(), maxExportDaysSpan.toString()]),
            };
          }
          // Task.parametersJson用特定パラメータの構築
          taskSpecificParamsForDb = { exportStartDate, exportEndDate };
          break;
        }
        case TASK_TYPE.MGMT_ITEM_IMPORT: {
          // 管理項目定義のインポート (TASK_TYPE.MGMT_ITEM_IMPORT) タスクの特定処理
          // 固有入力パラメータの抽出とサーバサイド検証
          const importFile = formData.get("importFile") as File | null;
          const originalFileName = formData.get("originalFileName") as string | null;
          if (!importFile || !originalFileName || importFile.size === 0) {
            return {
              success: false,
              message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0016, [FORM_FIELD_NAMES.MGMT_ITEM_CSV_FILE]),
            };
          }
          // サーバサイドファイル検証（セキュリティ強化：DoS攻撃防止と適切な入力検証）
          // 1. ファイル拡張子検証（定数を使用）
          const hasValidExtension = FILE_VALIDATION.CSV.ALLOWED_EXTENSIONS.some(ext =>
            originalFileName.toLowerCase().endsWith(ext)
          );
          if (!hasValidExtension) {
            return {
              success: false,
              message: PORTAL_ERROR_MESSAGES.EMEC0017,
            };
          }

          // 2. MIMEタイプ検証（定数を使用、設計要求に加えてセキュリティ強化）
          const isCsv = FILE_VALIDATION.CSV.ALLOWED_MIME_TYPES.includes(importFile.type as any);
          if (!isCsv) {
            return {
              success: false,
              message: PORTAL_ERROR_MESSAGES.EMEC0017,
            };
          }

          // 3. ファイルサイズ検証（DoS攻撃防止のため必須、定数を使用）
          if (importFile.size > FILE_VALIDATION.CSV.MAX_FILE_SIZE) {
            return {
              success: false,
              message: PORTAL_ERROR_MESSAGES.EMEC0028,
            };
          }
          // Azure Blob Storageへの一時ファイルアップロード
          // パス {currentUser.licenseId}/{AZURE_BLOB_PATHS.IMPORTS_PREFIX}/{生成したtaskId}/{FILE_NAMES.ASSETSFIELD_DEF_CSV} でアップロード
          try {
            uploadedBlobPath = await BlobActions.uploadFile(
              importFile,
              ENV.AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF!,
              `${session.user.licenseId}/${AZURE_BLOB_PATHS.IMPORTS_PREFIX}/${taskId}/${FILE_NAMES.ASSETSFIELD_DEF_CSV}`,
            );
          } catch (e) {
            Logger.error({ message: "管理項目定義インポート: Blobアップロード失敗。" + (e as Error).message, error: e });
            return {
              success: false,
              message: PORTAL_ERROR_MESSAGES.EMEC0018,
            };
          }
          // Task.parametersJson用特定パラメータの構築
          taskSpecificParamsForDb = {
            importedFileBlobPath: uploadedBlobPath,
            originalFileName,
          };
          break;
        }
        case TASK_TYPE.MGMT_ITEM_EXPORT:
          // 管理項目定義のエクスポート (TASK_TYPE.MGMT_ITEM_EXPORT) タスクの特定処理
          // 固有入力パラメータの抽出とサーバサイド検証: なし
          // Task.parametersJson用特定パラメータの構築: なし
          taskSpecificParamsForDb = {};
          break;
        default:
          return {
            success: false,
            message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0021, [TASK_ACTION_NAME.START]),
          };
      }

      // DBトランザクション開始（設計文書ステップ6）
      // レコード作成: ContainerConcurrencyStatusレコード作成（必要時）、Task レコード作成
      try {
        await prisma.$transaction(async (tx) => {
          // ContainerConcurrencyStatusレコード作成：レコードが存在しない場合のみ新規作成
          // 前段のgetContainerStatus()でnullが返された場合は、レコードが存在しないことを意味する
          if (containerStatus === null) {
            await tx.containerConcurrencyStatus.create({
              data: {
                targetVmName: serverDetails.azureVmName!,
                targetContainerName: serverDetails.dockerContainerName!,
                status: CONTAINER_STATUS.IDLE,
                currentTaskId: null,
              },
            });
          }
          // Task レコード作成: 生成した taskId を主キーとし、タスク名、初期ステータス TASK_STATUS.QUEUED、特定パラメータ等の情報を含む新規レコードを挿入
          const parametersJson = JSON.stringify(taskSpecificParamsForDb);
          await tx.task.create({
            data: {
              id: taskId,
              taskType,
              taskName,
              status: TASK_STATUS.QUEUED,
              submittedByUserId: session.user!.userId,
              licenseId: session.user!.licenseId,
              targetServerId: serverId,
              targetServerName: serverDetails.name,
              targetVmName: serverDetails.azureVmName,
              targetContainerName: serverDetails.dockerContainerName,
              targetHRWGroupName: serverDetails.hrwGroupName,
              parametersJson,
            },
          });
        });
      } catch (e) {
        // レコード作成に失敗した場合:トランザクションをロールバック、管理項目定義のインポートタスクの場合はアップロードした一時ファイルを削除
        Logger.error({ message: "タスク作成トランザクション失敗", error: e });
        if (taskType === TASK_TYPE.MGMT_ITEM_IMPORT && uploadedBlobPath) {
          try {
            await BlobActions.deleteBlob(
              ENV.AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF!,
              uploadedBlobPath,
            );
          } catch (delErr) {
            Logger.error({ message: "補償処理: アップロード済みファイル削除失敗", error: delErr });
          }
        }
        return {
          success: false,
          message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0021, [TASK_ACTION_NAME.START]),
        };
      }

      // タスクメッセージ構築・Service Bus (TaskInputQueue) へ送信
      // タスクIDを含むタスク実行要求メッセージを、環境変数 SERVICE_BUS_TASK_INPUT_QUEUE_NAME で指定されるService Busキューへ送信
      try {
        const queueName = ENV.SERVICE_BUS_TASK_INPUT_QUEUE_NAME;
        if (!queueName) {
          throw new Error("タスク実行キュー名が未設定です。");
        }
        await ServiceBusActions.sendMessage(queueName, { taskId });
        Logger.info(`Service Busへのメッセージ送信成功: taskId=${taskId}, queue=${queueName}`);
      } catch (error) {
        // 送信に失敗した場合:管理項目定義のインポートタスクの場合はアップロードした一時ファイルを削除、ステップ7.で作成されたタスクレコードを削除
        if (taskType === TASK_TYPE.MGMT_ITEM_IMPORT && uploadedBlobPath) {
          try {
            await BlobActions.deleteBlob(ENV.AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF!, uploadedBlobPath);
          } catch (delErr) {
            Logger.error({ message: "補償処理: アップロード済みファイル削除失敗", error: delErr });
          }
        }

        // ステップ7.で作成されたタスクレコードを削除
        try {
          await prisma.task.delete({
            where: { id: taskId },
          });
          Logger.info({ message: `補償処理: タスクレコード削除成功 taskId=${taskId}` });
        } catch (delErr) {
          Logger.error({ message: `補償処理: タスクレコード削除失敗 taskId=${taskId}`, error: delErr });
        }

        Logger.error({ message: "Service Bus送信失敗", error });
        return {
          success: false,
          message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0019, [TASK_ACTION_NAME.START]),
        };
      }

      // 関連パスのキャッシュ無効化 (revalidatePath)
      // タスク受付成功後、影響を受ける画面のキャッシュを無効化
      revalidatePath("/dashboard/tasks");
      revalidatePath("/dashboard/servers");

      if (taskType === TASK_TYPE.OPLOG_EXPORT) {
        revalidatePath("/dashboard/operation-logs");
      }

      // 成功応答返却
      return {
        success: true,
        message: formatMessage(
          PORTAL_ERROR_MESSAGES.EMEC0025,
          [serverDetails.name, taskTypeName]
        ),
      };
    } catch (error) {
      // 予期せぬ内部例外発生時の全局异常处理
      Logger.error({
        message: "createTaskAction: 予期せぬ内部例外が発生しました",
        error,
        source: "createTaskAction",
      });
      return {
        success: false,
        message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0027, [TASK_ACTION_NAME.START]),
      };
    }
  }

  /**
   * タスクの中止要求を受け付けます。
   *
   * 入力パラメータを検証し、対象タスクの情報を取得して、タスクステータスに基づく分岐処理を実行します。
   * QUEUED状態のタスクの場合、楽観的ロック制御でステータスをPENDING_CANCELLATIONに更新し、
   * Service Busへ中止要求メッセージを送信します。送信失敗時は補償処理を行います。
   *
   * @param {string} taskId - 中止対象のタスクID
   * @returns {Promise<TaskActionResult>} 中止要求の結果（成功/失敗、メッセージ等）を返すPromise
   */
  @LogFunctionSignature()
  static async requestTaskCancellation(
    taskId: string,
  ): Promise<TaskActionResult> {
    Logger.info(`requestTaskCancellation: 開始, taskId: ${taskId}`);

    // 1. 入力パラメータ検証
    // 入力パラメータの taskId が存在しているか、空でないか確認する
    if (!taskId || taskId.trim() === "") {
      Logger.error({
        message: "taskIdが空です。",
        source: "requestTaskCancellation",
      });
      return {
        success: false,
        message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0021, [TASK_ACTION_NAME.CANCEL]),
      };
    }

    try {
      // 2. 対象タスク情報取得
      // taskId を使用して、データベースの Task テーブルから該当するタスクのステータス、タスク名、最終更新日時を取得する
      const task = await prisma.task.findUnique({
        where: { id: taskId },
        select: { status: true, taskName: true, updatedAt: true },
      });

      if (!task) {
        Logger.error(`タスクが見つかりません。taskId: ${taskId}`);
        return {
          success: false,
          message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0021, [TASK_ACTION_NAME.CANCEL]),
        };
      }

      // 3. タスクステータス分岐処理
      // 取得した Task.status に基づき、以下の処理を行う

      // ステータスが QUEUED (実行待ち) 以外の場合
      if (task.status !== TASK_STATUS.QUEUED) {
        Logger.info({
          message: "中止可能な状態ではありません。",
          source: "requestTaskCancellation",
          taskId,
          currentStatus: task.status,
        });
        // タスクは他のユーザーによって先に中止操作が行われたか、既に実行開始したか、または終了（正常/エラー問わず）したため、
        // データベースの更新とService Busへの送信は行わず、中止不可能を示すエラーメッセージを返却する
        return {
          success: false,
          message: PORTAL_ERROR_MESSAGES.EMEC0023,
        };
      }

      // ステータスが QUEUED (実行待ち) の場合
      // 1. データベースの該当 Task レコードの status を PENDING_CANCELLATION (中止待ち) に更新する
      // タスクIDが一致、及び最終更新日時がステップ2.で取得した日時と一致することを条件とする
      const updateResult = await prisma.task.updateMany({
        where: {
          id: taskId,
          status: TASK_STATUS.QUEUED,
          updatedAt: task.updatedAt,
        },
        data: {
          status: TASK_STATUS.PENDING_CANCELLATION,
          updatedAt: new Date(),
        },
      });

      if (updateResult.count === 0) {
        Logger.error({
          message: "楽観的ロック制御により更新に失敗しました。",
          source: "requestTaskCancellation",
          taskId,
        });
        // 楽観的ロック制御により更新に失敗した場合は、他のユーザーによる同時操作が発生したため、中止不可能を示すエラーメッセージを返却する
        return {
          success: false,
          message: PORTAL_ERROR_MESSAGES.EMEC0023,
        };
      }

      // 2. Service Bus の TaskControlQueue へ、{ taskId: 入力パラメータのタスクID } を含むメッセージを送信する
      try {
        const queueName = ENV.SERVICE_BUS_TASK_CONTROL_QUEUE_NAME;
        if (!queueName) {
          throw new Error("タスク中止キュー名が未設定です。");
        }
        await ServiceBusActions.sendMessage(queueName, { taskId });
        Logger.info(`Service Busへの中止要求メッセージ送信成功: taskId=${taskId}, queue=${queueName}`);
      } catch (serviceBusError) {
        // メッセージ送信時にエラーが発生した場合は、データベースの該当 Task レコードの status を QUEUED (実行待ち) に更新する（戻す）
        Logger.error({
          message: "Service Bus送信失敗、補償処理を実行します。",
          error: serviceBusError,
          source: "requestTaskCancellation",
          taskId,
        });

        try {
          await prisma.task.update({
            where: { id: taskId },
            data: { status: TASK_STATUS.QUEUED },
          });
          Logger.info(`補償処理完了: タスクステータスをQUEUEDに戻しました。taskId=${taskId}`);
        } catch (rollbackError) {
          Logger.error({
            message: "補償処理（ロールバック）に失敗しました。",
            error: rollbackError,
            source: "requestTaskCancellation",
            taskId,
          });
        }

        // EMEC0019を返却する
        return {
          success: false,
          message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0019, [TASK_ACTION_NAME.CANCEL]),
        };
      }

      Logger.info(`requestTaskCancellation: 正常終了, taskId: ${taskId}`);
      // 3. 上記処理が全て成功した場合、EMEC0026を返却する
      return {
        success: true,
        message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0026, [task.taskName || ""]),
      };
    } catch (error) {
      Logger.error({
        message: "requestTaskCancellation: 予期しないエラーが発生しました。",
        error,
        source: "requestTaskCancellation",
        taskId,
      });
      return {
        success: false,
        message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0027, [TASK_ACTION_NAME.CANCEL]),
      };
    }
  }

  /**
   * タスク一覧のキャッシュを無効化し、再取得を促します。
   * @returns {Promise<void>}
   */
  @LogFunctionSignature()
  static async refreshTaskList(): Promise<void> {
    Logger.info("refreshTaskList: 開始");
    const session = await getIronSession<SessionData>(
      cookies(),
      sessionOptions,
    );
    if (session.user?.licenseId) {
      // Next.jsのOn-demand Revalidation機能を使用します。
      // revalidateTagを呼び出すことで、このタグに関連付けられたfetch（unstable_cache）のキャッシュが無効になります。
      const tag = `${PORTAL_CACHE_KEY_TASKS}-${session.user.licenseId}`;
      Logger.info(`Revalidating cache tag: ${tag}`);
      revalidateTag(tag);
    } else {
      Logger.info(
        "refreshTaskList: セッション情報が見つからないため、キャッシュの再検証をスキップしました。",
      );
    }
  }
}

/**
 * FormDataを普通のオブジェクトに変換するユーティリティ関数である。
 *
 * @param {FormData} formData - 入力フォームデータ。
 * @returns {Record<string, any>} 変換後のオブジェクト。
 */
function formDataToObject(formData: FormData): Record<string, any> {
  const obj: Record<string, any> = {};
  // FormDataの全エントリを走査し、File型はファイル名のみ記録する
  Array.from(formData.entries()).forEach(([key, value]) => {
    if (typeof File !== 'undefined' && value instanceof File) {
      obj[key] = `[File: ${value.name}]`;
    } else {
      obj[key] = value;
    }
  });
  return obj;
}

// Server Actionのエクスポート
// Next.js Server Actionsとして使用するため、クラスメソッドを関数として再エクスポート
export const createTask = TaskActions.createTaskAction;
export const cancelTask = TaskActions.requestTaskCancellation;
export const refreshTaskList = TaskActions.refreshTaskList;