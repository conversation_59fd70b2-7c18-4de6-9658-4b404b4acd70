# 客户测试报告生成指南

本指南说明如何基于 `test-cases.json` 数据和客户提供的模板生成完整的单体测试报告。

## 📋 文件说明

### 输入文件
- `test-cases.json` - 包含所有测试用例的结构化数据
- `UT動作確認チェックリスト_template.xlsx` - 客户提供的Excel模板

### 输出文件
- `UT動作確認チェックリスト_generated.xlsx` - 生成的完整测试报告

### 脚本文件
- `scripts/generate-customer-test-report.js` - 主要生成脚本
- `scripts/example-customer-report.js` - 使用示例和预览脚本

## 🚀 快速开始

### 1. 运行生成脚本
```bash
# 生成客户测试报告
npm run generate-customer-report

# 或者直接运行
node scripts/generate-customer-test-report.js
```

### 2. 查看预览信息
```bash
# 查看生成预览和配置信息
node scripts/example-customer-report.js
```

## 📊 生成的报告结构

### 工作表组织
报告将包含以下工作表：

1. **表紙** - 封面页（保持原模板）
2. **目次** - 目录页，更新为：
   - 1章.試験観点・テスト結果集計シート
   - 2章.ポータル
   - 3章.タスク Function App  
   - 4章.Runbook ジョブ Function App

3. **1.試験観点・テスト結果集計シート** - 统计信息工作表
4. **2.1, 2.2, 2.3** - ポータル的各大項目工作表
5. **3.1, 3.2, ..., 3.7** - タスク Function App的各大項目工作表
6. **4.1, 4.2** - Runbook ジョブ Function App的各大項目工作表

### 章节映射

| 项目 | 章节 | 标题 |
|------|------|------|
| jcs-endpoint-nextjs | 2章 | ポータル |
| jcs-backend-services-standard | 3章 | タスク Function App |
| jcs-backend-services-long-running | 4章 | Runbook ジョブ Function App |

## 📈 数据统计

基于当前的 `test-cases.json` 数据：

- **总测试用例数**: 417个
- **总项目数**: 3个
- **总大項目数**: 12个
- **总小項目数**: 27个

### 各项目详情

#### 2章.ポータル
- 大項目数: 3个
- 小項目数: 14个  
- 测试用例数: 185个
- 工作表: 2.1, 2.2, 2.3

#### 3章.タスク Function App
- 大項目数: 7个
- 小項目数: 9个
- 测试用例数: 161个
- 工作表: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7

#### 4章.Runbook ジョブ Function App
- 大項目数: 2个
- 小項目数: 4个
- 测试用例数: 71个
- 工作表: 4.1, 4.2

## 🔧 工作表内容

### 目次工作表
自动更新目录结构，包含：
- 章节标题
- 各大項目的链接
- 统一的编号格式

### 统计信息工作表
包含详细的测试用例统计：
- 按章节统计
- 按大項目统计
- 测试用例数量汇总

### 大項目工作表
每个大項目一个工作表，包含：

#### 表头结构
| 項番 | 試験観点 | 試験対象 | 試験手順 | 確認項目 | 実施日 | 実施者 | 再鑑者 | 試験結果 | 備考 |
|------|----------|----------|----------|----------|--------|--------|--------|----------|------|

#### 数据映射
- **項番**: 自动编号 (1, 2, 3, ...)
- **試験観点**: 来自 `テストケース名` 字段
- **試験対象**: 来自 `試験対象` 字段
- **試験手順**: 来自 `試験手順` 字段（处理换行符）
- **確認項目**: 来自 `確認項目` 字段（处理换行符）
- **実施日**: 空白（供手动填写）
- **実施者**: 空白（供手动填写）
- **再鑑者**: 空白（供手动填写）
- **試験結果**: 空白（供手动填写）
- **備考**: 空白（供手动填写）

## ⚙️ 配置选项

脚本中的主要配置项：

```javascript
const CONFIG = {
  INPUT_JSON: 'docs-delivery/unit-test-report/test-cases.json',
  TEMPLATE_FILE: 'docs-delivery/unit-test-report/UT動作確認チェックリスト_template.xlsx',
  OUTPUT_FILE: 'docs-delivery/unit-test-report/UT動作確認チェックリスト_generated.xlsx',
  
  PROJECT_MAPPING: {
    'jcs-endpoint-nextjs': {
      chapter: 2,
      title: 'フロントエンド',
      displayName: 'ポータル'
    },
    // ... 其他项目映射
  }
};
```

## 🎯 使用场景

### 1. 初次生成
当你有新的测试用例数据时，运行脚本生成完整报告。

### 2. 数据更新
当测试用例数据更新后，重新运行脚本更新报告。

### 3. 模板定制
如果需要修改模板格式，可以：
1. 更新模板文件
2. 修改脚本中的配置
3. 重新生成报告

## 🔍 质量保证

### 数据完整性
- ✅ 所有417个测试用例都会被包含
- ✅ 保持原始数据的完整性
- ✅ 正确处理换行符和特殊字符

### 格式一致性
- ✅ 统一的工作表命名规范
- ✅ 一致的表头格式
- ✅ 标准的单元格样式

### 可追溯性
- ✅ 每个测试用例都有明确的来源
- ✅ 保持与原始JSON数据的对应关系
- ✅ 支持数据更新和重新生成

## 📝 注意事项

1. **模板保持**: 脚本会保持原模板的基本格式和样式
2. **数据覆盖**: 每次运行会完全重新生成报告
3. **手动填写**: 実施日、実施者等字段需要手动填写
4. **文件路径**: 确保所有输入文件路径正确
5. **Excel兼容**: 生成的文件与Excel完全兼容

## 🛠️ 故障排除

### 常见问题

#### 1. 找不到模板文件
```
错误: ENOENT: no such file or directory
解决: 确认模板文件路径正确
```

#### 2. JSON数据格式错误
```
错误: Unexpected token in JSON
解决: 检查test-cases.json文件格式
```

#### 3. 工作表创建失败
```
错误: Cannot read property of undefined
解决: 检查项目映射配置是否正确
```

### 调试方法

1. 运行预览脚本检查配置：
   ```bash
   node scripts/example-customer-report.js
   ```

2. 检查输入文件是否存在：
   ```bash
   ls -la docs-delivery/unit-test-report/
   ```

3. 查看详细错误信息：
   ```bash
   node scripts/generate-customer-test-report.js --verbose
   ```

---

**最后更新**: 2025年7月16日  
**版本**: v1.0  
**维护者**: JCS开发团队