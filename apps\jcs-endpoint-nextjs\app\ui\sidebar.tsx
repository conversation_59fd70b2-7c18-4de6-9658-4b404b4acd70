/**
 * @file sidebar.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

"use client";

import clsx from "clsx";
import { usePathname } from "next/navigation";
import { initCollapses } from "flowbite";
import { useEffect } from "react";
import Link from "next/link";
import { navLinks } from "../lib/definitions";

// サイドバーコンポーネント
export default function Sidebar() {
  const pathname = usePathname();
  useEffect(() => {
    initCollapses();

    document.querySelectorAll("[data-collapse-toggle]").forEach((item) => {
      item.addEventListener("click", () => {
        const isExpanded = item.getAttribute("aria-expanded") === "true";
        const icon = item.querySelector("img.icon");
        icon && icon.classList.toggle("-rotate-90", !isExpanded);
      });
    });
  }, []);

  return (
    <div className="z-10 w-full h-full overflow-auto p-2 lg:sticky lg:!block">
      <aside
        id="sidebar-multi-level-sidebar"
        className="dark w-full h-full bg-gray-600 bg-gradient-bg shadow-inner drop-shadow"
        aria-label="Sidebar"
      >
        <div className="h-full overflow-y-auto bg-gray-50 px-3 py-4 dark:bg-transparent">
          <ul className="space-y-2 font-medium">
            {navLinks.map((menu) => (
              <li key={menu.key}>
                <button
                  type="button"
                  className="group flex w-full items-center rounded-lg p-2 text-base text-gray-900 transition duration-75 dark:text-white"
                  aria-controls={menu.key}
                  data-collapse-toggle={menu.key}
                >
                  <img
                    src="/screencloseicon_16.png"
                    className="icon me-2 h-4 w-4 transform-gpu"
                    alt="expanded"
                  />
                  <img
                    src="/UserDefinedGroups18.png"
                    className="h-6 w-6"
                    alt="servers"
                  />
                  <span className="ms-3 flex-1 whitespace-nowrap text-left rtl:text-right">
                    {menu.name}
                  </span>
                </button>
                <ul id={menu.key} className="space-y-2 py-2">
                  {menu.subs.map((sub) => (
                    <li key={sub.name}>
                      <Link
                        href={sub.href}
                        className={clsx(
                          "group flex w-full items-center rounded-lg p-2 pl-11 text-gray-900 transition duration-75 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700",
                          {
                            "from-blue-600 to-blue-500 dark:bg-gradient-to-b":
                              pathname === sub.href,
                          },
                        )}
                      >
                        <img
                          src="/UserDefinedGroups18.png"
                          className="h-6 w-6"
                          alt="servers"
                        />
                        <p className="ms-3 hidden md:block">{sub.name}</p>
                      </Link>
                    </li>
                  ))}
                </ul>
              </li>
            ))}
          </ul>
        </div>
      </aside>
    </div>
  );
}
