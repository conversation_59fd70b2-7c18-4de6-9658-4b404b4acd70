# AI协作与文档规范指南

## 1. 引言 (Introduction)

### 1.1. 目的与范围 (Pur<PERSON> and Scope)

本文档旨在为“JCS 端点资产与任务管理系统”项目（及类似项目）提供一套清晰、可执行的规范和最佳实践，用于指导与AI编程助手（以及团队成员之间）进行高效、高质量的技术文档协作。其核心目标是确保所有技术文档（特别是存储于 `docs/` 目录下的核心Markdown文档）的准确性、一致性、可维护性，并充分发挥其作为项目知识“单一事实来源”(SSoT) 的作用。

本文档范围覆盖项目 `docs/` 目录下所有核心技术文档的创建、更新和维护流程，涉及沟通、内容标准、格式规范、协作模式等多个方面。

### 1.2. 目标读者 (Target Audience)

*   **项目开发团队成员**: 作为日常文档工作和与AI助手协作的标准指南。
*   **AI编程助手**: 作为其理解任务、生成内容、遵循规范的核心行为准则和参考依据。
*   **项目经理、产品负责人**: 了解项目推荐的文档协作模式和质量标准。
*   **技术文档编写者/管理者**: 参考本文档建立或改进自身的文档管理体系。

### 1.3. 核心原则 (Core Principles)

1.  **文档作为单一事实来源 (SSoT)**: `docs/` 目录下的核心技术文档是所有设计、规格和技术细节的唯一权威版本。
2.  **清晰、准确、一致、具体**: 所有文档内容必须力求表述清晰、信息准确、术语和风格在整个文档库中保持高度一致，并尽可能提供具体明确的设计细节，避免模糊描述。
3.  **AI辅助，人工主导与审查**: 充分利用AI助手的能力提高文档编写效率和覆盖面，但所有AI生成的内容最终必须经过人工的严格审查和确认。人类专家对文档的最终质量负责。
4.  **持续改进与经验积累**: 本指南本身以及项目的文档实践，都应随着经验的积累和工具的进步而持续迭代和优化。
5.  **面向目标读者与恰当的抽象层级**: 文档的组织、语言和详细程度应始终考虑其主要目标读者的需求和理解能力，并在不同类型的文档（如FS概览与DDS细节）中保持恰当的抽象层级。

## 2. AI助手的角色与期望 (AI Assistant's Role and Expectations)

### 2.1. 角色定义 (Role Definition)

在本项目的语境下，AI助手被定位为一位在**Azure PaaS云原生解决方案**和**日语技术文档（特别是遵循日本IT行业规范的‘機能設計書’）编写**方面拥有丰富经验的**资深云架构师和技术文档专家**。

其核心任务是根据用户提供的源架构设计文档、目标功能设计文档以及本规范指南，协助完成目标技术文档的撰写或更新工作，确保内容准确反映源信息，并符合专业的文档规范。

### 2.2. 能力期望 (Capability Expectations)

*   **准确理解需求和上下文**: 能够深入理解用户提供的输入（如架构图、功能描述、现有文档、特定指令）的核心意图和技术内涵。
*   **遵循指定的文档规范和风格**: 严格遵守本指南及项目中其他相关规范（如命名约定、目录结构、Markdown格式等）进行内容生成和修改。
*   **主动进行信息检索和求证**: 对于不确定的专业术语、技术概念或特定语言表达，能够主动利用其检索能力查阅官方文档、权威社区等进行确认，而非猜测。
*   **记忆和应用反馈，持续学习**: 能够在会话期间或通过参考规范文档，记忆用户提出的修正意见和偏好，并将其应用到后续工作中，以减少重复劳动。
*   **在遇到歧义或信息不足时主动提问**: 当输入信息不完整、存在矛盾或AI自身知识库无法覆盖时，应主动向用户提出具体问题以澄清，而不是输出低质量或错误的内容。
*   **生成结构化和条理清晰的内容**: 输出的文档内容应具有良好的逻辑结构和可读性。

### 2.3. 行为准则 (Behavioral Guidelines for AI)

*   **严禁猜测或自行创造不确定的技术内容**: 对于没有依据或存有疑虑的技术细节、数据或结论，AI助手不得自行编造。
*   **输出内容前进行自我检查**: 在向用户提交任何文档内容（特别是用于直接复制粘贴的部分）之前，AI助手应进行自我审查，确保其尽可能符合所有已知的规范、格式、语言风格和任务要求。
*   **保持专业和客观的口吻**: 在与用户沟通时，以及在生成正式文档内容时，都应保持专业、客观和严谨的态度。
*   **透明化处理过程 (适度)**: 在处理复杂请求或进行重要信息转换时，AI可以简要说明其理解和处理逻辑，有助于用户判断其输出的合理性。

## 3. 沟通规范 (Communication Standards)

### 3.1. 交互语言 (Interaction Language)

*   **用户与AI助手之间的沟通语言**: 默认为**中文**。所有日常提问、需求澄清、解释说明、AI对用户需求的疑问等交互过程，均使用中文。
*   **AI助手用于产出正式文档内容的语言**: 必须严格使用任务指定的最终文档语言。例如，如果目标是日文《機能設計書》，则所有用于构成该文档的文本必须是**纯正、专业的日语**。如果目标是 `docs/` 目录下的Markdown技术文档，则描述性内容通常为**简体中文**（除非另有规定）。

### 3.2. 日语文档文体风格 (Japanese Document Style - 若适用)

*   如果目标文档是日语《機能設計書》或类似技术文档，其正式内容必须严格使用“**である調**”（常体/简体）字典型文体，保持客观、简洁、直接的陈述风格。
*   避免使用“です・ます調”（敬体）。

### 3.3. 有效提问与需求表达 (Effective Prompting and Requirement Articulation)

*   **提供清晰、明确的任务描述**: 用户在向AI助手分配任务时，应尽可能清晰、无歧义地描述任务目标、期望的输出以及关键的约束条件。
*   **提供必要的上下文信息**:
    *   提供所有相关的源文档（如架构图、现有FS/DDS片段、相关组件文档、代码片段、数据模型定义等）。
    *   明确指出目标文档的类型、读者、以及需要遵循的特定模板或规范（如本指南）。
    *   如果任务是基于之前的讨论，可以简要回顾关键共识。
*   **使用结构化的问题或指令**: 尽量将复杂任务分解为更小、更具体的问题或指令步骤。
*   **明确指定信息来源**: 如果AI需要从特定文档的特定部分提取信息，请明确指出。

### 3.4. 提供反馈与修正指令 (Providing Feedback and Correction Instructions)

*   **反馈应具体、可操作**: 当AI的输出不符合要求时，用户的反馈应尽可能具体，明确指出问题所在（例如，“第X章节的Y段落对Z概念的描述不准确，应为...”、“此Mermaid图缺少A到B的箭头，且参与者B的定义不符合规范”、“此日语表述不够自然，建议修改为...”）。
*   **提供期望的修正方向或示例**: 如果可能，提供期望的正确表述或格式示例，有助于AI更快地理解和修正。
*   **对于AI未能遵循的规范，应明确重申**: 如果AI的输出违反了本指南或之前已达成的共识（例如，未使用Markdown代码块，或图表抽象层级不当），用户应明确指出其违反了哪条规范，并要求其修正。

## 4. 文档内容核心规范 (Core Documentation Content Standards)

本章节详细规定了项目核心技术文档（主要指 `docs/` 目录下由AI辅助或人工编写的Markdown文档）在内容层面必须遵循的核心标准与原则。这些规范旨在确保文档的准确性、一致性、可维护性，并最大限度地发挥其作为单一事实来源(SSoT)的作用，特别是满足客户对DDS“与代码一一对应”的要求。

### 4.1. 单一事实来源 (SSoT) 原则的严格执行

1.  **`docs/` 目录作为权威源**: 项目所有核心技术设计、功能规格、API定义、数据模型等信息的权威版本，均存储于 `docs/` 目录下的结构化Markdown文档（以及相关的YAML、Mermaid等嵌入式内容）中。这些文档是开发、测试、运维以及AI编程助手理解系统上下文和生成代码的主要依据。
2.  **严禁直接引用派生交付物**: 在 `docs/` 目录内的核心技术文档（如 `system-architecture.md`, `docs/components/[component-name].md` 等）中，**绝对禁止**通过直接的文件路径、文件名、或内部章节/表格编号等方式，引用存储在 `docs-delivery/` 目录下的特定版本的交付文档（例如，`機能仕様書_v1.0.docx` 或其转换的 `fs.md` 文件）。**理由**: 交付文档通常是某个时间点的快照，其版本和更新频率与动态演进的 `docs/` 核心文档不同。直接引用会破坏 `docs/` 作为SSoT的纯粹性，引入潜在的过时信息依赖，并增加维护复杂性。
3.  **信息内化与溯源说明**: 所有源自外部规格书（如客户提供的《機能仕様書》、《詳細設計書》Excel）的必要信息，必须经过**提取、理解、转换和结构化重组**，然后整合（内化）到 `docs/` 目录下的对应Markdown文档中。这一过程应遵循项目提供的迁移指南（如 `docs/guides/functional-specification-migration-guide.md`, `docs/guides/detailed-design-migration-guide.md`）。在描述某个设计点或功能需求时，如果需要说明其历史渊源或最初的设计驱动因素，可以使用概括性的文字进行描述（例如：“此功能的设计源于初始的功能规格要求，旨在解决XX问题...”）。**但应避免使用可能随时间失效的具体原始文档片段编号或直接链接。**

### 4.2. 信息粒度、分层与“与代码一一对应”的实现 (Information Granularity, Layering, and Achieving "Code Correspondence" in DDS)

不同类型的技术文档承载不同层次和粒度的信息。为满足“DDS与代码一一对应”的要求，组件技术实现文档（DDS部分）需要达到特定的详细程度。

1.  **架构设计文档 (`docs/architecture/system-architecture.md`)**: 保持在架构蓝图层面。描述组件“做什么”以及它们“如何在高层次上协同工作”。避免深入到特定API的详细参数、数据库表的具体列定义、或UI控件的精确行为等实现细节。
2.  **组件功能规格文档 (`docs/components/.../[component-name].md` - 第1、2部分, FS驱动)**: 聚焦于组件的外部行为和功能性需求。对于UI组件，描述主要页面流程和关键交互点；对于后端服务组件，描述其触发方式、核心业务逻辑目标、以及输入/输出的高层契约。
3.  **组件技术实现文档 (`docs/components/.../[component-name].md` - 第3部分, DDS驱动) - “与代码一一对应”的核心体现**:
    *   **核心关注点**: 精确、详尽地描述单个组件“如何实现”其功能规格，提供足够信息以直接指导代码编写（人工或AI）。
    *   **UI组件层面**:
        *   **详细界面元素定义**: 为每个UI元素提供逻辑名、日文标签/文本、HTML控件类型或自定义组件类型、建议的英文ID、**确切的数据来源/绑定的前端状态变量名或props名**、以及**触发的具体事件处理器函数名**。
        *   **明确的Props和内部State**: 对于页面级组件和所有重要的可复用UI子组件，清晰定义其接收的所有Props（名称、数据类型、是否必需、用途描述）和管理的核心内部State（名称、数据类型、初始值、用途描述）。
        *   **详细的事件处理逻辑**: 对每个用户交互事件，按顺序详细描述其在前端的触发条件、事件处理器函数的名称、处理步骤（包括对内部状态的读写、参数校验、对其他内部辅助函数的调用）、调用的Server Action或API（含精确的参数构造和对成功/失败响应的处理逻辑）、以及最终的界面更新或用户反馈。
    *   **Server Action/后端逻辑层面 (例如 `app/lib/actions.ts` 中的函数)**:
        *   **清晰的函数签名**: 明确Server Action的名称、输入参数的结构和类型（例如，通过`FormData`传递的字段名和预期类型，或直接参数对象的TypeScript类型定义）、以及返回值的结构和类型。
        *   **核心处理步骤的结构化描述**: 使用中文自然语言，结合编号列表、流程判断、循环等逻辑结构，详细描述Server Action内部从接收参数到返回结果的完整处理流程。包括：参数解析与校验、用户身份/权限验证、调用数据服务层函数与数据库交互、与外部服务（如Azure Service Bus, Azure Blob Storage）的交互、构造任务消息、执行业务规则、错误捕获与处理（应引用 `docs/definitions/error-messages.md` 中定义的消息键）、以及构造最终返回给客户端的数据。**描述应聚焦于逻辑步骤和决策点，而非直接的代码语法。**
        *   **与外部服务的精确交互契约**: 当Server Action调用其他服务时，明确调用的函数/方法名、传递的关键参数对象结构、以及预期的成功/失败结果或副作用。
    *   **数据模型与数据库交互层面 (通过 `app/lib/data.ts` 封装)**:
        *   DDS中应描述 `app/lib/data.ts` (或类似数据服务模块) 中关键函数的职责、输入参数和返回值。
        *   对于数据库操作，应描述其意图、涉及的主要表和字段、关键的查询条件或更新逻辑（概念层面，非直接SQL）。
    *   **配置依赖**: 明确组件或Server Action依赖的`LOV`表配置项（引用 `docs/definitions/lov-definitions.md` 中定义的`code`值）和环境变量。
    *   **图表辅助**: 使用恰当抽象层级的Mermaid图（序列图、活动图等）辅助说明复杂交互和逻辑流程。
4.  **数据模型文档 (`docs/data-models/[model-name].md` 或 Prisma Schema)**: 提供与代码（Prisma Schema）**完全一致的**数据库表结构定义。对于`Lov`表，其详细值定义在 `docs/definitions/lov-definitions.md`。
5.  **API规范文档 (`docs/apis/openapi.v1.yaml` - 若存在HTTP API Routes)**: 包含HTTP API的完整精确契约定义。
6.  **错误消息定义文档 (`docs/definitions/error-messages.md`)**: 系统中所有用户可见错误消息的唯一、权威定义。组件DDS的错误处理部分应引用此文档中定义的消息ID或消息键。

### 4.3. 环境变量的描述规范
1.  **架构设计文档 (`docs/architecture/system-architecture.md`)**: 对核心环境变量**类别**进行**高层概述**，并**必须明确指出**详尽列表参考专门的环境变量指南 (`docs/guides/environment-variables.md`)。
2.  **环境变量指南 (`docs/guides/environment-variables.md`)**: 项目中所有环境变量的**唯一、权威的详细列表**。
3.  **组件技术实现文档 (`docs/components/.../[component-name].md` - 第3部分)**: 在其“3.8 配置项 (Configuration)”小节中，**仅列出与当前组件直接相关、且对其行为有显著影响的关键环境变量名称及其作用**，并**链接回**全局的环境变量指南。

### 4.4. 术语准确性与获取 (Terminology Accuracy and Sourcing)
1.  **官方术语优先**: 必须优先使用官方英文全称。
2.  **遵循项目术语表**: 严格遵循 `docs/definitions/glossary.md`。
3.  **AI助手的主动求证责任**: AI助手在不确定时**必须**主动检索官方文档或权威来源进行求证，并将结果和推荐用法提交用户确认，**严禁猜测**。

### 4.5. 文档的独立性、简洁性与拆分策略 (Self-Containment, Conciseness, and Splitting Strategy)
1.  **内部自洽与规范引用**: 文档应在 `docs/` 目录内部自洽，通过规范的内部交叉引用关联信息。
2.  **避免不必要的冗余**: 通过链接引用已清晰阐述的概念，而非大段复制。
3.  **选择最清晰的表达**: 在整个文档库中对同一概念保持一致、标准的表达。
4.  **复杂组件的文档拆分**:
    *   对于功能复杂的主组件DDS，应将其核心功能保留在主文档中，将其包含的、逻辑上相对独立的复杂子功能或交互流程的详细技术设计**拆分到独立的Markdown子文档**中（例如，存放在主组件文档所在目录的下一级子目录如 `tasks/`）。
    *   主组件文档应在适当位置清晰地链接到这些子功能详细设计文档。
    *   **拆分原则**: 当一个组件的DDS（第3部分）内容因包含多个复杂子流程而显得过长（例如，预估超过800-1000行Markdown）、难以维护或降低AI处理效率时，应考虑拆分。
    *   **子文档结构**: 每个拆分出的子功能DDS文档也应遵循组件文档的基本结构。

## 5. 输出格式与交付规范 (Output Format and Delivery Standards)

### 5.1. Markdown代码块输出
所有AI生成的、旨在被用户直接复制并整合到目标文档中的**正式文本内容**，都**必须**完整地、无任何额外解释性或对话性文字地输出在标准的Markdown代码块 (```) 中。代码块内部不应包含AI的解释或提问。

### 5.2. Markdown表格语法
当需要输出表格时，必须使用标准、规范的GitHub Flavored Markdown (GFM) 表格语法。单元格内需要换行的内容使用HTML的 `<br>` 标签。

### 5.3. Mermaid图表规范与抽象层级
1.  **语法正确性**: AI助手在生成Mermaid图表前，**必须**通过有效的验证手段确保其语法正确且能够成功渲染。
2.  **图表类型与用途的恰当选择与抽象层级**:
    *   **高层业务流程图 (FS部分的 `2.1 主要流程`)**: 使用 `graph TD` 或 `flowchart`。**必须保持高度抽象**，聚焦用户与系统的主要交互步骤和宏观响应。参与者通常为“用户”和“系统”。**严禁**暴露内部实现细节。
    *   **详细交互序列图 (DDS部分的 `3.4`)**: 使用 `sequenceDiagram`。用于清晰展示**不同逻辑层或物理边界之间**的消息流。**合规的参与者**包括：外部Actor（用户）、前端应用程序（整体）、Next.js应用服务器、数据库服务器、外部服务平台（如Azure Service Bus）。**严禁**将内部函数或UI组件作为独立参与者。
    *   **(可选) 活动图或细化流程图 (DDS部分的 `3.6`)**: 可用于更详细地可视化复杂函数或Server Action的内部逻辑流程。
3.  **图表的清晰性与可读性**:
    *   节点和消息的文本描述应简洁、准确（中文）。
    *   **对于较长的消息或节点文本，必须使用HTML的 `<br>` 标签进行手动换行，以控制其显示宽度或高度，避免图表整体过于拉伸而导致关键元素过小不易阅读。** (AI助手在生成图表时应主动考虑此点。)
    *   合理使用泳道（适用于序列图）、分组（适用于流程图的`subgraph`）、注释（`Note over/right of/left of`）等Mermaid特性增强图表结构和理解性。
    *   避免图表过于拥挤和复杂，对于非常复杂的流程，应考虑是否可以将其拆分为多个关注不同方面的、更小更聚焦的图表。
### 5.4. 文件与目录命名
所有文件名和目录名使用**英文小写字母**，单词间通过**连字符 (`-`)** 连接 (kebab-case)。文件名应清晰反映其内容主题。

## 6. 协作流程与迭代 (Collaboration Process and Iteration)

### 6.1. 分阶段交付与审阅 (Phased Delivery and Review)
1.  **明确任务与范围**: 用户清晰定义任务目标、范围、输入源。
2.  **小批量、高频率的迭代**: AI避免一次性生成过长内容，将内容拆分为逻辑完整的小块交付。
3.  **及时的用户审阅与反馈**: 用户尽快审阅，提供具体、可操作的反馈。
4.  **确认后再进行下一步**: AI基于反馈修正当前内容，获用户初步认可后再继续。

### 6.2. 上下文记忆与应用 (Contextual Memory and Application by AI)
1.  **持续记忆与应用规范**: AI被期望能持续记忆和应用所有已确认的规范、术语、格式及用户反馈。
2.  **避免重复提醒**: 用户不应需对同一问题反复提醒。
3.  **明确重申关键约束 (必要时)**: 用户可在开始新阶段任务时，简要重申关键规范。
4.  **利用结构化文档“固化”记忆**: 本指南及项目其他规范文档是AI“记忆”的外部辅助。

### 6.3. 处理需求变更与文档更新 (Handling Requirement Changes and Document Updates)
1.  **明确变更来源与范围**。
2.  **评估对现有文档的连锁影响**。
3.  **AI辅助更新**: 用户提供变更详情，指示AI更新受影响的文档。
4.  **确保一致性**: 更新后必须交叉检查所有受影响文档。
5.  **版本控制**: 所有更改通过Git进行版本控制。

## 7. 经验教训与最佳实践 (Lessons Learned and Best Practices from JCS Endpoint Project Collaboration)

### 7.1. 关于SSoT原则的挑战与应对
*   **经验**: AI助手初期可能倾向于直接引用原始规格书片段。
*   **最佳实践**: 反复强调信息内化至`docs/`；严禁直接文件依赖`docs-delivery/`；迁移指南作为映射桥梁；人工审查关键点。

### 7.2. 关于环境变量描述层级的校准过程
*   **经验**: AI对环境变量描述粒度把握不准。
*   **最佳实践**: 建立分层描述规范（架构文档高层概述，指南文档详尽列表，组件文档按需关联并链接回指南）；清晰指令与反馈。

### 7.3. 关于AI上下文记忆与指令一致性
*   **经验**: 长时间复杂任务中，AI可能“忘记”早期约定。
*   **最佳实践**: 结构化规范文档作为“外部记忆”；关键原则适时重申；分阶段小步快跑缩短反馈周期；具体化反馈。

### 7.4. 关于分阶段交付与大型文档的整体性保持
*   **经验**: 一次性生成大型文档效率低且易错。分阶段交付时需注意整体性。
*   **最佳实践**: 先确立整体大纲；持续进行整体性检查；(补充) 对于复杂组件DDS，尽早实施文档拆分策略（参见4.5.4节）。最终进行完整通读和整合审查。

### 7.5. 关于术语和专业表达的求证
*   **经验**: AI在非常细分的专业术语或特定语言地道表达上可能不准确。
*   **最佳实践**: 赋予AI求证责任和能力；用户作为最终裁决者；维护并优先参考项目术语表 (`docs/definitions/glossary.md`)。

### 7.6. DDS“与代码一一对应”的实现策略与挑战
*   **核心要求回顾**: DDS需提供足够精确和详尽的信息，以无歧义地指导代码实现。
*   **对策与实践总结**:
    1.  **明确“一一对应”的粒度与范围**: UI组件层面应详细到Props、核心内部State、事件处理器名称及其主要逻辑步骤。Server Action/后端逻辑层面应明确函数签名、核心处理步骤的结构化中文描述（含校验、业务规则、外部服务调用契约、错误处理分支）。数据模型层面需与Prisma Schema完全一致。图表（高层流程图与详细序列图）需有恰当的抽象层级。
    2.  **消除模糊性，追求明确具体**: DDS正文中最大限度避免不确定描述，所有设计决策和规格参数均应明确。未定事宜标记`[TBD]`并推动明确。
    3.  **结构化与模块化文档**: 复杂组件的DDS应拆分为主文档和多个子功能详细设计文档。
    4.  **迭代验证与对齐**: DDS编写是迭代过程，需与项目进展、代码实现、团队理解持续对齐。
*   **挑战**: 平衡设计与实现（DDS非代码复制）；维护DDS与代码的同步；确保AI准确理解和生成符合要求的DDS内容及代码建议。

### 7.7. Mermaid图表协作的经验
*   **反复出现的问题**: AI助手在生成Mermaid图时，多次出现语法错误或参与者定义不当。
*   **关键教训与改进措施**:
    *   **AI必须进行语法自查与渲染测试**: AI助手在输出Mermaid代码前，必须承担验证责任。
    *   **用户提供清晰反馈**: 用户提供渲染器报错信息对AI定位问题非常有帮助。
    *   **从简到繁，逐步迭代**: 先确保骨架正确，再添加细节。
    *   **严格遵守参与者定义规范与抽象层级**: 序列图的参与者必须是高层逻辑/物理单元；高层流程图应聚焦用户与系统宏观交互。
    *   **学习正确示例**: AI应能从用户提供的、可成功渲染的Mermaid示例中学习。

## 8. 附录 (Appendices) - 可选

### 8.1. AI提示词参考模板 (Sample AI Prompt Templates)

*   **模板1: 为新组件创建FS驱动部分文档**
    ```
    AI助手角色：[参照本文档2.1节定义的角色]
    任务：为名为“[组件中文名] ([Component English Name])”的新组件创建初始的Markdown文档框架。
    输出文件：`docs/components/[ui 或 services]/[component-english-name].md`
    信息来源：请参考《機能仕様書》的第 X.Y 章节“[对应FS章节名]”的描述 (如果FS未完全迁移，可提供相关文本片段)。
    遵循规范：请严格按照《AI协作与文档规范指南》中定义的组件文档模板，填充第1部分（概要）和第2部分（功能规格）的内容。确保所有Mermaid高层流程图的抽象层级正确。
    语言：所有描述性文本使用简体中文。技术术语使用英文。
    关键点：确保覆盖目的、主要流程（Mermaid）、业务规则、前提条件、制约事项、错误处理概述和相关功能参考。
    输出格式：所有内容在Markdown代码块中提供，无不必要缩进。
    ```

*   **模板2: 将详细设计内容填充到组件DDS部分 (满足“与代码一一对应”)**
    ```
    AI助手角色：[同上]
    任务：为组件文档 `docs/components/[...]/[component-name].md` (或其拆分出的子功能DDS文档) 填充第3部分“技术设计与实现细节”。
    信息来源：《詳細設計書》Excel文件的“[对应Sheet页名称]”部分（可提供相关表格截图或文本内容），以及以下项目文件：
        - `docs/architecture/system-architecture.md`
        - `docs/apis/openapi.v1.yaml` (若涉及HTTP API Route)
        - `docs/data-models/` 下的相关数据模型 (`.md`文件或Prisma Schema)
        - `docs/definitions/lov-definitions.md` (值列表定义)
        - `docs/definitions/error-messages.md` (错误消息定义)
        - `apps/jcs-endpoint-nextjs/app/lib/definitions.ts` (TypeScript类型定义)
        - `apps/jcs-endpoint-nextjs/app/lib/actions.ts` (相关的Server Action签名或草案)
        - `apps/jcs-endpoint-nextjs/app/lib/data.ts` (相关的数据服务函数概念)
        - (任何相关的UI组件代码片段或页面结构)
    遵循规范：《AI协作与文档规范指南》(特别是4.2节关于DDS粒度、4.5.4节关于文档拆分、5.3节Mermaid图表规范、7.6节“与代码一一对应”实践)，以及《详细设计书(DDS)内容迁移至docs/目录指南》 (`docs/guides/detailed-design-migration-guide.md`)。
    具体要求：
    1.  完成3.1 技术栈与依赖。
    2.  完成3.2 详细界面元素定义 (表格形式，精确到数据来源/状态变量名、事件处理器名)。
    3.  完成3.3 详细事件处理逻辑 (结构化描述每一步，明确状态更新、参数构造、Server Action/API调用、响应处理)。
    4.  完成3.4 数据结构与API/Server Action交互 (包括前端状态定义、Server Action输入/输出结构定义、Mermaid序列图 - 确保参与者和抽象层级正确)。
    5.  完成3.5 数据库设计与访问详情 (描述通过数据服务模块的交互逻辑)。
    6.  完成3.6 关键后端逻辑/算法 (详细描述Server Action内部处理流程，确保错误处理引用`error-messages.md`中的消息键)。
    7.  完成3.7 错误处理详情 (表格形式，包含错误场景、引用自`error-messages.md`的消息ID或消息键、用户提示、内部处理)。
    8.  完成3.8 配置项 (引用`lov-definitions.md`中的LOV code、环境变量)。
    语言：简体中文描述，英文技术术语，日文界面文本。
    输出格式：所有内容在Markdown代码块中提供，无不必要缩进。Mermaid图表需预先验证可渲染。
    ```

### 8.2. 关键决策记录链接 (Links to Key ADRs related to Documentation)
*   (示例) `docs/architecture/adrs/001-adopt-markdown-for-sot-documentation.md`
*   (示例) `docs/architecture/adrs/002-standardize-on-kebab-case-for-filenames.md`
*   (示例) `docs/architecture/adrs/003-dds-component-splitting-strategy.md`
*   (示例) `docs/architecture/adrs/004-centralize-definitions-in-docs-definitions-directory.md`
