---
description: 
globs: 
alwaysApply: true
---
# Project Structure Guidelines (for apps/jcs-endpoint-nextjs)

This document outlines the primary directory structure of the `apps/jcs-endpoint-nextjs` application (using Next.js App Router). Adhering to this structure ensures consistency and maintainability when developing with AI assistance.

**AI Primary Input for Detailed Design & Implementation:**
For detailed component design, Server Action logic, API interactions, and specific implementation details, the AI **MUST** primarily refer to the documentation within the Monorepo's `docs/` directory, especially:
*   `docs/components/.../` (for UI components, page logic, and associated Server Actions)
*   `docs/apis/openapi.v1.yaml` (for HTTP API Route contracts, if applicable)
*   `apps/jcs-endpoint-nextjs/prisma/schema.prisma` (as the Single Source of Truth for database structure)
*   `docs/data-models/` (for business context and explanations of the Prisma schema)
*   `docs/definitions/` (for error messages, LOV definitions, glossary)
*   `apps/jcs-endpoint-nextjs/app/lib/definitions.ts` (for core TypeScript types and constants)

The Markdown content within the `docs/` directory is primarily in **Chinese (Simplified)**, while all directory and file names are in **English (kebab-case)**.

## `apps/jcs-endpoint-nextjs/` Root Directory Structure

*   **`app/`**: Core application directory (Next.js App Router).
    *   **`api/`**: Contains all backend API Route handlers (e.g., `app/api/login/route.ts`).
        *   Organized by endpoint path (e.g., `app/api/notifications/system/route.ts`).
        *   Dynamic segments are used (e.g., `app/api/passwords/[id]/route.ts`).
    *   **`dashboard/`**: Protected routes and components for the authenticated user dashboard.
        *   Contains feature-specific subdirectories (e.g., `app/dashboard/servers/`, `app/dashboard/manuals/`).
        *   Each feature typically has a `page.tsx`, and may include `layout.tsx`, `error.tsx`.
        *   Dynamic routes for file downloads or specific item views (e.g., `app/dashboard/manuals/[serialNo]/[fileName]/route.ts`).
    *   **`callback/`**: Handles Keycloak authentication callback (`app/callback/page.tsx`).
    *   **`hooks/`**: Custom React hooks (e.g., `app/hooks/use-license.ts`). Files use `use-feature-name.ts` convention.
    *   **`lib/`**: Core shared logic, utilities, data fetching, configurations.
        *   `actions.ts`: Server Actions definitions.
        *   `data.ts`: Server-side data fetching logic (interacts with Prisma).
        *   `definitions.ts`: Core TypeScript type definitions and project-wide constants.
        *   `logger.ts`: Winston logger configuration.
        *   `portal-error.ts`: Custom error classes and handlers.
        *   `prisma.ts`: Prisma Client instantiation.
        *   `session.ts`: Iron Session management logic.
        *   `utils.ts`: General utility functions.
    *   **`login/`**: Login page components (`app/login/page.tsx`).
    *   **`ui/`**: Reusable UI components.
        *   Organized with general components directly under `ui/` (e.g., `Breadcrumb.tsx`, `Header.tsx`, `Modal.tsx` variants).
        *   Feature-specific UI components are in subdirectories (e.g., `app/ui/servers/table.tsx`, `app/ui/manuals/table.tsx`).
    *   `layout.tsx`: Root layout for the application.
    *   `global-error.tsx`: Global error boundary.
    *   `favicon.ico`: Application favicon.
*   **`prisma/`**: Contains Prisma schema and related files.
    *   `schema.prisma`: The Single Source of Truth for database structure.
*   **`public/`**: Static assets (images, etc.).
*   **`styles/`**: Global CSS files (e.g., `globals.css`).
*   **`cypress/`**: Cypress test files.
    *   `component/`: Component tests.
    *   `e2e/`: End-to-end tests.
    *   `fixtures/`: Test fixture data (JSON files).
    *   `support/`: Cypress support files and custom commands.
*   **`__tests__/`**: Jest test files.
    *   Organized to mirror the `app/` structure for testing API routes, hooks, and library functions.
*   **`types/`**: (e.g., `types/next-auth.d.ts`) Custom global type declarations.
*   **Configuration Files (Root of `apps/jcs-endpoint-nextjs/`):**
    *   `.eslintrc.json`, `.gitignore`, `next.config.js`, `package.json`, `postcss.config.js`, `prettier.config.js`, `tailwind.config.js`, `tsconfig.json`, `jest.config.js`, `jest.setup.js`, `middleware.ts`.

## Monorepo Context (`jcs-endpoint-monorepo/`)

The `apps/jcs-endpoint-nextjs/` application is part of a larger Monorepo (`jcs-endpoint-monorepo/`). While this application is self-contained for its deployment, be aware of:
*   **`docs/` Directory (Monorepo Root):** As stated above, this is the primary source for detailed design information. AI **MUST** be guided to use these documents.
*   **No External Shared Packages (`packages/`):** Code reuse is internal to this Next.js application or other applications/automation units within the Monorepo. There are no shared packages at the Monorepo root level that this Next.js app consumes directly via typical package management.
*   **Independent Git for Deployment:** `apps/jcs-endpoint-nextjs/` has its own `.git` repository solely for local Git deployment to Azure App Service. This is separate from the main Monorepo Git.

## General Naming Conventions (within `apps/jcs-endpoint-nextjs/`)

*   **Components (React):** PascalCase (e.g., `Header.tsx`, `ServersTable.tsx`).
*   **Hooks:** `use` prefix, kebab-case (e.g., `use-license.ts`).
*   **API Route Files:** `route.ts`.
*   **Page Files:** `page.tsx`.
*   **Layout Files:** `layout.tsx`.
*   **Error Boundary Files:** `error.tsx`, `global-error.tsx`.
*   **Library/Utility Files:** Generally kebab-case or camelCase (e.g., `utils.ts`, `portal-error.ts`).
*   **Directory Names:** Generally kebab-case (e.g., `audit-login-logs`, `support-files`).

## Guidelines for Adding New Code

*   **New API Endpoints:**
    *   Create directories under `app/api/` matching the URL. Place `route.ts` inside.
    *   Refer to `docs/apis/openapi.v1.yaml` (if applicable) and relevant component design docs in `docs/components/`.
    *   Use `app/lib/actions.ts` for Server Action logic if invoked by the API, or implement logic directly if it's a pure API.
*   **New Dashboard Pages/Features:**
    *   Create a directory under `app/dashboard/your-feature-name/`. Add `page.tsx`.
    *   UI components specific to this feature go into `app/ui/your-feature-name/`.
    *   **Crucially, consult the corresponding detailed design document in `docs/components/your-feature-module/` for specifications.**
*   **New UI Components:**
    *   General reusable components: `app/ui/`.
    *   Feature-specific: `app/ui/feature-name/`.
    *   **Refer to component design documents in `docs/components/` for UI element definitions, props, state, and event handling logic.**
*   **New Custom Hooks:** `app/hooks/`.
*   **New Server Actions:** Add to `app/lib/actions.ts`. **Design and contract MUST be documented in `docs/components/...` or `docs/components/actions/`.**
*   **New Utility Functions:**
    *   Generic: `app/lib/utils.ts`.
    *   Domain-specific (but not a Server Action): Consider a new file in `app/lib/` (e.g., `app/lib/azure-storage-helpers.ts`).
*   **New Type Definitions:**
    *   Shared/Core types: `app/lib/definitions.ts`.
    *   Component-local props/state: Define within the component file.

## Code Modularity and File Size (General Guideline)

*   **Strive for Modularity:** Code **MUST** be organized into logical modules with clear separation of concerns. This is a primary principle.
*   **File Size Consideration:** As a general guideline to promote modularity and readability, aim to keep individual code files (especially those containing complex logic or multiple components/functions) concise. Files significantly exceeding **approximately 500 lines of code** should be reviewed for potential refactoring into smaller, more focused modules. This is not an absolute hard limit for all file types but a strong recommendation for maintaining code clarity.
*   **AI Assistance in Refactoring:** When AI generates or modifies code that results in overly large files, it should be prompted to suggest or perform refactoring into smaller modules, ensuring logical cohesion is maintained.

---

**NOTE TO CURSOR:** This application (`apps/jcs-endpoint-nextjs`) is the primary focus when these rules are active. Always prioritize information from the Monorepo's `docs/` directory for detailed specifications, especially `docs/components/`. Use path aliases from `tsconfig.json` (e.g., `@/app/...`, `@/lib/...`). The `jcs-endpoint-nextjs` directory structure provided is authoritative.

---