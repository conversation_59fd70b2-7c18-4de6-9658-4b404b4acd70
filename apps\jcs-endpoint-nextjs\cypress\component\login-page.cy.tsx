import React from "react";
import Login from "@/app/login/page";

describe("ログイン画面のテスト", () => {
  it("ログインフォームが表示されることを確認する", () => {
    cy.intercept("GET", "/api/notifications/system", {
      statusCode: 200,
      body: [],
    });

    // Loginコンポーネントをマウント
    cy.nextMount(<Login />);
    // 「ログイン」というタイトルが表示されることを確認
    cy.get("h1:first").should("have.text", "JP1 Cloud Service");
    cy.get("h1:last").should("have.text", "エンドポイント管理");
    cy.contains("お知らせ");
    cy.contains("お知らせはありません。");
  });
});
