/**
 * @fileoverview データクリーンアップスクリプト
 * @description 開発環境の種子データを安全にクリーンアップする
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

// 环境变量加载
import * as dotenv from 'dotenv';
dotenv.config();

import { getPrismaClient, connectDatabase, disconnectDatabase, getDatabaseStats } from './utils/database-simple';

/**
 * 全ての種子データをクリーンアップする
 */
async function cleanupAllData(): Promise<void> {
  console.log('🧹 データクリーンアップを開始します');
  
  const prisma = getPrismaClient();
  
  try {
    // 外部キー制約を考慮した削除順序
    console.log('関連データを削除中...');
    await prisma.planSupport.deleteMany({});
    await prisma.planProvidedFile.deleteMany({});
    await prisma.planManual.deleteMany({});
    await prisma.planProduct.deleteMany({});
    await prisma.licensePlan.deleteMany({});
    
    console.log('メインデータを削除中...');
    await prisma.operationLog.deleteMany({});
    await prisma.task.deleteMany({});
    await prisma.server.deleteMany({});
    await prisma.notification.deleteMany({});
    await prisma.license.deleteMany({});
    await prisma.plan.deleteMany({});
    await prisma.productMedia.deleteMany({});
    await prisma.productManual.deleteMany({});
    await prisma.providedFile.deleteMany({});
    await prisma.supportFile.deleteMany({});
    await prisma.lov.deleteMany({});
    
    console.log('✅ データクリーンアップが完了しました');
  } catch (error) {
    console.error('❌ データクリーンアップに失敗しました:', error);
    throw error;
  }
}

/**
 * 統計情報を表示する
 */
async function showStats(): Promise<void> {
  try {
    const stats = await getDatabaseStats();
    console.log('=== データベース統計情報 ===');
    console.log(`ライセンス: ${stats.licenseCount}件`);
    console.log(`サーバー: ${stats.serverCount}件`);
    console.log(`タスク: ${stats.taskCount}件`);
    console.log(`LOV: ${stats.lovCount}件`);
    console.log(`プラン: ${stats.planCount}件`);
    console.log(`製品マニュアル: ${stats.productManualCount}件`);
    console.log(`製品媒体: ${stats.productMediaCount}件`);
    console.log(`提供ファイル: ${stats.providedFileCount}件`);
    console.log(`サポートファイル: ${stats.supportFileCount}件`);
    console.log(`操作ログ: ${stats.operationLogCount}件`);
    console.log('--- Plan関連データ ---');
    console.log(`プラン-製品関連: ${stats.planProductCount}件`);
    console.log(`プラン-マニュアル関連: ${stats.planManualCount}件`);
    console.log(`プラン-提供ファイル関連: ${stats.planProvidedFileCount}件`);
    console.log(`プラン-サポート関連: ${stats.planSupportCount}件`);
    console.log(`ライセンス-プラン関連: ${stats.licensePlanCount}件`);
    console.log(`取得時刻: ${stats.timestamp}`);
    console.log('========================');
  } catch (error) {
    console.error('❌ 統計情報の取得に失敗しました:', error);
  }
}

/**
 * 環境の妥当性をチェックする
 */
function validateEnvironment(): void {
  const dbUrl = process.env.MSSQL_PRISMA_URL;
  
  if (!dbUrl) {
    throw new Error('環境変数 MSSQL_PRISMA_URL が設定されていません');
  }

  if (!dbUrl.includes('database=dev')) {
    throw new Error('このツールは開発環境(database=dev)でのみ実行できます');
  }

  console.log('✅ 環境チェックが完了しました (開発環境)');
}

/**
 * メイン実行関数
 */
async function main(): Promise<void> {
  const args = process.argv.slice(2);
  
  try {
    // 環境チェック
    validateEnvironment();
    
    // データベース接続
    await connectDatabase();
    
    if (args.length === 0 || args[0] === 'all') {
      // 全データクリーンアップ
      await cleanupAllData();
    } else if (args[0] === 'stats') {
      // 統計情報表示
      await showStats();
    } else {
      console.log('使用方法:');
      console.log('  npm run clean          # 全データをクリーンアップ');
      console.log('  npm run clean stats    # 統計情報を表示');
      console.log('  npx tsx cleanup.ts     # 全データをクリーンアップ');
      console.log('  npx tsx cleanup.ts stats # 統計情報を表示');
    }
  } catch (error) {
    console.error('実行中にエラーが発生しました:', error);
    process.exit(1);
  } finally {
    await disconnectDatabase();
  }
}

// スクリプトとして直接実行された場合のみmainを実行
if (require.main === module) {
  main();
}
