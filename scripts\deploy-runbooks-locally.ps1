# 需要Azure PowerShell模块
# Install-Module -Name Az -Scope CurrentUser -Repository PSGallery -Force

param(
    [Parameter(Mandatory=$true)]
    [string]$ResourceGroupName,
    
    [Parameter(Mandatory=$true)]
    [string]$AutomationAccountName
)

# 确保已登录Azure
$context = Get-AzContext
if (-not $context) {
    Write-Host "请先登录Azure..."
    Connect-AzAccount
}

# 部署模块
Write-Host "正在部署PowerShell模块..."
$modulesPath = "..\automation\modules\JcsAutomationTools"
if (Test-Path $modulesPath) {
    $moduleFiles = Get-ChildItem -Path $modulesPath -Filter "*.ps*"
    foreach ($file in $moduleFiles) {
        $moduleName = [System.IO.Path]::GetFileNameWithoutExtension($file.Name)
        Write-Host "部署模块: $moduleName"
        Import-AzAutomationModule -ResourceGroupName $ResourceGroupName `
            -AutomationAccountName $AutomationAccountName `
            -Name $moduleName `
            -ContentLink $file.FullName `
            -Force
    }
}

# 部署Runbooks
Write-Host "正在部署Runbooks..."
$runbooksPath = "..\automation\runbooks"
$runbookFiles = Get-ChildItem -Path $runbooksPath -Filter "*.ps1" -Recurse
foreach ($file in $runbookFiles) {
    $runbookName = [System.IO.Path]::GetFileNameWithoutExtension($file.Name)
    Write-Host "部署Runbook: $runbookName"
    
    Import-AzAutomationRunbook -ResourceGroupName $ResourceGroupName `
        -AutomationAccountName $AutomationAccountName `
        -Name $runbookName `
        -Path $file.FullName `
        -Type PowerShell `
        -Force
    
    Publish-AzAutomationRunbook -ResourceGroupName $ResourceGroupName `
        -AutomationAccountName $AutomationAccountName `
        -Name $runbookName
}

Write-Host "部署完成！" 