/**
 * @file page-size.tsx
 * @description 
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

// ページ数コンポーネント
export default function PageSize() {
  const searchParams = useSearchParams();
  const { replace } = useRouter();
  const pathname = usePathname();
  const [size, setSize] = useState("");

  useEffect(() => {
    setSize(searchParams.get("size")?.toString() || "10");
  }, [searchParams]);

  const handleChange = (pageSize: string) => {
    const params = new URLSearchParams(searchParams);

    setSize(pageSize);
    params.set("size", pageSize);
    params.set("page", "1");
    params.delete("sort");
    params.delete("order");
    replace(`${pathname}?${params.toString()}`);
  };

  return (
    <div className="flex justify-center">
      <div className="flex items-center">
        <label
          htmlFor="small"
          className="ml-2 text-sm font-medium text-gray-900 dark:text-white"
        >
          行数/ページ:
        </label>
        <select
          id="small"
          value={size}
          className="ml-2 h-9 rounded-lg border border-gray-300 bg-gray-50 p-2 text-sm text-gray-900 focus:border-gray-300 focus:ring-gray-300"
          onChange={(e) => {
            handleChange(e.target.value);
          }}
        >
          <option value="10">10</option>
          <option value="30">30</option>
          <option value="50">50</option>
        </select>
      </div>
    </div>
  );
}
