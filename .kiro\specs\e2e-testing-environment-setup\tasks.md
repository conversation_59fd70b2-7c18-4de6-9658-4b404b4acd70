# 实施计划

- [x] 1. 创建和配置测试环境启动脚本
  - 编写一个能够并行启动所有三个应用的脚本
  - 确保脚本能够在退出时清理所有进程
  - _需求: 1.1, 1.4, 5.1, 5.4_

- [x] 2. 配置 Next.js 应用的测试环境
  - [x] 2.1 创建 Next.js 应用的测试环境配置文件
    - 创建 `.env.test` 文件，包含测试专用的环境变量
    - 确保测试环境变量与开发环境分离
    - 配置测试专用的数据库连接
    - _需求: 2.1, 2.2, 2.3_
  - [x] 2.2 更新 Next.js 启动配置
    - 修改启动脚本以使用测试环境变量
    - 确保 Next.js 应用能够在测试模式下正确启动
    - _需求: 1.2, 2.2_

- [x] 3. 配置 Azure Functions 的测试环境
  - [x] 3.1 配置标准 Azure Functions 的测试设置
    - 更新 `local.settings.json` 以包含测试专用配置
    - 确保函数应用能够连接到测试数据库
    - _需求: 1.2, 2.1, 2.4_
  - [x] 3.2 配置长时运行 Azure Functions 的测试设置
    - 更新 `local.settings.json` 以包含测试专用配置
    - 确保函数应用能够连接到测试数据库
    - _需求: 1.2, 2.1, 2.4_

- [x] 4. 实现认证辅助模块
  - 完善 `auth.helper.ts` 以支持直接注入认证 Cookie
  - 实现支持不同用户角色的认证功能
  - 确保认证状态在测试会话中持续存在
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [x] 5. 实现服务器列表页面测试
  - [x] 5.1 实现测试数据管理
    - 编写测试数据清理函数
    - 实现测试服务器记录创建逻辑
    - 确保测试完成后删除所有测试数据
    - _需求: 3.1, 3.2, 3.3_
  - [x] 5.2 实现服务器列表页面测试用例
    - 编写访问服务器列表页面的测试
    - 验证服务器名称、类型和管理 URL 的正确显示
    - 确保测试能够验证所有关键功能
    - _需求: 3.4, 3.5_

- [x] 6. 配置测试报告和调试工具
  - 配置 Playwright 以生成详细的 HTML 报告
  - 设置测试失败时的跟踪信息捕获
  - 确保测试报告包含足够的调试信息
  - _需求: 5.3, 5.5_

- [x] 7. 创建测试执行文档
  - 编写详细的测试环境设置指南
  - 提供测试执行的步骤说明
  - 包含常见问题和解决方案
  - _需求: 5.2_

- [x] 8. 验证完整测试流程
  - 验证环境启动脚本能够正确启动所有服务
  - 确认测试能够在本地环境中执行而无需额外配置
  - 验证测试报告生成和进程清理功能
  - _需求: 1.3, 5.2, 5.3, 5.4_