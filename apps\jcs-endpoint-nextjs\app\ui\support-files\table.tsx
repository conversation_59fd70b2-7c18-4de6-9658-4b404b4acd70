/**
 * @file table.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

import { ServerDataSupportFiles } from "@/app/lib/data/support-files";
import Thead from "../thead";

// サポートファイルテーブルコンポーネント
export default async function SupportFilesTable({
  filter,
  page,
  size,
  sort,
  order,
  preferSort,
}: {
  filter: string;
  page: number;
  size: number;
  sort: "updatedAt" | "title" | "productName" | "importance" | "publishedAt";
  order: "asc" | "desc";
  preferSort:
    | "updatedAt"
    | "title"
    | "productName"
    | "importance"
    | "publishedAt";
}) {
  const files = await ServerDataSupportFiles.fetchFilteredSupportFiles(
    filter,
    size,
    page,
    sort,
    order,
    preferSort,
  );

  return (
    <table className="whitespace-nowrap w-full text-left text-sm text-gray-500">
      <Thead
        headers={[
          { key: "updatedAt", label: "最終更新日" },
          { key: "title", label: "タイトル" },
          { key: "productName", label: "製品" },
          { key: "importance", label: "重要度" },
          { key: "publishedAt", label: "公開日" },
        ]}
        defaultOrder="updatedAt"
        defaultSort="desc"
      />
      <tbody>
        {files?.length !== 0 ? (
          files!.map((file) => (
            <tr key={file.id} className="border-b odd:bg-white even:bg-gray-50">
              <th
                scope="row"
                className="border-r text-gray-900 whitespace-nowrap px-6 py-4 font-medium"
              >
                {file.updatedAt}
              </th>
              <td className="border-r px-6 py-4">
                <a
                  target="_blank"
                  href={`support-files/${file.serialNo}/${file.fileName}`}
                  className="font-medium text-blue-600 hover:underline"
                >
                  {file.title}
                </a>
              </td>
              <td className="border-r px-6 py-4">{file.productName}</td>
              <td className="border-r px-6 py-4">{file.importance}</td>
              <td className="px-6 py-4">{file.publishedAt}</td>
            </tr>
          ))
        ) : (
          <tr>
            <td>
              <div className="p-4"></div>
            </td>
          </tr>
        )}
      </tbody>
    </table>
  );
}
