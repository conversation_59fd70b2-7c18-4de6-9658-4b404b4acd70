/**
 * @fileoverview Azure Service Bus関連サーバーアクションモジュール
 * @description
 * Service Busの指定キュー（TaskInputQueue）へタスク実行要求のメッセージを送信する機能を提供。
 * 送信失敗時の適切なエラー処理（EMEC0019）とリソース管理を実装。
 * ServiceBusClientのシングルトン管理によるリソース効率化。
 *
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { ServiceBusClient } from "@azure/service-bus";
import { DefaultAzureCredential, ManagedIdentityCredential } from "@azure/identity";
import { ENV, PORTAL_ERROR_MESSAGES } from "@/app/lib/definitions";

/**
 * Azure認証資格情報を環境に応じて取得する関数
 *
 * 認証方式の選択ロジック：
 * - 本番環境（Azure App Service/VM）：ManagedIdentityCredentialを使用
 * - ローカル開発環境（next dev）：DefaultAzureCredentialを使用
 * - ビルド時またはE2Eテスト環境：nullを返却（接続文字列使用を促す）
 *
 * @returns {TokenCredential | null} 適切な認証資格情報またはnull
 */
function getAzureCredential() {
  // 本番環境（Azure App Service/VM）でマネージドIDを使用
  // WEBSITE_SITE_NAMEはApp Serviceの標準環境変数
  if (process.env.WEBSITE_SITE_NAME) {
    return new ManagedIdentityCredential();
  }

  // ローカル開発時（next dev）のみDefaultAzureCredentialを使用
  if (process.env.NODE_ENV === 'development') {
    return new DefaultAzureCredential();
  }

  // ビルド時（NODE_ENV=production かつ Azure外）またはE2Eテスト環境では
  // nullを返却して接続文字列の使用を促す。これによりファイルシステムスキャンを回避。
  // E2Eテストは本地编译测试であり、接続文字列を使用する。
  return null;
}

/**
 * ServiceBusClientのシングルトンインスタンスを管理するクラス
 *
 * アプリケーション全体で1つのServiceBusClientのみを生成し、
 * 不要なリソース消費やメモリリークを防止する。
 * Azure Service Busとの接続を効率的に管理し、認証情報の再利用を可能にする。
 */
class ServiceBusClientSingleton {
  // シングルトンインスタンス
  private static instance: ServiceBusClient | null = null;

  /**
   * ServiceBusClientのインスタンスを取得する
   *
   * 初回呼び出し時に環境に応じた認証方式でServiceBusClientを生成し、
   * 以降の呼び出しでは同一インスタンスを返却する。
   *
   * 認証方式の選択ロジック：
   * - 本番環境（Azure App Service/VM）：ManagedIdentityCredentialを使用
   * - ローカル開発環境（next dev）：DefaultAzureCredentialを使用
   * - ビルド時またはE2Eテスト環境：接続文字列を使用（E2Eは本地编译测试）
   *
   * @returns {ServiceBusClient} シングルトンインスタンス
   * @throws 環境変数未設定時はEMEC0019エラー
   */
  static getInstance(): ServiceBusClient {
    if (!this.instance) {
      // 環境に応じた認証資格情報を取得
      const credential = getAzureCredential();

      if (credential) {
        // 認証資格情報が取得できた場合はNamespace Hostnameを使用
        if (!ENV.AZURE_SERVICEBUS_NAMESPACE_HOSTNAME) {
          throw new Error(PORTAL_ERROR_MESSAGES.EMEC0019);
        }
        this.instance = new ServiceBusClient(
          ENV.AZURE_SERVICEBUS_NAMESPACE_HOSTNAME,
          credential
        );
      } else {
        // 認証資格情報が取得できない場合（ビルド時など）は接続文字列を使用
        if (!ENV.AZURE_SERVICEBUS_CONNECTION_STRING) {
          throw new Error(PORTAL_ERROR_MESSAGES.EMEC0019);
        }
        this.instance = new ServiceBusClient(ENV.AZURE_SERVICEBUS_CONNECTION_STRING);
      }
    }
    return this.instance;
  }

  /**
   * アプリケーション終了時などにServiceBusClientを明示的にクローズする場合に使用。
   */
  static async close(): Promise<void> {
    if (this.instance) {
      await this.instance.close();
      this.instance = null;
    }
  }
}

/**
 * Azure Service Bus関連のアクションを提供するクラス
 *
 * タスクメッセージ構築・Service Bus (TaskInputQueue) へ送信、
 * 送信失敗時の補償処理とエラー応答返却（EMEC0019）、
 * リソースの適切な管理（sender のクローズ処理）を実装。
 *
 * Service Busへのメッセージ送信等の要求を一元的に受け付け、
 * サーバー側での厳格な入力検証・権限制御・補償処理を責任持って実施する。
 */
export class ServiceBusActions {
  /**
   * 指定されたキューにメッセージを送信する
   *
   * タスクIDを含むタスク実行要求メッセージを、環境変数 SERVICE_BUS_TASK_INPUT_QUEUE_NAME で指定されるService Busキューへ送信。
   * 送信に失敗した場合：EMEC0019「サーバの接続に失敗したため、タスクを{0}できませんでした...」を返却。
   * リソース管理：senderは必ずクローズしてリソースリーク防止。
   *
   * @param {string} queueName 送信先のキュー名（通常は TaskInputQueue）
   * @param {any} messageBody 送信するメッセージ本体（通常は { taskId: string }）
   * @returns {Promise<void>} 送信完了時はresolve
   * @throws Service Busのホスト名未設定、またはメッセージ送信失敗時はEMEC0019エラー
   */
  static async sendMessage(queueName: string, messageBody: any): Promise<void> {
    let sender;
    // ServiceBusClientはシングルトンから取得
    const sbClient = ServiceBusClientSingleton.getInstance();
    try {
      sender = sbClient.createSender(queueName);
      const message = {
        body: messageBody,
        contentType: "application/json",
      };
      await sender.sendMessages(message);
    } catch (err: any) {
      throw new Error(PORTAL_ERROR_MESSAGES.EMEC0019);
    } finally {
      // senderは必ずクローズする（リソースリーク防止）
      if (sender) {
        await sender.close();
      }
    }
  }
}
