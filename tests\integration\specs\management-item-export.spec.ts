/**
 * 管理项目导出功能测试
 * 验证管理项目定义导出任务的创建、执行和状态跟踪功能
 */

import { test, expect } from '@playwright/test';
import { loginAs, logout, UserRole } from '../support/auth.helper';
import {
  ServerType,
  TestServerConfig,
  cleanupAllTestServers,
  forceCleanupAllTestServers,
  createTestServer,
  createTestLicense,
  disconnectPrisma,
  prisma,
  generateTestLicenseId
} from '../support/server-data.helper';
import {
  interceptRefreshTokenRequests,
  clearNetworkInterceptors
} from '../support/network-interceptor.helper';
import { createSimpleSideEffectsAssertions } from '../support/simple-side-effects-assertions';
import { MockServerHelper } from '../support/mock-server-helper';
import { MockJobStatus } from '../support/azure-automation-mock-server';

/**
 * 关闭所有可能打开的模态框
 * @param page Playwright 页面对象
 */
async function closeAllModals(page: any) {
  // 多次尝试关闭模态框，因为有些模态框可能需要多次点击
  for (let attempt = 0; attempt < 3; attempt++) {
    // 尝试点击所有可见的 "Close modal" 按钮
    try {
      const closeButtons = await page.locator('button:has-text("Close modal")').all();
      for (const button of closeButtons) {
        if (await button.isVisible()) {
          await button.click();
          await page.waitForTimeout(300);
        }
      }
    } catch (error) {
      // 忽略错误，继续尝试其他方式
    }

    // 尝试点击所有可见的 "キャンセル" 按钮
    try {
      const cancelButtons = await page.locator('button:has-text("キャンセル")').all();
      for (const button of cancelButtons) {
        if (await button.isVisible()) {
          await button.click();
          await page.waitForTimeout(300);
        }
      }
    } catch (error) {
      // 忽略错误，继续尝试其他方式
    }

    // 尝试点击所有可见的 "閉じる" 按钮
    try {
      const closeJapaneseButtons = await page.locator('button:has-text("閉じる")').all();
      for (const button of closeJapaneseButtons) {
        if (await button.isVisible()) {
          await button.click();
          await page.waitForTimeout(300);
        }
      }
    } catch (error) {
      // 忽略错误，继续尝试其他方式
    }

    // 按 Escape 键
    try {
      await page.keyboard.press('Escape');
      await page.waitForTimeout(300);
    } catch (error) {
      // 忽略错误
    }

    // 检查是否还有模态框
    const remainingModals = await page.locator('[role="dialog"], .modal, [data-modal]').count();
    if (remainingModals === 0) {
      break;
    }
  }
}

/**
 * 全面清理测试数据，确保测试隔离
 * @param licenseId 许可证ID，用于隔离清理
 */
async function comprehensiveTestDataCleanup(licenseId: string) {
  try {
    console.log(`🧹 开始全面清理测试数据 (许可证: ${licenseId})`);

    // 1. 清理任务记录（包括所有状态的任务）
    const deletedTasks = await prisma.task.deleteMany({
      where: {
        OR: [
          { licenseId: licenseId },
          { id: { startsWith: 'test-' } }, // 清理所有测试任务
          { taskName: { contains: 'test' } }
        ]
      }
    });
    console.log(`✅ 清理了 ${deletedTasks.count} 个任务记录`);

    // 2. 清理容器并发状态记录
    const deletedContainerStatus = await prisma.containerConcurrencyStatus.deleteMany({
      where: {
        OR: [
          { targetVmName: { startsWith: 'test-' } },
          { targetContainerName: { startsWith: 'test-' } },
          { currentTaskId: { startsWith: 'test-' } }
        ]
      }
    });
    console.log(`✅ 清理了 ${deletedContainerStatus.count} 个容器状态记录`);

    // 3. 清理服务器记录
    const deletedServers = await prisma.server.deleteMany({
      where: {
        licenseId: licenseId
      }
    });
    console.log(`✅ 清理了 ${deletedServers.count} 个服务器记录`);

    // 4. 等待清理完成
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log(`✅ 全面清理完成 (许可证: ${licenseId})`);
  } catch (error) {
    console.error(`❌ 清理测试数据时出错:`, error);
    // 不抛出错误，避免影响测试执行
  }
}

/**
 * 安全地清理单个任务，包含异常处理
 * @param taskId 任务ID
 */
async function safeCleanupTask(taskId: string) {
  try {
    await prisma.task.delete({ where: { id: taskId } });
    console.log(`✅ 已清理测试任务: ${taskId}`);
  } catch (error) {
    console.log(`⚠️ 清理测试任务失败: ${taskId}`, error instanceof Error ? error.message : String(error));
    // 不抛出错误，避免影响测试执行
  }
}

/**
 * 安全地清理容器状态记录，包含异常处理
 * @param vmName VM名称
 * @param containerName 容器名称
 */
async function safeCleanupContainerStatus(vmName: string, containerName: string) {
  try {
    const deleted = await prisma.containerConcurrencyStatus.deleteMany({
      where: {
        targetVmName: vmName,
        targetContainerName: containerName
      }
    });
    console.log(`✅ 已清理 ${deleted.count} 个容器状态记录`);
  } catch (error) {
    console.log(`⚠️ 清理容器状态记录失败:`, error instanceof Error ? error.message : String(error));
    // 不抛出错误，避免影响测试执行
  }
}

// 测试许可证ID - 每个测试套件使用独立的许可证ID，确保完全隔离
let TEST_LICENSE_ID: string;

// 测试服务器配置生成函数 - 确保使用正确的许可证ID
function createTestServersConfig(licenseId: string): TestServerConfig[] {
  return [
    {
      name: 'e2e-test-general-manager-export',
      type: ServerType.GENERAL_MANAGER,
      url: 'https://example.com/gm-server-export',
      licenseId: licenseId,
      // 添加任务执行所需的必要字段
      azureVmName: 'test-vm-gm-export',
      dockerContainerName: 'test-container-gm-export',
      hrwGroupName: 'test-hrw-group-gm-export'
    },
    {
      name: 'e2e-test-relay-manager-export',
      type: ServerType.RELAY_MANAGER,
      url: 'https://example.com/relay-server-export',
      licenseId: licenseId,
      // 添加任务执行所需的必要字段
      azureVmName: 'test-vm-relay-export',
      dockerContainerName: 'test-container-relay-export',
      hrwGroupName: 'test-hrw-group-relay-export'
    }
  ];
}

test.describe('管理项目导出功能测试', () => {
  // 测试服务器配置 - 在describe内部定义，确保使用正确的许可证ID
  let testServers: TestServerConfig[];

  // 在所有测试开始前创建测试许可证
  test.beforeAll(async () => {
    // 为每个测试套件生成独立的许可证ID，确保完全隔离
    TEST_LICENSE_ID = generateTestLicenseId();

    // 创建测试许可证（如果不存在）
    await createTestLicense(TEST_LICENSE_ID);

    // 初始化测试服务器配置
    testServers = createTestServersConfig(TEST_LICENSE_ID);
  });

  // 在每个测试开始前清理数据，确保测试隔离
  test.beforeEach(async ({ page }) => {
    // 设置网络拦截，避免 RefreshToken 组件调用真实的 Keycloak API
    await interceptRefreshTokenRequests(page);

    // 使用全面的清理策略，确保数据完全清理
    await comprehensiveTestDataCleanup(TEST_LICENSE_ID);

    // 额外的兜底清理，确保没有遗留数据
    await cleanupAllTestServers();
    await forceCleanupAllTestServers();
  });

  // 在每个测试结束后清理数据和登出
  test.afterEach(async ({ page }) => {
    // 清理网络拦截
    await clearNetworkInterceptors(page);

    // 使用全面的清理策略，确保测试数据完全清理
    await comprehensiveTestDataCleanup(TEST_LICENSE_ID);

    // 登出
    await logout(page);
  });

  // 在所有测试结束后断开数据库连接
  test.afterAll(async () => {
    // 最终清理，确保没有遗留数据
    await comprehensiveTestDataCleanup(TEST_LICENSE_ID);
    await disconnectPrisma();
  });

  test('应该能够成功创建管理项目导出任务', async ({ page }) => {
    console.log('🔍 开始测试管理项目导出任务创建');

    // 创建测试服务器数据 - 只创建支持管理项目导出的服务器类型
    const testServer = testServers[0]; // 使用 GENERAL_MANAGER 类型
    console.log('🔍 测试服务器配置:', testServer);
    const createdServer = await createTestServer(testServer);
    console.log(`✅ 已创建测试服务器: ${testServer.name}`, createdServer);

    // 以管理员身份登录 - 使用动态许可证ID
    await loginAs(page, { role: UserRole.ADMIN, licenseId: TEST_LICENSE_ID });

    // 访问服务器列表页面
    await page.goto('/dashboard/servers');

    // 检查是否被重定向到登录页面
    if (page.url().includes('/login')) {
      console.log('🔄 被重定向到登录页面，重新登录');
      // 重新登录 - 使用动态许可证ID
      await loginAs(page, { role: UserRole.ADMIN, licenseId: TEST_LICENSE_ID });
      // 再次访问服务器列表页面
      await page.goto('/dashboard/servers');
    }

    // 关闭所有可能打开的模态框
    await closeAllModals(page);

    // 等待页面加载完成
    await expect(page.getByRole('columnheader', { name: 'サーバ名' })).toBeVisible();
    await page.waitForLoadState('networkidle');

    // 验证测试服务器显示在列表中
    await expect(page.getByText(testServer.name as string)).toBeVisible();

    // 查找服务器行中的任务操作下拉菜单
    const serverRow = page.locator(`tr:has(th:text("${testServer.name}"))`);

    // 查找"タスクを選択"按钮（下拉菜单的触发按钮）
    const taskSelectButton = serverRow.locator('button:has-text("タスクを選択")');

    // 严格断言：任务选择按钮必须存在
    await expect(taskSelectButton).toBeVisible();
    console.log('✅ 找到任务选择下拉菜单按钮');

    // 点击下拉菜单按钮
    await taskSelectButton.click();
    await page.waitForTimeout(500); // 减少等待时间

    // 在下拉菜单中查找管理项目导出选项
    const exportMenuItem = page.locator('button:has-text("管理項目定義のエクスポート")');

    // 严格断言：导出菜单项必须存在且可见
    await expect(exportMenuItem).toBeVisible();
    console.log('✅ 找到管理项目导出菜单项');

    // 点击导出菜单项
    await exportMenuItem.click();
    await page.waitForTimeout(500); // 减少等待时间

    // 等待确认对话框出现
    const confirmDialog = page.locator('[role="dialog"], .modal');

    // 严格断言：确认对话框必须出现
    await expect(confirmDialog).toBeVisible();
    console.log('✅ 确认对话框已显示');

    // 验证确认对话框内容包含服务器名称
    await expect(confirmDialog).toContainText(testServer.name as string);
    await expect(confirmDialog).toContainText('管理項目定義');
    await expect(confirmDialog).toContainText('エクスポート');

    // 点击确认按钮
    const confirmButton = confirmDialog.locator('button:has-text("OK"), button:has-text("はい"), button:has-text("実行")');

    // 严格断言：确认按钮必须存在
    await expect(confirmButton).toBeVisible();
    await confirmButton.first().click();

    // 验证任务创建成功的消息

    // 验证成功消息显示 - 严格断言EMEC0025格式
    // EMEC0025: "タスクの実行を受け付けました。実行状態はタスク一覧でご確認ください。\n対象サーバ：{0}\nタスク種別：{1}"
    const expectedSuccessMessage = page.locator('text=/タスクの実行を受け付けました。実行状態はタスク一覧でご確認ください。/');

    // 首先检查是否有错误消息
    const errorMessages = [
      'text=/サーバの接続に失敗したため、タスクを開始できませんでした/',
      'text=/EMEC0019/',
      'text=/Service Bus/',
      'text=/送信失敗/',
      'text=/エラー/',
      'text=/失敗/'
    ];

    let errorFound = false;
    for (const errorSelector of errorMessages) {
      if (await page.locator(errorSelector).count() > 0) {
        const errorText = await page.locator(errorSelector).textContent();
        console.log(`❌ 发现错误消息: ${errorText}`);
        errorFound = true;
        break;
      }
    }

    if (errorFound) {
      throw new Error('任务创建失败：Service Bus发送失败或其他错误');
    }

    // 严格断言：必须显示成功消息
    await expect(expectedSuccessMessage).toBeVisible();
    console.log('✅ 找到正确的EMEC0025成功消息');

    // 进一步验证消息包含服务器名称和任务类型
    const messageText = await expectedSuccessMessage.textContent();
    console.log(`📝 成功消息内容: ${messageText}`);
    expect(messageText).toContain(testServer.name as string);
    expect(messageText).toContain('管理項目定義のエクスポート');

    // 验证任务已在数据库中创建 - 强制断言
    await page.waitForTimeout(1000); // 减少等待时间
    const createdTask = await prisma.task.findFirst({
      where: {
        taskType: 'TASK_TYPE.MGMT_ITEM_EXPORT',
        licenseId: TEST_LICENSE_ID,
        targetServerName: testServer.name // 使用 targetServerName 而不是 targetServerId
      },
      orderBy: { submittedAt: 'desc' }
    });

    // 强制断言任务必须存在
    expect(createdTask).toBeTruthy();
    console.log('✅ 任务已在数据库中创建:', createdTask!.id);
    expect(createdTask!.taskType).toBe('TASK_TYPE.MGMT_ITEM_EXPORT');
    expect(createdTask!.status).toBe('TASK_STATUS.QUEUED');
    expect(createdTask!.targetServerName).toBe(testServer.name);
    expect(createdTask!.licenseId).toBe(TEST_LICENSE_ID);

    // 安全地清理创建的任务
    await safeCleanupTask(createdTask!.id);
  });

  test('应该完整测试管理项目导出的正常流程', async ({ page }) => {
    // 修复说明：此测试现在严格验证EMEC0025成功消息格式，
    // 当Service Bus发送失败时会正确检测EMEC0019错误消息并使测试失败
    console.log('🔍 开始测试完整的管理项目导出流程');

    // 创建测试服务器数据 - 使用支持导出的服务器类型
    const testServer = testServers[0]; // GENERAL_MANAGER 类型
    await createTestServer(testServer);
    console.log(`✅ 已创建测试服务器: ${testServer.name}`);

    // 以管理员身份登录
    await loginAs(page, { role: UserRole.ADMIN, licenseId: TEST_LICENSE_ID });

    // 访问服务器列表页面
    await page.goto('/dashboard/servers');
    await closeAllModals(page);
    await expect(page.getByRole('columnheader', { name: 'サーバ名' })).toBeVisible();
    await page.waitForLoadState('networkidle');

    // 确保服务器显示在列表中
    await expect(page.getByText(testServer.name as string)).toBeVisible();

    console.log(`✅ 服务器 ${testServer.name} 显示正常，开始导出流程`);

    // 查找服务器行
    const serverRow = page.locator(`tr:has-text("${testServer.name}")`);

    // 查找"タスクを選択"按钮（下拉菜单的触发按钮）
    const taskSelectButton = serverRow.locator('button:has-text("タスクを選択")');

    await expect(taskSelectButton).toBeVisible();
    await taskSelectButton.click();

    // 在下拉菜单中查找管理项目导出选项
    const exportMenuItem = page.locator('button:has-text("管理項目定義のエクスポート")');
    await expect(exportMenuItem).toBeVisible();
    await exportMenuItem.click();

    // 等待确认对话框出现
    await page.waitForTimeout(500); // 减少等待时间
    const confirmDialog = page.locator('[role="dialog"], .modal, [data-modal]');

    // 严格断言：确认对话框必须出现
    await expect(confirmDialog).toBeVisible();
    console.log('✅ 确认对话框已显示');

    // 验证对话框内容
    const dialogText = await confirmDialog.textContent();
    console.log(`📝 对话框内容: ${dialogText}`);

    // 验证对话框包含预期内容
    expect(dialogText).toContain('管理項目定義');
    expect(dialogText).toContain('エクスポート');
    expect(dialogText).toContain(testServer.name as string);

    // 点击确认按钮
    const confirmButton = confirmDialog.locator('button:has-text("OK"), button:has-text("はい"), button:has-text("実行"), button:has-text("確認"), [data-action="confirm"]');

    // 严格断言：确认按钮必须存在
    await expect(confirmButton.first()).toBeVisible();
    console.log('✅ 找到确认按钮');
    await confirmButton.first().click();

    // 验证任务创建成功的响应

    // 首先检查确认对话框是否已关闭（成功情况下应该关闭）
    const confirmDialogStillOpen = await page.locator('[role="dialog"], .modal, [data-modal]').count() > 0;
    console.log(`📝 确认对话框是否仍然打开: ${confirmDialogStillOpen}`);

    // 获取页面上所有可见的文本内容进行调试
    const pageText = await page.textContent('body');
    console.log(`📝 页面文本内容: ${pageText?.substring(0, 500)}...`);

    // 验证成功消息 - 严格断言EMEC0025格式
    // EMEC0025: "タスクの実行を受け付けました。実行状態はタスク一覧でご確認ください。\n対象サーバ：{0}\nタスク種別：{1}"
    const expectedSuccessMessage = page.locator('text=/タスクの実行を受け付けました。実行状態はタスク一覧でご確認ください。/');

    // 首先检查是否有错误消息
    const errorMessages = [
      'text=/サーバの接続に失敗したため、タスクを開始できませんでした/',
      'text=/EMEC0019/',
      'text=/Service Bus/',
      'text=/送信失敗/',
      'text=/エラー/',
      'text=/失敗/'
    ];

    let errorFound = false;
    for (const errorSelector of errorMessages) {
      if (await page.locator(errorSelector).count() > 0) {
        const errorText = await page.locator(errorSelector).textContent();
        console.log(`❌ 发现错误消息: ${errorText}`);
        errorFound = true;
        break;
      }
    }

    if (errorFound) {
      throw new Error('任务创建失败：Service Bus发送失败或其他错误');
    }

    // 严格断言：必须显示成功消息
    await expect(expectedSuccessMessage).toBeVisible();
    console.log('✅ 找到正确的EMEC0025成功消息');

    // 进一步验证消息包含服务器名称和任务类型
    const messageText = await expectedSuccessMessage.textContent();
    console.log(`📝 成功消息内容: ${messageText}`);
    expect(messageText).toContain(testServer.name as string);
    expect(messageText).toContain('管理項目定義のエクスポート');

    console.log('✅ 管理项目导出任务创建成功');

    // 验证任务在数据库中创建 - 强制断言
    await page.waitForTimeout(1000); // 减少等待时间
    const createdTask = await prisma.task.findFirst({
      where: {
        taskType: 'TASK_TYPE.MGMT_ITEM_EXPORT',
        licenseId: TEST_LICENSE_ID,
        targetServerName: testServer.name
      },
      orderBy: { submittedAt: 'desc' }
    });

    // 强制断言任务必须存在
    expect(createdTask).toBeTruthy();
    console.log(`✅ 任务已在数据库中创建: ${createdTask!.id}`);
    console.log(`📊 任务状态: ${createdTask!.status}`);
    console.log(`📊 任务类型: ${createdTask!.taskType}`);
    console.log(`📊 目标服务器: ${createdTask!.targetServerName}`);

    // 验证任务属性
    expect(createdTask!.taskType).toBe('TASK_TYPE.MGMT_ITEM_EXPORT');
    expect(createdTask!.status).toBe('TASK_STATUS.QUEUED');
    expect(createdTask!.licenseId).toBe(TEST_LICENSE_ID);
    expect(createdTask!.targetServerName).toBe(testServer.name);

    // 验证参数JSON（管理项目导出应该是空对象）
    expect(createdTask!.parametersJson).toBe('{}');

    // 安全地清理创建的任务
    await safeCleanupTask(createdTask!.id);
  });

  test('应该验证只有支持的服务器类型才能执行管理项目导出', async ({ page }) => {
    console.log('🔍 开始测试服务器类型限制');

    // 创建不同类型的测试服务器
    const generalManagerServer = testServers[0]; // GENERAL_MANAGER - 应该支持
    const relayManagerServer = testServers[1]; // RELAY_MANAGER - 应该支持
    const hibunConsoleServer = {
      name: 'e2e-test-hibun-console-export',
      type: ServerType.HIBUN_CONSOLE, // 不应该支持管理项目导出
      url: 'https://example.com/hibun-server-export',
      licenseId: TEST_LICENSE_ID,
      // 添加任务执行所需的必要字段（虽然HIBUN_CONSOLE可能不需要执行任务）
      azureVmName: 'test-vm-hibun-export',
      dockerContainerName: 'test-container-hibun-export',
      hrwGroupName: 'test-hrw-group-hibun-export'
    };

    await createTestServer(generalManagerServer);
    await createTestServer(relayManagerServer);
    await createTestServer(hibunConsoleServer);

    // 以管理员身份登录
    await loginAs(page, { role: UserRole.ADMIN, licenseId: TEST_LICENSE_ID });

    // 访问服务器列表页面
    await page.goto('/dashboard/servers');
    await closeAllModals(page);
    await expect(page.getByRole('columnheader', { name: 'サーバ名' })).toBeVisible();
    await page.waitForLoadState('networkidle');

    // 验证支持的服务器类型有任务选择功能（管理项目导出通过任务选择下拉菜单实现）
    for (const supportedServer of [generalManagerServer, relayManagerServer]) {
      const serverRow = page.locator(`tr:has(th:text("${supportedServer.name}"))`);

      // 正确的检查方式：查找"タスクを選択"按钮
      const taskSelectButton = serverRow.locator('button:has-text("タスクを選択")');
      const hasTaskSelectOption = await taskSelectButton.count() > 0;

      // 严格断言：支持的服务器类型必须有任务选择按钮
      expect(hasTaskSelectOption).toBeTruthy();
      console.log(`✅ ${supportedServer.name} (${supportedServer.type}) 有任务选择功能，支持管理项目导出`);

      // 进一步验证：点击任务选择按钮，检查是否有管理项目导出选项
      if (hasTaskSelectOption) {
        await taskSelectButton.click();
        await page.waitForTimeout(500);

        const exportMenuItem = page.locator('button:has-text("管理項目定義のエクスポート")');
        const hasExportMenuItem = await exportMenuItem.count() > 0;

        expect(hasExportMenuItem).toBeTruthy();
        console.log(`✅ ${supportedServer.name} 的任务选择菜单中包含管理项目导出选项`);

        // 点击其他地方关闭下拉菜单
        await page.click('body');
        await page.waitForTimeout(300);
      }
    }

    // 验证不支持的服务器类型的任务选择功能
    const hibunServerRow = page.locator(`tr:has(th:text("${hibunConsoleServer.name}"))`);
    const hibunTaskSelectButton = hibunServerRow.locator('button:has-text("タスクを選択")');
    const hibunHasTaskSelect = await hibunTaskSelectButton.count() > 0;

    if (hibunHasTaskSelect) {
      // 如果有任务选择按钮，检查是否包含管理项目导出选项
      await hibunTaskSelectButton.click();
      await page.waitForTimeout(500);

      const hibunExportMenuItem = page.locator('button:has-text("管理項目定義のエクスポート")');
      const hibunHasExportOption = await hibunExportMenuItem.count() > 0;

      // 严格断言：不支持的服务器类型不应该有管理项目导出选项
      expect(hibunHasExportOption).toBeFalsy();
      console.log(`✅ ${hibunConsoleServer.name} (${hibunConsoleServer.type}) 正确地不支持管理项目导出`);

      // 点击其他地方关闭下拉菜单
      await page.click('body');
      await page.waitForTimeout(300);
    } else {
      console.log(`✅ ${hibunConsoleServer.name} (${hibunConsoleServer.type}) 没有任务选择功能，正确地不支持管理项目导出`);
    }
  });

  test('应该正确处理容器繁忙状态的并发控制', async ({ page }) => {
    console.log('🔍 开始测试并发控制');

    // 创建测试服务器
    const testServer = testServers[0];
    console.log('🔍 测试服务器配置:', testServer);
    await createTestServer(testServer);
    console.log('✅ 测试服务器创建完成');

    // 先清理可能存在的容器状态记录，避免唯一约束冲突
    await prisma.containerConcurrencyStatus.deleteMany({
      where: {
        targetVmName: testServer.azureVmName!,
        targetContainerName: testServer.dockerContainerName!
      }
    });

    // 模拟容器繁忙状态 - 在数据库中创建一个BUSY状态的容器记录
    // 使用与测试服务器匹配的VM和容器名称
    console.log(`📝 创建容器状态记录: VM=${testServer.azureVmName}, Container=${testServer.dockerContainerName}`);
    const createdContainerStatus = await prisma.containerConcurrencyStatus.create({
      data: {
        targetVmName: testServer.azureVmName!,
        targetContainerName: testServer.dockerContainerName!,
        status: 'BUSY',
        currentTaskId: 'some-running-task-id'
      }
    });
    console.log(`✅ 容器状态记录创建成功:`, createdContainerStatus);

    // 验证创建的服务器记录
    const createdServer = await prisma.server.findFirst({
      where: {
        name: testServer.name as string,
        licenseId: TEST_LICENSE_ID
      }
    });
    console.log('📝 数据库中的服务器记录:', createdServer);

    // 以管理员身份登录
    await loginAs(page, { role: UserRole.ADMIN, licenseId: TEST_LICENSE_ID });

    // 访问服务器列表页面
    await page.goto('/dashboard/servers');
    await closeAllModals(page);
    await expect(page.getByRole('columnheader', { name: 'サーバ名' })).toBeVisible();
    await page.waitForLoadState('networkidle');

    // 尝试执行管理项目导出 - 使用与正常流程测试相同的步骤
    const serverRow = page.locator(`tr:has(th:text("${testServer.name}"))`);

    // 查找"タスクを選択"按钮（下拉菜单的触发按钮）
    const taskSelectButton = serverRow.locator('button:has-text("タスクを選択")');

    // 严格断言：任务选择按钮必须存在
    await expect(taskSelectButton).toBeVisible();
    console.log('✅ 找到任务选择下拉菜单按钮');

    // 点击下拉菜单按钮
    await taskSelectButton.click();
    await page.waitForTimeout(500); // 减少等待时间

    // 在下拉菜单中查找管理项目导出选项
    const exportMenuItem = page.locator('button:has-text("管理項目定義のエクスポート")');

    // 严格断言：导出菜单项必须存在且可见
    await expect(exportMenuItem).toBeVisible();
    console.log('✅ 找到管理项目导出菜单项');

    // 点击导出菜单项
    await exportMenuItem.click();
    await page.waitForTimeout(500); // 减少等待时间

    // 等待确认对话框出现
    const confirmDialog = page.locator('[role="dialog"], .modal');

    // 严格断言：确认对话框必须出现
    await expect(confirmDialog).toBeVisible();
    console.log('✅ 确认对话框已显示');

    // 点击确认按钮
    const confirmButton = confirmDialog.locator('button:has-text("OK"), button:has-text("はい"), button:has-text("実行")');

    // 严格断言：确认按钮必须存在
    await expect(confirmButton).toBeVisible();
    await confirmButton.first().click();
    await page.waitForTimeout(1000); // 减少等待时间

    // 验证显示EMEC0022容器繁忙错误消息
    // EMEC0022: "{0}に対するタスクを実行中のため実行できません。\n実行中のタスクが完了してから再度実行してください。"
    const expectedBusyMessage = `${testServer.name}に対するタスクを実行中のため実行できません。`;
    await expect(page.locator(`text=/${expectedBusyMessage}/`)).toBeVisible();

    // 安全地清理容器状态测试数据
    await safeCleanupContainerStatus(testServer.azureVmName!, testServer.dockerContainerName!);
  });

  test('应该能够在任务列表中查看创建的导出任务', async ({ page }) => {
    console.log('🔍 开始测试任务列表显示');

    // 创建测试服务器
    const testServer = testServers[0];
    await createTestServer(testServer);

    // 获取创建的服务器ID
    const createdServer = await prisma.server.findFirst({
      where: {
        name: testServer.name as string,
        licenseId: TEST_LICENSE_ID
      }
    });

    expect(createdServer).toBeTruthy();
    if (!createdServer) throw new Error('服务器创建失败');

    // 直接在数据库中创建一个管理项目导出任务
    const taskId = 'test-export-task-' + Date.now();
    await prisma.task.create({
      data: {
        id: taskId,
        taskName: `${testServer.name}-管理項目定義エクスポート-${new Date().toISOString().slice(0, 19).replace(/[-:]/g, '').replace('T', '')}`,
        taskType: 'TASK_TYPE.MGMT_ITEM_EXPORT',
        status: 'TASK_STATUS.QUEUED',
        submittedByUserId: 'admin',
        licenseId: TEST_LICENSE_ID,
        targetServerId: createdServer.id, // 使用实际的服务器ID
        targetServerName: testServer.name,
        targetVmName: 'test-vm-name',
        targetContainerName: 'test-container-name',
        targetHRWGroupName: 'test-hrw-group',
        parametersJson: '{}' // 管理项目导出没有特定参数
      }
    });

    console.log(`✅ 已在数据库中创建测试任务: ${taskId}`);

    // 以管理员身份登录
    await loginAs(page, { role: UserRole.ADMIN, licenseId: TEST_LICENSE_ID });

    // 访问任务列表页面
    await page.goto('/dashboard/tasks');
    await page.waitForLoadState('networkidle');


    // 验证任务列表页面加载成功（检查页面标题和表格存在）
    await expect(page).toHaveTitle(/タスク一覧/, { timeout: 10000 });
    await expect(page.locator('table')).toBeVisible({ timeout: 10000 });
    console.log('✅ 任务列表页面加载成功');

    // 验证创建的任务显示在列表中
    const taskRow = page.locator(`tr:has-text("${taskId}"), tr:has-text("管理項目定義エクスポート")`);
    await expect(taskRow).toBeVisible();

    // 验证任务详细信息
    await expect(taskRow).toContainText('管理項目定義エクスポート');
    await expect(taskRow).toContainText(testServer.name as string);
    await expect(taskRow).toContainText('実行待ち');

    // 安全地清理测试任务
    await safeCleanupTask(taskId);
  });

  test('应该正确处理服务器不存在的错误情况', async ({ page }) => {
    console.log('🔍 开始测试服务器不存在错误处理');

    // 不创建任何测试服务器，直接尝试访问不存在的服务器

    // 以管理员身份登录
    await loginAs(page, { role: UserRole.ADMIN, licenseId: TEST_LICENSE_ID });

    // 访问服务器列表页面
    await page.goto('/dashboard/servers');
    await closeAllModals(page);
    await page.waitForLoadState('networkidle');
    // 验证显示空状态消息
    const emptyMessage = page.locator('text=/該当するサーバーがありません|サーバーが見つかりません/');
    await expect(emptyMessage).toBeVisible();
  });

  test('应该验证管理项目导出任务的参数结构', async () => {
    console.log('🔍 开始测试任务参数结构');

    // 创建测试服务器
    const testServer = testServers[0];
    await createTestServer(testServer);

    // 获取创建的服务器ID
    const createdServer = await prisma.server.findFirst({
      where: {
        name: testServer.name as string,
        licenseId: TEST_LICENSE_ID
      }
    });

    // 严格断言：服务器必须创建成功
    expect(createdServer).toBeTruthy();
    if (!createdServer) throw new Error('服务器创建失败');

    // 直接在数据库中创建一个管理项目导出任务，验证参数结构
    const taskId = 'test-export-params-' + Date.now();
    const expectedParametersJson = '{}'; // 管理项目导出没有特定参数

    await prisma.task.create({
      data: {
        id: taskId,
        taskName: `${testServer.name}-管理項目定義エクスポート-${new Date().toISOString().slice(0, 19).replace(/[-:]/g, '').replace('T', '')}`,
        taskType: 'TASK_TYPE.MGMT_ITEM_EXPORT',
        status: 'TASK_STATUS.QUEUED',
        submittedByUserId: 'admin',
        licenseId: TEST_LICENSE_ID,
        targetServerId: createdServer.id, // 使用实际的服务器ID
        targetServerName: testServer.name,
        targetVmName: 'test-vm-name',
        targetContainerName: 'test-container-name',
        targetHRWGroupName: 'test-hrw-group',
        parametersJson: expectedParametersJson
      }
    });

    // 验证任务记录的参数结构
    const createdTask = await prisma.task.findUnique({
      where: { id: taskId }
    });

    if (createdTask) {
      console.log('✅ 任务已创建，验证参数结构');

      // 验证任务类型
      expect(createdTask.taskType).toBe('TASK_TYPE.MGMT_ITEM_EXPORT');

      // 验证参数JSON结构（管理项目导出应该是空对象）
      expect(createdTask.parametersJson).toBe(expectedParametersJson);

      // 验证其他必要字段
      expect(createdTask.targetServerName).toBe(testServer.name);
      expect(createdTask.licenseId).toBe(TEST_LICENSE_ID);
      expect(createdTask.status).toBe('TASK_STATUS.QUEUED');

      console.log('✅ 任务参数结构验证通过');
    } else {
      console.log('❌ 未找到创建的任务');
    }

    // 安全地清理测试任务
    await safeCleanupTask(taskId);
  });

  /**
   * 试験観点：完整导出流程副作用验证
   * 试験対象：管理项目导出的完整流程，包括Azure Functions副作用
   * 試験手順：
   * 1. 启动Mock Server
   * 2. 创建导出任务
   * 3. 验证所有副作用（数据库变更、日志记录、消息发送等）
   * 確認項目：
   * - 任务状态正确变更
   * - 系统日志正确记录
   * - Service Bus消息发送成功
   * - 文件导出记录创建
   */
  test('应该验证完整导出流程的所有副作用', async ({ page }) => {
    console.log('🔍 开始完整导出流程副作用验证测试');

    // 初始化Mock Server和副作用断言工具
    const mockServerHelper = new MockServerHelper();
    await mockServerHelper.startServer();

    const sideEffectsAssertions = createSimpleSideEffectsAssertions(prisma, page);

    try {
      // 清理Mock Server状态
      await mockServerHelper.clearAllJobs();
      await mockServerHelper.clearFailureScenarios();

      // 创建测试服务器
      const testServer: TestServerConfig = {
        name: `test-server-side-effects-${Date.now()}`,
        type: ServerType.GENERAL_MANAGER,
        licenseId: TEST_LICENSE_ID
      };

      await createTestServer(testServer);
      console.log(`✅ 已创建测试服务器: ${testServer.name}`);

      // 登录并执行导出操作
      await loginAs(page, { role: UserRole.ADMIN, licenseId: TEST_LICENSE_ID });
      await page.goto('/dashboard/servers');
      await closeAllModals(page);
      await page.waitForLoadState('networkidle');

      // 执行导出操作
      const serverRow = page.locator(`tr:has-text("${testServer.name}")`);
      const taskSelectButton = serverRow.locator('button:has-text("タスクを選択")');
      await taskSelectButton.click();

      const exportMenuItem = page.locator('button:has-text("管理項目定義のエクスポート")');
      await exportMenuItem.click();

      const confirmDialog = page.locator('[role="dialog"], .modal, [data-modal]');
      await expect(confirmDialog).toBeVisible();

      const confirmButton = confirmDialog.locator('button:has-text("OK"), button:has-text("はい"), button:has-text("実行")');
      await confirmButton.first().click();

      // 验证前端成功响应
      const successMessage = page.locator('text=/タスクの実行を受け付けました/');
      await expect(successMessage).toBeVisible();
      console.log('✅ 前端操作成功');

      // 获取创建的任务
      await page.waitForTimeout(2000);
      const createdTask = await prisma.task.findFirst({
        where: {
          taskType: 'TASK_TYPE.MGMT_ITEM_EXPORT',
          licenseId: TEST_LICENSE_ID,
          targetServerName: testServer.name
        },
        orderBy: { submittedAt: 'desc' }
      });

      expect(createdTask).toBeTruthy();
      const taskId = createdTask!.id;
      console.log(`✅ 任务已创建: ${taskId}`);

      // 验证初始副作用
      await sideEffectsAssertions.assertTaskStatusChange('PENDING', { taskId });

      // 等待Azure Functions处理
      await page.waitForTimeout(10000);

      // 验证处理过程中的副作用
      await sideEffectsAssertions.assertTaskStatusChange('RUNNING', {
        taskId,
        timeout: 60000,
        maxRetries: 20
      });

      // 验证任务开始时间设置
      await sideEffectsAssertions.assertTaskTimeFieldUpdated(taskId, 'startedAt');

      // 模拟作业完成
      const jobs = await mockServerHelper.getAllJobs();
      if (jobs.length > 0) {
        await mockServerHelper.setJobStatus(jobs[0].name, MockJobStatus.Completed);
        console.log(`✅ 模拟作业完成: ${jobs[0].name}`);
      }

      // 验证完成后的副作用
      await sideEffectsAssertions.assertTaskStatusChange('COMPLETED', {
        taskId,
        timeout: 90000,
        maxRetries: 30
      });

      // 验证任务结束时间和结果消息
      await sideEffectsAssertions.assertTaskTimeFieldUpdated(taskId, 'endedAt');
      await sideEffectsAssertions.assertTaskResultMessage(taskId, /完成|成功|success/i);

      console.log('✅ 完整导出流程副作用验证成功');

      // 清理测试任务
      await safeCleanupTask(taskId);

    } finally {
      // 停止Mock Server
      await mockServerHelper.stopServer();
    }
  });
});
