import fileFixture from "../../fixtures/ProvidedFile.json";
import planFixture from "../../fixtures/PlanProvidedFile.json";

describe("画面操作のテスト", () => {
  describe("提供ファイル一覧画面", () => {
    const validCredentials = {
      userId: "hitachi.taro.aa",
      password: "changeit!@#",
    };
    const filter1 = "who";
    const filter2 = "tim";
    const filter3 = "2023/11/1";
    const filter4 = "8 kb";
    const filter0 = "提供ファイル一覧画面";
    const filterPage2 = "9 kb";
    const filterRefresh = "kb";
    // @ts-ignore
    const formatBytes = (bytes) => {
      const k = 1024;
      const sizes = ["KB", "MB", "GB"];
      // @ts-ignore
      const removeTrailingZeros = (value) => {
        return value.replace(/\.?0+$/, "");
      };

      if (bytes < k * k) {
        return removeTrailingZeros((bytes / k).toFixed(2)) + " " + sizes[0];
      } else if (bytes < k * k * k) {
        return (
          removeTrailingZeros((bytes / (k * k)).toFixed(2)) + " " + sizes[1]
        );
      } else {
        return (
          removeTrailingZeros((bytes / (k * k * k)).toFixed(2)) + " " + sizes[2]
        );
      }
    };
    const files = fileFixture.ProvidedFile.map((f) => ({
      ...f,
      size: formatBytes(f.size),
      updatedAt: new Date(f.updatedAt)
        .toLocaleString("sv-SE", { timeZone: "Asia/Tokyo" })
        .replace(/-/g, "/"),
    }));
    // @ts-ignore
    const doFilter = (keyword) => {
      const uniqueProducts = planFixture.PlanProvidedFile.filter((file) =>
        ["standard", "lighta"].includes(file.planId),
      ).map((file) => file.name);

      return files.filter(
        (file) =>
          uniqueProducts.includes(file.name) &&
          (file.name.toLowerCase().includes(keyword.toLowerCase()) ||
            file.description.toLowerCase().includes(keyword.toLowerCase()) ||
            file.updatedAt.toLowerCase().includes(keyword.toLowerCase()) ||
            file.size.toLowerCase().includes(keyword.toLowerCase())),
      );
    };

    beforeEach(() => {
      cy.visit("/login");
      cy.get("#userId").type(validCredentials.userId);
      cy.get("#password").type(validCredentials.password);
      cy.get("button").click();
      cy.get("aside #file li:nth-child(4) a").click();
      cy.title().should("eq", "提供ファイル一覧");
    });

    describe("フィルタリング", () => {
      it("ファイル名で検索する", () => {
        cy.get(".bg-white.h-full input").type(filter1);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter1).length / 10)}`);
      });

      it("説明で検索する", () => {
        cy.get(".bg-white.h-full input").type(filter2);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter2).length / 10)}`);
      });

      it("最終更新日時で検索する", () => {
        cy.get(".bg-white.h-full input").type(filter3);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter3).length / 10)}`);
      });

      it("サイズで検索する", () => {
        cy.get(".bg-white.h-full input").type(filter4);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter4).length / 10)}`);
      });

      it("結果が0件", () => {
        cy.get(".bg-white.h-full input").type(filter0);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9").should(
          "have.length",
          0,
        );
      });

      it("結果が2ページ", () => {
        cy.get(".bg-white.h-full input").type(filterPage2);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", "2");
      });

      it("クリアアイコンをクリックする", () => {
        cy.get(".bg-white.h-full input").type(filter1);
        cy.get(".bg-white.h-full button").first().click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter1).length / 10)}`);
        cy.get(".bg-white.h-full button").last().click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter("").length / 10)}`);
      });
    });

    describe("ページネーション", () => {
      it("2ページボタンをクリックする", () => {
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "1");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "2");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .next()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "3");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .next()
          .next()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
        const maxPage = Math.ceil(doFilter("").length / 10);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage - 1}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .prev()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
      });

      it("3ページボタンをクリックする", () => {
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(3)
          .click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "1");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "2");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .next()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "3");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .next()
          .next()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
        const maxPage = Math.ceil(doFilter("").length / 10);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage - 1}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .prev()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
      });

      it("最大ページボタンをクリックする", () => {
        const maxPage = Math.ceil(doFilter("").length / 10);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(maxPage)
          .click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "1");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "2");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .next()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", `${maxPage}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage - 1}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .prev()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage - 2}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .prev()
          .prev()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
      });

      it("最大ページボタンの前のボタンをクリックする", () => {
        const maxPage = Math.ceil(doFilter("").length / 10);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(maxPage - 1)
          .click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "1");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "2");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .next()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", `${maxPage - 1}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .prev()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage - 2}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .prev()
          .prev()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
      });

      it("次へアイコンをクリックする", () => {
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .then((text) => {
            const maxPage = Number(text);

            for (let index = 1; index < maxPage; index++) {
              cy.get("#page-right").click();
              cy.wait(1000);

              if (index !== maxPage - 3) {
                cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
                  .contains(index)
                  .should("have.prop", "tagName", "A");
                cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
                  .contains(index + 1)
                  .should("have.prop", "tagName", "DIV");
              }
            }
          });
      });

      it("前へアイコンをクリックする", () => {
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .then((text) => {
            const maxPage = Number(text);

            for (let index = 1; index < maxPage; index++) {
              cy.get("#page-right").click();
              cy.wait(1000);

              if (index !== maxPage - 3) {
                cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
                  .contains(index)
                  .should("have.prop", "tagName", "A");
                cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
                  .contains(index + 1)
                  .should("have.prop", "tagName", "DIV");
              }
            }

            for (let index = 1; index < maxPage; index++) {
              cy.get("#page-left").click();
              cy.wait(1000);

              if (index !== maxPage - 3) {
                cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
                  .contains(maxPage - index)
                  .should("have.prop", "tagName", "DIV");
                cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
                  .contains(maxPage - index + 1)
                  .should("have.prop", "tagName", "A");
              }
            }
          });
      });

      it("行数/ページが30に選択する", () => {
        cy.get(".bg-white.h-full select").select("30");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter("").length / 30)}`);
        cy.get(".rounded-b-lg").within(() => {
          cy.get("table").then(($child) => {
            const parentClientHeight = $child.parent().height() || 0;
            const childScrollHeight = $child[0].scrollHeight;

            expect(childScrollHeight).to.be.greaterThan(parentClientHeight);
          });
        });
        cy.get("table tbody tr td:nth-child(3)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
        cy.get(".bg-white.h-full select").select("10");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter("").length / 10)}`);
        cy.get("table tbody tr td:nth-child(3)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("行数/ページが50に選択する", () => {
        cy.get(".bg-white.h-full select").select("50");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter("").length / 50)}`);
        cy.get(".rounded-b-lg").within(() => {
          cy.get("table").then(($child) => {
            const parentClientHeight = $child.parent().height() || 0;
            const childScrollHeight = $child[0].scrollHeight;

            expect(childScrollHeight).to.be.greaterThan(parentClientHeight);
          });
        });
        cy.get("table tbody tr td:nth-child(3)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
        cy.get(".bg-white.h-full select").select("10");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter("").length / 10)}`);
        cy.get("table tbody tr td:nth-child(3)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });
    });

    describe("ソート", () => {
      it("テーブルヘーダに、最終更新日時をクリックする", () => {
        cy.get("thead th span").contains("最終更新日時").click();
        cy.get("thead th span")
          .should("contain", "最終更新日時")
          .next("img")
          .should("have.class", "rotate-180");
        cy.get("thead th span")
          .contains("ファイル名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("説明")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("サイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr").then(($rows) => {
          const columnData = $rows
            .map((_, row) => {
              // @ts-ignore
              const updatedAt = row.querySelector("td:nth-child(3)").innerText;
              // @ts-ignore
              const name = row.querySelector("th").innerText;
              return { updatedAt: new Date(updatedAt).getTime(), name };
            })
            .get();

          const sortedColumnData = [...columnData].sort((a, b) => {
            const releaseDateComparison = a.updatedAt - b.updatedAt;
            if (releaseDateComparison !== 0) {
              return releaseDateComparison;
            }
            return a.name.localeCompare(b.name);
          });

          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、ファイル名をクリックする", () => {
        cy.get("thead th span").contains("ファイル名").click();
        cy.get("thead th span")
          .should("contain", "ファイル名")
          .next("img")
          .should("have.class", "rotate-180");
        cy.get("thead th span")
          .contains("説明")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("最終更新日時")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("サイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr th").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            a.localeCompare(b),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、ファイル名を2回クリックする", () => {
        cy.get("thead th span").contains("ファイル名").click();
        cy.wait(2000);
        cy.get("thead th span").contains("ファイル名").click();
        cy.get("thead th span")
          .should("contain", "ファイル名")
          .next("img")
          .should("not.have.class", "rotate-180");
        cy.get("thead th span")
          .contains("説明")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("最終更新日時")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("サイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr th").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、説明をクリックする", () => {
        cy.get("thead th span").contains("説明").click();
        cy.get("thead th span")
          .should("contain", "説明")
          .next("img")
          .should("have.class", "rotate-180");
        cy.get("thead th span")
          .contains("ファイル名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("最終更新日時")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("サイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(2)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            a.localeCompare(b),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、説明を2回クリックする", () => {
        cy.get("thead th span").contains("説明").click();
        cy.wait(2000);
        cy.get("thead th span").contains("説明").click();
        cy.get("thead th span")
          .should("contain", "説明")
          .next("img")
          .should("not.have.class", "rotate-180");
        cy.get("thead th span")
          .contains("ファイル名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("最終更新日時")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("サイズ")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(2)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、サイズをクリックする", () => {
        cy.get("thead th span").contains("サイズ").click();
        cy.get("thead th span")
          .should("contain", "サイズ")
          .next("img")
          .should("have.class", "rotate-180");
        cy.get("thead th span")
          .contains("ファイル名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("最終更新日時")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("説明")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(4)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            a.localeCompare(b),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、サイズを2回クリックする", () => {
        cy.get("thead th span").contains("サイズ").click();
        cy.wait(1000);
        cy.get("thead th span").contains("サイズ").click();
        cy.get("thead th span")
          .should("contain", "サイズ")
          .next("img")
          .should("not.have.class", "rotate-180");
        cy.get("thead th span")
          .contains("ファイル名")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("最終更新日時")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("説明")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(4)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });
    });

    describe("更新", () => {
      it("更新アイコンをクリックする", () => {
        cy.get(".bg-white.h-full input").type(filterRefresh);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full select").select("30");
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filterRefresh).length / 30)}`);
        cy.get("nav img").click();
        cy.wait(1000);
        cy.get(".bg-white.h-full input").should("have.value", filterRefresh);
        cy.get(".bg-white.h-full select option:selected").then(
          (selectedOption) => {
            cy.wrap(selectedOption).invoke("text").should("include", "10");
            cy.wrap(selectedOption).invoke("val").should("eq", "10");
          },
        );
      });
    });

    describe("ブラウザ", () => {
      it("更新ボタンをクリックする", () => {
        cy.get(".bg-white.h-full input").type(filterRefresh);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full select").select("30");
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filterRefresh).length / 30)}`);
        cy.reload();
        cy.wait(1000);
        cy.get(".bg-white.h-full input").should("have.value", filterRefresh);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filterRefresh).length / 30)}`);
        cy.get(".bg-white.h-full select option:selected").then(
          (selectedOption) => {
            cy.wrap(selectedOption).invoke("text").should("include", "30");
            cy.wrap(selectedOption).invoke("val").should("eq", "30");
          },
        );
      });

      it("戻るボタンをクリックし、進むボタンをクリックする", () => {
        cy.get(".bg-white.h-full input").type(filterRefresh);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full select").select("30");
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filterRefresh).length / 30)}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .should("have.prop", "tagName", "DIV");
        cy.go("back");
        cy.wait(1000);
        cy.get(".bg-white.h-full input").should("have.value", filterRefresh);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filterRefresh).length / 30)}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(1)
          .should("have.prop", "tagName", "DIV");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .should("have.prop", "tagName", "A");
        cy.get(".bg-white.h-full select option:selected").then(
          (selectedOption) => {
            cy.wrap(selectedOption).invoke("text").should("include", "30");
            cy.wrap(selectedOption).invoke("val").should("eq", "30");
          },
        );
        cy.go("forward");
        cy.wait(1000);
        cy.get(".bg-white.h-full input").should("have.value", filterRefresh);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filterRefresh).length / 30)}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .should("have.prop", "tagName", "DIV");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(1)
          .should("have.prop", "tagName", "A");
        cy.get(".bg-white.h-full select option:selected").then(
          (selectedOption) => {
            cy.wrap(selectedOption).invoke("text").should("include", "30");
            cy.wrap(selectedOption).invoke("val").should("eq", "30");
          },
        );
      });
    });
  });
});
