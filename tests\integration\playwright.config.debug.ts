/**
 * @fileoverview Playwright配置 - 调试模式
 * @description
 * 专门用于调试的Playwright配置，不会自动打开HTML报告。
 * 只输出控制台日志，方便快速调试。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright配置 - 调试模式
 */
export default defineConfig({
  testDir: './specs',
  
  // 全局设置
  fullyParallel: false, // 禁用并行执行，避免端口冲突
  forbidOnly: !!process.env.CI,
  retries: 0, // 调试时不重试
  workers: 1, // 单线程执行
  
  // 报告设置 - 只输出到控制台
  reporter: [
    ['list'], // 只显示列表格式，不生成HTML报告
  ],
  
  // 输出设置
  outputDir: '../test-results/artifacts',
  
  // 全局测试配置
  use: {
    // 基础URL
    baseURL: 'http://localhost:3000',

    // 浏览器设置
    headless: true,
    viewport: { width: 1280, height: 720 },
    ignoreHTTPSErrors: true,
    
    // 截图和视频 - 调试时保留更多信息
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    
    // 跟踪
    trace: 'retain-on-failure',
    
    // 超时设置
    actionTimeout: 10000,
    navigationTimeout: 30000,
  },
  
  // 项目配置
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
      testMatch: [
        '**/management-item-export.spec.ts',
        '**/final-export-test.spec.ts',
        '**/port-cleanup-test.spec.ts'
      ]
    },
  ],
  
  // Web服务器配置 - 启动Next.js
  webServer: {
    command: process.platform === 'win32'
      ? 'cd ../../apps/jcs-endpoint-nextjs && (if not exist .next\\BUILD_ID (echo ❌ Next.js 未构建！请先运行: npm run build && exit 1)) && npm run start'
      : 'cd ../../apps/jcs-endpoint-nextjs && (test -f .next/BUILD_ID || (echo "❌ Next.js 未构建！请先运行: npm run build" && exit 1)) && npm run start',
    port: 3000,
    reuseExistingServer: true, // 重用现有服务器，避免健康检查
    timeout: 90 * 1000,
    env: {
      NODE_ENV: 'test',
      PORT: '3000',
      FORCE_COLOR: '0',
      NODE_OPTIONS: '--max-old-space-size=4096',
    },
    stdout: 'pipe',
    stderr: 'pipe',
  },
  
  // 全局设置和清理
  globalSetup: require.resolve('./support/simple-global-setup.ts'),
  globalTeardown: undefined,
  
  // 超时设置
  timeout: 120000, // 单个测试2分钟超时
  expect: {
    timeout: 15000, // 断言15秒超时
  },
});