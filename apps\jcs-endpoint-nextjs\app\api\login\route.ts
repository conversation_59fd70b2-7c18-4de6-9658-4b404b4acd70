/**
 * @file route.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2024, 2025, Hitachi Solutions, Ltd.
 */

import { ENV } from "@/app/lib/definitions";
import { NextResponse } from "next/server";

// ログインの POST リクエストの処理
export async function POST() {
    // KE<PERSON><PERSON>OAKのREALM
    const realm = ENV.KEYCLOAK_REALM;
    // KEYCLOAKのCLIENT
    const clientId = ENV.KEYCLOAK_CLIENT;
    // KEYCLAOKログイン成功後のコールバックアドレス
    const redirectUri = ENV.KEYCLOAK_REDIRECT_URL;
    // KEYCLOAKサービスドメイン
    const publicDomainName = ENV.KEYCLOAK_PUBLIC_DOMAIN_NAME;
    const internalDomainName = ENV.KEYCLOAK_INTERNAL_DOMAIN_NAME;
    if (!realm || !clientId || !redirectUri || !publicDomainName || !internalDomainName) {
      return NextResponse.json({ status: 400});
    }
    // keycloakログインページアドレス作成
    const loginUrl = `realms/${realm}/protocol/openid-connect/auth?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}`;
    // keycloakアドレス認証
  try {
    const response = await fetch(`${internalDomainName}/${loginUrl}`, { method: 'GET' });
    if (response.ok) {
      // 正常なレスポンスを返す
      return NextResponse.json({ status: 200, url:`${publicDomainName}/${loginUrl}`});
    } else {
      // 異常なレスポンスを返す
      return NextResponse.json({ status: 400});
    }
  } catch (error) {
    return NextResponse.json({ status: 400});
  }
}
