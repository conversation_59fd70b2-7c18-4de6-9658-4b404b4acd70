/**
 * @file table.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

import { ServerDataManuals } from "@/app/lib/data/manuals";
import Thead from "../thead";

// マニュアルテーブルコンポーネント
export default async function ProductManualsTable({
  filter,
  page,
  size,
  sort,
  order,
}: {
  filter: string;
  page: number;
  size: number;
  sort: "name" | "serialNo" | "size";
  order: "asc" | "desc";
}) {
  const manuals = await ServerDataManuals.fetchFilteredProductManuals(
    filter,
    size,
    page,
    sort,
    order,
  );

  return (
    <table className="whitespace-nowrap w-full text-left text-sm text-gray-500">
      <Thead
        headers={[
          { key: "name", label: "マニュアル名称" },
          { key: "serialNo", label: "資料番号" },
          { key: "size", label: "サイズ" },
        ]}
        defaultOrder="name"
        defaultSort="asc"
      />
      <tbody>
        {manuals?.length !== 0 ? (
          manuals!.map((manual) => (
            <tr
              key={manual.id}
              className="border-b odd:bg-white even:bg-gray-50"
            >
              <th
                scope="row"
                className="border-r text-gray-900 whitespace-nowrap px-6 py-4 font-medium"
              >
                <a
                  target="_blank"
                  href={`manuals/${manual.serialNo}/${manual.fileName}`}
                  className="font-medium text-blue-600 hover:underline"
                >
                  {manual.name}
                </a>
              </th>
              <td className="border-r px-6 py-4">{manual.serialNo}</td>
              <td className="px-6 py-4 text-right">{manual.formattedSize}</td>
            </tr>
          ))
        ) : (
          <tr>
            <td>
              <div className="p-4"></div>
            </td>
          </tr>
        )}
      </tbody>
    </table>
  );
}
