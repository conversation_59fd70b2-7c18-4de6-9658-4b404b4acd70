# 本次迭代开发目标（详细可追踪版）

---

## 1. 服务器列表页面（/dashboard/servers）

| 编号 | 子任务描述 | 负责人 | 工时预估 | 优先级 | 代码行数预估 |
|----|-------|-----|------|-----|--------|
| 1.1 | 页面路由与基础布局 | 已完成 | - | - | 73 |
| 1.2 | 服务器数据获取与缓存（含LOV映射） | 已完成 | - | - | 120 |
| 1.3 | 搜索框组件实现 | 已完成 | - | - | 40 |
| 1.4 | 表格组件实现（含排序、分页） | 已完成 | - | - | 110 |
| 1.5 | 任务操作下拉菜单与权限控制 | 已完成 | - | - | 40 |
| 1.6 | 操作日志导出参数模态框 | 已完成（全部前端校验、UI、注释、LOV联动、tooltip换行、错误消息参数化均已实现，无硬编码，已通过验收） | - | - | 约100 |
| 1.7 | 管理项目定义导入参数模态框 | 已完成（已对齐设计稿，交互与样式bug已修复） | - | - | 50 |
| 1.8 | 通用二次确认模态框 | 已完成 | - | - | 30 |
| 1.9 | createTaskAction集成与参数传递 | 已完成（参数链路已全流程打通，UI端已通过FormData方式调用createTaskAction，后端已实现所有业务分支与校验） | - | - | 30 |
| 1.10 | 错误处理与用户提示（前端） | 已完成 | - | - | 20 |
| 1.11 | 错误处理与用户提示（后端） | 已完成 | - | - | 20 |
| 1.12 | 单元测试 | - | - | - | 60 |
| 1.13 | 集成测试 | - | - | - | 40 |
| 1.14 | 接口文档/契约校对 | - | - | - | 10 |

**小计：**
- 以上代码行数为格式化后、包含空行的实际/类比统计，符合现有代码风格和人类阅读习惯。
- 主要实现文件行数：
  - 改造部分：page.tsx（73）、data.ts相关（120）、table.tsx（80）、其他（110）
  - 新增部分：模态框（100）、任务集成（30）、错误处理（40）、测试（100）
- 合计：约713行（含注释），其中：
  - 改造部分：约383行
  - 新增部分：约270行
  - 测试部分：约60行
- 若新功能涉及更多业务分支、复杂UI或后端逻辑，建议在此基础上适当增加预估行数。
- 若有脚手架/部署脚本、环境配置等需求，需单独补充。

- 关键交互/分支/异常场景：
  - 并发任务发起时的只读校验与错误分支
  - 参数模态框的前端/服务端校验分支
  - 任务类型与服务器类型的可用性联动

---

## 2. 任务列表页面（/dashboard/tasks）

| 编号 | 子任务描述 | 负责人 | 工时预估 | 优先级 | 代码行数预估 |
|----|-------|-----|------|-----|--------|
| 2.1 | 页面路由与基础布局 | - | - | - | 80 |
| 2.2 | 任务数据获取与缓存（含LOV映射） | - | - | - | 130 |
| 2.3 | 筛选输入框组件实现 | - | - | - | 40 |
| 2.4 | 表格组件实现（含排序、分页） | - | - | - | 120 |
| 2.5 | 任务中止操作与确认弹窗 | - | - | - | 50 |
| 2.6 | 错误详情弹窗 | - | - | - | 40 |
| 2.7 | 产出文件下载功能 | - | - | - | 40 |
| 2.8 | createTaskAction与requestTaskCancellation集成 | - | - | - | 40 |
| 2.9 | 错误处理与用户提示（前端） | - | - | - | 20 |
| 2.10 | 错误处理与用户提示（后端） | - | - | - | 20 |
| 2.11 | 单元测试 | - | - | - | 70 |
| 2.12 | 集成测试 | - | - | - | 50 |
| 2.13 | 接口文档/契约校对 | - | - | - | 10 |

**小结：**
- 以上代码行数为格式化后、包含空行的实际/类比统计，符合现有代码风格和人类阅读习惯。
- 主要实现文件行数：页面主入口（80）、表格组件（100+）、后端服务相关（约130）、其他（约200）、测试（约120）。
- 合计：约913行（含注释）。
- 若新功能涉及更多业务分支、复杂UI或后端逻辑，建议在此基础上适当增加预估行数。
- 若有脚手架/部署脚本、环境配置等需求，需单独补充。

- 关键交互/分支/异常场景：
  - 任务状态流转的所有分支（正常、错误、中止、超时等）
  - 中止操作的幂等与失败分支
  - 错误详情与产出文件的权限校验

---

## 3. 后台Azure Functions（7个）

### 3.1 TaskExecuteFunc

| 编号 | 子任务描述 | 负责人 | 工时预估 | 优先级 | 代码行数预估 |
|----|-------|-----|------|-----|--------|
| 3.1.1 | 消息解析与任务上下文加载 | - | - | - | 60 |
| 3.1.2 | 并发锁获取与冲突处理 | - | - | - | 40 |
| 3.1.3 | Runbook参数构造 | - | - | - | 40 |
| 3.1.4 | Automation API调用 | - | - | - | 60 |
| 3.1.5 | 任务类型分支处理（操作日志导出） | - | - | - | 50 |
| 3.1.6 | 任务类型分支处理（管理定义导入） | - | - | - | 50 |
| 3.1.7 | 任务类型分支处理（管理定义导出） | - | - | - | 50 |
| 3.1.8 | 资源准备与临时文件处理 | - | - | - | 40 |
| 3.1.9 | 错误处理与日志 | - | - | - | 30 |
| 3.1.10 | 接口文档/契约校对 | - | - | - | 10 |
| 3.1.11 | 单元测试 | - | - | - | 40 |
| 3.1.12 | 集成测试 | - | - | - | 30 |
| 3.1.13 | 脚手架配置（host.json, local.settings.json） | - | - | - | 20 |
| 3.1.14 | 部署配置（function.json, bindings） | - | - | - | 20 |

**小计：**
- 以上代码行数为格式化后、包含空行的实际/类比统计，符合现有代码风格和人类阅读习惯。
- 主要实现文件行数：Function主入口（60+）、分支处理（150+）、API调用与参数构造（100+）、测试（70）、脚手架（40）。
- 合计：约665行（含注释）。
- 若有脚手架/部署脚本、环境配置等需求，需单独补充。

### 3.2 RunbookMonitorFunc

| 编号 | 子任务描述 | 负责人 | 工时预估 | 优先级 | 代码行数预估 |
|----|-------|-----|------|-----|--------|
| 3.2.1 | Runbook状态轮询 | - | - | - | 40 |
| 3.2.2 | 状态消息分类与分发 | - | - | - | 40 |
| 3.2.3 | 超时检测与异常分支处理 | - | - | - | 30 |
| 3.2.4 | 消息发送到RunbookStatusQueue | - | - | - | 30 |
| 3.2.5 | 错误处理与日志 | - | - | - | 20 |
| 3.2.6 | 接口文档/契约校对 | - | - | - | 10 |
| 3.2.7 | 单元测试 | - | - | - | 30 |
| 3.2.8 | 集成测试 | - | - | - | 20 |
| 3.2.9 | 脚手架配置（host.json, local.settings.json） | - | - | - | 20 |
| 3.2.10 | 部署配置（function.json, bindings） | - | - | - | 20 |

**小计：**
- 合计：约315行（含注释）。

### 3.3 RunbookProcessorFunc

| 编号 | 子任务描述 | 负责人 | 工时预估 | 优先级 | 代码行数预估 |
|----|-------|-----|------|-----|--------|
| 3.3.1 | 消息解析与任务终态处理 | - | - | - | 40 |
| 3.3.2 | 成果物归档（Blob上传、OperationLog记录） | - | - | - | 40 |
| 3.3.3 | 资源回收（并发锁释放、工作区清理） | - | - | - | 30 |
| 3.3.4 | 错误处理与日志 | - | - | - | 20 |
| 3.3.5 | 接口文档/契约校对 | - | - | - | 10 |
| 3.3.6 | 单元测试 | - | - | - | 20 |
| 3.3.7 | 集成测试 | - | - | - | 20 |
| 3.3.8 | 脚手架配置（host.json, local.settings.json） | - | - | - | 20 |
| 3.3.9 | 部署配置（function.json, bindings） | - | - | - | 20 |

**小计：**
- 合计：约265行（含注释）。

### 3.4 TaskCancellationFunc

| 编号 | 子任务描述 | 负责人 | 工时预估 | 优先级 | 代码行数预估 |
|----|-------|-----|------|-----|--------|
| 3.4.1 | 中止消息处理与状态流转 | - | - | - | 30 |
| 3.4.2 | 幂等性与异常分支处理 | - | - | - | 30 |
| 3.4.3 | 错误处理与日志 | - | - | - | 20 |
| 3.4.4 | 接口文档/契约校对 | - | - | - | 10 |
| 3.4.5 | 单元测试 | - | - | - | 20 |
| 3.4.6 | 集成测试 | - | - | - | 20 |
| 3.4.7 | 脚手架配置（host.json, local.settings.json） | - | - | - | 20 |
| 3.4.8 | 部署配置（function.json, bindings） | - | - | - | 20 |

**小计：**
- 合计：约203行（含注释）。

### 3.5 TaskExecuteTimeoutFunc

| 编号 | 子任务描述 | 负责人 | 工时预估 | 优先级 | 代码行数预估 |
|----|-------|-----|------|-----|--------|
| 3.5.1 | DLQ消息解析与补偿处理 | - | - | - | 30 |
| 3.5.2 | 资源回收（并发锁释放、工作区清理） | - | - | - | 20 |
| 3.5.3 | 错误处理与日志 | - | - | - | 20 |
| 3.5.4 | 接口文档/契约校对 | - | - | - | 10 |
| 3.5.5 | 单元测试 | - | - | - | 20 |
| 3.5.6 | 集成测试 | - | - | - | 20 |
| 3.5.7 | 脚手架配置（host.json, local.settings.json） | - | - | - | 20 |
| 3.5.8 | 部署配置（function.json, bindings） | - | - | - | 20 |

**小计：**
- 合计：约190行（含注释）。

### 3.6 TaskCancellationTimeoutFunc

| 编号 | 子任务描述 | 负责人 | 工时预估 | 优先级 | 代码行数预估 |
|----|-------|-----|------|-----|--------|
| 3.6.1 | DLQ消息解析与补偿处理 | - | - | - | 30 |
| 3.6.2 | 状态修正与异常分支处理 | - | - | - | 20 |
| 3.6.3 | 错误处理与日志 | - | - | - | 20 |
| 3.6.4 | 接口文档/契约校对 | - | - | - | 10 |
| 3.6.5 | 单元测试 | - | - | - | 20 |
| 3.6.6 | 集成测试 | - | - | - | 20 |
| 3.6.7 | 脚手架配置（host.json, local.settings.json） | - | - | - | 20 |
| 3.6.8 | 部署配置（function.json, bindings） | - | - | - | 20 |

**小计：**
- 合计：约190行（含注释）。

### 3.7 RunbookProcessorTimeoutFunc

| 编号 | 子任务描述 | 负责人 | 工时预估 | 优先级 | 代码行数预估 |
|----|-------|-----|------|-----|--------|
| 3.7.1 | DLQ消息解析与补偿处理 | - | - | - | 30 |
| 3.7.2 | 资源回收（并发锁释放、工作区清理） | - | - | - | 50 |
| 3.7.3 | 错误处理与日志 | - | - | - | 20 |
| 3.7.4 | 接口文档/契约校对 | - | - | - | 10 |
| 3.7.5 | 单元测试 | - | - | - | 20 |
| 3.7.6 | 集成测试 | - | - | - | 20 |
| 3.7.7 | 脚手架配置（host.json, local.settings.json） | - | - | - | 20 |
| 3.7.8 | 部署配置（function.json, bindings） | - | - | - | 20 |

**小计：**
- 合计：约190行（含注释）。

**后台Azure Functions总计：**
- 7个Function合计：约2,018行（含注释）
- 包含：
  - TaskExecuteFunc: 665行
  - RunbookMonitorFunc: 315行
  - RunbookProcessorFunc: 265行
  - TaskCancellationFunc: 203行
  - TaskExecuteTimeoutFunc: 190行
  - TaskCancellationTimeoutFunc: 190行
  - RunbookProcessorTimeoutFunc: 190行

---

## 4. 交付与追踪

| 子任务编号 | 子任务描述 | 负责人 | 工时预估 | 优先级 |
|-------|-------|-----|------|-----|
| 4.1 | 维护任务拆解清单，定期更新进度 | - | - | - |
| 4.2 | 明确每个子任务的负责人、预估工时、验收标准 | - | - | - |

> 说明：
> - 负责人、工时预估、优先级、代码行数预估请由项目负责人或各模块负责人补充。
> - 可根据实际进展进一步细化子任务。
> - 所有开发、测试、文档更新均需以 `docs/` 目录下的设计文档为唯一事实来源（SSoT），确保规范与实现一致。 