# 项目文档结构与协作指南

本文档旨在为团队成员及AI编程助手提供关于本项目文档结构、各文档用途以及如何协同使用这些文档进行开发和维护的指导。

## 1. 文档的单一事实来源 (Single Source of Truth - SSoT)

本项目所有技术设计、功能规格和实现细节的**权威信息源**均存储在 `docs/` 目录下的Markdown文件中。传统的交付文档（如日文的《機能仕様書》.docx 和《詳細設計書》.xlsx）应视为基于这些Markdown文档在特定时间点的输出或总结。

**核心原则：**

*   **先更新 `docs/`**: 任何设计变更、功能调整或实现细节的更新，都应首先在对应的Markdown文件中进行。
*   **再同步交付物**: 交付文档的更新应基于`docs/`中的最新内容进行。

## 2. `docs/` 目录结构概览

```plaintext
jcs-endpoint-monorepo/
├── apps/                     # 应用程序代码
├── automation/               # 自动化脚本代码
├── docs/                     # 核心技术文档 (中文内容, 英文文件名)
│   ├── README.md             # 本文档：项目文档结构与协作指南
│   ├── architecture/         # 系统架构、设计原则、ADRs
│   │   ├── system-architecture.md # 整体系统架构
│   │   ├── adrs/                  # (可选) 架构决策记录
│   │   └── diagrams/              # 架构图 (Mermaid源文件或图片)
│   ├── apis/                   # API规范 (OpenAPI)
│   │   └── openapi.v1.yaml
│   ├── components/             # 各核心组件/功能的详细说明
│   │   ├── README.md             # 功能模块与核心组件列表索引 (链接到各组件设计文档)
│   │   ├── 01-authentication/  # 示例：认证模块 (前端UI功能)
│   │   │   └── login-feature.md
│   │   ├── 03-servers/         # 示例：服务器列表及相关任务 (前端UI功能)
│   │   │   ├── server-list.md
│   │   │   └── tasks/            # 服务器列表相关的子功能或模态框设计
│   │   │       ├── export-operation-log.md
│   │   │       └── ... 
│   │   ├── actions/            # Server Actions 的详细设计文档
│   │   │   └── createTaskAction.md
│   │   ├── backend-services-functions/ # 后端Azure Functions 的详细设计文档
│   │   │   ├── function-task-execute.md
│   │   │   ├── function-runbook-monitor.md
│   │   │   ├── function-runbook-processor.md
│   │   │   ├── function-task-cancellation.md
│   │   │   ├── function-task-execute-timeout.md
│   │   │   ├── function-task-cancellation-timeout.md
│   │   │   ├── function-runbook-processor-timeout.md
│   │   │   └── README.md        # (可选) backend-services-functions 目录说明
│   │   └── ...                   # 其他组件/功能文档 (例如其他前端UI模块)
│   ├── data-models/            # 数据模型详细说明 (对Prisma Schema的补充)
│   │   ├── README.md             # 数据模型总览，指向Prisma Schema
│   │   ├── task.md
│   │   └── ...                   # 其他关键表的补充说明
│   ├── guides/                 # 开发、部署、迁移等指南
│   │   ├── development-setup.md
│   │   ├── functional-specification-migration-guide.md # 機能仕様書迁移指南
│   │   ├── detailed-design-migration-guide.md      # 詳細設計書(Excel)迁移指南
│   │   ├── ai-collaboration-and-documentation-standards.md # AI协作与文档规范
│   │   ├── environment-variables.md                # 环境变量指南
│   │   └── deployment-procedures.md
│   ├── definitions/            # 系统级定义与参考列表 (SSoT)
│   │   ├── README.md             # definitions 目录说明
│   │   ├── error-messages.md     # 错误消息定义
│   │   ├── glossary.md           # 项目术语表
│   │   └── lov-definitions.md    # 值列表(LOV)定义
├── docs-delivery/            # 存放交付用的日文文档 (Word, Excel)
│   ├── 機能仕様書/
│   │   └── JCSエンドポイント資産とタスク管理システム機能仕様書_vX.X.docx
│   ├── 詳細設計書/
│   │   └── JCSエンドポイント資産とタスク管理システム詳細設計書_vX.X.xlsx
│   └── templates/            # (可选) Word/Excel 模板
├── scripts/                  # 本地辅助脚本
└── README.md                 # 项目总览README
```

## 3. 各核心文档类型的职责与关联

### 3.1. 系统级文档 (`docs/architecture/`)

*   **`system-architecture.md`**:
    *   **内容**: 描述项目的整体架构、设计目标、核心约束、组件概览（高层职责与交互）、技术选型理由等。
    *   **来源**: 初始架构设计，并结合《機能仕様書》中的系统构成图和概要部分进行提炼和补充。
    *   **用途**: 为团队和AI提供项目的宏观视图和设计哲学。

### 3.2. 组件级文档 (`docs/components/`)

这是开发和维护的核心区域。**每个主要的前端功能模块、核心的后端服务组件（如Server Actions, Azure Functions, PowerShell Runbooks）以及重要的技术组件都应有其对应的Markdown详细设计文档，通常存放在`docs/components/`下的相关子目录中**（例如，前端模块如`03-servers/server-list.md`，Server Actions如`actions/createTaskAction.md`，Azure Functions如`backend-services-functions/function-task-execute.md`）。这些文件整合了来自《機能仕様書》和《詳細設計書》的相关信息，并按推荐的内容结构进行组织。

*   **`docs/components/README.md`**: 作为 `components` 目录的总入口，提供对各主要组件类别（如UI模块、Actions、后端Functions）及其设计文档的索引和导航。
*   **具体组件设计文档 (例如 `docs/components/03-servers/server-list.md` 或 `docs/components/backend-services-functions/function-task-execute.md`)**:
    *   **内容结构 (推荐)**:
        1.  概要 (Overview)
        2.  功能规格 (Functional Specifications)
        3.  技术设计与实现细节 (Technical Design & Implementation) (包含技术栈、UI定义、事件处理、数据/API交互、后端逻辑、错误处理、配置项等)
    *   **信息来源**: 功能需求主要来自《機能仕様書》；技术实现细节主要来自《詳細設計書》(Excel)。参考相应的迁移指南。
    *   **用途**: 作为组件开发的详细指南，AI编程的主要输入，团队知识共享库。

### 3.3. API规范 (`docs/apis/openapi.v1.yaml`)

*   **内容**: 使用OpenAPI 3.x 规范描述所有HTTP API的端点、请求/响应模型、认证方式等。
*   **来源**: 《詳細設計書》中的API接口定义部分，以及实际开发中的API设计。
*   **用途**: API的精确契约，可用于自动生成代码和文档。

### 3.4. 数据模型 (`docs/data-models/`)

*   **内容**:
    *   **权威来源**: 项目 `apps/jcs-endpoint-nextjs/prisma/schema.prisma` 文件是数据库结构的唯一事实来源 (SSoT)。
    *   **本目录用途**: 为 `prisma/schema.prisma` 中定义的关键模型（表）及其字段提供**中文业务逻辑名、详细用途解释、重要约束的业务含义说明、以及示例数据或特定业务规则的补充**。
*   **来源**: 《詳細設計書》中的数据库设计/表定义部分，并与 `prisma/schema.prisma` 保持一致和互补。
*   **用途**: 清晰定义数据结构（业务含义层面），辅助理解数据库设计。

### 3.5. 系统级定义与参考列表 (`docs/definitions/`)
*   **`error-messages.md`**: 系统中所有用户可见错误消息的权威定义。
*   **`glossary.md`**: 项目术语表。
*   **`lov-definitions.md`**: 所有值列表 (LOV) 的权威定义。
*   **用途**: 提供统一的、集中的核心定义参考。

### 3.6. 指南文档 (`docs/guides/`)

*   **内容**: 包含各类开发设置、编码标准、部署流程、文档迁移方法、AI协作规范、环境变量管理等。
*   **用途**: 规范开发行为，指导特定流程。

## 4. 文档工作流程：从源文档到交付物

### 4.1. 初期迁移 (Initial Migration)
(内容保持不变)
1.  分析源文档。
2.  规划Markdown结构。
3.  内容迁移 (遵循各迁移指南)。

### 4.2. 日常开发与维护 (Ongoing Development & Maintenance)
(内容保持不变)
1.  设计先行，更新Markdown。
2.  以Markdown为准。
3.  代码与文档同步。
4.  AI辅助开发 (基于Markdown文档)。

### 4.3. 生成交付文档 (Generating Deliverables)
(内容保持不变)
1.  以`docs/`为源。
2.  参考迁移指南的反向流程。
3.  内容提取、翻译与格式化。
4.  人工审查。
5.  归档。

## 5. 给AI助手的提示

*   当需要你协助编写代码、分析设计或生成文档时，请优先参考 `docs/` 目录下的Markdown文件。
*   我会明确指出你需要参考哪些具体的Markdown文件。
*   **整体系统架构**: 请查阅 [`系统架构设计文档`](./architecture/system-architecture.md)。
*   **具体组件/功能详细设计**: 请查阅 `docs/components/` 目录下对应的组件设计文档（例如，`actions/createTaskAction.md`，`backend-services-functions/function-task-execute.md`，或各UI模块的文档）。请首先参考 `docs/components/README.md` 以获取组件列表和导航。
*   **项目术语定义**: 请查阅 [`项目术语表`](./definitions/glossary.md)。
*   **值列表(LOV)定义**: 请查阅 [`LOV值列表定义`](./definitions/lov-definitions.md)。
*   **错误消息定义**: 请查阅 [`错误消息定义`](./definitions/error-messages.md)。
*   **API接口详细信息**: 请查阅 [`API规范文档`](./apis/openapi.v1.yaml) (若相关)。
*   **数据模型详细信息**: 请查阅 `prisma/schema.prisma` 及 `docs/data-models/` 下的补充说明文件。
*   **环境变量管理**: 请查阅 [`环境变量指南`](./guides/environment-variables.md)。

本指南旨在促进团队内部及与AI助手之间的高效协作，确保项目文档的统一性、准确性和可维护性。如有疑问或建议，请及时提出。
