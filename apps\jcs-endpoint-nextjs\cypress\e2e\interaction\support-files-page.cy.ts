import fileFixture from "../../fixtures/SupportFile.json";
import planFixture from "../../fixtures/PlanSupport.json";
import lovFixture from "../../fixtures/Lov.json";

describe("画面操作のテスト", () => {
  describe("サポート情報一覧画面", () => {
    const validCredentials = {
      userId: "hitachi.taro.aa",
      password: "changeit!@#",
    };
    const filter1 = "2023/11/28";
    const filter2 = "tim";
    const filter3 = "org";
    const filter4 = "AAA";
    const filter5 = "2023/12/23";
    const filter0 = "サポート情報一覧画面";
    const filterPage2 = "2023/02/1";
    const filterRefresh = "2023/12";
    // @ts-ignore
    const castValueToLabel = (value, lovs) => {
      // @ts-ignore
      const lovPair = lovs.find((lov) => lov.code === value);

      return lovPair?.value;
    };
    const files = fileFixture.SupportFile.map((file) => ({
      ...file,
      importance: castValueToLabel(file.importance, lovFixture.Lov),
    }));
    // @ts-ignore
    const doFilter = (keyword) => {
      const uniqueProducts = planFixture.PlanSupport.filter((file) =>
        ["standard", "lighta"].includes(file.planId),
      ).map((file) => file.serialNo);

      return files.filter(
        (file) =>
          uniqueProducts.includes(file.serialNo) &&
          (file.title.toLowerCase().includes(keyword.toLowerCase()) ||
            file.productName.toLowerCase().includes(keyword.toLowerCase()) ||
            file.updatedAt.toLowerCase().includes(keyword.toLowerCase()) ||
            file.publishedAt.toLowerCase().includes(keyword.toLowerCase()) ||
            file.importance.toLowerCase().includes(keyword.toLowerCase())),
      );
    };

    beforeEach(() => {
      cy.visit("/login");
      cy.get("#userId").type(validCredentials.userId);
      cy.get("#password").type(validCredentials.password);
      cy.get("button").click();
      cy.get("aside #file li:nth-child(5) a").click();
      cy.title().should("eq", "サポート情報一覧");
    });

    describe("フィルタリング", () => {
      it("最終更新日で検索する", () => {
        cy.get(".bg-white.h-full input").type(filter1);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter1).length / 10)}`);
      });

      it("タイトルで検索する", () => {
        cy.get(".bg-white.h-full input").type(filter2);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter2).length / 10)}`);
      });

      it("製品で検索する", () => {
        cy.get(".bg-white.h-full input").type(filter3);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter3).length / 10)}`);
      });

      it("重要度で検索する", () => {
        cy.get(".bg-white.h-full input").type(filter4);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter4).length / 10)}`);
      });

      it("公開日で検索する", () => {
        cy.get(".bg-white.h-full input").type(filter5);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter5).length / 10)}`);
      });

      it("結果が0件", () => {
        cy.get(".bg-white.h-full input").type(filter0);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9").should(
          "have.length",
          0,
        );
      });

      it("結果が2ページ", () => {
        cy.get(".bg-white.h-full input").type(filterPage2);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", "2");
      });

      it("クリアアイコンをクリックする", () => {
        cy.get(".bg-white.h-full input").type(filter1);
        cy.get(".bg-white.h-full button").first().click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filter1).length / 10)}`);
        cy.get(".bg-white.h-full button").last().click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter("").length / 10)}`);
      });
    });

    describe("ページネーション", () => {
      it("2ページボタンをクリックする", () => {
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "1");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "2");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .next()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "3");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .next()
          .next()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
        const maxPage = Math.ceil(doFilter("").length / 10);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage - 1}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .prev()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
      });

      it("3ページボタンをクリックする", () => {
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(3)
          .click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "1");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "2");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .next()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "3");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .next()
          .next()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
        const maxPage = Math.ceil(doFilter("").length / 10);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage - 1}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .prev()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
      });

      it("最大ページボタンをクリックする", () => {
        const maxPage = Math.ceil(doFilter("").length / 10);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(maxPage)
          .click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "1");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "2");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .next()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", `${maxPage}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage - 1}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .prev()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage - 2}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .prev()
          .prev()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
      });

      it("最大ページボタンの前のボタンをクリックする", () => {
        const maxPage = Math.ceil(doFilter("").length / 10);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(maxPage - 1)
          .click();
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "1");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", "2");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .first()
          .next()
          .next()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", `${maxPage - 1}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .prev()
          .should("have.prop", "tagName", "A")
          .invoke("text")
          .should("eq", `${maxPage - 2}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .prev()
          .prev()
          .prev()
          .should("have.prop", "tagName", "DIV")
          .invoke("text")
          .should("eq", "...");
      });

      it("次へアイコンをクリックする", () => {
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .then((text) => {
            const maxPage = Number(text);

            for (let index = 1; index < maxPage; index++) {
              cy.get("#page-right").click();
              cy.wait(1000);

              if (index !== maxPage - 3) {
                cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
                  .contains(index)
                  .should("have.prop", "tagName", "A");
                cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
                  .contains(index + 1)
                  .should("have.prop", "tagName", "DIV");
              }
            }
          });
      });

      it("前へアイコンをクリックする", () => {
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .then((text) => {
            const maxPage = Number(text);

            for (let index = 1; index < maxPage; index++) {
              cy.get("#page-right").click();
              cy.wait(1000);

              if (index !== maxPage - 3) {
                cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
                  .contains(index)
                  .should("have.prop", "tagName", "A");
                cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
                  .contains(index + 1)
                  .should("have.prop", "tagName", "DIV");
              }
            }

            for (let index = 1; index < maxPage; index++) {
              cy.get("#page-left").click();
              cy.wait(1000);

              if (index !== maxPage - 3) {
                cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
                  .contains(maxPage - index)
                  .should("have.prop", "tagName", "DIV");
                cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
                  .contains(maxPage - index + 1)
                  .should("have.prop", "tagName", "A");
              }
            }
          });
      });

      it("行数/ページが30に選択する", () => {
        cy.get(".bg-white.h-full select").select("30");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter("").length / 30)}`);
        cy.get(".rounded-b-lg").within(() => {
          cy.get("table").then(($child) => {
            const parentClientHeight = $child.parent().height() || 0;
            const childScrollHeight = $child[0].scrollHeight;

            expect(childScrollHeight).to.be.greaterThan(parentClientHeight);
          });
        });
        cy.get("table tbody tr th").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
        cy.get(".bg-white.h-full select").select("10");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter("").length / 10)}`);
        cy.get("table tbody tr th").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("行数/ページが50に選択する", () => {
        cy.get(".bg-white.h-full select").select("50");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter("").length / 50)}`);
        cy.get(".rounded-b-lg").within(() => {
          cy.get("table").then(($child) => {
            const parentClientHeight = $child.parent().height() || 0;
            const childScrollHeight = $child[0].scrollHeight;

            expect(childScrollHeight).to.be.greaterThan(parentClientHeight);
          });
        });
        cy.get("table tbody tr th").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
        cy.get(".bg-white.h-full select").select("10");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter("").length / 10)}`);
        cy.get("table tbody tr th").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });
    });

    // it("2ページボタンをクリックする", () => {
    //   cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //     .contains(2)
    //     .click();
    //   cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //     .contains(1)
    //     .should("have.prop", "tagName", "A");
    //   cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //     .contains(2)
    //     .should("have.prop", "tagName", "DIV");
    //   cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //     .contains(3)
    //     .should("have.prop", "tagName", "A");
    //   // cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //   //   .contains(4)
    //   //   .should("have.prop", "tagName", "A");
    // });

    // it("3ページボタンをクリックする", () => {
    //   cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //     .contains(3)
    //     .click();
    //   cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //     .contains(1)
    //     .should("have.prop", "tagName", "A");
    //   cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //     .contains(2)
    //     .should("have.prop", "tagName", "A");
    //   cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //     .contains(3)
    //     .should("have.prop", "tagName", "DIV");
    //   // cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //   //   .contains(4)
    //   //   .should("have.prop", "tagName", "A");
    // });

    // it("4ページボタンをクリックする", () => {
    //   cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //     .contains(4)
    //     .click();
    //   cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //     .contains(1)
    //     .should("have.prop", "tagName", "A");
    //   cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //     .contains(2)
    //     .should("have.prop", "tagName", "A");
    //   cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //     .contains(3)
    //     .should("have.prop", "tagName", "A");
    //   cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //     .contains(4)
    //     .should("have.prop", "tagName", "DIV");
    // });

    // it("次へアイコンをクリックする", () => {
    //   cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //     .last()
    //     .invoke("text")
    //     .then((text) => {
    //       const maxPage = Number(text);

    //       for (let index = 1; index < maxPage; index++) {
    //         cy.get("#page-right").click();
    //         cy.wait(1000);

    //         if (index !== maxPage - 3) {
    //           cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //             .contains(index)
    //             .should("have.prop", "tagName", "A");
    //           cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //             .contains(index + 1)
    //             .should("have.prop", "tagName", "DIV");
    //         }
    //       }
    //     });
    // });

    // it("前へアイコンをクリックする", () => {
    //   cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //     .last()
    //     .invoke("text")
    //     .then((text) => {
    //       const maxPage = Number(text);

    //       for (let index = 1; index < maxPage; index++) {
    //         cy.get("#page-right").click();
    //         cy.wait(1000);

    //         if (index !== maxPage - 3) {
    //           cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //             .contains(index)
    //             .should("have.prop", "tagName", "A");
    //           cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //             .contains(index + 1)
    //             .should("have.prop", "tagName", "DIV");
    //         }
    //       }

    //       for (let index = 1; index < maxPage; index++) {
    //         cy.get("#page-left").click();
    //         cy.wait(1000);

    //         if (index !== maxPage - 3) {
    //           cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //             .contains(maxPage - index)
    //             .should("have.prop", "tagName", "DIV");
    //           cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //             .contains(maxPage - index + 1)
    //             .should("have.prop", "tagName", "A");
    //         }
    //       }
    //     });
    // });

    // it("行数/ページが30に選択する", () => {
    //   cy.get(".bg-white.h-full select").select("30");
    //   cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //     .last()
    //     .invoke("text")
    //     .should("eq", `${Math.ceil(doFilter("").length / 30)}`);
    //   cy.get(".rounded-b-lg").within(() => {
    //     cy.get("table").then(($child) => {
    //       const parentClientHeight = $child.parent().height() || 0;
    //       const childScrollHeight = $child[0].scrollHeight;

    //       expect(childScrollHeight).to.be.greaterThan(parentClientHeight);
    //     });
    //   });
    //   cy.get("table tbody tr td:nth-child(2)").then(($column) => {
    //     const columnData = $column
    //       .map((_, el) => new Date(el.innerText).getTime())
    //       .get();

    //     const sortedColumnData = [...columnData].sort((a, b) => b - a);
    //     expect(columnData).to.deep.equal(sortedColumnData);
    //   });
    // });

    // it("行数/ページが50に選択する", () => {
    //   cy.get(".bg-white.h-full select").select("50");
    //   cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
    //     .last()
    //     .invoke("text")
    //     .should("eq", `${Math.ceil(doFilter("").length / 50)}`);
    //   cy.get(".rounded-b-lg").within(() => {
    //     cy.get("table").then(($child) => {
    //       const parentClientHeight = $child.parent().height() || 0;
    //       const childScrollHeight = $child[0].scrollHeight;

    //       expect(childScrollHeight).to.be.greaterThan(parentClientHeight);
    //     });
    //   });
    //   cy.get("table tbody tr th:nth-child(1)").then(($column) => {
    //     const columnData = $column
    //       .map((_, el) => new Date(el.innerText).getTime())
    //       .get();

    //     const sortedColumnData = [...columnData].sort((a, b) => b - a);
    //     expect(columnData).to.deep.equal(sortedColumnData);
    //   });
    // });

    describe("ソート", () => {
      it("テーブルヘーダに、最終更新日をクリックする", () => {
        cy.get("thead th span").contains("最終更新日").click();
        cy.get("thead th span")
          .should("contain", "最終更新日")
          .next("img")
          .should("have.class", "rotate-180");
        cy.get("thead th span")
          .contains("タイトル")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("重要度")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("公開日")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr").then(($rows) => {
          const columnData = $rows
            .map((_, row) => {
              // @ts-ignore
              const updatedAt = row.querySelector("th").innerText;
              // @ts-ignore
              const name = row.querySelector("td:nth-child(3)").innerText;
              return { updatedAt, name };
            })
            .get();

          const sortedColumnData = [...columnData].sort((a, b) => {
            const releaseDateComparison = a.updatedAt.localeCompare(
              b.updatedAt,
            );
            if (releaseDateComparison !== 0) {
              return releaseDateComparison;
            }
            return a.name.localeCompare(b.name);
          });

          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、タイトルをクリックする", () => {
        cy.get("thead th span").contains("タイトル").click();
        cy.get("thead th span")
          .should("contain", "タイトル")
          .next("img")
          .should("have.class", "rotate-180");
        cy.get("thead th span")
          .contains("最終更新日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("重要度")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("公開日")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(2)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            a.localeCompare(b),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、タイトルを2回クリックする", () => {
        cy.get("thead th span").contains("タイトル").click();
        cy.wait(2000);
        cy.get("thead th span").contains("タイトル").click();
        cy.get("thead th span")
          .should("contain", "タイトル")
          .next("img")
          .should("not.have.class", "rotate-180");
        cy.get("thead th span")
          .contains("最終更新日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("重要度")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("公開日")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(2)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、製品をクリックする", () => {
        cy.get("thead th span").contains("製品").click();
        cy.get("thead th span")
          .should("contain", "製品")
          .next("img")
          .should("have.class", "rotate-180");
        cy.get("thead th span")
          .contains("最終更新日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("タイトル")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("重要度")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("公開日")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(3)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            a.localeCompare(b),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、製品を2回クリックする", () => {
        cy.get("thead th span").contains("製品").click();
        cy.wait(2000);
        cy.get("thead th span").contains("製品").click();
        cy.get("thead th span")
          .should("contain", "製品")
          .next("img")
          .should("not.have.class", "rotate-180");
        cy.get("thead th span")
          .contains("最終更新日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("タイトル")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("重要度")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("公開日")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(3)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、重要度をクリックする", () => {
        cy.get("thead th span").contains("重要度").click();
        cy.get("thead th span")
          .should("contain", "重要度")
          .next("img")
          .should("have.class", "rotate-180");
        cy.get("thead th span")
          .contains("最終更新日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("タイトル")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("公開日")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(4)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            a.localeCompare(b),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、重要度を2回クリックする", () => {
        cy.get("thead th span").contains("重要度").click();
        cy.wait(2000);
        cy.get("thead th span").contains("重要度").click();
        cy.get("thead th span")
          .should("contain", "重要度")
          .next("img")
          .should("not.have.class", "rotate-180");
        cy.get("thead th span")
          .contains("最終更新日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("タイトル")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("公開日")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(4)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            b.localeCompare(a),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、公開日をクリックする", () => {
        cy.get("thead th span").contains("公開日").click();
        cy.get("thead th span")
          .should("contain", "公開日")
          .next("img")
          .should("have.class", "rotate-180");
        cy.get("thead th span")
          .contains("最終更新日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("タイトル")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("重要度")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        cy.get("table tbody tr td:nth-child(5)").then(($column) => {
          const columnData = $column.map((_, el) => el.innerText).get();

          const sortedColumnData = [...columnData].sort((a, b) =>
            a.localeCompare(b),
          );
          expect(columnData).to.deep.equal(sortedColumnData);
        });
      });

      it("テーブルヘーダに、公開日を2回クリックする", () => {
        cy.get("thead th span").contains("公開日").click();
        cy.wait(2000);
        cy.get("thead th span").contains("公開日").click();
        cy.get("thead th span")
          .should("contain", "公開日")
          .next("img")
          .should("not.have.class", "rotate-180");
        cy.get("thead th span")
          .contains("最終更新日")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("タイトル")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("製品")
          .next("img")
          .should("not.exist");
        cy.get("thead th span")
          .contains("重要度")
          .next("img")
          .should("not.exist");
        cy.wait(1000);
        // cy.get("table tbody tr td:nth-child(5)").then(($column) => {
        //   const columnData = $column.map((_, el) => el.innerText).get();

        //   const sortedColumnData = [...columnData].sort((a, b) =>
        //     b.localeCompare(a),
        //   );
        //   expect(columnData).to.deep.equal(sortedColumnData);
        // });
      });
    });

    describe("更新", () => {
      it("更新アイコンをクリックする", () => {
        cy.get(".bg-white.h-full input").type(filterRefresh);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full select").select("30");
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filterRefresh).length / 30)}`);
        cy.get("nav img").click();
        cy.wait(1000);
        cy.get(".bg-white.h-full input").should("have.value", filterRefresh);
        cy.get(".bg-white.h-full select option:selected").then(
          (selectedOption) => {
            cy.wrap(selectedOption).invoke("text").should("include", "10");
            cy.wrap(selectedOption).invoke("val").should("eq", "10");
          },
        );
      });
    });

    describe("ブラウザ", () => {
      it("更新ボタンをクリックする", () => {
        cy.get(".bg-white.h-full input").type(filterRefresh);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full select").select("30");
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filterRefresh).length / 30)}`);
        cy.reload();
        cy.wait(1000);
        cy.get(".bg-white.h-full input").should("have.value", filterRefresh);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filterRefresh).length / 30)}`);
        cy.get(".bg-white.h-full select option:selected").then(
          (selectedOption) => {
            cy.wrap(selectedOption).invoke("text").should("include", "30");
            cy.wrap(selectedOption).invoke("val").should("eq", "30");
          },
        );
      });

      it("戻るボタンをクリックし、進むボタンをクリックする", () => {
        cy.get(".bg-white.h-full input").type(filterRefresh);
        cy.get(".bg-white.h-full button").first().click();
        cy.wait(1000);
        cy.get(".bg-white.h-full select").select("30");
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .click();
        cy.wait(1000);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filterRefresh).length / 30)}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .should("have.prop", "tagName", "DIV");
        cy.go("back");
        cy.wait(1000);
        cy.get(".bg-white.h-full input").should("have.value", filterRefresh);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filterRefresh).length / 30)}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(1)
          .should("have.prop", "tagName", "DIV");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .should("have.prop", "tagName", "A");
        cy.get(".bg-white.h-full select option:selected").then(
          (selectedOption) => {
            cy.wrap(selectedOption).invoke("text").should("include", "30");
            cy.wrap(selectedOption).invoke("val").should("eq", "30");
          },
        );
        cy.go("forward");
        cy.wait(1000);
        cy.get(".bg-white.h-full input").should("have.value", filterRefresh);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .last()
          .invoke("text")
          .should("eq", `${Math.ceil(doFilter(filterRefresh).length / 30)}`);
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(2)
          .should("have.prop", "tagName", "DIV");
        cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
          .contains(1)
          .should("have.prop", "tagName", "A");
        cy.get(".bg-white.h-full select option:selected").then(
          (selectedOption) => {
            cy.wrap(selectedOption).invoke("text").should("include", "30");
            cy.wrap(selectedOption).invoke("val").should("eq", "30");
          },
        );
      });
    });
  });
});
