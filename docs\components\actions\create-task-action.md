# Server Action: `createTaskAction` - 后台任务创建

## 1. 概要 (Overview)

### 1.1 核心职责 (Core Responsibilities)
`createTaskAction` 是一个在服务器端执行的Next.js Server Action，作为所有通过门户界面发起的后台任务（例如操作日志导出、管理项目定义导入/导出等）的统一创建入口。其主要职责包括：
1.  接收并解析来自客户端的任务创建请求（包含任务类型、目标服务器ID及该任务特有的参数）。
2.  执行通用的前置校验：包括用户身份与会话验证、输入参数的基本格式校验。
3.  根据目标服务器ID从数据库获取必要的执行上下文信息，包括目标服务器名称 (`Server.name`)、许可证ID (`Server.licenseId`)、目标Azure虚拟机名称 (`Server.azureVmName`)、目标Docker容器名称 (`Server.dockerContainerName`)、以及该服务器关联的Hybrid Runbook Worker组名 (`Server.hrwGroupName`)。
4.  执行权限校验，确保当前登录用户的许可证ID (`currentUser.licenseId`) 与目标服务器的许可证ID (`serverLicenseId`) 一致。**对于特定任务类型如“操作日志导出” (`TASK_TYPE.OPLOG_EXPORT`)，还需额外校验用户的 `License.basicPlan` 是否在 `LOV` 表中 `OPERATION_LOG_CONFIG.ALLOWED_PLANS` 定义的允许列表中。**
5.  执行目标容器 (`azureVmName`, `dockerContainerName`) 并发状态的检查 (基于 `ContainerConcurrencyStatus` 表)：
    *   如果目标容器的并发状态记录不存在，则在本Action的数据库事务内（详细流程见2. 通用处理流程与逻辑 步骤6.2）创建该记录并初始化状态为 `IDLE`。
    *   如果记录存在且状态为 `BUSY`，则拒绝创建新任务并向用户返回 `messageId: 'EMEC0022'`。
    *   本Action**不负责**将容器状态更新为 `BUSY`；此操作由下游的[`TaskExecuteFunc`组件设计文档](../../components/backend-services-functions/function-task-execute.md)负责。
6.  根据不同的任务类型 (`taskType`)，委托执行该任务特定的参数详细校验和文件处理逻辑（如文件上传到临时存储）。这部分逻辑的详细定义参见各具体任务的组件设计文档。
7.  在数据库事务内，执行任务记录保留策略：确保每个服务器的任务记录不超过配置的上限（该上限值从 `LOV` 表 `TASK_CONFIG.MAX_RETENTION_COUNT` 获取，其定义参见[`LOV值列表定义`](../../definitions/lov-definitions.md)），并在删除特定类型的旧任务时，联动删除其关联的已导出文件或已上传的临时导入文件。
8.  在应用层显式生成一个新的全局唯一 `taskId` (UUID格式)。此 `taskId` 将作为 `Task` 表的主键，并由[`TaskExecuteFunc`组件设计文档](../../components/backend-services-functions/function-task-execute.md)用作提交给Azure Automation的作业ID。
9.  在数据库中通过单一事务操作（接步骤6.2后），创建新的`Task`记录，包含生成的 `taskId`、从步骤3获取的执行上下文信息（如 `targetVmName`, `dockerContainerName`, `hrwGroupName`）、任务特定参数 (如 `importedFileBlobPath`) 等所有相关信息。
10. 构造标准化的任务消息（主要包含 `taskId`，因为详细参数已存入`Task`表供[`TaskExecuteFunc`组件设计文档](../../components/backend-services-functions/function-task-execute.md)查询），并将其发送到Azure Service Bus的`TaskInputQueue`（队列名称通过环境变量 `SERVICE_BUS_TASK_INPUT_QUEUE_NAME` 配置，其定义参见[`环境变量指南`](../../guides/environment-variables.md)）。
11. **补偿逻辑**：如果在步骤5.1（文件上传到Blob临时区，针对导入任务）之后，后续的数据库事务（步骤6）或Service Bus消息发送（步骤7）失败，**必须负责删除**这个已上传的临时Blob文件。
12. 向客户端返回标准化的操作结果 (`CreateTaskActionResult`)，包含任务是否成功提交的状态和用户提示信息（其消息ID定义在[`错误消息定义`](../../definitions/error-messages.md)中）。**此返回值不直接包含新创建的 `taskId`。**

*(注: 调用此Server Action通常应在前端用户完成所有参数输入并通过最终确认对话框确认执行之后。)*

### 1.2 输入参数 (`formData`)
此Server Action通过 `FormData` 对象接收来自客户端的参数。以下是主要的通用和任务特定参数字段的定义。

| 字段名 (Key in `FormData`) | 数据类型 | 是否必需 | 描述 (中文) | 示例值 (来自 `FormData` 时通常为字符串) |
|-------------------------|------|------|---------|-----------------------------|
| `taskType` | `string` | 是 | 任务的类型代码。其值必须是 `LOV` 表中 `parentCode='TASK_TYPE'` 定义的有效 `code` 之一 (参考[`LOV值列表定义`](../../definitions/lov-definitions.md))。 | `"TASK_TYPE.OPLOG_EXPORT"` |
| `serverId` | `string` | 是 | 目标服务器的唯一ID (对应 `Server.id`)。 | `"clxqm5s2c000108l3g0r1h2f4"` |
| `exportStartDate` | `string` | 任务特定 | 操作日志导出的开始日期，格式为 "YYYY-MM-DD"。仅当 `taskType` 为 `'TASK_TYPE.OPLOG_EXPORT'` 时必需。 | `"2023-01-01"` |
| `exportEndDate` | `string` | 任务特定 | 操作日志导出的结束日期，格式为 "YYYY-MM-DD"。仅当 `taskType` 为 `'TASK_TYPE.OPLOG_EXPORT'` 时必需。 | `"2023-01-31"` |
| `importFile` | `File` | 任务特定 | 用户上传的管理项目定义CSV文件对象。仅当 `taskType` 为 `'TASK_TYPE.MGMT_ITEM_IMPORT'` 时必需。 (通过`FormData`直接传递`File`对象)。 | (一个实际的 `File` 对象) |
| `originalFileName` | `string` | 任务特定 | 上传文件的原始名称。此信息通常可以从客户端 `File` 对象的 `name` 属性获取。在服务器端，此名称用于日志记录、审计追踪及可能的错误反馈上下文。仅当 `taskType` 为 `'TASK_TYPE.MGMT_ITEM_IMPORT'` 时相关。 | `"my_definitions.csv"` |

*(注: 所有参数在Server Action内部会进行类型转换和进一步校验。客户端在构造 `FormData` 时应确保字段名和基本格式正确。更详细的类型定义（如 `CreateTaskActionParams`，如果适用）应在 `apps/jcs-endpoint-nextjs/app/lib/definitions.ts` 中维护，以指导客户端参数构造和服务器端解析逻辑。)*

### 1.3 返回值 (`TaskActionResult`)
此Server Action返回一个Promise，其解析值为一个包含操作结果的对象。该对象的TypeScript类型定义为 `TaskActionResult` (应在 `apps/jcs-endpoint-nextjs/app/lib/definitions.ts` 中维护)。

**`TaskActionResult` 对象结构:**

| 字段名 | 数据类型 | 描述 (中文) |
|-----|------|---------|
| `success` | `boolean` | 指示任务创建请求是否已成功被后端接受并发送到消息队列。`true`表示成功，`false`表示在 `createTaskAction` 处理过程中发生错误导致失败。 |
| `message` | `string?` | 直接的日文消息文本。通常，使用`messageId`查找标准消息文本，如需填充参数请填充业务参数。 |

## 2. 通用处理流程与逻辑 (Generic Processing Flow and Logic)

以下描述 `createTaskAction` 在接收到请求后的通用处理步骤。特定任务类型的差异化处理将在后续章节说明。

```mermaid
graph TD
    A["开始: Server Action (createTaskAction)"] --> B["步骤1: 解析通用参数<br/>(taskType, serverId)"];
    B -- "验证失败" --> ZFailCommonParam["返回错误响应<br/>(messageId: EMEC0021,<br/>message: 「サーバの接続に失敗したため、タスクを開始できませんでした...」)"];
    B -- "验证成功" --> C["步骤2: 用户身份与会话校验"];
    C -- "校验失败" --> ZFailAuth["返回错误响应<br/>(messageId: EMEC0005)"];
    C -- "校验成功" --> D["步骤3: 获取服务器执行配置<br/>(serverName, licenseId, azureVmName, dockerContainerName, hrwGroupName)<br/>与通用权限校验 (用户licenseId需与服务器licenseId匹配)"];
    D -- "配置不完整或通用权限校验失败" --> ZFailConfig["返回错误响应<br/>(messageId: EMEC0021 or EMEC0005)"];
    D -- "获取成功与通用权限校验通过" --> F_PrecheckConcurrency["步骤4: 容器并发状态确认"];
    F_PrecheckConcurrency -- "容器为 'BUSY'" --> ZFailConcurrency["返回错误响应<br/>(messageId: EMEC0022,<br/>message: 目标服务器名)"];
    F_PrecheckConcurrency -- "容器为 'IDLE' 或 记录不存在" --> E_GenTaskId["步骤5.0: 生成 taskId (UUID)"];
    E_GenTaskId --> E["步骤5.1: 特定任务类型逻辑执行<br/>(特定权限校验、参数构建、文件临时上传等)"];
    E -- "处理失败 (含特定权限校验失败)" --> ZFailTaskSpecific["返回错误响应<br/>(特定任务对应的messageId。<br/>若为导入任务且文件已上传，则删除临时文件)"];
    E -- "处理成功" --> F_Transaction["步骤6: DB核心事务处理"];
    
    subgraph F_Transaction_Details ["步骤6 DB核心事务处理细节"]
        direction TB
        F0_Retention["步骤6.1 (事务内)<br/>执行任务记录保留策略 (含关联Blob文件删除)"] --> F1_CreateConcurrency["步骤6.2 (事务内)<br/>创建ContainerConcurrencyStatus记录<br/>(若记录不存在，则初始化为IDLE)"];
        F1_CreateConcurrency --> F2_GenerateTaskName["步骤6.3 (事务内)<br/>生成Task.taskName<br/>({Server.name}-{LOV日文名}-{YYYYMMDDHHmmss})"];
        F2_GenerateTaskName --> F3_ParamsDb["步骤6.4 (事务内)<br/>构建Task.parametersJson"];
        F3_ParamsDb --> F4_CreateTask["步骤6.5 (事务内)<br/>创建Task记录<br/>(含taskId, 执行上下文信息等)"];
    end

    F_Transaction -- "DB事务失败" --> ZDbError["返回错误响应<br/>(messageId: EMEC0006)。<br/>若为导入任务且文件已上传，则删除临时文件"];
    F_Transaction -- "DB事务成功" --> G_SendMessage["步骤7: 构建任务消息并发送至Service Bus<br/>(TaskInputQueue，消息体仅含taskId)"];
    G_SendMessage -- "发送失败" --> ZQueueError["返回错误响应<br/>(messageId: EMEC0019)。<br/>Task状态更新为错误(resultMessage记录EMEC0019对应文本)。<br/>若为导入任务且文件已上传，则删除临时文件"];
    G_SendMessage -- "发送成功" --> H_Revalidate["步骤8: 调用revalidatePath使相关缓存失效"];
    H_Revalidate --> M_Success["步骤9: 返回成功响应<br/>(messageId: EMEC0025)"];
    
    ZFailCommonParam --> X["结束"];
    ZFailAuth --> X;
    ZFailConfig --> X;
    ZFailTaskSpecific --> X;
    ZFailConcurrency --> X;
    ZDbError --> X;
    ZQueueError --> X;
    M_Success --> X;
```

**通用处理步骤详解:**

1.  **解析通用参数 (`taskType`, `serverId`)**:
    *   从传入的 `FormData` 对象中安全地提取 `taskType` 和 `serverId` 字段的值。
    *   进行基础校验：确保这两个字段均已提供，且 `taskType` 的值是系统中已定义的有效任务类型代码 (对照 `LOV` 表中 `parentCode='TASK_TYPE'` 的 `code` 值列表，其定义参见[`LOV值列表定义`](../../definitions/lov-definitions.md))。`serverId` 应符合预期的格式（例如，CUID）。
    *   若基础校验失败（例如，参数缺失或 `taskType` 无效），立即构造并返回包含 `messageId: 'EMEC0021'` (message: 「サーバの接続に失敗したため、タスクを開始できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021)」) 的失败 `CreateTaskActionResult`。

2.  **用户身份与会话校验**:
    *   调用封装的用户会话服务 (例如，`app/lib/session.ts` 中的 `getIronSessionData()`) 获取当前已认证用户的核心信息，包括用户ID (`currentUser.id`，将作为 `Task.submittedByUserId`) 和其关联的许可证ID (`currentUser.licenseId`) 以及其许可证计划代码 (`currentUser.license.basicPlan`)。
    *   若用户未登录或会话无效（例如，无法获取到有效的用户信息），则构造并返回包含 `messageId: 'EMEC0005'` 的失败 `CreateTaskActionResult`。

3.  **获取服务器执行配置与通用权限校验**:
    *   调用数据服务函数 (例如，`app/lib/data.ts` 中的 `getServerDetailsForTask(serverId: string)`)，根据传入的 `serverId` 从数据库查询 `Server` 表记录。
    *   此函数应返回一个包含以下关键执行上下文信息的对象：
        *   `serverName: string` (来自 `Server.name`)
        *   `serverLicenseId: string` (来自 `Server.licenseId`)
        *   `azureVmName: string` (来自 `Server.azureVmName`)
        *   `dockerContainerName: string` (来自 `Server.dockerContainerName`)
        *   `hrwGroupName: string` (来自 `Server.hrwGroupName`)
    *   **通用权限校验**: 严格比较当前登录用户的 `currentUser.licenseId` 与从数据库获取的 `serverLicenseId`。两者必须完全一致。
    *   若无法根据 `serverId`找到服务器记录，或查询到的服务器配置信息不完整（例如，`dockerContainerName` 或 `azureVmName` 或 `hrwGroupName` 为空），或通用权限校验失败，则构造并返回相应的失败 `CreateTaskActionResult`：
        *   配置不完整: `messageId: 'EMEC0021'` (message: 「サーバの接続に失敗したため、タスクを開始できませんでした...」)。内部日志应记录详细原因。
        *   通用权限校验失败: `messageId: 'EMEC0005'`。

4.  **容器并发状态确认**:
    *   使用步骤3中获取的 `azureVmName` 和 `dockerContainerName`，查询 `ContainerConcurrencyStatus` 表以获取目标容器的当前状态。
    *   如果查询到记录且其 `status` 为 `'BUSY'`，则立即构造并返回 `messageId: 'EMEC0022'` (message: 「{サーバ名}に対するタスクを実行中のため実行できません。\n実行中のタスクが完了してから再度実行してください。」，其中 `{サーバ名}` 替换为步骤3中获取的 `serverName`) 的失败 `CreateTaskActionResult`。不进行后续操作。
    *   如果记录存在且 `status` 为 `'IDLE'`，或者记录不存在，则继续后续流程。(记录不存在的情况将在步骤6.2中处理)

5.  **特定任务类型逻辑执行 (taskId生成、参数构建、文件临时上传等)**:
    *   **5.0. `taskId` 生成**: 调用UUID库 (如 `uuid`) 显式生成一个新的、全局唯一的 `taskId` (UUID格式)。此 `taskId` 将用于后续所有操作。
    *   **5.1. 特定任务类型逻辑**:
        *   此步骤是 `createTaskAction` 的核心扩展点，其具体实现逻辑根据传入的 `taskType` 的不同而有所差异。
        *   **通用职责描述**:
            *   **特定权限校验**: 对于某些任务类型（如 `'TASK_TYPE.OPLOG_EXPORT'`），在此处执行额外的、特定于该任务的权限校验逻辑。若校验失败，返回特定权限错误 `messageId`。
            *   针对当前 `taskType`，从 `FormData` 中提取该任务特有的参数。
            *   对这些特有参数执行详细的服务器端校验。
            *   如果任务涉及文件上传（如 `'TASK_TYPE.MGMT_ITEM_IMPORT'`），在此步骤中处理将接收到的 `File` 对象上传到Azure Blob Storage的临时安全存储位置。成功上传后获取临时文件的引用 (`importedFileBlobPath` 和 `originalFileName`)。
            *   准备好两组特定于此任务的参数：`taskSpecificParamsForDb` (将与执行上下文信息一起存入 `Task` 表的相应字段或 `parametersJson`) 和 `taskSpecificParamsForSb` (将发送到Service Bus供Runbook使用)。
        *   **详细实现定位**: 每个具体任务类型的上述详细逻辑，应在其独立的组件设计文档的 `3.6.1` 章节中详细描述。本 `createTaskAction.md` 文档的第3节将进一步阐述此扩展机制。
        *   若在此步骤中的任何特定任务逻辑处理失败（包括特定权限校验失败，或文件上传失败），则应构造并返回包含针对该任务的特定 `messageId` (来自[`错误消息定义`](../../definitions/error-messages.md)) 的失败 `CreateTaskActionResult`。**特别地，如果文件已上传到临时存储后发生错误，必须删除该临时文件。**

6.  **DB核心事务处理**:
    *   以下所有数据库操作均在一个数据库事务 (`prisma.$transaction`) 内执行，以确保原子性。
    1.  **执行任务记录保留策略**: (逻辑与之前版本一致，详见1.1节第7点)
    2.  **创建`ContainerConcurrencyStatus`记录**: 使用步骤3中获取的 `azureVmName` 和 `dockerContainerName` 作为复合主键，查询 `ContainerConcurrencyStatus` 表。如果记录不存在，则创建新记录，`status` 初始化为 `'IDLE'`，`currentTaskId` 为 `NULL`。如果记录已存在（且状态为IDLE，因为BUSY状态已在步骤4被拦截），则不进行操作。
    3.  **生成`Task.taskName`**: 格式为 `{Server.name}-{taskTypeの日本語名}-{YYYYMMDDHHmmss}`。其中 `{Server.name}` 为步骤3获取的服务器名，`{taskTypeの日本語名}` 为根据 `taskType` 代码从 `LOV` 表 (`parentCode='TASK_TYPE'`) 查询到的对应日文名称 (`name` 字段)，日期时间为当前生成时间。
    4.  **构建`Task.parametersJson`**: 合并从步骤5.1中准备的 `taskSpecificParamsForDb` 中与任务参数相关的部分 (如 `exportStartDate`, `exportEndDate`, `importedFileBlobPath`, `originalFileName`)，并序列化为JSON字符串。
    5.  **创建`Task`记录**: 向 `Task` 表插入新记录，使用步骤5.0中生成的 `taskId` 作为主键，并包含所有必要信息：
        *   `taskName` (从步骤6.3生成)
        *   `taskType`
        *   `status: 'TASK_STATUS.QUEUED'` (其定义参见[`LOV值列表定义`](../../definitions/lov-definitions.md))
        *   `submittedAt: new Date()`
        *   `submittedByUserId: currentUser.id`
        *   `licenseId: currentUser.licenseId`
        *   `targetServerId: serverId`
        *   `targetServerName: serverName` (从步骤3获取)
        *   `targetVmName: azureVmName` (从步骤3获取)
        *   `dockerContainerName: dockerContainerName` (从步骤3获取)
        *   `hrwGroupName: hrwGroupName` (从步骤3获取)
        *   `parametersJson` (从步骤6.4构造)
    *   **DB事务失败处理**: 若上述任一步骤（除Blob删除外）失败导致事务回滚，捕获异常，记录错误。**如果文件已在步骤5.1中上传到临时存储（针对导入任务），则必须删除该临时文件。** 然后返回 `messageId: 'EMEC0006'` 的失败 `CreateTaskActionResult`。

7.  **构建任务消息并发送至Service Bus (`TaskInputQueue`)**:
    *   仅当步骤6的数据库事务成功提交后执行。
    *   消息体应尽量简洁，主要包含 `taskId` (步骤5.0中生成的)。[`TaskExecuteFunc`组件设计文档](../../components/backend-services-functions/function-task-execute.md)会根据此 `taskId` 从数据库查询完整的任务详情和参数。
        ```json
        {
          "taskId": "generated-uuid-for-the-task"
        }
        ```
    *   目标队列名称通过环境变量 `SERVICE_BUS_TASK_INPUT_QUEUE_NAME` 配置 (参考[`环境变量指南`](../../guides/environment-variables.md))。
    *   **发送失败处理 (补偿逻辑)**:
        *   记录严重错误。
        *   更新 `Task` 记录状态为 `'TASK_STATUS.COMPLETED_ERROR'` (参考[`LOV值列表定义`](../../definitions/lov-definitions.md))，并在 `resultMessage` 字段中记录 `EMEC0019` 对应的日文消息文本。
        *   **如果文件已在步骤5.1中上传到临时存储（针对导入任务），则必须删除该临时文件。**
        *   返回 `messageId: 'EMEC0019'` (message: 「サーバの接続に失敗したため、タスクを開始できませんでした...」) 的失败 `CreateTaskActionResult`。

8.  **调用`revalidatePath`使相关缓存失效**:
    *   成功创建任务并发送消息后，调用 `revalidatePath('/dashboard/tasks')` (及其他可能受影响的路径，例如 `/dashboard/operation-logs` 如果创建的是操作日志导出任务)。

9.  **返回成功响应**:
    *   返回 `{ success: true, messageId: "EMEC0025" }`。

## 3. 特定任务类型处理逻辑的扩展点

`createTaskAction` 的通用处理流程在上述第5.1步 (“特定任务类型逻辑执行”) 中，通过判断传入的 `taskType` 参数值，来调用或执行针对该特定任务类型的附加处理逻辑。这部分逻辑是实现不同后台任务（如操作日志导出、管理项目定义导入/导出等）差异化行为的关键。

每个具体任务类型的组件设计文档（例如，[`操作日志导出功能组件设计`](../../components/03-servers/tasks/export-operation-log.md)）必须在其 `3.6.1` 章节（或类似章节，标题为“`createTaskAction` 中处理 `[TASK_TYPE_CODE]` 的特定逻辑分支”）中，详细描述当 `createTaskAction` 处理该特定 `taskType` 时，需要执行的以下方面的详细逻辑：

*   **该任务类型的唯一标识 (`taskType` LOV Code 值)**。

*   **在步骤5.0中生成的 `taskId` 如何被用于此特定任务逻辑** (例如，在构造Blob上传路径时)。

*   **此任务类型特有的权限校验逻辑 (若有)**:
    *   例如，对于 `'TASK_TYPE.OPLOG_EXPORT'`，需要校验当前登录用户的 `License.basicPlan` 是否存在于 `LOV` 表中 `parentCode='OPERATION_LOG_CONFIG.ALLOWED_PLANS'` 定义的 `value` 列表中。
    *   如果校验失败，应立即构造并返回包含特定权限错误 `messageId` (如 `EMEC0005` 或更具体的) 的失败 `CreateTaskActionResult`。

*   **特有输入参数的提取与服务器端深度校验规则**:
    *   从 `FormData` 对象中提取该任务类型特有的所有参数字段。
    *   对这些提取出的参数执行严格的服务器端校验。例如：日期格式、日期范围、日期跨度（对照 `LOV('OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN').value`，其定义参见[`LOV值列表定义`](../../definitions/lov-definitions.md)）、上传文件类型（MIME类型，失败时返回 `EMEC0017`）。
    *   如果任何校验失败，应立即构造并返回包含特定 `messageId` (来自[`错误消息定义`](../../definitions/error-messages.md)) 的失败 `CreateTaskActionResult`。

*   **涉及文件上传时的处理逻辑 (例如，针对 `'TASK_TYPE.MGMT_ITEM_IMPORT'`)**:
    *   详细描述如何安全地将从 `FormData` 中获取的 `File` 对象的内容上传到Azure Blob Storage的一个临时存储位置。
    *   包括：
        *   使用步骤5.0中已生成的 `taskId`。
        *   **目标Blob容器名称的确定**: 通过环境变量 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` (其值应为 "assetsfield-def") 配置。
        *   **Blob路径/名称的生成规则**: 遵循 `{licenseId}/imports/{generatedTaskId}/assetsfield_def.csv` (文件名固定为 `assetsfield_def.csv`，其中 `{generatedTaskId}` 即为步骤5.0中生成的 `taskId`)。
        *   使用的SDK方法。
        *   成功后获取并记录临时Blob的引用 (`importedFileBlobPath` 为相对路径，其值格式为 `{licenseId}/imports/{generatedTaskId}/assetsfield_def.csv`；`originalFileName` 也被记录在 `taskSpecificParamsForDb` 中，其用途为日志、审计和错误反馈上下文)。
        *   处理上传失败的逻辑 (返回 `messageId: 'EMEC0018'`)。
    *   文件内容的详细业务逻辑校验（例如，CSV列的顺序、数据格式、业务规则符合性等）**不由** `createTaskAction`直接负责，而是由后续的后端处理流程（如Azure Automation Runbook）在实际执行导入操作时进行。`createTaskAction` 仅负责接收文件并将其安全传递给后端（通过上传到临时存储）。

*   **构造存入 `Task.parametersJson` 的特定参数部分 (`taskSpecificParamsForDb`) 的逻辑**:
    *   清晰定义一个JSON对象结构，该结构包含了所有需要持久化到 `Task` 表 `parametersJson` 字段的、特定于此任务类型的配置参数 (例如 `exportStartDate`, `exportEndDate` for OPLOG_EXPORT; `importedFileBlobPath`, `originalFileName` for MGMT_ITEM_IMPORT)。
    *   此对象将与步骤3中获取的通用执行上下文参数 (如 `dockerContainerName`, `azureVmName`, `hrwGroupName` 等，如果决定不将这些上下文参数直接存入`Task`表的专用字段，则它们也应包含在此JSON中或一个独立的JSON结构中，然后一起或分别存入`parametersJson`或`Task`表的专用字段)。当前设计倾向于将执行上下文信息直接存入Task表的专用字段，`parametersJson`仅存储任务特定参数。

*   **构造发送到Service Bus消息体中，供下游[`TaskExecuteFunc`](../../components/backend-services-functions/function-task-execute.md)使用的参数结构 (如果除了`taskId`外还需要其他信息直接在消息中传递)**:
    *   清晰定义一个JSON对象结构，该结构包含了所有需要直接通过Service Bus消息传递给[`TaskExecuteFunc`](../../components/backend-services-functions/function-task-execute.md)的、特定于此任务类型的参数。当前设计倾向于仅在消息中传递 `taskId`，由[`TaskExecuteFunc`](../../components/backend-services-functions/function-task-execute.md)根据 `taskId` 查询数据库获取全部所需参数。 如果此策略有变，需在此处明确。

*   **此任务类型特有的、未被通用错误处理覆盖的错误及其对应的 `messageId`** (这些 `messageId` 定义在[`错误消息定义`](../../definitions/error-messages.md)中)。

## 4. 通用错误处理机制

`createTaskAction` Server Action 采用以下通用错误处理机制：

*   **顶层异常捕获**: Server Action 主函数体使用 `try...catch` 捕获任何未预料的异常，记录严重错误日志，并向客户端返回 `messageId: "EMEC0027"` (message: 「サーバの接続に失敗したため、タスクを開始できませんでした...」) 的失败 `CreateTaskActionResult` (表示未预期的内部错误)。
*   **可预期的业务逻辑错误**: 在各处理步骤中明确捕获，并构造返回包含具体 `messageId` 和可选 `message` 的失败 `CreateTaskActionResult`。所有 `messageId` 均指向[`错误消息定义`](../../definitions/error-messages.md)中。
*   **关键操作失败后的补偿逻辑**:
    *   **数据库事务失败**: 事务自动回滚。**如果文件已在步骤5.1中上传到临时存储（针对导入任务），则必须删除该临时文件。** 返回数据库错误结果 (`messageId: 'EMEC0006'`)。特别地，如果在执行任务记录保留策略的步骤6.1中删除Blob文件失败，该失败不应导致主事务回滚，应记录错误并继续。
    *   **Service Bus消息发送失败**: 执行补偿操作（更新`Task`状态为`'TASK_STATUS.COMPLETED_ERROR'`并在`resultMessage`字段中记录 `EMEC0019` 对应的日文消息文本）。**如果文件已在步骤5.1中上传到临时存储（针对导入任务），则必须删除该临时文件。**然后返回队列发送错误结果 (`messageId: 'EMEC0019'`)。

## 5. 依赖的数据服务和外部服务接口契约 (简述)

`createTaskAction` Server Action 的执行依赖于项目内部封装的多个数据服务函数以及与外部Azure服务的交互。这些依赖的职责和大致接口契约如下：

*   **用户会话服务 (`app/lib/session.ts`)**:
    *   `getIronSessionData()`: 获取并验证当前用户会话信息 (用户ID, 许可证ID, 许可证计划代码 `License.basicPlan`)。
*   **核心数据服务模块 (`app/lib/data.ts`)**:
    *   `getServerDetailsForTask(serverId)`: 获取服务器执行上下文配置 (name, licenseId, azureVmName, dockerContainerName, hrwGroupName)。
    *   `getContainerStatus(azureVmName, dockerContainerName)`: 查询容器并发状态。
    *   `createOrGetContainerStatusInTx(tx, azureVmName, dockerContainerName)`: 在事务中查询或创建（如果不存在则初始化为IDLE）`ContainerConcurrencyStatus`记录。
    *   `createTaskInDbInTx(tx, taskData)`: 在事务中创建新Task记录 (包含所有必要字段，如 id, taskName, taskType, status, submittedAt, submittedByUserId, licenseId, targetServerId, targetServerName, targetVmName, dockerContainerName, hrwGroupName, parametersJson)。
    *   `getTasksForRetentionCheck(tx, serverId, limit)`: 在事务中查询指定服务器的已完成旧任务记录，用于保留策略。
    *   `deleteTaskAndAssociatedResourcesInTx(tx, task: Task, blobDeleteService)`: 在事务中删除指定任务记录，并根据任务类型调用 `blobDeleteService` 来删除关联的 `OperationLog` 记录和Blob文件（包括导入任务的临时文件和导出任务的产物文件）。
    *   `updateTaskStatusAndError(taskId, newStatus, resultMessage?, errorMessage?, errorCode?)`: (补偿用) 更新Task状态和结果/错误信息。
    *   `fetchCachedLovByCode(code)` / `fetchCachedLovValue(code, defaultValue)` / `fetchLovValuesByParentCode(parentCode)`: 获取LOV配置。
*   **文件上传服务 (封装Azure Blob Storage SDK)**:
    *   `uploadFileToTempBlob(file: File, targetRelativePath: string, containerNameEnvVar: string)`: 上传文件至由环境变量 `containerNameEnvVar` 指定的临时Blob容器的 `targetRelativePath` (相对路径)。
*   **Blob存储删除服务 (封装Azure Blob Storage SDK)**:
    *   `deleteBlobFile(blobRelativePath: string, containerNameEnvVar: string)`: 删除由环境变量 `containerNameEnvVar` 指定的容器中的Blob文件（使用相对路径）。
*   **Azure Service Bus发送服务 (封装 `@azure/service-bus` SDK)**:
    *   `sendMessageToQueue(queueNameEnvVar: string, messageBody: any)`: 将消息发送到由环境变量 `queueNameEnvVar` 指定的队列。
*   **Next.js 缓存操作**:
    *   `revalidatePath(path)`: 使缓存失效。
*   **UUID生成服务**:
    *   例如 `import { v4 as uuidv4 } from 'uuid';` `const newTaskId = uuidv4();`

*(注：上述函数签名和模块路径为示例性描述，具体实现以项目代码为准。本组件设计文档主要阐明依赖关系和职责。)*
