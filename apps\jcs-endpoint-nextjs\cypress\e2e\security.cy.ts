describe("セキュリティテスト", () => {
  it("CSRF 防御テスト", () => {
    cy.request({
      method: "POST",
      url: "/api/auth/callback/credentials",
      form: true,
      body: {
        redirect: false,
        userId: "system01",
        password: "jp123456",
        callbackUrl: "http://localhost:3000/login",
        json: true,
      },
      failOnStatusCode: false,
    }).then((response) => {
      expect(response.status).to.eq(200);

      expect(response.body.url).to.contains("/api/auth/signin?csrf=true");
    });
  });

  it("XSS 防御テスト", () => {
    cy.intercept("GET", "/api/notifications/system", {
      statusCode: 200,
      body: [
        {
          id: "clmekkvls0003bjy0p7ty3fuu",
          content: "<script>alert('XSS attack');</script>通常のメッセージ",
          publishedAt: "2023/08/02",
        },
      ],
      headers: {
        "content-type": "text/plain",
      },
    });

    cy.visit("/login");
    cy.contains("通常のメッセージ").should("be.visible");
    cy.contains("XSS attack").should("not.exist");
  });
});
