/**
 * @fileoverview タスクファイルダウンロード用APIルート
 * @description
 * 管理項目定義のエクスポートタスクで生成されたファイルのダウンロード要求を処理するAPIルート。
 * 設計文書に従い、セッション認証とAzure Blob StorageからのSAS URL生成を経て、
 * セキュアなファイルダウンロードを提供する。
 *
 * 主な処理フロー：
 * 1. ユーザーセッションとライセンス権限の確認
 * 2. Azure Blob Storageコンテナ設定の取得
 * 3. ファイルパスの構築とSAS URL生成
 * 4. SAS URLへのリダイレクト応答
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { NextRequest, NextResponse } from "next/server";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

import { SessionData, sessionOptions } from "@/app/lib/session";
import { BlobActions } from "@/app/lib/integrations/azure-blob";
import { ServerDataLov } from "@/app/lib/data/lov";
import Logger from "@/app/lib/logger";
import {
  LOV_CODE_AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF,
  PORTAL_ERROR_MESSAGES,
  AZURE_BLOB_PATHS,
  FILE_NAMES,
} from "@/app/lib/definitions";

/**
 * GETリクエストを処理するハンドラ
 *
 * 管理項目定義のエクスポートタスクで生成されたファイルのダウンロード要求を処理する。
 * 設計文書に従い、データベース検証を行わずに直接SAS URLを生成してリダイレクトする。
 *
 * 処理概要：
 * - セッション認証によりライセンスIDを取得
 * - LOVからAzure Blob Storageコンテナ名を取得
 * - ファイルパスを{licenseID}/{AZURE_BLOB_PATHS.EXPORTS_PREFIX}/{taskID}/{FILE_NAMES.ASSETSFIELD_DEF_CSV}形式で構築
 * - SAS URLを生成してブラウザをリダイレクト
 *
 * @param _request NextRequestオブジェクト（未使用）
 * @param params URLから抽出された動的パラメータ
 * @param params.params パラメータオブジェクト
 * @param params.params.taskId ダウンロード対象のタスクID
 * @returns 成功時はSAS URLへのリダイレクト、失敗時はエラーレスポンス
 * @throws セッション無効時は401エラー
 * @throws LOV設定不正時は500エラー
 * @throws SAS URL生成失敗時は500エラー
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: { taskId: string } },
) {
  const { taskId } = params;

  try {
    // セッション認証とライセンス権限の確認
    const session = await getIronSession<SessionData>(cookies(), sessionOptions);
    if (!session.user?.licenseId) {
      Logger.error({
        message: "タスクファイルダウンロード: セッションが無効または未認証",
        taskId,
        sessionExists: !!session,
        userExists: !!session.user,
        source: "GET /dashboard/tasks/[taskId]/download",
      });
      return new NextResponse(
        PORTAL_ERROR_MESSAGES.EMEC0007,
        { status: 500 },
      );
    }
    const { licenseId } = session.user;

    // Azure Blob Storageコンテナ設定の取得
    const containerLov = await ServerDataLov.fetchLov(
      LOV_CODE_AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF,
    );

    if (!containerLov?.value) {
      Logger.error({
        message: "タスクファイルダウンロード: Azure Blob Storageコンテナ設定が見つからない",
        lovCode: LOV_CODE_AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF,
        taskId,
        licenseId,
        containerLovExists: !!containerLov,
        source: "GET /dashboard/tasks/[taskId]/download",
      });
      return new NextResponse(PORTAL_ERROR_MESSAGES.EMEC0007, { status: 500 });
    }

    // ファイルパスの構築とSAS URL生成
    // {licenseID}/{AZURE_BLOB_PATHS.EXPORTS_PREFIX}/{taskID}/{FILE_NAMES.ASSETSFIELD_DEF_CSV} の形式でパスを構築
    const blobPathSegments: string[] = [
      licenseId,
      AZURE_BLOB_PATHS.EXPORTS_PREFIX,
      taskId,
      FILE_NAMES.ASSETSFIELD_DEF_CSV,
    ];

    const blobUrlWithSAS = await BlobActions.generateBlobUrlWithSAS(
      containerLov.value,
      blobPathSegments,
    );

    // SAS URLへのリダイレクト実行
    Logger.info({
      message: "タスクファイルダウンロード: SAS URLリダイレクト実行",
      taskId,
      licenseId,
      containerName: containerLov.value,
      blobPath: blobPathSegments.join("/"),
      source: "GET /dashboard/tasks/[taskId]/download",
    });
    redirect(blobUrlWithSAS);
  } catch (error: any) {
    Logger.error({
      message: "タスクファイルダウンロード: 予期せぬエラーが発生",
      taskId,
      errorMessage: error.message,
      errorStack: error.stack,
      errorName: error.name,
      source: "GET /dashboard/tasks/[taskId]/download",
    });
    return new NextResponse(PORTAL_ERROR_MESSAGES.EMEC0007, { status: 500 });
  }
}
