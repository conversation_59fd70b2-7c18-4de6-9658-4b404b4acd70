import Header from "@/app/ui/header";
import { SessionProvider } from "next-auth/react";

describe("ナビゲーションバーのテスト", () => {
  it("ログアウトボタンが表示さることを確認する", () => {
    cy.intercept("GET", "/api/auth/session", {
      statusCode: 200,
      body: {
        user: {
          name: "hitachi.taro.aa",
          email: "hisol",
          id: "clpc4u33k0000bj7cjnde2ssr",
          userId: "hitachi.taro.aa",
          licenseId: "hisol",
        },
        expires: "2024-01-14T07:01:27.519Z",
      },
    });

    // ナビゲーションバーコンポーネントをマウント
    cy.mount(
      <SessionProvider>
        <Header />
      </SessionProvider>,
    );
    // タイトルが表示されることを確認
    cy.contains("現在のユーザー");
    // ログアウトボタンが表示されていることを確認
    cy.contains("ログアウト");
    cy.contains("パスワード変更");
    cy.contains("お知らせ");
    cy.contains("ライセンス");
  });

  it("ログアウトボタンをクリックすると確認ダイアログを表示する", () => {
    cy.intercept("GET", "/api/auth/session", {
      statusCode: 200,
      body: {
        user: {
          name: "hitachi.taro.aa",
          email: "hisol",
          id: "clpc4u33k0000bj7cjnde2ssr",
          userId: "hitachi.taro.aa",
          licenseId: "hisol",
        },
        expires: "2024-01-14T07:01:27.519Z",
      },
    });

    // ナビゲーションバーコンポーネントをマウント
    cy.mount(
      <SessionProvider>
        <Header />
      </SessionProvider>,
    );
    cy.wait(1000);
    cy.get("#logout-modal").should("have.class", "hidden");
    cy.get("nav").find("button").eq(0).click();
    cy.get("#logout-modal").should("not.have.class", "hidden");
  });

  it("パスワード変更ボタンをクリックするとダイアログを表示する", () => {
    cy.intercept("GET", "/api/auth/session", {
      statusCode: 200,
      body: {
        user: {
          name: "hitachi.taro.aa",
          email: "hisol",
          id: "clpc4u33k0000bj7cjnde2ssr",
          userId: "hitachi.taro.aa",
          licenseId: "hisol",
        },
        expires: "2024-01-14T07:01:27.519Z",
      },
    });

    // ナビゲーションバーコンポーネントをマウント
    cy.mount(
      <SessionProvider>
        <Header />
      </SessionProvider>,
    );
    cy.wait(1000);
    cy.get("#password-modal").should("have.class", "hidden");
    cy.get("nav").find("button").eq(1).click();
    cy.get("#password-modal").should("not.have.class", "hidden");
  });

  it("お知らせボタンをクリックするとダイアログを表示する", () => {
    cy.intercept("GET", "/api/auth/session", {
      statusCode: 200,
      body: {
        user: {
          name: "hitachi.taro.aa",
          email: "hisol",
          id: "clpc4u33k0000bj7cjnde2ssr",
          userId: "hitachi.taro.aa",
          licenseId: "hisol",
        },
        expires: "2024-01-14T07:01:27.519Z",
      },
    });

    // ナビゲーションバーコンポーネントをマウント
    cy.mount(
      <SessionProvider>
        <Header />
      </SessionProvider>,
    );
    cy.wait(1000);
    cy.get("#notification-modal").should("have.class", "hidden");
    cy.get("nav").find("button").eq(2).click();
    cy.get("#notification-modal").should("not.have.class", "hidden");
  });
});
