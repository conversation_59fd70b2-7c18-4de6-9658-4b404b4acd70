/**
 * @fileoverview 製品媒体データ生成器
 * @description 開発環境用の大量の製品媒体データを生成する
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { PrismaClient } from '@prisma/client';
import { BaseGenerator } from './base.generator';

/**
 * 製品媒体データ生成器クラス
 * 様々な製品とバージョンの媒体データを大量に生成する
 */
export class ProductMediaGenerator extends BaseGenerator {
  private readonly MEDIA_COUNT = 100; // 生成する媒体数
  private readonly BATCH_SIZE = 10;

  constructor(prisma: PrismaClient) {
    super(prisma, 'ProductMediaGenerator');
  }

  /**
   * 生成器名を取得する
   * @returns 生成器名
   */
  getGeneratorName(): string {
    return '製品媒体データ';
  }

  /**
   * 生成予定のデータ数を取得する
   * @returns 生成予定数
   */
  getExpectedCount(): number {
    return this.MEDIA_COUNT;
  }

  /**
   * 既存の製品媒体データをクリーンアップする
   * 関連するプラン製品も削除する
   */
  async cleanup(): Promise<void> {
    try {
      // 関連するプラン製品を先に削除
      await this.prisma.planProduct.deleteMany({});
      
      // 製品媒体データを削除
      const deleteResult = await this.prisma.productMedia.deleteMany({});
      console.log(`既存製品媒体データを削除しました (${deleteResult.count}件)`);
    } catch (error) {
      console.error('製品媒体データのクリーンアップに失敗しました', error);
      throw error;
    }
  }

  /**
   * 製品媒体データを生成する
   * 様々な製品とバージョンの媒体データを大量に生成する
   * @returns 生成されたデータの数
   */
  async generate(): Promise<number> {
    try {
      console.log(`${this.MEDIA_COUNT}件の製品媒体データを生成します`);

      // OSタイプのLOVデータを取得
      const osTypes = await this.getOSTypes();
      if (osTypes.length === 0) {
        console.warn('OSタイプのLOVデータが見つかりません。デフォルト値を使用します。');
      }

      console.log(`${osTypes.length}種類のOSタイプを使用します`);

      // バッチで製品媒体データを生成
      return await this.generateInBatches(
        this.MEDIA_COUNT,
        this.BATCH_SIZE,
        async (startIndex: number, count: number) => {
          return await this.generateMediaBatch(startIndex, count, osTypes);
        }
      );
    } catch (error) {
      console.error('製品媒体データ生成中にエラーが発生しました', error);
      throw error;
    }
  }

  /**
   * 製品媒体データのバッチを生成する
   * @param startIndex 開始インデックス
   * @param count 生成数
   * @param osTypes OSタイプのLOVデータ
   * @returns 生成された製品媒体データ
   */
  private async generateMediaBatch(
    startIndex: number,
    count: number,
    osTypes: any[]
  ): Promise<any[]> {
    const medias = [];

    for (let i = 0; i < count; i++) {
      const mediaIndex = startIndex + i + 1;
      const productInfo = this.generateProductInfo(mediaIndex, osTypes);
      
      const media = {
        name: productInfo.name,
        productCode: productInfo.productCode,
        version: productInfo.version,
        os: productInfo.os,
        releasedAt: this.generateReleaseDate(),
        mediaName: this.generateMediaName(productInfo.productCode, productInfo.version),
        mediaSize: this.generateMediaSize(),
        bigMediaSize: this.generateBigMediaSize(),
        documentName: this.generateDocumentName(productInfo.name),
        documentSize: this.generateDocumentSize(),
      };

      medias.push(media);
    }

    // バッチでデータベースに挿入
    await this.prisma.productMedia.createMany({
      data: medias,
    });

    return medias;
  }

  /**
   * 製品情報を生成する
   * @param index インデックス
   * @param osTypes OSタイプのLOVデータ
   * @returns 製品情報
   */
  private generateProductInfo(index: number, osTypes: any[]): {
    name: string;
    productCode: string;
    version: string;
    os: string;
  } {
    const products = [
      { name: 'JP1/統括マネージャ', code: 'JP1GM' },
      { name: 'JP1/中継マネージャ', code: 'JP1RM' },
      { name: '秘文/管理コンソール', code: 'HIBUN' },
      { name: 'JP1/ログ管理', code: 'JP1LOG' },
      { name: 'JP1/監視システム', code: 'JP1MON' },
      { name: 'JP1/バックアップ', code: 'JP1BAK' },
      { name: 'JP1/セキュリティ管理', code: 'JP1SEC' },
      { name: 'JP1/ネットワーク管理', code: 'JP1NET' },
      { name: 'JP1/システム運用', code: 'JP1OPS' },
      { name: 'JP1/性能管理', code: 'JP1PERF' },
      { name: 'JP1/構成管理', code: 'JP1CFG' },
      { name: 'JP1/変更管理', code: 'JP1CHG' },
      { name: 'JP1/問題管理', code: 'JP1PROB' },
      { name: 'JP1/資産管理', code: 'JP1ASSET' },
      { name: 'JP1/パッチ管理', code: 'JP1PATCH' }
    ];

    const versions = ['1.0', '1.1', '2.0', '2.1', '2.2', '3.0', '3.1', '4.0'];

    // LOVデータからOSタイプのcodeを取得、なければデフォルト値を使用
    const availableOSCodes = osTypes.length > 0
      ? osTypes.map(lov => lov.code)
      : ['OS_TYPE.WIN', 'OS_TYPE.LINUX', 'OS_TYPE.AIX', 'OS_TYPE.SOLARIS', 'OS_TYPE.HPUX'];

    // 一意性を保証するため、インデックスベースで組み合わせを生成
    const productIndex = index % products.length;
    const versionIndex = Math.floor(index / products.length) % versions.length;
    // OSは循環的に分配（より均等な分布のため）
    const osIndex = index % availableOSCodes.length;

    const product = products[productIndex];
    const version = versions[versionIndex];
    const os = availableOSCodes[osIndex];

    return {
      name: product.name,
      productCode: product.code,
      version,
      os,
    };
  }

  /**
   * リリース日を生成する
   * @returns リリース日（YYYY-MM-DD形式）
   */
  private generateReleaseDate(): string {
    const startDate = new Date('2020-01-01');
    const endDate = new Date('2024-12-31');
    const randomDate = new Date(
      startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime())
    );
    return randomDate.toISOString().split('T')[0];
  }

  /**
   * 媒体名を生成する
   * @param productCode 製品コード
   * @param version バージョン
   * @returns 媒体名
   */
  private generateMediaName(productCode: string, version: string): string {
    const mediaTypes = ['DVD', 'CD-ROM', 'USB', 'Download'];
    const mediaType = this.faker.randomFromArray(mediaTypes);
    
    return `${productCode}_v${version}_${mediaType}`;
  }

  /**
   * 媒体サイズを生成する（MB単位）
   * @returns 媒体サイズ
   */
  private generateMediaSize(): number {
    // 100MB から 8GB の範囲でランダム生成
    return this.faker.randomInt(100, 8192);
  }

  /**
   * 大容量媒体サイズを生成する（バイト単位）
   * @returns 大容量媒体サイズまたはnull
   */
  private generateBigMediaSize(): bigint | null {
    if (this.faker.randomBoolean(0.3)) { // 30%の確率で大容量媒体
      // 10GB から 100GB の範囲
      const sizeInGB = this.faker.randomInt(10, 100);
      return BigInt(sizeInGB * 1024 * 1024 * 1024);
    }
    return null;
  }

  /**
   * ドキュメント名を生成する
   * @param productName 製品名
   * @returns ドキュメント名
   */
  private generateDocumentName(productName: string): string {
    const docTypes = [
      'インストールガイド',
      'ユーザーマニュアル',
      'リリースノート',
      'セットアップガイド',
      'クイックスタートガイド'
    ];
    
    const docType = this.faker.randomFromArray(docTypes);
    return `${productName} ${docType}`;
  }

  /**
   * ドキュメントサイズを生成する（KB単位）
   * @returns ドキュメントサイズ
   */
  private generateDocumentSize(): number {
    // 500KB から 20MB の範囲でランダム生成
    return this.faker.randomInt(500, 20480);
  }

  /**
   * 製品媒体の統計情報を取得する
   * @returns 統計情報
   */
  async getStatistics(): Promise<{
    totalCount: number;
    byProductCode: Record<string, number>;
    byOS: Record<string, number>;
    byVersion: Record<string, number>;
    totalMediaSize: number;
    averageMediaSize: number;
    withBigMedia: number;
  }> {
    const [
      totalCount,
      medias,
      withBigMedia,
    ] = await Promise.all([
      this.prisma.productMedia.count(),
      this.prisma.productMedia.findMany({
        select: {
          productCode: true,
          os: true,
          version: true,
          mediaSize: true,
        },
      }),
      this.prisma.productMedia.count({ where: { bigMediaSize: { not: null } } }),
    ]);

    // 各種集計
    const byProductCode: Record<string, number> = {};
    const byOS: Record<string, number> = {};
    const byVersion: Record<string, number> = {};
    let totalMediaSize = 0;

    medias.forEach(media => {
      byProductCode[media.productCode] = (byProductCode[media.productCode] || 0) + 1;
      byOS[media.os] = (byOS[media.os] || 0) + 1;
      byVersion[media.version] = (byVersion[media.version] || 0) + 1;
      totalMediaSize += media.mediaSize;
    });

    const averageMediaSize = totalCount > 0 ? Math.round(totalMediaSize / totalCount) : 0;

    return {
      totalCount,
      byProductCode,
      byOS,
      byVersion,
      totalMediaSize,
      averageMediaSize,
      withBigMedia,
    };
  }

  /**
   * OSタイプのLOVデータを取得する
   * @returns OSタイプの配列
   */
  private async getOSTypes(): Promise<any[]> {
    return await this.prisma.lov.findMany({
      where: {
        parentCode: 'OS_TYPE',
        isEnabled: true,
      },
      select: {
        code: true,
        name: true,
        value: true,
      },
    });
  }

  /**
   * 配列からランダムな要素を選択する
   * @param array 選択元の配列
   * @returns ランダムに選択された要素
   */
  private randomFromArray<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }
}
