## 参照値一覧

本機能と関連する主要な参照値を以下に定義する。これらの値はデータベースの値の一覧（Lov）テーブルに格納され、画面表示や内部ロジックで使用される。

### <タスク種別> 親コード: TASK_TYPE

| # | コード | 名称 | 値 | 備考 |
|:---|:---|:---|:---|:---|
| 1 | TASK_TYPE.OPLOG_EXPORT | 操作ログのエクスポート | 操作ログのエクスポート | 操作ログをエクスポートするためのタスク種別。 |
| 2 | TASK_TYPE.MGMT_ITEM_IMPORT | 管理項目定義のインポート | 管理項目定義のインポート | 管理項目定義をインポートするためのタスク種別。 |
| 3 | TASK_TYPE.MGMT_ITEM_EXPORT | 管理項目定義のエクスポート | 管理項目定義のエクスポート | 管理項目定義をエクスポートするためのタスク種別。 |

### <タスクステータス> 親コード: TASK_STATUS

| # | コード | 名称 | 値 | 備考 |
|:---|:---|:---|:---|:---|
| 1 | TASK_STATUS.QUEUED | 実行待ち | 実行待ち | タスクがシステムに受け付けられ、実行待機中であることを示す。 |
| 2 | TASK_STATUS.PENDING_CANCELLATION | 中止待ち | 中止待ち | QUEUED（実行待ち）のタスクに対しユーザーによりタスクの中止が要求されたが、まだ中止されていない状態。 |
| 3 | TASK_STATUS.RUNBOOK_SUBMITTED | Runbookジョブ作成完了 | 実行中 | Azure AutomationのRunbook（基盤スクリプト）ジョブが正常に作成され、タスクの実行が開始したが、まだ終了していない状態。 |
| 4 | TASK_STATUS.RUNBOOK_PROCESSING | Runbookジョブ処理中 | 実行中 | Azure AutomationのRunbook（基盤スクリプト）ジョブの実行が終了（正常終了/異常状態/タイムアウトのどれか）し、タスクの後処理が行われている状態。 |
| 5 | TASK_STATUS.COMPLETED_SUCCESS | 正常終了 | 正常終了 | タスクが正常に完了したことを示す。 |
| 6 | TASK_STATUS.COMPLETED_ERROR | エラー | エラー | タスクがエラーやタイムアウトにより異常終了したことを示す。 |
| 7 | TASK_STATUS.CANCELLED | 中止 | 中止 | タスクが実行開始する前にユーザーによって中止されたことを示す。 |