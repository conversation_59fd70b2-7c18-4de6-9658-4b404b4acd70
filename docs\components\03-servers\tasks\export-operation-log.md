# 组件：操作日志导出功能 (Export Operation Log Feature)

## 1. 概要 (Overview)

### 1.1 目的 (Purpose)
本功能允许用户针对特定的JP1/ITDM2服务器，在指定的日期范围内，发起一个后台任务，以导出该服务器的操作日志。导出的日志文件（通常为ZIP压缩包，内含CSV或文本日志）将存储在Azure Blob Storage中，并在“操作日志列表”中供用户下载（任务列表通常只显示任务状态，不直接提供此类多文件结果的下载）。此功能主要用于安全审计、故障排查和合规性检查。

### 1.2 用户故事/需求 (User Stories/Requirements)
*   作为一名顾客系统管理员，我希望能方便地为选定的JP1/ITDM2服务器指定日期范围并导出其操作日志，以便进行审计分析。
*   作为一名顾客系统管理员，我希望导出的操作日志文件能够安全存储，并且我可以在任务完成后从门户的操作日志列表下载它们。
*   作为一名顾客系统管理员，我希望在选择日期范围时，系统能提示我最大允许的导出天数跨度。

### 1.3 高层依赖与交互 (High-Level Dependencies & Interactions)
*   **依赖于**:
    *   [服务器列表主功能组件设计](../server-list.md)：用户通过此界面的任务操作菜单发起本导出任务。本组件 (`OperationLogExportModal`) 被 `ServerListPage` 调用和管理。
    *   用户认证模块：确保用户已登录并具有相应权限，特别是其 `License.basicPlan` 必须符合 `LOV` 表中 `OPERATION_LOG_CONFIG.ALLOWED_PLANS` 定义的允许执行此功能的套餐代码列表。
    *   Server Action [`createTaskAction` 组件设计文档](../../actions/create-task-action.md)：作为所有后台任务创建请求的统一入口。
    *   数据服务模块 (`apps/jcs-endpoint-nextjs/app/lib/data.ts`)：用于从数据库获取 `LOV` 配置（如日期跨度限制 `OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN`，以及操作日志导出权限相关的 `OPERATION_LOG_CONFIG.ALLOWED_PLANS` 配置），间接通过 `createTaskAction` 使用。
    *   常量定义模块 (`apps/jcs-endpoint-nextjs/app/lib/definitions.ts`)：用于可能的内部常量定义（但用户可见消息文本通过消息ID从[`错误消息定义`](../../../definitions/error-messages.md)获取）。
    *   环境变量：`AZURE_STORAGE_CONTAINER_OPLOGS` (由 `RunbookProcessorFunc` 和 `createTaskAction` 中的保留策略逻辑使用)。详细定义参见 [`环境变量指南`](../../../guides/environment-variables.md)。
    *   后端核心服务 (Azure Functions `RunbookProcessorFunc`, Azure Automation Runbook)：负责实际的任务编排与执行，以及结果文件归档和元数据记录。
    *   Azure SQL Database：存储 `Task`, `ContainerConcurrencyStatus`, `Server`, `License`, `Lov`, `OperationLog` 等表。
    *   Azure Blob Storage：用于存储最终导出的日志ZIP文件。
    *   Azure Files：Runbook将导出的日志ZIP文件输出到其Azure Files工作区的 `exports/` 目录，供 `RunbookProcessorFunc` 归档。
*   **交互**:
    *   用户在[服务器列表主功能组件设计](../server-list.md)的界面选择“操作ログのエクスポート”操作后，`ServerListPage` 组件将显示本功能对应的参数输入对话框 (`OperationLogExportModal`)。
    *   用户在 `OperationLogExportModal` 中输入日期范围并点击“エクスポート”按钮后，该模态框会将选定的日期参数回调给 `ServerListPage`。
    *   `ServerListPage` 随后会弹出一个通用的二次确认模态框 (使用 `app/ui/message-modal.tsx`)，显示最终确认信息 (使用消息ID `EMEC0026`，其日文文本为 “{0}の操作ログを{1}から{2}の期間でエクスポートします。\nよろしいですか？”)，其中占位符由服务器名和日期范围替换。
    *   用户在通用确认对话框中确认后，`ServerListPage` 才调用 [`createTaskAction` Server Action](../../actions/create-task-action.md)，并将任务类型 (`TASK_TYPE.OPLOG_EXPORT`)、服务器ID以及指定的日期范围作为参数提交。**`createTaskAction` 成功后会将任务消息发送到队列，后续由一系列后端Azure Functions进行异步处理，主要包括：[`TaskExecuteFunc`](../../backend-services-functions/function-task-execute.md) 负责任务的启动和Runbook提交，[`RunbookMonitorFunc`](../../backend-services-functions/function-runbook-monitor.md) 负责监控Runbook执行状态，[`RunbookProcessorFunc`](../../backend-services-functions/function-runbook-processor.md) 负责处理最终结果（包括将导出的日志文件归档到Azure Blob Storage并在`OperationLog`表创建记录）和资源清理。前端在 `createTaskAction` 调用成功后，不直接获取新任务ID，而是通过后续的任务列表刷新来查看新创建的任务及其状态。**
    *   任务的执行状态和结果可在[任务列表组件设计](../../13-task-list.md)中跟踪。
    *   导出的日志文件最终通过[操作日志列表组件设计](../../08-operation-log-list.md)提供的链接从Azure Blob Storage下载。每个导出的ZIP文件对应 `OperationLog` 表中的一条记录。

## 2. 功能规格 (Functional Specifications)

### 2.1 主要流程 (High-Level Flow)
```mermaid
graph TD
    A["用户在服务器列表选择目标服务器<br/>并从任务菜单选择“操作ログのエクスポート”"] --> B["服务器列表页面 (ServerListPage): <br/>打开“操作日志导出参数对话框”<br/>(OperationLogExportModal)"];
    B --> C["用户: 在 OperationLogExportModal 中<br/>通过日历选择有效的开始日期和结束日期"];
    C --> D{用户点击 OperationLogExportModal 的<br/>“エクスポート”按钮};
    D -- 参数通过客户端校验 --> E_ParamsSubmitted["OperationLogExportModal:<br/>将选定的日期参数回调给 ServerListPage"];
    E_ParamsSubmitted --> E_ConfirmOpen["ServerListPage:<br/>打开通用二次确认模态框 (MessageModal)<br/>(使用消息ID EMEC0026，填充服务器名和日期范围)"];
    E_ConfirmOpen -- 用户在 MessageModal 点击“OK/はい” --> F_CallServerAction["ServerListPage (Frontend):<br/>调用 createTaskAction Server Action<br/>(taskType: 'TASK_TYPE.OPLOG_EXPORT', serverId, exportStartDate, exportEndDate)"];
    F_CallServerAction -- Server Action成功接收并处理 --> G["服务器列表页面 (Frontend):<br/>根据消息ID EMEC0025 (参考 '错误消息定义')<br/>显示任务提交成功日文提示。<br/>(关闭所有相关模态框)"];
    G --> H["用户: 可在“任务列表”查看任务状态，<br/>并在“操作日志列表”查看和下载结果文件。<br/>(前端不直接从createTaskAction获取taskId)"];
    D -- 参数未通过客户端校验 (例如 EMEC0016, EMEC0024, EMEC0020) --> J["OperationLogExportModal:<br/>在对话框内显示错误提示<br/>(基于相应的消息ID)"];
    B -- 用户点击“キャンセル”或关闭 OperationLogExportModal --> K[操作取消, 参数对话框关闭];
    E_ConfirmOpen -- "用户在 MessageModal 点击“キャンセル”或关闭" --> K_BackToParams["操作取消, 返回参数输入模态框<br/>(OperationLogExportModal)，<br/>并保留之前输入的日期。"];
    F_CallServerAction -- "Server Action处理失败 (例如参数服务端校验失败, 容器繁忙 EMEC0022 等)" --> L["ServerListPage (Frontend):<br/>根据返回的消息ID (参考 '错误消息定义')<br/>显示相应的日文错误提示。<br/>(关闭所有相关模态框)"];
```

### 2.2 业务规则 (Business Rules)
*   本操作仅对类型为JP1/ITDM2的服务器（其`Server.type`值为`'SERVER_TYPE.GENERAL_MANAGER'`或`'SERVER_TYPE.RELAY_MANAGER'`，由[服务器列表主功能组件设计](../server-list.md)控制入口的可见性）可用。
*   **权限控制**: 用户的许可证计划 (`License.basicPlan`) 必须在 `LOV` 表中 `parentCode='OPERATION_LOG_CONFIG.ALLOWED_PLANS'` 定义的允许列表中，才有权限执行操作日志导出功能。此校验由[服务器列表主功能组件设计](../server-list.md)在渲染UI时执行，并且由 [`createTaskAction` Server Action](../../actions/create-task-action.md) 在后端再次校验。
*   用户必须在参数输入对话框 (`OperationLogExportModal`) 中指定一个有效的开始日期和结束日期。日期输入框初始值为空白，且只能通过日历图标点击打开日历菜单进行选择，键盘输入禁用。日历菜单的初始值（即首次打开时高亮的日期）为当前操作的日期。
*   **日期校验规则**: 客户端与服务器端均需执行以下校验：
    *   开始日期和结束日期均为必填项。客户端提示 `EMEC0016` (占位符 `{0}` 分别替换为“開始日”和“終了日”)。
    *   结束日期必须大于或等于开始日期。客户端/服务端提示 `EMEC0024`。
    *   指定的日期范围（结束日期 - 开始日期 + 1天）不能超过系统配置的最大允许天数。该最大天数从 `LOV` 表获取，其配置项的 `code` 为 `'OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN'` (例如，其 `value` 为 "30")。如果从`LOV`表获取此值失败，则使用系统预设的默认值（例如30天）。客户端/服务端提示 `EMEC0020` (占位符 `{0}` 替换为最大天数值)。服务器端的详细校验逻辑在[`createTaskAction`的特定分支](../../actions/create-task-action.md#3-特定任务类型处理逻辑的扩展点)中定义。
*   后台任务的并发控制由通用的 [`createTaskAction` Server Action](../../actions/create-task-action.md) (并发检查，若记录不存在则创建) 及其调用的后端服务 (`TaskExecuteFunc` 负责写锁)处理。
*   导出的操作日志文件（通常是多个ZIP压缩包，每个对应一个日志分片或源）将以特定命名规则存储在Azure Blob Storage中。目标容器的名称通过环境变量 `AZURE_STORAGE_CONTAINER_OPLOGS` 进行配置。Blob存储的路径格式示例为：`{licenseId}/{taskId}/{Task.taskName}_{連番}.zip` (此处的 `{Task.taskName}` 是 `Task` 表中记录的任务名，`{連番}` 是Runbook可能生成多个文件时的序号)。
*   每个成功的导出任务完成后，其生成的每个日志文件的元数据（如文件名、大小、关联的任务ID、所属许可证ID、实际存储的Blob文件名/路径等）将被记录到 `OperationLog` 数据库表中。此操作通常由后端的 `RunbookProcessorFunc` 在Runbook成功执行并上报结果后完成。其表结构定义参见[`OperationLog数据模型定义`](../../../data-models/operation-log.md)。
*   每个发起的导出任务都会在 `Task` 数据库表中创建一条记录，用于跟踪其生命周期。`Task.outputBlobPath` 字段**不用于**存储本类型任务的最终文件路径，因为操作日志导出可能产生多个文件，其元数据由 `OperationLog` 表管理。其表结构定义参见[`Task数据模型定义`](../../../data-models/task.md)。
*   **任务记录与文件保留策略**: `Task` 表中的任务记录以及关联的 `OperationLog` 表记录，将遵循系统中定义的任务记录保留策略（每个服务器的任务记录上限由 `LOV` 表 `TASK_CONFIG.MAX_RETENTION_COUNT` 的值设定，默认为10条，定义于[`LOV值列表定义`](../../../definitions/lov-definitions.md)）。当为某个服务器创建新任务导致超出此限制时，最早的旧 `Task` 记录将被删除。**由于本任务类型为操作日志导出，在删除对应的 `Task` 记录时，系统将联动删除 `OperationLog` 表中所有与该 `Task.id` 关联的记录，并删除这些 `OperationLog` 记录所指向的、存储在Azure Blob Storage中的所有操作日志ZIP文件。** 此保留策略的详细执行逻辑定义在 [`createTaskAction` Server Action](../../actions/create-task-action.md) 的任务记录保留策略部分。

### 2.3 用户界面概述 (User Interface Overview)
*   **发起入口**: [服务器列表主功能组件设计](../server-list.md)界面的每行JP1/ITDM2服务器记录的任务操作下拉菜单中的“操作ログのエクスポート”选项。
*   **主要界面**:
    1.  **操作日志导出参数输入对话框 (`OperationLogExportModal`)**:
        *   组件路径: `apps/jcs-endpoint-nextjs/app/ui/servers/modals/operation-log-export-modal.tsx`
        *   核心功能: 提供开始日期和结束日期的选择器（初始为空白，通过日历选择，日历初始为当天，键盘输入禁用），显示最大允许导出天数提示 (基于 `LOV` 表 `OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN` 的值，获取失败则用默认值)，进行客户端参数校验，并将有效的参数提交给调用方 (`ServerListPage`)。
    2.  **通用二次确认对话框 (由 `ServerListPage` 使用 `app/ui/message-modal.tsx` 弹出)**:
        *   在 `OperationLogExportModal` 提交有效参数后，由 `ServerListPage` 弹出。
        *   提示消息: 使用消息ID `EMEC0026` (“{0}の操作ログを{1}から{2}の期間でエクスポートします。\nよろしいですか？”)，其中 `{0}` 替换为目标服务器名称，`{1}` 替换为开始日期，`{2}` 替换为结束日期。

### 2.4 前提条件 (Preconditions)
*   用户已通过身份验证并成功登录到门户系统。
*   用户的许可证计划 (`License.basicPlan`) 必须在 `LOV` 表中 `OPERATION_LOG_CONFIG.ALLOWED_PLANS` 定义的允许列表中。
*   目标服务器记录在系统中存在，并且其类型允许执行操作日志导出任务。
*   后端任务处理相关的Azure服务（Service Bus, Automation, Functions, Storage等）及数据库表已正确配置并运行。
*   Server Action [`createTaskAction` 组件设计文档](../../actions/create-task-action.md) 已在 `apps/jcs-endpoint-nextjs/app/lib/actions.ts` 中定义并可用。
*   `LOV` 表中与操作日志导出相关的配置项（如 `TASK_TYPE.OPLOG_EXPORT`, `OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN`, `OPERATION_LOG_CONFIG.ALLOWED_PLANS`, `TASK_CONFIG.MAX_RETENTION_COUNT`）已正确配置。其权威定义参见[`LOV值列表定义`](../../../definitions/lov-definitions.md)。
*   相关的**环境变量**（如 `AZURE_STORAGE_CONTAINER_OPLOGS`）已正确设置。其权威定义参见[`环境变量指南`](../../../guides/environment-variables.md)。
*   用户提示信息的消息ID定义在[`错误消息定义`](../../../definitions/error-messages.md)中。

### 2.5 制约事项 (Constraints/Limitations)
*   不支持一次性为多个服务器批量导出操作日志。
*   导出操作是异步的后台任务，用户需要通过[任务列表组件设计](../../13-task-list.md)或[操作日志列表组件设计](../../08-operation-log-list.md)来查看任务的完成状态和下载导出的文件。
*   导出的日期范围受系统配置 (`LOV:OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN`) 的最大天数限制。

### 2.6 注意事项 (Notes/Considerations)
*   导出的操作日志文件的具体内容和格式，由目标服务器上JP1/ITDM2管理工具的日志导出功能决定。本系统负责触发该功能并收集其产物（可能为多个ZIP文件）。
*   对于非常大的日期范围或极大量的日志数据，导出任务的执行时间可能会较长。前端应有明确的任务已提交的异步处理提示 (消息ID `EMEC0025`)，后端应有相应的超时监控机制（参考[`系统架构文档`](../../../architecture/system-architecture.md#6-后端核心服务-backend-core-services---azure-functions)中关于后端核心服务的部分）。

### 2.7 错误处理概述 (Error Handling Overview)
*   **客户端参数校验错误**: 在参数输入对话框 (`OperationLogExportModal`) 中进行即时校验。若用户输入的日期范围无效（例如，未填开始/结束日期提示 `EMEC0016`，结束日期早于开始日期提示 `EMEC0024`，或超出最大允许天数跨度提示 `EMEC0020`），将在对话框内显示明确的日文错误提示信息（消息文本从[`错误消息定义`](../../../definitions/error-messages.md)获取），并阻止提交。
*   **任务提交失败 (由 `createTaskAction` 返回)**: 如果 [`createTaskAction` Server Action](../../actions/create-task-action.md) 因参数服务端校验失败、目标服务器容器繁忙、权限不足或后端队列服务暂时不可用等原因未能成功创建任务，它将返回包含错误信息的 `CreateTaskActionResult` 对象。调用方（[服务器列表主功能组件设计](../server-list.md)）将根据返回的消息ID (其日文文本在[`错误消息定义`](../../../definitions/error-messages.md)) 向用户显示相应的日文错误提示。
*   **后台任务执行失败**: 若任务在后端执行过程中失败（例如，Runbook执行错误，可能返回 `EMET0012` - 有详细错误），`Task` 表中对应任务的状态将被更新为错误状态，并可能记录错误详情。用户可在[任务列表组件设计](../../13-task-list.md)中查看到此失败状态。

### 2.8 相关功能参考 (Related Functional References)
*   [服务器列表主功能组件设计](../server-list.md)
*   Server Action: [`createTaskAction` 组件设计文档](../../actions/create-task-action.md)
*   [任务列表组件设计](../../13-task-list.md)
*   [操作日志列表组件设计](../../08-operation-log-list.md)
*   数据模型:
    *   [`Task` 数据模型定义](../../../data-models/task.md)
    *   [`Server` 数据模型定义](../../../data-models/server.md)
    *   [`License` 数据模型定义](../../../data-models/license.md)
    *   [`ContainerConcurrencyStatus` 数据模型定义](../../../data-models/container-concurrency-status.md)
    *   [`OperationLog` 数据模型定义](../../../data-models/operation-log.md)
*   系统级定义:
    *   [`LOV值列表定义`](../../../definitions/lov-definitions.md)
    *   [`错误消息定义`](../../../definitions/error-messages.md)
    *   [`项目术语表`](../../../definitions/glossary.md)
*   [`环境变量指南`](../../../guides/environment-variables.md)
*   常量定义: `apps/jcs-endpoint-nextjs/app/lib/definitions.ts` (用于内部常量，用户消息通过消息ID获取)

---

## 3. 技术设计与实现细节 (Technical Design & Implementation)

### 3.1 技术栈与依赖 (Technology Stack & Dependencies)
*   **前端 (操作日志导出参数模态框组件 - `OperationLogExportModal`)**:
    *   React (Next.js App Router 架构下的Client Component)
    *   日期选择器组件 (例如，`react-datepicker`，或项目统一封装的日期选择器)
    *   客户端状态管理: 使用React Hooks (`useState` 或 `useReducer`) 管理模态框内部的表单数据（开始日期、结束日期）、校验错误信息、以及提交状态。
*   **后端 (主要通过 [`createTaskAction` Server Action](../../actions/create-task-action.md))**:
    *   Next.js Server Action (`createTaskAction` 位于 `apps/jcs-endpoint-nextjs/app/lib/actions.ts`)
    *   Prisma ORM (通过 `app/lib/data.ts` 封装数据访问逻辑，间接使用)
    *   Azure Service Bus SDK (通过 `createTaskAction` 间接使用)
*   **共享类型定义与常量**: `apps/jcs-endpoint-nextjs/app/lib/definitions.ts` (例如 `ServerDefinition`, `CreateTaskActionResult`)。
*   **配置数据来源**:
    *   `LOV` 表 (业务参数如日期跨度限制): 通过 `apps/jcs-endpoint-nextjs/app/lib/data.ts` 中的数据服务函数访问。其权威定义参见[`LOV值列表定义`](../../../definitions/lov-definitions.md)。
    *   环境变量 (基础设施配置如容器名): 通过 Node.js `process.env` 访问。其权威定义参见[`环境变量指南`](../../../guides/environment-variables.md)。

### 3.2 详细界面元素定义 (Detailed UI Element Definitions)

#### 3.2.1 操作日志导出参数对话框 (`apps/jcs-endpoint-nextjs/app/ui/servers/modals/operation-log-export-modal.tsx`)
*   **用途**: 允许用户为“操作日志导出”任务指定开始日期和结束日期。
*   **实现风格**: 自定义React客户端组件，提供日期选择、客户端即时校验反馈，并将最终参数回调给父组件。

| # | 控件内部逻辑名(中文) | 界面显示文本(日文) | 控件类型 (Type) | 建议英文ID (ID) | 数据来源/状态 (Data Source/State) (中文描述) | 主要行为/事件 (Behavior/Event) (中文描述) | 校验规则/显示逻辑 (Validation/Display Logic) (中文描述) | 格式/备注 (Format/Notes) (中文描述) |
|---|-------------|------------|-------------|-------------|------------------------------------|---------------------------------|---------------------------------------------|-----------------------------|
| 1 | 对话框容器 | - | `Dialog` (例如，Headless UI 的 `Dialog` 或自定义模态框容器) | `exportOpLogModal` | `props.isOpen: boolean` (从父组件 `ServerListPage` 传入，控制模态框的显示与隐藏) | - | 当 `props.isOpen` 为 `true` 时显示。应为模态对话框，通常包含背景遮罩层。 | 整体对话框组件。 |
| 2 | 对话框标题 | 操作ログのエクスポート | `Dialog.Title` (或 `h2`) | `exportOpLogModalTitle` | `props.title?: string` (可选，若父组件不传，则使用固定文本“操作ログのエクスポート”) | - | - | - |
| 3 | 关闭按钮 | × (或SVG关闭图标) | `button` (或 `Dialog.Close`) | `exportOpLogModalCloseBtn` | - | 点击时，调用从 `props` 传入的 `onClose` 回调函数 (即 `ServerListPage` 中的 `handleCloseTaskModal`)。 | 通常位于对话框右上角，视觉上应清晰可辨。 | - |
| 4 | 服务器名称提示 | 対象サーバ: {サーバ名} | `p` | `exportOpLogServerName` | `props.serverName: string` (从父组件传入，值为操作目标服务器的名称) | - | 固定显示，用于增强用户操作的上下文感知。 `{サーバ名}` 部分会被实际服务器名称动态替换。 | 文本样式应比主内容稍弱。 |
| 5 | 日期范围提示 | エクスポートする期間を{0}日以内で指定してください。 | `p` (class: `text-sm text-gray-600` 或类似) | `exportOpLogDateHint` | `props.maxExportDaysSpan: number` (从父组件 `ServerListPage` 传入，其值来源于 `LOV('OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN').value`，若LOV获取失败则使用默认值如30)。`{0}` 部分会被 `props.maxExportDaysSpan` 的实际数值动态替换。 | - | 固定显示的指导性文本，帮助用户理解日期选择的约束条件。 | - |
| 6 | "开始日"表单组 | - | `div` (class: `form-group mb-4` 或类似) | `opLogStartDateGroup` | - | - | 逻辑上将标签和日期选择器组织在一起。 | - |
| 7 | "开始日"标签 | 開始日 | `label` | `opLogStartDateLabel` | (静态日文文本 "開始日") | - | `htmlFor="opLogStartDateInput"` 以关联到对应的输入控件。 | - |
| 8 | "开始日"日期选择器 (必填) | (日历图标, Placeholder: YYYY/MM/DD) | `DatePicker` (自定义或第三方日期选择器组件) | `opLogStartDateInput` | `internalState.startDate: Date | null` (组件内部 `useState` 管理的开始日期状态)。**键盘输入禁用**。 | 当用户通过点击日历图标打开日历菜单并选择新的日期时，触发 `onChange` 事件，调用组件内部的 `handleStartDateChange(date: Date | null)` 事件处理器 (参见3.3.2.1节)，更新 `internalState.startDate` 并触发参数校验。日历菜单的初始默认高亮日期为当前操作的日期。 | **必填项**。选择的日期不能晚于当前选定的“结束日”。**初始值为空白**。用户界面上应以 "YYYY/MM/DD" 格式显示所选日期。若未选择，提示 `EMEC0016` (占位符 `{0}` 为“開始日”)。 | 例如使用 `react-datepicker`。 |
| 9 | "结束日"表单组 | - | `div` (class: `form-group mb-4` 或类似) | `opLogEndDateGroup` | - | - | 逻辑上将标签和日期选择器组织在一起。 | - |
| 10 | "结束日"标签 | 終了日 | `label` | `opLogEndDateLabel` | (静态日文文本 "終了日") | - | `htmlFor="opLogEndDateInput"` 以关联到对应的输入控件。 | - |
| 11 | "结束日"日期选择器 (必填) | (日历图标, Placeholder: YYYY/MM/DD) | `DatePicker` (自定义或第三方日期选择器组件) | `opLogEndDateInput` | `internalState.endDate: Date | null` (组件内部 `useState` 管理的结束日期状态)。**键盘输入禁用**。 | 当用户通过点击日历图标打开日历菜单并选择新的日期时，触发 `onChange` 事件，调用组件内部的 `handleEndDateChange(date: Date | null)` 事件处理器 (参见3.3.2.2节)，更新 `internalState.endDate` 并触发参数校验。日历菜单的初始默认高亮日期为当前操作的日期。 | **必填项**。选择的日期不能早于当前选定的“开始日”(提示 `EMEC0024`)。与“开始日”之间的天数跨度不能超过 `props.maxExportDaysSpan` 天 (提示 `EMEC0020`)。**初始值为空白**。用户界面上应以 "YYYY/MM/DD" 格式显示所选日期。若未选择，提示 `EMEC0016` (占位符 `{0}` 为“終了日”)。 | 例如使用 `react-datepicker`。 |
| 12 | 参数校验错误信息区 | (空白或具体的日文错误信息) | `div` (class: `text-red-500 text-sm min-h-[1.25rem]`) | `opLogValidationError` | `internalState.validationErrorMsgId: string | null` (组件内部 `useState` 管理的校验错误消息ID)。日文错误信息文本通过此ID从[`错误消息定义`](../../../definitions/error-messages.md)中获取，并替换占位符（若有）。 | - | 当 `internalState.validationErrorMsgId` 有值时，显示对应的日文错误提示。例如，消息ID `EMEC0016`, `EMEC0024`, 或 `EMEC0020` 对应的文本。 | 确保有足够的空间显示单行错误信息。 |
| 13 | 对话框操作按钮区域 | - | `div` (class: `flex justify-end space-x-2 mt-6`) | `opLogModalActions` | - | - | 包含“エクスポート”和“キャンセル”两个按钮，通常右对齐。 | - |
| 14 | "导出"按钮 | エクスポート | `button` (通常应用主要操作按钮样式，如 `btn-primary`) | `opLogSubmitBtn` | `internalState.isSubmitting: boolean` (组件内部 `useState` 管理的提交状态，用于禁用按钮和显示加载状态)。 | 点击时，调用组件内部的 `handleSubmit` 事件处理器 (参见3.3.4节)。 | 当 `internalState.startDate` 和 `internalState.endDate` 均已选择，且 `internalState.validationErrorMsgId` 为空 (即客户端校验通过)，并且 `internalState.isSubmitting` 为 `false` 时，此按钮才激活。提交中禁用并**应显示**加载指示（例如，按钮文本变为“処理中...”或在按钮旁显示加载图标）。 | 核心的提交操作按钮。 |
| 15 | "取消"按钮 | キャンセル | `button` (通常应用次要操作按钮样式，如 `btn-secondary`) | `opLogCancelBtn` | `internalState.isSubmitting: boolean` (用于在提交中禁用此按钮) | 点击时，调用从 `props` 传入的 `onClose` 回调函数 (即 `ServerListPage` 中的 `handleCloseTaskModal`)。 | 在 `internalState.isSubmitting` 为 `true` 时，此按钮也应被禁用，以防止用户在提交过程中取消。 | 用于关闭对话框并中止操作。 |

### 3.3 详细事件处理逻辑 (Detailed Event Handling)

本节描述 `OperationLogExportModal` 组件内部以及其与父组件 `ServerListPage` 之间的关键事件处理流程。

#### 3.3.1 从服务器列表选择“操作ログのエクスポート” (父组件 `ServerListPage` 的行为)
*   **触发位置**: [服务器列表主功能组件设计](../server-list.md) (`page.tsx`) 中的 `handleOpenTaskInitiation` 函数 (或类似逻辑)。
*   **处理流程**: 当用户选择“操作ログのエクスポート”任务时，`ServerListPage` 会：
    1.  从 `LOV` 表 (或其缓存，通过 `app/lib/data.ts` 中的服务) 获取 `'OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN'` 的值。**若获取失败，则使用系统定义的默认值（例如30天）。**
    2.  设置其内部状态 (例如 `activeTaskContext.paramsModal`)，将 `isOpen` 设为 `true`，`type` 设为 `'TASK_TYPE.OPLOG_EXPORT'`，并传入 `server.name`，以及包含 `maxExportDaysSpan` (获取到的值或默认值)、`onSubmit` (指向 `ServerListPage` 的 `handleTaskParamsSubmitted` 回调，该函数内部会触发打开通用确认模态框)、`onClose` (指向 `ServerListPage` 的 `handleCloseTaskModal` 回调) 等的 `props`。
*   **结果**: `OperationLogExportModal` 组件因 `props.isOpen` 变为 `true` 而显示。

#### 3.3.2 在参数对话框中选择开始/结束日期 (`OperationLogExportModal` 内部行为)

*   **子事件 3.3.2.1: `handleStartDateChange(date: Date | null)` (当开始日期选择器值变化时)**
    *   **触发位置**: `OperationLogExportModal` 组件内部，“开始日”日期选择器 (`opLogStartDateInput`) 的 `onChange` 事件。
    *   **处理流程**:
        1.  更新组件内部状态 `internalState.startDate` 为用户新选择的 `date` 值。
        2.  调用组件内部的参数校验函数 `validateDateRange()` (参见3.3.3节)，根据新的 `startDate` 和当前的 `endDate` 重新进行校验。
    *   **界面显示更新**: “开始日”日期选择器显示新选中的日期 (YYYY/MM/DD格式)。参数校验错误信息区 (`opLogValidationError`) 根据 `validateDateRange()` 的结果更新显示。

*   **子事件 3.3.2.2: `handleEndDateChange(date: Date | null)` (当结束日期选择器值变化时)**
    *   **触发位置**: `OperationLogExportModal` 组件内部，“结束日”日期选择器 (`opLogEndDateInput`) 的 `onChange` 事件。
    *   **处理流程**:
        1.  更新组件内部状态 `internalState.endDate` 为用户新选择的 `date` 值。
        2.  调用组件内部的参数校验函数 `validateDateRange()` (参见3.3.3节)，根据新的 `endDate` 和当前的 `startDate` 重新进行校验。
    *   **界面显示更新**: “结束日”日期选择器显示新选中的日期 (YYYY/MM/DD格式)。参数校验错误信息区 (`opLogValidationError`) 根据 `validateDateRange()` 的结果更新显示。

#### 3.3.3 在参数对话框中进行参数校验 (`validateDateRange()` - `OperationLogExportModal` 内部函数)
*   **触发时机**: 在 `handleStartDateChange` 或 `handleEndDateChange` 事件处理函数中被调用，以及在 `handleSubmit` (点击“导出”按钮) 函数执行前被主动调用。
*   **输入**: 组件内部状态 `internalState.startDate`, `internalState.endDate`，以及从 `props` 接收的 `props.maxExportDaysSpan`。
*   **核心处理逻辑**:
    1.  初始化一个局部变量 `currentValidationErrorMsgId = null`。
    2.  **必填项校验**:
        *   如果 `internalState.startDate` 为 `null`，则设置 `currentValidationErrorMsgId` 为 `'EMEC0016'` (其日文模板为 "{0}を指定してください。" 前端UI逻辑需将 `{0}` 替换为 “開始日”)。
        *   如果 `internalState.endDate` 为 `null`，则设置 `currentValidationErrorMsgId` 为 `'EMEC0016'` (其日文模板为 "{0}を指定してください。" 前端UI逻辑需将 `{0}` 替换为 “終了日”)。
    3.  **日期顺序校验**:
        *   如果 `internalState.startDate` 和 `internalState.endDate` 均已选择，且 `internalState.endDate` 的日期早于 `internalState.startDate` 的日期，则设置 `currentValidationErrorMsgId` 为 `'EMEC0024'`。
    4.  **日期范围跨度校验**:
        *   如果 `internalState.startDate` 和 `internalState.endDate` 均已选择且日期顺序正确，则计算天数跨度 `daySpan` (结束日期 - 开始日期 + 1)。
        *   如果 `daySpan` 大于 `props.maxExportDaysSpan`，则设置 `currentValidationErrorMsgId` 为 `'EMEC0020'` (其日文模板为 "{0} 日を超える期間が指定されました。{0} 日以内の期間を指定して再度実行してください。" 前端UI逻辑需使用 `props.maxExportDaysSpan` 的值替换占位符 `{0}`)。
    5.  更新组件内部状态 `internalState.validationErrorMsgId = currentValidationErrorMsgId`。
*   **效果**: 更新UI上的错误信息显示（通过消息ID从[`错误消息定义`](../../../definitions/error-messages.md)获取日文文本）。

#### 3.3.4 在参数对话框中点击“エクスポート”按钮 (`handleSubmit` - `OperationLogExportModal` 内部函数)
*   **触发位置**: “导出”按钮 (`opLogSubmitBtn`) 的 `onClick` 事件。
*   **处理流程**:
    1.  调用 `validateDateRange()` 进行最终校验。
    2.  若 `internalState.validationErrorMsgId` 不为空 (校验失败)，则不执行后续操作。
    3.  若校验通过：
        a.  设置 `internalState.isSubmitting = true`。
        b.  格式化 `internalState.startDate` 和 `internalState.endDate` 为 "YYYY-MM-DD" 字符串。
        c.  调用 `props.onSubmit({ exportStartDate: formattedStartDate, exportEndDate: formattedEndDate })`。此 `onSubmit` 是 `ServerListPage` 传递过来的函数 (即 `handleTaskParamsSubmitted`)，它将负责关闭此参数模态框并打开通用二次确认模态框。
        d.  等待 `props.onSubmit` Promise 完成后（即二次确认模态框被处理或关闭后），在 `finally` 块中设置 `internalState.isSubmitting = false`。
*   **界面显示更新**: 若校验失败，显示错误信息。若校验通过并开始提交，“导出”按钮应被禁用并显示加载指示。参数模态框通常在回调给父组件后由父组件控制关闭。

#### 3.3.5 (通用二次确认对话框的处理逻辑 - 在 `ServerListPage` 中)
*   当 `OperationLogExportModal` 调用其 `props.onSubmit` 并传递日期参数后，`ServerListPage` 中的对应处理函数 (例如 `handleTaskParamsSubmitted`) 会被触发。
*   此函数会首先调用 `handleCloseTaskModal('params')` 关闭 `OperationLogExportModal`。
*   然后，打开一个通用的消息模态框 (`app/ui/message-modal.tsx`)。
*   **传递给通用模态框的Props**:
    *   `title`: "操作ログのエクスポート確認" (或直接使用任务类型名："操作ログのエクスポート")
    *   `message`: 使用消息ID `EMEC0026` (“{0}の操作ログを{1}から{2}の期間でエクスポートします。\nよろしいですか？”)，并用实际的服务器名、开始日期、结束日期替换占位符。
    *   `onConfirm`: 指向 `ServerListPage` 中一个真正调用 `createTaskAction` 的函数 (例如 `handleActualTaskSubmit`)，并将 `taskType` (`'TASK_TYPE.OPLOG_EXPORT'`)、`serverId` 以及从 `OperationLogExportModal` 传递过来的日期参数构造到 `FormData` 中。
    *   `onCancel`: 指向 `ServerListPage` 中关闭此通用确认模态框的函数 (例如 `handleCloseTaskModal('confirm', server, taskType, taskSpecificParams)`)。
*   只有当用户在此通用确认模态框中点击“确认”后，才会实际调用 `createTaskAction`。

#### 3.3.6 在参数对话框或通用确认对话框中点击“キャンセル”或关闭
*   **参数对话框 (`OperationLogExportModal`)**: 点击“キャンセル”或关闭图标时，调用其 `props.onClose`，由 `ServerListPage` 的 `handleCloseTaskModal('params')` 关闭。
*   **通用确认对话框**: 点击“キャンセル”或关闭时，调用其 `props.onCancel`。`ServerListPage` 的 `handleCloseTaskModal('confirm', ...)` 逻辑应负责关闭通用确认模态框，并重新打开 `OperationLogExportModal` (参数模态框)，**并恢复之前已输入的日期参数**。

### 3.4 数据结构与API/Server Action交互 (Data Structures and API/Server Action Interaction)

#### 3.4.1 前端状态
*   **`OperationLogExportModal` 内部状态 (`internalState`)**:
    *   `startDate: Date | null`
    *   `endDate: Date | null`
    *   `validationErrorMsgId: string | null` (错误消息ID，日文文本从[`错误消息定义`](../../../definitions/error-messages.md)获取并替换占位符)
    *   `isSubmitting: boolean`
*   **`ServerListPage` (`page.tsx`) 组件状态**: (已在[服务器列表主功能组件设计](../server-list.md#341-前端核心状态管理)中定义，特别是 `activeTaskContext`，用于管理所有任务模态框的显隐及参数传递，以及通用二次确认模态框的状态和参数)。

#### 3.4.2 Server Action: `createTaskAction` (针对 `TASK_TYPE.OPLOG_EXPORT`)

`ServerListPage` (在通用二次确认模态框确认后) 调用 [`createTaskAction` Server Action](../../actions/create-task-action.md) 时传递的 `FormData` 包含：
*   `taskType: string`: `'TASK_TYPE.OPLOG_EXPORT'`。
*   `serverId: string`: 目标服务器的ID。
*   `exportStartDate: string`: 操作日志导出的开始日期，格式为 "YYYY-MM-DD" (从 `OperationLogExportModal` 回调的参数中获取)。
*   `exportEndDate: string`: 操作日志导出的结束日期，格式为 "YYYY-MM-DD" (从 `OperationLogExportModal` 回调的参数中获取)。

返回值结构为 [`CreateTaskActionResult`](../../actions/create-task-action.md#13-返回值-createtaskactionresult)。

#### 3.4.3 主要交互序列图 (Mermaid Sequence Diagram for Export Operation Log)

```mermaid
sequenceDiagram
    actor User as 用户
    participant FrontendApp as 前端应用 (门户)
    participant NextJsAppServer as Next.js 应用服务器 (后端)
    participant Database as 数据库 (Task表等)
    participant MessageQueue as 消息队列 (TaskInputQueue)

    User->>FrontendApp: 1. 在服务器列表选择“操作日志导出”
    FrontendApp->>User: 2. (前端内部逻辑) 显示操作日志导出参数模态框
    User->>FrontendApp: 3. 在参数模态框中选择开始/结束日期并提交
    FrontendApp->>FrontendApp: 4. (前端内部逻辑) 客户端参数校验
    alt 日期参数无效 (客户端校验)
        FrontendApp->>User: 5a. 在参数模态框内显示错误提示
    else 日期参数有效 (客户端校验)
        FrontendApp->>User: 5b. (前端内部逻辑) 显示通用二次确认信息<br/>(例如：“确定要导出[服务器]从[日期A]到[日期B]的操作日志吗？”)
        User->>FrontendApp: 6. 点击“OK/はい” (最终确认)
        FrontendApp->>NextJsAppServer: 7. 发起后台任务创建请求 (HTTP POST)<br/>(调用 createTaskAction Server Action,<br/>携带taskType, serverId, exportStartDate, exportEndDate)
        activate NextJsAppServer
        NextJsAppServer->>NextJsAppServer: 8. (在createTaskAction内) 执行通用前置校验<br/>(用户会话,权限(含操作日志导出权限再校验),服务器配置,并发检查, <br/> 若并发记录不存在则创建并初始化为IDLE)<br/>及特定任务逻辑(日期参数校验等)
        alt 服务端校验失败 或 容器繁忙 或 权限不足
            NextJsAppServer-->>FrontendApp: 9a. 返回错误响应 (含messageId)
        else 服务端校验通过且容器空闲且有权限
            NextJsAppServer->>Database: 9b. (在createTaskAction内) 执行核心数据库事务<br/>(含任务保留策略, 创建Task记录)
            activate Database
            Database-->>NextJsAppServer: 10b. 数据库操作结果
            deactivate Database
            alt 数据库事务失败 (或并发锁获取失败)
                NextJsAppServer-->>FrontendApp: 11a. 返回DB错误响应 (含messageId)
            else 数据库事务成功
                NextJsAppServer->>MessageQueue: 11b. (在createTaskAction内) 发送任务消息 (仅含taskId)
                activate MessageQueue
                MessageQueue-->>NextJsAppServer: 12b. 消息发送结果
                deactivate MessageQueue
                alt 消息发送失败
                    NextJsAppServer-->>FrontendApp: 13a. 返回队列错误响应 (含messageId)
                else 消息发送成功
                    NextJsAppServer->>NextJsAppServer: 13b. (在createTaskAction内) 调用 revalidatePath
                    NextJsAppServer-->>FrontendApp: 14b. 返回成功响应 (含messageId)
                end
            end
        end
        deactivate NextJsAppServer
        
        FrontendApp->>User: 15. 根据Server Action响应结果<br/>显示成功或失败提示 (基于messageId)<br/>(关闭所有相关模态框)
    end
```

### 3.5 数据库设计与访问详情 (Database Design and Access Details - 主要通过Server Action间接访问)
本组件 (`OperationLogExportModal`) 不直接与数据库交互。其提交的参数最终由 [`createTaskAction` Server Action](../../actions/create-task-action.md) 处理。

*   **`Task` 表**: 当任务类型为 `'TASK_TYPE.OPLOG_EXPORT'` 时，`parametersJson` 字段将存储包含 `exportStartDate` (YYYY-MM-DD) 和 `exportEndDate` (YYYY-MM-DD) 的JSON字符串。执行上下文信息（如 `dockerContainerName`）也由 `createTaskAction` 处理并存入 `Task` 表的专用字段。其表结构定义参见[`Task数据模型定义`](../../../data-models/task.md)。
*   **`OperationLog` 表**: 当操作日志导出任务成功完成后，后端的 `RunbookProcessorFunc` 会负责将导出的日志文件信息记录到此表中。其表结构定义参见[`OperationLog数据模型定义`](../../../data-models/operation-log.md)。
*   其他相关表如 `ContainerConcurrencyStatus`, `Server`, `License`, `Lov` 的交互逻辑详见 [`createTaskAction` Server Action文档](../../actions/create-task-action.md)。

### 3.6 关键后端逻辑/算法 (Key Backend Logic/Algorithms - 主要在Server Action `createTaskAction` 中)

操作日志导出功能的特定后端逻辑主要体现在 [`createTaskAction` Server Action](../../actions/create-task-action.md) 内部处理 `taskType` 为 `'TASK_TYPE.OPLOG_EXPORT'` 的分支中。

#### 3.6.1 [`createTaskAction`](../../actions/create-task-action.md) 中处理 `TASK_TYPE.OPLOG_EXPORT` 的特定逻辑分支

当 [`createTaskAction` Server Action](../../actions/create-task-action.md) 接收到 `taskType` 为 `'TASK_TYPE.OPLOG_EXPORT'` 的请求时，在执行通用的任务创建前置步骤（包括在步骤5.0生成`taskId`）之后，将执行以下特定于操作日志导出的逻辑（详细定义见 `createTaskAction.md` 的3节）：

1.  **权限二次校验**: 再次校验当前登录用户的 `License.basicPlan` 是否在 `LOV` 表中 `parentCode='OPERATION_LOG_CONFIG.ALLOWED_PLANS'` 定义的允许列表中。若校验失败，返回相应的权限错误 `messageId`。
2.  **提取并校验特定参数**: 从 `FormData` 中提取 `exportStartDate` 和 `exportEndDate`。服务器端校验其存在性、"YYYY-MM-DD"格式、日期顺序 (结束日期 >= 开始日期，否则返回 `EMEC0024`)、以及日期跨度是否超过 `LOV('OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN').value` (否则返回 `EMEC0020`，占位符由服务器端填充最大天数值)。
3.  **构造存入 `Task.parametersJson` 的特定参数部分 (`taskSpecificParamsForDb`)**: 包含 `exportStartDate` 和 `exportEndDate`。
    *   示例 `parametersJson` 内容: `{"exportStartDate": "2023-01-01", "exportEndDate": "2023-01-31"}`
4.  **构造发送到Service Bus消息体中（供 `TaskExecuteFunc` 使用）的参数**: 当前设计为仅在消息中传递 `taskId`。`TaskExecuteFunc` 将根据 `taskId` 从数据库的 `Task` 记录中（特别是其 `parametersJson` 字段）获取 `exportStartDate` 和 `exportEndDate` 等信息。
5.  后续的通用处理流程（如并发检查、任务记录保留策略、数据库事务、消息发送）遵循 `createTaskAction.md` 的定义。

### 3.7 错误处理详情 (Detailed Error Handling)

| 错误场景描述 (中文) | 触发位置 (前端/ServerAction) | 返回的 `CreateTaskActionResult` / 客户端处理方式 | 用户提示信息 (日文界面文本) (基于消息ID，其日文文本定义在[`错误消息定义`](../../../definitions/error-messages.md) 中，并替换占位符若有) | 系统内部处理及日志记录建议 (中文) |
|-------------|------------------------|----------------------------------------|------------------------------------------------------------------------------------------------|--------------------|
| **客户端校验**: 开始日期未选择 | 前端 (`OperationLogExportModal`) | `internalState.validationErrorMsgId` 更新为 `'EMEC0016'` 并在UI显示。提交被阻止。 | 消息ID: `EMEC0016` (占位符 `{0}` 替换为 "開始日") | `OperationLogExportModal` 组件：阻止提交，在UI上显示错误。 |
| **客户端校验**: 结束日期未选择 | 前端 (`OperationLogExportModal`) | `internalState.validationErrorMsgId` 更新为 `'EMEC0016'` 并在UI显示。提交被阻止。 | 消息ID: `EMEC0016` (占位符 `{0}` 替换为 "終了日") | `OperationLogExportModal` 组件：阻止提交，在UI上显示错误。 |
| **客户端校验**: 结束日期早于开始日期 | 前端 (`OperationLogExportModal`) | `internalState.validationErrorMsgId` 更新为 `'EMEC0024'` 并在UI显示。提交被阻止。 | 消息ID: `EMEC0024` | `OperationLogExportModal` 组件：阻止提交，在UI上显示错误。 |
| **客户端校验**: 日期范围跨度超过最大允许天数 | 前端 (`OperationLogExportModal`) | `internalState.validationErrorMsgId` 更新为 `'EMEC0020'` 并在UI显示。提交被阻止。 | 消息ID: `EMEC0020` (占位符`{0}`被替换为从`props.maxExportDaysSpan`获取的最大天数值) | `OperationLogExportModal` 组件：使用 `props.maxExportDaysSpan` 进行校验，若失败则更新内部错误状态。 |
| **服务器端校验**: 权限不足 (用户 `License.basicPlan` 不在允许列表) | `createTaskAction` Server Action (特定逻辑分支) | `{ success: false, messageId: 'EMEC0005' }` (或更具体的权限错误ID) | 消息ID: `EMEC0005` (或特定权限错误ID) | `createTaskAction`: 记录权限校验失败日志。返回错误给客户端。 |
| **服务器端校验**: 日期格式无效 (例如，非 "YYYY-MM-DD") | `createTaskAction` Server Action (特定逻辑分支) | `{ success: false, messageId: 'EMEC0016' }` (占位符由服务器端填充为相关日期字段名) | 消息ID: `EMEC0016` (服务器端返回此ID时，前端可能需要根据上下文明确是哪个日期字段格式错误，或使用更通用的“日期格式无效”提示) | `createTaskAction`: 解析日期字符串失败。记录错误日志，包含无效的日期字符串。返回错误给客户端。 |
| **服务器端校验**: 结束日期早于开始日期 | `createTaskAction` Server Action (特定逻辑分支) | `{ success: false, messageId: 'EMEC0024' }` | 消息ID: `EMEC0024` | `createTaskAction`: 服务器端日期顺序校验失败。记录错误日志。返回错误给客户端。 |
| **服务器端校验**: 日期范围跨度超过最大允许天数 | `createTaskAction` Server Action (特定逻辑分支) | `{ success: false, messageId: 'EMEC0020' }` (占位符由服务器端填充最大天数值) | 消息ID: `EMEC0020` (占位符`{0}`为服务器端获取的LOV最大天数值) | `createTaskAction`: 服务器端日期范围跨度校验失败。记录错误日志，包含请求的日期范围和配置的最大天数。返回错误给客户端。 |
| (其他通用错误场景参考 [`createTaskAction` Server Action文档](../../actions/create-task-action.md) 和 [服务器列表主功能组件设计](../server-list.md)) | `createTaskAction` Server Action / `ServerListPage` | (参考对应文档) | (参考对应文档) | (参考对应文档) |

### 3.8 配置项 (Configuration)
*   **`LOV` 表** (其权威定义参见 [`LOV值列表定义`](../../../definitions/lov-definitions.md)):
    *   `TASK_TYPE.OPLOG_EXPORT` (`code`: "TASK_TYPE.OPLOG_EXPORT", `name` (日文): "操作ログのエクスポート")
    *   `OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN` (`code`: "OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN", `value`: "30" (示例值))。**`OperationLogExportModal` 组件在获取此值失败时，应使用系统预设的默认值 (例如30天)。**
    *   `OPERATION_LOG_CONFIG.ALLOWED_PLANS` (及其子条目): 定义了哪些许可证基本契约计划代码 (`License.basicPlan`) 被允许执行“操作日志导出”功能。
    *   `TASK_CONFIG.MAX_RETENTION_COUNT` (`code`: "TASK_CONFIG.MAX_RETENTION_COUNT", `value`: "10" (示例值))
    *   相关消息ID (例如 `EMEC0026`，其日文文本在[`错误消息定义`](../../../definitions/error-messages.md)中定义，并由 `ServerListPage` 填充占位符)。
*   **环境变量** (其权威定义参见 [`环境变量指南`](../../../guides/environment-variables.md)):
    *   `AZURE_STORAGE_CONTAINER_OPLOGS`: 指定存储最终导出的操作日志ZIP文件的Azure Blob Storage容器名称。此配置主要由后端的 `RunbookProcessorFunc` (或类似服务) 和执行文件删除的逻辑（在 `createTaskAction` 中的保留策略部分）使用。
    *   间接依赖于 [`createTaskAction` Server Action](../../actions/create-task-action.md) 所需的其他环境变量。
*   常量定义 (`apps/jcs-endpoint-nextjs/app/lib/definitions.ts`) (用于内部逻辑或类型，用户可见消息通过消息ID从[`错误消息定义`](../../../definitions/error-messages.md)获取)。

### 3.9 注意事项与其他 (Notes/Miscellaneous)
*   **Runbook脚本 (`Export-OperationLog.ps1` - 假设名称)**:
    *   需能正确接收并解析从Service Bus消息中传递过来的参数，特别是 `targetContainerName`, `startDate` (格式可能为YYYYMMDD), `endDate` (格式可能为YYYYMMDD)。
    *   负责调用目标Docker容器内JP1/ITDM2的日志导出命令，并打包结果为ZIP文件。
    *   将生成的ZIP文件（可能多个）输出到Azure Files工作区的 `exports/` 目录，文件名建议规范化（例如，`exportoplog_{連番}.zip`）。
    *   执行完毕后上报任务状态及结果信息。
*   **后端状态处理服务 (`RunbookProcessorFunc`)**:
    *   成功时，从工作区读取所有导出的ZIP文件，上传到由环境变量 `AZURE_STORAGE_CONTAINER_OPLOGS` 指定的Blob容器，路径格式为 `{licenseId}/{taskId}/{Task.taskName}_{連番}.zip`。
    *   为每个上传成功的ZIP文件，在 `OperationLog` 数据库表中创建一条记录。
    *   清理工作区。
*   **任务记录与文件生命周期**: 本任务产生的 `Task` 记录、`OperationLog` 记录以及存储在Azure Blob Storage中的日志文件，均受系统定义的任务记录保留策略（基于 `LOV:TASK_CONFIG.MAX_RETENTION_COUNT`）的约束。当旧的 `Task` 记录被删除时，其关联的 `OperationLog` 记录和对应的所有Blob文件也将被一并清除（此删除逻辑由[`createTaskAction` Server Action](../../actions/create-task-action.md)中的保留策略部分负责）。
