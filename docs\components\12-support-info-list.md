# 组件：支持信息列表 (Support Information List)

## 1. 概要 (Overview)

### 1.1 目的 (Purpose)
本组件为“JCS 端点资产与任务管理系统”的已登录用户（特指顾客系统管理员）提供一个界面，用于查看和确认由服务方（如日立）发布的与本服务相关的支持信息。这些信息可能包括已知的障害回避或预防措施、重要的注意唤起事项、产品更新通告等，旨在帮助用户更好地使用服务并应对潜在问题。

### 1.2 用户故事/需求 (User Stories/Requirements)
- 作为一名顾客系统管理员，我希望能够查看一个包含所有与我当前契约相关的“支持信息”的列表。
- 作为一名顾客系统管理员，我希望能看到每条支持信息的关键摘要，如最后更新日期、标题、涉及的产品以及其重要程度。
- 作为一名顾客系统管理员，我希望能方便地从列表中查阅支持信息的详细内容（`fs.md` 暗示标题本身可能包含主要信息，或未来可扩展为点击查看详情）。
- 作为一名顾客系统管理员，我希望能根据标题、产品或重要程度等关键字快速筛选和查找我需要的支持信息。

### 1.3 高层依赖与交互 (High-Level Dependencies & Interactions)
- **用户认证与授权**: 用户的身份（特别是关联的契约计划ID）决定了其能在列表中看到哪些支持信息。
- **主界面 (Main Screen)**: 本组件的入口点是[主界面](./02-main-screen.md)侧边栏的“サポート情報一覧 (支持信息列表)”菜单项。
- **数据存储**:
    - 支持信息的元数据（最后更新日、标题、产品、重要度、公开日、关联的文件名等）存储在Azure SQL Database的 `SupportFile` 表中 (根据`fs.md`的DB设计，表名为 `SupportFile`，用于存储支持信息，而 `PlanSupport` 表关联契约计划ID和通番，控制可见性)。
    - 重要程度的显示文本（如AAA, AA, A, B, C, -）来源于 `LOV` 表 (parentCode = `SUPPORT_IMPORTANCE`)。
- **文件存储与下载 (若支持信息包含附件)**:
    - 如果支持信息条目关联了具体的附件文件（`fs.md` 表 `SupportFile` 有 `ファイル名前` 列），这些文件实际存储在Azure Blob Storage的特定容器（如 `support-files`）中。
    - 用户下载附件时，前端通过后端API获取一个安全的、有时限的共享访问签名 (SAS) 链接。
- **后端API (Next.js API Routes)**: 前端通过调用后端API来获取用户可见的支持信息列表和（如果适用）附件下载链接。

## 2. 功能规格 (Functional Specifications)

### 2.1 主要流程 (High-Level Flow)
1.  用户成功登录系统后，通过侧边导航栏访问“サポート情報一覧 (支持信息列表)”菜单项。
2.  系统加载并向用户展示其有权访问的支持信息列表，每条记录包含最后更新日、标题、产品、重要度、公开日。标题可能作为查看详情或下载关联附件的入口。
3.  用户可以使用界面提供的筛选框输入关键字，点击搜索或按回车后，列表将根据匹配结果刷新。
4.  用户可以点击列表表头对支持信息进行排序。
5.  用户如果看到支持信息条目关联了可下载的附件（例如，通过一个下载图标或链接），可以点击来下载对应的文件。
    ```mermaid
    sequenceDiagram
        participant User as 👤 用户 (Browser)
        participant SupportInfoListScreen as ℹ️ 支持信息列表界面
        participant NextJsApiRoutes as 🌐 Next.js API Routes
        participant SupportInfoDB as 💾 SupportFile/PlanSupport表 (SQL DB)
        participant AzureBlobStorage as ☁️ Azure Blob Storage

        User->>SupportInfoListScreen: 打开支持信息列表
        SupportInfoListScreen->>NextJsApiRoutes: 请求支持信息列表数据 (GET /api/support-info)
        NextJsApiRoutes->>SupportInfoDB: 查询用户可见的支持信息元数据 (基于用户契约计划ID)
        SupportInfoDB-->>NextJsApiRoutes: 返回支持信息元数据列表
        NextJsApiRoutes-->>SupportInfoListScreen: 显示支持信息列表

        alt 用户下载支持信息的附件 (如果存在)
            User->>SupportInfoListScreen: 点击支持信息A的附件下载链接
            SupportInfoListScreen->>NextJsApiRoutes: 请求附件的下载URL (GET /api/support-info/A_file_id/download-url)
            NextJsApiRoutes->>AzureBlobStorage: 为附件生成SAS Token
            AzureBlobStorage-->>NextJsApiRoutes: 返回带SAS Token的URL
            NextJsApiRoutes-->>SupportInfoListScreen: 返回安全的下载URL
            SupportInfoListScreen->>User: (浏览器)开始下载附件
        end
    ```
6.  用户可以使用分页控件浏览更多的支持信息记录。
7.  用户可以点击界面右上角的“更新”图标按钮，手动刷新整个支持信息列表。

### 2.2 业务规则 (Business Rules)
-   **信息来源与可见性**:
    *   支持信息的元数据从门户数据库的 `SupportFile` 表中获取。
    *   用户可见的支持信息范围由其契约计划ID通过 `PlanSupport` 表（或类似机制）关联确定。
-   **只读显示**: 列表中的所有支持信息均为只读展示。
-   **附件文件存储路径 (如果适用)**: 支持信息附件存储在Azure Blob Storage的 `support-files` 容器下，可能按通番（serial number）分子目录组织（例如：`support-files/{通番}/{文件名}`）。
-   **附件下载机制 (如果适用)**: 与操作日志下载类似，用户下载附件时，后端API为目标Blob生成一个具有读取权限且有时间限制（默认为2小时，可由`LOV`表 `AZURE_STORAGE.SAS_TTL_SECONDS` 配置）的共享访问签名 (SAS) Token。
-   **日期显示**: 所有日期（如最后更新日、公开日）的显示应遵循用户浏览器的时区设置（格式 `YYYY/MM/DD`）。
-   **重要程度显示**: “重要度”字段根据 `LOV` 表中 `SUPPORT_IMPORTANCE` 的定义显示对应文本：
    *   AAA：业务系统的运用が停止し、発生頻度が高い
    *   AA：业务系统的运用が停止する可能性がある
    *   A：业务系统的运用が停止する可能性は低い
    *   B：业务系统的运用に与える影響が少ない
    *   C：业务系统的运用に与える影響は殆ど無い
    *   -：重要度なし(予防保守情報や使用時の注意事項など)
-   **默认排序** (依据 `fs.md` 对サポート情報一覧的ソート规则):
    1.  主要排序键：“最終更新日 (最后更新日)”，降序（最新的在前）。
    2.  次要排序键：“製品 (产品)”，字典序升序（A→Z, 0→9）。

### 2.3 用户界面概述 (User Interface Overview)

-   **界面草图/核心区域**: (参考 `fs.md` 图4.12.6 (1) 作为高层概念)
    1.  **筛选区**: 位于列表上方，提供文本输入框进行关键字筛选，以及搜索和清除按钮。
    2.  **支持信息列表表格**:
        *   列：最終更新日 (最后更新日)、タイトル (标题，可能兼作详情/附件下载链接)、製品 (产品)、重要度 (重要程度)、公開日 (公开日)。
        *   每行代表一条支持信息。
    3.  **分页控件**: 位于列表下方。
    4.  **更新按钮**: 通常位于列表右上角。
-   **主要交互点**:
    *   用户在筛选框中输入文本，进行列表筛选。
    *   用户点击可排序的列标题，进行升序/降序排序。
    *   用户点击“タイトル”列中的标题链接（如果设计为可点击查看详情或下载附件）。
    *   用户使用分页控件浏览更多记录。
    *   用户点击“更新”按钮，重新加载列表。
-   **画面項目 (Screen Items - High Level)**:
    *   输入：筛选关键字。
    *   显示：支持信息的最后更新日期、标题、相关产品、重要程度文本、公开日期。
    *   用户可操作：筛选、排序、分页、查看详情/下载附件（如果适用）。
-   **Tab键顺序** (依据 `fs.md` 描述):
    *   Tab键焦点在列表区域时，应按“タイトル”列从上到下的顺序遍历（如果标题是可交互元素）。

### 2.4 前提条件 (Preconditions)
-   用户必须已通过[登录功能](./01-login.md)完成身份验证，并拥有有效的活动会话。
-   系统管理员已在 `SupportFile` 数据库表中正确录入了支持信息的元数据，并通过 `PlanSupport` 表（或类似机制）配置了用户契约计划可访问的支持信息范围。
-   如果支持信息包含附件，对应的附件文件已上传到Azure Blob Storage的 `support-files` 容器中，并与数据库元数据中的路径一致。
-   后端用于获取支持信息列表和（如果适用）生成附件下载链接的API端点必须可用。

### 2.5 制约事项 (Constraints/Limitations)
-   **浏览器兼容性**: 仅正式支持 Microsoft Edge 和 Google Chrome 浏览器。
-   **语言支持**: 界面显示语言仅支持日语。
-   **列表性能**: 如果支持信息条目非常多，列表的加载、筛选和分页性能可能会受到影响。
-   **详情查看**: `fs.md` 未明确“标题”点击后的行为是展开详情还是直接下载附件（如果存在）。当前假设标题本身包含足够信息，或点击后会下载关联文件。

### 2.6 注意事项 (Notes/Considerations)
-   “重要程度”的显示应使用 `LOV` 表中定义的标准文本，确保信息一致性。
-   “产品”列可能涉及多个产品，显示方式需要清晰。

### 2.7 错误处理概述 (Error Handling Overview)
-   **列表加载失败**: 如果后端API调用失败导致无法获取支持信息列表，界面应向用户显示通用的错误提示信息（例如，“支持信息列表加载失败，请稍后重试。”），并记录详细技术错误。
-   **附件下载失败 (如果适用)**:
    *   如果请求下载链接时后端API出错，应提示用户“无法获取下载链接，请重试。”
    *   如果生成的下载链接无效，用户点击下载后浏览器层面会报错或下载失败。
-   **无权访问信息**: 如果用户因权限配置问题理论上不应看到任何支持信息，列表应显示为空或特定提示（例如，“当前没有您可访问的支持信息。”）。

### 2.8 相关功能参考 (Related Functional References)
*   **主要入口**: [主界面 (Main Screen)](./02-main-screen.md) - 侧边栏的“サポート情報一覧”菜单是访问本功能的途径。
*   **认证关联**: [登录 (Login)](./01-login.md) - 用户的登录身份（特别是契约计划ID）决定了其能看到的支持信息范围。
*   **系统整体架构**: `../../architecture/system-architecture.md` - 描述了支持信息数据如何从存储到呈现给用户的流程。
*   **核心数据模型**:
    *   `../../data-models/support-file-table.md` (假设文件名) - 定义了支持信息元数据的主要来源表结构。
    *   `../../data-models/plan-support-table.md` (假设文件名) - 定义了用户契约计划与可访问支持信息之间的关联。
    *   `../../data-models/lov-table.md` - 包含“重要程度” (`SUPPORT_IMPORTANCE`) 的显示文本定义和可能的SAS Token有效期配置。
*   **源功能规格**: `fs.md` (通常位于 `docs-delivery/機能仕様書/` 或项目指定的源文档位置) - 本组件功能规格的原始日文描述，特别是其“4.12 サポート情報一覧”章节。
