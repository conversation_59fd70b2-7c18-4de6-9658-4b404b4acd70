/**
 * @fileoverview Next.jsアプリケーションのリクエスト処理前に実行されるミドルウェア
 * @description 認証状態の確認、ルーティング制御、およびセキュリティチェックを実行する
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";


/**
 * Next.jsミドルウェア関数
 * リクエスト処理前にセキュリティチェックと認証状態確認を実行する
 * @param req - Next.jsリクエストオブジェクト
 * @returns レスポンスまたはリダイレクト処理
 */
export default async function middleware(req: NextRequest) {
  const path = req.nextUrl.pathname;
  const search = req.nextUrl.search;

  // NULL文字チェック（セキュリティ対策）
  // URLパスやクエリパラメータにNULL文字が含まれている場合は不正なリクエストとして拒否
  if (path.includes('\0') || path.includes('%00') ||
      search.includes('\0') || search.includes('%00')) {
    return NextResponse.json(
      { error: "不正なリクエスト: NULL文字が検出されました" },
      { status: 400 }
    );
  }

  const session = await getIronSession<SessionData>(cookies(), sessionOptions);

  if (!session.user && path === "/") {
    return NextResponse.redirect(new URL("/login", req.url));
  } else if (session.user && (path === "/login" || path === "/register")) {
    return NextResponse.redirect(new URL("/dashboard", req.url));
  } else if (session.user && (path === "/" || path === "/dashboard")) {
    return NextResponse.redirect(new URL("/dashboard/servers", req.url));
  } else if (!session.user && path.startsWith("/dashboard")) {
    // Server Action リクエスト（POST メソッド）は 401 エラーを返し、リダイレクトは行わない
    // これにより、クライアント側の useServerAction Hook がエラーをキャッチしてリダイレクトを処理できる
    if (req.method === "POST") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    // ページアクセスリクエスト（GET メソッド）はリダイレクトを実行
    return NextResponse.redirect(new URL("/login", req.url));
  } else if (!session.user && (path === "/callback")) {
    return NextResponse.next();
  } else if (session.user && path === "/callback") {
    return NextResponse.redirect(new URL("/dashboard/servers", req.url));
  } else if (
    !session.user &&
    path.startsWith("/api/") &&
    !path.startsWith("/api/callback") &&
    !path.startsWith("/api/logout") &&
    !path.startsWith("/api/login") &&
    path !== "/api/notifications/system"
  ) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  return NextResponse.next();
}
