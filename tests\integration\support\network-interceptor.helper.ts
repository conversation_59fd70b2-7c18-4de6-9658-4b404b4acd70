/**
 * @file network-interceptor.helper.ts
 * @description 网络请求拦截辅助函数，用于在测试中模拟和控制网络请求
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { Page } from '@playwright/test';

/**
 * 拦截并模拟 RefreshToken API 请求
 * 避免在测试中调用真实的 Keycloak 服务
 * @param page Playwright 页面对象
 */
export async function interceptRefreshTokenRequests(page: Page): Promise<void> {
  // 拦截 /api/refreshToken 请求
  await page.route('/api/refreshToken', async (route) => {
    // 模拟成功的响应 - 必须返回 { status: 200 } 格式
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        status: 200
      })
    });
  });

  // 拦截可能的 Keycloak 相关请求
  await page.route('**/auth/**', async (route) => {
    // 模拟成功的响应
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        success: true,
        message: 'Keycloak request mocked'
      })
    });
  });

  // 拦截登出请求（避免意外登出）
  await page.route('/api/logout', async (route) => {
    // 模拟成功的响应，但不执行实际登出
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        success: true,
        message: 'Logout request intercepted (not executed)'
      })
    });
  });
}

/**
 * 拦截并模拟所有认证相关的 API 请求
 * @param page Playwright 页面对象
 */
export async function interceptAuthRequests(page: Page): Promise<void> {
  await interceptRefreshTokenRequests(page);
  
  // 可以根据需要添加更多认证相关的拦截
}

/**
 * 清除所有网络拦截
 * @param page Playwright 页面对象
 */
export async function clearNetworkInterceptors(page: Page): Promise<void> {
  await page.unroute('/api/refreshToken');
  await page.unroute('**/auth/**');
  await page.unroute('/api/logout');
}



/**
 * 模拟 RefreshToken 组件的测试场景
 * 包括成功、失败、超时等各种情况
 */
export class RefreshTokenTestScenarios {
  private page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  /**
   * 模拟 RefreshToken 成功场景
   */
  async mockSuccess(): Promise<void> {
    await this.page.route('/api/refreshToken', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 200
        })
      });
    });
  }

  /**
   * 模拟 RefreshToken 失败场景
   */
  async mockFailure(): Promise<void> {
    await this.page.route('/api/refreshToken', async (route) => {
      await route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 400
        })
      });
    });
  }

  /**
   * 模拟 RefreshToken 超时场景
   */
  async mockTimeout(): Promise<void> {
    await this.page.route('/api/refreshToken', async (route) => {
      // 延迟响应模拟超时
      await new Promise(resolve => setTimeout(resolve, 5000));
      await route.fulfill({
        status: 408,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 408
        })
      });
    });
  }

  /**
   * 清除所有模拟
   */
  async clearMocks(): Promise<void> {
    await this.page.unroute('/api/refreshToken');
  }
}
