#!/usr/bin/env node

const ExcelJS = require('exceljs');
const path = require('path');

async function analyzeCustomerTemplate() {
    try {
        console.log('📋 分析客户提供的UT動作確認チェックリスト.xlsx模板...\n');
        
        const workbook = new ExcelJS.Workbook();
        const templatePath = path.join(__dirname, '..', 'docs-delivery', 'unit-test-report', 'UT動作確認チェックリスト.xlsx');
        
        await workbook.xlsx.readFile(templatePath);
        
        console.log('📊 工作表信息:');
        console.log('='.repeat(50));
        
        workbook.eachSheet((worksheet, sheetId) => {
            console.log(`\n工作表 ${sheetId}: "${worksheet.name}"`);
            console.log(`- 行数: ${worksheet.rowCount}`);
            console.log(`- 列数: ${worksheet.columnCount}`);
            console.log(`- 状态: ${worksheet.state || 'visible'}`);
            
            // 分析前10行的内容结构
            console.log('\n📝 内容结构分析:');
            for (let rowNum = 1; rowNum <= Math.min(15, worksheet.rowCount); rowNum++) {
                const row = worksheet.getRow(rowNum);
                const values = [];
                
                for (let colNum = 1; colNum <= Math.min(10, worksheet.columnCount); colNum++) {
                    const cell = row.getCell(colNum);
                    if (cell.value !== null && cell.value !== undefined && cell.value !== '') {
                        values.push(`${getColumnLetter(colNum)}: "${cell.value}"`);
                    }
                }
                
                if (values.length > 0) {
                    console.log(`  行${rowNum}: ${values.join(', ')}`);
                }
            }
            
            // 分析列标题
            console.log('\n📋 列标题分析:');
            const headerRow = worksheet.getRow(1);
            for (let colNum = 1; colNum <= worksheet.columnCount; colNum++) {
                const cell = headerRow.getCell(colNum);
                if (cell.value) {
                    console.log(`  ${getColumnLetter(colNum)}列: "${cell.value}"`);
                }
            }
            
            // 分析合并单元格
            if (worksheet.model.merges && worksheet.model.merges.length > 0) {
                console.log('\n🔗 合并单元格:');
                worksheet.model.merges.forEach((merge, index) => {
                    console.log(`  ${index + 1}: ${merge}`);
                });
            }
            
            console.log('\n' + '-'.repeat(50));
        });
        
        // 生成模板结构总结
        console.log('\n📋 模板结构总结:');
        console.log('='.repeat(50));
        
        const templateStructure = {
            worksheets: [],
            totalSheets: workbook.worksheets.length
        };
        
        workbook.eachSheet((worksheet, sheetId) => {
            const sheetInfo = {
                id: sheetId,
                name: worksheet.name,
                rowCount: worksheet.rowCount,
                columnCount: worksheet.columnCount,
                headers: [],
                sampleData: []
            };
            
            // 提取标题行
            const headerRow = worksheet.getRow(1);
            for (let colNum = 1; colNum <= worksheet.columnCount; colNum++) {
                const cell = headerRow.getCell(colNum);
                if (cell.value) {
                    sheetInfo.headers.push({
                        column: getColumnLetter(colNum),
                        value: cell.value
                    });
                }
            }
            
            // 提取样本数据（前3行非空数据）
            let sampleCount = 0;
            for (let rowNum = 2; rowNum <= worksheet.rowCount && sampleCount < 3; rowNum++) {
                const row = worksheet.getRow(rowNum);
                const rowData = {};
                let hasData = false;
                
                for (let colNum = 1; colNum <= worksheet.columnCount; colNum++) {
                    const cell = row.getCell(colNum);
                    if (cell.value !== null && cell.value !== undefined && cell.value !== '') {
                        rowData[getColumnLetter(colNum)] = cell.value;
                        hasData = true;
                    }
                }
                
                if (hasData) {
                    sheetInfo.sampleData.push(rowData);
                    sampleCount++;
                }
            }
            
            templateStructure.worksheets.push(sheetInfo);
        });
        
        console.log(JSON.stringify(templateStructure, null, 2));
        
        return templateStructure;
        
    } catch (error) {
        console.error('❌ 分析模板时出错:', error.message);
        if (error.code === 'ENOENT') {
            console.error('💡 请确保文件路径正确: docs-delivery/unit-test-report/UT動作確認チェックリスト.xlsx');
        }
        throw error;
    }
}

function getColumnLetter(columnNumber) {
    let result = '';
    while (columnNumber > 0) {
        columnNumber--;
        result = String.fromCharCode(65 + (columnNumber % 26)) + result;
        columnNumber = Math.floor(columnNumber / 26);
    }
    return result;
}

// 如果直接运行此脚本
if (require.main === module) {
    analyzeCustomerTemplate()
        .then(() => {
            console.log('\n✅ 模板分析完成!');
        })
        .catch(error => {
            console.error('❌ 分析失败:', error.message);
            process.exit(1);
        });
}

module.exports = { analyzeCustomerTemplate };