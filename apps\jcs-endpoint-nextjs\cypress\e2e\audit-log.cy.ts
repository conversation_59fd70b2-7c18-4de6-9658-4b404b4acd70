describe("ログイン監査ログのテスト", () => {
  const validCredentials = {
    userId: "hitachi.taro.aa",
    password: "changeit!@#",
  };

  it("ログアウトの時監査ログを正常に記録できる", () => {
    cy.intercept("POST", "/api/audit-login-logs", (req) => {
      expect(req.body.auditType).to.eq("LOGOUT");
      expect(req.body.loginMessage).to.eq("");
    });

    cy.visit("/login");
    cy.get("#userId").type(validCredentials.userId);
    cy.get("#password").type(validCredentials.password);
    cy.get("button").contains("ログイン").click();
    cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/servers");
    cy.get("button").contains("ログアウト").click();
    cy.get(
      "#logout-modal > .w-full > .relative > .flex-row-reverse > button.bg-gradient-dark",
    ).click();
    cy.url().should("eq", Cypress.config().baseUrl + "/login");
  });
});
