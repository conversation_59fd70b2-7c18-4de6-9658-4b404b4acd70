# 数据模型: 计划可用手册 (PlanManual)

*   **表名 (逻辑名)**: `PlanManual`
*   **对应UI界面**: N/A (主要由系统内部用于定义契约计划与产品手册之间的可用关系)
*   **主要用途**: 作为 `Plan` (契约计划信息) 表和 `ProductManual` (产品手册) 表之间的多对多关联（中间）表。它记录了特定契约计划下，哪些产品手册（由其唯一资料编号`serialNo`标识）是可用的。

## 1. 字段定义

| 字段名 (Prisma/英文) | 数据类型 (SQL参考) | PK | FK | UQ | Nullable | Default  | 描述 (中文)                                                                                     |
| :------------------- | :----------------- | :--- | :--- | :--- | :------- | :------- | :---------------------------------------------------------------------------------------------- |
| `id`                 | VARCHAR(36)        | ●    |      |      |          | `cuid()` | 主键。CUID格式，由系统自动生成。                                                                    |
| `serialNo`           | VARCHAR(XX)        |      | ●    |      |          |          | **外键**。关联到 `ProductManual` 表的 `serialNo` (唯一键)。表示关联的具体产品手册。                      |
| `planId`             | VARCHAR(XX)        |      | ●    |      | Yes      |          | **可选外键**。关联到 `Plan` 表的 `planId` (业务唯一键)。表示此手册可用性条目属于哪个契约计划。             |

*PK: Primary Key, FK: Foreign Key, UQ: Unique Key*
*XX表示具体长度，取决于实际的数据库Schema定义。*

## 2. 关系

*   **对 `Plan` (`plan`)**: 可选的多对一关系。通过 `planId` 字段关联到 `Plan` 表的 `planId` 唯一键。一个手册可用性条目可以属于一个契约计划，或者在 `planId` 为 NULL 的情况下不直接属于任何特定计划（需确认此业务逻辑是否适用）。
*   **对 `ProductManual` (`productManual`)**: 多对一关系。通过 `serialNo` 字段关联到 `ProductManual` 表的 `serialNo` 唯一键。

## 3. 索引

*   `PRIMARY KEY (id)`
*   `INDEX idx_planId_plan_manual (planId)` (Prisma Schema已定义，用于优化按计划ID的查询)
*   `INDEX idx_serialNo_plan_manual (serialNo)` (Prisma Schema已定义，用于优化按手册资料编号的查询，并支持外键约束)
*   (可选) `UNIQUE KEY UQ_PlanManual_Plan_SerialNo (planId, serialNo)` (如果业务要求一个计划下每个手册的关联是唯一的)