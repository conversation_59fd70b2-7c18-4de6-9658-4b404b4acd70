describe("画面操作のテスト", () => {
  describe("ログアウト確認ダイアログ", () => {
    const validCredentials = {
      userId: "hitachi.taro.aa",
      password: "changeit!@#",
    };

    beforeEach(() => {
      cy.visit("/login");
      cy.get("#userId").type(validCredentials.userId);
      cy.get("#password").type(validCredentials.password);
      cy.get("button").click();
    });

    it("キャンセルボタンをクリックすると、ダイアログが閉じられる", () => {
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("ログアウト").click();
      cy.get("#logout-modal button").contains("キャンセル").click();
      cy.get("#logout-modal").should("have.class", "hidden");
      cy.get("aside #file li:nth-child(1) a").click();
      cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/oplogs");
      cy.title().should("eq", "操作ログ一覧");
    });

    it("✖アイコンをクリックすると、ダイアログが閉じられる", () => {
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("ログアウト").click();
      cy.get("#logout-modal button svg.h-3").click();
      cy.get("#logout-modal").should("have.class", "hidden");
      cy.get("aside #file li:nth-child(1) a").click();
      cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/oplogs");
      cy.title().should("eq", "操作ログ一覧");
    });

    it("OKボタンをクリックすると、ログイン画面へ遷移する", () => {
      cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/servers");
      cy.title().should("eq", "サーバ一覧");
      cy.get("button").contains("ログアウト").click();
      cy.get("#logout-modal button").contains("OK").click();
      cy.url().should("eq", Cypress.config().baseUrl + "/login");
      cy.get("#userId").type(validCredentials.userId);
      cy.get("#password").type(validCredentials.password);
      cy.get("button").click();
      cy.contains("サーバ一覧").should("be.visible");
    });
  });
});
