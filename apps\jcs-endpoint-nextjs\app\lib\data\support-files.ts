import prisma from "@/app/lib/prisma";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { revalidateTag, unstable_cache } from "next/cache";
import { cookies } from "next/headers";
import {
  ENV,
  SUPPORT_IMPORTANCE,
  PORTAL_CACHE_KEY_SUPPORT_FILES
} from "../definitions";
import { LogFunctionSignature } from "../logger";
import { handleServerError } from "../portal-error";
import { castValueToLabel } from "../utils";

/**
 * サポートファイル（SupportFile）関連のデータ操作を行う静的クラスです。
 * サポートファイルのキャッシュ取得、ページ数計算、フィルタ・ソート処理を担当します。
 */
export class ServerDataSupportFiles {
  /**
   * 指定されたライセンスIDに紐づくサポートファイル一覧をキャッシュ付きで取得します。
   * キャッシュキーとタグにライセンスIDを含めることで、正確なキャッシュ無効化を実現します。
   *
   * @param {string} licenseId - ライセンスID
   * @returns {Promise<any[]>} サポートファイル情報配列
   */
  static async fetchCachedSupportFiles(licenseId: string) {
    const cachedFn = unstable_cache(
      async () => {
        const plansWithLicense = await prisma.license.findUnique({
          where: { licenseId },
          select: {
            licensePlans: { select: { planId: true } },
          },
        });
        const planFiles = await prisma.planSupport.findMany({
          where: {
            planId: {
              in: plansWithLicense!.licensePlans.map((plan) => plan.planId),
            },
          },
          distinct: ["serialNo"],
          include: { supportFile: true },
        });
        const importanceTypes = await prisma.lov.findMany({
          where: { parentCode: SUPPORT_IMPORTANCE },
        });
        return planFiles.map((planFile) => {
          const { supportFile } = planFile;
          return {
            ...supportFile,
            importance: castValueToLabel(supportFile.importance, importanceTypes) || "",
          };
        });
      },
      [`${PORTAL_CACHE_KEY_SUPPORT_FILES}-${licenseId}`],
      {
        revalidate: ENV.APP_CACHE_TTL_SECONDS,
        tags: [`${PORTAL_CACHE_KEY_SUPPORT_FILES}-${licenseId}`],
      },
    );
    return await cachedFn();
  }

  /**
   * サポートファイル一覧のページ数を取得します。
   *
   * @param {string} filter - フィルタ文字列
   * @param {number} size - 1ページあたりの件数
   * @param {boolean} refresh - キャッシュ強制リフレッシュ有無
   * @returns {Promise<number>} ページ数
   */
  @LogFunctionSignature()
  static async fetchSupportFilePages(filter: string, size: number, refresh: boolean) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);
      if (refresh) {
        revalidateTag(`${PORTAL_CACHE_KEY_SUPPORT_FILES}-${session!.user.licenseId}`);
      }
      const cachedFiles = await this.fetchCachedSupportFiles(session!.user.licenseId);
      if (cachedFiles) {
        if (filter) {
          const filteredFiles = cachedFiles.filter(
            (file) =>
              file.updatedAt.toLowerCase().includes(filter.toLowerCase()) ||
              file.title.toLowerCase().includes(filter.toLowerCase()) ||
              file.productName.toLowerCase().includes(filter.toLowerCase()) ||
              file.importance.toLowerCase().includes(filter.toLowerCase()) ||
              file.publishedAt.toLowerCase().includes(filter.toLowerCase()),
          );
          return Math.ceil(Number(filteredFiles.length) / size);
        } else {
          return Math.ceil(Number(cachedFiles.length) / size);
        }
      } else {
        return 0;
      }
    } catch (error) {
      handleServerError(error);
    }
  }

  /**
   * フィルタ・ソート・ページング済みのサポートファイル一覧を取得します。
   *
   * @param {string} filter - フィルタ文字列
   * @param {number} size - 1ページあたりの件数
   * @param {number} page - ページ番号
   * @param {"updatedAt"|"title"|"productName"|"importance"|"publishedAt"} sort - ソートキー
   * @param {"asc"|"desc"} order - ソート順
   * @param {string} [preferSort] - 優先ソートキー
   * @returns {Promise<any[]>} サポートファイル情報配列
   */
  @LogFunctionSignature()
  static async fetchFilteredSupportFiles(
    filter: string,
    size: number,
    page: number,
    sort: "updatedAt" | "title" | "productName" | "importance" | "publishedAt",
    order: "asc" | "desc",
    preferSort?: "updatedAt" | "title" | "productName" | "importance" | "publishedAt",
  ) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);
      const cachedSupportFiles = await this.fetchCachedSupportFiles(session!.user.licenseId);
      if (cachedSupportFiles) {
        let filteredSupportFiles = cachedSupportFiles;
        if (filter) {
          filteredSupportFiles = cachedSupportFiles.filter(
            (file) =>
              file.updatedAt.toLowerCase().includes(filter.toLowerCase()) ||
              file.title.toLowerCase().includes(filter.toLowerCase()) ||
              file.productName.toLowerCase().includes(filter.toLowerCase()) ||
              file.importance.toLowerCase().includes(filter.toLowerCase()) ||
              file.publishedAt.toLowerCase().includes(filter.toLowerCase()),
          );
        }
        if (sort) {
          filteredSupportFiles.sort((a, b) => {
            const aValue = a[sort].toLowerCase();
            const bValue = b[sort].toLowerCase();
            if (!preferSort || sort === preferSort) {
              if (order === "asc") {
                return aValue.localeCompare(bValue);
              } else {
                return bValue.localeCompare(aValue);
              }
            } else {
              let firstComparison;
              if (order === "asc") {
                firstComparison = aValue.localeCompare(bValue);
              } else {
                firstComparison = bValue.localeCompare(aValue);
              }
              if (firstComparison !== 0) {
                return firstComparison;
              }
              return a[preferSort].localeCompare(b[preferSort]);
            }
          });
        }
        const startIndex = (page - 1) * size;
        const endIndex = startIndex + size;
        const paginatedSupportFiles = filteredSupportFiles.slice(startIndex, endIndex);
        return paginatedSupportFiles;
      } else {
        return [];
      }
    } catch (error) {
      handleServerError(error);
    }
  }
} 