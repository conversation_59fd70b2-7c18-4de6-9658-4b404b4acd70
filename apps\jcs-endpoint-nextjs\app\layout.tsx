/**
 * @file layout.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

// These styles apply to every route in the application
import "@/styles/globals.css";
import { Metadata } from "next"; // メタ情報インターフェース
import { FC, PropsWithChildren } from "react"; // 遅延読み込みコンポーネント

const title = "JP1 Cloud Service エンドポイント管理";
const description = "JP1 Cloud Service エンドポイント管理";

export const metadata: Metadata = {
  title,
  description,
  themeColor: "#FFF",
  metadataBase: new URL("http://localhost:3000"),
};

const RootLayout: FC<PropsWithChildren> = function ({ children }) {
  return (
    <html lang="ja">
      <body className="font-body antialiased">{children}</body>
    </html>
  );
};

export default RootLayout;
