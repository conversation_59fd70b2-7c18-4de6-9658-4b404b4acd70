/**
 * @file use-notifications.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

import { fetcher } from "@/app/lib/utils";
import useSWR from "swr";

// ユーザーに関連するお知らせリスト取得のためのカスタムフック
const useNotifications = (url: string) => {
  // const url = `/api/notifications`; // お知らせリストAPIのエンドポイント
  const { data, error, isLoading, mutate } = useSWR(url, fetcher); // SWRフックを使用してデータ取得

  // データ、エラー、読み込み中の状態、データ更新関数を返す
  return {
    data,
    error,
    isLoading,
    mutate,
  };
};

// カスタムフックをエクスポート
export default useNotifications;
