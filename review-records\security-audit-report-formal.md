# JCS端点資産・タスク管理システム - セキュリティ監査報告書（修正対応版）

**監査日**: 2025年1月17日
**監査範囲**: 全システム（Next.js アプリケーション、Azure Functions）
**監査基準**: セキュリティ開発チェックリスト準拠
**監査員**: セキュリティ監査チーム
**報告書バージョン**: 修正対応版 v1.0

---

## 概要

JCS端点資産・タスク管理システムに対して実施したセキュリティ監査において発見された脆弱性について、修正対応を完了した。本報告書は、発見された**18件のセキュリティ問題**の詳細と実施した対策について記載する。

### 監査結果統計
- **発見問題総数**: 18件
- **高リスク問題**: 9件（全て修正完了）
- **中リスク問題**: 6件（全て修正完了）
- **低リスク問題**: 3件（全て修正完了）
- **修正完了率**: 100%

### 修正対応期間
- **修正開始日**: 2025年1月10日
- **修正完了日**: 2025年1月17日
- **修正期間**: 7日間

---

## セキュリティ問題詳細一覧

| 題名 | 現象 | 発生条件 | 原因 | 対策－修正前(対策前) | 対策－修正後(対策後) |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **CL-1-6: NULL文字フィルタリングの不備** | URLパラメータ内のNULL文字（%00）が適切に処理されず、アプリケーションの入力検証を回避される。結果として、パス遍歴攻撃などのセキュリティリスクに繋がる可能性がある。 | 悪意のあるユーザーが、URLのパスやクエリパラメータにNULL文字を含んだリクエストを送信した際に発生する。 | middleware.tsおよび各APIルートにおいて、NULL文字を検知・拒否する統一的な入力検証ロジックが実装されていないため。 | middleware.tsおよび各APIにNULL文字に対する統一的なフィルタリング処理が存在しない状態であった。 | 1. middleware.tsにNULL文字（\0または%00）を検知する機能を追加し、該当リクエストを400エラーとして処理する。<br>2. APIエンドポイントで同様のチェックを実装する。 |
| **CL-5-8: Cookieのセキュリティフラグ設定不備** | セッションCookieのsecure属性がfalse、sameSite属性がlaxに設定されている。これにより、本番環境で中間者攻撃やセッションハイジャックのリスクが高まる。 | ユーザーが本番環境（HTTPS）でシステムにログインし、セッションCookieが発行される際に発生する。 | app/lib/session.ts内において、Cookieのセキュリティ属性が本番環境を考慮せず、安全性の低い値で固定されているため。 | Cookieのsecure属性はfalse、sameSite属性はlaxに設定されていた。 | 1. 本番環境ではsecure属性をtrueに設定する。<br>2. sameSite属性をより厳格なstrictに変更する。<br>3. これらの設定を環境変数によって動的に管理する。 |
| **CL-9-3: キャッシュ制御の不備** | 認証が必要なページにおいて、Cache-Controlヘッダが適切に設定されていない箇所がある。これにより、ブラウザや中間プロキシに機密情報を含むページがキャッシュされ、情報漏洩に繋がるリスクがある。 | ユーザーがログイン後、機密情報を含むページ（例: /dashboard/*）にアクセスした際に発生する。 | next.config.jsや各ページコンポーネントにおいて、認証後のページに対するキャッシュ無効化ヘッダの指定が網羅的でないため。 | 一部のページでのみキャッシュ制御が実装されており、認証後の全ページに対する包括的な設定が不足していた。 | 1. 認証が必要な全ページ（/dashboard/*）およびAPIエンドポイント（/api/*）に対して、キャッシュを禁止するHTTPヘッダ（Cache-Control: no-store等）をデフォルトで設定する。<br>2. CSPやX-Frame-Options等の他のセキュリティヘッダも併せて実装する。 |
| **CL-19-1: React Strict Modeの無効化** | 開発中の潜在的な問題を検知するreactStrictModeが無効化されている。原因は、特定のコンポーネント（Keycloakコールバック）が冪等でなく、Strict Mode有効時に2回実行されて不具合が起きるため。 | next.config.jsにてreactStrictModeがfalseに設定されている状態で、開発および本番ビルドが行われる場合に発生する。 | call-back-form.tsxコンポーネント内の処理に冪等性がなく、再実行に耐えられない設計であったため、問題の顕在化を避ける目的でStrict Mode自体を無効にしていた。 | reactStrictModeがfalseに設定されていた。 | 1. CallBackFormコンポーネントをuseRefなどを用いて修正し、処理が複数回実行されても問題ないように冪等性を確保する。<br>2. reactStrictModeをtrueに設定し、有効化する。 |
| **CL-19-2: デバッグ情報漏洩のリスク** | TypeScriptの型エラーが存在する状態で本番ビルドが成功する設定（ignoreBuildErrors: true）になっている。これにより、意図しない型不整合やロジックエラーが本番環境に含まれ、予期せぬ挙動やデバッグ情報の漏洩に繋がる可能性がある。 | next.config.jsにてignoreBuildErrorsがtrueに設定されている状態でnext buildコマンドが実行された場合に発生する。 | 開発中に発生したTypeScriptの型エラーを解消せず、ビルドを強制的に成功させるためにignoreBuildErrorsオプションが有効にされていたため。 | next.config.jsのtypescript設定でignoreBuildErrorsがtrueとなっていた。 | 1. ignoreBuildErrorsをfalseに変更する。<br>2. この変更によって顕在化する全てのTypeScriptエラーを修正し、型安全性を確保した上でビルドプロセスを厳格化する。 |
| **CL-9-6: セッション鍵のハードコーディング** | セッション暗号化用の秘密鍵が、ソースコード内に直接ハードコーディングされていた。これにより、リポジトリへのアクセス権を持つ者全員が秘密鍵を閲覧でき、セキュリティレベルが著しく低下していた。 | app/lib/session.tsファイルを参照した際に発生する。 | セキュリティ上重要な値を環境変数として外部から注入する設計原則が守られておらず、利便性のために直接コードに記述されていたため。 | セッション鍵がapp/lib/session.ts内に文字列としてハードコードされていた。 | 1. 秘密鍵を環境変数（ENV.SESSION_SECRET）から読み込むように修正した。<br>2. 環境変数が未設定の場合のデフォルト値を用意し、後方互換性を確保した。 |
| **CL-3-1: 不安全な乱数生成** | Reactコンポーネントのキー生成などに、暗号学的に安全でないMath.random()が使用されていた。この乱数は予測可能であるため、セキュリティ用途には不適切である。 | license-modal.tsxなどの対象コンポーネントが表示される際に発生する。 | Math.random()の予測可能性に関する認識が不足しており、一意な値を生成する目的で安易に使用されていたため。 | Math.random()を用いて乱数を生成していた。 | 1. 暗号学的に安全な乱数を生成するcrypto.randomUUID()に置換した。 |
| **CL-23-1, CL-21-6, CL-7-2: ファイルサイズの制限不備** | ファイルアップロード機能において、アップロードされるファイルのサイズに上限が設けられていなかった。これにより、巨大なファイルを送りつけられることによるサービス妨害（DoS）攻撃に対して脆弱であった。 | ユーザーが管理定義のインポート機能などで、意図的に巨大なファイルをアップロードしようとした際に発生する。 | クライアントサイドおよびサーバーサイドの両方で、受け付けるファイルの最大サイズを検証するロジックが実装されていなかったため。 | ファイルアップロード時のサイズ検証が存在しなかった。 | 1. クライアントとサーバーの両方で10MBのファイルサイズ上限を設ける検証を実装した。<br>2. 制限値は統一的な定数として管理する。 |
| **CL-2-2: エラーログからの情報漏洩** | アプリケーションでエラーが発生した際、本番環境のログにスタックトレースなどの詳細なデバッグ情報が出力されていた。これにより、攻撃者にシステムの内部構造に関するヒントを与え、情報漏洩に繋がる可能性があった。 | 本番環境で予期せぬエラーが発生し、ログが記録される際に発生する。 | portal-error.tsにおいて、ログ出力レベルが環境（開発/本番）に応じて制御されておらず、常に詳細な情報が出力される設定になっていたため。 | 本番環境でもエラーのスタックトレースがログに出力されていた。 | 1. 本番環境（production）ではスタックトレースをログに出力しないように、環境に応じたログレベルの制御を実装した。 |
| **CL-19-2: TypeScript型安全性問題（refreshToken）** | ignoreBuildErrorsの無効化により、NextApiRequestの型不整合エラーが露呈し、本番ビルド失敗のリスクがある。<br>・影響箇所: api/refreshToken/route.ts | ビルドプロセスでTypeScriptの型チェックが厳格化された際に発生する。 | Next.js App Router環境のAPIルートで、引数の型にレガシーなNextApiRequestが使用されているため。 | APIルートハンドラの引数にNextApiRequest型が使用されていた。 | 1. 引数の型をApp Router標準のNextRequest型に変更する。<br>2. ビルドプロセスの検証を強化する。 |
| **CL-19-2: TypeScript型安全性問題（oplogs）** | ignoreBuildErrorsの無効化により、NextApiResponseの型不整合エラーが露呈し、本番ビルド失敗のリスクがある。<br>・影響箇所: app/dashboard/oplogs/[licenseId]/[fileName]/route.ts | ビルドプロセスでTypeScriptの型チェックが厳格化された際に発生する。 | Next.js App Router環境のAPIルートで、引数の型にレガシーなNextApiResponseが使用されているため。 | APIルートハンドラの引数にNextApiResponse型が使用されていた。 | 1. 引数の型をWeb標準のRequest型に変更する。<br>2. ビルドプロセスの検証を強化する。 |
| **CL-19-2: TypeScript型安全性問題（header）** | ignoreBuildErrorsの無効化により、MessageModalコンポーネントのプロパティ不足エラーが露呈し、本番ビルド失敗のリスクがある。<br>・影響箇所: header.tsx | ビルドプロセスでTypeScriptの型チェックが厳格化された際に発生する。 | header.tsxからMessageModalコンポーネントを呼び出す際に、必須プロパティであるisOpenが渡されていなかったため。 | MessageModal呼び出し時にisOpenプロパティが欠落していた。 | 1. コンポーネント呼び出し箇所でisOpenプロパティを追加する。<br>2. ビルドプロセスの検証を強化する。 |
| **CL-23-10: 依存関係の脆弱性スキャン不足** | プロジェクトが利用するサードパーティ製ライブラリ（依存関係）に対する定期的な脆弱性スキャンが実施されていない。これにより、既知の脆弱性を持つライブラリが意図せず使用され続け、システム全体が危険に晒されるリスクがある。 | 開発プロセスにおいて、依存関係の脆弱性をチェックする手順（例: npm audit）が定義・実施されていない場合に発生する。 | 開発ライフサイクルに、サードパーティライブラリの脆弱性を定期的に棚卸し、更新するプロセスが組み込まれていないため。 | npm audit等の脆弱性スキャンが定常的に実施されていなかった。 | 1. 月次でnpm auditを手動実行し、脆弱性レポートを確認・評価するプロセスを導入する。<br>2. 脆弱性が発見された場合は、迅速に対応計画を立て、依存関係を更新する。 |
| **CL-10-2: タスク Function App のグローバル変数のスレッドセーフティ問題** | Azureサービスへ接続するクライアントインスタンスがグローバル変数として定義・共有されている。Azure Functionsのようなサーバーレス環境では、並行リクエスト間でインスタンスが再利用され、競合状態（Race Condition）を引き起こし、データ不整合に繋がる可能性がある。 | 複数のリクエストがほぼ同時に同一のAzure Functionに到達し、共有クライアントインスタンスにアクセスした場合に発生する。 | lib/azureClients.tsにおいて、リクエスト毎にインスタンスを生成するのではなく、モジュールスコープで単一のインスタンスを生成・再利用する設計になっているため。 | Azureクライアントインスタンスがグローバルな状態で共有されていた。 | 1. ファクトリーパターンを導入し、リクエスト毎に新しいクライアントインスタンスを生成する方式に変更する。<br>2. これにより、リクエスト間の状態分離を徹底し、スレッドセーフティを確保する。 |
| **CL-10-2: Runbook ジョブ Function App のグローバル変数のスレッドセーフティ問題** | Azureサービスへ接続するクライアントインスタンスがグローバル変数として定義・共有されている。Azure Functionsのようなサーバーレス環境では、並行リクエスト間でインスタンスが再利用され、競合状態（Race Condition）を引き起こし、データ不整合に繋がる可能性がある。 | 複数のリクエストがほぼ同時に同一のAzure Functionに到達し、共有クライアントインスタンスにアクセスした場合に発生する。 | lib/azureClients.tsにおいて、リクエスト毎にインスタンスを生成するのではなく、モジュールスコープで単一のインスタンスを生成・再利用する設計になっているため。 | Azureクライアントインスタンスがグローバルな状態で共有されていた。 | 1. ファクトリーパターンを導入し、リクエスト毎に新しいクライアントインスタンスを生成する方式に変更する。<br>2. これにより、リクエスト間の状態分離を徹底し、スレッドセーフティを確保する。 |
| **CL-4-2: API認証・認可制御の不備** | API呼び出し時の認証・認可チェックが不十分で、適切なセッション検証とライセンス確認が実装されていない。これにより、未認証ユーザーや権限のないユーザーがAPIにアクセスできるリスクがある。 | APIエンドポイントへのアクセス時、特にセッション検証やライセンス確認が必要な操作において発生する。 | Webアプリケーションにおけるアクセス認可の3段階構成（ログイン有無、ページ単位の許可、パラメタ単位の許可）が適切に実装されていなかったため。 | 基本的な認証のみで、包括的なセッション検証とライセンス確認が不足していた。 | 1. セッション検証機能を強化し、有効なセッションのみAPIアクセスを許可。<br>2. ライセンス確認機能を実装し、適切な権限を持つユーザーのみアクセス可能に。<br>3. 監査ログ記録機能を追加し、セキュリティイベントを記録。 |
| **CL-10-1: 並行処理競合状態の防止不備** | データベース更新時に楽観ロック制御が実装されておらず、並行処理による競合状態が発生し、データ整合性が保証されない。複数のプロセスが同時に同一リソースを更新する際に、データ競合が発生するリスクがある。 | 複数のプロセスが同時に同一タスクまたはコンテナステータスを更新する場合に発生する。特に高負荷時やマルチユーザー環境で顕在化しやすい。 | 楽観ロック制御が実装されておらず、並行処理時のデータ競合を防ぐメカニズムが不足していたため。データベース更新時の競合状態防止策が不十分であった。 | updateManyでupdatedAt条件なしの更新処理が実装されていた。 | 1. updateManyでupdatedAt: originalUpdatedAtによる楽観ロック制御を追加。<br>2. 並行処理時のデータ競合を防ぐメカニズムを実装し、データ整合性を確保。 |
| **CL-1-3: 入力データ妥当性検証の不備** | 関数パラメータの型が不安全で、実行時エラーの可能性がある。Service Busから送信されるメッセージの型安全性が確保されておらず、予期しないデータ形式による障害が発生するリスクがある。 | Service Busから不正な形式のメッセージが送信される場合、または外部システムからの入力データが想定外の形式である場合に発生する。 | any型使用により型安全性が確保されておらず、送られてきたデータの妥当性チェックが不十分であったため。入力データの検証機能が適切に実装されていなかった。 | message: anyパラメータ型を使用し、適切な型チェックが実装されていなかった。 | 1. message: unknown型に変更し、適切な型チェックを実装。<br>2. 送信されたデータの妥当性を厳密にチェックする機能を追加し、型安全性を確保。 |

---

## 修正対応サマリー

### 修正分類別統計
- **入力検証・フィルタリング**: 3件
- **認証・セッション管理**: 3件
- **キャッシュ・ヘッダ制御**: 1件
- **開発環境・ビルド設定**: 2件
- **型安全性・TypeScript**: 4件
- **ログ・情報漏洩**: 1件
- **ファイルアップロード**: 1件
- **依存関係管理**: 1件
- **並行処理・スレッドセーフティ**: 3件

### 影響範囲別統計
- **Next.js アプリケーション**: 12件
- **Azure Functions（タスク）**: 2件
- **Azure Functions（Runbook）**: 1件
- **共通ライブラリ**: 2件
- **開発プロセス**: 1件

### 修正完了状況
- **即座対応（1-2日）**: 9件（完了）
- **短期対応（3-5日）**: 7件（完了）
- **中期対応（1週間）**: 2件（完了）

---

## セキュリティ向上効果

### 1. 入力検証の強化
- NULL文字フィルタリングによるパス遍歴攻撃の防止
- ファイルサイズ制限によるDoS攻撃の防止
- 統一的な入力検証ロジックの実装

### 2. セッション・認証セキュリティの向上
- Cookieセキュリティフラグの適切な設定
- セッション鍵の環境変数管理
- 本番環境でのHTTPS強制

### 3. 情報漏洩の防止
- 本番環境でのスタックトレース出力制御
- キャッシュ制御による機密情報保護
- デバッグ情報の適切な管理

### 4. 型安全性・コード品質の向上
- TypeScriptビルドエラーの厳格化
- Next.js App Router対応の型定義
- React Strict Modeによる潜在的問題の検知

### 5. 並行処理の安全性確保
- Azureクライアントのスレッドセーフティ実装
- ファクトリーパターンによる状態分離
- 競合状態の防止

### 6. 開発プロセスの改善
- 依存関係脆弱性スキャンの定期実施
- セキュリティ監査の継続的実施
- 修正対応の迅速化

---

## 結論

本セキュリティ監査により発見された18件の問題について、全て修正対応を完了した。これにより、システム全体のセキュリティレベルが大幅に向上し、以下の効果が期待される：

1. **攻撃耐性の向上**: 入力検証強化により、パス遍歴攻撃やDoS攻撃に対する耐性が向上
2. **情報漏洩リスクの軽減**: 適切なキャッシュ制御とログ管理により、機密情報の漏洩リスクを軽減
3. **セッションセキュリティの強化**: Cookieセキュリティフラグとセッション鍵管理の改善
4. **システム安定性の向上**: 型安全性とスレッドセーフティの確保により、システムの安定性が向上
5. **継続的セキュリティの確保**: 定期的な脆弱性スキャンプロセスの導入

今後も定期的なセキュリティ監査を実施し、新たな脅威に対する継続的な対策を講じることを推奨する。

---

**報告書作成日**: 2025年1月17日  
**作成者**: セキュリティ監査チーム  
**承認者**: プロジェクトマネージャー  
**次回監査予定**: 2025年4月17日（3ヶ月後）
