# 组件：管理项目定义导出功能 (Export Management Item Definition Feature)

## 1. 概要 (Overview)

### 1.1 目的 (Purpose)
本功能允许用户针对特定的JP1/ITDM2服务器，发起一个后台任务，以导出该服务器当前的管理项目定义。导出的定义文件（通常为CSV格式，固定文件名为 `assetsfield_def.csv`）将存储在Azure Blob Storage中，并可在“任务列表”中供用户下载。此功能旨在帮助用户备份服务器配置或在其他环境中复用配置。

### 1.2 用户故事/需求 (User Stories/Requirements)
*   作为一名顾客系统管理员，我希望能方便地从服务器列表为选定的JP1/ITDM2服务器发起管理项目定义导出任务，以便备份当前配置。
*   作为一名顾客系统管理员，我希望导出的管理项目定义文件能够安全存储，并且我可以在任务完成后从门户下载它。

### 1.3 高层依赖与交互 (High-Level Dependencies & Interactions)
*   **依赖于**:
    *   [服务器列表主功能组件设计](../server-list.md)：用户通过此界面的任务操作菜单发起本导出任务，并负责处理最终的确认流程。
    *   用户认证模块：确保用户已登录并具有相应权限。
    *   Server Action [`createTaskAction` 组件设计文档](../../actions/create-task-action.md)：作为所有后台任务创建请求的统一入口。
    *   数据服务模块 (`apps/jcs-endpoint-nextjs/app/lib/data.ts`)：间接通过 `createTaskAction` 使用。
    *   常量定义模块 (`apps/jcs-endpoint-nextjs/app/lib/definitions.ts`)：用于可能的内部常量定义（但用户可见消息文本通过消息ID从[`错误消息定义`](../../../definitions/error-messages.md)获取）。
    *   环境变量：`AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` (由 `RunbookProcessorFunc` 和 `createTaskAction` 中的保留策略逻辑使用，其值应为 "assetsfield-def")。详细定义参见 [`环境变量指南`](../../../guides/environment-variables.md)。
    *   后端核心服务 (Azure Functions `RunbookProcessorFunc`, Azure Automation Runbook)：负责实际的任务编排与执行，以及结果文件归档。
    *   Azure SQL Database：存储 `Task`, `ContainerConcurrencyStatus`, `Server`, `License`, `Lov` 等表。
    *   Azure Blob Storage：用于存储最终导出的管理项目定义CSV文件 (固定文件名为 `assetsfield_def.csv`)，路径为 `{licenseId}/exports/{taskId}/assetsfield_def.csv`。
    *   Azure Files：Runbook将导出的CSV文件输出到其Azure Files工作区的 `exports/` 目录 (文件名为 `assetsfield_def.csv`)，供 `RunbookProcessorFunc` 归档。
*   **交互**:
    *   用户在[服务器列表主功能组件设计](../server-list.md)的界面选择“管理项目定义のエクスポート”操作后，`ServerListPage` 组件将显示一个通用的二次确认对话框 (使用 `app/ui/message-modal.tsx`)，提示信息为：“{サーバ名}の管理項目定義をエクスポートします。よろしいですか？” (其中 `{サーバ名}` 替换为目标服务器名称)。
    *   用户在通用确认对话框中确认执行后，`ServerListPage` 将调用 [`createTaskAction` Server Action](../../actions/create-task-action.md)，并将任务类型 (`TASK_TYPE.MGMT_ITEM_EXPORT`) 和服务器ID作为参数提交。**`createTaskAction` 成功后会将任务消息发送到队列，后续由一系列后端Azure Functions进行异步处理，主要包括：[`TaskExecuteFunc`](../../backend-services-functions/function-task-execute.md) 负责任务的启动和Runbook提交，[`RunbookMonitorFunc`](../../backend-services-functions/function-runbook-monitor.md) 负责监控Runbook执行状态，[`RunbookProcessorFunc`](../../backend-services-functions/function-runbook-processor.md) 负责处理最终结果（包括将导出的定义文件归档到Azure Blob Storage并更新`Task.outputBlobPath`）和资源清理。前端在 `createTaskAction` 调用成功后，不直接获取新任务ID，而是通过后续的任务列表刷新来查看新创建的任务及其状态。**
    *   任务的执行状态和结果可在[任务列表组件设计](../../13-task-list.md)中跟踪。
    *   导出的管理项目定义文件最终通过任务列表提供的下载链接从Azure Blob Storage下载。该链接的路径信息由后端[`RunbookProcessorFunc`](../../backend-services-functions/function-runbook-processor.md)在任务成功完成后更新到 `Task.outputBlobPath` 字段中 (指向路径为 `{licenseId}/exports/{taskId}/assetsfield_def.csv` 的文件，容器由环境变量 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` 指定)。

## 2. 功能规格 (Functional Specifications)

### 2.1 主要流程 (High-Level Flow)
```mermaid
graph TD
    A["用户在服务器列表选择目标服务器<br/>并从任务菜单选择<br/>“管理项目定义のエクスポート”"] --> B["服务器列表页面 (ServerListPage):<br/>打开通用确认模态框 (MessageModal)<br/>(消息：“{サーバ名}の管理項目定義をエクスポートします。よろしいですか？”)"];
    B -- 用户点击“OK/エクスポート”按钮 --> C["服务器列表页面 (ServerListPage Frontend):<br/>调用 createTaskAction Server Action<br/>(taskType: 'TASK_TYPE.MGMT_ITEM_EXPORT', serverId)"];
    C -- Server Action成功接收并处理 --> D["服务器列表页面 (ServerListPage Frontend):<br/>根据消息ID EMEC0025 (参考 '错误消息定义')<br/>显示任务提交成功日文提示。<br/>(关闭确认模态框)"];
    D --> E["用户可在“任务列表”<br/>查看任务状态和结果<br/>(任务成功后，包含下载链接)。<br/>(前端不直接从createTaskAction获取taskId)"];
    B -- 用户点击“キャンセル”或关闭对话框 --> F[操作取消, 对话框关闭];
    C -- Server Action处理失败<br/>(例如容器繁忙 EMEC0022 等) --> G["服务器列表页面 (ServerListPage Frontend):<br/>根据返回的消息ID (参考 '错误消息定义')<br/>显示相应的日文错误提示。<br/>(关闭确认模态框)"];
```

### 2.2 业务规则 (Business Rules)
*   本操作仅对类型为JP1/ITDM2的服务器（其`Server.type`值为`'SERVER_TYPE.GENERAL_MANAGER'`或`'SERVER_TYPE.RELAY_MANAGER'`，由[服务器列表主功能组件设计](../server-list.md)控制入口的可见性）可用。
*   此任务类型不需要用户提供额外的参数，仅需在通用确认对话框中确认即可发起。确认对话框提示文本为 “{サーバ名}の管理項目定義をエクスポートします。よろしいですか？” (其中 `{サーバ名}` 替换为目标服务器名称)。
*   后台任务的并发控制由通用的 [`createTaskAction` Server Action](../../actions/create-task-action.md) (并发检查，若记录不存在则创建) 及其调用的后端服务 (`TaskExecuteFunc` 负责写锁)处理。
*   导出的管理项目定义文件（CSV格式，由后端Runbook确保导出的文件名固定为 `assetsfield_def.csv`）将存储在Azure Blob Storage中。目标容器的名称通过环境变量 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` (其值应为 "assetsfield-def") 进行配置。Blob存储的相对路径格式为：`{licenseId}/exports/{Task.id}/assetsfield_def.csv`。
*   成功导出的文件的实际存储相对路径将由后端的 `RunbookProcessorFunc` 在任务完成后，更新到对应 `Task` 记录的 `outputBlobPath` 字段中。其表结构定义参见[`Task数据模型定义`](../../../data-models/task.md)。
*   每个发起的导出任务都会在 `Task` 数据库表中创建一条记录。
*   **任务记录与文件保留策略**: `Task` 表中的任务记录遵循系统中定义的保留策略（每个服务器的任务记录上限由 `LOV` 表 `TASK_CONFIG.MAX_RETENTION_COUNT` 的值设定，默认为10条，定义于[`LOV值列表定义`](../../../definitions/lov-definitions.md)）。当为某个服务器创建新任务导致超出此限制时，最早的旧 `Task` 记录将被删除。**由于本任务类型为管理项目定义导出，在删除对应的 `Task` 记录时，系统将联动删除其在Azure Blob Storage中通过 `Task.outputBlobPath` 字段关联的管理项目定义CSV文件。** 此保留策略的详细执行逻辑定义在 [`createTaskAction` Server Action](../../actions/create-task-action.md) 的任务记录保留策略部分。

### 2.3 用户界面概述 (User Interface Overview)
*   **发起入口**: [服务器列表主功能组件设计](../server-list.md)界面的每行JP1/ITDM2服务器记录的任务操作下拉菜单中的“管理項目定義のエクスポート”选项。
*   **主要界面**: 一个通用的二次确认对话框，用于在执行操作前向用户进行最终确认。
    *   **实现组件**: 通用消息模态框 `apps/jcs-endpoint-nextjs/app/ui/message-modal.tsx` (由[服务器列表主功能组件设计](../server-list.md)调用并配置)。
    *   **提示消息**: “{サーバ名}の管理項目定義をエクスポートします。よろしいですか？” (其中 `{サーバ名}` 替换为目标服务器名称)。

### 2.4 前提条件 (Preconditions)
*   用户已通过身份验证并成功登录到门户系统。
*   目标服务器记录在系统中存在，并且其类型允许执行管理项目定义导出任务。
*   后端任务处理相关的Azure服务及数据库表已正确配置并运行。
*   Server Action [`createTaskAction` 组件设计文档](../../actions/create-task-action.md) 已在 `apps/jcs-endpoint-nextjs/app/lib/actions.ts` 中定义并可用。
*   `LOV` 表中与管理项目定义导出相关的配置项（如 `TASK_TYPE.MGMT_ITEM_EXPORT`, `TASK_CONFIG.MAX_RETENTION_COUNT`）已正确配置。其权威定义参见[`LOV值列表定义`](../../../definitions/lov-definitions.md)。
*   相关的**环境变量**（如 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF`，其值应为 "assetsfield-def"）已正确设置。其权威定义参见[`环境变量指南`](../../../guides/environment-variables.md)。
*   用户提示信息的消息ID定义在[`错误消息定义`](../../../definitions/error-messages.md)中 (例如 `EMEC0025` 用于成功提交)。

### 2.5 制约事项 (Constraints/Limitations)
*   不支持一次性为多个服务器批量导出管理项目定义。
*   导出操作是异步的后台任务，用户需要通过[任务列表组件设计](../../13-task-list.md)来查看任务的完成状态和下载导出的文件。

### 2.6 注意事项 (Notes/Considerations)
*   导出的管理项目定义CSV文件的具体内容和结构，由目标服务器上JP1/ITDM2管理工具的导出功能决定。后端Runbook执行导出后，文件名应固定为 `assetsfield_def.csv`，并放置在Azure Files工作区的 `exports/` 目录。

### 2.7 错误处理概述 (Error Handling Overview)
*   **任务提交失败 (由 `createTaskAction` 返回)**: 如果 [`createTaskAction` Server Action](../../actions/create-task-action.md) 因目标服务器容器繁忙 (返回 `EMEC0022`)、或后端队列服务暂时不可用 (返回 `EMEC0019`) 等原因未能成功创建任务，它将返回包含错误信息的 `CreateTaskActionResult` 对象。调用方（[服务器列表主功能组件设计](../server-list.md)）将根据返回的消息ID (其日文文本在[`错误消息定义`](../../../definitions/error-messages.md)) 向用户显示相应的日文错误提示。
*   **后台任务执行失败**: 若任务在后端执行过程中失败（例如，Runbook执行错误，可能返回 `EMET0012` - 有详细错误），`Task` 表中对应任务的状态将被更新为错误状态，并可能记录错误详情。用户可在[任务列表组件设计](../../13-task-list.md)中查看到此失败状态。

### 2.8 相关功能参考 (Related Functional References)
*   [服务器列表主功能组件设计](../server-list.md)
*   Server Action: [`createTaskAction` 组件设计文档](../../actions/create-task-action.md)
*   [任务列表组件设计](../../13-task-list.md)
*   数据模型:
    *   [`Task` 数据模型定义](../../../data-models/task.md)
    *   [`Server` 数据模型定义](../../../data-models/server.md)
    *   [`License` 数据模型定义](../../../data-models/license.md)
    *   [`ContainerConcurrencyStatus` 数据模型定义](../../../data-models/container-concurrency-status.md)
*   系统级定义:
    *   [`LOV值列表定义`](../../../definitions/lov-definitions.md)
    *   [`错误消息定义`](../../../definitions/error-messages.md)
    *   [`项目术语表`](../../../definitions/glossary.md)
*   [`环境变量指南`](../../../guides/environment-variables.md)
*   常量定义: `apps/jcs-endpoint-nextjs/app/lib/definitions.ts` (用于内部常量，用户消息通过消息ID获取)

---

## 3. 技术设计与实现细节 (Technical Design & Implementation)

### 3.1 技术栈与依赖 (Technology Stack & Dependencies)
*   **前端 (通用确认对话框的调用方为 [服务器列表主功能组件设计](../server-list.md) (`page.tsx`))**:
    *   React (Next.js App Router 架构)
    *   通用消息模态框组件: `apps/jcs-endpoint-nextjs/app/ui/message-modal.tsx`
*   **后端 (主要通过 [`createTaskAction` Server Action](../../actions/create-task-action.md))**:
    *   Next.js Server Action (`createTaskAction` 位于 `apps/jcs-endpoint-nextjs/app/lib/actions.ts`)
*   **共享类型定义与常量**: `apps/jcs-endpoint-nextjs/app/lib/definitions.ts`。
*   **配置数据来源**:
    *   `LOV` 表 (业务参数)。其权威定义参见[`LOV值列表定义`](../../../definitions/lov-definitions.md)。
    *   环境变量 (基础设施配置如容器名 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF`)。其权威定义参见[`环境变量指南`](../../../guides/environment-variables.md)。

### 3.2 详细界面元素定义 (Detailed UI Element Definitions)

#### 3.2.1 管理项目定义导出确认对话框 (由 `ServerListPage` 使用通用 `app/ui/message-modal.tsx` 弹出)
*   **用途**: 在用户发起“管理项目定义导出”任务前，向用户进行最终确认。
*   **实现组件**: `apps/jcs-endpoint-nextjs/app/ui/message-modal.tsx`。
*   **调用时机**: 当用户在[服务器列表主功能组件设计](../server-list.md)的任务操作下拉菜单中选择了“管理項目定義のエクスポート”选项后，`ServerListPage` 组件 (`page.tsx`) 会通过更新其内部的 `activeConfirmModal` (或类似名称) 状态来触发显示此通用模态框，并为其传递特定的 `props`。
*   **传递给 `app/ui/message-modal.tsx` 的关键Props (由 `ServerListPage` 设置)**:

| Prop名 | 数据类型 | 描述 (中文) | 值/来源 (中文描述) |
|-------|------|---------|-------------|
| `isOpen` | `boolean` | 控制模态框的显示与隐藏。 | `ServerListPage` 的 `activeConfirmModal.isOpen` 状态 (当 `activeConfirmModal.taskType === 'TASK_TYPE.MGMT_ITEM_EXPORT'` 时)。 |
| `title` | `string` | 模态框的标题 (日文)。 | "管理項目定義のエクスポート確認" (或直接从 `LOV` 表中 `TASK_TYPE.MGMT_ITEM_EXPORT` 任务类型的 `name` 字段获取，并附加“の確認”)。 |
| `message` | `React.ReactNode` | 模态框中显示的确认信息文本 (日文)。 | “{サーバ名}の管理項目定義をエクスポートします。よろしいですか？”。其中 `{サーバ名}` 部分由 `ServerListPage` 的 `activeConfirmModal.server.name` 动态替换后传入。 |
| `confirmText` | `string` | 确认按钮上显示的文本 (日文)。 | "エクスポート" (或 "OK")。 |
| `cancelText` | `string` | 取消按钮上显示的文本 (日文)。 | "キャンセル"。 |
| `onConfirm` | `() => Promise<void> \| void` | 用户点击确认按钮 (例如“エクスポート”) 时，由此Prop指定的回调函数将被执行。 | `ServerListPage` 中定义的 `handleActualTaskSubmit` 方法 (已绑定上下文，该方法内部将构造不含特定任务参数的FormData并调用 `createTaskAction`)。 |
| `onCancel` | `() => void` | 用户点击取消按钮或关闭模态框时，由此Prop指定的回调函数将被执行。 | `ServerListPage` 中定义的关闭通用确认模态框的方法 (例如 `handleCloseConfirmModal`)。 |
| `isProcessing` | `boolean` | 指示确认操作（即调用 `onConfirm` 后的处理）是否正在进行中。此状态用于在 `message-modal.tsx` 内部禁用按钮或显示加载状态。 | `ServerListPage` 中管理的 `isSubmittingTask` 状态。 |
| `type` | `'info' \| 'warning' \| 'error' \| 'confirmation'` | 模态框的类型，影响其视觉样式（例如图标）。对于此确认操作，应为 `'confirmation'` 或 `'info'`。 | `'confirmation'` (或 `'info'`)。 |

### 3.3 详细事件处理逻辑 (Detailed Event Handling)

本功能的前端事件处理逻辑主要由[服务器列表主功能组件设计](../server-list.md) (`page.tsx`) 驱动，通过其 `handleOpenTaskInitiation` (或类似函数，用于决定打开哪个模态框或直接进入确认流程) 和 `handleActualTaskSubmit` (或类似函数，用于在通用确认后调用Server Action) 进行管理。

#### 3.3.1 从服务器列表选择“管理项目定义导出” (打开通用确认对话框)
*   **处理流程**: 当用户选择此任务时，`ServerListPage` 的 `handleOpenTaskInitiation` (或其内部逻辑) 判断此任务类型 (`'TASK_TYPE.MGMT_ITEM_EXPORT'`) 不需要参数输入，因此直接准备并显示一个配置了特定标题、确认消息 (文本为“{サーバ名}の管理項目定義をエクスポートします。よろしいですか？”) 和回调的通用消息模态框 (`app/ui/message-modal.tsx`)。

#### 3.3.2 在通用确认对话框中点击“确认” (调用 `createTaskAction`)
*   **处理流程**: 通用消息模态框的 `onConfirm` 回调（即 `ServerListPage` 的 `handleActualTaskSubmit`）被触发。该函数会：
    1.  设置提交状态 `isSubmittingTask = true`。
    2.  从 `activeConfirmModal.server.id` (或类似状态) 获取 `serverId`。
    3.  构造仅包含 `taskType: 'TASK_TYPE.MGMT_ITEM_EXPORT'` 和 `serverId` 的 `FormData`。
    4.  调用 `await createTaskAction(undefined, formData)`。
    5.  根据返回的 `CreateTaskActionResult` 处理成功或失败的UI反馈（关闭模态框、显示通知，通知文本基于返回的 `messageId`，例如成功时为 `EMEC0025`）。[`createTaskAction` Server Action](../../actions/create-task-action.md) 内部应负责调用 `revalidatePath`。
    6.  最后设置 `isSubmittingTask = false`。

#### 3.3.3 在通用确认对话框中点击“キャンセル”或关闭
*   **处理流程**: 通用消息模态框的 `onCancel` 回调被触发，关闭该模态框。

### 3.4 数据结构与API/Server Action交互 (Data Structures and API/Server Action Interaction)

#### 3.4.1 前端状态 (在 `ServerListPage` 中管理)
*   `activeConfirmModal`: (结构示例: `{ isOpen: boolean; taskType: string | null; server: ServerDefinition | null; title: string; message: React.ReactNode; onConfirm: () => void; ... }`)
*   `isSubmittingTask`: (布尔值)

#### 3.4.2 Server Action: `createTaskAction` (针对 `TASK_TYPE.MGMT_ITEM_EXPORT`)

调用 [`createTaskAction` Server Action](../../actions/create-task-action.md) 时传递的 `FormData` 包含：
*   `taskType: string`: `'TASK_TYPE.MGMT_ITEM_EXPORT'`。
*   `serverId: string`。

返回值结构为 [`CreateTaskActionResult`](../../actions/create-task-action.md#13-返回值-createtaskactionresult)。

#### 3.4.3 主要交互序列图 (Mermaid Sequence Diagram for Export Management Definition)

```mermaid
sequenceDiagram
    actor User as 用户
    participant FrontendApp as 前端应用 (门户)
    participant NextJsAppServer as Next.js 应用服务器 (后端)
    participant Database as 数据库 (Task表等)
    participant MessageQueue as 消息队列 (TaskInputQueue)

    User->>FrontendApp: 1. 在服务器列表选择“管理项目定义导出”
    FrontendApp->>User: 2. (前端内部逻辑) 显示通用二次确认信息<br/>(例如：“确定要导出[服务器]的管理项目定义吗？”)
    User->>FrontendApp: 3. 点击“OK/はい” (最终确认)
    FrontendApp->>NextJsAppServer: 4. 发起后台任务创建请求 (HTTP POST)<br/>(调用 createTaskAction Server Action,<br/>携带taskType='TASK_TYPE.MGMT_ITEM_EXPORT', serverId)
    activate NextJsAppServer
    NextJsAppServer->>NextJsAppServer: 5. (在createTaskAction内) 执行通用前置校验<br/>(用户会话,权限,服务器配置,并发检查, <br/> 若并发记录不存在则创建并初始化为IDLE)<br/>及特定任务逻辑(此类型任务无额外特定参数校验)
    alt 前置校验失败 (例如容器繁忙)
        NextJsAppServer-->>FrontendApp: 6a. 返回错误响应 (含messageId)
    else 前置校验成功
        NextJsAppServer->>Database: 6b. (在createTaskAction内) 执行核心数据库事务<br/>(含任务保留策略, 创建Task记录)
        activate Database
        Database-->>NextJsAppServer: 7b. 数据库事务结果
        deactivate Database
        alt 数据库事务失败 (或并发锁获取失败)
            NextJsAppServer-->>FrontendApp: 8a. 返回DB错误响应 (含messageId)
        else 数据库事务成功
            NextJsAppServer->>MessageQueue: 8b. (在createTaskAction内) 发送任务消息 (仅含taskId)
            activate MessageQueue
            MessageQueue-->>NextJsAppServer: 9b. 消息发送结果
            deactivate MessageQueue
            alt 消息发送失败
                NextJsAppServer-->>FrontendApp: 10a. 返回队列错误响应 (含messageId)
            else 消息发送成功
                NextJsAppServer->>NextJsAppServer: 10b. (在createTaskAction内) 调用 revalidatePath
                NextJsAppServer-->>FrontendApp: 11b. 返回成功响应 (含messageId)
            end
        end
    end
    deactivate NextJsAppServer
    
    FrontendApp->>User: 12. 根据Server Action响应结果<br/>显示成功或失败提示 (基于messageId)。<br/>(关闭确认模态框)
```

### 3.5 数据库设计与访问详情 (Database Design and Access Details - 主要通过Server Action间接访问)
*   **`Task` 表**: 当任务类型为 `'TASK_TYPE.MGMT_ITEM_EXPORT'` 时，`parametersJson` 字段将存储包含执行上下文信息（如 `dockerContainerName`, `azureVmName`, `hrwGroupName`）的JSON字符串，这些信息由 `createTaskAction` 从 `Server` 表查询并准备。`outputBlobPath` 字段将由后端 `RunbookProcessorFunc` 更新，指向最终导出的CSV文件 (固定名为 `assetsfield_def.csv`) 在Azure Blob Storage (`AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` 指定的容器) 中的相对路径 `{licenseId}/exports/{taskId}/assetsfield_def.csv`。其表结构定义参见[`Task数据模型定义`](../../../data-models/task.md)。
*   其他相关表如 `ContainerConcurrencyStatus`, `Server`, `License`, `Lov` 的交互逻辑详见 [`createTaskAction` Server Action文档](../../actions/create-task-action.md)。

### 3.6 关键后端逻辑/算法 (Key Backend Logic/Algorithms - 主要在Server Action `createTaskAction` 中)

#### 3.6.1 [`createTaskAction`](../../actions/create-task-action.md) 中处理 `TASK_TYPE.MGMT_ITEM_EXPORT` 的特定逻辑分支

当 [`createTaskAction` Server Action](../../actions/create-task-action.md) 接收到 `taskType` 为 `'TASK_TYPE.MGMT_ITEM_EXPORT'` 的请求时，其特定逻辑主要体现在（详细定义见 `createTaskAction.md` 的3节）：
1.  **特有输入参数的提取与服务器端深度校验规则**: 此任务类型**不包含**由前端用户直接输入的特定参数。因此，在 `createTaskAction` 的特定任务逻辑处理步骤中，针对此 `taskType` 的分支将不执行额外的参数提取或校验。
2.  **构造存入 `Task.parametersJson` 的特定参数部分 (`taskSpecificParamsForDb`)**: 无特定于此任务的用户输入参数需要存入 `parametersJson`。（执行上下文信息如 `dockerContainerName` 等已直接存入 `Task` 表的专用字段）。
3.  **构造发送到Service Bus消息体中（供 `TaskExecuteFunc` 使用）的参数**: **当前设计为仅在消息中传递 `taskId`。** `TaskExecuteFunc` 将根据 `taskId` 从数据库的 `Task` 记录中获取所有执行所需的上下文信息。
4.  后续的通用处理流程（如并发检查、任务记录保留策略、数据库事务、消息发送）遵循 `createTaskAction.md` 的定义。特别注意，在执行任务记录保留策略时，对于此类型的旧任务，会根据 `Task.outputBlobPath` 联动删除已导出的文件。

### 3.7 错误处理详情 (Detailed Error Handling)

由于此任务类型在前端没有用户输入的特定参数，其主要的错误场景与 [`createTaskAction` Server Action文档](../../actions/create-task-action.md) 的通用错误处理机制重合。用户提示信息通过消息ID从[`错误消息定义`](../../../definitions/error-messages.md)获取。

| 错误场景描述 (中文) | 触发位置 (ServerAction调用方 / ServerAction) | 返回的 `CreateTaskActionResult` / 客户端处理方式 | 用户提示信息 (日文界面文本) (基于消息ID，其日文文本定义在[`错误消息定义`](../../../definitions/error-messages.md) 中) | 系统内部处理及日志记录建议 (中文) |
|-------------|---------------------------------------|----------------------------------------|---------------------------------------------------------------------------------------|--------------------|
| (所有通用错误场景，参考 [`createTaskAction` Server Action文档](../../actions/create-task-action.md) 和 [服务器列表主功能组件设计](../server-list.md) 的错误处理详情部分。) | `createTaskAction` Server Action / `ServerListPage` | (参考对应文档) | (参考对应文档，例如容器繁忙 `EMEC0022`，DB错误 `EMEC0006`，队列错误 `EMEC0019` 等) | (参考对应文档) |

### 3.8 配置项 (Configuration)
*   **`LOV` 表** (其权威定义参见 [`LOV值列表定义`](../../../definitions/lov-definitions.md)):
    *   `TASK_TYPE.MGMT_ITEM_EXPORT` (`code`: "TASK_TYPE.MGMT_ITEM_EXPORT", `name` (日文): "管理項目定義のエクスポート")
    *   `TASK_CONFIG.MAX_RETENTION_COUNT` (`code`: "TASK_CONFIG.MAX_RETENTION_COUNT", `value`: "10" (示例值))
    *   相关消息ID (例如 `EMEC0025` 用于成功提交，其日文文本在[`错误消息定义`](../../../definitions/error-messages.md)中定义)。
*   **环境变量** (其权威定义参见 [`环境变量指南`](../../../guides/environment-variables.md)):
    *   `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF`: (值应为 "assetsfield-def") 指定存储最终导出的管理项目定义CSV文件的Azure Blob Storage容器名称。此配置主要由后端的 `RunbookProcessorFunc` (或类似服务) 和执行文件删除的逻辑（在 `createTaskAction` 中的保留策略部分）使用。
    *   间接依赖于 [`createTaskAction` Server Action](../../actions/create-task-action.md) 所需的其他环境变量。
*   **常量定义 (`apps/jcs-endpoint-nextjs/app/lib/definitions.ts`)**: (不直接用于用户消息，但可能用于内部逻辑或类型)。用户可见消息通过消息ID从[`错误消息定义`](../../../definitions/error-messages.md)获取。

### 3.9 注意事项与其他 (Notes/Miscellaneous)
*   **Runbook脚本 (`Export-ManagementDefinition.ps1` - 假设名称)**:
    *   需能正确接收 `targetContainerName` 参数。
    *   负责调用目标Docker容器内JP1/ITDM2的管理项目定义导出命令。
    *   将导出的CSV文件（**Runbook应确保导出的文件名固定为 `assetsfield_def.csv`**）输出到Azure Files工作区的 `exports/assetsfield_def.csv` 路径。
    *   执行完毕后上报任务状态及结果信息。
*   **后端状态处理服务 (`RunbookProcessorFunc`)**:
    *   成功时，从工作区读取 `exports/assetsfield_def.csv` 文件，上传到由环境变量 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` 指定的Blob容器，路径为 `{licenseId}/exports/{taskId}/assetsfield_def.csv`。
    *   **更新对应 `Task` 记录的 `outputBlobPath` 字段**，使其指向该CSV文件在Blob Storage中的相对路径。
    *   清理工作区。
*   **任务记录与文件生命周期**: 本任务产生的 `Task` 记录以及存储在Azure Blob Storage中的CSV文件，均受系统定义的任务记录保留策略（基于 `LOV:TASK_CONFIG.MAX_RETENTION_COUNT`）的约束。当旧的 `Task` 记录被删除时，其通过 `outputBlobPath` 关联的Blob文件也将被一并清除（此删除逻辑由[`createTaskAction` Server Action](../../actions/create-task-action.md)中的保留策略部分负责）。
