/**
 * @fileoverview テーブルヘッダーコンポーネント
 * @description
 * ソート機能付きのテーブルヘッダーを提供する。各列ヘッダーにはソートアイコンが表示され、
 * クリックによってソート順序を切り替えることができる。sortable属性がfalseの列は
 * ソート機能が無効化され、クリックイベントやソートアイコンが表示されない。
 * URLパラメータを通じてソート状態を管理し、ページ遷移時にソート状態を保持する。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

"use client";

import clsx from "clsx";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

interface TheadProps {
  headers: { key: string; label: string; sortable?: boolean }[];
  defaultSort: string;
  defaultOrder: string;
}

/**
 * テーブルヘッダーコンポーネント
 *
 * ソート機能付きのテーブルヘッダーを生成する。各列のソート状態をURLパラメータで管理し、
 * sortable属性がfalseの列はソート機能を無効化する。
 * @param props - コンポーネントのプロパティ
 * @param props.headers - ヘッダー列の定義配列（key、label、sortable属性を含む）
 * @param props.defaultOrder - デフォルトのソート対象列
 * @param props.defaultSort - デフォルトのソート順序（asc/desc）
 * @returns ソート機能付きのテーブルヘッダー要素
 */
export default function Thead({
  headers,
  defaultOrder,
  defaultSort,
}: TheadProps) {
  const pathname = usePathname();
  const { replace } = useRouter();
  const searchParams = useSearchParams();
  const [sort, setSort] = useState("");
  const [order, setOrder] = useState("");

  useEffect(() => {
    setSort(searchParams.get("sort") || defaultOrder);
    setOrder(searchParams.get("order") || defaultSort);
  }, [searchParams, defaultOrder, defaultSort]);

  const Icon = ({ isAsc }: { isAsc?: boolean }) => (
    <img
      src="/screenopenicon16.png"
      className={clsx("icon h-4 w-4 transform-gpu", { "rotate-180": isAsc })}
      alt="sort"
    />
  );

  function toogleOrder() {
    const newOrder = order === "asc" ? "desc" : "asc";
    setOrder(newOrder);

    return newOrder;
  }

  function handleSort(sortKey: string) {
    const params = new URLSearchParams(searchParams.toString());

    if (sortKey === sort) {
      params.set("order", toogleOrder());
    } else {
      setSort(sortKey);
      setOrder("asc");
      params.set("sort", sortKey);
      params.set("preferSort", sortKey);
      params.set("order", "asc");
    }

    params.set("page", "1");
    replace(`${pathname}?${params.toString()}`);
  }

  return (
    <thead className="bg-gray-50 text-xs text-gray-700">
      <tr>
        {headers.map((header, index) => {
          const isSortable = header.sortable !== false;
          return (
            <th
              key={header.key}
              scope="col"
              className={clsx("px-6 py-3", { "border-l": index !== 0 })}
            >
              <div
                className={clsx("flex justify-between", {
                  "cursor-pointer hover:opacity-80 transition duration-300": isSortable,
                })}
                onClick={isSortable ? () => handleSort(header.key) : undefined}
              >
                <span>{header.label}</span>
                {isSortable && header.key === sort ? (
                  <Icon isAsc={order === "asc"} />
                ) : (
                  <div className="w-4"></div>
                )}
              </div>
            </th>
          );
        })}
      </tr>
    </thead>
  );
}
