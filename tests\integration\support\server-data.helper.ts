/**
 * 服务器测试数据管理辅助模块
 * 用于 Playwright 测试中创建、管理和清理服务器测试数据
 */

import { PrismaClient } from '@prisma/client';
import { randomUUID } from 'crypto';

// 创建 Prisma 客户端实例
const prisma = new PrismaClient();

// 导出 prisma 实例供测试使用
export { prisma };

// 测试许可证ID常量 - 使用worker隔离策略
// 动态生成许可证ID以支持真正的并发测试
export function generateTestLicenseId(): string {
  const workerId = process.env.PLAYWRIGHT_WORKER_INDEX || process.env.TEST_WORKER_INDEX || Math.floor(Math.random() * 1000);
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 6);
  return `test-license-w${workerId}-${timestamp}-${randomSuffix}`;
}

// 为了向后兼容，保留静态ID（但建议使用动态生成）
export const TEST_LICENSE_ID = generateTestLicenseId();

/**
 * 测试服务器类型枚举
 * 注意：这些值必须与实际的LOV定义中的code值完全一致
 */
export enum ServerType {
  GENERAL_MANAGER = 'SERVER_TYPE.GENERAL_MANAGER',
  RELAY_MANAGER = 'SERVER_TYPE.RELAY_MANAGER',
  HIBUN_CONSOLE = 'SERVER_TYPE.HIBUN_CONSOLE'
}

/**
 * 测试服务器配置接口
 */
export interface TestServerConfig {
  name?: string;
  type: ServerType;
  url?: string;
  licenseId: string;
  hrwGroupName?: string;
  azureVmName?: string;
  dockerContainerName?: string;
}

/**
 * 创建的测试服务器记录
 */
export interface TestServer {
  id: string;
  name: string;
  type: string;
  url: string;
  licenseId: string;
  hrwGroupName?: string | null;
  azureVmName?: string | null;
  dockerContainerName?: string | null;
}

// 存储当前测试会话中创建的所有测试服务器ID
const createdTestServerIds: string[] = [];

/**
 * 生成唯一的测试服务器名称 - 支持worker隔离
 * @param baseName 基础名称
 * @returns 带有worker ID和时间戳的唯一名称
 */
function generateUniqueServerName(baseName: string = 'e2e-test-server'): string {
  const workerId = process.env.PLAYWRIGHT_WORKER_INDEX || process.env.TEST_WORKER_INDEX || Math.floor(Math.random() * 1000);
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  return `${baseName}-w${workerId}-${timestamp}-${randomSuffix}`;
}

/**
 * 创建测试服务器记录
 * @param config 服务器配置
 * @returns 创建的服务器记录
 */
export async function createTestServer(config: TestServerConfig): Promise<TestServer> {
  // 生成默认值
  const serverName = config.name || generateUniqueServerName();
  const serverUrl = config.url || `https://example.com/server/${randomUUID()}`;
  
  try {
    // 创建服务器记录
    const server = await prisma.server.create({
      data: {
        name: serverName,
        type: config.type,
        url: serverUrl,
        licenseId: config.licenseId,
        hrwGroupName: config.hrwGroupName,
        azureVmName: config.azureVmName,
        dockerContainerName: config.dockerContainerName
      }
    });
    
    // 记录创建的服务器ID，用于后续清理
    createdTestServerIds.push(server.id);
    
    return server;
  } catch (error) {
    console.error('创建测试服务器失败:', error);
    throw error;
  }
}

/**
 * 创建多个测试服务器记录
 * @param configs 服务器配置数组
 * @returns 创建的服务器记录数组
 */
export async function createMultipleTestServers(configs: TestServerConfig[]): Promise<TestServer[]> {
  const servers: TestServer[] = [];
  
  for (const config of configs) {
    const server = await createTestServer(config);
    servers.push(server);
  }
  
  return servers;
}

/**
 * 清理指定的测试服务器
 * @param serverId 服务器ID
 */
export async function cleanupTestServer(serverId: string): Promise<void> {
  try {
    // 使用 deleteMany 而不是 delete，这样即使记录不存在也不会抛出异常
    const result = await prisma.server.deleteMany({
      where: { id: serverId }
    });

    if (result.count > 0) {
      console.log(`清理测试服务器 ${serverId} 成功`);
    } else {
      console.log(`测试服务器 ${serverId} 不存在，跳过清理`);
    }

    // 从记录列表中移除
    const index = createdTestServerIds.indexOf(serverId);
    if (index > -1) {
      createdTestServerIds.splice(index, 1);
    }
  } catch (error) {
    console.error(`清理测试服务器 ${serverId} 失败:`, error);
    // 不抛出异常，以免影响其他清理操作
  }
}

/**
 * 清理当前测试会话中创建的所有测试服务器
 */
export async function cleanupAllTestServers(): Promise<void> {
  // 复制数组，因为在迭代过程中会修改原数组
  const serverIdsToCleanup = [...createdTestServerIds];
  
  for (const serverId of serverIdsToCleanup) {
    await cleanupTestServer(serverId);
  }
}

/**
 * 按许可证ID清理测试服务器 - 支持并发测试隔离
 * @param licenseId 许可证ID
 */
export async function cleanupTestServersByLicense(licenseId: string): Promise<void> {
  try {
    // 首先获取要删除的服务器ID列表
    const serversToDelete = await prisma.server.findMany({
      where: { licenseId },
      select: { id: true }
    });

    if (serversToDelete.length > 0) {
      const serverIds = serversToDelete.map(server => server.id);

      // 先删除相关的Task记录
      const taskDeleteResult = await prisma.task.deleteMany({
        where: {
          targetServerId: { in: serverIds }
        }
      });

      if (taskDeleteResult.count > 0) {
        console.log(`清理了 ${taskDeleteResult.count} 个相关任务`);
      }
    }

    // 然后删除服务器
    const result = await prisma.server.deleteMany({
      where: { licenseId }
    });
    console.log(`清理了许可证 ${licenseId} 的 ${result.count} 个服务器`);
  } catch (error) {
    console.error(`清理许可证 ${licenseId} 的服务器失败:`, error);
    throw error;
  }
}

/**
 * 清理所有包含特定名称模式的测试服务器
 * 用于清理可能在之前测试运行中未正确清理的服务器
 * @param pattern 名称模式（正则表达式）
 */
export async function cleanupTestServersByNamePattern(pattern: RegExp): Promise<void> {
  try {
    // 查找匹配模式的所有服务器
    const servers = await prisma.server.findMany({
      where: {
        name: {
          contains: 'e2e-test' // 基本过滤
        }
      }
    });
    
    // 进一步过滤匹配正则表达式的服务器
    const matchingServers = servers.filter(server => pattern.test(server.name));
    
    // 删除匹配的服务器
    for (const server of matchingServers) {
      await prisma.server.delete({
        where: { id: server.id }
      });
      console.log(`已清理测试服务器: ${server.name}`);
    }
  } catch (error) {
    console.error('按名称模式清理测试服务器失败:', error);
    // 不抛出异常，以免影响测试执行
  }
}

/**
 * 获取指定许可证ID下的所有服务器
 * @param licenseId 许可证ID
 * @returns 服务器记录数组
 */
export async function getServersByLicenseId(licenseId: string): Promise<TestServer[]> {
  try {
    return await prisma.server.findMany({
      where: { licenseId }
    });
  } catch (error) {
    console.error(`获取许可证 ${licenseId} 的服务器失败:`, error);
    throw error;
  }
}

/**
 * 创建测试许可证
 * @param licenseId 许可证ID
 */
export async function createTestLicense(licenseId: string): Promise<void> {
  try {
    // 检查许可证是否已存在
    const existingLicense = await prisma.license.findUnique({
      where: { licenseId: licenseId }
    });

    if (!existingLicense) {
      await prisma.license.create({
        data: {
          licenseId: licenseId,
          type: 'test',
          expiredAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1年后过期
          maxClients: 100,
          isMaintenance: false,
          isDisabled: false,
          basicPlan: 'test-plan'
        }
      });
      console.log(`测试许可证已创建: ${licenseId}`);
    } else {
      console.log(`测试许可证已存在: ${licenseId}`);
    }
  } catch (error) {
    // 如果是唯一约束冲突，说明许可证已存在，这是正常的
    if (error instanceof Error && error.message.includes('Unique constraint failed')) {
      console.log(`测试许可证已存在（并发创建）: ${licenseId}`);
      return;
    }
    console.error('创建测试许可证失败:', error);
    throw error;
  }
}

/**
 * 在测试开始前清理可能存在的测试数据
 * @param licenseId 许可证ID
 */
export async function cleanupBeforeTest(licenseId: string): Promise<void> {
  // 清理所有包含 'e2e-test' 的服务器记录
  try {
    await prisma.server.deleteMany({
      where: {
        name: {
          contains: 'e2e-test'
        },
        licenseId
      }
    });
  } catch (error) {
    console.error('清理测试前数据失败:', error);
    // 不抛出异常，以免阻止测试执行
  }
}

/**
 * 强制清理所有测试服务器数据
 * 不依赖内存中的ID记录，直接从数据库删除所有测试数据
 * 注意：由于现在与应用共享数据库，只清理测试相关的数据
 */
export async function forceCleanupAllTestServers(): Promise<void> {
  try {
    // 先查找所有测试服务器
    const testServers = await prisma.server.findMany({
      where: {
        name: {
          contains: 'e2e-test'
        }
      },
      select: { id: true }
    });

    if (testServers.length > 0) {
      const serverIds = testServers.map(s => s.id);

      // 先清理相关的任务记录，避免外键约束违反
      await prisma.task.deleteMany({
        where: {
          targetServerId: {
            in: serverIds
          }
        }
      });

      // 然后删除测试服务器记录
      const testServerResult = await prisma.server.deleteMany({
        where: {
          id: {
            in: serverIds
          }
        }
      });

      console.log(`强制清理了所有 ${testServerResult.count} 个服务器（测试环境）`);
    } else {
      console.log(`强制清理了所有 0 个服务器（测试环境）`);
    }

    // 清空内存中的记录
    createdTestServerIds.length = 0;
  } catch (error) {
    console.error('强制清理测试服务器失败:', error);
    // 不抛出异常，以免阻止测试执行
  }
}

/**
 * 关闭 Prisma 客户端连接
 * 应在所有测试完成后调用
 */
export async function disconnectPrisma(): Promise<void> {
  await prisma.$disconnect();
}