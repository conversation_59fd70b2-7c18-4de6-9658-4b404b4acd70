# JCS 开发环境种子数据生成工具

专为 JCS 端点管理系统开发环境设计的种子数据生成工具。一键生成超过 2,000 条真实日文业务数据。

## 🎯 主要特性

- **🔐 hisol 许可证** - 预配置开发环境必需的许可证
- **📊 覆盖所有页面** - 支持所有 dashboard 页面数据
- **🌏 真实日文数据** - 使用真实的日本企业名称和业务术语
- **⚡ 高性能** - 6-10秒生成完整数据集
- **🛡️ 安全机制** - 只能在开发环境运行

## 🚀 使用方法

### 基本命令

```bash
# 安装依赖
npm install

# 生成所有数据
npm run seed

# 清空所有数据
npm run clean

# 查看统计信息
npm run clean:stats

# 一键重置（清空+重新生成）
npm run reseed
```

### 环境配置

编辑 `.env` 文件，确保数据库URL包含 `database=dev`：

```bash
MSSQL_PRISMA_URL=sqlserver://192.168.11.193:65220;database=dev;user=SA;password=Uknowit1^_^;encrypt=DANGER_PLAINTEXT;
```

## 📊 生成的数据

### 最新验证结果 (2025-07-27)

- ✅ **总记录数**: 2,232条
- ✅ **执行时间**: 6-10秒
- ✅ **包含 hisol 许可证**

### 数据分布

| 数据类型 | 数量 | 对应页面 |
|----------|------|----------|
| LOV数据 | 51件 | 系统基础数据 |
| 许可证 | 11件 | licenses (包含hisol) |
| 服务器 | 200件 | servers |
| 任务 | 500件 | tasks |
| 产品手册 | 150件 | **manuals** |
| 产品媒体 | 100件 | **medias** |
| 提供文件 | 80件 | **provided-files** |
| 支持文件 | 120件 | **support-files** |
| 操作日志 | 1,000件 | **oplogs** |
| 计划数据 | 20件 | plans |

### Plan关联关系

- **プラン-製品関連**: 108件
- **プラン-マニュアル関連**: 84件
- **プラン-提供ファイル関連**: 69件
- **プラン-サポート関連**: 95件

## 📁 项目结构

```
tools/seed-dev/
├── seed-data.ts               # 🚀 主脚本
├── cleanup.ts                 # 🧹 清理脚本
├── generators/                # 🏭 10个数据生成器
├── data/                      # 📊 静态数据文件
└── utils/                     # 🛠️ 工具函数
```

## 🔧 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `.env` 文件配置
   - 确认数据库URL包含 `database=dev`

2. **Prisma客户端错误**
   ```bash
   npm run prisma:generate
   ```

3. **权限错误**
   - 确保数据库URL包含 `database=dev`
   - 这是安全机制，防止在生产环境误操作

4. **重复键错误**
   ```bash
   npm run clean  # 清空后重新生成
   ```

## 📋 技术信息

- **Node.js**: >= 18.0.0
- **数据库**: SQL Server (开发环境)
- **ORM**: Prisma
- **语言**: TypeScript

---

**⚠️ 重要**: 本工具仅用于开发环境，请勿在生产环境中使用。
