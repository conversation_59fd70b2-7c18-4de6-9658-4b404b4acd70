import fixture from "../../fixtures/OperationLog.json";

describe("初期化表示のテスト", () => {
  describe("操作ログ一覧画面", () => {
    const validCredentials = {
      userId: "hitachi.taro.aa",
      password: "changeit!@#",
    };

    beforeEach(() => {
      cy.visit("/login");
      cy.get("#userId").type(validCredentials.userId);
      cy.get("#password").type(validCredentials.password);
      cy.get("button").click();
      cy.wait(2000);
      cy.visit("/dashboard/oplogs");
    });

    it("タイトルが正しく表示される", () => {
      cy.title().should("eq", "操作ログ一覧");
      cy.get("nav").should("contain", "ファイル");
      cy.get("nav").should("contain", "操作ログ一覧");
      cy.get("aside .from-blue-600").should("contain", "操作ログ一覧");
    });

    it("アクションバーが正しく表示される", () => {
      cy.get(".bg-white.h-full input").should(
        "have.attr",
        "placeholder",
        "フィルター",
      );
      cy.get(".bg-white.h-full button span").should("contain", "search");
      cy.get(".bg-white.h-full button span").should("contain", "clear");
      cy.get("#page-left").should("have.prop", "tagName", "DIV");
      cy.get("#page-right").should("have.prop", "tagName", "A");
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .first()
        .should("have.prop", "tagName", "DIV");
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .first()
        .nextAll()
        .should("have.prop", "tagName", "A");
      cy.get(".bg-white.h-full label").should("contain", "行数/ページ:");
      cy.get(".bg-white.h-full select option:selected").then(
        (selectedOption) => {
          cy.wrap(selectedOption).invoke("text").should("include", "10");
          cy.wrap(selectedOption).invoke("val").should("eq", "10");
        },
      );
    });

    it("ページ数が正しく表示される", () => {
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .first()
        .should("have.prop", "tagName", "DIV")
        .invoke("text")
        .should("eq", "1");
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .first()
        .next()
        .should("have.prop", "tagName", "A")
        .invoke("text")
        .should("eq", "2");
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .first()
        .next()
        .next()
        .should("have.prop", "tagName", "A")
        .invoke("text")
        .should("eq", "3");
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .first()
        .next()
        .next()
        .next()
        .should("have.prop", "tagName", "DIV")
        .invoke("text")
        .should("eq", "...");
      const maxPage = Math.ceil(
        fixture.OperationLog.filter((o) => o.licenseId === "hisol").length / 10,
      );
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .last()
        .should("have.prop", "tagName", "A")
        .invoke("text")
        .should("eq", `${maxPage}`);
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .last()
        .prev()
        .should("have.prop", "tagName", "A")
        .invoke("text")
        .should("eq", `${maxPage - 1}`);
      cy.get(".bg-white.h-full .flex-column .bg-white .w-9")
        .last()
        .prev()
        .prev()
        .should("have.prop", "tagName", "DIV")
        .invoke("text")
        .should("eq", "...");
    });

    it("テーブルヘーダが正しく表示される", () => {
      cy.get("thead th span").should("contain", "ログ名");
      cy.get("thead th span")
        .should("contain", "登録日時")
        .next("img")
        .should("exist");
      cy.get("thead th span")
        .should("contain", "登録日時")
        .next("img")
        .should("not.have.class", "rotate-180");
      cy.get("thead th span").should("contain", "保管期限");
      cy.get("thead th span").should("contain", "サイズ");
      cy.get("table").find("thead th").should("have.css", "text-align", "left");
    });

    it("テーブルボディが正しく表示される", () => {
      cy.get("table")
        .find("tbody th, tbody td:nth-child(2), tbody td:nth-child(3)")
        .each((element) => {
          cy.wrap(element).should("have.css", "textAlign", "left");
        });
      cy.get("table")
        .find("tbody td:nth-child(4)")
        .each((element) => {
          cy.wrap(element).should("have.css", "textAlign", "right");
        });
      cy.get("table").find("tbody th").find("a").should("exist");
      cy.get("table tbody tr th a").should("have.attr", "target", "_blank");
      cy.get("table tbody tr").each(($row) => {
        const textInFirstColumn = $row.find("th").text();
        const rowData = fixture.OperationLog.find(
          (s) => s.name === textInFirstColumn,
        );
        expect(textInFirstColumn).to.eq(rowData?.name);

        // @ts-ignore
        const hrefValue = $row.find("th a").attr("href");
        expect(hrefValue).to.eq(
          `oplogs/${rowData?.licenseId}/${rowData?.fileName}`,
        );
      });
    });

    it("登録日時列が降順で並んでいる", () => {
      cy.get("table tbody tr td:nth-child(2)").then(($column) => {
        const columnData = $column
          .map((index, el) => new Date(el.innerText).getTime())
          .get();

        const sortedColumnData = [...columnData].sort((a, b) => b - a);
        expect(columnData).to.deep.equal(sortedColumnData);
      });
    });

    it("テーブルには横方向および縦方向のスクロールバーがない", () => {
      cy.get(".rounded-b-lg").within(() => {
        cy.get("table").then(($child) => {
          const parentClientWidth = $child.parent().width() || 0;
          const parentClientHeight = $child.parent().height() || 0;

          const childScrollWidth = $child[0].scrollWidth;
          const childScrollHeight = $child[0].scrollHeight;

          expect(childScrollWidth).to.be.equal(parentClientWidth);
          expect(childScrollHeight).to.be.equal(parentClientHeight);
        });
      });
    });
  });
});
