---
description: 
globs: 
alwaysApply: true
---
# Tech Stack Overview

This document outlines the primary technologies and architectural patterns used in the `apps/jcs-endpoint-nextjs` application. This information guides AI-assisted programming with Cursor to ensure consistency and effective development. AI should also infer library versions from the project's `package.json` and existing code patterns.

## Core Technologies

*   **Framework:** Next.js (App Router)
*   **Language:** TypeScript
*   **Runtime:** Node.js
*   **Server Logic:** Next.js Server Actions (enabled in `next.config.js`)

## Frontend

*   **UI Components:** Custom-built React components
*   **Styling:**
    *   Tailwind CSS (configured in `tailwind.config.js`)
    *   Global styles in `styles/globals.css`.
    *   Custom themes/gradients defined in `tailwind.config.js`.
*   **UI Widgets/Interactivity:** Flowbite (integrated via Tailwind CSS plugin)
*   **Client-side Data & State:**
    *   SWR (for data fetching, see hooks like `use-license.ts`)
    *   React Context/State (for local component state)
*   **Icons:** React Icons

## Backend & API

*   **API Structure:** Next.js API Routes (located in `app/api/`)
*   **Authentication:** Keycloak (integrated for user authentication)
*   **Session Management:** Iron Session (for server-side session handling)
*   **Token Handling:** `jsonwebtoken` library (for JWT decoding, likely from Keycloak)
*   **Database ORM:** Prisma
*   **File Storage:** Azure Storage Blob

## Development & Operations

*   **Logging:** Winston (custom setup in `app/lib/logger.ts`, includes `@LogFunctionSignature` decorator)
*   **Testing:**
    *   Cypress (for Component and End-to-End testing)
    *   Jest (for unit testing, setup in `jest.setup.js`)
    *   Code coverage reporting using `nyc` (combining Jest & Cypress reports).
*   **Code Quality:**
    *   ESLint (configured via `.eslintrc.json`)
    *   Prettier (configured via `prettier.config.js` with plugins for import/attribute organization and Tailwind CSS)
*   **Package Manager:** (Refer to `package.json` and associated lockfile, e.g., `package-lock.json` indicates npm)
*   **Environment Variables:** Sourced from the environment, with definitions and fallbacks often managed in `app/lib/definitions.ts` (as the `ENV` object).

---

**NOTE TO CURSOR:** Focus on the architectural patterns implied by these technologies (e.g., App Router conventions, Server Actions usage, Prisma for DB access, SWR for client data fetching). Infer specific library versions and detailed API usage from the existing codebase, `package.json`, and the detailed design documents in the Monorepo's `docs/` directory.

---