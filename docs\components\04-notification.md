# 组件：通知信息显示 (Notification Display)

## 1. 概要 (Overview)

### 1.1 目的 (Purpose)
本组件旨在向“JCS 端点资产与任务管理系统”的用户展示由门户系统管理员发布的通知消息。其核心目的是确保用户能够及时获取与系统相关的公告、维护通知、更新说明或其他重要信息。通知信息主要通过主界面导航栏的“お知らせ (通知)”按钮触发的弹窗进行展示，也可能在登录等特定环节展示简要提醒。

### 1.2 用户故事/需求 (User Stories/Requirements)
- 作为一名顾客系统管理员（用户），我希望在登录门户后，能够方便地通过点击导航栏上的“通知”按钮来查看系统管理员发布的最新公告和重要信息。
- 作为一名顾客系统管理员（用户），我希望通知内容清晰易读，如果内容较多，应该可以滚动查看。
- 作为一名门户系统管理员（服务提供方），我希望能够发布针对所有用户、特定契约计划用户、特定契约ID用户或单个用户的通知。
- 作为一名门户系统管理员（服务提供方），我希望发布的通知内容能够被用户及时看到。

### 1.3 高层依赖与交互 (High-Level Dependencies & Interactions)
- **用户认证**: 用户的身份（用户ID、关联的契约ID、契约计划ID）决定了其能看到的通知范围。
- **主界面 (Main Screen)**: 本组件的主要入口点是[主界面](./02-main-screen.md)导航栏上的“お知らせ (通知)”按钮。
- **数据存储**: 通知内容及其元数据（如发布日期、目标用户类型、目标ID等）存储在Azure SQL Database的 `Notification` 表中。
- **后端API (Next.js API Routes)**: 前端通过调用后端API来获取当前用户可见的通知列表。该API负责根据用户身份从数据库查询并过滤通知。
- **登录界面 (Login)**: `fs.md`提及登录界面也可能显示通知信息。这暗示登录流程中可能也会调用API获取通知。

## 2. 功能规格 (Functional Specifications)

### 2.1 主要流程 (High-Level Flow)
1.  用户成功登录系统后，主界面导航栏显示“お知らせ (通知)”按钮。
2.  用户点击“お知らせ (通知)”按钮。
3.  前端向后端API发送请求，获取当前用户可见的通知列表。
4.  后端API根据用户的ID、契约ID、契约计划ID等信息，从 `Notification` 表中查询符合条件的通知记录。
5.  后端API将查询到的通知列表（通常按发布日期降序排列）返回给前端。
6.  前端在一个模态对话框（通知窗口）中展示获取到的通知内容。
    *   若无符合条件的通知，则显示“お知らせはありません。(没有通知。)”之类的提示。
    *   若通知内容过多，超出显示区域，则提供垂直滚动条。
7.  用户阅读通知后，可以点击通知窗口的“閉じる (关闭)”按钮或窗口右上角的“X”按钮关闭通知窗口。

```mermaid
sequenceDiagram
    participant User as 👤 用户 (Browser)
    participant MainScreen as 🖥️ 主界面
    participant NotificationModal as 📢 通知弹窗
    participant NextJsApiRoutes as 🌐 Next.js API Routes
    participant NotificationDB as 💾 Notification表 (SQL DB)

    User->>MainScreen: 点击导航栏“お知らせ”按钮
    MainScreen->>NextJsApiRoutes: GET /api/notifications (请求当前用户通知)
    NextJsApiRoutes->>NotificationDB: 查询Notification表 (基于userId, licenseId, planId, 及共通通知)
    NotificationDB-->>NextJsApiRoutes: 返回符合条件的通知列表
    NextJsApiRoutes-->>MainScreen: HTTP 200 OK, body: { notifications: [...] }
    MainScreen->>NotificationModal: 打开通知弹窗并传入通知数据
    NotificationModal->>User: 显示通知内容 (可滚动)
    User->>NotificationModal: 点击“閉じる”按钮
    NotificationModal-->>MainScreen: 关闭弹窗
```
*注：`fs.md`还提及“登录画面显示通知信息”，这部分流程类似，但触发点在登录组件。*

### 2.2 业务规则 (Business Rules)
-   **通知内容来源**: 所有通知内容均由门户系统管理员在门户数据库的 `Notification` 表中进行维护。
-   **通知格式**: 通知内容仅支持纯文本格式，不支持富文本、HTML或图片。
-   **通知目标类型**: 通知可以针对以下四种目标发布：
    1.  **系统共通**: 对所有登录用户可见。
    2.  **契约计划ID (contractPlanId) 特定**: 仅对属于特定契约计划的用户可见。
    3.  **契约ID (licenseId) 特定**: 仅对属于特定契约（许可证）的用户可见。
    4.  **用户ID (userId) 特定**: 仅对指定用户ID的用户可见。
-   **通知获取逻辑**: 当用户请求通知时，后端API应获取所有“系统共通”通知，以及与该用户当前`contractPlanId`、`licenseId`、`userId`分别匹配的所有特定通知。
-   **通知显示顺序**: 获取到的多条通知应在通知窗口中按其发布日期（或`fs.md`中定义的其他排序字段，如“登録日時”）降序排列，即最新的通知显示在最上方。
-   **内容为空处理**: 如果没有符合当前用户查看条件的通知，通知窗口应显示明确的提示信息，如“お知らせはありません。(没有通知。)”
-   **动态加载**: 每次用户点击“お知らせ”按钮打开通知窗口时，都应从后端重新加载最新的通知内容，不应使用前端缓存的旧数据。
-   **登录界面通知**: `fs.md`中“ログイン”功能的“お知らせエリア”描述了登录界面也会显示通知。此处的通知获取和显示规则应与主界面通知弹窗一致（即获取共通、契约计划、契约、用户四类通知）。

### 2.3 用户界面概述 (User Interface Overview)

-   **入口点**:
    *   主界面导航栏右侧的“お知らせ (通知)”按钮。
    *   登录界面的特定通知显示区域 (根据`fs.md`描述)。
-   **通知窗口 (模态对话框)**:
    *   **触发**: 点击主界面导航栏的“お知らせ”按钮后弹出。
    *   **标题**: “お知らせ (通知)”。
    *   **内容区域**:
        *   用于显示通知文本。
        *   若内容超出固定高度（如 `fs.md` 提及的 `h-96`），则此区域应出现垂直滚动条 (`overflow-y-auto`)。
        *   文本内容应保留换行符 (`whitespace-pre-line`)。
        *   每条通知可能包含其发布日期和通知正文。
    *   **关闭操作**:
        *   窗口右上角提供标准的“X”关闭图标。
        *   窗口底部提供一个“閉じる (关闭)”按钮。
    *   **尺寸**: 窗口宽度可能是响应式的，根据屏幕大小调整（`fs.md` 提及 `max-w-3xl` 到 `2xl:max-w-6xl`）。
-   **界面草图**: (参考 `fs.md` 图4.4.6 (1) 作为通知弹窗的高层概念)
    *   `![通知弹窗示意图](./assets/notification-modal-sketch.png)` (假设存在此图)
-   **主要显示字段 (概念)**:
    *   通知的发布日期/时间。
    *   通知的文本内容。

### 2.4 前提条件 (Preconditions)
-   用户必须已成功通过[登录功能](./01-login.md)完成身份验证并拥有有效的会话（对于主界面通知按钮）。
-   门户系统管理员已在 `Notification` 数据库表中录入了相应的通知信息。
-   后端API (`/api/notifications`) 必须可用，并能正确根据用户身份查询和返回通知数据。

### 2.5 制约事项 (Constraints/Limitations)
-   **浏览器兼容性**: 仅正式支持 Microsoft Edge 和 Google Chrome 浏览器。
-   **语言支持**: 界面显示语言仅支持日语。
-   **内容格式**: 通知内容严格限制为纯文本，不支持任何形式的富文本、HTML标记、图片或附件。
-   **无分类/优先级**: 系统不提供对通知进行分类（如按紧急程度）或设置优先级的功能。所有通知按既定规则（通常是时间）排序显示。
-   **无已读/未读状态**: 系统不跟踪用户是否已阅读某条通知。每次打开通知窗口都会显示所有符合条件的当前通知。

### 2.6 注意事项 (Notes/Considerations)
-   通知内容应保持简洁明了，方便用户快速阅读和理解。
-   对于系统维护、计划停机等非常重要的通知，门户系统管理员应确保其及时发布和更新。
-   虽然 `fs.md` 中未明确提及，但从用户体验角度考虑，如果通知列表非常长，可以考虑在通知窗口内部也实现分页或“加载更多”功能，但这超出了当前FS的明确要求。

### 2.7 错误处理概述 (Error Handling Overview)
-   **通知加载失败**:
    *   如果用户点击“お知らせ”按钮后，后端API调用失败（例如，网络错误、服务器内部错误、数据库连接失败），导致无法获取通知内容，通知窗口应显示一个用户友好的错误提示信息（例如，“无法加载通知信息，请稍后重试。”或类似`fs.md`中EMEC0006, EMEC0007的提示），而不是空白或原始错误代码。
    *   详细的技术错误应记录在前端控制台和/或后端日志中。
-   **无通知信息**: 如果后端API成功返回，但没有符合当前用户条件的通知，通知窗口应清晰地显示“お知らせはありません。”。
-   **显示异常 (罕见)**: 如果因前端渲染问题导致通知窗口本身显示异常（例如，样式错乱、无法关闭），这通常属于更广泛的UI bug，应通过测试发现和修复。保持窗口可关闭是基本要求。

### 2.8 相关功能参考 (Related Functional References)
*   **主要入口**: [主界面 (Main Screen)](./02-main-screen.md) - 导航栏上的“お知らせ”按钮是访问本功能的主要途径。
*   **辅助入口**: [登录 (Login)](./01-login.md) - 登录界面也可能展示相关的通知信息。
*   **系统整体架构**: `../../architecture/system-architecture.md` - 描述了通知功能的数据流和与后端服务的交互。
*   **核心数据模型**: `../../data-models/notification-table.md` (假设文件名) - 定义了通知信息存储的表结构。
*   **源功能规格**: `fs.md` (通常位于 `docs-delivery/機能仕様書/` 或项目指定的源文档位置) - 本组件功能规格的原始日文描述，特别是其“4.4 お知らせ表示”章节。
