import { Page } from '@playwright/test';
import fs from 'fs';
import path from 'path';

/**
 * 📊 性能监控辅助器 - 业界最佳实践
 * 
 * 功能：
 * - Web Vitals 监控
 * - 页面加载性能分析
 * - 网络请求监控
 * - 内存使用监控
 * - 性能报告生成
 */
export class PerformanceHelper {
  private page: Page;
  private metrics: PerformanceMetrics = {};
  private startTime: number = 0;
  private networkRequests: NetworkRequest[] = [];

  constructor(page: Page) {
    this.page = page;
  }

  /**
   * 🚀 开始性能监控
   */
  async startMonitoring(): Promise<void> {
    this.startTime = Date.now();
    
    // 监听网络请求
    this.page.on('request', (request) => {
      this.networkRequests.push({
        url: request.url(),
        method: request.method(),
        startTime: Date.now(),
        resourceType: request.resourceType(),
      });
    });

    this.page.on('response', (response) => {
      const request = this.networkRequests.find(req => 
        req.url === response.url() && !req.endTime
      );
      
      if (request) {
        request.endTime = Date.now();
        request.status = response.status();
        request.size = parseInt(response.headers()['content-length'] || '0');
        request.duration = request.endTime - request.startTime;
      }
    });

    // 注入性能监控脚本
    await this.page.addInitScript(() => {
      // Web Vitals 监控
      window.performanceMetrics = {
        navigationStart: performance.timeOrigin,
        marks: {},
        measures: {},
        vitals: {},
      };

      // 监控 Core Web Vitals
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            window.performanceMetrics.navigation = {
              domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,
              loadComplete: navEntry.loadEventEnd - navEntry.loadEventStart,
              firstPaint: 0, // 将在 paint 观察器中设置
              firstContentfulPaint: 0,
            };
          }
          
          if (entry.entryType === 'paint') {
            if (entry.name === 'first-paint') {
              window.performanceMetrics.navigation.firstPaint = entry.startTime;
            }
            if (entry.name === 'first-contentful-paint') {
              window.performanceMetrics.navigation.firstContentfulPaint = entry.startTime;
            }
          }
        }
      });

      observer.observe({ entryTypes: ['navigation', 'paint'] });

      // 自定义性能标记
      window.markPerformance = (name: string) => {
        performance.mark(name);
        window.performanceMetrics.marks[name] = performance.now();
      };

      window.measurePerformance = (name: string, startMark: string, endMark?: string) => {
        performance.measure(name, startMark, endMark);
        const measure = performance.getEntriesByName(name, 'measure')[0];
        window.performanceMetrics.measures[name] = measure.duration;
      };
    });

    console.log('📊 性能监控已启动');
  }

  /**
   * 🛑 停止性能监控
   */
  async stopMonitoring(): Promise<void> {
    // 收集最终的性能指标
    await this.collectMetrics();
    
    // 生成性能报告
    await this.generateReport();
    
    console.log('📊 性能监控已停止');
  }

  /**
   * 📊 收集性能指标
   */
  async collectMetrics(): Promise<void> {
    try {
      // 从页面获取性能数据
      const pageMetrics = await this.page.evaluate(() => {
        return {
          navigation: window.performanceMetrics?.navigation || {},
          marks: window.performanceMetrics?.marks || {},
          measures: window.performanceMetrics?.measures || {},
          memory: (performance as any).memory ? {
            usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
            totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
            jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit,
          } : null,
        };
      });

      // 计算网络性能指标
      const networkMetrics = this.calculateNetworkMetrics();

      this.metrics = {
        testDuration: Date.now() - this.startTime,
        page: pageMetrics,
        network: networkMetrics,
        timestamp: new Date().toISOString(),
      };

    } catch (error) {
      console.error('❌ 性能指标收集失败:', error);
    }
  }

  /**
   * 🌐 计算网络性能指标
   */
  private calculateNetworkMetrics(): NetworkMetrics {
    const completedRequests = this.networkRequests.filter(req => req.endTime);
    
    if (completedRequests.length === 0) {
      return {
        totalRequests: 0,
        averageResponseTime: 0,
        totalDataTransferred: 0,
        slowestRequest: null,
        fastestRequest: null,
      };
    }

    const durations = completedRequests.map(req => req.duration!);
    const totalSize = completedRequests.reduce((sum, req) => sum + (req.size || 0), 0);

    const slowestRequest = completedRequests.reduce((prev, current) => 
      (current.duration! > prev.duration!) ? current : prev
    );

    const fastestRequest = completedRequests.reduce((prev, current) => 
      (current.duration! < prev.duration!) ? current : prev
    );

    return {
      totalRequests: completedRequests.length,
      averageResponseTime: durations.reduce((sum, duration) => sum + duration, 0) / durations.length,
      totalDataTransferred: totalSize,
      slowestRequest: {
        url: slowestRequest.url,
        duration: slowestRequest.duration!,
        size: slowestRequest.size || 0,
      },
      fastestRequest: {
        url: fastestRequest.url,
        duration: fastestRequest.duration!,
        size: fastestRequest.size || 0,
      },
    };
  }

  /**
   * 📈 生成性能报告
   */
  async generateReport(): Promise<void> {
    try {
      const reportDir = path.resolve(__dirname, '../../test-results/performance');
      if (!fs.existsSync(reportDir)) {
        fs.mkdirSync(reportDir, { recursive: true });
      }

      const reportPath = path.join(reportDir, `performance-${Date.now()}.json`);
      fs.writeFileSync(reportPath, JSON.stringify(this.metrics, null, 2));

      console.log(`📈 性能报告已生成: ${reportPath}`);
    } catch (error) {
      console.error('❌ 性能报告生成失败:', error);
    }
  }

  /**
   * 🎯 标记性能点
   */
  async mark(name: string): Promise<void> {
    await this.page.evaluate((markName) => {
      if (window.markPerformance) {
        window.markPerformance(markName);
      }
    }, name);
  }

  /**
   * 📏 测量性能区间
   */
  async measure(name: string, startMark: string, endMark?: string): Promise<void> {
    await this.page.evaluate((measureName, start, end) => {
      if (window.measurePerformance) {
        window.measurePerformance(measureName, start, end);
      }
    }, name, startMark, endMark);
  }

  /**
   * ⚡ 等待页面性能稳定
   */
  async waitForPerformanceStable(timeout = 10000): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      const isStable = await this.page.evaluate(() => {
        // 检查是否有正在进行的网络请求
        return (performance as any).getEntriesByType('resource')
          .filter((entry: any) => entry.responseEnd === 0).length === 0;
      });

      if (isStable) {
        console.log('⚡ 页面性能已稳定');
        return;
      }

      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.warn('⚠️ 页面性能稳定等待超时');
  }

  /**
   * 📊 获取当前性能指标
   */
  getMetrics(): PerformanceMetrics {
    return this.metrics;
  }
}

// 类型定义
export interface PerformanceMetrics {
  testDuration?: number;
  page?: {
    navigation?: NavigationMetrics;
    marks?: Record<string, number>;
    measures?: Record<string, number>;
    memory?: MemoryMetrics;
  };
  network?: NetworkMetrics;
  timestamp?: string;
}

export interface NavigationMetrics {
  domContentLoaded: number;
  loadComplete: number;
  firstPaint: number;
  firstContentfulPaint: number;
}

export interface MemoryMetrics {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
}

export interface NetworkMetrics {
  totalRequests: number;
  averageResponseTime: number;
  totalDataTransferred: number;
  slowestRequest: RequestSummary | null;
  fastestRequest: RequestSummary | null;
}

export interface NetworkRequest {
  url: string;
  method: string;
  startTime: number;
  endTime?: number;
  status?: number;
  size?: number;
  duration?: number;
  resourceType: string;
}

export interface RequestSummary {
  url: string;
  duration: number;
  size: number;
}

// 扩展 Window 接口
declare global {
  interface Window {
    performanceMetrics: any;
    markPerformance: (name: string) => void;
    measurePerformance: (name: string, startMark: string, endMark?: string) => void;
  }
}
