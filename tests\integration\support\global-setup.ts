import { chromium, FullConfig } from '@playwright/test';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

/**
 * 🚀 全局测试环境设置 - 业界最佳实践
 * 
 * 功能：
 * - 环境验证和准备
 * - 数据库初始化和清理
 * - 服务健康检查
 * - 测试数据准备
 * - 性能监控初始化
 */
async function globalSetup(config: FullConfig) {
  console.log('🚀 开始全局测试环境设置...');

  try {
    // 0. 🔧 加载环境变量
    await loadEnvironmentVariables();

    // 1. 📋 环境验证
    await validateEnvironment();
    
    // 2. 🗄️ 数据库准备
    await setupDatabase();
    
    // 3. 🔧 服务健康检查
    await waitForServices();
    
    // 4. 📊 测试数据准备
    await prepareTestData();
    
    // 5. 📈 性能监控初始化
    await initializeMonitoring();
    
    console.log('✅ 全局测试环境设置完成');
    
  } catch (error) {
    console.error('❌ 全局设置失败:', error);
    throw error;
  }
}

/**
 * 📋 环境验证 - 确保所有必需的工具和配置都可用
 */
async function validateEnvironment() {
  console.log('📋 验证测试环境...');
  
  // 检查必需的环境变量
  const requiredEnvVars = [
    'MSSQL_PRISMA_URL',
    'AZURE_SERVICEBUS_CONNECTION_STRING',
    'AZURE_STORAGE_CONNECTION_STRING',
  ];
  
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`缺少必需的环境变量: ${envVar}`);
    }
  }
  
  // 检查必需的工具
  try {
    execSync('func --version', { stdio: 'pipe' });
  } catch {
    throw new Error('Azure Functions Core Tools 未安装或不可用');
  }
  
  // 创建测试结果目录
  const testResultsDir = path.resolve(__dirname, '../../test-results');
  if (!fs.existsSync(testResultsDir)) {
    fs.mkdirSync(testResultsDir, { recursive: true });
  }
  
  console.log('✅ 环境验证完成');
}

/**
 * 🗄️ 数据库准备 - 初始化测试数据库
 */
async function setupDatabase() {
  console.log('🗄️ 准备测试数据库...');
  
  try {
    // 运行数据库迁移
    execSync('cd ../../apps/jcs-endpoint-nextjs && npx prisma migrate deploy', {
      stdio: 'pipe',
      env: { ...process.env, NODE_ENV: 'test' }
    });
    
    // 生成 Prisma 客户端
    execSync('cd ../../apps/jcs-endpoint-nextjs && npx prisma generate', {
      stdio: 'pipe'
    });
    
    console.log('✅ 数据库准备完成');
  } catch (error) {
    console.error('❌ 数据库准备失败:', error);
    throw error;
  }
}

/**
 * 🔧 服务健康检查 - 等待所有服务启动完成
 */
async function waitForServices() {
  console.log('🔧 等待服务启动...');
  
  const services = [
    { name: 'Next.js', url: 'http://localhost:3000', timeout: 180000 },
    { name: 'Azure Functions (Standard)', url: 'http://localhost:7072/api/health', timeout: 90000 },
    { name: 'Azure Functions (Long-running)', url: 'http://localhost:7071/api/health', timeout: 90000 },
  ];
  
  for (const service of services) {
    await waitForService(service.name, service.url, service.timeout);
  }
  
  console.log('✅ 所有服务已启动');
}

/**
 * 等待单个服务启动
 */
async function waitForService(name: string, url: string, timeout: number) {
  console.log(`⏳ 等待 ${name} 启动...`);
  
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    try {
      const response = await fetch(url);
      if (response.ok || response.status === 404) { // 404 也算服务已启动
        console.log(`✅ ${name} 已启动`);
        return;
      }
    } catch {
      // 服务还未启动，继续等待
    }
    
    await new Promise(resolve => setTimeout(resolve, 2000)); // 等待 2 秒
  }
  
  throw new Error(`${name} 启动超时 (${timeout}ms)`);
}

/**
 * 📊 测试数据准备 - 创建测试所需的基础数据
 */
async function prepareTestData() {
  console.log('📊 准备测试数据...');

  try {
    // 初始化 LOV 种子数据
    const { initializeLovSeedData } = await import('./lov-data.helper');
    await initializeLovSeedData();

    console.log('✅ 测试数据准备完成');
  } catch (error) {
    console.error('❌ 测试数据准备失败:', error);
    throw error;
  }
}

/**
 * 📈 性能监控初始化
 */
async function initializeMonitoring() {
  console.log('📈 初始化性能监控...');
  
  // 创建性能监控目录
  const monitoringDir = path.resolve(__dirname, '../../test-results/monitoring');
  if (!fs.existsSync(monitoringDir)) {
    fs.mkdirSync(monitoringDir, { recursive: true });
  }
  
  // 记录测试开始时间
  const testSession = {
    startTime: new Date().toISOString(),
    environment: 'test',
    services: ['next.js', 'azure-functions-standard', 'azure-functions-long-running'],
  };
  
  fs.writeFileSync(
    path.join(monitoringDir, 'test-session.json'),
    JSON.stringify(testSession, null, 2)
  );
  
  console.log('✅ 性能监控初始化完成');
}

/**
 * 加载测试环境变量
 *
 * 从 Next.js 应用的 .env.test.local 文件加载环境变量到当前进程
 */
async function loadEnvironmentVariables() {
  console.log('🔧 加载测试环境变量...');

  // 加载 Next.js 测试环境变量
  const nextjsEnvPath = path.resolve(__dirname, '../../../apps/jcs-endpoint-nextjs/.env.test.local');

  if (fs.existsSync(nextjsEnvPath)) {
    // 使用 dotenv 加载环境变量
    const result = dotenv.config({ path: nextjsEnvPath });

    if (result.error) {
      console.log(`⚠️ 加载环境变量时出现警告: ${result.error.message}`);
    } else {
      console.log('✅ 成功加载 Next.js 测试环境变量');
    }
  } else {
    console.log(`⚠️ 未找到 Next.js 环境变量文件: ${nextjsEnvPath}`);
  }

  // 设置 NODE_ENV 为 test（如果尚未设置）
  if (!process.env.NODE_ENV) {
    process.env.NODE_ENV = 'test';
    console.log('✅ 设置 NODE_ENV=test');
  }

  console.log('✅ 环境变量加载完成');
}

export default globalSetup;
