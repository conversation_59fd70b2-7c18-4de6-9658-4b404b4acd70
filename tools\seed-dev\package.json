{"name": "jcs-seed-dev", "version": "1.0.0", "description": "JCS 开发环境种子数据生成工具 - 一键生成2000+条真实日文业务数据", "main": "seed-data.ts", "scripts": {"postinstall": "prisma generate", "seed": "tsx seed-data.ts", "clean": "tsx cleanup.ts", "clean:stats": "tsx cleanup.ts stats", "reseed": "npm run clean && npm run seed", "dev": "tsx --watch seed-data.ts", "build": "tsc", "lint": "eslint . --ext .ts", "format": "prettier --write .", "prisma:generate": "prisma generate", "prisma:studio": "prisma studio"}, "keywords": ["jcs", "seed-data", "development", "database", "prisma"], "author": "WSST", "license": "UNLICENSED", "private": true, "dependencies": {"@prisma/client": "^4.14.0", "dayjs": "^1.11.13", "dotenv": "^17.2.1", "tsx": "^4.19.2", "typescript": "^5.6.3"}, "devDependencies": {"@types/node": "^22.9.0", "eslint": "^9.14.0", "prettier": "^3.3.3", "prisma": "^4.14.0"}, "engines": {"node": ">=18.0.0"}}