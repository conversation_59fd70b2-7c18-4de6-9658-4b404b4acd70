### **本地集成测试环境与Playwright实施终极指南**

本指南是为 `jcs-endpoint-monorepo` 项目建立本地端到端（E2E）集成测试的**唯一权威方案**。请遵循以下步骤，从零开始构建一个完整、可靠的测试环境。

#### **Part 1: 最终项目结构**

在我们开始之前，先看一下完成后我们的项目会是什么样子。这套结构是经过精心设计的，以确保测试代码的独立性和可维护性。

```plaintext
jcs-endpoint-monorepo/
├── apps/
│   ├── jcs-endpoint-nextjs/
│   │   └── .env.local          # [新增] 用于本地服务发现
│   ├── jcs-backend-services-standard/
│   └── jcs-backend-services-long-running/
├── scripts/
│   └── start-local-env.sh      # [新增] 一键启动所有本地服务的脚本
└── tests/                      # [新增] 存放所有测试的根目录
    └── integration/            # 集成测试工作区
        ├── specs/              # 存放所有测试用例 (*.spec.ts)
        │   └── task-management.spec.ts
        ├── support/            # 存放测试辅助模块
        │   └── auth.helper.ts  # (示例) 认证辅助模块
        ├── package.json        # 测试环境的依赖管理
        ├── playwright.config.ts # Playwright 的核心配置文件
        └── tsconfig.json       # TypeScript 配置文件
```

#### **Part 2: 环境搭建与依赖安装 (一次性)**

1.  **创建测试工作区**:
    在项目根目录 (`jcs-endpoint-monorepo/`) 下执行。
    ```bash
    # 创建完整的目录结构
    mkdir -p tests/integration/specs
    mkdir -p tests/integration/support
    ```

2.  **初始化测试项目**:
    进入 `tests/integration` 目录，并将其初始化为一个独立的npm项目。
    ```bash
    cd tests/integration
    npm init -y
    ```

3.  **安装Playwright及其依赖**:
    ```bash
    # 安装 Playwright 和 TypeScript
    npm install --save-dev @playwright/test typescript

    # 安装浏览器驱动 (Chromium, Firefox, WebKit)
    npx playwright install
    ```

4.  **安装所有应用的依赖 (关键步骤)**:
    回到项目根目录，确保所有三个核心应用都已安装依赖。**如果跳过此步，环境将无法启动。**
    ```bash
    cd ../..  # 回到根目录
    
    # 为每个应用安装依赖
    (cd apps/jcs-endpoint-nextjs && npm install)
    (cd apps/jcs-backend-services-standard && npm install)
    (cd apps/jcs-backend-services-long-running && npm install)
    ```

#### **Part 3: 编写配置文件与核心脚本**

现在，我们来创建运行环境所需的所有文件。请将以下内容完整复制到对应路径的文件中。

**1. `tests/integration/playwright.config.ts` (Playwright核心配置)**
```typescript
import { defineConfig, devices } from '@playwright/test';

/**
 * 唯一、最佳实践的Playwright配置文件。
 */
export default defineConfig({
  testDir: './specs',
  timeout: 30 * 1000,
  expect: {
    timeout: 5000,
  },
  fullyParallel: true,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    // 关键：设置Next.js应用的本地访问地址为基础URL
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],
});
```

**2. `apps/jcs-endpoint-nextjs/.env.local` (服务发现)**
```env
# 本地服务发现配置文件。此文件不应提交到Git。

# 标准Function App (5分钟超时) 的本地地址
NEXT_PUBLIC_API_STANDARD_BASE_URL=http://localhost:7072/api

# 长时运行Function App (30分钟超时) 的本地地址
NEXT_PUBLIC_API_LONG_RUNNING_BASE_URL=http://localhost:7071/api
```

**3. `scripts/start-local-env.sh` (一键启动脚本)**
```bash
#!/bin/bash

# 本地集成测试环境一键启动脚本 (最终版)
# 并行启动所有应用，并在退出时自动、优雅地关闭所有进程。

# 清理函数：当脚本退出时（例如按Ctrl+C），此函数将被调用
cleanup() {
    echo ""
    echo "🛑 Shutting down local environment..."
    # 使用 kill -- -$$ 来杀死所有由该脚本启动的子进程
    # 2>/dev/null 用于抑制当进程已不存在时可能出现的错误信息
    kill -- -$$ 2>/dev/null
    echo "✅ Environment shut down."
}

# 捕获退出(EXIT)、中断(INT)、终止(TERM)信号，并执行cleanup函数
trap cleanup EXIT INT TERM

echo "🚀 Starting local integration environment..."
echo "-----------------------------------------"

# 并行启动所有服务，并将其置于后台运行 (&)

# 启动标准Function App
(cd apps/jcs-backend-services-standard && func start --port 7072) &

# 启动长时运行Function App
(cd apps/jcs-backend-services-long-running && func start) &

# 启动Next.js应用
(cd apps/jcs-endpoint-nextjs && npm run dev) &

echo "-----------------------------------------"
echo "✅ All services started in the background."
echo "   - Next.js Frontend: http://localhost:3000"
echo "   - Standard Functions: http://localhost:7072"
echo "   - Long-Running Functions: http://localhost:7071"
echo ""
echo "✨ Your environment is ready for Playwright tests."
echo "Press Ctrl+C to shut down all services gracefully."
echo "-----------------------------------------"

# 等待所有后台进程结束。脚本会在此处暂停，直到用户按下Ctrl+C
wait
```
**赋予执行权限 (仅需一次)**:
```bash
chmod +x scripts/start-local-env.sh
```

#### **Part 4: 编写第一个集成测试用例**

这是我们将所有东西串联起来的地方。我们将编写一个测试用例，它将模拟数据库、处理认证，并与我们的多服务环境互动。

**`tests/integration/specs/task-management.spec.ts` (示例测试文件)**
```typescript
import { test, expect } from '@playwright/test';
// 数据库客户端的引用路径需要根据您的项目结构进行调整
import { PrismaClient } from '../../../apps/jcs-endpoint-nextjs/node_modules/.prisma/client';

const prisma = new PrismaClient();

/**
 * @fileoverview タスク管理機能に関するE2Eテスト。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */
test.describe('タスク管理機能のE2Eテスト', () => {

  /**
   * 各テストの実行前に、データベースをクリーンな状態にリセットする。
   * これにより、テストの独立性と再現性が保証される。
   */
  test.beforeEach(async ({ page }) => {
    // データベースから既存のタスクをすべて削除
    await prisma.task.deleteMany({});
    
    // === 認証の処理 (ベストプラクティス) ===
    // ここで `tests/support/auth.helper.ts` のような補助関数を呼び出し、
    // ログイン状態を示すセッションCookieを直接ブラウザに注入する。
    // これにより、UI経由でのログイン操作を完全にスキップでき、テストが高速化・安定化する。
    // 例: await loginAs(page, { role: 'admin' });
  });

  /**
    * 試験観点： ユーザーがUIを通じて新しいタスクを作成し、そのタスクがリストに表示されること。
    * 試験対象： タスク作成フォーム、タスク一覧表示ページ、および関連するAPIエンドポイント。
    * 試験手順：
    *   1. タスク作成ページにアクセスする。
    *   2. フォームに有効なデータを入力する。
    *   3. 「作成」ボタンをクリックする。
    * 確認項目：
    *   1. ページがタスク一覧にリダイレクトされること。
    *   2. 作成したタスク名が一覧に表示されること。
    *   3. データベースに新しいタスクのレコードが正しく保存されていること。
    */
  test('新しいタスクの作成が正常に完了すること', async ({ page }) => {
    // Arrange: テストの前提条件を準備
    const newTaskName = `自動テストタスク ${Date.now()}`;
    await page.goto('/tasks/new');

    // Act: テスト対象の操作を実行
    await page.getByLabel('タスク名').fill(newTaskName);
    await page.getByLabel('説明').fill('Playwrightによる自動E2Eテスト');
    await page.getByRole('button', { name: '作成' }).click();

    // Assert: 結果を検証
    // 1. UIの検証
    await expect(page).toHaveURL(/.*\/tasks/); // URLが正しいか
    await expect(page.locator(`text=${newTaskName}`)).toBeVisible(); // 新タスクが表示されているか

    // 2. データベースの検証 (究極の信頼性担保)
    const taskInDb = await prisma.task.findFirst({
      where: { name: newTaskName },
    });
    expect(taskInDb).not.toBeNull();
    expect(taskInDb?.description).toBe('Playwrightによる自動E2Eテスト');
  });
});
```

#### **Part 5: 最终执行工作流**

现在，您日常进行集成测试的流程被简化为以下几个简单步骤：

1.  **启动环境**: 打开一个终端，运行启动脚本。让它在前台运行。
    ```bash
    ./scripts/start-local-env.sh
    ```

2.  **运行测试**: 打开**第二个**终端，进入测试目录，然后运行Playwright。
    ```bash
    cd tests/integration
    npx playwright test
    ```

3.  **分析结果**: 测试完成后，在第二个终端中查看HTML报告。
    ```bash
    npx playwright show-report
    ```

4.  **关闭环境**: 当您完成测试后，回到第一个终端，按下 `Ctrl+C`。所有服务将自动停止。

这份指南为您提供了一套完整、健壮且符合最佳实践的本地集成测试解决方案。它解决了环境启动、服务发现和测试编写的所有核心问题。