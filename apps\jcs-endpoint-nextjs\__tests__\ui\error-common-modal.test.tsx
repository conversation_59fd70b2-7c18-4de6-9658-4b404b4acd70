/**
 * @jest-environment jsdom
 */

// Web API polyfills
Object.defineProperty(global, 'Response', {
  value: class Response {
    constructor(_body?: any, _init?: any) {}
    static json(data: any) { return new Response(JSON.stringify(data)); }
  }
});
Object.defineProperty(global, 'Request', {
  value: class Request {
    constructor(_input: any, _init?: any) {}
  }
});

// Flowbite Modalのモック
jest.mock("flowbite", () => ({
  Modal: jest.fn().mockImplementation(() => ({
    show: jest.fn(),
    hide: jest.fn(),
    destroy: jest.fn(),
  })),
}));

import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import ErrorCommonModal from "@/app/ui/ErrorCommonModal";

/**
 * @fileoverview Error Common Modal Component Tests
 * @description Tests for the error common modal component.
 * Verifies modal display, error/info message display, and text content validation.
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

describe("ErrorCommonModal", () => {
  const mockProps = {
    isOpen: true,
    title: "エラー",
    error: "エラーが発生しました。",
    onClose: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  /**
   * 試験観点：モーダル非表示時の動作確認
   * 試験対象：ErrorCommonModal コンポーネントの表示制御
   * 試験手順：
   * 1. isOpen=falseでコンポーネントをレンダリング
   * 確認項目：
   * - モーダルが表示されないこと
   */
  it("正常系: モーダル非表示時は何も表示されない", () => {
    render(<ErrorCommonModal {...mockProps} isOpen={false} />);

    expect(screen.queryByText("エラー")).not.toBeInTheDocument();
    expect(screen.queryByText("エラーが発生しました。")).not.toBeInTheDocument();
  });

  /**
   * 試験観点：エラーメッセージ表示時のUI要素確認
   * 試験対象：ErrorCommonModal コンポーネントのエラー表示機能
   * 試験手順：
   * 1. エラーメッセージを指定してレンダリング
   * 確認項目：
   * - タイトルが表示されること
   * - エラーメッセージが表示されること
   * - 閉じるボタンが表示されること
   */
  it("正常系: エラーメッセージ表示時のUI要素", () => {
    render(<ErrorCommonModal {...mockProps} />);

    expect(screen.getByText("エラー")).toBeInTheDocument();
    expect(screen.getByText("エラーが発生しました。")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /close/i })).toBeInTheDocument();
  });

  /**
   * 試験観点：情報メッセージ表示時のUI要素確認
   * 試験対象：ErrorCommonModal コンポーネントの情報表示機能
   * 試験手順：
   * 1. 情報メッセージを指定してレンダリング
   * 確認項目：
   * - タイトルが表示されること
   * - 情報メッセージが表示されること
   * - 閉じるボタンが表示されること
   */
  it("正常系: 情報メッセージ表示時のUI要素", () => {
    const infoProps = {
      isOpen: true,
      title: "情報",
      message: "処理が完了しました。",
      onClose: jest.fn(),
    };

    render(<ErrorCommonModal {...infoProps} />);

    expect(screen.getByText("情報")).toBeInTheDocument();
    expect(screen.getByText("処理が完了しました。")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /close/i })).toBeInTheDocument();
  });

  /**
   * 試験観点：閉じるボタンクリック時の動作確認
   * 試験対象：ErrorCommonModal コンポーネントの閉じる機能
   * 試験手順：
   * 1. 閉じるボタンをクリック
   * 確認項目：
   * - onCloseコールバックが呼び出されること
   */
  it("正常系: 閉じるボタンクリック時の動作", async () => {
    render(<ErrorCommonModal {...mockProps} />);

    await userEvent.click(screen.getByRole("button", { name: /close/i }));

    expect(mockProps.onClose).toHaveBeenCalledTimes(1);
  });

  /**
   * 試験観点：必須テキストの表示確認
   * 試験対象：ErrorCommonModal コンポーネントの必須テキスト表示
   * 試験手順：
   * 1. コンポーネントをレンダリング
   * 2. 必須テキストがすべて表示されていることを確認
   * 確認項目：
   * - 必須テキストがすべて表示されていること
   */
  it("必須テキストがすべて表示されること", () => {
    render(<ErrorCommonModal {...mockProps} />);

    // 必須テキスト
    const requiredTexts = [
      "エラー",
      "エラーが発生しました。",
    ];

    const bodyText = document.body.textContent || "";
    
    // 必須テキストがすべて存在することを確認
    requiredTexts.forEach(requiredText => {
      expect(bodyText).toContain(requiredText);
    });
  });

  /**
   * 試験観点：許可されたテキストのみ表示確認
   * 試験対象：ErrorCommonModal コンポーネントのテキスト表示制御
   * 試験手順：
   * 1. コンポーネントをレンダリング
   * 2. 許可されたテキストのみが表示されていることを確認
   * 確認項目：
   * - 許可されていないテキストが表示されていないこと
   */
  it("許可されたテキストのみ表示されること", () => {
    render(<ErrorCommonModal {...mockProps} />);

    // 許可されたテキストの完全リスト
    const allowedTexts = [
      "エラー",
      "エラーが発生しました。",
      "Close",
      "modal",
      "閉じる",
      "が発生しました。",
    ];

    const bodyText = document.body.textContent || "";
    
    // 許可されたテキストをすべて除去
    let remainingText = bodyText;
    allowedTexts.forEach(allowedText => {
      remainingText = remainingText.replace(new RegExp(allowedText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), '');
    });
    
    // 残ったテキストから空白文字を除去
    const unauthorizedText = remainingText.replace(/[\s\n\r\t]+/g, '').trim();
    
    // 許可されていないテキストがある場合はテストを失敗させる
    if (unauthorizedText.length > 0) {
      expect(`許可されていないテキストが検出されました: "${unauthorizedText}"`).toBe('');
    }
  });
});
