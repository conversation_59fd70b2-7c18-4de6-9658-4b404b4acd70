/**
 * @fileoverview サーバ一覧画面のメインページコンポーネント
 * @description
 * サーバ一覧画面は、ユーザーが契約しているサーバの一覧を表示し、各サーバに対してバックグラウンドタスク
 * （操作ログのエクスポート、管理項目定義のインポート・エクスポート）を実行できる画面である。
 *
 * 主な機能：
 * 1. サーバ一覧の表示：契約IDに紐づくサーバ情報を取得し、サーバ名・種別・管理画面URLを一覧表示
 * 2. フィルタリング：サーバ名、種別、管理画面URLによる検索機能
 * 3. ソート機能：サーバ名、種別、管理画面URLによる昇順・降順ソート
 * 4. ページネーション：10件、30件、50件の表示件数選択とページ切り替え
 * 5. タスクメニュー：サーバ種別と契約プランに応じたタスク実行メニューの表示制御
 * 6. 更新機能：最新のサーバ情報とタスク状況の再取得
 *
 * 表示制御：
 * - 操作ログエクスポート：基本契約プランコードがSTANDARD/LIGHT_Bの場合のみ表示
 * - タスクメニュー：サーバ種別がGENERAL_MANAGER/RELAY_MANAGERの場合のみ表示
 * - 管理画面リンク：各サーバの管理画面URLへのハイパーリンク
 *
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { randomUUID } from "crypto";
import { ServerDataServers } from "@/app/lib/data/servers";
import { ServerDataLov } from "@/app/lib/data/lov";
import PageSize from "@/app/ui/page-size";
import Pagination from "@/app/ui/pagination";
import RefreshToken from "@/app/ui/refreshToken";
import Search from "@/app/ui/search";
import Table from "@/app/ui/servers/table";
import { TableSkeleton } from "@/app/ui/skeletons";
import { Metadata } from "next";
import { Suspense } from "react";
import { fetchUserCanExportOplog } from "@/app/lib/data/servers";
import { LOV_CODE_OPLOG_EXPORT_MAX_DAYS } from "@/app/lib/definitions";

export const metadata: Metadata = {
  title: "サーバ一覧", // ページのタイトルです。
};

/**
 * サーバ一覧画面のメインページコンポーネント
 *
 * URLクエリパラメータに基づいてサーバ一覧の表示状態を制御し、
 * フィルタリング、ページネーション、ソート機能を提供する。
 *
 * @param searchParams URLクエリパラメータ
 * @param searchParams.filter フィルタリングキーワード（サーバ名、種別、管理画面URLで検索）
 * @param searchParams.page 現在のページ番号（デフォルト: 1）
 * @param searchParams.size 1ページあたりの表示件数（デフォルト: 10）
 * @param searchParams.sort ソート対象フィールド（name/type/url、デフォルト: name）
 * @param searchParams.order ソート順（asc/desc、デフォルト: asc）
 * @returns サーバ一覧画面のJSX要素
 */
export default async function Page({
  searchParams,
}: {
  searchParams?: {
    filter?: string;
    page?: string;
    size?: string;
    sort?: "name" | "type" | "url";
    order?: "asc" | "desc";
  };
}) {
  // ページ初期化処理：URLパラメータ未指定時の初期条件設定
  // フィルター空、ページ1、行数10、ソートキー「サーバ名の物理名」、昇順
  const filter = searchParams?.filter || "";
  const size = Number(searchParams?.size) || 10;
  const currentPage = Number(searchParams?.page) || 1;
  const sort = searchParams?.sort || "name";
  const order = searchParams?.order || "asc";

  // データ取得と編集処理：契約IDに紐づくサーバ情報の全件取得、総ページ数計算
  const totalPages =
    (await ServerDataServers.fetchServersPages(filter, size, !searchParams?.page)) ||
    0;

  // セッションリフレッシュ用の一意識別子を生成し、RefreshTokenの再マウントを制御
  const refresh = randomUUID();

  // ユーザーの基本契約プランコードによる操作ログエクスポート可否判定
  // STANDARD・LIGHT_Bの場合は表示、LIGHT_Aの場合は非表示
  const canExportOplog = await fetchUserCanExportOplog();

  // 操作ログエクスポート期間の最大日数設定値を取得
  const maxExportDaysLov = await ServerDataLov.fetchLov(LOV_CODE_OPLOG_EXPORT_MAX_DAYS);
  const maxExportDaysSpan = Number(maxExportDaysLov?.value) || 30;

  return (
    <div className="p-4 h-full flex flex-col">
      <div className="flex-column flex flex-wrap items-center justify-between rounded-t-xl border border-gray-200 bg-gray-100 p-2 md:flex-row">
        <Search />
        {totalPages > 0 && (
          <div className="flex items-center">
            <Pagination totalPages={totalPages} />
            <PageSize />
          </div>
        )}
      </div>
      <div className="relative overflow-x-auto overflow-y-auto shadow-md rounded-b-lg">
        <Suspense
          key={filter + currentPage + size + sort + order}
          fallback={<TableSkeleton />}
        >
          {/* @ts-expect-error Server Component */}
          <Table
            filter={filter}
            page={currentPage}
            size={size}
            sort={sort}
            order={order}
            canExportOplog={canExportOplog}
            maxExportDaysSpan={maxExportDaysSpan}
          />
        </Suspense>
      </div>
      <RefreshToken key={refresh} />
    </div>
  );
}
