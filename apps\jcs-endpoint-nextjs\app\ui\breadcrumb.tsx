/**
 * @file breadcrumb.tsx
 * @description 
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { navLinks } from "../lib/definitions";

const findNavLinks = (
  targetPath: string,
): { firstLevel: string; secondLevel: string } | null => {
  for (const link of navLinks) {
    const matchingSubLink = link.subs.find((sub) => sub.href === targetPath);

    if (matchingSubLink) {
      return { firstLevel: link.name, secondLevel: matchingSubLink.name };
    }
  }

  return null;
};

// パンくずリストコンポーネント
export default function Breadcrumb() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { replace } = useRouter();
  const [nav, setNav] = useState<{
    firstLevel: string;
    secondLevel: string;
  } | null>(null);

  useEffect(() => {
    setNav(findNavLinks(pathname));
  }, [pathname]);

  function handleRefresh(): void {
    const params = new URLSearchParams(searchParams);

    // params.delete("filter");
    params.delete("page");
    params.delete("size");
    params.delete("sort");
    params.delete("order");
    replace(`${pathname}?${params.toString()}`);
  }

  return (
    <nav
      className="shrink-0 h-10 border-b border-b-[#424242] bg-gray-600 bg-gradient-header px-5 text-sm font-medium text-gray-300 shadow-inner drop-shadow"
      aria-label="Breadcrumb"
    >
      <div className="inline-flex h-full w-full items-center justify-between">
        <ol className="inline-flex items-center">
          <li className="inline-flex items-center">
            <span className="ms-1 text-sm font-medium text-gray-300">
              {nav && nav.firstLevel}
            </span>
          </li>
          <li aria-current="page">
            <div className="ml-2 flex items-center">
              <span>-</span>
              <span className="ms-1 text-sm font-medium text-gray-300 md:ms-2">
                {nav && nav.secondLevel}
              </span>
            </div>
          </li>
        </ol>
        <img
          src="/refresh_up.png"
          className="h-6 w-6 cursor-pointer hover:opacity-80 transition duration-300"
          alt="medias"
          tabIndex={0}
          onClick={(e) => handleRefresh()}
        />
      </div>
    </nav>
  );
}
