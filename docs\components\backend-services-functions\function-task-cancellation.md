# 组件：任务中止函数 (Task Cancellation Function)

## 1. 概要 (Overview)

### 1.1. 目的 (Purpose)
本Azure Function (`TaskCancellationFunc`) 旨在处理从门户系统发起的后台任务中止请求。它通过监听Azure Service Bus的`TaskControlQueue`队列中的消息来触发，并负责根据任务的当前状态更新数据库中`Task`表的相关记录，以反映中止操作的结果。

### 1.2. 范围 (Scope)
本文档详细描述`TaskCancellationFunc`的技术设计，包括其触发机制、核心处理逻辑、与数据库的交互、错误处理机制以及相关的配置项。

### 1.3. 组件类型 (Component Type)
*   Azure Function (Node.js/TypeScript 运行时)
*   触发器: Azure Service Bus Queue Trigger (监听 `TaskControlQueue`)

### 1.4. 名词定义 (Glossary References)
*   **TaskControlQueue**: Azure Service Bus队列，用于接收包含待中止任务ID的消息。
*   其他相关术语请参考项目核心术语表 `docs/definitions/glossary.md`。
*   本文档中所有用户可见的错误和提示信息，均引用自项目核心错误消息定义文档 `docs/definitions/error-messages.md`。
*   本文档中所有环境变量的定义，均参考项目核心环境变量指南 `docs/guides/environment-variables.md`。
*   任务状态码常量（如`TASK_STATUS_PENDING_CANCELLATION_CODE`）定义在 `app/lib/definitions.ts`。

## 2. 功能规格 (Functional Specifications)

### 2.1. 主要流程 (Main Process Flow)

```mermaid
graph TD
    A["TaskControlQueue接收到中止消息<br/>(包含taskId)"] --> B{TaskCancellationFunc被触发};
    B --> C[1.解析消息, 获取taskId];
    C --> D["2.查询Task表获取任务当前状态<br/>(使用taskId)"];
    D --> E{任务当前状态是?};
    E -- "PENDING_CANCELLATION" --> F["2.1 更新Task状态为CANCELLED<br/>更新resultDetails为EMET0004"];
    E -- "RUNBOOK_SUBMITTED 或<br/>RUNBOOK_PROCESSING" --> G["2.2 更新Task的resultDetails为EMET0011<br/>(状态不变更)"];
    E -- "COMPLETED_SUCCESS,<br/>COMPLETED_ERROR, 或<br/>CANCELLED" --> H["2.3 不做任何处理 (幂等)"];
    E -- "QUEUED (理论上较少见)" --> I[2.4 更新Task状态为CANCELLED<br/>更新resultDetails为EMET0004];
    E -- "其他/未知状态" --> J[2.5 记录警告/错误, 不做处理或标记错误];
    F --> K[3.记录成功处理日志];
    G --> K;
    H --> K;
    I --> K;
    J --> Z["结束处理 (含错误记录)"];
    K --> Z;
```
**图 2.1: TaskCancellationFunc 主要处理流程图**

### 2.2. 功能点描述 (Functional Points)
1.  **消息触发**: 函数由`TaskControlQueue`中的新消息触发。消息体包含需要中止的任务的ID (`taskId`)。
2.  **任务状态校验与更新**:
    *   根据`taskId`从数据库`Task`表查询该任务的当前`status`和`resultDetails`。
    *   如果任务的当前`status`为 `TASK_STATUS_PENDING_CANCELLATION_CODE` ("PENDING_CANCELLATION"):
        *   将`Task.status`更新为 `TASK_STATUS_CANCELLED_CODE` ("CANCELLED")。
        *   将`Task.resultDetails`更新为消息键 `EMET0004` 对应的日文消息 ("ユーザーによってタスクが中止されました。")。
        *   更新`Task.endedAt`为当前时间。
    *   如果任务的当前`status`为 `TASK_STATUS_RUNBOOK_SUBMITTED_CODE` ("RUNBOOK_SUBMITTED") 或 `TASK_STATUS_RUNBOOK_PROCESSING_CODE` ("RUNBOOK_PROCESSING"):
        *   **不修改**`Task.status`。
        *   将`Task.resultDetails`更新为消息键 `EMET0011` 对应的日文消息 ("タスクの実行がすでに開始したため中止できませんでした。")。
    *   如果任务的当前`status`为 `TASK_STATUS_COMPLETED_SUCCESS_CODE` ("COMPLETED_SUCCESS"), `TASK_STATUS_COMPLETED_ERROR_CODE` ("COMPLETED_ERROR"), 或 `TASK_STATUS_CANCELLED_CODE` ("CANCELLED"):
        *   不执行任何数据库更新操作（幂等处理）。记录相应的参考日志。
    *   如果任务的当前`status`为 `TASK_STATUS_QUEUED_CODE` ("QUEUED") (此情况理论上较少，因为门户端的`ServerAction.requestTaskCancellation`通常会先将状态更新为`PENDING_CANCELLATION`):
        *   将`Task.status`更新为 `TASK_STATUS_CANCELLED_CODE` ("CANCELLED")。
        *   将`Task.resultDetails`更新为消息键 `EMET0004` 对应的日文消息。
        *   更新`Task.endedAt`为当前时间。
    *   对于其他未预期的任务状态，记录警告或错误日志，通常不进行状态变更，或将其标记为特定错误状态（如果业务需要）。
3.  **日志记录**: 对所有接收的消息、执行的数据库操作、遇到的错误以及最终处理结果进行详细日志记录。

### 2.3. 业务规则 (Business Rules)
*   只有处于`PENDING_CANCELLATION`或`QUEUED`状态的任务才能被真正“中止”（即状态变更为`CANCELLED`）。
*   对于已经开始执行（`RUNBOOK_SUBMITTED`, `RUNBOOK_PROCESSING`）的任务，中止请求仅会更新其`resultDetails`以提示用户，但不会改变其运行状态。这类任务将继续执行直至`COMPLETED_SUCCESS`或`COMPLETED_ERROR`。
*   对已完成或已中止的任务的重复中止请求，将被幂等处理，不产生副作用。

### 2.4. 前提条件 (Preconditions)
*   Azure Service Bus (`TaskControlQueue`) 正常运行。
*   Azure SQL Database (`Task`表) 正常运行且可访问。
*   相关的`LOV`定义（特别是`TASK_STATUS`）和错误消息定义（`EMET0004`, `EMET0011`）已在系统中正确配置。

### 2.5. 制约事项 (Constraints)
*   本函数不负责实际停止正在Azure Automation中运行的Runbook作业。该职责由[`RunbookProcessorFunc`](./function-runbook-processor.md)（在其检测到超时或特定错误时）或[`TaskExecuteTimeoutFunc`](./function-task-execute-timeout.md)等承担。本函数仅处理数据库中`Task`记录的状态和备注。

### 2.6. 注意事项 (Notes)
*   本函数的设计应保证幂等性，即多次处理同一个中止消息（例如由于Service Bus的至少一次传递保证）不会产生意外的副作用。
*   错误处理必须健壮，确保即使发生非预期错误，也不会导致消息丢失或Function App崩溃。无法处理的消息应通过Service Bus的死信队列(DLQ)机制进行管理。

---

## 3. 技术设计与实现细节 (Technical Design & Implementation)

### 3.1. 技术栈与依赖 (Technology Stack & Dependencies)
*   **运行时**: Azure Functions (Node.js/TypeScript)。
*   **触发器**: Azure Service Bus Queue Trigger。
    *   队列名称: 由环境变量 `SERVICE_BUS_TASK_CONTROL_QUEUE_NAME` 指定。
*   **数据库交互**: Prisma ORM (通过 `app/lib/data.ts` 或直接使用 `app/lib/prisma.ts`)。
*   **消息队列SDK**: `@azure/service-bus`。
*   **日志**: `app/lib/logger.ts`。
*   **核心定义与常量**: `app/lib/definitions.ts` (包含`TASK_STATUS`常量、消息键等)。
*   **数据模型**: `docs/data-models/task.md` (Task表结构)。

### 3.2. 详细界面元素定义 (Detailed UI Element Definitions)
*   本组件为后端Azure Function，无直接用户界面。其执行结果会间接影响“任务列表”页面的显示。

### 3.3. 详细事件处理逻辑 (Detailed Event Handling)
*   本组件由Service Bus消息触发，其核心处理逻辑见3.6节。

### 3.4. 数据结构与API/Server Action交互 (Data Structures and API/Server Action Interaction)

#### 3.4.1. 输入消息结构 (from `TaskControlQueue`)
消息体为一个JSON对象，预期包含以下字段：
```typescript
// 示例消息结构
{
  "taskId": "cuid-or-uuid-of-the-task-to-cancel" 
  // 可能还包含其他辅助信息，如发起取消请求的用户ID (用于审计)，但核心是taskId
}
```

#### 3.4.2. 与数据库的交互 (`Task` 表)
详见3.5节。

#### 3.4.3. 序列图 (Mermaid `sequenceDiagram`)

```mermaid
sequenceDiagram
    participant SBControlQ as "Azure Service Bus (TaskControlQueue)"
    participant FuncApp as "TaskCancellationFunc (Azure Function)"
    participant Database as "Azure SQL Database (Task表)"

    SBControlQ->>FuncApp: 消息: { taskId: "task123" }
    activate FuncApp
    FuncApp->>FuncApp: 1. 解析消息，获取 taskId
    FuncApp->>Database: 2. (内部通过app/lib/data) Prisma: task.findUnique({ where: { id: "task123" } })
    activate Database
    Database-->>FuncApp: Task对象 (e.g., status: 'PENDING_CANCELLATION')
    deactivate Database

    alt 任务状态为 PENDING_CANCELLATION
        FuncApp->>Database: (内部通过app/lib/data) Prisma: task.update(...)<br/>data: { status: 'CANCELLED', resultDetails: "EMET0004", endedAt: now() }
        activate Database
        Database-->>FuncApp: 更新成功
        deactivate Database
    else 任务状态为 RUNBOOK_SUBMITTED 或 RUNBOOK_PROCESSING
        FuncApp->>Database: (内部通过app/lib/data) Prisma: task.update(...)<br/>data: { resultDetails: "EMET0011" }
        activate Database
        Database-->>FuncApp: 更新成功
        deactivate Database
    else 任务已结束或状态为 QUEUED
        FuncApp->>FuncApp: (内部逻辑) 进一步判断并可能更新DB或仅记录日志
        Note right of FuncApp: 若状态为QUEUED, 更新status为CANCELLED, resultDetails为EMET0004<br/>若已结束, 则仅记录日志 (幂等处理)
    else 其他/未知状态
        FuncApp->>FuncApp: (内部逻辑) 记录警告/错误
    end
    FuncApp->>FuncApp: 3. 记录处理完成日志
    deactivate FuncApp
```

### 3.5. 数据库设计与访问详情 (Database Design and Access Details)

#### 3.5.1. 相关表引用
*   **`Task` 表**: 主要目标表，用于读取任务当前状态并更新其`status`, `resultDetails`, `endedAt`字段。

#### 3.5.2. 主要数据查询/变更逻辑

1.  **查询任务当前状态**:
    *   **目的**: 获取指定`taskId`的任务的完整记录，特别是`status`字段。
    *   **Prisma 操作 (通过 `ServerData.getTaskById` 或直接)**:
        ```typescript
        const task = await prisma.task.findUnique({
          where: { id: message.taskId },
        });
        ```
2.  **更新任务状态和结果详情**:
    *   **目的**: 根据业务规则（见2.2节）修改任务的`status`, `resultDetails`, 和 `endedAt`。
    *   **Prisma 操作 (示例，当状态为PENDING_CANCELLATION时)**:
        ```typescript
        const updatedTask = await prisma.task.update({
          where: { id: message.taskId },
          data: {
            status: TASK_STATUS_CANCELLED_CODE, // 来自 app/lib/definitions.ts
            resultDetails: getErrorMessageByKey(PORTAL_ERROR_MESSAGES_KEYS.EMET0004), // 获取日文消息文本
            endedAt: new Date(),
          },
        });
        ```
    *   **Prisma 操作 (示例，当状态为RUNBOOK_SUBMITTED时)**:
        ```typescript
        const updatedTask = await prisma.task.update({
          where: { id: message.taskId },
          data: {
            resultDetails: getErrorMessageByKey(PORTAL_ERROR_MESSAGES_KEYS.EMET0011),
            // status 和 endedAt 不变
          },
        });
        ```
*   **事务管理**: 每个消息的处理（查询后更新）应被视为一个独立的逻辑单元。如果`getTaskById`和后续的`update`需要在原子操作中完成以避免竞态条件（尽管对于此特定Function的逻辑，这种风险较低，因为`PENDING_CANCELLATION`状态本身就是一种锁），可以使用`prisma.$transaction`。但通常情况下，分离的读和写操作，配合Service Bus的重试机制，已经足够健壮。

### 3.6. 关键后端逻辑/算法 (Key Backend Logic/Algorithms)

`TaskCancellationFunc` 的核心逻辑是一个基于从消息中获取的`taskId`的任务当前状态的条件判断（switch-case 或 if-else-if 结构）：

1.  **从Service Bus消息中提取`taskId`。**
    *   如果消息格式无效或`taskId`缺失，记录错误，将消息发送到DLQ（如果Azure Functions主机配置为自动处理无效消息）或手动处理。
2.  **调用`ServerData.getTaskById(taskId)`获取任务记录。**
    *   如果任务未找到 (返回`null`)，记录错误日志（可能意味着任务已被删除或ID错误），安全结束处理（避免消息无限重试）。
    *   如果数据库查询失败，记录错误，允许Service Bus重试机制处理（或最终进入DLQ）。
3.  **根据`task.status`执行条件分支**:
    *   **`PENDING_CANCELLATION`**:
        *   构造更新数据: `{ status: TASK_STATUS_CANCELLED_CODE, resultDetails: messageTextForEmet0004, endedAt: new Date() }`。
        *   调用`prisma.task.update()`。
        *   记录成功日志。
    *   **`RUNBOOK_SUBMITTED` 或 `RUNBOOK_PROCESSING`**:
        *   构造更新数据: `{ resultDetails: messageTextForEmet0011 }`。
        *   调用`prisma.task.update()`。
        *   记录成功日志。
    *   **`COMPLETED_SUCCESS`, `COMPLETED_ERROR`, `CANCELLED`**:
        *   记录参考日志（表明是幂等处理或无需操作）。
    *   **`QUEUED`**: (与 `PENDING_CANCELLATION` 类似处理，但应记录此路径被触发的情况，因其非典型)
        *   构造更新数据: `{ status: TASK_STATUS_CANCELLED_CODE, resultDetails: messageTextForEmet0004, endedAt: new Date() }`。
        *   调用`prisma.task.update()`。
        *   记录成功日志。
    *   **Default (其他任何意外状态)**:
        *   记录警告或错误日志，包含意外的`taskId`和`status`。
        *   不进行数据库更新，以避免破坏数据。
4.  **全局错误处理**: Function App的入口点应包含`try-catch`块，捕获任何未预期的异常。
    *   记录详细错误堆栈。
    *   如果错误是可重试的（例如临时网络问题），允许Azure Functions的内置重试或Service Bus的重试机制介入。
    *   如果错误是持久性的（例如数据校验失败、代码逻辑错误），则应确保消息最终能进入DLQ，避免无限循环。

### 3.7. 错误处理详情 (Detailed Error Handling)

| #  | 错误场景描述 (中文) | 触发位置 | 引用的消息ID/消息键 (用户可见部分) | 系统内部处理及日志记录建议 (中文) | 日志级别 |
|----|-----------------|----------|------------------------------------|---------------------------------|-----------|
| 1  | Service Bus消息格式无效或`taskId`缺失 | Function触发，消息解析阶段 | (无用户可见消息，内部错误) | 记录错误，包含原始消息内容。如果可能，将消息移至DLQ。 | ERROR |
| 2  | 根据`taskId`未在数据库中找到对应任务 | 调用`ServerData.getTaskById`后 | (无用户可见消息，内部错误) | 记录错误，包含未找到的`taskId`。安全结束，避免消息重试死循环。 | ERROR |
| 3  | 数据库查询/更新操作失败 (通用) | Prisma调用时 (`findUnique`, `update`) | (无用户可见消息，内部错误，但其结果可能间接导致任务列表显示`EMET0007`) | 记录详细的数据库错误信息和堆栈。允许Service Bus/Function的重试。若持续失败，消息应进入DLQ。 | ERROR |
| 4  | 遇到未预期的`Task.status`值 | 状态判断逻辑的`default`分支 | (无用户可见消息，内部错误) | 记录警告或错误，包含`taskId`和意外的`status`值。 | WARN/ERROR |
| 5  | Function执行超时 (超出`FUNCTION_TIMEOUT_SECONDS`) | Azure Functions运行时 | (无直接用户可见消息，但可能导致`TaskControlQueue`消息进入DLQ，进而触发`TaskCancellationTimeoutFunc`，该Function可能会更新任务状态为错误并记录`EMET0006`) | Azure平台记录超时。消息会进入`TaskControlQueue`的DLQ。 | ERROR |

### 3.8. 配置项 (Configuration)

#### 3.8.1. 值列表 (LOV) 定义 (源自 `docs/definitions/lov-definitions.md`)
*   `TASK_STATUS` (父级代码): 本函数会使用其下定义的内部状态码常量。
    *   `TASK_STATUS_PENDING_CANCELLATION_CODE`
    *   `TASK_STATUS_CANCELLED_CODE`
    *   `TASK_STATUS_RUNBOOK_SUBMITTED_CODE`
    *   `TASK_STATUS_RUNBOOK_PROCESSING_CODE`
    *   `TASK_STATUS_COMPLETED_SUCCESS_CODE`
    *   `TASK_STATUS_COMPLETED_ERROR_CODE`
    *   `TASK_STATUS_QUEUED_CODE`

#### 3.8.2. 环境变量 (源自 `docs/guides/environment-variables.md`)
*   `SERVICE_BUS_TASK_CONTROL_QUEUE_NAME`: 指定本Function监听的Service Bus队列名称。
*   `MSSQL_PRISMA_URL`: Prisma ORM连接数据库所需的连接字符串。
*   `LOG_LEVEL`: 控制日志输出级别。
*   `FUNCTION_TIMEOUT_SECONDS`: (若在`host.json`中通过此配置) Azure Function的执行超时时间。

#### 3.8.3. 错误与提示消息 (源自 `docs/definitions/error-messages.md`)
本函数内部主要使用以下消息键来更新`Task.resultDetails`，这些消息最终会在任务列表页面对用户可见：
*   `EMET0004`: "ユーザーによってタスクが中止されました。"
*   `EMET0011`: "タスクの実行がすでに開始したため中止できませんでした。"

### 3.9. 注意事项与其他 (Notes/Miscellaneous)
*   **幂等性**: 函数设计需要考虑消息可能被重复投递的情况，确保重复处理不会导致数据状态错误。例如，如果一个已成功更新为`CANCELLED`的任务再次收到中止消息，函数应能识别并跳过重复更新。当前的逻辑（检查`task.status`）已部分满足此要求。
*   **死信队列 (DLQ) 处理**: 需要有相应的[`TaskCancellationTimeoutFunc`](./function-task-cancellation-timeout.md)来监听 `TaskControlQueue` 的DLQ，以处理本函数因执行超时或其他持久性错误而无法成功消费的消息。[`TaskCancellationTimeoutFunc`](./function-task-cancellation-timeout.md)的职责主要是记录错误，并可能尝试将相关任务标记为一个特定的错误状态，以便人工介入。
*   **依赖服务可用性**: 本函数强依赖Azure Service Bus和Azure SQL Database的可用性。
*   **日志的重要性**: 清晰且包含上下文（如`taskId`）的日志对于问题排查至关重要。
