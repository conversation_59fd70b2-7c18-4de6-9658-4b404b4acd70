{"name": "jcs-endpoint-monorepo", "version": "1.0.0", "private": true, "scripts": {"format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md,ps1}\"", "docs:dev": "cd docs && mkdocs serve", "generate-test-excel": "node scripts/generate-test-excel.js", "create-test-template": "node scripts/create-test-template.js", "test-excel-help": "node scripts/example-usage.js", "analyze-customer-template": "node scripts/analyze-customer-template.js", "create-customer-template": "node scripts/create-customer-template.js", "generate-customer-report": "node scripts/generate-customer-test-report.js"}, "devDependencies": {"prettier": "^3.0.0", "exceljs": "^4.4.0"}}