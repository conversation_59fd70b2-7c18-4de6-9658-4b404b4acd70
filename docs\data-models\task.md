# 数据模型: 任务 (Task)

*   **表名 (逻辑名)**: `Task`
*   **物理表名 (Prisma Model)**: `Task` (参考项目根目录下 `prisma/schema.prisma` 文件获取权威定义)
*   **对应UI界面**: 任务列表 (タスク一覧)
*   **主要用途**: 持久化存储由门户用户界面发起的后台任务的请求详情、内部执行状态、最终结果及相关元数据。是整个后台任务生命周期跟踪和管理的核心。

## 1. 字段定义

| 字段名 (Prisma Model) | 数据类型 (SQL参考) | PK | FK | UQ | Nullable | Default | 中文描述 | 日文名称 (界面参考) |
|--------------------|--------------|----|----|----|----------|---------|------|-------------|
| `id` | `String` | ● | - | ● | - | - | 主键，任务的唯一标识符，采用UUID格式。**此ID由门户后端 (`createTaskAction`) 在任务创建时使用UUID库生成**，并将作为TaskID在系统各组件间传递，用于关联和追踪，同时也将作为提交给Azure Automation的作业ID。 | (内部ID) |
| `taskName` | `String` | - | - | - | Yes | - | **任务描述性名称**。一个易于人类识别的名称，由门户后端 (`createTaskAction`) 按规则（例如 `{Server.name}-{TaskTypeLov.name}-{YYYYMMDDHHmmss}`）自动生成，主要用于在日志或管理界面中快速识别任务。 | タスク名 |
| `taskType` | `String` | - | - | - | - | - | **任务类型代码**。标识任务的具体业务操作类型，例如："TASK_TYPE.OPLOG_EXPORT", "TASK_TYPE.MGMT_ITEM_IMPORT", "TASK_TYPE.MGMT_ITEM_EXPORT"。其值对应值列表 (LOV) 中 `parentCode='TASK_TYPE'` 定义的 `code`。 | タスク種別 |
| `status` | `String` | - | - | - | - | - | **任务内部状态码**。表示任务在其生命周期中所处的当前内部处理阶段。核心内部状态码包括："QUEUED", "PENDING_CANCELLATION", "RUNBOOK_SUBMITTED", "RUNBOOK_PROCESSING", "COMPLETED_SUCCESS", "COMPLETED_ERROR", "CANCELLED"。这些内部状态码会通过值列表 (LOV) 中 `parentCode='TASK_STATUS'` 的定义映射为用户界面上可见的日文外部显示状态。 | ステータス |
| `submittedAt` | `DateTime` | - | - | - | - | `now()` | **任务提交时间**。门户后端接收到用户请求并在数据库中成功创建此任务记录的时间戳。 | 受付日時 (通常不在列表直接显示，但可用于排序或详情) |
| `startedAt` | `DateTime` | - | - | - | Yes | - | **后台作业开始时间**。表示关联的Azure Automation作业被成功提交到Azure Automation平台并获取到作业ID的时间点。 | 開始日時 |
| `endedAt` | `DateTime` | - | - | - | Yes | - | **任务结束时间**。任务达到其最终状态（如`COMPLETED_SUCCESS`, `COMPLETED_ERROR`, `CANCELLED`）的时间点。 | 終了日時 |
| `updatedAt` | `DateTime` | - | - | - | - | `@updatedAt` | **记录最后更新时间**。每当此任务记录在数据库中被修改时，此时间戳会自动更新为当前时间。(由Prisma自动管理) | (内部审计用) |
| `submittedByUserId` | `String` | - | - | - | - | - | **提交用户ID**。发起此任务的用户的唯一标识符。 | 実行ユーザー |
| `licenseId` | `String` | - | ● | - | - | - | **关联许可证业务ID**。此任务所属的许可证/契约的业务ID (对应 `License.licenseId`)。 | (内部关联) |
| `targetServerId` | `String` | - | ● | - | - | - | **目标服务器ID**。此任务操作针对的具体服务器记录的唯一ID (对应 `Server.id`)。 | (内部关联) |
| `targetServerName` | `String` | - | - | - | Yes | - | **目标服务器名称 (冗余)**。冗余存储的目标服务器的显示名称。由门户后端 (`createTaskAction`) 从`Server`表查询并填充，主要用于在用户界面直接显示，以优化查询性能。 | サーバ名 |
| `targetVmName` | `String?` | - | - | - | Yes | - | **目标Azure VM名称 (冗余)**。执行此任务的目标Azure虚拟机的准确名称。由门户后端 (`createTaskAction`) 从`Server`表查询并填充，供`TaskExecuteFunc`等后端组件使用。 | (内部关联) |
| `dockerContainerName` | `String?` | - | - | - | Yes | - | **目标Docker容器名称 (冗余)**。执行此任务的目标Docker容器的准确名称。由门户后端 (`createTaskAction`) 从`Server`表查询并填充，供`TaskExecuteFunc`等后端组件使用。 | (内部关联) |
| `hrwGroupName` | `String?` | - | - | - | Yes | - | **Hybrid Runbook Worker组名 (冗余)**。此任务执行目标的Azure Automation HRW组名。由门户后端 (`createTaskAction`) 从`Server`表查询并填充，供`TaskExecuteFunc`提交作业时使用。 | (内部关联) |
| `parametersJson` | `String` | - | - | - | Yes | - | **任务参数 (JSON)**。存储任务执行所需的、特定于任务类型的参数的JSON字符串。例如，管理项目定义导入任务时，此处存储已上传CSV文件在Azure Blob Storage中的路径。 | (内部参数) |
| `resultMessage` | `String` | - | - | - | Yes | - | **任务结果摘要/用户可见错误或提示**。存储任务执行的最终结果摘要或用户可理解的错误/提示信息（通常源自`EMETxxxx`或`EMECxxxx`系列消息）。 | タスク詳細 |
| `outputBlobPath` | `String` | - | - | - | Yes | - | **输出文件Blob相对路径**。仅当特定任务类型（如管理项目定义导出）成功完成并产生可下载文件时，此字段存储该文件在Azure Blob Storage中相对于其容器根目录的路径。 | (内部，用于下载链接) |
| `errorMessage` | `String` | - | - | - | Yes | - | **内部技术错误信息**。【内部使用】记录任务失败时的详细技术性错误信息。 | (内部错误) |
| `errorCode` | `String` | - | - | - | Yes | - | **内部错误代码**。【内部使用】记录任务失败时的内部错误代码或分类码。 | (内部错误) |

*PK: Primary Key, FK: Foreign Key, UQ: Unique Constraint*
*(数据类型参考项目代码库中 `prisma/schema.prisma` 文件的权威定义。SQL参考仅为示意。)*
*(日文名称列中，括号内为“内部XX”表示该字段主要供系统内部逻辑使用，不直接作为列标题在典型用户界面显示，但其值可能间接影响UI展示。)*
*(Nullable列中的“Yes”或“-”基于Prisma Schema中字段类型后的 `?` 标记或其是否为必需。)*

## 2. 关系 (Relations)

本表与其他核心数据模型的关系，已在项目代码库的 `prisma/schema.prisma` 文件中通过关系字段明确定义。

*   **对 `License` (`license`)**: 多对一。通过 `licenseId` 字段（外键，引用 `License.licenseId` 唯一键）与`License`表关联。一个任务必须属于一个许可证。Prisma Schema中定义为 `@relation(fields: [licenseId], references: [licenseId], onDelete: NoAction, onUpdate: NoAction)`。
*   **对 `Server` (`targetServer`)**: 多对一。通过 `targetServerId` 字段（外键，引用 `Server.id` 主键）与`Server`表关联。一个任务必须针对一个服务器条目。Prisma Schema中定义为 `@relation(fields: [targetServerId], references: [id], onDelete: NoAction, onUpdate: NoAction)`。
*   **对 `OperationLog` (`operationLogs`)**: 一对多。一个操作日志导出类型的任务（`Task`）可以生成一条或多条操作日志元数据记录（`OperationLog`）。通过`OperationLog.generatedByTaskId` 外键实现关联。Prisma Schema中通过 `OperationLog[]` 反向关系体现。

**关于外键约束策略的说明**: `onDelete: NoAction, onUpdate: NoAction` 表示如果关联的 `License` 或 `Server` 记录被删除或其被引用的键被更新，数据库层面不会自动对 `Task` 表做任何操作。数据完整性的维护（例如，如何处理与已删除服务器关联的任务）由应用层面逻辑负责。

## 3. 索引 (Indexes)

为保证查询性能，设计上要求在 `prisma/schema.prisma` 文件中（或应在其中）定义以下索引：

*   **主键**: `id` (由 `@id` 隐式创建索引)。
*   **辅助索引**:
    *   `@@index([status])`: 支持后端服务高效查询特定状态的任务 (例如 `RunbookMonitorFunc` 查询 `RUNBOOK_SUBMITTED` 状态的任务)。
    *   `@@index([targetServerId, submittedAt])`: 支持任务记录保留策略中按服务器和提交时间查询并清理旧任务的逻辑。
    *   `@@index([licenseId, submittedAt])`: 支持按许可证和提交时间查询任务，可能用于用户界面的任务列表展示和筛选。

*(索引的最终配置应基于实际的后端查询模式和性能测试结果进行调优。)*

## 4. 备注 (Notes)

*   本表是“任务列表 (タスク一覧)”用户界面的主要数据源，也是后台任务处理流程中状态流转和信息传递的核心载体。
*   **执行上下文信息**: 字段 `targetVmName`, `dockerContainerName`, `hrwGroupName` 由门户后端 `createTaskAction` 在创建任务时从 `Server` 表查询并冗余存储于此，供后端 `TaskExecuteFunc` 直接使用，以减少 `TaskExecuteFunc` 的数据库查询依赖并简化其逻辑。
*   **状态管理**: `status`字段存储的是系统内部定义的状态码。这些内部状态码通过值列表 (LOV 表中 `parentCode='TASK_STATUS'` 的条目) 映射为用户界面上可见的日文外部显示状态。详细的内部状态及其流转逻辑，请参考系统架构设计文档。
*   **参数与结果的存储**: `parametersJson` 和 `resultMessage` 设计为存储JSON字符串或文本，以提供一定的灵活性来适应不同任务类型的特定需求。
*   **任务记录保留策略**: 本表中的任务记录受系统定义的保留策略约束。对于每个服务器 (`targetServerId`)，其关联的任务记录总数上限由值列表（LOV 表中 `TASK_CONFIG.MAX_RETENTION_COUNT`，默认10）定义。当 `TaskExecuteFunc` 成功处理一个新任务（例如，将其提交给Azure Automation）后，它会检查当前服务器的已完成任务（状态为 `COMPLETED_SUCCESS`, `COMPLETED_ERROR`, `CANCELLED`）数量。如果加上当前新激活的任务后，总任务数可能超过或已达到上限，`TaskExecuteFunc` 将按 `submittedAt` 字段升序（即最旧的优先）清理该服务器下一个或多个已完成状态的旧任务记录，以尽量将总任务数维持在配置的上限内。删除这些旧的已完成任务记录时，必须确保联动删除其在Azure Blob Storage中的所有关联文件以及`OperationLog`表中的相关记录。此清理操作应设计为异步且容错的，不阻塞`TaskExecuteFunc`的核心任务处理流程。