/**
 * @fileoverview Azure Automation Mock Server启动脚本
 * @description
 * 用于启动Azure Automation Mock Server的独立脚本。
 * 可以在集成测试之前启动，为测试提供模拟的Azure Automation服务。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { AzureAutomationMockServer } from '../support/azure-automation-mock-server';

/**
 * Mock Server启动函数
 */
async function startMockServer(): Promise<void> {
  const port = parseInt(process.env.MOCK_SERVER_PORT || '3001');

  const server = new AzureAutomationMockServer(port);

  try {
    await server.start();
    console.log(`Mock Server started successfully!`);
    console.log(`Base URL: ${server.getBaseUrl()}`);
    console.log('Press Ctrl+C to stop the server');

    // 优雅关闭处理
    process.on('SIGINT', async () => {
      console.log('\nReceived SIGINT, shutting down gracefully...');
      await server.stop();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      console.log('\nReceived SIGTERM, shutting down gracefully...');
      await server.stop();
      process.exit(0);
    });

  } catch (error) {
    console.error('Failed to start Mock Server:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本，则启动服务器
if (require.main === module) {
  startMockServer().catch(console.error);
}

export { startMockServer };
