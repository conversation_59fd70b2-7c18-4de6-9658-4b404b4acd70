import prisma from "@/app/lib/prisma";
import { LogFunctionSignature } from "../logger";

/**
 * LOV（List of Values）に関するデータ操作を行う静的クラスです。
 * 本クラスはLOVリストおよび単一LOV項目の取得を担当します。
 */
export class ServerDataLov {
  /**
   * 指定されたparentCodeに紐づくLOVリストを取得します。
   *
   * @param {string} parentCode - 親LOVコード
   * @returns {Promise<any[]>} LOV項目配列
   */
  @LogFunctionSignature()
  static async fetchLovList(parentCode: string) {
    // parentCodeで有効なLOV項目を全件取得
    const lovs = await prisma.lov.findMany({
      where: {
        parentCode,
        isEnabled: true,
      },
    });
    return lovs;
  }

  /**
   * 指定されたcodeに該当するLOV項目を取得します。
   *
   * @param {string} code - LOVコード
   * @returns {Promise<any>} 単一LOV項目
   */
  @LogFunctionSignature()
  static async fetchLov(code: string) {
    // codeで有効なLOV項目を1件取得
    const lov = await prisma.lov.findFirst({
      where: {
        code,
        isEnabled: true,
      },
    });
    return lov;
  }
} 