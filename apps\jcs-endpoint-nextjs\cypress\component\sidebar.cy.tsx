// cypress/integration/sidebar.spec.js

import Sidebar from "@/app/ui/sidebar";

describe("サイドバー コンポーネント", () => {
  it("サイドバーが正しく表示され、メニューが正しい内容で構築されていること", () => {
    // コンポーネントをマウント
    cy.mount(<Sidebar />);

    // サイドバーが表示されていることを確認するテストケース
    cy.get("#sidebar-multi-level-sidebar").should("be.visible");

    // 一级メニューが正しく構築されていることを確認
    cy.get("[data-collapse-toggle]").eq(0).should("contain.text", "管理");
    cy.get("[data-collapse-toggle]").eq(1).should("contain.text", "ファイル");

    // 二级メニューが正しく構築されていることを確認
    cy.get("[data-collapse-toggle]").eq(0).click(); // 管理メニューを展開
    cy.get("#management li").each(($subMenu, subIndex) => {
      cy.wrap($subMenu).should("contain.text", `サーバ一覧`);
    });

    cy.get("[data-collapse-toggle]").eq(1).click(); // ファイルメニューを展開
    cy.get("#file li").each(($subMenu, subIndex) => {
      const subMenuText = [
        "操作ログ一覧",
        "製品媒体一覧",
        "マニュアル一覧",
        "提供ファイル一覧",
        "サポート情報一覧",
      ];
      cy.wrap($subMenu).should("contain.text", subMenuText[subIndex]);
    });
  });

  it("メニューアイテムが正しく展開および折りたたまれること", () => {
    // コンポーネントをマウント
    cy.mount(<Sidebar />);

    // メニューアイテムの展開と折りたたみが正常に動作することを確認するテストケース
    cy.get("[data-collapse-toggle]").eq(0).click(); // 管理メニューを展開
    cy.get("[data-collapse-toggle]")
      .eq(0)
      .should("have.attr", "aria-expanded", "false");

    cy.get("[data-collapse-toggle]").eq(1).click(); // ファイルメニューを展開
    cy.get("[data-collapse-toggle]")
      .eq(1)
      .should("have.attr", "aria-expanded", "false");
  });

  it("サブメニューへの正しい遷移が行われること", () => {
    // コンポーネントをマウント
    cy.mount(<Sidebar />);

    // サブメニューへの正しい遷移が行われることを確認するテストケース
    cy.get("#management li")
      .eq(0)
      .find("a")
      .should("have.attr", "href", "/dashboard/servers");
  });
});
