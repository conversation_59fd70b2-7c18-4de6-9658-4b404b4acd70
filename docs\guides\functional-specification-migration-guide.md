# 機能仕様書 (FS) 内容迁移至 docs/ 目录指南

## 1. 目的

本指南旨在提供一套清晰的步骤和方法，将项目现有的日文版《機能仕様書》(Functional Specification, FS) 的内容，系统地、准确地迁移并转化为项目 `docs/` 目录下的Markdown文档。这些Markdown文档将作为项目的核心技术文档，是设计信息的**单一事实来源 (Single Source of Truth - SSoT)**，服务于开发团队的理解、沟通，并作为AI编程助手的主要信息输入源。

本指南侧重于从《機能仕様書》中提取功能需求、高层设计和业务逻辑，并将其结构化地记录在`docs/architecture/`和`docs/components/`等相关目录的Markdown文件中。

## 2. 准备工作

1.  **理解目标 `docs/` 目录结构**: 熟悉 `docs/guides/monorepo-structure-and-deployment-workflow.md` 中定义的 `docs/` 目录结构及其各子目录的用途，特别是 `docs/architecture/` 和 `docs/components/`。
2.  **获取最新的機能仕様書**: 确保您正在使用的是最新版本的FS文档（通常为Word或类似格式）。
3.  **Markdown编辑器**: 如 VS Code (配合 Markdown All in One 等插件)、Typora、Obsidian，便于编辑、预览和格式化Markdown内容。
4.  **文档转换工具 (可选)**: 如 Pandoc，可用于将 `.docx` 文件初步转换为 Markdown，减少手动复制粘贴。
5.  **术语表 (`docs/definitions/glossary.md`)**: 在迁移过程中，遇到项目特定的业务术语或技术缩写，及时记录其中文、日文（原文）及英文（官方或通用）对照，并给出简要定义。AI助手也将参考此术语表。

## 3. 迁移原则

*   **准确反映原意**: 迁移后的中文内容必须准确传达日文FS的核心需求和设计意图。
*   **结构化与模块化**: 将FS中的大章节分解为`docs/`目录下更小、更专注的Markdown文件。
*   **中文表达，英文术语**: 所有迁移到`docs/`目录的描述性文本均使用**简体中文**。技术组件、服务名称及标准术语优先使用官方英文全称。项目内部组件和功能模块在文件名和内部引用时，建议使用英文小写并用连字符连接（kebab-case）。
*   **Markdown最佳实践**: 充分利用Markdown的排版功能（标题、列表、表格、代码块、图片引用、Mermaid图）来组织内容，使其清晰易读。
*   **聚焦“是什么”与“为什么”**: FS主要描述系统“应该做什么”（功能需求）、“为什么这么做”（业务背景与目标），以及高层级的系统交互和约束。避免过早陷入实现细节，这些细节将由《詳細設計書》补充。
*   **图片与图表**:
    *   FS中的图片（截图、架构图等）应提取出来，根据其内容性质保存到 `docs/architecture/diagrams/` (针对系统级图表) 或 `docs/components/[component-name]/assets/` (针对特定组件的UI草图等) 目录下。然后在Markdown中通过相对路径引用。
    *   对于流程图、状态图等，强烈建议使用Mermaid在Markdown中重新绘制，以便于版本控制和后续修改。

## 4. 组件文档 (`docs/components/[component-name].md`) 的内容来源与组织

每个源自《機能仕様書》的功能模块或主要特性，都应在 `docs/components/` 目录下有一个对应的Markdown文件。该文件应至少包含以下源于FS的信息，并遵循推荐的结构模板。**此模板侧重于捕获功能规格层面的信息。**

```markdown
# 组件：[组件/功能中文名称] ([Component/Function English Name])

## 1. 概要 (Overview)
    *   **目的 (Purpose):** [从FS的“目的”或“概要”部分翻译和提炼。清晰阐述该功能的核心价值和在系统中的作用。]
    *   **用户故事/需求 (User Stories/Requirements):** [如果FS中包含或可提炼出用户故事，在此列出。例如：“作为[角色]，我希望能够[做什么]，以便[达到什么目标]。”若无，可省略或根据FS内容编写。]
    *   **高层依赖与交互 (High-Level Dependencies & Interactions):** [简述本组件在功能层面与其他主要用户可见组件或系统功能的关键依赖和交互方式。例如：“本功能依赖‘用户认证模块’提供用户信息，并与‘订单管理模块’进行数据交互。”更详细的技术依赖将在第3部分阐述。]

## 2. 功能规格 (Functional Specifications)

### 2.1 主要流程 (High-Level Flow)
    *   [来源于FS中的“処理フロー”、“ユースケース記述”或功能步骤描述。使用中文步骤列表、编号列表或Mermaid流程图 (`graph TD` 或 `flowchart LR`) 来清晰描述用户与系统的主要交互路径和系统响应。]
    *   **示例 (Mermaid 流程图):**
        ```mermaid
        graph TD
            A[用户访问登录页面] --> B(输入用户名和密码);
            B --> C{系统验证凭据};
            C -- 验证成功 --> D[跳转到主仪表盘];
            C -- 验证失败 --> E[显示错误信息];
        ```
    *   **示例 (步骤列表):**
        1.  用户导航至XX页面。
        2.  系统加载并显示YY数据。
        3.  用户执行ZZ操作。
        4.  系统处理操作并更新界面。

### 2.2 业务规则 (Business Rules)
    *   [来源于FS中定义的关键业务规则、约束条件、计算逻辑等。使用列表形式清晰陈述。]
    *   **示例:**
        *   用户密码长度必须在8到16个字符之间。
        *   订单金额超过1000元时，需要进行二次确认。
        *   普通用户最多能创建5个项目。

### 2.3 用户界面概述 (User Interface Overview) - 若适用
    *   [本节描述功能涉及的用户界面高层概念，非详细的控件定义。详细的UI元素定义参考《詳細設計書》的迁移指南。]
    *   **界面草图/核心区域 (UI Sketch/Key Areas):** [如果FS提供界面截图或线框图，将其保存到 `docs/components/[component-name]/assets/` 目录并在此引用。用文字描述界面的主要组成部分和布局。]
        *   例如: `![登录界面草图](./assets/login-screen-sketch.png)`
        *   “登录界面包含用户名输入区、密码输入区和登录按钮。”
    *   **主要交互点 (Key Interactions):** [列出用户在该界面上的主要操作以及系统的预期高层响应，不涉及具体控件ID或事件名。]
        *   例如：“用户输入凭据后点击登录按钮，系统验证用户信息。”
        *   “用户点击忘记密码链接，系统导航至密码重置流程。”
    *   **画面項目 (Screen Items - High Level):** [如果FS中提及了关键的输入输出字段的**概念**，可以在此列出其业务含义，而非详细的技术属性。]
        *   例如：“用户需要提供：用户名、密码。”
        *   “系统将显示：欢迎信息、用户仪表盘链接。”

### 2.4 前提条件 (Preconditions)
    *   [来源于FS中的“前提条件”部分。列出该功能正常执行前，系统或用户必须满足的外部条件。]
    *   **示例:**
        *   用户必须已通过身份验证。
        *   相关的基础数据（如产品目录）必须已配置完成。

### 2.5 制约事项 (Constraints/Limitations)
    *   [来源于FS中的“制限事項”部分。列出该功能在设计或实现上存在的已知限制或约束。]
    *   **示例:**
        *   本功能不支持并发编辑。
        *   数据导出格式仅限CSV。

### 2.6 注意事项 (Notes/Considerations)
    *   [来源于FS中的“注意事項”部分。记录与该功能相关的其他重要说明、潜在影响或未来可能的扩展点。]

### 2.7 错误处理概述 (Error Handling Overview)
    *   [来源于FS中的“エラー処理”部分。描述高层次的错误场景及系统应如何响应用户。避免列出具体的错误代码或详细的技术处理流程，这些属于详细设计范畴。具体的错误消息定义参考 `../../definitions/error-messages.md`。]
    *   **示例:**
        *   当用户输入无效时，应向用户提供清晰的错误提示信息。
        *   当发生不可恢复的系统错误时，应引导用户联系技术支持。
        *   操作失败时，应允许用户重试或取消。

### 2.8 相关功能参考 (Related Functional References)
    *   [此部分列出对理解本组件*功能上下文*和*用户流程*有帮助的其他**FS级别**的组件文档或高层架构文档。避免在此列出详细的数据模型、API定义等纯技术实现细节文档，这些将在后续的第3部分中详细引用。]
    *   **示例:**
        *   [用户认证模块](./user-authentication.md) - 提供本组件所需的用户身份验证。
        *   [订单管理模块](./order-management.md) - 本组件将与之进行订单数据交互。
        *   `../../architecture/system-architecture.md` - 系统整体架构图，展示本组件在系统中的位置。
        *   `[FS源文档名].docx` (位于 `docs-delivery/機能仕様書/`) - 本功能规格的原始日文描述。
        *   项目术语定义参见 `../../definitions/glossary.md`。

---
*注意：本组件文档的第1和第2部分主要捕获《機能仕様書》层面的信息。关于此组件更详细的技术实现细节（如API具体参数、数据库交互细节、详细的UI控件属性和事件处理逻辑），请参考《詳細設計書 (DDS) 内容迁移至 docs/ 目录指南》 (`../../guides/detailed-design-migration-guide.md`)，相关内容将填充到本组件文档后续的“3. 技术设计与实现细节”部分。*
---

## 3. 技术设计与实现细节 (Technical Design & Implementation) - DD驱动
    <!-- 此部分内容将来源于《詳細設計書》，详细描述组件的“如何实现”。 -->
    <!-- 例如：
    ### 3.1 技术栈与依赖
        *   前端: React (v18), Zustand
        *   后端API: Node.js (Express) - 相关接口定义见 `../../apis/openapi.v1.yaml`
        *   数据存储: Azure SQL Database - 表结构见 `../../data-models/`
        *   值列表定义: `../../definitions/lov-definitions.md`
        *   错误消息定义: `../../definitions/error-messages.md`
    ### 3.2 详细界面元素定义
    ### 3.3 详细事件处理逻辑
    ### 3.4 データ構造とAPIインタラクション
        *   调用 `GET /api/orders` 获取订单列表，请求/响应结构参见 `../../apis/openapi.v1.yaml` 中的 `/orders` 定义。
        *   订单数据存储于 `Order` 表，表结构参见 `../../data-models/order-table.md`。
    ### 3.5 数据库设计与访问细节
        *   订单查询逻辑: ...
    ...
    -->
```

## 5. 《機能仕様書》其他章节到Markdown的迁移指南

### 5.1. 文档元信息 (FS執筆に当たっての統一方針, 変更管理方法 等)
*   **处理方式**: 通常不直接迁移。项目文档规范在 `docs/README.md` 或贡献指南中定义，版本控制通过Git实现。

### 5.2. 概要 (開発背景、目的)
*   **迁移目标**: `docs/architecture/system-architecture.md` 的引言、背景和目标部分；`docs/README.md` (项目总体概述)。
*   **迁移步骤**: 提炼核心信息，整合到目标文档。
    *   **AI提示**: "请将以下日文文本（[FS背景目的段落]）提炼并翻译成中文，作为`system-architecture.md`的引言，强调项目价值。"

### 5.3. システム構成図 (System Configuration Diagram)
*   **迁移目标**: `docs/architecture/system-architecture.md` 中的架构图部分；图源文件（如Mermaid）存放在 `docs/architecture/diagrams/system-architecture.md` (或 `.mermaid` 文件)。
*   **迁移步骤**: 推荐使用Mermaid重绘。如果原图为图片，保存到 `docs/architecture/diagrams/` 并在文档中引用。
    *   **AI提示**: "根据此系统架构图（或其日文描述），请生成对应的Mermaid `graph TD` 或 `C4Context` 图的初始代码框架。"

### 5.4. 機能一覧 (Function List)
*   **迁移目标**:
    *   可以创建一个 `docs/components/README.md` 作为组件/功能模块的总览页面。
    *   或者，如果功能列表不是非常庞大，也可以在 `docs/architecture/system-architecture.md` 中列出核心功能模块。
*   **迁移步骤**:
    1.  将FS中的功能列表转换为Markdown表格。
    2.  为列表中的每个主要功能（如 "ログイン", "メイン画面", "サーバ一覧" 等），确保在 `docs/components/` 目录下存在或规划了对应的详细组件文档（例如 `login.md`, `main-screen.md`, `server-list.md`）。
    3.  在功能列表的Markdown表格中，为每个功能添加指向其详细组件文档的相对链接。

*   **Markdown格式示例 (在 `docs/components/README.md` 或 `system-architecture.md` 中)**:

    ```markdown
    ### 主要功能模块 (主要機能一覧)

    | No. | 機能ID (FS参照) | 機能名称 (日文)     | 中文名称/简述         | 详细文档 (链接到 `docs/components/`) |
    |-----|-----------------|-----------------------|-----------------------|---------------------------------------|
    | 1   | FS-001          | ログイン              | 用户登录与认证        | [用户登录](./login.md)                 |
    | 2   | FS-002          | メイン画面            | 主操作界面            | [主界面](./main-screen.md)             |
    | 3   | FS-003          | サーバ一覧            | 服务器列表管理        | [服务器列表](./server-list.md)         |
    | ... | ...             | ...                   | ...                   | ...                                   |
    ```

### 5.5. データベース設計 (Database Design) / ストレージ設計 (Storage Design) - 高层概述

*   **FS内容**: 通常是关于数据存储的高层概念、主要数据实体、存储选型（如Azure SQL, Azure Blob Storage）及其理由。不包含详细的表结构或字段定义。
*   **迁移目标**: `docs/architecture/system-architecture.md` 中关于数据持久化、存储策略的章节。
*   **迁移步骤**:
    1.  提炼FS中关于数据存储的策略、选型原因和核心概念。
    2.  用中文将这些高层设计决策整合到 `system-architecture.md` 的相关部分。
    3.  如果FS中提及了非常核心的几个数据实体名称，可以在 `docs/data-models/README.md` 中列出，并注明详细设计将在《詳細設計書》中定义。

### 5.6. セキュリティ (Security) - 高层需求

*   **FS内容**: 安全相关的功能需求、认证机制概述（如“通过Keycloak进行SSO”）、主要授权原则等。
*   **迁移目标**:
    *   整体安全架构和策略: `docs/architecture/security.md` (如果创建此专门文档) 或 `docs/architecture/system-architecture.md` 的安全章节。
    *   如果某个功能与安全直接相关（如登录功能），其高层安全需求也应在其对应的 `docs/components/[component-name].md` 的 "功能规格" 部分提及。
*   **迁移步骤**: 将FS中的安全需求和设计原则，按其粒度分配到架构文档或具体的组件文档中。

### 5.7. その他 (Others - 技术构成, 非功能性需求等)

*   **`技術構成` (Technology Stack - High Level)**: 如果FS中提及了关键的技术选型（如“采用Azure PaaS服务”），这部分信息应在 `docs/architecture/technology-stack.md` 或 `system-architecture.md` 中体现。详细的技术栈列表通常在《詳細設計書》中。
*   **`非機能要件` (Non-Functional Requirements - NFRs)**: 如性能、可用性、可扩展性等高层需求，应迁移到 `docs/architecture/non-functional-requirements.md` 或在 `system-architecture.md` 中有专门章节描述。
*   **`環境変数` (Environment Variables - Conceptual)**: 如果FS提到某些功能行为受配置影响，可以记录这些概念性的配置点，但具体的环境变量名称和值在《詳細設計書》中定义，并统一记录在 `docs/guides/environment-variables.md`。
*   **`障害時の動作` (Behavior in Case of Failure - High Level)**: FS中关于系统故障时的高层行为要求（如“系统应能从故障中恢复”、“关键数据不应丢失”）应在NFRs或系统架构文档中体现。

## 6. 从 `docs/` 生成/更新 《機能仕様書》(Word)

本节指导如何利用 `docs/` 目录下的Markdown文档作为权威信息源，来准备或更新日文版《機能仕様書》（通常为Word格式）作为交付物。

### 6.1. 原则

*   **`docs/` 为单一事实来源**: 所有设计信息应首先在 `docs/` 目录的Markdown文件中更新和维护。
*   **人工审核与调整**: 最终的交付文档需人工审查日语表达的准确性、专业性和格式。

### 6.2. 准备工作

1.  **最新的 `docs/` 内容**: 确保 `docs/architecture/system-architecture.md` 和所有相关的 `docs/components/[component-name].md` 文件（特别是模板中的第1和第2部分）已更新。
2.  **《機能仕様書》Word模板**: (`docs-delivery/templates/機能仕様書_テンプレート.docx`)。
3.  **工具与辅助**: Markdown编辑器, Word, AI助手, Pandoc (可选)。

### 6.3. 内容提取、翻译与格式化

1.  **整体结构**: 参照Word模板的章节结构。
2.  **概要、系统构成**:
    *   **源信息**: `docs/architecture/system-architecture.md`。
    *   **转换**: 提取相关章节（如引言、目标、系统架构图描述、高层组件说明）。使用AI辅助或人工翻译成专业的日文（である調），粘贴到Word模板的对应部分。Mermaid架构图可截图或导出为图片后插入。
3.  **機能一覧 (Function List)**:
    *   **源信息**: `docs/components/README.md` 或 `system-architecture.md` 中的功能列表Markdown表格。
    *   **转换**: 将Markdown表格内容（功能名称、概要等）复制，使用AI辅助或人工翻译成日文，并在Word中创建或填充表格。
4.  **各機能詳細 (Detailed Function Specifications)**:
    *   **源信息**: 每个 `docs/components/[component-name].md` 文件中的 “1. 概要 (Overview)” 和 “2. 功能规格 (Functional Specifications)” 部分。
    *   **转换**:
        *   针对每个功能，将其Markdown文档中对应章节的内容（目的、主要流程、业务规则、UI概述、前提条件、制约事项、注意事项、错误处理概述等），通过AI辅助或人工翻译成日文（である調）。
        *   将翻译后的文本、重绘/截图的流程图、界面草图等，按照《機能仕様書》模板中对各機能詳細的格式要求（如目的、前提条件、処理フロー、画面概要等）组织并粘贴到Word文档中。错误消息文本参考 `docs/definitions/error-messages.md` 中的日文定义。
5.  **数据库设计、存储设计、安全等章节 (高层描述)**:
    *   **源信息**: `docs/architecture/system-architecture.md` 或其他相关架构文档中的高层描述。
    *   **转换**: 提取相关内容，翻译并整合到Word模板的对应章节。

### 6.4. 最终审查与交付

*   对生成的《機能仕様書》Word文档进行彻底的日文和内容审查，确保与`docs/`源信息一致且符合质量要求。
*   在文件名或文档属性中明确标注版本号和日期。
*   归档到 `docs-delivery/機能仕様書/` 并进行版本控制。

## 7. 注意事项

*   **FS 与 DDS 的界限**: 在迁移FS时，专注于“是什么”和“为什么”，避免过早引入过多实现细节。`docs/components/[component-name].md` 中的第1和第2部分应严格对应FS内容，其后续的“3. 技术设计与实现细节”部分则对应DDS内容。
*   **保持更新**: `docs/` 是动态的，确保交付文档能反映其最新状态。当Markdown源文档更新时，应评估是否需要更新交付用的Word文档。

本指南旨在帮助团队将《機能仕様書》的内容高效、准确地迁移到项目共享的Markdown文档中，为后续的详细设计、开发和维护提供坚实基础。
