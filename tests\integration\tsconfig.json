{
  // TypeScript 配置，适用于 Playwright 测试
  "compilerOptions": {
    "target": "ESNext", // 现代 JS 语法
    "module": "CommonJS",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "types": ["@playwright/test", "node"]
  },
  "include": ["specs/**/*.ts", "support/**/*.ts"]
} 