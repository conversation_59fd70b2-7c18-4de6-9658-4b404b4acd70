{"metadata": {"generatedAt": "2025-07-16T07:15:40.190Z", "generatedBy": "extract-test-cases-json.js", "description": "JCS Endpoint Monorepo テストケース抽出結果", "structure": "プロジェクト名 -> 大項目 -> 小項目 -> テストケースリスト"}, "statistics": {"totalProjects": 3, "totalMajorCategories": 12, "totalMinorCategories": 27, "totalTestCases": 420, "byProject": {"jcs-endpoint-nextjs": {"displayName": "ポータル", "majorCategories": 3, "minorCategories": 14, "testCases": 185}, "jcs-backend-services-standard": {"displayName": "タスク Function App", "majorCategories": 7, "minorCategories": 9, "testCases": 164}, "jcs-backend-services-long-running": {"displayName": "Runbook ジョブ Function App", "majorCategories": 2, "minorCategories": 4, "testCases": 71}}}, "testCases": {"jcs-endpoint-nextjs": {"displayName": "ポータル", "type": "frontend", "categories": {"app": {"displayName": "app", "subCategories": {"route": {"displayName": "route", "testCases": [{"テストケース名": "正常系：有効なセッションとタスクIDでダウンロードが成功する", "試験観点": "正常なダウンロード処理", "試験対象": "設計文書に従った基本的なダウンロードフロー", "試験手順": "1. 有効なセッションとタスクIDでリクエストを送信\r\n2. LOV設定が正常に取得される\r\n3. SAS URLが生成される\r\n4. リダイレクトが実行される", "確認項目": "- ServerDataLov.fetchLovが正しいパラメータで呼ばれること\r\n- BlobActions.generateBlobUrlWithSASが正しいパラメータで呼ばれること\r\n- redirectが正しいSAS URLで呼ばれること\r\n- 適切なログが出力されること"}, {"テストケース名": "異常系：セッションが存在しない場合は500エラーを返す", "試験観点": "セッションが存在しない場合のエラー処理", "試験対象": "認証失敗時の500エラーレスポンス（oplogs下載路由と同様）", "試験手順": "1. セッションが存在しない状態でリクエストを送信\r\n2. 500エラーが返されることを確認", "確認項目": "- 500ステータスコードが返されること\r\n- EMEC0007エラーメッセージが返されること\r\n- エラーログが出力されること"}, {"テストケース名": "異常系：ライセンスIDが存在しない場合は500エラーを返す", "試験観点": "ライセンスIDが存在しない場合のエラー処理", "試験対象": "不完全なセッション情報での認証失敗", "試験手順": "1. ライセンスIDが含まれていないセッションでリクエストを送信\r\n2. 500エラーが返されることを確認", "確認項目": "- 500ステータスコードが返されること\r\n- 適切なエラーログが出力されること"}, {"テストケース名": "異常系：LOV設定が存在しない場合は500エラーを返す", "試験観点": "LOV設定が存在しない場合のエラー処理", "試験対象": "Azure Blob Storageコンテナ設定取得失敗時の500エラー", "試験手順": "1. LOV設定が存在しない状態でリクエストを送信\r\n2. 500エラーが返されることを確認", "確認項目": "- 500ステータスコードが返されること\r\n- EMEC0007エラーメッセージが返されること\r\n- 適切なエラーログが出力されること"}, {"テストケース名": "異常系：LOV設定のvalue値が空の場合は500エラーを返す", "試験観点": "LOV設定のvalue値が空の場合のエラー処理", "試験対象": "不正なLOV設定での500エラー", "試験手順": "1. LOV設定のvalue値が空の状態でリクエストを送信\r\n2. 500エラーが返されることを確認", "確認項目": "- 500ステータスコードが返されること\r\n- 適切なエラーログが出力されること"}, {"テストケース名": "異常系：SAS URL生成が失敗した場合は500エラーを返す", "試験観点": "SAS URL生成失敗時のエラー処理", "試験対象": "Azure Blob Storage操作失敗時の500エラー", "試験手順": "1. SAS URL生成が失敗する状態でリクエストを送信\r\n2. 500エラーが返されることを確認", "確認項目": "- 500ステータスコードが返されること\r\n- EMEC0007エラーメッセージが返されること\r\n- 適切なエラーログが出力されること"}, {"テストケース名": "異常系：予期しない例外が発生した場合は500エラーを返す", "試験観点": "予期しない例外発生時のエラー処理", "試験対象": "システム例外での500エラー", "試験手順": "1. セッション取得時に予期しない例外が発生する状態でリクエストを送信\r\n2. 500エラーが返されることを確認", "確認項目": "- 500ステータスコードが返されること\r\n- EMEC0007エラーメッセージが返されること\r\n- 詳細なエラーログが出力されること"}, {"テストケース名": "異常系：LOV取得時に例外が発生した場合は500エラーを返す", "試験観点": "LOV取得時の例外処理", "試験対象": "データベース接続失敗時の500エラー", "試験手順": "1. LOV取得時にデータベース例外が発生する状態でリクエストを送信\r\n2. 500エラーが返されることを確認", "確認項目": "- 500ステータスコードが返されること\r\n- 適切なエラーログが出力されること"}, {"テストケース名": "境界値：特殊文字を含むタスクIDでも正常に処理される", "試験観点": "特殊文字を含むタスクIDの処理", "試験対象": "URLエンコードされたタスクIDでの正常処理", "試験手順": "1. 特殊文字を含むタスクIDでリクエストを送信\r\n2. 正常にSAS URLが生成されることを確認", "確認項目": "- 特殊文字がそのまま処理されること\r\n- 正常にリダイレクトが実行されること"}, {"テストケース名": "境界値：長いタスクIDでも正常に処理される", "試験観点": "長いタスクIDの処理", "試験対象": "最大長に近いタスクIDでの正常処理", "試験手順": "1. 長いタスクIDでリクエストを送信\r\n2. 正常に処理されることを確認", "確認項目": "- 長いタスクIDが正常に処理されること\r\n- ファイルパスが正しく構築されること"}]}}}, "lib": {"displayName": "lib", "subCategories": {"create-task-error-handling": {"displayName": "create-task-error-handling", "testCases": [{"テストケース名": "異常系: 予期しない内部例外の処理", "試験観点": "予期しない内部例外の処理", "試験対象": "createTaskAction の通用例外処理", "試験手順": "1. getIronSession で予期しない例外を発生させる\r\n2. 適切なエラーメッセージが返されることを確認", "確認項目": "- EMEC0027 エラーメッセージが返されること\r\n- success が false であること"}, {"テストケース名": "異常系: セッション取得時のネットワークエラー", "試験観点": "セッション取得時の例外処理", "試験対象": "createTaskAction のセッション例外処理", "試験手順": "1. getIronSession でネットワークエラーを発生させる\r\n2. 適切なエラーメッセージが返されることを確認", "確認項目": "- EMEC0027 エラーメッセージが返されること"}, {"テストケース名": "異常系: メモリ不足例外の処理", "試験観点": "メモリ不足例外の処理", "試験対象": "createTaskAction のメモリ例外処理", "試験手順": "1. getIronSession でメモリ不足エラーを発生させる\r\n2. 適切なエラーメッセージが返されることを確認", "確認項目": "- EMEC0027 エラーメッセージが返されること"}, {"テストケース名": "異常系: null参照例外の処理", "試験観点": "null例外の処理", "試験対象": "createTaskAction のnull例外処理", "試験手順": "1. getIronSession でnull参照エラーを発生させる\r\n2. 適切なエラーメッセージが返されることを確認", "確認項目": "- EMEC0027 エラーメッセージが返されること"}, {"テストケース名": "異常系: 非同期処理例外の処理", "試験観点": "非同期処理例外の処理", "試験対象": "createTaskAction の非同期例外処理", "試験手順": "1. getIronSession で非同期処理エラーを発生させる\r\n2. 適切なエラーメッセージが返されることを確認", "確認項目": "- EMEC0027 エラーメッセージが返されること"}]}, "refresh-task-list": {"displayName": "refresh-task-list", "testCases": [{"テストケース名": "正常系: 有効なセッション情報でキャッシュ無効化", "試験観点": "正常系のキャッシュ無効化処理", "試験対象": "refreshTaskList 関数", "試験手順": "1. 有効なセッション情報でrefreshTaskListを実行\r\n2. 適切なキャッシュタグで無効化が実行されることを確認", "確認項目": "- revalidateTagが正しいタグで呼び出されること"}, {"テストケース名": "異常系: セッション情報が存在しない場合", "試験観点": "セッション情報が存在しない場合", "試験対象": "refreshTaskList 関数のセッション検証", "試験手順": "1. セッション情報が存在しない状況でrefreshTaskListを実行\r\n2. キャッシュ無効化がスキップされることを確認", "確認項目": "- revalidateTagが呼び出されないこと"}, {"テストケース名": "異常系: ライセンスIDが存在しない場合", "試験観点": "ユーザー情報が不完全な場合", "試験対象": "refreshTaskList 関数のセッション検証", "試験手順": "1. ライセンスIDが存在しない状況でrefreshTaskListを実行\r\n2. キャッシュ無効化がスキップされることを確認", "確認項目": "- revalidateTagが呼び出されないこと"}, {"テストケース名": "異常系: ユーザー情報がnullの場合", "試験観点": "ユーザー情報が null の場合", "試験対象": "refreshTaskList 関数のセッション検証", "試験手順": "1. user が null の状況でrefreshTaskListを実行\r\n2. キャッシュ無効化がスキップされることを確認", "確認項目": "- revalidateTagが呼び出されないこと"}]}, "task-cancel": {"displayName": "task-cancel", "testCases": [{"テストケース名": "正常系: 実行待ちタスクの中止処理", "試験観点": "実行待ち状態のタスクに対する正常な中止処理", "試験対象": "requestTaskCancellation関数のQUEUED状態タスク処理", "試験手順": "1. QUEUED状態のタスクデータをモック設定\r\n2. データベース更新処理を成功するようモック設定\r\n3. Service Bus送信処理を成功するようモック設定\r\n4. cancelTask関数を実行", "確認項目": "- タスクIDと最終更新日時を条件とした楽観的ロック制御でのupdateMany実行\r\n- ステータスがPENDING_CANCELLATIONに更新されること\r\n- Service Busへタスク中止メッセージが送信されること\r\n- 成功応答としてタスク中止受付完了メッセージが返されること"}, {"テストケース名": "異常系: 存在しないタスクの中止処理", "試験観点": "存在しないタスクIDに対する中止要求のエラーハンドリング", "試験対象": "requestTaskCancellation関数のタスク情報取得処理", "試験手順": "1. データベースからnullが返されるようモック設定（タスク不存在を模擬）\r\n2. 存在しないタスクIDでcancelTask関数を実行\r\n3. エラー応答の内容を検証", "確認項目": "- 処理結果がfalseであること\r\n- サーバ接続失敗を示すエラーメッセージが返されること\r\n- データベース更新処理が実行されないこと\r\n- Service Bus送信処理が実行されないこと"}, {"テストケース名": "異常系: 中止不可能なステータスのタスク", "試験観点": "実行中状態のタスクに対する中止要求の拒否処理", "試験対象": "requestTaskCancellation関数のタスクステータス分岐処理", "試験手順": "1. RUNBOOK_SUBMITTED状態のタスクデータをモック設定\r\n2. cancelTask関数を実行\r\n3. 中止不可を示すエラー応答の内容を検証", "確認項目": "- 処理結果がfalseであること\r\n- タスク中止不可を示すエラーメッセージが返されること\r\n- データベース更新処理が実行されないこと\r\n- Service Bus送信処理が実行されないこと"}, {"テストケース名": "異常系: 他ユーザーのタスク中止試行", "試験観点": "他ユーザーが作成したタスクに対する中止要求の処理", "試験対象": "requestTaskCancellation関数の権限制御処理", "試験手順": "1. 他ユーザーが作成したタスクデータをモック設定\r\n2. cancelTask関数を実行\r\n3. エラー応答の内容を検証", "確認項目": "- 処理結果がfalseであること\r\n- サーバ接続失敗を示すエラーメッセージが返されること\r\n- データベース更新処理が実行されないこと\r\n- Service Bus送信処理が実行されないこと"}, {"テストケース名": "境界条件: 既に中止済みのタスク", "試験観点": "既に中止済み状態のタスクに対する中止要求の拒否処理", "試験対象": "requestTaskCancellation関数のタスクステータス分岐処理", "試験手順": "1. CANCELLED状態のタスクデータをモック設定\r\n2. cancelTask関数を実行\r\n3. 中止不可を示すエラー応答の内容を検証", "確認項目": "- 処理結果がfalseであること\r\n- タスク中止不可を示すエラーメッセージが返されること\r\n- データベース更新処理が実行されないこと\r\n- Service Bus送信処理が実行されないこと"}, {"テストケース名": "異常系: データベース更新エラー処理", "試験観点": "データベース更新処理における例外発生時のエラーハンドリング", "試験対象": "requestTaskCancellation関数の例外処理機能", "試験手順": "1. タスク情報取得は成功するようモック設定\r\n2. データベース更新処理で例外が発生するようモック設定\r\n3. cancelTask関数を実行\r\n4. 例外処理によるエラー応答の内容を検証", "確認項目": "- 処理結果がfalseであること\r\n- 予期しないエラーを示すエラーメッセージが返されること\r\n- Service Bus送信処理が実行されないこと"}, {"テストケース名": "異常系: セッション情報が存在しない場合", "試験観点": "セッション情報不存在時のエラーハンドリング", "試験対象": "requestTaskCancellation関数のセッション検証処理", "試験手順": "1. 空のセッション情報をモック設定\r\n2. cancelTask関数を実行\r\n3. セッションエラー応答の内容を検証", "確認項目": "- 処理結果がfalseであること\r\n- セッション不正を示すエラーメッセージが返されること\r\n- データベース処理が実行されないこと\r\n- Service Bus送信処理が実行されないこと"}, {"テストケース名": "異常系: taskIdが空の場合", "試験観点": "空のタスクIDに対する入力パラメータ検証", "試験対象": "requestTaskCancellation関数の入力パラメータ検証処理", "試験手順": "1. 空文字列のタスクIDでcancelTask関数を実行\r\n2. パラメータエラー応答の内容を検証", "確認項目": "- 処理結果がfalseであること\r\n- パラメータ不正を示すエラーメッセージが返されること\r\n- データベース処理が実行されないこと\r\n- Service Bus送信処理が実行されないこと"}, {"テストケース名": "異常系: ライセンスIDが異なるタスクの中止試行", "試験観点": "異なるライセンスIDのタスクに対する中止要求の拒否処理", "試験対象": "requestTaskCancellation関数の権限制御処理", "試験手順": "1. 異なるライセンスIDを持つタスクデータをモック設定\r\n2. cancelTask関数を実行\r\n3. 権限エラー応答の内容を検証", "確認項目": "- 処理結果がfalseであること\r\n- 権限不足を示すエラーメッセージが返されること\r\n- データベース更新処理が実行されないこと\r\n- Service Bus送信処理が実行されないこと"}, {"テストケース名": "異常系: 楽観的ロック制御失敗", "試験観点": "楽観的ロック制御による更新失敗時のエラーハンドリング", "試験対象": "requestTaskCancellation関数の楽観的ロック制御処理", "試験手順": "1. タスク情報取得は成功するようモック設定\r\n2. データベース更新処理で更新件数0を返すようモック設定\r\n3. cancelTask関数を実行\r\n4. 楽観的ロック制御失敗エラー応答の内容を検証", "確認項目": "- 処理結果がfalseであること\r\n- 楽観的ロック制御失敗を示すエラーメッセージが返されること\r\n- Service Bus送信処理が実行されないこと"}, {"テストケース名": "異常系: Service Bus送信失敗時の補償処理", "試験観点": "Service Bus送信失敗時の補償処理機能", "試験対象": "requestTaskCancellation関数の補償処理機能", "試験手順": "1. タスク情報取得とデータベース更新は成功するようモック設定\r\n2. Service Bus送信処理で例外が発生するようモック設定\r\n3. 補償処理のデータベース更新は成功するようモック設定\r\n4. cancelTask関数を実行\r\n5. 補償処理実行とエラー応答の内容を検証", "確認項目": "- 処理結果がfalseであること\r\n- Service Bus送信失敗を示すエラーメッセージが返されること\r\n- 補償処理でタスクステータスがQUEUEDに戻されること"}, {"テストケース名": "異常系: Service Bus送信失敗時の補償処理も失敗", "試験観点": "Service Bus送信失敗時の補償処理自体も失敗する場合のエラーハンドリング", "試験対象": "requestTaskCancellation関数の補償処理例外処理機能", "試験手順": "1. タスク情報取得とデータベース更新は成功するようモック設定\r\n2. Service Bus送信処理で例外が発生するようモック設定\r\n3. 補償処理のデータベース更新でも例外が発生するようモック設定\r\n4. cancelTask関数を実行\r\n5. 補償処理失敗時のエラー応答の内容を検証", "確認項目": "- 処理結果がfalseであること\r\n- Service Bus送信失敗を示すエラーメッセージが返されること\r\n- 補償処理失敗のログが記録されること"}]}, "tasks": {"displayName": "tasks", "testCases": [{"テストケース名": "正常系：管理定義インポートタスクを作成する", "試験観点": "管理項目定義インポートタスクの正常系処理", "試験対象": "createTaskAction関数の管理項目定義インポート処理機能", "試験手順": "1. 正常なCSVファイル、MGMT_ITEM_IMPORTタスク種別、サーバーID、ファイル名をFormDataに設定\r\n2. Azure Blob Storageへの一時アップロード処理を実行\r\n3. データベーストランザクション開始、タスクレコード作成\r\n4. Service BusのTaskInputQueueへメッセージ送信\r\n5. 関連パスのキャッシュ無効化", "確認項目": "- 処理結果がtrueで返されること\r\n- タスク実行受付完了メッセージが返されること\r\n- Azure Blob Storageへファイルがアップロードされること\r\n- Service Busへメッセージが送信されること"}, {"テストケース名": "異常系：無効なファイル形式の場合はエラーを返す", "試験観点": "不正なファイル形式の場合のエラー検証。", "試験対象": "管理項目定義インポートタスク作成処理", "試験手順": "1. 拡張子がcsv以外のファイルをFormDataに設定する。\r\n2. タスク作成処理を実行する。\r\n3. エラーメッセージが返ることを確認する。", "確認項目": "- result.successがfalseであること。\r\n- result.messageがEMEC0017であること。"}, {"テストケース名": "異常系：タスク種別が不足している場合はエラーを返す", "試験観点": "共通パラメータ解析失敗（taskType不足）", "試験対象": "設計文書「06-タスク受付処理詳細.md」共通パラメータ解析", "試験手順": "1. taskTypeを含まないFormDataを作成\r\n2. createTaskActionを実行\r\n3. EMEC0021エラーが返されることを確認", "確認項目": "- success: falseが返されること\r\n- EMEC0021「サーバの接続に失敗したため、タスクを{0}できませんでした」（{0}は「開始」で置換）が返されること"}, {"テストケース名": "異常系：importFileが不足している場合はエラーを返す", "試験観点": "importFileが不足している場合のエラー検証。", "試験対象": "管理項目定義インポートタスク作成処理", "試験手順": "1. importFileをFormDataに設定しない。\r\n2. タスク作成処理を実行する。\r\n3. エラーメッセージが返ることを確認する。", "確認項目": "- result.successがfalseであること。\r\n- result.messageがEMEC0016（ファイル）であること。"}, {"テストケース名": "異常系：originalFileNameが不足している場合はエラーを返す", "試験観点": "originalFileNameが不足している場合のエラー検証。", "試験対象": "管理項目定義インポートタスク作成処理", "試験手順": "1. originalFileNameをFormDataに設定しない。\r\n2. タスク作成処理を実行する。\r\n3. エラーメッセージが返ることを確認する。", "確認項目": "- result.successがfalseであること。\r\n- result.messageがEMEC0016（ファイル）であること。"}, {"テストケース名": "異常系：ファイルサイズ超過時はEMEC0028を返す", "試験観点": "ファイルサイズ超過時のエラー検証", "試験対象": "createTask（管理項目定義インポート）", "試験手順": "1. 最大サイズを超えるファイルを作成する\r\n2. createTaskActionを実行する\r\n3. エラーメッセージがEMEC0028であることを確認する", "確認項目": "- result.successがfalseであること\r\n- result.messageがEMEC0028（ファイルサイズ超過）であること"}, {"テストケース名": "異常系：Blobアップロード失敗時はEMEC0018を返す", "試験観点": "Blobアップロード失敗時のエラー検証。", "試験対象": "管理項目定義インポートタスク作成処理", "試験手順": "1. BlobActions.uploadFileが例外を投げるようにモックする。\r\n2. タスク作成処理を実行する。\r\n3. エラーメッセージがEMEC0018であることを確認する。", "確認項目": "- result.successがfalseであること。\r\n- result.messageがEMEC0018であること。"}, {"テストケース名": "異常系：ServiceBus送信失敗時はEMEC0019を返す", "試験観点": "ServiceBus送信失敗時のエラー検証。", "試験対象": "管理項目定義インポートタスク作成処理", "試験手順": "1. ServiceBusActions.sendMessageが例外を投げるようにモックする。\r\n2. タスク作成処理を実行する。\r\n3. エラーメッセージがEMEC0019であることを確認する。", "確認項目": "- result.successがfalseであること。\r\n- result.messageがEMEC0019であること。"}, {"テストケース名": "異常系：DBトランザクション失敗時はEMEC0006を返す", "試験観点": "DBトランザクション失敗時のエラー検証。", "試験対象": "管理項目定義インポートタスク作成処理", "試験手順": "1. prisma.$transactionが例外を投げるようにモックする。\r\n2. タスク作成処理を実行する。\r\n3. エラーメッセージがEMEC0006であることを確認する。", "確認項目": "- result.successがfalseであること。\r\n- result.messageがEMEC0006であること。"}, {"テストケース名": "正常系：管理定義エクスポートタスクを作成する", "試験観点": "正常なパラメータでタスク作成が成功すること。", "試験対象": "createTask（管理項目定義エクスポート）", "試験手順": "1. 正常なタスク種別・サーバーIDをFormDataに設定する。\r\n2. createTaskActionを実行する。\r\n3. 成功メッセージにサーバー名が含まれることを確認する。", "確認項目": "- result.successがtrueであること。\r\n- result.messageにサーバー名が含まれること。"}, {"テストケース名": "異常系：タスク種別が不足している場合はエラーを返す", "試験観点": "共通パラメータ解析失敗（taskType不足）", "試験対象": "設計文書「06-タスク受付処理詳細.md」共通パラメータ解析", "試験手順": "1. taskTypeを含まないFormDataを作成\r\n2. createTaskActionを実行\r\n3. EMEC0021エラーが返されることを確認", "確認項目": "- success: falseが返されること\r\n- EMEC0021「サーバの接続に失敗したため、タスクを{0}できませんでした」（{0}は「開始」で置換）が返されること"}, {"テストケース名": "異常系：serverIdが不足している場合はエラーを返す", "試験観点": "共通パラメータ解析失敗（serverId不足）", "試験対象": "設計文書「06-タスク受付処理詳細.md」共通パラメータ解析", "試験手順": "1. serverIdを含まないFormDataを作成\r\n2. createTaskActionを実行\r\n3. EMEC0021エラーが返されることを確認", "確認項目": "- success: falseが返されること\r\n- EMEC0021「サーバの接続に失敗したため、タスクを{0}できませんでした」（{0}は「開始」で置換）が返されること"}, {"テストケース名": "異常系：サーバ実行構成取得失敗時はEMEC0021を返す", "試験観点": "サーバ実行構成取得失敗時のエラー検証", "試験対象": "createTask（管理項目定義エクスポート）", "試験手順": "1. ServerDataServers.getServerDetailsForTaskが例外を投げるようにモックする\r\n2. createTaskActionを実行する\r\n3. エラーメッセージがEMEC0021であることを確認する", "確認項目": "- result.successがfalseであること\r\n- result.messageがEMEC0021（サーバ接続失敗）であること"}, {"テストケース名": "異常系：サーバが見つからない場合はEMEC0021を返す", "試験観点": "サーバが見つからない場合のエラー検証", "試験対象": "createTask（管理項目定義エクスポート）", "試験手順": "1. ServerDataServers.getServerDetailsForTaskがnullを返すようにモックする\r\n2. createTaskActionを実行する\r\n3. エラーメッセージがEMEC0021であることを確認する", "確認項目": "- result.successがfalseであること\r\n- result.messageがEMEC0021（サーバ接続失敗）であること"}, {"テストケース名": "異常系：サーバ構成情報不足時はEMEC0021を返す", "試験観点": "サーバ構成情報不足時のエラー検証", "試験対象": "createTask（管理項目定義エクスポート）", "試験手順": "1. ServerDataServers.getServerDetailsForTaskが不完全な構成情報を返すようにモックする\r\n2. createTaskActionを実行する\r\n3. エラーメッセージがEMEC0021であることを確認する", "確認項目": "- result.successがfalseであること\r\n- result.messageがEMEC0021（サーバ接続失敗）であること"}, {"テストケース名": "異常系：コンテナステータス取得失敗時はEMEC0021を返す", "試験観点": "コンテナステータス取得失敗時のエラー検証", "試験対象": "createTask（管理項目定義エクスポート）", "試験手順": "1. ServerDataTasks.getContainerStatusが例外を投げるようにモックする\r\n2. createTaskActionを実行する\r\n3. エラーメッセージがEMEC0021であることを確認する", "確認項目": "- result.successがfalseであること\r\n- result.messageがEMEC0021（サーバ接続失敗）であること"}, {"テストケース名": "異常系：DBトランザクション失敗時はEMEC0006を返す", "試験観点": "DBトランザクション失敗時のエラー検証。", "試験対象": "createTask（管理項目定義エクスポート）", "試験手順": "1. prisma.$transactionが例外を投げるようにモックする。\r\n2. createTaskActionを実行する。\r\n3. エラーメッセージがEMEC0006であることを確認する。", "確認項目": "- result.successがfalseであること。\r\n- result.messageがEMEC0006であること。"}, {"テストケース名": "正常系：操作ログエクスポートタスクを作成する", "試験観点": "正常なパラメータでタスク作成が成功すること。", "試験対象": "createTask（操作ログエクスポート）", "試験手順": "1. 正常な日付範囲・最大日数・タスク種別・サーバーIDをFormDataに設定する。\r\n2. createTaskActionを実行する。\r\n3. 成功メッセージにサーバー名が含まれることを確認する。", "確認項目": "- result.successがtrueであること。\r\n- result.messageにサーバー名が含まれること。"}, {"テストケース名": "異常系：タスク種別が不足している場合はエラーを返す", "試験観点": "タスク種別が不足している場合のエラー検証。", "試験対象": "createTask（操作ログエクスポート）", "試験手順": "1. taskTypeをFormDataに設定しない。\r\n2. createTaskActionを実行する。\r\n3. エラーメッセージが返ることを確認する。", "確認項目": "- result.successがfalseであること。\r\n- result.messageがEMEC0016（タスク種別）であること。"}, {"テストケース名": "異常系：serverIdが不足している場合はエラーを返す", "試験観点": "共通パラメータ解析失敗（serverId不足）", "試験対象": "設計文書「06-タスク受付処理詳細.md」共通パラメータ解析", "試験手順": "1. serverIdを含まないFormDataを作成\r\n2. createTaskActionを実行\r\n3. EMEC0021エラーが返されることを確認", "確認項目": "- success: falseが返されること\r\n- EMEC0021「サーバの接続に失敗したため、タスクを{0}できませんでした」（{0}は「開始」で置換）が返されること"}, {"テストケース名": "異常系：exportStartDateが不足している場合はエラーを返す", "試験観点": "exportStartDateが不足している場合のエラー検証。", "試験対象": "createTask（操作ログエクスポート）", "試験手順": "1. exportStartDateをFormDataに設定しない。\r\n2. createTaskActionを実行する。\r\n3. エラーメッセージが返ることを確認する。", "確認項目": "- result.successがfalseであること。\r\n- result.messageがEMEC0016（開始日）であること。"}, {"テストケース名": "異常系：exportEndDateが不足している場合はエラーを返す", "試験観点": "exportEndDateが不足している場合のエラー検証。", "試験対象": "createTask（操作ログエクスポート）", "試験手順": "1. exportEndDateをFormDataに設定しない。\r\n2. createTaskActionを実行する。\r\n3. エラーメッセージが返ることを確認する。", "確認項目": "- result.successがfalseであること。\r\n- result.messageがEMEC0016（終了日）であること。"}, {"テストケース名": "異常系：開始日の形式が不正な場合はEMEC0016を返す", "試験観点": "日付形式不正時のエラー検証", "試験対象": "createTask（操作ログエクスポート）", "試験手順": "1. 不正な形式の開始日を設定する\r\n2. createTaskActionを実行する\r\n3. エラーメッセージがEMEC0016であることを確認する", "確認項目": "- result.successがfalseであること\r\n- result.messageがEMEC0016（開始日）であること"}, {"テストケース名": "異常系：終了日が開始日より前の場合はEMEC0024を返す", "試験観点": "終了日 < 開始日の場合のエラー検証", "試験対象": "createTask（操作ログエクスポート）", "試験手順": "1. 終了日が開始日より前の日付を設定する\r\n2. createTaskActionを実行する\r\n3. エラーメッセージがEMEC0024であることを確認する", "確認項目": "- result.successがfalseであること\r\n- result.messageがEMEC0024（日付順序エラー）であること"}, {"テストケース名": "異常系：最大日数設定取得失敗時はEMEC0021を返す", "試験観点": "最大日数設定取得失敗時のエラー検証。", "試験対象": "createTask（操作ログエクスポート）", "試験手順": "1. データベースからの最大日数設定取得をnullで失敗させる。\r\n2. createTaskActionを実行する。\r\n3. エラーメッセージが返ることを確認する。", "確認項目": "- result.successがfalseであること。\r\n- result.messageがEMEC0021（サーバ接続失敗）であること。"}, {"テストケース名": "異常系：日付範囲が最大日数を超える場合はエラーを返す", "試験観点": "日付範囲が最大日数を超える場合のエラー検証。", "試験対象": "createTask（操作ログエクスポート）", "試験手順": "1. exportStartDateとexportEndDateの日数差がmaxExportDaysSpanを超える値を設定する。\r\n2. createTaskActionを実行する。\r\n3. エラーメッセージがEMEC0020（最大日数超過）であることを確認する。", "確認項目": "- result.successがfalseであること。\r\n- result.messageがEMEC0020（最大日数超過）であること。"}, {"テストケース名": "異常系：DBトランザクション失敗時はEMEC0006を返す", "試験観点": "DBトランザクション失敗時のエラー検証。", "試験対象": "createTask（操作ログエクスポート）", "試験手順": "1. prisma.$transactionが例外を投げるようにモックする。\r\n2. createTaskActionを実行する。\r\n3. エラーメッセージがEMEC0006であることを確認する。", "確認項目": "- result.successがfalseであること。\r\n- result.messageがEMEC0006であること。"}, {"テストケース名": "異常系：コンテナがBUSYの場合はEMEC0022を返す", "試験観点": "コンテナがBUSYの場合のエラー検証。", "試験対象": "createTask（操作ログエクスポート）", "試験手順": "1. ServerDataTasks.getContainerStatusがBUSYを返すようにモックする。\r\n2. 正常なパラメータでcreateTaskActionを実行する。\r\n3. EMEC0022エラーが返ることを確認する。", "確認項目": "- result.successがfalseであること。\r\n- result.messageがEMEC0022であること。"}, {"テストケース名": "異常系：コンテナステータスが未知の文字列の場合はEMEC0021を返す", "試験観点": "コンテナステータスが未知の文字列の場合のエラー検証", "試験対象": "createTask（管理項目定義エクスポート）", "試験手順": "1. ServerDataTasks.getContainerStatusが未知の文字列を返すようにモックする\r\n2. 正常なパラメータでcreateTaskActionを実行する\r\n3. EMEC0021エラーが返ることを確認する", "確認項目": "- result.successがfalseであること\r\n- result.messageがEMEC0021（サーバ接続失敗）であること"}, {"テストケース名": "異常系：コンテナステータスが空文字列の場合はEMEC0021を返す", "試験観点": "コンテナステータスが空文字列の場合のエラー検証", "試験対象": "createTask（操作ログエクスポート）", "試験手順": "1. ServerDataTasks.getContainerStatusが空文字列を返すようにモックする\r\n2. 正常なパラメータでcreateTaskActionを実行する\r\n3. EMEC0021エラーが返ることを確認する", "確認項目": "- result.successがfalseであること\r\n- result.messageがEMEC0021（サーバ接続失敗）であること"}, {"テストケース名": "異常系：コンテナステータスが数値文字列の場合はEMEC0021を返す", "試験観点": "コンテナステータスが数値文字列の場合のエラー検証", "試験対象": "createTask（管理項目定義インポート）", "試験手順": "1. ServerDataTasks.getContainerStatusが数値文字列を返すようにモックする\r\n2. 正常なパラメータでcreateTaskActionを実行する\r\n3. EMEC0021エラーが返ることを確認する", "確認項目": "- result.successがfalseであること\r\n- result.messageがEMEC0021（サーバ接続失敗）であること"}, {"テストケース名": "ContainerConcurrencyStatusが存在しない場合は新規作成される", "試験観点": "ContainerConcurrencyStatusが存在しない場合に新規作成されること。", "試験対象": "createTask（全タスク種別共通）", "試験手順": "1. prismaMock.$transactionでtx.containerConcurrencyStatus.createをモック。\r\n2. createが呼ばれることを検証。", "確認項目": "- createが1回呼ばれること。"}, {"テストケース名": "taskNameが正しいフォーマットで生成される", "試験観点": "taskNameが指定されたフォーマットで正しく生成されること", "試験対象": "createTask関数のタスク名生成機能（全タスク種別共通）", "試験手順": "1. prismaMock.$transactionでtx.task.createをモック設定\r\n2. createTaskAction関数を実行\r\n3. data.taskNameが\"{ServerName}-{タスク種別}-{YYYYMMDDHHmmss}\"形式であることを検証", "確認項目": "- taskNameがサーバー名、タスク種別、タイムスタンプを含む正しいフォーマットであること\r\n- タイムスタンプ部分が14桁の数字であること"}, {"テストケース名": "異常系：予期せぬ内部例外発生時はEMEC0027を返す", "試験観点": "予期せぬ内部例外発生時のエラー検証", "試験対象": "createTask（全タスク種別共通）", "試験手順": "1. getIronSessionが予期せぬ例外を投げるようにモックする\r\n2. createTaskActionを実行する\r\n3. エラーメッセージがEMEC0027であることを確認する", "確認項目": "- result.successがfalseであること\r\n- result.messageがEMEC0027（予期せぬ例外）であること"}, {"テストケース名": "正常系: BUSYステータスの取得", "試験観点": "BUSYステータス取得機能の動作確認", "試験対象": "fetchContainerConcurrencyStatus関数の正常系処理", "試験手順": "1. BUSYステータスのコンテナでfetchContainerConcurrencyStatusを呼び出し", "確認項目": "- BUSYステータスが正しく取得されること"}, {"テストケース名": "境界条件: レコードが存在しない場合はnullを返す", "試験観点": "レコード不存在時の境界条件確認", "試験対象": "fetchContainerConcurrencyStatus関数の境界値処理", "試験手順": "1. 存在しないコンテナでfetchContainerConcurrencyStatusを呼び出し", "確認項目": "- nullが返されること"}, {"テストケース名": "正常系: IDLEステータスの取得", "試験観点": "IDLEステータス取得機能の動作確認", "試験対象": "fetchContainerConcurrencyStatus関数の正常系処理", "試験手順": "1. IDLEステータスのコンテナでfetchContainerConcurrencyStatusを呼び出し", "確認項目": "- IDLEステータスが正しく取得されること"}, {"テストケース名": "正常系: 基本的なタスク一覧取得", "試験観点": "基本的なタスク一覧取得機能の動作確認", "試験対象": "fetchFilteredTasks関数の正常系処理", "試験手順": "1. 基本的な条件でfetchFilteredTasksを呼び出し", "確認項目": "- タスク一覧が正しく取得されること"}, {"テストケース名": "正常系: フィルタリング処理", "試験観点": "フィルタリング機能の動作確認", "試験対象": "fetchFilteredTasks関数のフィルタリング処理", "試験手順": "1. フィルタ条件を指定してfetchFilteredTasksを呼び出し", "確認項目": "- 指定条件に一致するタスクのみが取得されること"}, {"テストケース名": "正常系: ソート処理（タスク名昇順）", "試験観点": "ソート機能の動作確認", "試験対象": "fetchFilteredTasks関数のソート処理", "試験手順": "1. タスク名昇順でfetchFilteredTasksを呼び出し", "確認項目": "- タスクが指定順序でソートされること"}, {"テストケース名": "正常系: ページング処理", "試験観点": "ページング機能の動作確認", "試験対象": "fetchFilteredTasks関数のページング処理", "試験手順": "1. ページサイズとページ番号を指定してfetchFilteredTasksを呼び出し", "確認項目": "- 指定ページのタスクが正しく取得されること"}, {"テストケース名": "境界条件: 空のフィルター結果", "試験観点": "フィルター結果なしの境界条件確認", "試験対象": "fetchFilteredTasks関数の境界値処理", "試験手順": "1. 該当データがないフィルタ条件でfetchFilteredTasksを呼び出し", "確認項目": "- 空の結果が返されること"}, {"テストケース名": "正常系: LOV値変換処理", "試験観点": "LOV値変換機能の動作確認", "試験対象": "fetchFilteredTasks関数のLOV値変換処理", "試験手順": "1. LOV値を含むタスクでfetchFilteredTasksを呼び出し", "確認項目": "- LOV値が正しく変換されること"}, {"テストケース名": "正常系: 基本的な総ページ数計算", "試験観点": "基本的なページ数計算機能の動作確認", "試験対象": "fetchTasksPages関数のページ数計算ロジック", "試験手順": "1. 基本的な条件でfetchTasksPagesを呼び出し", "確認項目": "- 正しいページ数が計算されること"}, {"テストケース名": "正常系: フィルタリング適用時の総ページ数計算", "試験観点": "フィルタリング適用時のページ数計算確認", "試験対象": "fetchTasksPages関数のフィルタリング機能", "試験手順": "1. フィルタ条件を指定してfetchTasksPagesを呼び出し", "確認項目": "- フィルタリング後の正しいページ数が計算されること"}, {"テストケース名": "境界条件: データなし時の総ページ数", "試験観点": "データなし時の境界条件確認", "試験対象": "fetchTasksPages関数の境界値処理", "試験手順": "1. データが存在しない状態でfetchTasksPagesを呼び出し", "確認項目": "- ページ数が0となること"}, {"テストケース名": "正常系: キャッシュリフレッシュフラグの動作", "試験観点": "キャッシュリフレッシュ機能の動作確認", "試験対象": "fetchTasksPages関数のキャッシュ制御機能", "試験手順": "1. キャッシュリフレッシュフラグを有効にしてfetchTasksPagesを呼び出し", "確認項目": "- キャッシュリフレッシュが正しく動作すること"}]}, "servers": {"displayName": "servers", "testCases": [{"テストケース名": "指定されたフィルタ条件とページサイズに基づいてページ数を計算する", "試験観点": "ページ数計算機能の動作確認", "試験対象": "fetchServersPages関数のページ数計算ロジック", "試験手順": "1. 指定されたフィルタ条件とページサイズでfetchServersPagesを呼び出し", "確認項目": "- 正しいページ数が計算されること"}, {"テストケース名": "データベース接続エラーが発生した場合、適切なエラーメッセージを返す", "試験観点": "データベース接続エラー時の例外処理確認", "試験対象": "fetchServersPages関数のエラーハンドリング", "試験手順": "1. PrismaClientInitializationErrorを発生させる", "確認項目": "- 適切なエラーメッセージ（EMEC0006）がthrowされること"}, {"テストケース名": "予期せぬサーバーエラーが発生した場合、一般的なエラーメッセージを返す", "試験観点": "予期せぬエラー時の例外処理確認", "試験対象": "fetchServersPages関数の一般的なエラーハンドリング", "試験手順": "1. 一般的なErrorを発生させる", "確認項目": "- 一般的なエラーメッセージ（EMEC0001）がthrowされること"}, {"テストケース名": "フィルタ条件がサーバータイプと一致しない場合の結果を検証する", "試験観点": "フィルタ条件不一致時の動作確認", "試験対象": "fetchFilteredServers関数のフィルタリング機能", "試験手順": "1. サーバータイプと一致しないフィルタ条件を指定", "確認項目": "- 条件に一致しないデータが除外されること"}, {"テストケース名": "フィルタ条件がサーバータイプと一致する場合の結果を検証する", "試験観点": "フィルタ条件一致時の動作確認", "試験対象": "fetchFilteredServers関数のフィルタリング機能", "試験手順": "1. サーバータイプと一致するフィルタ条件を指定", "確認項目": "- 条件に一致するデータが正しく抽出されること"}, {"テストケース名": "データベースエラーが発生した場合の処理を検証する", "試験観点": "データベースエラー時の例外処理確認", "試験対象": "fetchFilteredServers関数のエラーハンドリング", "試験手順": "1. PrismaClientInitializationErrorを発生させる", "確認項目": "- 適切なエラーメッセージ（EMEC0006）がthrowされること"}, {"テストケース名": "予期せぬエラーが発生した場合の処理を検証する", "試験観点": "予期せぬエラー時の例外処理確認", "試験対象": "fetchFilteredServers関数の一般的なエラーハンドリング", "試験手順": "1. 一般的なErrorを発生させる", "確認項目": "- 一般的なエラーメッセージ（EMEC0007）がthrowされること"}, {"テストケース名": "指定されたサーバーIDに基づいて詳細情報を取得する", "試験観点": "サーバー詳細情報取得機能の動作確認", "試験対象": "getServerDetailsForTask関数の正常系処理", "試験手順": "1. 有効なサーバーIDを指定してgetServerDetailsForTaskを呼び出し", "確認項目": "- 指定されたサーバーの詳細情報が正しく取得されること"}, {"テストケース名": "データベースエラーが発生した場合、適切なエラーメッセージを返す", "試験観点": "データベースエラー時の例外処理確認", "試験対象": "getServerDetailsForTask関数のエラーハンドリング", "試験手順": "1. データベースエラーを発生させる", "確認項目": "- 適切なエラーメッセージ（EMEC0007）がthrowされること"}, {"テストケース名": "有効なプランを持つユーザーの場合、エクスポート権限がtrueとなる", "試験観点": "有効プラン保持ユーザーの権限確認", "試験対象": "fetchUserCanExportOplog関数の権限判定ロジック", "試験手順": "1. 有効なプランを持つユーザーでfetchUserCanExportOplogを呼び出し", "確認項目": "- エクスポート権限がtrueとなること"}, {"テストケース名": "無効なプランを持つユーザーの場合、エクスポート権限がfalseとなる", "試験観点": "無効プラン保持ユーザーの権限確認", "試験対象": "fetchUserCanExportOplog関数の権限判定ロジック", "試験手順": "1. 無効なプランを持つユーザーでfetchUserCanExportOplogを呼び出し", "確認項目": "- エクスポート権限がfalseとなること"}, {"テストケース名": "プランが未設定の場合、エクスポート権限がfalseとなる", "試験観点": "プラン未設定ユーザーの権限確認", "試験対象": "fetchUserCanExportOplog関数の権限判定ロジック", "試験手順": "1. プランが未設定のユーザーでfetchUserCanExportOplogを呼び出し", "確認項目": "- エクスポート権限がfalseとなること"}, {"テストケース名": "異常系: ライセンス情報が見つからない場合、エクスポート権限がfalseとなる", "試験観点": "ライセンス情報が見つからない場合の権限判定", "試験対象": "fetchUserCanExportOplog のライセンス未存在時の処理", "試験手順": "1. prismaMock.license.findUniqueがnullを返すようにモック設定\r\n2. fetchUserCanExportOplogを実行\r\n3. falseが返されることを確認", "確認項目": "- ライセンス情報が見つからない場合にfalseが返されること"}, {"テストケース名": "境界条件: LOV設定が無効化されている場合、エクスポート権限がfalseとなる", "試験観点": "LOV設定が無効化されている場合の権限判定", "試験対象": "fetchUserCanExportOplog のLOV無効化時の処理", "試験手順": "1. LOVエントリのisEnabledがfalseの場合をモック設定\r\n2. fetchUserCanExportOplogを実行\r\n3. falseが返されることを確認", "確認項目": "- LOV設定が無効化されている場合にfalseが返されること"}, {"テストケース名": "異常系: データベースエラー時は例外が投げられる", "試験観点": "データベースエラー時の権限判定処理", "試験対象": "fetchUserCanExportOplog のDB例外処理", "試験手順": "1. prismaMock.license.findUniqueがエラーを投げるようにモック設定\r\n2. fetchUserCanExportOplogを実行\r\n3. 例外が適切に処理されることを確認", "確認項目": "- データベースエラーが発生した場合に適切に例外が投げられること"}, {"テストケース名": "境界条件: 特殊文字を含むフィルター条件での検索", "試験観点": "特殊文字を含むフィルター条件での検索", "試験対象": "ServerDataServers.fetchFilteredServers のフィルタリング処理", "試験手順": "1. 特殊文字を含むサーバー名のデータを設定\r\n2. 特殊文字を含むフィルター条件で検索を実行\r\n3. 正しくマッチングされることを確認", "確認項目": "- 特殊文字を含むフィルター条件が正しく動作すること\r\n- 大文字小文字を区別しない検索が動作すること"}, {"テストケース名": "境界条件: 極端に大きなページサイズでの処理", "試験観点": "極端に大きなページサイズでの処理", "試験対象": "ServerDataServers.fetchFilteredServers のページング処理", "試験手順": "1. 大量のサーバーデータを設定\r\n2. 極端に大きなページサイズで検索を実行\r\n3. 全データが正しく返されることを確認", "確認項目": "- 極端に大きなページサイズでも正しく動作すること\r\n- メモリ使用量が適切であること"}, {"テストケース名": "境界条件: ページ番号が総ページ数を超える場合は空配列を返す", "試験観点": "ページ番号が総ページ数を超える場合の処理", "試験対象": "ServerDataServers.fetchFilteredServers のページング境界処理", "試験手順": "1. 少量のサーバーデータを設定\r\n2. 存在しないページ番号で検索を実行\r\n3. 空配列が返されることを確認", "確認項目": "- 存在しないページ番号の場合に空配列が返されること\r\n- エラーが発生しないこと"}]}}}, "ui": {"displayName": "ui", "subCategories": {"confirm-modal": {"displayName": "confirm-modal", "testCases": [{"テストケース名": "正常系: モーダル非表示時はnullを返す", "試験観点": "モーダル非表示時の動作確認", "試験対象": "ConfirmModal コンポーネントの表示制御", "試験手順": "1. is<PERSON>pen=falseでコンポーネントをレンダリング", "確認項目": "- モーダルが表示されないこと"}, {"テストケース名": "正常系: モーダル基本UI要素の表示", "試験観点": "モーダル基本UI要素の表示確認", "試験対象": "ConfirmModal コンポーネントの基本UI表示機能", "試験手順": "1. 正常なpropsでコンポーネントをレンダリング", "確認項目": "- タイトルが表示されること\r\n- メッセージが表示されること\r\n- OKボタンが表示されること\r\n- キャンセルボタンが表示されること"}, {"テストケース名": "正常系: カスタムボタンテキストの表示", "試験観点": "カスタムボタンテキストの表示確認", "試験対象": "ConfirmModal コンポーネントのカスタムボタンテキスト表示", "試験手順": "1. カスタムボタンテキストを指定してレンダリング", "確認項目": "- カスタムボタンテキストが表示されること"}, {"テストケース名": "正常系: 確認ボタンクリック時の動作", "試験観点": "確認ボタンクリック時の動作確認", "試験対象": "ConfirmModal コンポーネントの確認機能", "試験手順": "1. 確認ボタンをクリック", "確認項目": "- onConfirmコールバックが呼び出されること"}, {"テストケース名": "正常系: キャンセルボタンクリック時の動作", "試験観点": "キャンセルボタンクリック時の動作確認", "試験対象": "ConfirmModal コンポーネントのキャンセル機能", "試験手順": "1. キャンセルボタンをクリック", "確認項目": "- onCancelコールバックが呼び出されること"}, {"テストケース名": "必須テキストがすべて表示されること", "試験観点": "必須テキストの表示確認", "試験対象": "ConfirmModal コンポーネントの必須テキスト表示", "試験手順": "1. コンポーネントをレンダリング\r\n2. 必須テキストがすべて表示されていることを確認", "確認項目": "- 必須テキストがすべて表示されていること"}, {"テストケース名": "許可されたテキストのみ表示されること", "試験観点": "許可されたテキストのみ表示確認", "試験対象": "ConfirmModal コンポーネントのテキスト表示制御", "試験手順": "1. コンポーネントをレンダリング\r\n2. 許可されたテキストのみが表示されていることを確認", "確認項目": "- 許可されていないテキストが表示されていないこと"}]}, "error-common-modal": {"displayName": "error-common-modal", "testCases": [{"テストケース名": "正常系: モーダル非表示時は何も表示されない", "試験観点": "モーダル非表示時の動作確認", "試験対象": "ErrorCommonModal コンポーネントの表示制御", "試験手順": "1. is<PERSON>pen=falseでコンポーネントをレンダリング", "確認項目": "- モーダルが表示されないこと"}, {"テストケース名": "正常系: エラーメッセージ表示時のUI要素", "試験観点": "エラーメッセージ表示時のUI要素確認", "試験対象": "ErrorCommonModal コンポーネントのエラー表示機能", "試験手順": "1. エラーメッセージを指定してレンダリング", "確認項目": "- タイトルが表示されること\r\n- エラーメッセージが表示されること\r\n- 閉じるボタンが表示されること"}, {"テストケース名": "正常系: 情報メッセージ表示時のUI要素", "試験観点": "情報メッセージ表示時のUI要素確認", "試験対象": "ErrorCommonModal コンポーネントの情報表示機能", "試験手順": "1. 情報メッセージを指定してレンダリング", "確認項目": "- タイトルが表示されること\r\n- 情報メッセージが表示されること\r\n- 閉じるボタンが表示されること"}, {"テストケース名": "正常系: 閉じるボタンクリック時の動作", "試験観点": "閉じるボタンクリック時の動作確認", "試験対象": "ErrorCommonModal コンポーネントの閉じる機能", "試験手順": "1. 閉じるボタンをクリック", "確認項目": "- onCloseコールバックが呼び出されること"}, {"テストケース名": "必須テキストがすべて表示されること", "試験観点": "必須テキストの表示確認", "試験対象": "ErrorCommonModal コンポーネントの必須テキスト表示", "試験手順": "1. コンポーネントをレンダリング\r\n2. 必須テキストがすべて表示されていることを確認", "確認項目": "- 必須テキストがすべて表示されていること"}, {"テストケース名": "許可されたテキストのみ表示されること", "試験観点": "許可されたテキストのみ表示確認", "試験対象": "ErrorCommonModal コンポーネントのテキスト表示制御", "試験手順": "1. コンポーネントをレンダリング\r\n2. 許可されたテキストのみが表示されていることを確認", "確認項目": "- 許可されていないテキストが表示されていないこと"}]}, "actions-dropdown-wrapper": {"displayName": "actions-dropdown-wrapper", "testCases": [{"テストケース名": "正常系: 初期読み込み状態でSpinnerを表示する", "試験観点": "初期読み込み状態でのSpinner表示", "試験対象": "ServerActionsDropdownWrapper コンポーネントの初期読み込み状態", "試験手順": "1. コンポーネントをレンダリング\r\n2. 初期状態でSpinnerが表示されることを確認", "確認項目": "- 初期状態でSpinnerが表示されること\r\n- ServerActionsDropdownが表示されないこと"}, {"テストケース名": "正常系: 読み込み完了後にServerActionsDropdownを表示する", "試験観点": "読み込み完了後のServerActionsDropdown表示", "試験対象": "ServerActionsDropdownWrapper コンポーネントの読み込み完了状態", "試験手順": "1. コンポーネントをレンダリング\r\n2. 100ms経過後にServerActionsDropdownが表示されることを確認", "確認項目": "- 読み込み完了後にServerActionsDropdownが表示されること\r\n- Spinnerが表示されないこと"}, {"テストケース名": "正常系: ドロップダウンが表示されない場合に透明プレースホルダーを表示する", "試験観点": "ドロップダウンが表示されない場合の透明プレースホルダー表示", "試験対象": "ServerActionsDropdownWrapper コンポーネントの透明プレースホルダー表示機能", "試験手順": "1. ドロップダウンが表示されない条件でコンポーネントをレンダリング\r\n2. 100ms経過後に透明プレースホルダーが表示されることを確認", "確認項目": "- ドロップダウンが表示されない場合に透明プレースホルダーが表示されること\r\n- 固定サイズが適用されていること"}, {"テストケース名": "正常系: Spinnerプレースホルダーに固定サイズが適用されている", "試験観点": "Spinnerプレースホルダーの固定サイズ確認", "試験対象": "ServerActionsDropdownWrapper コンポーネントのSpinnerプレースホルダー", "試験手順": "1. コンポーネントをレンダリング\r\n2. Spinnerプレースホルダーに固定サイズクラスが適用されていることを確認", "確認項目": "- Spinnerプレースホルダーに正しい固定サイズクラスが適用されていること"}]}, "management-definition-import-modal": {"displayName": "management-definition-import-modal", "testCases": [{"テストケース名": "正常系: モーダル非表示時はnullを返す", "試験観点": "モーダル非表示時の動作確認", "試験対象": "ManagementDefinitionImportModal コンポーネントの表示制御", "試験手順": "1. is<PERSON>pen=falseでコンポーネントをレンダリング", "確認項目": "- モーダルが表示されないこと"}, {"テストケース名": "正常系: モーダル基本UI要素の表示", "試験観点": "モーダル基本UI要素の表示確認", "試験対象": "ManagementDefinitionImportModal コンポーネントの基本UI表示機能", "試験手順": "1. 正常なpropsでコンポーネントをレンダリング", "確認項目": "- タイトルが表示されること\r\n- 閉じるボタンが表示されること\r\n- ファイルアップロード領域が表示されること\r\n- 必須入力ヒントが表示されること\r\n- OKボタンとキャンセルボタンが表示されること"}, {"テストケース名": "正常系: ×ボタンクリック時にモーダルが閉じる", "試験観点": "閉じるボタンクリック時の動作確認", "試験対象": "ManagementDefinitionImportModal コンポーネントの閉じる機能", "試験手順": "1. 閉じるボタンをクリック", "確認項目": "- onCloseコールバックが呼び出されること"}, {"テストケース名": "正常系: キャンセルボタンクリック時にモーダルが閉じる", "試験観点": "キャンセルボタンクリック時の動作確認", "試験対象": "ManagementDefinitionImportModal コンポーネントのキャンセル機能", "試験手順": "1. キャンセルボタンをクリック", "確認項目": "- onCloseコールバックが呼び出されること"}, {"テストケース名": "異常系: ファイル未選択時のバリデーションエラー", "試験観点": "必須入力バリデーション（ファイル未選択）", "試験対象": "ManagementDefinitionImportModal コンポーネントの入力検証機能", "試験手順": "1. ファイルを選択せずにOKボタンをクリック", "確認項目": "- バリデーションエラーが発生すること\r\n- onSubmitコールバックが呼び出されないこと"}, {"テストケース名": "異常系: 無効なファイル形式のバリデーションエラー", "試験観点": "ファイル形式バリデーション", "試験対象": "ManagementDefinitionImportModal コンポーネントのファイル形式検証機能", "試験手順": "1. CSV以外のファイルを選択\r\n2. OKボタンをクリック", "確認項目": "- ファイル形式バリデーションエラーが発生すること\r\n- onSubmitコールバックが呼び出されないこと"}, {"テストケース名": "正常系: CSVファイル指示文の表示", "試験観点": "CSVファイル指示文の表示確認", "試験対象": "ManagementDefinitionImportModal コンポーネントの指示文表示機能", "試験手順": "1. モーダルをレンダリング", "確認項目": "- CSVファイル指示文が正しく表示されること"}, {"テストケース名": "正常系: ファイル入力のCSV受け入れ確認", "試験観点": "ファイル入力のaccept属性確認", "試験対象": "ManagementDefinitionImportModal コンポーネントのファイル入力制御", "試験手順": "1. モーダルをレンダリング", "確認項目": "- ファイル入力がCSVファイルを受け入れること"}, {"テストケース名": "必須テキストがすべて表示されること", "試験観点": "必須テキストの表示確認", "試験対象": "ManagementDefinitionImportModal コンポーネントの必須テキスト表示", "試験手順": "1. コンポーネントをレンダリング\r\n2. 必須テキストがすべて表示されていることを確認", "確認項目": "- 必須テキストがすべて表示されていること"}, {"テストケース名": "許可されたテキストのみ表示されること", "試験観点": "許可されたテキストのみ表示確認", "試験対象": "ManagementDefinitionImportModal コンポーネントのテキスト表示制御", "試験手順": "1. コンポーネントをレンダリング\r\n2. 許可されたテキストのみが表示されていることを確認", "確認項目": "- 許可されていないテキストが表示されていないこと"}, {"テストケース名": "初期値の動的更新が正しく動作すること", "試験観点": "初期値の動的更新確認", "試験対象": "initialValuesプロパティの変更時の状態更新", "試験手順": "1. 初期値なしでコンポーネントをレンダリング\r\n2. 初期値ありでpropsを更新\r\n3. 初期値なしでpropsを更新", "確認項目": "- 初期値が設定された時に状態が更新されること\r\n- 初期値がクリアされた時に状態がクリアされること"}]}, "operation-log-export-modal": {"displayName": "operation-log-export-modal", "testCases": [{"テストケース名": "正常系: モーダル非表示時はnullを返す", "試験観点": "モーダル非表示時の動作確認", "試験対象": "OperationLogExportModal コンポーネントの表示制御", "試験手順": "1. is<PERSON>pen=falseでコンポーネントをレンダリング", "確認項目": "- モーダルが表示されないこと"}, {"テストケース名": "正常系: モーダル基本UI要素の表示", "試験観点": "モーダル基本UI要素の表示確認", "試験対象": "OperationLogExportModal コンポーネントの基本UI表示機能", "試験手順": "1. 正常なpropsでコンポーネントをレンダリング", "確認項目": "- タイトルが表示されること\r\n- 閉じるボタンが表示されること\r\n- 期間制限説明が表示されること\r\n- 開始日・終了日ラベルが表示されること\r\n- 必須マークが表示されること\r\n- OKボタンとキャンセルボタンが表示されること\r\n- 必須入力ヒントが表示されること"}, {"テストケース名": "正常系: 日付入力フィールドの初期表示", "試験観点": "日付入力フィールドの初期表示確認", "試験対象": "OperationLogExportModal コンポーネントの日付入力フィールド表示", "試験手順": "1. 正常なpropsでコンポーネントをレンダリング", "確認項目": "- 開始日入力フィールドにプレースホルダーが表示されること\r\n- 終了日入力フィールドにプレースホルダーが表示されること\r\n- 日付選択アイコンが表示されること"}, {"テストケース名": "正常系: ×ボタンクリック時にモーダルが閉じる", "試験観点": "×ボタンクリック時の動作確認", "試験対象": "OperationLogExportModal コンポーネントの閉じる機能", "試験手順": "1. ×ボタンをクリック", "確認項目": "- onCloseコールバックが呼び出されること"}, {"テストケース名": "正常系: キャンセルボタンクリック時にモーダルが閉じる", "試験観点": "キャンセルボタンクリック時の動作確認", "試験対象": "OperationLogExportModal コンポーネントのキャンセル機能", "試験手順": "1. キャンセルボタンをクリック", "確認項目": "- onCloseコールバックが呼び出されること"}, {"テストケース名": "異常系: 開始日未入力時のバリデーションエラー", "試験観点": "必須入力バリデーション（開始日未入力）", "試験対象": "OperationLogExportModal コンポーネントの入力検証機能", "試験手順": "1. 開始日を未入力のままOKボタンをクリック", "確認項目": "- EMEC0016エラーメッセージが表示されること\r\n- onSubmitコールバックが呼び出されないこと"}, {"テストケース名": "異常系: 終了日未入力時のバリデーションエラー", "試験観点": "必須入力バリデーション（終了日未入力）", "試験対象": "OperationLogExportModal コンポーネントの入力検証機能", "試験手順": "1. 開始日のみ入力し、終了日を未入力のままOKボタンをクリック", "確認項目": "- バリデーションエラーが発生すること\r\n- onSubmitコールバックが呼び出されないこと"}, {"テストケース名": "異常系: 終了日が開始日より前の場合のバリデーションエラー", "試験観点": "日付順序バリデーション", "試験対象": "OperationLogExportModal コンポーネントの日付順序検証機能", "試験手順": "1. 終了日を開始日より前の日付に設定\r\n2. OKボタンをクリック", "確認項目": "- EMEC0024エラーメッセージが表示されること\r\n- onSubmitコールバックが呼び出されないこと"}, {"テストケース名": "異常系: 最大日数超過時のバリデーションエラー", "試験観点": "最大日数超過バリデーション", "試験対象": "OperationLogExportModal コンポーネントの日数制限検証機能", "試験手順": "1. 最大日数を超える期間を設定\r\n2. OKボタンをクリック", "確認項目": "- EMEC0020エラーメッセージが表示されること\r\n- onSubmitコールバックが呼び出されないこと"}, {"テストケース名": "正常系: 有効な日付入力時の送信", "試験観点": "正常な日付入力時の送信処理", "試験対象": "OperationLogExportModal コンポーネントの正常送信機能", "試験手順": "1. 有効な開始日と終了日を入力\r\n2. OKボタンをクリック", "確認項目": "- onSubmitコールバックが正しいパラメータで呼び出されること\r\n注記：確認モーダルはactions-dropdown.tsxで統一管理されるため、ここでは確認画面のテストは行わない。"}, {"テストケース名": "正常系: キャンセルボタンクリック時の動作", "試験観点": "キャンセルボタンの動作", "試験対象": "OperationLogExportModal コンポーネントのキャンセル機能", "試験手順": "1. 有効な日付を入力\r\n2. キャンセルボタンをクリック", "確認項目": "- onCloseコールバックが呼び出されること\r\n- onSubmitコールバックが呼び出されないこと\r\n注記：確認モーダルはactions-dropdown.tsxで統一管理されるため、ここでは確認画面のテストは行わない。"}, {"テストケース名": "パラメータ送信の検証", "試験観点": "パラメータ送信の検証", "試験対象": "onSubmitに渡されるパラメータ内容", "試験手順": "1. 有効な日付範囲を設定してOKボタンをクリック", "確認項目": "- onSubmitに正しい日付パラメータが渡されること\r\n注記：確認モーダルはactions-dropdown.tsxで統一管理されるため、ここでは確認画面のテストは行わない。"}, {"テストケース名": "日付表示フォーマットの検証", "試験観点": "日付表示フォーマットの検証", "試験対象": "formatDateToSlash関数の出力形式", "試験手順": "1. 日付文字列を変換", "確認項目": "- 出力形式が「YYYY/MM/DD」（スラッシュ区切り）であること"}, {"テストケース名": "確認画面キャンセル時の動作検証", "試験観点": "確認画面キャンセル時の動作検証", "試験対象": "確認画面キャンセル時の状態遷移", "試験手順": "1. 確認画面でキャンセルボタンをクリック", "確認項目": "- onCloseコールバックが呼ばれないこと（モーダル全体を閉じてはいけない）\r\n- 入力画面に戻ること"}, {"テストケース名": "必須テキストがすべて表示されること", "試験観点": "必須テキストの表示確認", "試験対象": "OperationLogExportModal コンポーネントの必須テキスト表示", "試験手順": "1. コンポーネントをレンダリング\r\n2. 必須テキストがすべて表示されていることを確認", "確認項目": "- 必須テキストがすべて表示されていること"}, {"テストケース名": "許可されたテキストのみ表示されること", "試験観点": "許可されたテキストのみ表示確認", "試験対象": "OperationLogExportModal コンポーネントのテキスト表示制御", "試験手順": "1. コンポーネントをレンダリング\r\n2. 許可されたテキストのみが表示されていることを確認", "確認項目": "- 許可されていないテキストが表示されていないこと"}, {"テストケース名": "日付表示エリアクリック時にinputのshowPickerまたはclickが呼ばれること", "試験観点": "日付表示エリアクリック時のinput要素showPicker/click呼び出し確認", "試験対象": "日付選択器のクリック連携機能", "試験手順": "1. コンポーネントをレンダリング\r\n2. 開始日表示エリアをクリック\r\n3. 隠れたinput要素のshowPicker()またはclick()メソッドが呼ばれることを確認", "確認項目": "- 表示エリアクリック時にinput.showPicker()またはinput.click()が呼ばれること"}, {"テストケース名": "初期値の動的更新が正しく動作すること", "試験観点": "初期値の動的更新確認", "試験対象": "initialValuesプロパティの変更時の状態更新", "試験手順": "1. 初期値なしでコンポーネントをレンダリング\r\n2. 初期値ありでpropsを更新\r\n3. 初期値なしでpropsを更新", "確認項目": "- 初期値が設定された時に状態が更新されること\r\n- 初期値がクリアされた時に状態がクリアされること"}]}, "table": {"displayName": "table", "testCases": [{"テストケース名": "サーバーデータを正しくレンダリングすること", "試験観点": "サーバーデータのレンダリング。", "試験対象": "サーバーデータのテーブル表示。", "試験手順": "1. ServersTableを初期パラメータでレンダリングする。\r\n2. サーバ名・データ行が正しく表示されていることを確認する。", "確認項目": "- サーバ名ヘッダーが表示されること。\r\n- 各サーバーデータ行が表示されること。"}, {"テストケース名": "サーバー名ヘッダーをクリックでソート順が切り替わること", "試験観点": "ソート機能。", "試験対象": "サーバー名ヘッダークリック時のソート挙動。", "試験手順": "1. ServersTableをレンダリングする。\r\n2. サーバ名ヘッダーをクリックする。\r\n3. ソートコールバックやUI変化を確認する（実装に応じて）。", "確認項目": "- サーバ名ヘッダークリックでソート順が切り替わること。"}, {"テストケース名": "ページ切り替え時に正しいサーバーデータが表示されること", "試験観点": "ページング機能。", "試験対象": "ページ切り替え時のデータ表示。", "試験手順": "1. 2ページ目のデータをモックし、ServersTableをレンダリングする。\r\n2. 2ページ目のサーバーデータが表示されることを確認する。", "確認項目": "- 2ページ目のサーバーデータが正しく表示されること。"}, {"テストケース名": "typeCodeがラベルに正しくマッピングされて表示されること", "試験観点": "LOVマッピング。", "試験対象": "typeCodeからラベルへのマッピング表示。", "試験手順": "1. ServersTableをレンダリングする。\r\n2. typeCodeがラベルに変換されて表示されていることを確認する。", "確認項目": "- typeCodeが正しいラベルで表示されること。"}, {"テストケース名": "サーバーデータが空の場合、適切なメッセージが表示されること", "試験観点": "空データ時の表示。", "試験対象": "サーバーデータが空の場合のUI表示。", "試験手順": "1. サーバーデータが空の状態でServersTableをレンダリングする。\r\n2. 空データ用のメッセージが表示されることを確認する。", "確認項目": "- 「該当するサーバーがありません」メッセージが表示されること。"}, {"テストケース名": "サーバー名が非常に長い場合でも正しく表示されること", "試験観点": "長いサーバー名の表示。", "試験対象": "長いサーバー名のUI表示。", "試験手順": "1. サーバー名が非常に長いデータでServersTableをレンダリングする。\r\n2. 長いサーバー名が正しく表示されることを確認する。", "確認項目": "- 長いサーバー名が正しく表示されること。"}, {"テストケース名": "管理画面列が正しい超リンクで表示されること", "試験観点": "管理画面列のリンク表示。", "試験対象": "管理画面列のaタグ・href・target属性。", "試験手順": "1. ServersTableをレンダリングする。\r\n2. 管理画面列がaタグで表示されていることを確認する。\r\n3. href属性・target属性が正しいことを確認する。", "確認項目": "- 管理画面列がaタグで表示されていること。\r\n- href属性・target属性が正しいこと。"}, {"テストケース名": "必須表頭がすべて表示されること", "試験観点": "必須表頭の表示確認", "試験対象": "ServersTable コンポーネントの表頭表示", "試験手順": "1. コンポーネントをレンダリング\r\n2. 必須表頭がすべて表示されていることを確認", "確認項目": "- 必須表頭がすべて表示されていること"}, {"テストケース名": "許可されたテキストのみ表示されること", "試験観点": "許可されたテキストのみ表示確認", "試験対象": "ServersTable コンポーネントのテキスト表示制御", "試験手順": "1. コンポーネントをレンダリング\r\n2. 許可されたテキストのみが表示されていることを確認", "確認項目": "- 許可されていないテキストが表示されていないこと"}, {"テストケース名": "タスク列と下拉メニューの表示条件が正しいこと", "試験観点": "タスク列とドロップダウンメニューの表示条件確認", "試験対象": "ServersTableコンポーネントのタスク関連UI表示機能", "試験手順": "1. ServersTableコンポーネントをレンダリング\r\n2. タスク列の表示を確認\r\n3. ドロップダウンメニューの表示条件を検証", "確認項目": "- タスク列が正しく表示されること\r\n- ドロップダウンメニューが適切な条件で表示されること"}, {"テストケース名": "タスクボタンが正常に表示されること", "試験観点": "タスクボタンの正常表示。", "試験対象": "サーバ行のタスクボタン表示。", "試験手順": "1. ServersTableをレンダリングする。\r\n2. 「タスクを選択」ボタンが表示されることを確認する。", "確認項目": "- 「タスクを選択」ボタンが表示されること。\r\n注記：isTaskRunning状態制御は削除され、モーダルウィンドウが処理の前置上下文として機能する。"}, {"テストケース名": "境界条件: 操作ログエクスポート権限がない場合のUI制御", "試験観点": "操作ログエクスポート権限がない場合のUI制御", "試験対象": "canExportOplog=falseの場合のタスクメニュー表示制御", "試験手順": "1. canExportOplog=falseでServersTableをレンダリング\r\n2. 操作ログエクスポートメニューが表示されないことを確認\r\n3. 他のメニューは正常に表示されることを確認", "確認項目": "- 操作ログエクスポートメニューが非表示になること\r\n- 管理項目定義のインポート・エクスポートは表示されること"}, {"テストケース名": "境界条件: HIBUN_CONSOLEサーバーではタスクメニューが非表示", "試験観点": "サーバー種別による表示制御", "試験対象": "HIBUN_CONSOLEサーバーでのタスクメニュー非表示", "試験手順": "1. HIBUN_CONSOLEタイプのサーバーデータでServersTableをレンダリング\r\n2. タスクメニューが表示されないことを確認", "確認項目": "- HIBUN_CONSOLEサーバーではタスクメニューが非表示になること"}, {"テストケース名": "境界条件: 極端に長いURL表示の処理", "試験観点": "極端に長いURL表示の処理", "試験対象": "長いURLの表示とリンク機能", "試験手順": "1. 極端に長いURLを持つサーバーデータでServersTableをレンダリング\r\n2. URLが正しく表示されることを確認\r\n3. リンク機能が正常に動作することを確認", "確認項目": "- 長いURLが正しく表示されること\r\n- href属性が正しく設定されること"}, {"テストケース名": "境界条件: 特殊文字を含むサーバー名の表示", "試験観点": "特殊文字を含むサーバー名の表示", "試験対象": "特殊文字を含むサーバー名の正しい表示", "試験手順": "1. 特殊文字を含むサーバー名のデータでServersTableをレンダリング\r\n2. 特殊文字が正しくエスケープされて表示されることを確認", "確認項目": "- 特殊文字が正しく表示されること\r\n- XSS攻撃が防がれること"}, {"テストケース名": "機能テスト: ソート機能の詳細動作確認", "試験観点": "ソート機能の詳細動作確認", "試験対象": "各列のソート機能とソートアイコンの表示", "試験手順": "1. 複数のサーバーデータでServersTableをレンダリング\r\n2. 各列ヘッダーのクリック動作を確認\r\n3. ソート順の変更が正しく反映されることを確認", "確認項目": "- 各列でソートが正しく動作すること\r\n- ソートアイコンが適切に表示されること"}, {"テストケース名": "UI/UX: レスポンシブデザインでの表示確認", "試験観点": "レスポンシブデザインでの表示確認", "試験対象": "異なる画面サイズでのテーブル表示", "試験手順": "1. ServersTableをレンダリング\r\n2. テーブルのレスポンシブ要素を確認\r\n3. スクロールバーの表示制御を確認", "確認項目": "- テーブルがレスポンシブに表示されること\r\n- 必要に応じてスクロールバーが表示されること"}, {"テストケース名": "機能テスト: actions列はソート機能が無効化されていること", "試験観点": "actions列のソート機能無効化確認", "試験対象": "タスク列（actions）のソート機能", "試験手順": "1. ServersTableをレンダリング\r\n2. タスク列ヘッダーの要素を取得\r\n3. ソート関連のクラスやイベントが設定されていないことを確認", "確認項目": "- タスク列ヘッダーにcursor-pointerクラスが設定されていないこと\r\n- タスク列ヘッダーにhover:opacity-80クラスが設定されていないこと\r\n- タスク列ヘッダーにソートアイコンが表示されないこと"}, {"テストケース名": "機能テスト: ソート可能列とソート不可列が正しく区別されること", "試験観点": "ソート可能列とソート不可列の区別確認", "試験対象": "各列のソート機能の有効/無効状態", "試験手順": "1. ServersTableをレンダリング\r\n2. 各列ヘッダーのソート機能状態を確認", "確認項目": "- サーバ名、種別、管理画面列はソート機能が有効であること\r\n- タスク列はソート機能が無効であること"}, {"テストケース名": "正常系: テーブル基本構造と列ヘッダーが正しく表示される", "試験観点": "設計文書項目1-22のテーブル基本構造確認", "試験対象": "設計文書「02-画面項目定義.md」タスク一覧項目1-22", "試験手順": "1. TasksTableをレンダリング\r\n2. テーブル構造と列ヘッダーを確認", "確認項目": "- 項目1: タスク一覧エリア（table）が表示されること\r\n- 項目2,5,8,11,14,17,20,23: 各列ヘッダーが正しく表示されること\r\n- 項目4,7,10,13,16,19,22: 各データが正しく表示されること"}, {"テストケース名": "正常系: タスク名が正しいフォーマットで表示される", "試験観点": "設計文書項目4のタスク名フォーマット確認", "試験対象": "設計文書「02-画面項目定義.md」項目4のタスク名フォーマット", "試験手順": "1. TasksTableをレンダリング\r\n2. タスク名が正しいフォーマットで表示されることを確認", "確認項目": "- フォーマット：{ServerName}-{タスク種別}-{YYYYMMDDHHmmss}\r\n- 各タスクのタスク名が正しく表示されること"}, {"テストケース名": "正常系: 日時が正しいフォーマットで表示される", "試験観点": "設計文書項目10,13の日時フォーマット確認", "試験対象": "設計文書「02-画面項目定義.md」項目10,13の日時表示", "試験手順": "1. TasksTableをレンダリング\r\n2. 開始日時・終了日時が正しいフォーマットで表示されることを確認", "確認項目": "- フォーマット：YYYY/MM/DD hh:mm:ss\r\n- ユーザーのタイムゾーンで表示されること\r\n- 終了日時がnullの場合は空文字で表示されること"}, {"テストケース名": "正常系: タスク詳細がステータスに応じて動的に表示される", "試験観点": "設計文書項目24のタスク詳細動的表示確認", "試験対象": "設計文書「02-画面項目定義.md」項目24のステータス別表示制御", "試験手順": "1. 各ステータスのタスクでTasksTableをレンダリング\r\n2. ステータスに応じた表示内容を確認", "確認項目": "- 実行待ち（QUEUED）：中止ボタンが表示されること\r\n- 正常終了（COMPLETED_SUCCESS）かつ管理項目定義のエクスポート：ダウンロードリンクが表示されること\r\n- エラー（COMPLETED_ERROR）：エラー詳細表示リンクが表示されること"}, {"テストケース名": "境界条件: データなし時の表示確認", "試験観点": "データなし時の表示確認", "試験対象": "タスクデータが空の場合の表示制御", "試験手順": "1. 空のタスクデータでTasksTableをレンダリング\r\n2. 適切なメッセージが表示されることを確認", "確認項目": "- 「該当するタスクがありません」メッセージが表示されること\r\n- テーブル構造は維持されること"}, {"テストケース名": "正常系: デフォルトソート（開始日時降順）のアイコン表示", "試験観点": "設計文書項目9,12のソートアイコン表示確認", "試験対象": "設計文書「02-画面項目定義.md」項目9,12のソート表示制御", "試験手順": "1. デフォルトソート（開始日時降順）でTasksTableをレンダリング\r\n2. ソートアイコンの表示を確認", "確認項目": "- 項目9: 開始日時ソート（降順のアイコン）が表示されること\r\n- 項目12: 終了日時ソート（非表示）であること\r\n- その他のソートアイコンは非表示であること"}, {"テストケース名": "正常系: 中止ステータスのタスク表示確認", "試験観点": "中止ステータスのタスク表示確認", "試験対象": "設計文書「02-画面項目定義.md」項目24の中止タスク表示", "試験手順": "1. 中止ステータスのタスクを含むデータでレンダリング\r\n2. 中止メッセージが表示されることを確認", "確認項目": "- 中止（CANCELLED）：EMET0004メッセージが表示されること"}, {"テストケース名": "必須テキストがすべて表示されること", "試験観点": "必須テキストの表示確認", "試験対象": "TasksTable コンポーネントの必須テキスト表示", "試験手順": "1. コンポーネントをレンダリング\r\n2. 必須テキストがすべて表示されていることを確認", "確認項目": "- 必須テキストがすべて表示されていること"}, {"テストケース名": "許可されたテキストのみ表示されること", "試験観点": "許可されたテキストのみ表示確認", "試験対象": "TasksTable コンポーネントのテキスト表示制御", "試験手順": "1. コンポーネントをレンダリング\r\n2. 許可されたテキストのみが表示されていることを確認", "確認項目": "- 許可されていないテキストが表示されていないこと"}, {"テストケース名": "機能テスト: actions列はソート機能が無効化されていること", "試験観点": "actions列のソート機能無効化確認", "試験対象": "タスク詳細列（actions）のソート機能", "試験手順": "1. TasksTableをレンダリング\r\n2. タスク詳細列ヘッダーの要素を取得\r\n3. ソート関連のクラスやイベントが設定されていないことを確認", "確認項目": "- タスク詳細列ヘッダーにcursor-pointerクラスが設定されていないこと\r\n- タスク詳細列ヘッダーにhover:opacity-80クラスが設定されていないこと\r\n- タスク詳細列ヘッダーにソートアイコンが表示されないこと"}, {"テストケース名": "機能テスト: ソート可能列とソート不可列が正しく区別されること", "試験観点": "ソート可能列とソート不可列の区別確認", "試験対象": "各列のソート機能の有効/無効状態", "試験手順": "1. TasksTableをレンダリング\r\n2. 各列ヘッダーのソート機能状態を確認", "確認項目": "- タスク名、ステータス、開始日時、終了日時、サーバ名、タスク種別、実行ユーザー列はソート機能が有効であること\r\n- タスク詳細列はソート機能が無効であること"}]}, "actions-modals": {"displayName": "actions-modals", "testCases": [{"テストケース名": "実行待ちタスクの中止操作確認画面表示", "試験観点": "実行待ちタスクに対する中止操作確認画面の表示機能", "試験対象": "TaskActionsModal コンポーネントの中止確認画面表示機能", "試験手順": "1. QUEUED状態のタスクでコンポーネントをレンダリング\r\n2. 中止ボタンをクリック\r\n3. 確認画面の表示内容を検証", "確認項目": "- タイトル「確認」が表示されること\r\n- ×ボタンが活性状態で表示されること\r\n- 確認メッセージが正しく表示されること\r\n- OKボタンが活性状態で表示されること\r\n- キャンセルボタンが活性状態で表示されること"}, {"テストケース名": "タスク中止要求成功時の受付完了画面表示", "試験観点": "タスク中止要求の成功時における受付完了画面の表示機能", "試験対象": "TaskActionsModal コンポーネントの中止処理成功時の画面表示機能", "試験手順": "1. 中止確認画面でOKボタンをクリック\r\n2. cancelTaskが成功を返すよう設定\r\n3. 受付完了画面の表示内容を検証", "確認項目": "- cancelTaskサーバーアクションが呼び出されること\r\n- タイトル「情報」が表示されること\r\n- 受付完了メッセージが表示されること\r\n- 閉じるボタンが活性状態で表示されること\r\n- refreshTaskListが呼び出されること"}, {"テストケース名": "タスク中止要求失敗時のエラー画面表示", "試験観点": "タスク中止要求の失敗時におけるエラー画面の表示機能", "試験対象": "TaskActionsModal コンポーネントの中止処理失敗時の画面表示機能", "試験手順": "1. cancelTaskが失敗を返すよう設定\r\n2. 中止確認画面でOKボタンをクリック\r\n3. エラー画面の表示内容を検証", "確認項目": "- タイトル「エラー」が表示されること\r\n- エラーメッセージが表示されること\r\n- 閉じるボタンが活性状態で表示されること"}, {"テストケース名": "エラー終了タスクのエラー詳細表示", "試験観点": "エラー終了タスクに対するエラー詳細表示機能", "試験対象": "TaskActionsModal コンポーネントのエラー詳細表示機能", "試験手順": "1. COMPLETED_ERROR状態のタスクでコンポーネントをレンダリング\r\n2. エラー詳細表示リンクをクリック\r\n3. エラー詳細画面の表示内容を検証", "確認項目": "- タイトル「エラー詳細」が表示されること\r\n- resultMessageの内容が表示されること\r\n- 閉じるボタンが活性状態で表示されること"}, {"テストケース名": "エクスポートタスク正常終了時のダウンロードリンク表示", "試験観点": "エクスポートタスク正常終了時のダウンロードリンク表示機能", "試験対象": "TaskActionsModal コンポーネントのダウンロードリンク表示機能", "試験手順": "1. COMPLETED_SUCCESS状態かつMGMT_ITEM_EXPORTタイプのタスクでコンポーネントをレンダリング\r\n2. ダウンロードリンクの表示内容を検証", "確認項目": "- ダウンロードリンクが表示されること\r\n- href属性が正しく設定されること（/dashboard/tasks/{taskId}/download）"}, {"テストケース名": "正常系: 実行中・中止待ちステータスでの空欄表示", "試験観点": "実行中・中止待ちステータスでの空欄表示機能", "試験対象": "TaskActionsModal コンポーネントの実行中タスクに対する表示制御機能", "試験手順": "1. RUNBOOK_SUBMITTED状態のタスクでコンポーネントをレンダリング\r\n2. 操作ボタンが表示されないことを検証", "確認項目": "- 中止ボタンが表示されないこと\r\n- エラー詳細リンクが表示されないこと\r\n- ダウンロードリンクが表示されないこと"}, {"テストケース名": "正常系: 操作ログエクスポート正常終了時のメッセージ表示", "試験観点": "操作ログエクスポート正常終了時のメッセージ表示機能", "試験対象": "TaskActionsModal コンポーネントの操作ログエクスポート完了時の表示機能", "試験手順": "1. COMPLETED_SUCCESS状態かつOPLOG_EXPORTタイプのタスクでコンポーネントをレンダリング\r\n2. 完了メッセージの表示内容を検証", "確認項目": "- 操作ログエクスポート完了メッセージが表示されること"}]}, "page": {"displayName": "page", "testCases": [{"テストケース名": "初期表示時のデフォルト条件設定", "試験観点": "タスク一覧ページの初期表示時のデフォルト条件設定", "試験対象": "TasksPage コンポーネントの初期表示機能", "試験手順": "1. searchParamsを未指定でページコンポーネントをレンダリング\r\n2. データ取得関数が正しいデフォルト値で呼び出されることを検証", "確認項目": "- フィルター条件が空文字で設定されること\r\n- ページ番号が1で設定されること\r\n- 1ページあたりの行数が10で設定されること\r\n- ソートキーが開始日時（startedAt）で設定されること\r\n- ソート順序が降順（desc）で設定されること"}, {"テストケース名": "正常系: ヘッダ部の基本UI要素が正しく表示される", "試験観点": "設計文書項目1-11のヘッダ部UI要素表示確認", "試験対象": "設計文書「02-画面項目定義.md」ヘッダ部情報", "試験手順": "1. ページコンポーネントをレンダリング\r\n2. 各UI要素が正しく表示されることを確認", "確認項目": "- 項目1: タスク一覧（h1）\r\n- 項目2: 更新ボタン（活性）\r\n- 項目3: フィルターの入力（input）\r\n- 項目4: フィルターの検索ボタン（活性）\r\n- 項目5: フィルターのクリアボタン（活性）\r\n- 項目6-9: ページネーション要素\r\n- 項目10-11: 行数/ページ選択"}, {"テストケース名": "正常系: URLパラメータに基づく状態設定", "試験観点": "URLパラメータに基づく状態設定", "試験対象": "設計文書「05-イベント定義.md」イベントNo.1", "試験手順": "1. 特定のsearchParamsでページコンポーネントをレンダリング\r\n2. パラメータが正しく解析されることを確認", "確認項目": "- フィルター、ページ、サイズ、ソート、オーダーが正しく設定されること"}, {"テストケース名": "境界条件: データなし時はページネーションが非表示になる", "試験観点": "データなし時のUI制御", "試験対象": "totalPages=0の場合のページネーション非表示", "試験手順": "1. totalPages=0でページコンポーネントをレンダリング", "確認項目": "- ページネーションコンポーネントが表示されないこと\r\n- 行数/ページ選択が表示されないこと\r\n- その他の要素は表示されること"}, {"テストケース名": "正常系: スケルトンローディングの表示確認", "試験観点": "Suspenseとスケルトンローディングの動作確認", "試験対象": "設計文書「05-イベント定義.md」イベントNo.1のスケルトンローディング", "試験手順": "1. データ取得処理を遅延させる\r\n2. スケルトンローディングが表示されることを確認", "確認項目": "- データ取得中にスケルトンローディングが表示されること"}, {"テストケース名": "正常系: RefreshTokenコンポーネントのキー設定", "試験観点": "RefreshTokenコンポーネントのキー設定", "試験対象": "セッション有効期限延長処理のトリガー", "試験手順": "1. ページコンポーネントをレンダリング\r\n2. RefreshTokenコンポーネントが適切なキーで設定されることを確認", "確認項目": "- generateSecureIdが呼ばれること\r\n- RefreshTokenコンポーネントが存在すること"}]}}}}}, "jcs-backend-services-standard": {"displayName": "タスク Function App", "type": "backend", "categories": {"lib": {"displayName": "lib", "subCategories": {"azureClients": {"displayName": "azureClients", "testCases": [{"テストケース名": "環境変数検証: 全ての必要な環境変数が設定されている場合、正常にモジュールがロードされる", "試験観点": "環境変数の存在確認処理", "試験対象": "azureClients モジュールの環境変数検証ロジック", "試験手順": "1. 必要な環境変数が全て設定されている状態でモジュールをインポート\r\n2. モジュールが正常にロードされることを確認", "確認項目": "- モジュールが例外なく正常にロードされること"}, {"テストケース名": "環境変数検証: AZURE_STORAGE_CONNECTION_STRING が未設定の場合、エラーがスローされる", "試験観点": "環境変数不足時のエラー処理", "試験対象": "azureClients モジュールの環境変数検証ロジック", "試験手順": "1. AZURE_STORAGE_CONNECTION_STRING を削除\r\n2. モジュールのインポートを試行", "確認項目": "- 適切なエラーメッセージが含まれた例外がスローされること"}, {"テストケース名": "環境変数検証: AZURE_STORAGE_ACCOUNT_NAME が未設定の場合、エラーがスローされる", "試験観点": "環境変数不足時のエラー処理", "試験対象": "azureClients モジュールの環境変数検証ロジック", "試験手順": "1. AZURE_STORAGE_ACCOUNT_NAME を削除\r\n2. モジュールのインポートを試行", "確認項目": "- 適切なエラーメッセージが含まれた例外がスローされること"}, {"テストケース名": "正常系: createBlobServiceClient が正しいパラメータでクライアントを作成", "試験観点": "Azure Blob Service クライアント作成の正常処理", "試験対象": "createBlobServiceClient 関数", "試験手順": "1. createBlobServiceClient を呼び出す\r\n2. 正しいパラメータで BlobServiceClient が作成されることを確認", "確認項目": "- BlobServiceClient が正しい URL と認証情報で作成されること\r\n- 作成されたクライアントが返されること"}, {"テストケース名": "正常系: createShareServiceClient が正しい接続文字列でクライアントを作成", "試験観点": "Azure Files Service クライアント作成の正常処理", "試験対象": "createShareServiceClient 関数", "試験手順": "1. createShareServiceClient を呼び出す\r\n2. 正しい接続文字列で ShareServiceClient が作成されることを確認", "確認項目": "- ShareServiceClient が正しい接続文字列で作成されること\r\n- 作成されたクライアントが返されること"}, {"テストケース名": "正常系: createServiceBusClient が正しいパラメータでクライアントを作成", "試験観点": "Azure Service Bus クライアント作成の正常処理", "試験対象": "createServiceBusClient 関数", "試験手順": "1. createServiceBusClient を呼び出す\r\n2. 正しいパラメータで ServiceBusClient が作成されることを確認", "確認項目": "- ServiceBusClient が正しい名前空間と認証情報で作成されること\r\n- 作成されたクライアントが返されること"}, {"テストケース名": "正常系: createAutomationJob が正しいパラメータでジョブを作成", "試験観点": "Azure Automation ジョブ作成の正常処理", "試験対象": "createAutomationJob 関数", "試験手順": "1. 正常なパラメータで createAutomationJob を呼び出す\r\n2. 正しい URL とボディで fetch が呼ばれることを確認", "確認項目": "- 認証トークンが正しく取得されること\r\n- 正しい URL と HTTP メソッドで fetch が呼ばれること\r\n- 正しいリクエストボディが送信されること"}, {"テストケース名": "異常系: createAutomationJob でAPI呼び出しが失敗した場合、エラーがスローされる", "試験観点": "Azure Automation ジョブ作成の異常処理", "試験対象": "createAutomationJob 関数", "試験手順": "1. fetch が失敗レスポンスを返すようにモック\r\n2. createAutomationJob を呼び出す", "確認項目": "- 適切なエラーメッセージで例外がスローされること"}, {"テストケース名": "正常系: stopAutomationJob が正しいパラメータでジョブを停止", "試験観点": "Azure Automation ジョブ停止の正常処理", "試験対象": "stopAutomationJob 関数", "試験手順": "1. 正常なパラメータで stopAutomationJob を呼び出す\r\n2. 正しい URL とメソッドで fetch が呼ばれることを確認", "確認項目": "- 正しい URL と HTTP メソッドで fetch が呼ばれること\r\n- 認証ヘッダーが正しく設定されること"}, {"テストケース名": "異常系: stopAutomationJob でAPI呼び出しが失敗した場合、エラーがスローされる", "試験観点": "Azure Automation ジョブ停止の異常処理", "試験対象": "stopAutomationJob 関数", "試験手順": "1. fetch が失敗レスポンスを返すようにモック\r\n2. stopAutomationJob を呼び出す", "確認項目": "- 適切なエラーメッセージで例外がスローされること"}, {"テストケース名": "正常系: getAutomationJobStatus でジョブが存在する場合、正しいデータが返される", "試験観点": "Azure Automation ジョブ状態取得の正常処理（ジョブ存在）", "試験対象": "getAutomationJobStatus 関数", "試験手順": "1. fetch が 200 レスポンスを返すようにモック\r\n2. getAutomationJobStatus を呼び出す", "確認項目": "- 正しい URL と HTTP メソッドで fetch が呼ばれること\r\n- ジョブデータと正しいステータスが返されること\r\n- exists フラグが true であること"}, {"テストケース名": "正常系: getAutomationJobStatus でジョブが存在しない場合、404ステータスが返される", "試験観点": "Azure Automation ジョブ状態取得の正常処理（ジョブ不存在）", "試験対象": "getAutomationJobStatus 関数", "試験手順": "1. fetch が 404 レスポンスを返すようにモック\r\n2. getAutomationJobStatus を呼び出す", "確認項目": "- 正しいステータスコードが返されること\r\n- exists フラグが false であること\r\n- jobData が undefined であること"}, {"テストケース名": "異常系: getAutomationJobStatus でAPI呼び出しが失敗した場合、エラーがスローされる", "試験観点": "Azure Automation ジョブ状態取得の異常処理", "試験対象": "getAutomationJobStatus 関数", "試験手順": "1. fetch が 500 レスポンスを返すようにモック\r\n2. getAutomationJobStatus を呼び出す", "確認項目": "- 適切なエラーメッセージで例外がスローされること"}, {"テストケース名": "正常系: authenticatedFetch で相対URLが正しく完全URLに変換される", "試験観点": "認証付きfetch関数の相対URL処理", "試験対象": "authenticatedFetch 関数（間接的にテスト）", "試験手順": "1. 相対URLでAzure Automation APIを呼び出す\r\n2. 正しい完全URLが構築されることを確認", "確認項目": "- 相対URLが正しく完全URLに変換されること\r\n- 認証ヘッダーが正しく設定されること"}, {"テストケース名": "正常系: authenticatedFetch で完全URLがそのまま使用される", "試験観点": "認証付きfetch関数の完全URL処理", "試験対象": "authenticatedFetch 関数（間接的にテスト）", "試験手順": "1. 完全URLを直接指定してテスト\r\n2. URLがそのまま使用されることを確認", "確認項目": "- 完全URLがそのまま使用されること\r\n- 認証ヘッダーが正しく設定されること"}, {"テストケース名": "異常系: Azure認証が失敗した場合、エラーが伝播される", "試験観点": "Azure認証失敗時のエラー処理", "試験対象": "authenticatedFetch 関数（間接的にテスト）", "試験手順": "1. DefaultAzureCredential.getToken が失敗するようにモック\r\n2. Azure Automation API を呼び出す", "確認項目": "- 認証エラーが適切に伝播されること"}]}, "cleanup": {"displayName": "cleanup", "testCases": [{"テストケース名": "正常系: タスク件数が上限内の場合、クリーンアップ不要で処理終了", "試験観点": "タスク件数が上限内の場合のクリーンアップ不要処理", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. 最大保持件数を10件に設定\r\n2. 現在のタスク件数を5件に設定\r\n3. cleanupOldTasks を呼び出す", "確認項目": "- クリーンアップ不要のログが出力されること\r\n- タスク削除処理が実行されないこと"}, {"テストケース名": "正常系: 削除対象タスクが存在しない場合、処理終了", "試験観点": "削除対象タスクが存在しない場合の処理", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. 最大保持件数を5件に設定\r\n2. 現在のタスク件数を10件に設定\r\n3. 削除対象タスクを0件に設定\r\n4. cleanupOldTasks を呼び出す", "確認項目": "- 削除対象タスクが存在しないログが出力されること\r\n- 削除処理が実行されないこと"}, {"テストケース名": "正常系: MgmtItemImportタスクの正常削除処理", "試験観点": "MgmtItemImportタスクの正常削除処理", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. 最大保持件数を1件に設定\r\n2. 現在のタスク件数を3件に設定\r\n3. MgmtItemImportタスクを削除対象に設定\r\n4. cleanupOldTasks を呼び出す", "確認項目": "- 操作ログが削除されること\r\n- Blobファイルが削除されること\r\n- タスクレコードが削除されること"}, {"テストケース名": "正常系: MgmtItemExportタスクの正常削除処理", "試験観点": "MgmtItemExportタスクの正常削除処理", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. MgmtItemExportタスクを削除対象に設定\r\n2. cleanupOldTasks を呼び出す", "確認項目": "- 正しいBlobプレフィックスでファイル削除が実行されること\r\n- assetsコンテナが使用されること"}, {"テストケース名": "正常系: OpLogExportタスクの正常削除処理", "試験観点": "OpLogExportタスクの正常削除処理", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. OpLogExportタスクを削除対象に設定\r\n2. cleanupOldTasks を呼び出す", "確認項目": "- 正しいBlobプレフィックスでファイル削除が実行されること\r\n- oplogsコンテナが使用されること"}, {"テストケース名": "正常系: licenseId未設定タスクのBlobフォルダ削除スキップ処理", "試験観点": "licenseIdが未設定のタスクのスキップ処理", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. licenseIdが未設定のタスクを削除対象に設定\r\n2. cleanupOldTasks を呼び出す", "確認項目": "- Blobフォルダ削除がスキップされること\r\n- 適切な警告ログが出力されること\r\n- タスクレコードは削除されること"}, {"テストケース名": "正常系: 削除対象Blobファイルが存在しない場合の処理", "試験観点": "削除対象Blobファイルが存在しない場合の処理", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. Blobファイルが存在しないタスクを削除対象に設定\r\n2. cleanupOldTasks を呼び出す", "確認項目": "- 削除対象ファイルが存在しないログが出力されること\r\n- タスクレコードは削除されること"}, {"テストケース名": "異常系: 個別タスク削除処理でエラーが発生した場合、エラーログ出力後に処理継続", "試験観点": "個別タスク削除処理でのエラーハンドリング", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. 操作ログ削除でエラーが発生するようにモック\r\n2. cleanupOldTasks を呼び出す", "確認項目": "- エラーログが出力されること\r\n- 処理が継続されること（他のタスクの処理に影響しない）"}, {"テストケース名": "異常系: クリーンアップ処理全体で致命的エラーが発生した場合、エラーログ出力", "試験観点": "クリーンアップ処理全体での致命的エラーハンドリング", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. 最大保持件数取得でエラーが発生するようにモック\r\n2. cleanupOldTasks を呼び出す", "確認項目": "- 致命的エラーログが出力されること\r\n- 例外がスローされないこと"}, {"テストケース名": "境界値: 最大保持件数の設定が存在しない場合、デフォルト値10件が使用される", "試験観点": "最大保持件数のデフォルト値処理", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. 最大保持件数の設定が存在しない状況をモック\r\n2. cleanupOldTasks を呼び出す", "確認項目": "- デフォルト値（10件）が使用されること\r\n- 適切なログが出力されること"}, {"テストケース名": "境界値: 複数タスクの一括削除処理", "試験観点": "複数タスクの一括削除処理", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. 複数の削除対象タスクを設定\r\n2. cleanupOldTasks を呼び出す", "確認項目": "- 全てのタスクが順次処理されること\r\n- 各タスクの削除ログが出力されること"}]}, "utils": {"displayName": "utils", "testCases": [{"テストケース名": "有効なエラーコード: 正常メッセージ生成", "試験観点": "正常系メッセージ生成の検証。", "試験対象": "formatTaskErrorMessage関数の正常系。", "試験手順": "1. 有効なエラーコードでメッセージを生成する場合をテスト。", "確認項目": "- 正しいエラーメッセージが返されること。"}, {"テストケース名": "無効なエラーコード: 空文字列返却", "試験観点": "異常系メッセージ生成の検証。", "試験対象": "formatTaskErrorMessage関数の異常系。", "試験手順": "1. 無効なエラーコードでメッセージを生成する場合をテスト。", "確認項目": "- 空文字列が返されること。"}, {"テストケース名": "パラメータ付き: 正常置換", "試験観点": "パラメータ置換機能の検証。", "試験対象": "formatTaskErrorMessage関数のパラメータ置換。", "試験手順": "1. パラメータ付きでメッセージを生成する場合をテスト。", "確認項目": "- パラメータが正しく置換されること。"}, {"テストケース名": "パラメータなし: デフォルト空配列", "試験観点": "デフォルトパラメータ機能の検証。", "試験対象": "formatTaskErrorMessage関数のデフォルトパラメータ。", "試験手順": "1. パラメータなしでメッセージを生成する場合をテスト。", "確認項目": "- デフォルトの空配列が使用されること。"}, {"テストケース名": "PrismaClientエラー: true返却", "試験観点": "Prismaエラー判定の正常系検証。", "試験対象": "isPrismaError関数の正常系。", "試験手順": "1. PrismaClientエラーの場合をテスト。", "確認項目": "- trueが返されること。"}, {"テストケース名": "通常エラー: false返却", "試験観点": "Prismaエラー判定の異常系検証。", "試験対象": "isPrismaError関数の異常系。", "試験手順": "1. 通常のエラーの場合をテスト。", "確認項目": "- falseが返されること。"}, {"テストケース名": "Azure Files RestError: true返却", "試験観点": "Azure Filesエラー判定の正常系検証。", "試験対象": "isAzureFilesError関数の正常系。", "試験手順": "1. Azure Files RestErrorの場合をテスト。", "確認項目": "- trueが返されること。"}, {"テストケース名": "ShareError: true返却", "試験観点": "Azure Filesエラー判定の正常系検証。", "試験対象": "isAzureFilesError関数の正常系。", "試験手順": "1. ShareErrorの場合をテスト。", "確認項目": "- trueが返されること。"}, {"テストケース名": "通常エラー: false返却", "試験観点": "Azure Filesエラー判定の異常系検証。", "試験対象": "isAzureFilesError関数の異常系。", "試験手順": "1. 通常のエラーの場合をテスト。", "確認項目": "- falseが返されること。"}, {"テストケース名": "Azure Blob RestError: true返却", "試験観点": "Azure Blobエラー判定の正常系検証。", "試験対象": "isAzureBlobError関数の正常系。", "試験手順": "1. Azure Blob RestErrorの場合をテスト。", "確認項目": "- trueが返されること。"}, {"テストケース名": "通常エラー: false返却", "試験観点": "Azure Blobエラー判定の異常系検証。", "試験対象": "isAzureBlobError関数の異常系。", "試験手順": "1. 通常のエラーの場合をテスト。", "確認項目": "- falseが返されること。"}, {"テストケース名": "指定エラーコード存在: true返却", "試験観点": "エラーコード判定の正常系検証。", "試験対象": "hasErrorCode関数の正常系。", "試験手順": "1. 指定されたエラーコードを持つエラーの場合をテスト。", "確認項目": "- trueが返されること。"}, {"テストケース名": "異なるエラーコード: false返却", "試験観点": "エラーコード判定の異常系検証。", "試験対象": "hasErrorCode関数の異常系。", "試験手順": "1. 異なるエラーコードを持つエラーの場合をテスト。", "確認項目": "- falseが返されること。"}, {"テストケース名": "エラーコードなし: false返却", "試験観点": "エラーコード判定の異常系検証。", "試験対象": "hasErrorCode関数の異常系。", "試験手順": "1. エラーコードを持たないエラーの場合をテスト。", "確認項目": "- falseが返されること。"}, {"テストケース名": "タスクディレクトリ不存在: 正常終了", "試験観点": "ディレクトリ不存在分岐の検証。", "試験対象": "deleteTaskWorkspaceDirectory関数のディレクトリ不存在分岐。", "試験手順": "1. タスクディレクトリが存在しない場合をテスト。", "確認項目": "- context.logに\"存在しません\"が出力されること。\n- 処理が正常終了すること。"}, {"テストケース名": "importsディレクトリ不存在: 処理継続", "試験観点": "importsディレクトリ不存在分岐の検証。", "試験対象": "deleteTaskWorkspaceDirectory関数のimports削除分岐。", "試験手順": "1. importsディレクトリが存在しない場合をテスト。", "確認項目": "- context.logに\"imports/ディレクトリが存在しません\"が出力されること。\n- 処理が継続されること。"}, {"テストケース名": "importsファイル存在: ファイル削除", "試験観点": "importsファイル削除分岐の検証。", "試験対象": "deleteTaskWorkspaceDirectory関数のimportsファイル削除分岐。", "試験手順": "1. assetsfield_def.csvファイルが存在する場合をテスト。", "確認項目": "- ファイルが削除されること。\n- context.logに\"ファイル削除: imports/assetsfield_def.csv\"が出力されること。"}, {"テストケース名": "exportsファイル存在: ファイル削除", "試験観点": "exportsファイル削除分岐の検証。", "試験対象": "deleteTaskWorkspaceDirectory関数のexportsファイル削除分岐。", "試験手順": "1. exportsディレクトリにファイルが存在する場合をテスト。", "確認項目": "- ファイルが削除されること。\n- context.logに\"ファイル削除: exports/filename\"が出力されること。"}, {"テストケース名": "imports削除例外: エラーログ記録・処理継続", "試験観点": "imports削除例外分岐の検証。", "試験対象": "deleteTaskWorkspaceDirectory関数のimports削除例外処理分岐。", "試験手順": "1. imports削除で例外が発生する場合をテスト。", "確認項目": "- context.logにエラーメッセージが出力されること。\n- 処理が継続されること（例外は再スローされない）。"}, {"テストケース名": "exports削除例外: エラーログ記録・処理継続", "試験観点": "exports削除例外分岐の検証。", "試験対象": "deleteTaskWorkspaceDirectory関数のexports削除例外処理分岐。", "試験手順": "1. exports削除で例外が発生する場合をテスト。", "確認項目": "- context.logにエラーメッセージが出力されること。\n- 処理が継続されること（例外は再スローされない）。"}, {"テストケース名": "メイン削除例外: エラーログ記録・例外再スロー", "試験観点": "メイン削除例外分岐の検証。", "試験対象": "deleteTaskWorkspaceDirectory関数のメイン例外処理分岐。", "試験手順": "1. メイン処理で例外が発生する場合をテスト。", "確認項目": "- context.errorにエラーメッセージが出力されること。\n- 例外が再スローされること。"}, {"テストケース名": "正常系: 完全削除フロー", "試験観点": "正常系フローの網羅的検証。", "試験対象": "deleteTaskWorkspaceDirectory関数の正常系フロー。", "試験手順": "1. 正常系の完全なフローをテスト。", "確認項目": "- 全ての処理が正常に実行されること。\n- 適切なログが出力されること。"}]}}}, "RunbookMonitorFunc.test.ts": {"displayName": "RunbookMonitorFunc.test.ts", "subCategories": {"RunbookMonitorFunc": {"displayName": "RunbookMonitorFunc", "testCases": [{"テストケース名": "RUNBOOK_SUBMITTEDタスクが存在しない場合: 即終了", "試験観点": "監視対象タスクなし分岐の検証。", "試験対象": "RunbookMonitorFuncのタスクなし分岐。", "試験手順": "1. RUNBOOK_SUBMITTEDタスクが存在しない場合をテスト。", "確認項目": "- context.logに\"存在しません\"が出力されること。\n- getAutomationJobStatusが呼ばれないこと。"}, {"テストケース名": "startedAt未設定タスク: スキップ", "試験観点": "startedAt未設定分岐の検証。", "試験対象": "RunbookMonitorFuncのstartedAt未設定分岐。", "試験手順": "1. startedAtが未設定のタスクをテスト。", "確認項目": "- context.warnに\"startedAt未設定のためスキップ\"が出力されること。\n- getAutomationJobStatusが呼ばれないこと。"}, {"テストケース名": "タイムアウトタスク: Timeout処理", "試験観点": "タイムアウト分岐の検証。", "試験対象": "RunbookMonitorFuncのタイムアウト分岐。", "試験手順": "1. タイムアウトしたタスクをテスト（6時間前に開始）。", "確認項目": "- context.logに\"タイムアウトしました\"が出力されること。\n- DB更新とServiceBus送信が実行されること。"}, {"テストケース名": "Azure Automation Job不存在: スキップ", "試験観点": "Job不存在分岐の検証。", "試験対象": "RunbookMonitorFuncのJob不存在分岐。", "試験手順": "1. Azure Automation Jobが存在しない場合をテスト。", "確認項目": "- context.warnに\"ジョブが存在しません\"が出力されること。\n- DB更新が実行されないこと。"}, {"テストケース名": "ジョブステータス実行中: 処理対象外", "試験観点": "実行中ステータス分岐の検証。", "試験対象": "RunbookMonitorFuncの実行中ステータス分岐。", "試験手順": "1. ジョブステータスが実行中状態の場合をテスト。", "確認項目": "- context.logに\"実行中状態のため処理対象外\"が出力されること。\n- DB更新が実行されないこと。"}, {"テストケース名": "ジョブステータス終了状態: 処理対象", "試験観点": "終了状態分岐の検証。", "試験対象": "RunbookMonitorFuncの終了状態分岐。", "試験手順": "1. ジョブステータスが終了状態の場合をテスト。", "確認項目": "- context.logに\"終了状態のため処理対象\"が出力されること。\n- DB更新とServiceBus送信が実行されること。"}, {"テストケース名": "ジョブステータス未知: スキップ", "試験観点": "未知ステータス分岐の検証。", "試験対象": "RunbookMonitorFuncの未知ステータス分岐。", "試験手順": "1. ジョブステータスが未知の状態の場合をテスト。", "確認項目": "- context.warnに\"未知ジョブステータス\"が出力されること。\n- DB更新が実行されないこと。"}, {"テストケース名": "Azure Automation API失敗: スキップ", "試験観点": "API失敗分岐の検証。", "試験対象": "RunbookMonitorFuncのAPI失敗分岐。", "試験手順": "1. Azure Automation API呼び出しが失敗する場合をテスト。", "確認項目": "- context.errorに\"API呼び出し失敗\"が出力されること。\n- DB更新が実行されないこと。"}, {"テストケース名": "DB更新楽観ロック失敗: スキップ", "試験観点": "楽観ロック失敗分岐の検証。", "試験対象": "RunbookMonitorFuncの楽観ロック失敗分岐。", "試験手順": "1. DB更新が楽観ロック失敗（0件更新）の場合をテスト。", "確認項目": "- context.errorに\"楽観ロック失敗\"が出力されること。\n- ServiceBus送信が実行されないこと。"}, {"テストケース名": "DB更新例外: スキップ", "試験観点": "DB更新例外分岐の検証。", "試験対象": "RunbookMonitorFuncのDB更新例外分岐。", "試験手順": "1. DB更新が例外で失敗する場合をテスト。", "確認項目": "- context.errorに\"DB更新失敗\"が出力されること。\n- ServiceBus送信が実行されないこと。"}, {"テストケース名": "ServiceBus送信失敗・ロールバック成功", "試験観点": "ServiceBus送信失敗・ロールバック成功分岐の検証。", "試験対象": "RunbookMonitorFuncのServiceBus送信失敗分岐。", "試験手順": "1. ServiceBus送信が失敗し、ロールバック成功の場合をテスト。", "確認項目": "- context.errorに\"送信失敗\"が出力されること。\n- context.logに\"戻しました\"が出力されること。"}, {"テストケース名": "ServiceBus送信失敗・ロールバック失敗（0件）", "試験観点": "ServiceBus送信失敗・ロールバック失敗分岐の検証。", "試験対象": "RunbookMonitorFuncのロールバック失敗分岐。", "試験手順": "1. ServiceBus送信が失敗し、ロールバック失敗（0件）の場合をテスト。", "確認項目": "- context.errorに\"送信失敗\"が出力されること。\n- context.errorに\"戻し失敗\"が出力されること。"}, {"テストケース名": "ServiceBus送信失敗・ロールバック例外", "試験観点": "ServiceBus送信失敗・ロールバック例外分岐の検証。", "試験対象": "RunbookMonitorFuncのロールバック例外分岐。", "試験手順": "1. ServiceBus送信が失敗し、ロールバック例外の場合をテスト。", "確認項目": "- context.errorに\"送信失敗\"が出力されること。\n- context.errorに\"戻し失敗\"が出力されること。"}, {"テストケース名": "タスク処理中予期せぬ例外: 継続", "試験観点": "タスク処理中例外分岐の検証。", "試験対象": "RunbookMonitorFuncのタスク処理中例外分岐。", "試験手順": "1. タスク処理中に予期せぬ例外が発生する場合をテスト。", "確認項目": "- context.errorに\"予期せぬ内部エラー\"が出力されること。\n- 処理が継続されること。"}, {"テストケース名": "Task取得致命的例外: エラーログ記録", "試験観点": "全体例外処理分岐の検証。", "試験対象": "RunbookMonitorFuncの全体例外処理。", "試験手順": "1. Task取得で致命的例外が発生する場合をテスト。", "確認項目": "- context.errorに\"データ取得失敗または致命的エラー\"が出力されること。"}, {"テストケース名": "jobDataなし: デフォルト値使用", "試験観点": "jobDataなし分岐の検証。", "試験対象": "RunbookMonitorFuncのjobDataなし分岐。", "試験手順": "1. jobDataがundefinedの場合をテスト。", "確認項目": "- 空文字列がデフォルト値として使用されること。\n- 処理が継続されること。"}, {"テストケース名": "jobData.propertiesなし: デフォルト値使用", "試験観点": "properties未定義分岐の検証。", "試験対象": "RunbookMonitorFuncのproperties未定義分岐。", "試験手順": "1. jobData.propertiesがundefinedの場合をテスト。", "確認項目": "- 空文字列がデフォルト値として使用されること。\n- 処理が継続されること。"}, {"テストケース名": "正常系: 完全フロー", "試験観点": "正常系フローの網羅的検証。", "試験対象": "RunbookMonitorFuncの正常系フロー。", "試験手順": "1. 正常系の完全なフローをテスト。", "確認項目": "- 全ての処理が正常に実行されること。\n- 適切なログが出力されること。"}]}}}, "RunbookProcessorTimeoutFunc.test.ts": {"displayName": "RunbookProcessorTimeoutFunc.test.ts", "subCategories": {"RunbookProcessorTimeoutFunc": {"displayName": "RunbookProcessorTimeoutFunc", "testCases": [{"テストケース名": "メッセージがnull: エラーログ記録", "試験観点": "メッセージnull分岐の検証。", "試験対象": "RunbookProcessorTimeoutFuncのメッセージ検証分岐。", "試験手順": "1. messageがnullの場合をテスト。", "確認項目": "- context.errorに\"メッセージが不正\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "メッセージが非object: エラーログ記録", "試験観点": "メッセージ型検証分岐の検証。", "試験対象": "RunbookProcessorTimeoutFuncのメッセージ検証分岐。", "試験手順": "1. messageが文字列（非object）の場合をテスト。", "確認項目": "- context.errorに\"メッセージが不正\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "taskId不足: エラーログ記録", "試験観点": "taskId不足分岐の検証。", "試験対象": "RunbookProcessorTimeoutFuncのtaskId検証分岐。", "試験手順": "1. taskIdが不足している場合をテスト。", "確認項目": "- context.errorに\"taskIdが特定できません\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "taskId非文字列: エラーログ記録", "試験観点": "taskId型検証分岐の検証。", "試験対象": "RunbookProcessorTimeoutFuncのtaskId検証分岐。", "試験手順": "1. taskIdが非文字列型の場合をテスト。", "確認項目": "- context.errorに\"taskIdが特定できません\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "taskId空文字列: エラーログ記録", "試験観点": "taskId空文字列分岐の検証。", "試験対象": "RunbookProcessorTimeoutFuncのtaskId検証分岐。", "試験手順": "1. taskIdが空文字列の場合をテスト。", "確認項目": "- context.errorに\"taskIdが特定できません\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "Task不存在: エラーログ記録", "試験観点": "Task不存在分岐の検証。", "試験対象": "RunbookProcessorTimeoutFuncのTask取得失敗分岐。", "試験手順": "1. Taskが存在しない場合をテスト。", "確認項目": "- context.errorに\"存在しません\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "RUNBOOK_PROCESSING以外のステータス: 警告ログ記録", "試験観点": "ステータス不正分岐の検証。", "試験対象": "RunbookProcessorTimeoutFuncのステータス判定分岐。", "試験手順": "1. RUNBOOK_PROCESSING以外のステータスの場合をテスト。", "確認項目": "- context.logに\"処理対象外です\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "RUNBOOK_PROCESSING正常処理: 成功", "試験観点": "正常系フローの検証。", "試験対象": "RunbookProcessorTimeoutFuncの正常系フロー。", "試験手順": "1. RUNBOOK_PROCESSINGのタスクを正常に処理する場合をテスト。", "確認項目": "- DBトランザクションが実行されること。\n- context.logに成功メッセージが出力されること。"}, {"テストケース名": "DBトランザクション失敗: エラーログ記録", "試験観点": "DBトランザクション失敗分岐の検証。", "試験対象": "RunbookProcessorTimeoutFuncのDBトランザクション失敗分岐。", "試験手順": "1. DBトランザクションが失敗する場合をテスト。", "確認項目": "- context.errorに\"DBトランザクション失敗\"が出力されること。\n- 処理が継続されること。"}, {"テストケース名": "Azure Files削除失敗: 処理継続", "試験観点": "Azure Files削除失敗分岐の検証。", "試験対象": "RunbookProcessorTimeoutFuncのAzure Files削除失敗分岐。", "試験手順": "1. Azure Files削除が失敗する場合をテスト。", "確認項目": "- context.errorに\"削除失敗\"が出力されること。\n- 後続処理が継続されること。"}, {"テストケース名": "Task取得例外: エラーログ記録", "試験観点": "Task取得例外分岐の検証。", "試験対象": "RunbookProcessorTimeoutFuncのTask取得例外分岐。", "試験手順": "1. Task取得で例外が発生する場合をテスト。", "確認項目": "- context.errorに\"予期せぬ内部エラー\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "Container更新0件: エラーログ記録", "試験観点": "Container更新0件分岐の検証。", "試験対象": "RunbookProcessorTimeoutFuncのContainer更新0件分岐。", "試験手順": "1. Container更新件数が0件の場合をテスト。", "確認項目": "- context.errorに\"コンテナ実行状態テーブル更新失敗\"が出力されること。\n- トランザクションがロールバックされること。"}, {"テストケース名": "Task更新0件: エラーログ記録", "試験観点": "Task更新0件分岐の検証。", "試験対象": "RunbookProcessorTimeoutFuncのTask更新0件分岐。", "試験手順": "1. Task更新件数が0件の場合をテスト。", "確認項目": "- context.errorに\"タスクテーブル更新失敗\"が出力されること。\n- トランザクションがロールバックされること。"}, {"テストケース名": "楽観ロック失敗: 並行更新検出", "試験観点": "楽観ロック制御の正確性検証（設計文書6章の要件）。", "試験対象": "RunbookProcessorTimeoutFuncの楽観ロック制御分岐。", "試験手順": "1. 楽観ロック失敗の具体的なシナリオをテスト（設計文書の重要要件）。", "確認項目": "- Task読取後に他プロセスがupdatedAtを更新した場合\n- updateMany の count が 0 になること\n- トランザクションがロールバックされること"}, {"テストケース名": "Container更新条件: 設計仕様準拠", "試験観点": "Container更新条件の正確性検証（設計文書の具体的要件）。", "試験対象": "RunbookProcessorTimeoutFuncのContainer更新WHERE条件。", "試験手順": "1. Container更新の具体的な条件をテスト（設計文書5章の要件）。", "確認項目": "- WHERE条件が設計通りに設定されること\n- targetVmName, targetContainerName, status=BUSY, currentTaskId=taskId"}, {"テストケース名": "正常系: 完全フロー", "試験観点": "正常系フローの網羅的検証。", "試験対象": "RunbookProcessorTimeoutFuncの正常系フロー。", "試験手順": "1. 正常系の完全なフローをテスト。", "確認項目": "- 全ての処理が正常に実行されること。\n- 適切なログが出力されること。"}]}}}, "TaskCancellationFunc.test.ts": {"displayName": "TaskCancellationFunc.test.ts", "subCategories": {"TaskCancellationFunc": {"displayName": "TaskCancellationFunc", "testCases": [{"テストケース名": "メッセージがnull: エ<PERSON><PERSON><PERSON><PERSON>", "試験観点": "メッセージnull分岐の検証。", "試験対象": "TaskCancellationFuncのメッセージ検証分岐。", "試験手順": "1. messageがnullの場合をテスト。", "確認項目": "- context.errorに\"メッセージが不正\"が出力されること。\n- 例外がthrowされること。"}, {"テストケース名": "メッセージが非object: エラーthrow", "試験観点": "メッセージ型検証分岐の検証。", "試験対象": "TaskCancellationFuncのメッセージ検証分岐。", "試験手順": "1. messageが文字列（非object）の場合をテスト。", "確認項目": "- context.errorに\"メッセージが不正\"が出力されること。\n- 例外がthrowされること。"}, {"テストケース名": "taskId不足: エラー<PERSON>row", "試験観点": "taskId不足分岐の検証。", "試験対象": "TaskCancellationFuncのtaskId検証分岐。", "試験手順": "1. taskIdが不足している場合をテスト。", "確認項目": "- context.errorに\"taskIdが不正\"が出力されること。\n- 例外がthrowされること。"}, {"テストケース名": "taskId非文字列: エラーthrow", "試験観点": "taskId型検証分岐の検証。", "試験対象": "TaskCancellationFuncのtaskId検証分岐。", "試験手順": "1. taskIdが非文字列型の場合をテスト。", "確認項目": "- context.errorに\"taskIdが不正\"が出力されること。\n- 例外がthrowされること。"}, {"テストケース名": "taskId空文字列: エラーthrow", "試験観点": "taskId空文字列分岐の検証。", "試験対象": "TaskCancellationFuncのtaskId検証分岐。", "試験手順": "1. taskIdが空文字列の場合をテスト。", "確認項目": "- context.errorに\"taskIdが不正\"が出力されること。\n- 例外がthrowされること。"}, {"テストケース名": "Task不存在: エラーthrow", "試験観点": "Task不存在分岐の検証。", "試験対象": "TaskCancellationFuncのTask取得失敗分岐。", "試験手順": "1. Taskが存在しない場合をテスト。", "確認項目": "- context.errorに\"存在しない\"が出力されること。\n- 例外がthrowされること。"}, {"テストケース名": "PENDING_CANCELLATION以外のステータス: エラーthrow", "試験観点": "ステータス不正分岐の検証。", "試験対象": "TaskCancellationFuncのステータス判定分岐。", "試験手順": "1. PENDING_CANCELLATION以外のステータスの場合をテスト。", "確認項目": "- context.errorに\"PENDING_CANCELLATION ではない\"が出力されること。\n- 例外がthrowされること。"}, {"テストケース名": "PENDING_CANCELLATION正常更新: 成功", "試験観点": "正常系フローの検証。", "試験対象": "TaskCancellationFuncの正常系フロー。", "試験手順": "1. PENDING_CANCELLATIONのタスクを正常に更新する場合をテスト。", "確認項目": "- prisma.task.updateManyが正しいパラメータで呼ばれること。\n- context.logに成功メッセージが出力されること。"}, {"テストケース名": "楽観ロック失敗: エラーthrow", "試験観点": "楽観ロック失敗分岐の検証。", "試験対象": "TaskCancellationFuncの楽観ロック制御分岐。", "試験手順": "1. 楽観ロック失敗（0件更新）の場合をテスト。", "確認項目": "- context.errorに\"楽観ロック失敗\"が出力されること。\n- 例外がthrowされること。"}, {"テストケース名": "DB更新例外: エラーre-throw", "試験観点": "DB更新例外分岐の検証。", "試験対象": "TaskCancellationFuncのDB更新例外分岐。", "試験手順": "1. DB更新で例外が発生する場合をテスト。", "確認項目": "- context.errorに\"エラー発生\"が出力されること。\n- 例外がre-throwされること。"}, {"テストケース名": "formatTaskErrorMessage例外: エラーre-throw", "試験観点": "formatTaskErrorMessage例外分岐の検証。", "試験対象": "TaskCancellationFuncのformatTaskErrorMessage例外分岐。", "試験手順": "1. formatTaskErrorMessageで例外が発生する場合をテスト。", "確認項目": "- context.errorに\"エラー発生\"が出力されること。\n- 例外がre-throwされること。"}, {"テストケース名": "Task取得例外: エラーre-throw", "試験観点": "Task取得例外分岐の検証。", "試験対象": "TaskCancellationFuncのTask取得例外分岐。", "試験手順": "1. Task取得で例外が発生する場合をテスト。", "確認項目": "- context.errorに\"エラー発生\"が出力されること。\n- 例外がre-throwされること。"}, {"テストケース名": "正常系: 完全フロー", "試験観点": "正常系フローの網羅的検証。", "試験対象": "TaskCancellationFuncの正常系フロー。", "試験手順": "1. 正常系の完全なフローをテスト。", "確認項目": "- 全ての処理が正常に実行されること。\n- 適切なログが出力されること。"}]}}}, "TaskCancellationTimeoutFunc.test.ts": {"displayName": "TaskCancellationTimeoutFunc.test.ts", "subCategories": {"TaskCancellationTimeoutFunc": {"displayName": "TaskCancellationTimeoutFunc", "testCases": [{"テストケース名": "メッセージがnull: エラーログ記録", "試験観点": "メッセージnull分岐の検証。", "試験対象": "TaskCancellationTimeoutFuncのメッセージ検証分岐。", "試験手順": "1. messageがnullの場合をテスト。", "確認項目": "- context.errorに\"メッセージが不正\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "メッセージが非object: エラーログ記録", "試験観点": "メッセージ型検証分岐の検証。", "試験対象": "TaskCancellationTimeoutFuncのメッセージ検証分岐。", "試験手順": "1. messageが文字列（非object）の場合をテスト。", "確認項目": "- context.errorに\"メッセージが不正\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "taskId不足: エラーログ記録", "試験観点": "taskId不足分岐の検証。", "試験対象": "TaskCancellationTimeoutFuncのtaskId検証分岐。", "試験手順": "1. taskIdが不足している場合をテスト。", "確認項目": "- context.errorに\"taskIdが不正\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "taskId非文字列: エラーログ記録", "試験観点": "taskId型検証分岐の検証。", "試験対象": "TaskCancellationTimeoutFuncのtaskId検証分岐。", "試験手順": "1. taskIdが非文字列型の場合をテスト。", "確認項目": "- context.errorに\"taskIdが不正\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "taskId空文字列: エラーログ記録", "試験観点": "taskId空文字列分岐の検証。", "試験対象": "TaskCancellationTimeoutFuncのtaskId検証分岐。", "試験手順": "1. taskIdが空文字列の場合をテスト。", "確認項目": "- context.errorに\"taskIdが不正\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "Task不存在: エラーログ記録", "試験観点": "Task不存在分岐の検証。", "試験対象": "TaskCancellationTimeoutFuncのTask取得失敗分岐。", "試験手順": "1. Taskが存在しない場合をテスト。", "確認項目": "- context.errorに\"存在しない\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "PENDING_CANCELLATION以外のステータス: エラーログ記録", "試験観点": "ステータス不正分岐の検証。", "試験対象": "TaskCancellationTimeoutFuncのステータス判定分岐。", "試験手順": "1. PENDING_CANCELLATION以外のステータスの場合をテスト。", "確認項目": "- context.errorに\"PENDING_CANCELLATION ではない\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "PENDING_CANCELLATION正常更新: 成功", "試験観点": "正常系フローの検証。", "試験対象": "TaskCancellationTimeoutFuncの正常系フロー。", "試験手順": "1. PENDING_CANCELLATIONのタスクを正常に更新する場合をテスト。", "確認項目": "- prisma.task.updateManyが正しいパラメータで呼ばれること。\n- context.logに成功メッセージが出力されること。"}, {"テストケース名": "楽観ロック失敗: エラーログ記録", "試験観点": "楽観ロック失敗分岐の検証。", "試験対象": "TaskCancellationTimeoutFuncの楽観ロック制御分岐。", "試験手順": "1. 楽観ロック失敗（0件更新）の場合をテスト。", "確認項目": "- context.errorに\"更新失敗または更新件数が0件\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "DB更新例外: エラーログ記録", "試験観点": "DB更新例外分岐の検証。", "試験対象": "TaskCancellationTimeoutFuncのDB更新例外分岐。", "試験手順": "1. DB更新で例外が発生する場合をテスト。", "確認項目": "- context.errorに\"エラー発生\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "formatTaskErrorMessage例外: エラーログ記録", "試験観点": "formatTaskErrorMessage例外分岐の検証。", "試験対象": "TaskCancellationTimeoutFuncのformatTaskErrorMessage例外分岐。", "試験手順": "1. formatTaskErrorMessageで例外が発生する場合をテスト。", "確認項目": "- context.errorに\"エラー発生\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "Task取得例外: エラーログ記録", "試験観点": "Task取得例外分岐の検証。", "試験対象": "TaskCancellationTimeoutFuncのTask取得例外分岐。", "試験手順": "1. Task取得で例外が発生する場合をテスト。", "確認項目": "- context.errorに\"エラー発生\"が出力されること。\n- 処理が終了すること。"}, {"テストケース名": "正常系: 完全フロー", "試験観点": "正常系フローの網羅的検証。", "試験対象": "TaskCancellationTimeoutFuncの正常系フロー。", "試験手順": "1. 正常系の完全なフローをテスト。", "確認項目": "- 全ての処理が正常に実行されること。\n- 適切なログが出力されること。"}]}}}, "TaskExecuteFunc.test.ts": {"displayName": "TaskExecuteFunc.test.ts", "subCategories": {"TaskExecuteFunc": {"displayName": "TaskExecuteFunc", "testCases": [{"テストケース名": "入力検証: message が null の場合、エラーログを出力して正常終了", "試験観点": "入力パラメータの検証処理", "試験対象": "TaskExecuteFunc の入力メッセージ検証ロジック", "試験手順": "1. message に null を渡す\r\n2. TaskExecuteFunc を呼び出す", "確認項目": "- context.error に適切なエラーメッセージが出力されること\r\n- 関数が正常終了すること（例外が発生しないこと）"}, {"テストケース名": "入力検証: message が文字列の場合、エラーログを出力して正常終了", "試験観点": "入力パラメータの検証処理", "試験対象": "TaskExecuteFunc の入力メッセージ検証ロジック", "試験手順": "1. message に文字列を渡す（オブジェクトではない）\r\n2. TaskExecuteFunc を呼び出す", "確認項目": "- context.error に適切なエラーメッセージが出力されること\r\n- 関数が正常終了すること"}, {"テストケース名": "入力検証: taskId が存在しない場合、エラーログを出力して正常終了", "試験観点": "taskId パラメータの検証処理", "試験対象": "TaskExecuteFunc の taskId 検証ロジック", "試験手順": "1. taskId が存在しないメッセージを渡す\r\n2. TaskExecuteFunc を呼び出す", "確認項目": "- context.error に taskId 不足のエラーメッセージが出力されること\r\n- 関数が正常終了すること"}, {"テストケース名": "入力検証: taskId が文字列でない場合、エラーログを出力して正常終了", "試験観点": "taskId パラメータの検証処理", "試験対象": "TaskExecuteFunc の taskId 検証ロジック", "試験手順": "1. taskId が数値のメッセージを渡す\r\n2. TaskExecuteFunc を呼び出す", "確認項目": "- context.error に taskId 不正のエラーメッセージが出力されること\r\n- 関数が正常終了すること"}, {"テストケース名": "データベース操作: タスクが存在しない場合、エラーログを出力して正常終了", "試験観点": "タスク情報取得時の異常処理", "試験対象": "TaskExecuteFunc のタスク情報取得ロジック", "試験手順": "1. prisma.task.findUnique が null を返すようにモック\r\n2. 有効な taskId でTaskExecuteFunc を呼び出す", "確認項目": "- context.error にタスク不存在のエラーメッセージが出力されること\r\n- 関数が正常終了すること"}, {"テストケース名": "データベース操作: タスク情報が不完全な場合、EMET0009エラーでタスクを更新", "試験観点": "タスク情報の構成不備検証", "試験対象": "TaskExecuteFunc のタスク情報検証ロジック", "試験手順": "1. 必須フィールドが不足したタスクを返すようにモック\r\n2. TaskExecuteFunc を呼び出す", "確認項目": "- prisma.task.updateMany が EMET0009 エラーで呼ばれること\r\n- 関数が正常終了すること"}, {"テストケース名": "データベース操作: タスクステータスがQUEUEDでない場合、警告ログを出力して正常終了", "試験観点": "タスクステータス確認処理", "試験対象": "TaskExecuteFunc のタスクステータス検証ロジック", "試験手順": "1. ステータスが QUEUED でないタスクを返すようにモック\r\n2. TaskExecuteFunc を呼び出す", "確認項目": "- context.warn に適切なメッセージが出力されること\r\n- 関数が正常終了すること"}, {"テストケース名": "データベース操作: コンテナ実行状態が存在しない場合、EMET0007エラーでタスクを更新", "試験観点": "コンテナ実行状態取得時の異常処理", "試験対象": "TaskExecuteFunc のコンテナ実行状態取得ロジック", "試験手順": "1. 正常なタスクを返すようにモック\r\n2. prisma.containerConcurrencyStatus.findUnique が null を返すようにモック\r\n3. TaskExecuteFunc を呼び出す", "確認項目": "- prisma.task.updateMany が EMET0007 エラーで呼ばれること\r\n- 関数が正常終了すること"}, {"テストケース名": "データベース操作: コンテナ実行状態のstatus欠落の場合、EMET0009エラーでタスクを更新", "試験観点": "コンテナ実行状態の構成不備検証", "試験対象": "TaskExecuteFunc のコンテナ実行状態検証ロジック", "試験手順": "1. 正常なタスクを返すようにモック\r\n2. status フィールドが欠落したコンテナ実行状態を返すようにモック\r\n3. TaskExecuteFunc を呼び出す", "確認項目": "- prisma.task.updateMany が EMET0009 エラーで呼ばれること\r\n- 関数が正常終了すること"}, {"テストケース名": "データベース操作: コンテナ状態がBUSYの場合、EMET0001エラーでタスクを更新", "試験観点": "コンテナステータス確認処理", "試験対象": "TaskExecuteFunc のコンテナステータス検証ロジック", "試験手順": "1. 正常なタスクとBUSY状態のコンテナを返すようにモック\r\n2. TaskExecuteFunc を呼び出す", "確認項目": "- prisma.task.updateMany が EMET0001 エラーで呼ばれること\r\n- 関数が正常終了すること"}, {"テストケース名": "データベース操作: コンテナ状態が未知のステータスの場合、EMET0001エラーでタスクを更新", "試験観点": "コンテナステータス確認処理（未知のステータス）", "試験対象": "TaskExecuteFunc のコンテナステータス検証ロジック", "試験手順": "1. 正常なタスクと未知のステータスのコンテナを返すようにモック\r\n2. TaskExecuteFunc を呼び出す", "確認項目": "- prisma.task.updateMany が EMET0001 エラーで呼ばれること\r\n- 関数が正常終了すること"}, {"テストケース名": "正常系: MgmtItemImportタスクの完全な処理フロー", "試験観点": "管理項目定義インポートタスクの正常処理", "試験対象": "TaskExecuteFunc の MgmtItemImport タスク処理ロジック", "試験手順": "1. 正常な MgmtItemImport タスクとIDLE状態のコンテナを設定\r\n2. 全ての外部依存を正常動作でモック\r\n3. TaskExecuteFunc を呼び出す", "確認項目": "- Azure Files 作業ディレクトリが作成されること\r\n- Blob からファイルがダウンロードされること\r\n- タスクステータスが RUNBOOK_SUBMITTED に更新されること\r\n- Azure Automation ジョブが作成されること\r\n- クリーンアップ処理が実行されること"}, {"テストケース名": "正常系: MgmtItemExportタスクの完全な処理フロー", "試験観点": "管理項目定義エクスポートタスクの正常処理", "試験対象": "TaskExecuteFunc の MgmtItemExport タスク処理ロジック", "試験手順": "1. 正常な MgmtItemExport タスクとIDLE状態のコンテナを設定\r\n2. 全ての外部依存を正常動作でモック\r\n3. TaskExecuteFunc を呼び出す", "確認項目": "- Azure Files 作業ディレクトリが作成されること\r\n- Blob ファイル処理がスキップされること\r\n- 正しい Runbook 名で Azure Automation ジョブが作成されること"}, {"テストケース名": "正常系: OpLogExportタスクの完全な処理フロー", "試験観点": "操作ログエクスポートタスクの正常処理", "試験対象": "TaskExecuteFunc の OpLogExport タスク処理ロジック", "試験手順": "1. 正常な OpLogExport タスクとIDLE状態のコンテナを設定\r\n2. 全ての外部依存を正常動作でモック\r\n3. TaskExecuteFunc を呼び出す", "確認項目": "- 正しい Runbook 名で Azure Automation ジョブが作成されること"}, {"テストケース名": "異常系: 未定義のタスクタイプの場合、事前チェックでEMET0009エラーでタスクを更新", "試験観点": "未定義タスクタイプの処理", "試験対象": "TaskExecuteFunc の環境変数事前チェックロジック", "試験手順": "1. 未定義のタスクタイプを持つタスクを設定\r\n2. TaskExecuteFunc を呼び出す", "確認項目": "- 事前チェックで未定義タスクタイプが検出されること\r\n- EMET0009 エラーでタスクが更新されること\r\n- 補償処理は実行されないこと（早期失敗）"}, {"テストケース名": "異常系: Runbook環境変数が未設定の場合、事前チェックでEMET0009エラーでタスクを更新", "試験観点": "Runbook環境変数が未設定の場合の処理", "試験対象": "TaskExecuteFunc の環境変数事前チェックロジック", "試験手順": "1. RUNBOOK_MGMT_ITEM_IMPORT 環境変数を削除\r\n2. 管理項目インポートタスクでTaskExecuteFuncを呼び出す", "確認項目": "- 事前チェックで環境変数不足が検出されること\r\n- EMET0009 エラーでタスクが更新されること\r\n- 補償処理は実行されないこと（早期失敗）"}, {"テストケース名": "異常系: AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF環境変数が未設定の場合、事前チェックでEMET0009エラーでタスクを更新", "試験観点": "AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF環境変数が未設定の場合の処理", "試験対象": "TaskExecuteFunc の環境変数事前チェックロジック", "試験手順": "1. AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF 環境変数を削除\r\n2. 管理項目インポートタスクでTaskExecuteFuncを呼び出す", "確認項目": "- 事前チェックで環境変数不足が検出されること\r\n- EMET0009 エラーでタスクが更新されること\r\n- 補償処理は実行されないこと（早期失敗）"}, {"テストケース名": "異常系: MgmtItemExportタスクでRunbook環境変数が未設定の場合、事前チェックでEMET0009エラーでタスクを更新", "試験観点": "MgmtItemExportタスクでRunbook環境変数が未設定の場合の処理", "試験対象": "TaskExecuteFunc の環境変数事前チェックロジック", "試験手順": "1. RUNBOOK_MGMT_ITEM_EXPORT 環境変数を削除\r\n2. 管理項目エクスポートタスクでTaskExecuteFuncを呼び出す", "確認項目": "- 事前チェックで環境変数不足が検出されること\r\n- EMET0009 エラーでタスクが更新されること"}, {"テストケース名": "異常系: OpLogExportタスクでRunbook環境変数が未設定の場合、事前チェックでEMET0009エラーでタスクを更新", "試験観点": "OpLogExportタスクでRunbook環境変数が未設定の場合の処理", "試験対象": "TaskExecuteFunc の環境変数事前チェックロジック", "試験手順": "1. RUNBOOK_OPLOG_EXPORT 環境変数を削除\r\n2. 操作ログエクスポートタスクでTaskExecuteFuncを呼び出す", "確認項目": "- 事前チェックで環境変数不足が検出されること\r\n- EMET0009 エラーでタスクが更新されること"}, {"テストケース名": "異常系: Azure Files作業ディレクトリ作成失敗の場合、補償処理を実行してEMET0002エラーでタスクを更新", "試験観点": "Azure Files作業ディレクトリ作成失敗の処理", "試験対象": "TaskExecuteFunc のAzure Files操作エラー処理", "試験手順": "1. Azure Files作業ディレクトリ作成でエラーを発生させる\r\n2. TaskExecuteFunc を呼び出す", "確認項目": "- 補償処理が実行されること\r\n- EMET0002 エラーでタスクが更新されること"}, {"テストケース名": "異常系: Azure Blob Storage操作失敗の場合、補償処理を実行してEMET0003エラーでタスクを更新", "試験観点": "Azure Blob Storage操作失敗の処理", "試験対象": "TaskExecuteFunc のAzure Blob操作エラー処理", "試験手順": "1. MgmtItemImportタスクでBlob操作エラーを発生させる\r\n2. TaskExecuteFunc を呼び出す", "確認項目": "- 補償処理が実行されること\r\n- EMET0003 エラーでタスクが更新されること"}, {"テストケース名": "異常系: Azure Automation API呼び出し失敗の場合、EMET0013エラーでタスクを更新", "試験観点": "Azure Automation API呼び出し失敗の処理", "試験対象": "TaskExecuteFunc のAzure Automation API エラー処理", "試験手順": "1. Azure Automation API呼び出しでエラーを発生させる\r\n2. TaskExecuteFunc を呼び出す", "確認項目": "- updateTaskToErrorAfterRunbookSubmitted が呼ばれること\r\n- EMET0013 エラーでタスクが更新されること"}, {"テストケース名": "異常系: データベース更新失敗の場合、補償処理を実行してEMET0007エラーでタスクを更新", "試験観点": "データベース更新失敗の処理", "試験対象": "TaskExecuteFunc のPrismaエラー処理", "試験手順": "1. コンテナステータス更新でPrismaエラーを発生させる\r\n2. TaskExecuteFunc を呼び出す", "確認項目": "- 補償処理が実行されること\r\n- EMET0007 エラーでタスクが更新されること"}, {"テストケース名": "異常系: タスクステータス更新で楽観ロック失敗の場合、補償処理を実行", "試験観点": "タスクステータス更新時の楽観ロック失敗処理", "試験対象": "TaskExecuteFunc の楽観ロック制御", "試験手順": "1. タスクステータス更新で更新件数0を返すようにモック\r\n2. TaskExecuteFunc を呼び出す", "確認項目": "- 補償処理が実行されること\r\n- 適切なログが出力されること"}, {"テストケース名": "補助関数: updateTaskToError で更新件数が0の場合、例外をthrow", "試験観点": "updateTaskToError関数の正常動作", "試験対象": "updateTaskToError 補助関数", "試験手順": "1. updateTaskToError が正常に実行される条件を設定\r\n2. エラー更新が失敗する条件を設定", "確認項目": "- 更新件数が0の場合に例外がthrowされること"}, {"テストケース名": "補助関数: updateTaskToErrorAfterRunbookSubmitted で更新件数が0の場合、例外をthrow", "試験観点": "updateTaskToErrorAfterRunbookSubmitted関数の正常動作", "試験対象": "updateTaskToErrorAfterRunbookSubmitted 補助関数", "試験手順": "1. Azure Automation API エラーを発生させる\r\n2. updateTaskToErrorAfterRunbookSubmitted での更新が失敗するようにモック", "確認項目": "- 更新件数が0の場合に例外がthrowされること"}, {"テストケース名": "正常系: クリーンアップ処理でエラーが発生しても処理全体は成功", "試験観点": "クリーンアップ処理のエラー処理", "試験対象": "TaskExecuteFunc のクリーンアップ処理エラーハンドリング", "試験手順": "1. 正常なタスク処理を設定\r\n2. クリーンアップ処理でエラーを発生させる", "確認項目": "- クリーンアップエラーが処理全体に影響しないこと\r\n- 適切なエラーログが出力されること"}, {"テストケース名": "補償処理: taskID/更新日時不正時のスキップ処理", "試験観点": "補償処理でのタスクID/更新日時不正分岐の検証（設計文書の補償処理要件）。", "試験対象": "TaskExecuteFuncの補償処理分岐。", "試験手順": "1. 補償処理でtaskIdまたはoriginalTaskUpdatedAtが不正な場合のスキップ処理をテスト。", "確認項目": "- context.errorに\"タスクステータス更新をスキップします\"が出力されること。\r\n- タスク更新処理がスキップされること。"}, {"テストケース名": "補償処理: EMET0001エラー処理分岐", "試験観点": "EMET0001エラー補償処理分岐の検証（設計文書の競合処理要件）。", "試験対象": "TaskExecuteFuncの補償処理分岐。", "試験手順": "1. EMET0001エラーコードを持つエラーが発生し、補償処理でEMET0001エラー処理分岐が実行される場合をテスト。", "確認項目": "- 適切な補償処理が実行されること。\r\n- EMET0001エラーでタスクが更新されること。"}, {"テストケース名": "補償処理: Azure Automation例外処理", "試験観点": "Azure Automation例外補償処理分岐の検証（設計文書のRunbook作成例外処理要件）。", "試験対象": "TaskExecuteFuncの補償処理分岐。", "試験手順": "1. Azure Automation Runbook作成でエラーが発生する場合をテスト。", "確認項目": "- 適切な補償処理が実行されること。\r\n- 一般的なエラーでタスクが更新されること。"}, {"テストケース名": "補償処理: EMET0009エラー処理分岐", "試験観点": "EMET0009エラー補償処理分岐の検証（設計文書のタスク情報検証要件）。", "試験対象": "TaskExecuteFuncの補償処理分岐。", "試験手順": "1. EMET0009エラーが発生し、補償処理でEMET0009エラー処理分岐が実行される場合をテスト。", "確認項目": "- EMET0009エラーでタスクが更新されること。\r\n- 補償処理が適切に実行されること。"}, {"テストケース名": "補償処理: Prismaエラー処理分岐", "試験観点": "Prismaエラー補償処理分岐の検証（設計文書のDB例外処理要件）。", "試験対象": "TaskExecuteFuncの補償処理分岐。", "試験手順": "1. Prismaエラーが発生し、補償処理でPrismaエラー処理分岐が実行される場合をテスト。", "確認項目": "- EMET0007エラーでタスクが更新されること。\r\n- 補償処理が適切に実行されること。"}, {"テストケース名": "補償処理: 作業ディレクトリ不存在時の正常処理", "試験観点": "作業ディレクトリ不存在時の正常処理の検証。", "試験対象": "deleteTaskWorkspaceDirectory関数の存在チェック分岐。", "試験手順": "1. deleteTaskWorkspaceDirectory関数で作業ディレクトリが存在しない場合をテスト。", "確認項目": "- 作業ディレクトリが存在しない旨のログが出力されること。\r\n- 処理が正常に継続されること。"}, {"テストケース名": "補償処理: 作業ディレクトリ削除失敗時のエラー処理", "試験観点": "補償処理での作業ディレクトリ削除失敗分岐の検証（設計文書の補償処理要件）。", "試験対象": "TaskExecuteFuncの補償処理分岐。", "試験手順": "1. 補償処理で作業ディレクトリ削除が失敗する場合をテスト。", "確認項目": "- 作業ディレクトリ削除失敗のエラーログが出力されること。\r\n- 処理が継続されること。"}, {"テストケース名": "補償処理: taskID不正時の真のスキップ処理", "試験観点": "補償処理でのタスクID/更新日時不正分岐の検証（設計文書の補償処理要件）。", "試験対象": "TaskExecuteFuncの補償処理分岐。", "試験手順": "1. taskIdまたはoriginalTaskUpdatedAtが不正な場合の補償処理スキップをテスト。", "確認項目": "- タスクステータス更新がスキップされること。\r\n- 適切なスキップログが出力されること。"}, {"テストケース名": "補償処理: EMET0009エラー処理分岐の真の実行", "試験観点": "EMET0009エラー補償処理分岐の検証（設計文書のタスク情報検証要件）。", "試験対象": "TaskExecuteFuncの補償処理分岐。", "試験手順": "1. EMET0009エラーが発生し、補償処理でEMET0009エラー処理分岐が実行される場合をテスト。", "確認項目": "- EMET0009エラーでタスクが更新されること。\r\n- 補償処理が適切に実行されること。"}, {"テストケース名": "異常系: タスクステータス更新で楽観ロック失敗（ユーザー取消）の場合、補償処理を実行", "試験観点": "設計文書の楽観ロック失敗補償処理要件の検証。", "試験対象": "TaskExecuteFuncの楽観ロック失敗補償処理。\r\n* 注意：この分岐は複雑な集成テストでは再現困難なため、\r\n既存の正常系テストをベースに最小限の変更で楽観ロック失敗をシミュレート。", "試験手順": "1. 楽観ロック失敗時の補償処理を直接テスト。", "確認項目": "- 補償処理の警告ログが出力されること。\r\n- 作業ディレクトリ削除が実行されること。\r\n- コンテナステータスがIDLEに復旧されること。"}]}}}, "TaskExecuteTimeoutFunc.test.ts": {"displayName": "TaskExecuteTimeoutFunc.test.ts", "subCategories": {"TaskExecuteTimeoutFunc": {"displayName": "TaskExecuteTimeoutFunc", "testCases": [{"テストケース名": "メッセージがnull: 処理終了", "試験観点": "メッセージnull分岐の検証。", "試験対象": "TaskExecuteTimeoutFuncのメッセージ検証分岐。", "試験手順": "1. messageがnullの場合をテスト。", "確認項目": "- context.errorに\"メッセージが不正\"が出力されること。\n- getAutomationJobStatusが呼ばれないこと。"}, {"テストケース名": "メッセージが非object: 処理終了", "試験観点": "メッセージ型検証分岐の検証。", "試験対象": "TaskExecuteTimeoutFuncのメッセージ検証分岐。", "試験手順": "1. messageが文字列（非object）の場合をテスト。", "確認項目": "- context.errorに\"メッセージが不正\"が出力されること。\n- getAutomationJobStatusが呼ばれないこと。"}, {"テストケース名": "taskIdが空文字列: 処理終了", "試験観点": "taskId空文字列分岐の検証。", "試験対象": "TaskExecuteTimeoutFuncのtaskId検証分岐。", "試験手順": "1. taskIdが空文字列のメッセージを渡す。", "確認項目": "- context.errorに\"taskIdが不足/不正\"が出力されること。\n- getAutomationJobStatusが呼ばれないこと。"}, {"テストケース名": "taskIdが非文字列: 処理終了", "試験観点": "taskId型検証分岐の検証。", "試験対象": "TaskExecuteTimeoutFuncのtaskId検証分岐。", "試験手順": "1. taskIdが数値のメッセージを渡す。", "確認項目": "- context.errorに\"taskIdが不足/不正\"が出力されること。\n- getAutomationJobStatusが呼ばれないこと。"}, {"テストケース名": "Azure Automation Job存在: 補償不要で終了", "試験観点": "Job存在時の早期終了分岐の検証。", "試験対象": "TaskExecuteTimeoutFuncのJob存在確認分岐。", "試験手順": "1. Azure Automation Jobが存在する場合をテスト。", "確認項目": "- context.logに\"補償処理は不要\"が出力されること。\n- prisma.task.findUniqueが呼ばれないこと。"}, {"テストケース名": "Azure Automation API失敗: 処理終了", "試験観点": "API失敗時の早期終了分岐の検証。", "試験対象": "TaskExecuteTimeoutFuncのAPI失敗分岐。", "試験手順": "1. Azure Automation API呼び出しが失敗する場合をテスト。", "確認項目": "- context.errorに\"API呼び出し失敗\"が出力されること。\n- prisma.task.findUniqueが呼ばれないこと。"}, {"テストケース名": "Azure Files削除失敗: 処理継続", "試験観点": "Files削除失敗時の処理継続分岐の検証。", "試験対象": "TaskExecuteTimeoutFuncのFiles削除失敗分岐。", "試験手順": "1. Azure Files削除が失敗する場合をテスト。\n2. 他の処理は正常に実行される。", "確認項目": "- context.logに\"削除失敗\"が出力されること。\n- 後続処理が継続されること。"}, {"テストケース名": "Task不存在: 処理終了", "試験観点": "Task不存在時の早期終了分岐の検証。", "試験対象": "TaskExecuteTimeoutFuncのTask取得失敗分岐。", "試験手順": "1. Taskが存在しない場合をテスト。", "確認項目": "- context.errorに\"存在しません\"が出力されること。\n- prisma.containerConcurrencyStatus.updateManyが呼ばれないこと。"}, {"テストケース名": "Container状態更新0件: 処理継続", "試験観点": "Container更新0件時の処理継続分岐の検証。", "試験対象": "TaskExecuteTimeoutFuncのContainer更新分岐。", "試験手順": "1. Container状態更新が0件の場合をテスト。", "確認項目": "- context.logに\"更新件数0件\"が出力されること。\n- 後続処理が継続されること。"}, {"テストケース名": "Container状態更新失敗: 処理継続", "試験観点": "Container更新失敗時の処理継続分岐の検証。", "試験対象": "TaskExecuteTimeoutFuncのContainer更新失敗分岐。", "試験手順": "1. Container状態更新が失敗する場合をテスト。", "確認項目": "- context.logに\"更新失敗\"が出力されること。\n- 後続処理が継続されること。"}, {"テストケース名": "TaskステータスPENDING_CANCELLATION: Task更新スキップ", "試験観点": "PENDING_CANCELLATION状態時のTask更新スキップ分岐の検証。", "試験対象": "TaskExecuteTimeoutFuncのTask状態判定分岐。", "試験手順": "1. TaskのstatusがPENDING_CANCELLATIONの場合をテスト。", "確認項目": "- context.logに\"EMET0005エラー更新をスキップ\"が出力されること。\n- prisma.task.updateManyが呼ばれないこと（Task更新がスキップされる）。"}, {"テストケース名": "TaskステータスCANCELLED: Task更新スキップ", "試験観点": "CANCELLED状態時のTask更新スキップ分岐の検証。", "試験対象": "TaskExecuteTimeoutFuncのTask状態判定分岐。", "試験手順": "1. TaskのstatusがCANCELLEDの場合をテスト。", "確認項目": "- context.logに\"EMET0005エラー更新をスキップ\"が出力されること。\n- prisma.task.updateManyが呼ばれないこと（Task更新がスキップされる）。"}, {"テストケース名": "Task更新0件: 処理継続", "試験観点": "Task更新0件時の処理継続分岐の検証。", "試験対象": "TaskExecuteTimeoutFuncのTask更新分岐。", "試験手順": "1. Task更新が0件の場合をテスト。", "確認項目": "- context.logに\"更新件数0件\"が出力されること。\n- 処理が正常に完了すること。"}, {"テストケース名": "Task更新失敗: 処理継続", "試験観点": "Task更新失敗時の処理継続分岐の検証。", "試験対象": "TaskExecuteTimeoutFuncのTask更新失敗分岐。", "試験手順": "1. Task更新が失敗する場合をテスト。", "確認項目": "- context.logに\"更新失敗\"が出力されること。\n- 処理が正常に完了すること。"}, {"テストケース名": "正常系: 完全フロー", "試験観点": "正常系フローの網羅的検証。", "試験対象": "TaskExecuteTimeoutFuncの正常系フロー。", "試験手順": "1. 正常系の完全なフローをテスト。", "確認項目": "- 全ての処理が正常に実行されること。\n- 適切なログが出力されること。"}, {"テストケース名": "予期せぬ例外: エラーログ記録", "試験観点": "全体例外処理分岐の検証。", "試験対象": "TaskExecuteTimeoutFuncの全体例外処理。", "試験手順": "1. 予期せぬ例外が発生する場合をテスト。", "確認項目": "- context.errorに\"予期せぬ内部エラー\"が出力されること。\n- 処理が正常に終了すること。"}, {"テストケース名": "taskId未設定時の例外: unknownログ記録", "試験観点": "taskId未設定時の例外処理分岐の検証。", "試験対象": "TaskExecuteTimeoutFuncの例外処理でのtaskId分岐。", "試験手順": "1. taskIdが設定される前に例外が発生する場合をテスト。", "確認項目": "- context.logに\"unknown\"が出力されること。"}, {"テストケース名": "Container情報null: undefined使用", "試験観点": "null値のContainer情報での分岐検証。", "試験対象": "TaskExecuteTimeoutFuncのContainer更新分岐。", "試験手順": "1. TaskのtargetVmNameとtargetContainerNameがnullの場合をテスト。", "確認項目": "- Container更新でundefinedが使用されること。"}]}}}}}, "jcs-backend-services-long-running": {"displayName": "Runbook ジョブ Function App", "type": "backend", "categories": {"lib": {"displayName": "lib", "subCategories": {"azureClients": {"displayName": "azureClients", "testCases": [{"テストケース名": "環境変数検証: 全ての必要な環境変数が設定されている場合、正常にモジュールがロードされる", "試験観点": "環境変数の存在確認処理", "試験対象": "azureClients モジュールの環境変数検証ロジック", "試験手順": "1. 必要な環境変数が全て設定されている状態でモジュールをインポート\r\n2. モジュールが正常にロードされることを確認", "確認項目": "- モジュールが例外なく正常にロードされること"}, {"テストケース名": "環境変数検証: AZURE_STORAGE_CONNECTION_STRING が未設定の場合、エラーがスローされる", "試験観点": "環境変数不足時のエラー処理", "試験対象": "azureClients モジュールの環境変数検証ロジック", "試験手順": "1. AZURE_STORAGE_CONNECTION_STRING を削除\r\n2. モジュールのインポートを試行", "確認項目": "- 適切なエラーメッセージが含まれた例外がスローされること"}, {"テストケース名": "環境変数検証: AZURE_STORAGE_ACCOUNT_NAME が未設定の場合、エラーがスローされる", "試験観点": "環境変数不足時のエラー処理", "試験対象": "azureClients モジュールの環境変数検証ロジック", "試験手順": "1. AZURE_STORAGE_ACCOUNT_NAME を削除\r\n2. モジュールのインポートを試行", "確認項目": "- 適切なエラーメッセージが含まれた例外がスローされること"}, {"テストケース名": "正常系: createBlobServiceClient が正しいパラメータでクライアントを作成", "試験観点": "Azure Blob Service クライアント作成の正常処理", "試験対象": "createBlobServiceClient 関数", "試験手順": "1. createBlobServiceClient を呼び出す\r\n2. 正しいパラメータで BlobServiceClient が作成されることを確認", "確認項目": "- BlobServiceClient が正しい URL と認証情報で作成されること\r\n- 作成されたクライアントが返されること"}, {"テストケース名": "正常系: createShareServiceClient が正しい接続文字列でクライアントを作成", "試験観点": "Azure Files Service クライアント作成の正常処理", "試験対象": "createShareServiceClient 関数", "試験手順": "1. createShareServiceClient を呼び出す\r\n2. 正しい接続文字列で ShareServiceClient が作成されることを確認", "確認項目": "- ShareServiceClient が正しい接続文字列で作成されること\r\n- 作成されたクライアントが返されること"}, {"テストケース名": "正常系: createServiceBusClient が正しいパラメータでクライアントを作成", "試験観点": "Azure Service Bus クライアント作成の正常処理", "試験対象": "createServiceBusClient 関数", "試験手順": "1. createServiceBusClient を呼び出す\r\n2. 正しいパラメータで ServiceBusClient が作成されることを確認", "確認項目": "- ServiceBusClient が正しい名前空間と認証情報で作成されること\r\n- 作成されたクライアントが返されること"}, {"テストケース名": "正常系: createAutomationJob が正しいパラメータでジョブを作成", "試験観点": "Azure Automation ジョブ作成の正常処理", "試験対象": "createAutomationJob 関数", "試験手順": "1. 正常なパラメータで createAutomationJob を呼び出す\r\n2. 正しい URL とボディで fetch が呼ばれることを確認", "確認項目": "- 認証トークンが正しく取得されること\r\n- 正しい URL と HTTP メソッドで fetch が呼ばれること\r\n- 正しいリクエストボディが送信されること"}, {"テストケース名": "異常系: createAutomationJob でAPI呼び出しが失敗した場合、エラーがスローされる", "試験観点": "Azure Automation ジョブ作成の異常処理", "試験対象": "createAutomationJob 関数", "試験手順": "1. fetch が失敗レスポンスを返すようにモック\r\n2. createAutomationJob を呼び出す", "確認項目": "- 適切なエラーメッセージで例外がスローされること"}, {"テストケース名": "正常系: stopAutomationJob が正しいパラメータでジョブを停止", "試験観点": "Azure Automation ジョブ停止の正常処理", "試験対象": "stopAutomationJob 関数", "試験手順": "1. 正常なパラメータで stopAutomationJob を呼び出す\r\n2. 正しい URL とメソッドで fetch が呼ばれることを確認", "確認項目": "- 正しい URL と HTTP メソッドで fetch が呼ばれること\r\n- 認証ヘッダーが正しく設定されること"}, {"テストケース名": "異常系: stopAutomationJob でAPI呼び出しが失敗した場合、エラーがスローされる", "試験観点": "Azure Automation ジョブ停止の異常処理", "試験対象": "stopAutomationJob 関数", "試験手順": "1. fetch が失敗レスポンスを返すようにモック\r\n2. stopAutomationJob を呼び出す", "確認項目": "- 適切なエラーメッセージで例外がスローされること"}, {"テストケース名": "正常系: getAutomationJobStatus でジョブが存在する場合、正しいデータが返される", "試験観点": "Azure Automation ジョブ状態取得の正常処理（ジョブ存在）", "試験対象": "getAutomationJobStatus 関数", "試験手順": "1. fetch が 200 レスポンスを返すようにモック\r\n2. getAutomationJobStatus を呼び出す", "確認項目": "- 正しい URL と HTTP メソッドで fetch が呼ばれること\r\n- ジョブデータと正しいステータスが返されること\r\n- exists フラグが true であること"}, {"テストケース名": "正常系: getAutomationJobStatus でジョブが存在しない場合、404ステータスが返される", "試験観点": "Azure Automation ジョブ状態取得の正常処理（ジョブ不存在）", "試験対象": "getAutomationJobStatus 関数", "試験手順": "1. fetch が 404 レスポンスを返すようにモック\r\n2. getAutomationJobStatus を呼び出す", "確認項目": "- 正しいステータスコードが返されること\r\n- exists フラグが false であること\r\n- jobData が undefined であること"}, {"テストケース名": "異常系: getAutomationJobStatus でAPI呼び出しが失敗した場合、エラーがスローされる", "試験観点": "Azure Automation ジョブ状態取得の異常処理", "試験対象": "getAutomationJobStatus 関数", "試験手順": "1. fetch が 500 レスポンスを返すようにモック\r\n2. getAutomationJobStatus を呼び出す", "確認項目": "- 適切なエラーメッセージで例外がスローされること"}, {"テストケース名": "正常系: authenticatedFetch で相対URLが正しく完全URLに変換される", "試験観点": "認証付きfetch関数の相対URL処理", "試験対象": "authenticatedFetch 関数（間接的にテスト）", "試験手順": "1. 相対URLでAzure Automation APIを呼び出す\r\n2. 正しい完全URLが構築されることを確認", "確認項目": "- 相対URLが正しく完全URLに変換されること\r\n- 認証ヘッダーが正しく設定されること"}, {"テストケース名": "正常系: authenticatedFetch で完全URLがそのまま使用される", "試験観点": "認証付きfetch関数の完全URL処理", "試験対象": "authenticatedFetch 関数（間接的にテスト）", "試験手順": "1. 完全URLを直接指定してテスト\r\n2. URLがそのまま使用されることを確認", "確認項目": "- 完全URLがそのまま使用されること\r\n- 認証ヘッダーが正しく設定されること"}, {"テストケース名": "異常系: Azure認証が失敗した場合、エラーが伝播される", "試験観点": "Azure認証失敗時のエラー処理", "試験対象": "authenticatedFetch 関数（間接的にテスト）", "試験手順": "1. DefaultAzureCredential.getToken が失敗するようにモック\r\n2. Azure Automation API を呼び出す", "確認項目": "- 認証エラーが適切に伝播されること"}]}, "cleanup": {"displayName": "cleanup", "testCases": [{"テストケース名": "正常系: タスク件数が上限内の場合、クリーンアップ不要で処理終了", "試験観点": "タスク件数が上限内の場合のクリーンアップ不要処理", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. 最大保持件数を10件に設定\r\n2. 現在のタスク件数を5件に設定\r\n3. cleanupOldTasks を呼び出す", "確認項目": "- クリーンアップ不要のログが出力されること\r\n- タスク削除処理が実行されないこと"}, {"テストケース名": "正常系: 削除対象タスクが存在しない場合、処理終了", "試験観点": "削除対象タスクが存在しない場合の処理", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. 最大保持件数を5件に設定\r\n2. 現在のタスク件数を10件に設定\r\n3. 削除対象タスクを0件に設定\r\n4. cleanupOldTasks を呼び出す", "確認項目": "- 削除対象タスクが存在しないログが出力されること\r\n- 削除処理が実行されないこと"}, {"テストケース名": "正常系: MgmtItemImportタスクの正常削除処理", "試験観点": "MgmtItemImportタスクの正常削除処理", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. 最大保持件数を1件に設定\r\n2. 現在のタスク件数を3件に設定\r\n3. MgmtItemImportタスクを削除対象に設定\r\n4. cleanupOldTasks を呼び出す", "確認項目": "- 操作ログが削除されること\r\n- Blobファイルが削除されること\r\n- タスクレコードが削除されること"}, {"テストケース名": "正常系: MgmtItemExportタスクの正常削除処理", "試験観点": "MgmtItemExportタスクの正常削除処理", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. MgmtItemExportタスクを削除対象に設定\r\n2. cleanupOldTasks を呼び出す", "確認項目": "- 正しいBlobプレフィックスでファイル削除が実行されること\r\n- assetsコンテナが使用されること"}, {"テストケース名": "正常系: OpLogExportタスクの正常削除処理", "試験観点": "OpLogExportタスクの正常削除処理", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. OpLogExportタスクを削除対象に設定\r\n2. cleanupOldTasks を呼び出す", "確認項目": "- 正しいBlobプレフィックスでファイル削除が実行されること\r\n- oplogsコンテナが使用されること"}, {"テストケース名": "正常系: licenseId未設定タスクのBlobフォルダ削除スキップ処理", "試験観点": "licenseIdが未設定のタスクのスキップ処理", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. licenseIdが未設定のタスクを削除対象に設定\r\n2. cleanupOldTasks を呼び出す", "確認項目": "- Blobフォルダ削除がスキップされること\r\n- 適切な警告ログが出力されること\r\n- タスクレコードは削除されること"}, {"テストケース名": "正常系: 削除対象Blobファイルが存在しない場合の処理", "試験観点": "削除対象Blobファイルが存在しない場合の処理", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. Blobファイルが存在しないタスクを削除対象に設定\r\n2. cleanupOldTasks を呼び出す", "確認項目": "- 削除対象ファイルが存在しないログが出力されること\r\n- タスクレコードは削除されること"}, {"テストケース名": "異常系: 個別タスク削除処理でエラーが発生した場合、エラーログ出力後に処理継続", "試験観点": "個別タスク削除処理でのエラーハンドリング", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. 操作ログ削除でエラーが発生するようにモック\r\n2. cleanupOldTasks を呼び出す", "確認項目": "- エラーログが出力されること\r\n- 処理が継続されること（他のタスクの処理に影響しない）"}, {"テストケース名": "異常系: クリーンアップ処理全体で致命的エラーが発生した場合、エラーログ出力", "試験観点": "クリーンアップ処理全体での致命的エラーハンドリング", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. 最大保持件数取得でエラーが発生するようにモック\r\n2. cleanupOldTasks を呼び出す", "確認項目": "- 致命的エラーログが出力されること\r\n- 例外がスローされないこと"}, {"テストケース名": "境界値: 最大保持件数の設定が存在しない場合、デフォルト値10件が使用される", "試験観点": "最大保持件数のデフォルト値処理", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. 最大保持件数の設定が存在しない状況をモック\r\n2. cleanupOldTasks を呼び出す", "確認項目": "- デフォルト値（10件）が使用されること\r\n- 適切なログが出力されること"}, {"テストケース名": "境界値: 複数タスクの一括削除処理", "試験観点": "複数タスクの一括削除処理", "試験対象": "cleanupOldTasks 関数", "試験手順": "1. 複数の削除対象タスクを設定\r\n2. cleanupOldTasks を呼び出す", "確認項目": "- 全てのタスクが順次処理されること\r\n- 各タスクの削除ログが出力されること"}]}, "utils": {"displayName": "utils", "testCases": [{"テストケース名": "有効なエラーコード: 正常メッセージ生成", "試験観点": "正常系メッセージ生成の検証。", "試験対象": "formatTaskErrorMessage関数の正常系。", "試験手順": "1. 有効なエラーコードでメッセージを生成する場合をテスト。", "確認項目": "- 正しいエラーメッセージが返されること。"}, {"テストケース名": "無効なエラーコード: 空文字列返却", "試験観点": "異常系メッセージ生成の検証。", "試験対象": "formatTaskErrorMessage関数の異常系。", "試験手順": "1. 無効なエラーコードでメッセージを生成する場合をテスト。", "確認項目": "- 空文字列が返されること。"}, {"テストケース名": "パラメータ付き: 正常置換", "試験観点": "パラメータ置換機能の検証。", "試験対象": "formatTaskErrorMessage関数のパラメータ置換。", "試験手順": "1. パラメータ付きでメッセージを生成する場合をテスト。", "確認項目": "- パラメータが正しく置換されること。"}, {"テストケース名": "パラメータなし: デフォルト空配列", "試験観点": "デフォルトパラメータ機能の検証。", "試験対象": "formatTaskErrorMessage関数のデフォルトパラメータ。", "試験手順": "1. パラメータなしでメッセージを生成する場合をテスト。", "確認項目": "- デフォルトの空配列が使用されること。"}, {"テストケース名": "PrismaClientエラー: true返却", "試験観点": "Prismaエラー判定の正常系検証。", "試験対象": "isPrismaError関数の正常系。", "試験手順": "1. PrismaClientエラーの場合をテスト。", "確認項目": "- trueが返されること。"}, {"テストケース名": "通常エラー: false返却", "試験観点": "Prismaエラー判定の異常系検証。", "試験対象": "isPrismaError関数の異常系。", "試験手順": "1. 通常のエラーの場合をテスト。", "確認項目": "- falseが返されること。"}, {"テストケース名": "Azure Files RestError: true返却", "試験観点": "Azure Filesエラー判定の正常系検証。", "試験対象": "isAzureFilesError関数の正常系。", "試験手順": "1. Azure Files RestErrorの場合をテスト。", "確認項目": "- trueが返されること。"}, {"テストケース名": "ShareError: true返却", "試験観点": "Azure Filesエラー判定の正常系検証。", "試験対象": "isAzureFilesError関数の正常系。", "試験手順": "1. ShareErrorの場合をテスト。", "確認項目": "- trueが返されること。"}, {"テストケース名": "通常エラー: false返却", "試験観点": "Azure Filesエラー判定の異常系検証。", "試験対象": "isAzureFilesError関数の異常系。", "試験手順": "1. 通常のエラーの場合をテスト。", "確認項目": "- falseが返されること。"}, {"テストケース名": "Azure Blob RestError: true返却", "試験観点": "Azure Blobエラー判定の正常系検証。", "試験対象": "isAzureBlobError関数の正常系。", "試験手順": "1. Azure Blob RestErrorの場合をテスト。", "確認項目": "- trueが返されること。"}, {"テストケース名": "通常エラー: false返却", "試験観点": "Azure Blobエラー判定の異常系検証。", "試験対象": "isAzureBlobError関数の異常系。", "試験手順": "1. 通常のエラーの場合をテスト。", "確認項目": "- falseが返されること。"}, {"テストケース名": "指定エラーコード存在: true返却", "試験観点": "エラーコード判定の正常系検証。", "試験対象": "hasErrorCode関数の正常系。", "試験手順": "1. 指定されたエラーコードを持つエラーの場合をテスト。", "確認項目": "- trueが返されること。"}, {"テストケース名": "異なるエラーコード: false返却", "試験観点": "エラーコード判定の異常系検証。", "試験対象": "hasErrorCode関数の異常系。", "試験手順": "1. 異なるエラーコードを持つエラーの場合をテスト。", "確認項目": "- falseが返されること。"}, {"テストケース名": "エラーコードなし: false返却", "試験観点": "エラーコード判定の異常系検証。", "試験対象": "hasErrorCode関数の異常系。", "試験手順": "1. エラーコードを持たないエラーの場合をテスト。", "確認項目": "- falseが返されること。"}, {"テストケース名": "タスクディレクトリ不存在: 正常終了", "試験観点": "ディレクトリ不存在分岐の検証。", "試験対象": "deleteTaskWorkspaceDirectory関数のディレクトリ不存在分岐。", "試験手順": "1. タスクディレクトリが存在しない場合をテスト。", "確認項目": "- context.logに\"存在しません\"が出力されること。\n- 処理が正常終了すること。"}, {"テストケース名": "importsディレクトリ不存在: 処理継続", "試験観点": "importsディレクトリ不存在分岐の検証。", "試験対象": "deleteTaskWorkspaceDirectory関数のimports削除分岐。", "試験手順": "1. importsディレクトリが存在しない場合をテスト。", "確認項目": "- context.logに\"imports/ディレクトリが存在しません\"が出力されること。\n- 処理が継続されること。"}, {"テストケース名": "importsファイル存在: ファイル削除", "試験観点": "importsファイル削除分岐の検証。", "試験対象": "deleteTaskWorkspaceDirectory関数のimportsファイル削除分岐。", "試験手順": "1. assetsfield_def.csvファイルが存在する場合をテスト。", "確認項目": "- ファイルが削除されること。\n- context.logに\"ファイル削除: imports/assetsfield_def.csv\"が出力されること。"}, {"テストケース名": "exportsファイル存在: ファイル削除", "試験観点": "exportsファイル削除分岐の検証。", "試験対象": "deleteTaskWorkspaceDirectory関数のexportsファイル削除分岐。", "試験手順": "1. exportsディレクトリにファイルが存在する場合をテスト。", "確認項目": "- ファイルが削除されること。\n- context.logに\"ファイル削除: exports/filename\"が出力されること。"}, {"テストケース名": "imports削除例外: エラーログ記録・処理継続", "試験観点": "imports削除例外分岐の検証。", "試験対象": "deleteTaskWorkspaceDirectory関数のimports削除例外処理分岐。", "試験手順": "1. imports削除で例外が発生する場合をテスト。", "確認項目": "- context.logにエラーメッセージが出力されること。\n- 処理が継続されること（例外は再スローされない）。"}, {"テストケース名": "exports削除例外: エラーログ記録・処理継続", "試験観点": "exports削除例外分岐の検証。", "試験対象": "deleteTaskWorkspaceDirectory関数のexports削除例外処理分岐。", "試験手順": "1. exports削除で例外が発生する場合をテスト。", "確認項目": "- context.logにエラーメッセージが出力されること。\n- 処理が継続されること（例外は再スローされない）。"}, {"テストケース名": "メイン削除例外: エラーログ記録・例外再スロー", "試験観点": "メイン削除例外分岐の検証。", "試験対象": "deleteTaskWorkspaceDirectory関数のメイン例外処理分岐。", "試験手順": "1. メイン処理で例外が発生する場合をテスト。", "確認項目": "- context.errorにエラーメッセージが出力されること。\n- 例外が再スローされること。"}, {"テストケース名": "正常系: 完全削除フロー", "試験観点": "正常系フローの網羅的検証。", "試験対象": "deleteTaskWorkspaceDirectory関数の正常系フロー。", "試験手順": "1. 正常系の完全なフローをテスト。", "確認項目": "- 全ての処理が正常に実行されること。\n- 適切なログが出力されること。"}]}}}, "RunbookProcessorFunc.test.ts": {"displayName": "RunbookProcessorFunc.test.ts", "subCategories": {"RunbookProcessorFunc": {"displayName": "RunbookProcessorFunc", "testCases": [{"テストケース名": "正常系: 操作ログエクスポート完了処理", "試験観点": "操作ログエクスポート完了時の正常処理検証（設計文書4.a要件）", "試験対象": "RunbookProcessorFuncのCompleted状態・OPLOG_EXPORT処理分岐", "試験手順": "1. automationJobStatus=Completed、taskType=OPLOG_EXPORTのメッセージを送信\r\n2. Azure Filesからexportoplog_*.zipファイルを取得\r\n3. Azure Blob StorageへのコピーとOperationLogレコード作成を実行", "確認項目": "- タスクステータスがCOMPLETED_SUCCESSに更新されること\r\n- OperationLogレコードが正しく作成されること\r\n- コンテナステータスがIDLEに更新されること"}, {"テストケース名": "正常系: 管理項目定義エクスポート完了処理", "試験観点": "管理項目定義エクスポート完了時の正常処理検証（設計文書4.b要件）", "試験対象": "RunbookProcessorFuncのCompleted状態・MGMT_ITEM_EXPORT処理分岐", "試験手順": "1. automationJobStatus=Completed、taskType=MGMT_ITEM_EXPORTのメッセージを送信\r\n2. Azure Filesからassetsfield_def.csvファイルを取得\r\n3. Azure Blob Storageへのコピーを実行", "確認項目": "- タスクステータスがCOMPLETED_SUCCESSに更新されること\r\n- assetsfield_def.csvが正しいパスにコピーされること"}, {"テストケース名": "異常系: 必須パラメータ不足", "試験観点": "必須パラメータ不足時のエラー処理検証（設計文書2要件）", "試験対象": "RunbookProcessorFuncのメッセージ解析・検証分岐", "試験手順": "1. taskIdまたはautomationJobStatusが不足したメッセージを送信\r\n2. エラーログ記録と例外throwを確認", "確認項目": "- 適切なエラーメッセージがログに記録されること\r\n- 例外がthrowされてリトライが発生すること"}, {"テストケース名": "異常系: タスク存在なし", "試験観点": "タスク存在チェックのエラー処理検証（設計文書3要件）", "試験対象": "RunbookProcessorFuncのタスク取得分岐", "試験手順": "1. 存在しないtaskIdでメッセージを送信\r\n2. エラーログ記録と例外throwを確認", "確認項目": "- タスク不存在エラーがログに記録されること\r\n- 例外がthrowされてリトライが発生すること"}, {"テストケース名": "異常系: タスクステータス不正", "試験観点": "タスクステータス不正時の処理終了検証（設計文書3要件）", "試験対象": "RunbookProcessorFuncのステータス判定分岐", "試験手順": "1. status=RUNBOOK_PROCESSING以外のタスクでメッセージを送信\r\n2. ログ記録と処理終了を確認", "確認項目": "- 適切なログメッセージが記録されること\r\n- 例外がthrowされずに正常終了すること"}, {"テストケース名": "異常系: 無効なメッセージ形式", "試験観点": "無効なメッセージ形式のエラー処理検証（設計文書1要件）", "試験対象": "RunbookProcessorFuncのメッセージ基本検証分岐", "試験手順": "1. nullまたは不正な形式のメッセージを送信\r\n2. エラーログ記録と例外throwを確認", "確認項目": "- 適切なエラーメッセージがログに記録されること\r\n- 例外がthrowされてリトライが発生すること"}, {"テストケース名": "補償処理: 楽観ロック失敗検出", "試験観点": "楽観ロック制御の正確性検証（設計文書7要件）", "試験対象": "RunbookProcessorFuncの楽観ロック制御分岐", "試験手順": "1. Task読取後に他プロセスがupdatedAtを更新した場合をシミュレート\r\n2. updateMany の count が 0 になることを確認\r\n3. トランザクションがロールバックされることを確認", "確認項目": "- 楽観ロック失敗が正しく検出されること\r\n- 適切なエラーメッセージが記録されること\r\n- 例外がthrowされてリトライが発生すること"}, {"テストケース名": "補償処理: コンテナ状態更新失敗", "試験観点": "コンテナ状態更新失敗時の補償処理検証（設計文書8要件）", "試験対象": "RunbookProcessorFuncのコンテナ状態更新分岐", "試験手順": "1. 正常なタスク処理後にコンテナ状態更新が失敗する場合をシミュレート\r\n2. updateMany の count が 0 になることを確認\r\n3. トランザクションがロールバックされることを確認", "確認項目": "- コンテナ状態更新失敗が正しく検出されること\r\n- 適切なエラーメッセージが記録されること\r\n- 例外がthrowされてリトライが発生すること"}, {"テストケース名": "境界条件: エクスポートファイル不存在", "試験観点": "エクスポートファイル不存在時のエラー処理検証（設計文書4.a.ii要件）", "試験対象": "RunbookProcessorFuncのファイル存在チェック分岐", "試験手順": "1. automationJobStatus=Completed、taskType=OPLOG_EXPORTでファイルが存在しない場合\r\n2. EMET0015エラーコードでタスクステータス更新を確認", "確認項目": "- タスクステータスがCOMPLETED_ERRORに更新されること\r\n- エラーコードEMET0015が設定されること"}, {"テストケース名": "境界条件: Failed状態・errordetail.txt存在", "試験観点": "Failed状態でerrordetail.txt存在時の処理検証（設計文書4.ii要件）", "試験対象": "RunbookProcessorFuncのFailed状態処理分岐", "試験手順": "1. automationJobStatus=Failedでerrordetail.txtが存在する場合\r\n2. ファイル内容を読み取りエラーメッセージに設定", "確認項目": "- errordetail.txtの内容がresultMessageに設定されること\r\n- エラーコードEMET0011が設定されること"}, {"テストケース名": "境界条件: Timeout状態・ジョブ停止処理", "試験観点": "Timeout状態でのジョブ停止処理検証（設計文書4.iii要件）", "試験対象": "RunbookProcessorFuncのTimeout状態処理分岐", "試験手順": "1. automationJobStatus=TimeoutでAzure Automation API呼び出し\r\n2. ジョブ停止成功後のタスクステータス更新を確認", "確認項目": "- stopAutomationJobが正しいtaskIdで呼び出されること\r\n- エラーコードEMET0005が設定されること"}, {"テストケース名": "境界条件: 未対応タスク種別エラー", "試験観点": "未対応タスク種別のエラー処理検証（設計文書4要件）", "試験対象": "RunbookProcessorFuncの未対応タスク種別分岐", "試験手順": "1. automationJobStatus=Completedで未対応のtaskTypeを送信\r\n2. 未対応タスク種別エラーが発生することを確認", "確認項目": "- 適切なエラーメッセージが記録されること\r\n- 例外がthrowされてリトライが発生すること"}, {"テストケース名": "補償処理: Azure Blob操作エラー", "試験観点": "Azure Blob操作エラー時の補償処理検証（設計文書補償要件）", "試験対象": "RunbookProcessorFuncのBlob操作エラー分岐", "試験手順": "1. 正常なタスク処理中にBlob操作エラーを発生させる\r\n2. EMET0003エラーコードでタスクステータス更新を確認", "確認項目": "- Blob操作エラーが正しく検出されること\r\n- エラーコードEMET0003が設定されること"}, {"テストケース名": "補償処理: Azure Files操作エラー", "試験観点": "Azure Files操作エラー時の補償処理検証（設計文書補償要件）", "試験対象": "RunbookProcessorFuncのFiles操作エラー分岐", "試験手順": "1. 正常なタスク処理中にFiles操作エラーを発生させる\r\n2. EMET0002エラーコードでタスクステータス更新を確認", "確認項目": "- Files操作エラーが正しく検出されること\r\n- エラーコードEMET0002が設定されること"}, {"テストケース名": "境界条件: 管理項目定義ファイル不存在", "試験観点": "管理項目定義ファイル不存在時のエラー処理検証（設計文書4.b.ii要件）", "試験対象": "RunbookProcessorFuncの管理項目定義ファイル存在チェック分岐", "試験手順": "1. automationJobStatus=Completed、taskType=MGMT_ITEM_EXPORTでファイルが存在しない場合\r\n2. EMET0015エラーコードでタスクステータス更新を確認", "確認項目": "- ファイル不存在が正しく検出されること\r\n- エラーコードEMET0015が設定されること"}, {"テストケース名": "境界条件: Failed状態・errordetail.txt不存在", "試験観点": "Failed状態でerrordetail.txt不存在時の処理検証（設計文書4.iii要件）", "試験対象": "RunbookProcessorFuncのFailed状態・errordetail.txt不存在分岐", "試験手順": "1. automationJobStatus=Failedでerrordetail.txtが存在しない場合\r\n2. EMET0012エラーコードでタスクステータス更新を確認", "確認項目": "- errordetail.txt不存在が正しく検出されること\r\n- エラーコードEMET0012が設定されること"}, {"テストケース名": "境界条件: メンテナンス状態処理", "試験観点": "メンテナンス状態の処理検証（設計文書4要件）", "試験対象": "RunbookProcessorFuncのRemoving/Stopped/Stopping状態処理分岐", "試験手順": "1. automationJobStatus=Stoppedでメッセージを送信\r\n2. EMET0010エラーコードでタスクステータス更新を確認", "確認項目": "- メンテナンス状態が正しく処理されること\r\n- エラーコードEMET0010が設定されること"}, {"テストケース名": "正常系: Resuming状態・ジョブ停止成功", "試験観点": "Resuming状態でジョブ停止成功時の処理検証（設計文書4要件）", "試験対象": "RunbookProcessorFuncのResuming状態・ジョブ停止成功分岐", "試験手順": "1. automationJobStatus=ResumingでAzure Automation API成功をシミュレート\r\n2. EMET0010エラーコードでタスクステータス更新を確認", "確認項目": "- ジョブ停止が正常に完了すること\r\n- エラーコードEMET0010が設定されること"}, {"テストケース名": "補償処理: Resuming状態・ジョブ停止失敗", "試験観点": "Resuming状態でジョブ停止失敗時の処理検証（設計文書4要件）", "試験対象": "RunbookProcessorFuncのResuming状態・ジョブ停止失敗分岐", "試験手順": "1. automationJobStatus=ResumingでAzure Automation API失敗をシミュレート\r\n2. エラーログ記録と例外throwを確認", "確認項目": "- ジョブ停止失敗が正しく検出されること\r\n- 例外がthrowされてリトライが発生すること"}, {"テストケース名": "補償処理: Timeout状態・ジョブ停止失敗", "試験観点": "Timeout状態でジョブ停止失敗時の処理検証（設計文書4要件）", "試験対象": "RunbookProcessorFuncのTimeout状態・ジョブ停止失敗分岐", "試験手順": "1. automationJobStatus=TimeoutでAzure Automation API失敗をシミュレート\r\n2. エラーログ記録と例外throwを確認", "確認項目": "- タイムアウト時ジョブ停止失敗が正しく検出されること\r\n- 例外がthrowされてリトライが発生すること"}, {"テストケース名": "異常系: 予期しないautomationJobStatus", "試験観点": "予期しないautomationJobStatusのエラー処理検証（設計文書要件）", "試験対象": "RunbookProcessorFuncの予期しないステータス分岐", "試験手順": "1. 未定義のautomationJobStatusでメッセージを送信\r\n2. エラーログ記録と例外throwを確認", "確認項目": "- 予期しないステータスが正しく検出されること\r\n- 例外がthrowされてリトライが発生すること"}, {"テストケース名": "境界条件: Azure Files削除失敗", "試験観点": "Azure Files作業ディレクトリ削除失敗時の処理検証（設計文書5要件）", "試験対象": "RunbookProcessorFuncのAzure Filesクリーンアップ分岐", "試験手順": "1. 正常なタスク処理後にAzure Files削除エラーを発生させる\r\n2. エラーログ記録を確認（例外はthrowしない）", "確認項目": "- Azure Files削除失敗が正しくログに記録されること\r\n- 処理が継続されること（例外がthrowされない）"}]}}}}}}}