{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "", "FUNCTIONS_WORKER_RUNTIME": "node", "AzureWebJobsFeatureFlags": "EnableWorkerIndexing", "MSSQL_PRISMA_URL": "file:./test.db", "AZURE_SERVICEBUS_NAMESPACE_HOSTNAME": "", "SERVICE_BUS_TASK_INPUT_QUEUE_NAME": "task-input-queue-test", "SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME": "runbook-status-queue-test", "SERVICE_BUS_TASK_CONTROL_QUEUE_NAME": "task-control-queue-test", "AZURE_STORAGE_BLOB_CONNECTION_STRING": "", "AZURE_STORAGE_FILES_CONNECTION_STRING": "", "AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF": "assetsfield-def-test", "AZURE_STORAGE_CONTAINER_OPLOGS": "oplogs-test", "AZURE_AUTOMATION_ACCOUNT_NAME": "test-account", "SUBSCRIPTION_ID": "test-subscription", "RESOURCE_GROUP_NAME": "test-resource-group", "RUNBOOK_MGMT_ITEM_IMPORT": "Import-Management-Item-Test", "RUNBOOK_MGMT_ITEM_EXPORT": "Export-Management-Item-Test", "RUNBOOK_OPLOG_EXPORT": "Export-Operation-Log-Test", "RUNBOOK_MONITOR_INTERVAL_SECONDS": 60, "AZURE_MANAGEMENT_BASE_URL": "http://localhost:3001"}}