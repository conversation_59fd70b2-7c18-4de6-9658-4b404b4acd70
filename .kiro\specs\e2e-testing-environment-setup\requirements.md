# 需求文档

## 简介

本功能专注于为 jcs-endpoint-monorepo 项目建立完整、可靠的本地端到端（E2E）测试环境，使用 Playwright 进行测试。系统需要支持三个应用程序（Next.js 前端、标准 Azure Functions、长时运行 Azure Functions）的测试，并具备适当的环境隔离、数据管理和认证处理能力。

## 需求

### 需求 1

**用户故事：** 作为开发者，我希望有一个完整的 E2E 测试环境设置，以便我能够自信地在本地运行集成测试。

#### 验收标准

1. 当测试环境设置完成时，系统应该同时在不同端口上运行所有三个应用程序（Next.js、标准 Functions、长时运行 Functions）
2. 当执行测试时，系统应该使用与开发设置分离的独立测试环境配置
3. 当环境启动时，所有服务应该在其指定的 URL 上可访问（localhost:3000、localhost:7071、localhost:7072）
4. 当环境关闭时，所有进程应该优雅地终止，不留下孤立进程

### 需求 2

**用户故事：** 作为开发者，我希望有适当的环境配置管理，以便测试和开发环境不会相互干扰。

#### 验收标准

1. 当运行测试时，系统应该使用专用的测试环境变量，不与开发设置冲突
2. 当 Next.js 应用在测试模式下运行时，它应该使用后端服务的测试专用 API 端点
3. 当创建环境文件时，它们应该被适当地从版本控制中排除，以防止凭据泄露
4. 当服务启动时，它们应该使用正确的测试配置自动发现彼此

### 需求 3

**用户故事：** 作为开发者，我希望能够运行服务器列表页面测试，并具备适当的数据设置和清理，以便测试可靠且隔离。

#### 验收标准

1. 当测试开始时，系统应该清理任何现有的测试数据以确保干净状态
2. 当需要测试数据时，系统应该在数据库中创建特定的测试服务器记录
3. 当测试完成时，系统应该删除所有测试数据以防止与其他测试干扰
4. 当访问服务器列表页面时，系统应该正确显示测试服务器数据
5. 当验证服务器信息时，系统应该验证服务器名称、类型和管理 URL 是否正确显示

### 需求 4

**用户故事：** 作为开发者，我希望在测试中有适当的认证处理，以便我可以测试需要认证的功能而无需手动登录步骤。

#### 验收标准

1. 当测试需要认证时，系统应该提供辅助函数直接注入认证 Cookie
2. 当设置认证时，测试应该跳过基于 UI 的登录流程以加快执行速度
3. 当使用认证辅助函数时，它们应该支持不同的用户角色和权限
4. 当建立认证状态时，它应该在整个测试会话中持续存在

### 需求 5

**用户故事：** 作为开发者，我希望有一个简化的测试执行工作流程，以便运行测试简单高效。

#### 验收标准

1. 当启动测试环境时，单个命令应该启动所有必需的服务
2. 当运行测试时，它们应该在本地环境中执行而无需额外配置
3. 当测试完成时，应该自动生成详细的 HTML 报告
4. 当关闭时，单个操作应该干净地停止所有服务
5. 当测试失败时，应该捕获跟踪信息用于调试