# 数据模型: 服务器 (Server)

*   **表名 (逻辑名)**: `Server`
*   **物理表名 (Prisma Model)**: `Server` (参考项目根目录下 `prisma/schema.prisma` 文件获取权威定义)
*   **对应UI界面**: 服务器列表 (サーバ一覧)
*   **主要用途**: 存储用户可以访问和管理的“服务器”条目信息。每个条目代表一个具体的、可由本系统进行操作的IT服务实例（例如，运行在特定Azure虚拟机上的JP1/ITDM2组件的Docker容器，或秘文管理控制台的接入点等）。此表包含将后台任务精确路由到目标执行环境所需的关键信息。

## 1. 字段定义

| 字段名 (Prisma Model) | 数据类型 (SQL参考) | PK | FK | UQ | Nullable | Default | 中文描述 | 日文名称 (界面参考/逻辑参考) |
|--------------------|--------------|----|----|----|----------|---------|------|------------------|
| `id` | `String` | ● | - | - | - | `cuid()` | 主键。系统自动生成的唯一标识符 (CUID格式)。 | (内部ID) |
| `name` | `String` | - | - | - | - | - | **服务器/服务的显示名称**。用户在门户界面上看到的、用于识别此服务器条目的名称。 | サーバ名 |
| `type` | `String` | - | - | - | - | - | **服务器/服务的类型代码**。标识此服务器条目的业务类型。其具体值及对应显示文本通过值列表 (LOV 表中 `parentCode='SERVER_TYPE'` 的条目) 管理。 | 種別 |
| `url` | `String` | - | - | - | - | - | **管理界面URL**。如果此服务器条目有其自身的、独立的Web管理界面，此字段存储该界面的访问URL。 | 管理画面URL |
| `hrwGroupName` | `String?` | - | - | - | Yes | - | **Hybrid Runbook Worker组名 (可选)**。此服务器条目在Azure Automation中对应的Hybrid Runbook Worker Group的准确名称。对于需要执行后台自动化任务的服务器，此字段在任务发起前必须已被有效配置。 | (内部配置) HRWグループ名 |
| `licenseId` | `String` | - | ● | - | - | - | **关联的许可证业务ID**。此服务器条目所属的客户许可证/契约的业务ID (对应 `License.licenseId`)。 | (内部关联) 契約ID |
| `azureVmName` | `String?` | - | - | - | Yes | - | **目标Azure VM名称 (可选)**。此服务器实例实际运行所在的Azure虚拟机的准确名称。此信息对于并发控制 (`ContainerConcurrencyStatus`表) 和任务执行至关重要。对于需要执行后台自动化任务的服务器，此字段在任务发起前必须已被有效配置。 | (内部配置) Azure VM名 |
| `dockerContainerName` | `String?` | - | - | - | Yes | - | **目标Docker容器名称 (可选)**。如果此服务器实例是以Docker容器形式运行在目标Azure VM上，此字段存储该Docker容器的准确名称。对于需要与Docker容器交互的后台任务，此字段在任务发起前必须已被有效配置。 | (内部配置) Dockerコンテナ名 |

*PK: Primary Key, FK: Foreign Key, UQ: Unique Constraint*
*(数据类型参考项目代码库中 `prisma/schema.prisma` 文件的权威定义。SQL参考仅为示意。)*
*(Nullable列中的“Yes”基于Prisma Schema中字段类型后的 `?` 标记。这些字段虽然在数据库层面可为空（可能为了数据迁移或支持不执行自动化任务的服务器类型），但其业务逻辑上的必要性由应用层在任务创建和执行前进行校验。)*

## 2. 关系 (Relations)

本表与其他核心数据模型的关系，已在项目代码库的 `prisma/schema.prisma` 文件中通过关系字段明确定义。

*   **对 `License` (`license`)**: 多对一关系。一个服务器条目 (`Server`) 必须属于一个许可证 (`License`)。通过 `licenseId` 字段（外键，引用 `License.licenseId` 唯一键）与`License`表关联。Prisma Schema中定义为 `@relation(fields: [licenseId], references: [licenseId], onDelete: NoAction, onUpdate: NoAction)`。
*   **对 `Task` (`tasks`)**: 一对多关系。一个服务器条目 (`Server`) 可以是多个后台任务 (`Task`) 的执行目标。Prisma Schema中通过 `Task[]` 反向关系体现。

**关于外键约束策略的说明**: `onDelete: NoAction, onUpdate: NoAction` 表示如果关联的 `License` 记录被删除或其 `licenseId` 被更新，数据库层面不会自动对 `Server` 表做任何操作。数据完整性的维护由应用层面逻辑负责。

## 3. 任务执行上下文获取逻辑

当用户在门户界面选择一个服务器并发起后台任务时，系统从本`Server`表中获取执行该任务所需上下文信息的流程如下：

1.  门户后端根据传入的服务器ID (`Server.id`)，查询本表以获取对应的服务器记录。
2.  从查询到的`Server`记录中，提取 `hrwGroupName`, `azureVmName`, `dockerContainerName` 等关键信息。
3.  门户后端在创建任务（例如，在`Task`表中创建记录并发送消息到队列）之前，会根据任务类型校验这些关键字段是否已被有效配置。如果缺失必要信息，将阻止任务创建并向用户返回错误。
4.  这些已校验的上下文信息被包含在发送给后续后端处理组件（如`TaskExecuteFunc`）的任务消息中或在`Task`表中记录，供其使用。

## 4. 索引 (Indexes)

根据 `prisma/schema.prisma` 文件中的定义，本表当前主要的索引是：

*   **主键**: `id` (由 `@id` 隐式创建索引)。
*   **外键 `licenseId`**: Prisma 在为 `licenseId` 字段定义到 `License.licenseId` 的关系时，通常会在此字段上创建索引以优化关联查询的性能。

*(基于前端列表页采用全量数据缓存后进行过滤和排序的策略，如果后端没有其他基于非唯一键字段（如 `name` 或 `type`）的频繁、高性能查询需求，则目前定义的索引可能已足够。索引的最终配置应基于实际的后端查询模式和性能测试结果进行调优。)*

## 5. 备注 (Notes)

*   本表是门户“服务器列表 (サーバ一覧)”功能以及后台任务目标定位的核心数据源。
*   字段 `hrwGroupName`, `azureVmName`, 和 `dockerContainerName` 对于需要系统执行自动化后台任务的服务器条目是关键的配置信息。应用层逻辑会在任务创建和执行前校验这些字段的有效性。