import fixture from "../../fixtures/Notification.json";

describe("初期化表示のテスト", () => {
  describe("お知らせダイアログ", () => {
    const validCredentials = {
      userId: "hitachi.taro.aa",
      password: "changeit!@#",
    };

    it("利用者お知らせが正しく表示される", () => {
      cy.visit("/login");
      cy.get("#userId").type(validCredentials.userId);
      cy.get("#password").type(validCredentials.password);
      cy.get("button").contains("ログイン").click();
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("お知らせ").click();
      cy.get(".font-semibold").contains("お知らせ");
      cy.get(".whitespace-pre-line").then((elements) => {
        const dates = elements.map((index, element) => {
          // @ts-ignore
          const match = /(\d{4}\/\d{1,2}\/\d{1,2})\s/.exec(element.textContent);

          return match ? match[1] : null;
        });

        for (let i = 0; i < dates.length - 1; i++) {
          const current = new Date(dates[i]);
          const next = new Date(dates[i + 1]);
          expect(current).to.be.gte(next);
        }

        expect(dates.length).to.be.eq(
          fixture.Notification.filter(
            (n) =>
              n.type === "SYSTEM" ||
              (n.type === "LICENSE" && n.licenseId === "hisol") ||
              (n.type === "PLAN" &&
                n.planId &&
                ["standard", "lighta"].includes(n.planId)) ||
              (n.type === "USER" && n.userId === validCredentials.userId),
          ).length,
        );
      });
    });
  });
});
