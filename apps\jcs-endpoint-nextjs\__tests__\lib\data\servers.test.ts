// 模拟 portal-error
jest.mock("@/app/lib/portal-error", () => ({
  handleServerError: jest.fn((error) => {
    const { PORTAL_ERROR_MESSAGES } = require("@/app/lib/definitions");
    const { PrismaClientInitializationError } = require("@prisma/client/runtime/library");

    // 模拟 handleServerError 的实际行为
    if (error instanceof PrismaClientInitializationError) {
      throw new Error(PORTAL_ERROR_MESSAGES.EMEC0006);
    }
    throw new Error(PORTAL_ERROR_MESSAGES.EMEC0007);
  }),
}));

// 模拟 prisma
jest.mock("@/app/lib/prisma", () => ({
  __esModule: true,
  default: {
    server: {
      findUnique: jest.fn(),
    },
    license: {
      findUnique: jest.fn(),
    },
    lov: {
      findFirst: jest.fn(),
    },
  },
}));

import { ServerDataServers, fetchUserCanExportOplog } from "@/app/lib/data/servers";
import { PORTAL_ERROR_MESSAGES } from "@/app/lib/definitions";
import { PrismaClientInitializationError } from "@prisma/client/runtime/library";
import { expect } from "@jest/globals";
import prisma from "@/app/lib/prisma";

const prismaMock = prisma as jest.Mocked<typeof prisma>;

// 不需要模拟整个data模块，直接测试servers模块

/**
 * ServerDataServersクラスのユニットテスト
 *
 * @description
 * サーバー情報の取得、フィルタリング、ページネーション機能の正確性と堅牢性を検証する。
 * また、データベースエラーやサーバーエラー発生時の例外処理・エラーメッセージ返却も確認する。
 * 本テストは、サーバーリスト画面の安定動作を保証するための重要な品質担保である。
 */
describe("ServerDataServers", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  /**
   * fetchServersPagesメソッドのテスト
   *
   * @description
   * サーバーリストのページ数計算ロジックと、エラー発生時の例外処理を検証する。
   * ページネーションの正確性と、DB障害時のユーザー向けエラー返却の品質を担保する。
   */
  describe("fetchServersPages", () => {
    /**
     * 試験観点：ページ数計算機能の動作確認
     * 試験対象：fetchServersPages関数のページ数計算ロジック
     * 試験手順：
     * 1. 指定されたフィルタ条件とページサイズでfetchServersPagesを呼び出し
     * 確認項目：
     * - 正しいページ数が計算されること
     */
    it("指定されたフィルタ条件とページサイズに基づいてページ数を計算する", async () => {
      const filter = "Server";
      const size = 10;
      const count = 25;
      const refresh = false;
      ServerDataServers.fetchCachedServers = jest.fn().mockResolvedValue(Array(count).fill({
        id: "1",
        name: "Server1",
        type: "type",
        url: "url",
        licenseId: "license123",
        hrwGroupName: null,
        azureVmName: null,
        dockerContainerName: null,
        typeCode: "type"
      }));
      const result = await ServerDataServers.fetchServersPages(filter, size, refresh);
      expect(result).toBe(Math.ceil(count / size));
    });

    /**
     * 試験観点：データベース接続エラー時の例外処理確認
     * 試験対象：fetchServersPages関数のエラーハンドリング
     * 試験手順：
     * 1. PrismaClientInitializationErrorを発生させる
     * 確認項目：
     * - 適切なエラーメッセージ（EMEC0006）がthrowされること
     */
    it("データベース接続エラーが発生した場合、適切なエラーメッセージを返す", async () => {
      const filter = "someFilter";
      const size = 10;
      const refresh = false;
      ServerDataServers.fetchCachedServers = jest.fn().mockRejectedValue(new PrismaClientInitializationError("Prisma エラー", "5.6"));
      await expect(ServerDataServers.fetchServersPages(filter, size, refresh)).rejects.toThrowError(PORTAL_ERROR_MESSAGES.EMEC0006);
    });

    /**
     * 試験観点：予期せぬエラー時の例外処理確認
     * 試験対象：fetchServersPages関数の一般的なエラーハンドリング
     * 試験手順：
     * 1. 一般的なErrorを発生させる
     * 確認項目：
     * - 一般的なエラーメッセージ（EMEC0001）がthrowされること
     */
    it("予期せぬサーバーエラーが発生した場合、一般的なエラーメッセージを返す", async () => {
      const filter = "someFilter";
      const size = 10;
      const refresh = false;
      ServerDataServers.fetchCachedServers = jest.fn().mockRejectedValue(new Error("原因不明のエラー"));
      await expect(ServerDataServers.fetchServersPages(filter, size, refresh)).rejects.toThrowError(PORTAL_ERROR_MESSAGES.EMEC0007);
    });
  });

  /**
   * fetchFilteredServersメソッドのテスト
   *
   * @description
   * サーバーリストのフィルタリング・ソート機能の正確性と、異常系の例外処理を検証する。
   * ユーザーが指定した条件で正しくデータが抽出・整列されるか、また障害時に適切なエラーが返るかを確認する。
   */
  describe("fetchFilteredServers", () => {
    /**
     * 試験観点：フィルタ条件不一致時の動作確認
     * 試験対象：fetchFilteredServers関数のフィルタリング機能
     * 試験手順：
     * 1. サーバータイプと一致しないフィルタ条件を指定
     * 確認項目：
     * - 条件に一致しないデータが除外されること
     */
    it("フィルタ条件がサーバータイプと一致しない場合の結果を検証する", async () => {
      const filter = "someFilter";
      const size = 10;
      const page = 1;
      const sort = "name";
      const order = "asc";
      // mock データ type フィールドにfilterが含まれない場合の動作を検証
      const mockData = [
        {
          id: "1",
          name: "Server1",
          type: "SERVER_TYPE.GENERAL_MANAGER",
          url: "http://example.com",
          licenseId: "wsst",
          hrwGroupName: null,
          azureVmName: null,
          dockerContainerName: null,
          typeCode: "SERVER_TYPE.GENERAL_MANAGER"
        },
      ];
      ServerDataServers.fetchCachedServers = jest.fn().mockResolvedValue(mockData);
      const result = await ServerDataServers.fetchFilteredServers(filter, size, page, sort, order);
      expect(result).toEqual([]); // 結果は空配列となることを期待
    });

    /**
     * 試験観点：フィルタ条件一致時の動作確認
     * 試験対象：fetchFilteredServers関数のフィルタリング機能
     * 試験手順：
     * 1. サーバータイプと一致するフィルタ条件を指定
     * 確認項目：
     * - 条件に一致するデータが正しく抽出されること
     */
    it("フィルタ条件がサーバータイプと一致する場合の結果を検証する", async () => {
      const filter = "MANAGER";
      const size = 10;
      const page = 1;
      const sort = "name";
      const order = "asc";
      const expectedResult = [
        {
          id: "1",
          name: "Server1",
          type: "SERVER_TYPE.GENERAL_MANAGER",
          url: "http://example.com",
          licenseId: "wsst",
          hrwGroupName: null,
          azureVmName: null,
          dockerContainerName: null,
          typeCode: "SERVER_TYPE.GENERAL_MANAGER"
        },
      ];
      ServerDataServers.fetchCachedServers = jest.fn().mockResolvedValue(expectedResult);
      const result = await ServerDataServers.fetchFilteredServers(filter, size, page, sort, order);
      expect(result).toEqual(expectedResult);
    });

    /**
     * 試験観点：データベースエラー時の例外処理確認
     * 試験対象：fetchFilteredServers関数のエラーハンドリング
     * 試験手順：
     * 1. PrismaClientInitializationErrorを発生させる
     * 確認項目：
     * - 適切なエラーメッセージ（EMEC0006）がthrowされること
     */
    it("データベースエラーが発生した場合の処理を検証する", async () => {
      const filter = "someFilter";
      const size = 10;
      const page = 1;
      const sort = "name";
      const order = "asc";
      ServerDataServers.fetchCachedServers = jest.fn().mockRejectedValue(new PrismaClientInitializationError("Prisma エラー", "5.6"));
      await expect(ServerDataServers.fetchFilteredServers(filter, size, page, sort, order)).rejects.toThrowError(PORTAL_ERROR_MESSAGES.EMEC0006);
    });

    /**
     * 試験観点：予期せぬエラー時の例外処理確認
     * 試験対象：fetchFilteredServers関数の一般的なエラーハンドリング
     * 試験手順：
     * 1. 一般的なErrorを発生させる
     * 確認項目：
     * - 一般的なエラーメッセージ（EMEC0007）がthrowされること
     */
    it("予期せぬエラーが発生した場合の処理を検証する", async () => {
      const filter = "someFilter";
      const size = 10;
      const page = 1;
      const sort = "name";
      const order = "asc";
      ServerDataServers.fetchCachedServers = jest.fn().mockRejectedValue(new Error("原因不明のエラー"));
      await expect(ServerDataServers.fetchFilteredServers(filter, size, page, sort, order)).rejects.toThrowError(PORTAL_ERROR_MESSAGES.EMEC0007);
    });
  });

  /**
   * getServerDetailsForTaskメソッドのテスト
   *
   * @description
   * タスク実行に必要なサーバー詳細情報の取得機能を検証する。
   * サーバーID指定時に正しい情報が返るか、DB障害時に例外が返るかを確認する。
   */
  describe("getServerDetailsForTask", () => {
    /**
     * 試験観点：サーバー詳細情報取得機能の動作確認
     * 試験対象：getServerDetailsForTask関数の正常系処理
     * 試験手順：
     * 1. 有効なサーバーIDを指定してgetServerDetailsForTaskを呼び出し
     * 確認項目：
     * - 指定されたサーバーの詳細情報が正しく取得されること
     */
    it("指定されたサーバーIDに基づいて詳細情報を取得する", async () => {
      const serverId = "server123";
      const expectedDetails = {
        name: "Server1",
        licenseId: "license123",
        azureVmName: "vm1",
        dockerContainerName: "container1",
        hrwGroupName: "group1",
      };
      (prismaMock.server.findUnique as jest.Mock).mockResolvedValue(expectedDetails);
      const result = await ServerDataServers.getServerDetailsForTask(serverId);
      expect(result).toEqual(expectedDetails);
      expect(prismaMock.server.findUnique).toHaveBeenCalledWith({
        where: { id: serverId },
        select: {
          name: true,
          licenseId: true,
          azureVmName: true,
          dockerContainerName: true,
          hrwGroupName: true,
        },
      });
    });

    /**
     * 試験観点：データベースエラー時の例外処理確認
     * 試験対象：getServerDetailsForTask関数のエラーハンドリング
     * 試験手順：
     * 1. データベースエラーを発生させる
     * 確認項目：
     * - 適切なエラーメッセージ（EMEC0007）がthrowされること
     */
    it("データベースエラーが発生した場合、適切なエラーメッセージを返す", async () => {
      const serverId = "server123";
      (prismaMock.server.findUnique as jest.Mock).mockRejectedValue(new Error("DB error"));
      await expect(ServerDataServers.getServerDetailsForTask(serverId)).rejects.toThrowError(PORTAL_ERROR_MESSAGES.EMEC0007);
    });
  });
});

/**
 * fetchUserCanExportOplog関数のユニットテスト
 *
 * @description
 * ユーザーの操作ログエクスポート権限判定ロジックを検証する。
 * ライセンスプランとLOV設定に基づき、権限有無が正しく判定されるかを確認する。
 * 本テストは、操作ログエクスポート機能のセキュリティ・権限制御の品質担保を目的とする。
 */
describe("fetchUserCanExportOplog", () => {
  // 兜底mock cookiesでiron-sessionが常にダミーcookieを取得できるよう保証
  beforeAll(() => {
    const { cookies } = require("next/headers");
    (cookies as jest.Mock).mockReturnValue({
      get: jest.fn(() => ({ value: "dummy-session" })),
      set: jest.fn(),
      delete: jest.fn(),
    });
  });

  /**
   * 試験観点：有効プラン保持ユーザーの権限確認
   * 試験対象：fetchUserCanExportOplog関数の権限判定ロジック
   * 試験手順：
   * 1. 有効なプランを持つユーザーでfetchUserCanExportOplogを呼び出し
   * 確認項目：
   * - エクスポート権限がtrueとなること
   */
  it("有効なプランを持つユーザーの場合、エクスポート権限がtrueとなる", async () => {
    (prismaMock.license.findUnique as jest.Mock).mockResolvedValue({
      basicPlan: "PLAN_A",
    });
    (prismaMock.lov.findFirst as jest.Mock).mockResolvedValue({
      id: "id2",
      code: "SOME_CODE",
      name: "SOME_NAME",
      parentCode: "OPERATION_LOG_CONFIG.ALLOWED_PLANS",
      value: "PLAN_A",
      isEnabled: true,
    });
    const result = await fetchUserCanExportOplog();
    expect(result).toBe(true);
  });

  /**
   * 試験観点：無効プラン保持ユーザーの権限確認
   * 試験対象：fetchUserCanExportOplog関数の権限判定ロジック
   * 試験手順：
   * 1. 無効なプランを持つユーザーでfetchUserCanExportOplogを呼び出し
   * 確認項目：
   * - エクスポート権限がfalseとなること
   */
  it("無効なプランを持つユーザーの場合、エクスポート権限がfalseとなる", async () => {
    (prismaMock.license.findUnique as jest.Mock).mockResolvedValue({
      basicPlan: "PLAN_B",
    });
    (prismaMock.lov.findFirst as jest.Mock).mockResolvedValue(null);
    const result = await fetchUserCanExportOplog();
    expect(result).toBe(false);
  });

  /**
   * 試験観点：プラン未設定ユーザーの権限確認
   * 試験対象：fetchUserCanExportOplog関数の権限判定ロジック
   * 試験手順：
   * 1. プランが未設定のユーザーでfetchUserCanExportOplogを呼び出し
   * 確認項目：
   * - エクスポート権限がfalseとなること
   */
  it("プランが未設定の場合、エクスポート権限がfalseとなる", async () => {
    (prismaMock.license.findUnique as jest.Mock).mockResolvedValue({
      basicPlan: "",
    });
    const result = await fetchUserCanExportOplog();
    expect(result).toBe(false);
  });

  /**
   * 試験観点：ライセンス情報が見つからない場合の権限判定
   * 試験対象：fetchUserCanExportOplog のライセンス未存在時の処理
   * 試験手順：
   * 1. prismaMock.license.findUniqueがnullを返すようにモック設定
   * 2. fetchUserCanExportOplogを実行
   * 3. falseが返されることを確認
   * 確認項目：
   * - ライセンス情報が見つからない場合にfalseが返されること
   */
  it("異常系: ライセンス情報が見つからない場合、エクスポート権限がfalseとなる", async () => {
    (prismaMock.license.findUnique as jest.Mock).mockResolvedValue(null);
    const result = await fetchUserCanExportOplog();
    expect(result).toBe(false);
  });

  /**
   * 試験観点：LOV設定が無効化されている場合の権限判定
   * 試験対象：fetchUserCanExportOplog のLOV無効化時の処理
   * 試験手順：
   * 1. LOVエントリのisEnabledがfalseの場合をモック設定
   * 2. fetchUserCanExportOplogを実行
   * 3. falseが返されることを確認
   * 確認項目：
   * - LOV設定が無効化されている場合にfalseが返されること
   */
  it("境界条件: LOV設定が無効化されている場合、エクスポート権限がfalseとなる", async () => {
    (prismaMock.license.findUnique as jest.Mock).mockResolvedValue({
      basicPlan: "PLAN_A",
    });
    // LOV設定が無効化されている場合、findFirstはnullを返す
    // （実際のクエリではisEnabled: trueの条件があるため）
    (prismaMock.lov.findFirst as jest.Mock).mockResolvedValue(null);
    const result = await fetchUserCanExportOplog();
    expect(result).toBe(false);
  });

  /**
   * 試験観点：データベースエラー時の権限判定処理
   * 試験対象：fetchUserCanExportOplog のDB例外処理
   * 試験手順：
   * 1. prismaMock.license.findUniqueがエラーを投げるようにモック設定
   * 2. fetchUserCanExportOplogを実行
   * 3. 例外が適切に処理されることを確認
   * 確認項目：
   * - データベースエラーが発生した場合に適切に例外が投げられること
   */
  it("異常系: データベースエラー時は例外が投げられる", async () => {
    (prismaMock.license.findUnique as jest.Mock).mockRejectedValue(new Error("Database connection failed"));
    await expect(fetchUserCanExportOplog()).rejects.toThrow("Database connection failed");
  });
});

/**
 * ServerDataServersクラスの追加テスト - 境界条件とエラーハンドリング
 *
 * @description
 * 既存テストでカバーされていない境界条件、エラーハンドリング、
 * パフォーマンス関連のテストケースを追加する。
 */
describe("ServerDataServers - 境界条件とエラーハンドリング", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  /**
   * 試験観点：特殊文字を含むフィルター条件での検索
   * 試験対象：ServerDataServers.fetchFilteredServers のフィルタリング処理
   * 試験手順：
   * 1. 特殊文字を含むサーバー名のデータを設定
   * 2. 特殊文字を含むフィルター条件で検索を実行
   * 3. 正しくマッチングされることを確認
   * 確認項目：
   * - 特殊文字を含むフィルター条件が正しく動作すること
   * - 大文字小文字を区別しない検索が動作すること
   */
  it("境界条件: 特殊文字を含むフィルター条件での検索", async () => {
    const mockData = [
      {
        id: "1",
        name: "サーバー-テスト_01",
        type: "一般管理サーバー",
        url: "http://test-server_01.example.com",
        licenseId: "license123",
        hrwGroupName: null,
        azureVmName: null,
        dockerContainerName: null,
        typeCode: "GENERAL_MANAGER"
      },
    ];
    ServerDataServers.fetchCachedServers = jest.fn().mockResolvedValue(mockData);

    const result = await ServerDataServers.fetchFilteredServers("テスト_01", 10, 1, "name", "asc");
    expect(result).toEqual(mockData);
  });

  /**
   * 試験観点：極端に大きなページサイズでの処理
   * 試験対象：ServerDataServers.fetchFilteredServers のページング処理
   * 試験手順：
   * 1. 大量のサーバーデータを設定
   * 2. 極端に大きなページサイズで検索を実行
   * 3. 全データが正しく返されることを確認
   * 確認項目：
   * - 極端に大きなページサイズでも正しく動作すること
   * - メモリ使用量が適切であること
   */
  it("境界条件: 極端に大きなページサイズでの処理", async () => {
    const largeDataSet = Array.from({ length: 100 }, (_, index) => ({
      id: `server-${index}`,
      name: `サーバー${index}`,
      type: "一般管理サーバー",
      url: `http://server${index}.example.com`,
      licenseId: "license123",
      hrwGroupName: null,
      azureVmName: null,
      dockerContainerName: null,
      typeCode: "GENERAL_MANAGER"
    }));
    ServerDataServers.fetchCachedServers = jest.fn().mockResolvedValue(largeDataSet);

    const result = await ServerDataServers.fetchFilteredServers("", 1000, 1, "name", "asc");
    expect(result).toHaveLength(100);
    expect(result).toEqual(largeDataSet);
  });

  /**
   * 試験観点：ページ番号が総ページ数を超える場合の処理
   * 試験対象：ServerDataServers.fetchFilteredServers のページング境界処理
   * 試験手順：
   * 1. 少量のサーバーデータを設定
   * 2. 存在しないページ番号で検索を実行
   * 3. 空配列が返されることを確認
   * 確認項目：
   * - 存在しないページ番号の場合に空配列が返されること
   * - エラーが発生しないこと
   */
  it("境界条件: ページ番号が総ページ数を超える場合は空配列を返す", async () => {
    const mockData = [
      {
        id: "1",
        name: "サーバー1",
        type: "一般管理サーバー",
        url: "http://server1.example.com",
        licenseId: "license123",
        hrwGroupName: null,
        azureVmName: null,
        dockerContainerName: null,
        typeCode: "GENERAL_MANAGER"
      },
    ];
    ServerDataServers.fetchCachedServers = jest.fn().mockResolvedValue(mockData);

    const result = await ServerDataServers.fetchFilteredServers("", 10, 5, "name", "asc");
    expect(result).toEqual([]);
  });
});