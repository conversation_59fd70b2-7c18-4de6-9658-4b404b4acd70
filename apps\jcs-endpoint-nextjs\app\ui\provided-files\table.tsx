/**
 * @file table.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

import { ServerDataProvidedFiles } from "@/app/lib/data/provided-files";
import Thead from "../thead";

// 提供ファイルテーブルコンポーネント
export default async function ProvidedFilesTable({
  filter,
  page,
  size,
  sort,
  order,
  preferSort,
}: {
  filter: string;
  page: number;
  size: number;
  sort: "name" | "description" | "updatedAt" | "size";
  order: "asc" | "desc";
  preferSort: "name" | "description" | "updatedAt" | "size";
}) {
  const files = await ServerDataProvidedFiles.fetchFilteredProvidedFiles(
    filter,
    size,
    page,
    sort,
    order,
    preferSort,
  );

  return (
    <table className="whitespace-nowrap w-full text-left text-sm text-gray-500">
      <Thead
        headers={[
          { key: "name", label: "ファイル名" },
          { key: "description", label: "説明" },
          { key: "updatedAt", label: "最終更新日時" },
          { key: "size", label: "サイズ" },
        ]}
        defaultOrder="updatedAt"
        defaultSort="desc"
      />
      <tbody>
        {files?.length !== 0 ? (
          files!.map((file) => (
            <tr key={file.id} className="border-b odd:bg-white even:bg-gray-50">
              <th
                scope="row"
                className="border-r text-gray-900 whitespace-nowrap px-6 py-4 font-medium"
              >
                <a
                  target="_blank"
                  href={`provided-files/${file.fileName}`}
                  className="font-medium text-blue-600 hover:underline"
                >
                  {file.name}
                </a>
              </th>
              <td className="border-r px-6 py-4">{file.description}</td>
              <td className="border-r px-6 py-4">{file.updatedAt}</td>
              <td className="px-6 py-4 text-right">{file.formattedSize}</td>
            </tr>
          ))
        ) : (
          <tr>
            <td>
              <div className="p-4"></div>
            </td>
          </tr>
        )}
      </tbody>
    </table>
  );
}
