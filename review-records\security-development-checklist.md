# JCS Endpoint Project - 適用セキュリティ開発チェックリスト

**(全79項目)**

本文書は、「JCS エンドポイント資産とタスク管理システム」プロジェクトに適用されるセキュリティ開発のチェックリストである。これは、汎用的な開発原則リストから、本プロジェクトの技術スタック（Next.js、Azure Functions (Node.js/TypeScript)、Prisma ORM、Azure Blob Storage）及びアーキテクチャに合わせて抽出・編集されたものである。不適用な技術（SSI、LDAP、XML DB、メール送信等）に関する項目は除外されている。

---

## 1. データチェック機能 (24項目)

- [ ] **1-1**: 入力値の妥当性をチェックする機能は、サーバ側アプリケーションには必ず組み込む。クライアント側はどちらでも構わない。なお、入力値のチェック機能は、一元化する（チェックする項目が同じ場合は、１つのモジュールで処理する）。
- [ ] **1-2**: ユーザ入力フォームでユーザID・パスワードなど個人情報・機密情報を送信する場合は、GETメソッドではなくPOSTメソッドで送信する。URLパラメータを使用する場合は、情報漏えい防止のため、URLパラメータには秘密にすべき情報は格納しないようにすること（例: a) ユーザIDとパスワード, b) クレジットカード番号, c) 個人情報, d) プライベートデータ, e)セッションID）。
- [ ] **1-3**: 送られてきたデータは、全て妥当性をチェックする（データのフォーマットだけではなく、受け付けるべき値として妥当かもチェックする）。例えば、入力ボックス内の値、ラジオボタン項目の値、チェックボックス項目の値、hiddenフィールドの値、Cookieの値などをチェックする。
- [ ] **1-4**: ユーザに送ったデータはすべて改ざんされて送り返される可能性がある。このため、ユーザのステータスなどセッション管理上重要な情報はユーザ側に送らず、サーバ側でセッション変数に格納して管理する。特に、hiddenフィールドやCookieの情報に注意する。
- [ ] **1-5**: モジュール毎にデコード処理を行うと、不要なデコード処理が入り込みやすいため、デコード処理は一元化する。
- [ ] **1-6**: 入力文字内のNULL文字（\0, %00）は排除する。URLのリクエストパラメタに「%00」が含まれていたら排除する。（「%00」がデコードされると「0x00」に変換され、OSやPerl、C/C++で作成したモジュールでは、「0x00」をNULL文字と解釈するため、予想外のデータが読み出される可能性がある。）
- [ ] **1-7**: HTTPレスポンスヘッダのContent-Type フィールドに文字コード(charset)を明示的に指定する。例：「Content-Type:text/html; charset=UTF-8」
- [ ] **1-8**: 入力された文字に対するエスケープ処理（サニタイジング）は、リクエストを受け取った時ではなく、HTML生成時に行う。エスケープ処理（サニタイジング）は、一元化する（エスケープ（サニタイジング）する項目が同じ場合は、１つのモジュールで処理する）。
- [ ] **1-9**: 入力チェック機能をWebアプリケーションに実装し、条件に合わない値を入力された場合は、処理を先に進めず、再入力を求めるようにする。入力文字のチェックは、入力を許可する文字のパターン（ホワイトリスト）を作成し、それ以外は拒否して処理を中止する。
- [ ] **1-10**: タグの属性値は「""（ダブルクォート）」でくくる。
- [ ] **1-11**: 次の部分にはユーザが入力した文字を埋め込まない: URL属性、イベントハンドラ属性（onで始まるタグ属性）、`<SCRIPT>`タグ、コメントタグ、スタイル属性、外部スタイルシートへのリンク。
- [ ] **1-12**: URL を出力するときは、「http://」や「https://」で始まるURLのみを許可するようにする。許可するURLは、ホワイトリストを作成して管理する。
- [ ] **1-13**: DOM操作の際、意図しないDOMツリーの変更を避けるため、innerHTML等での操作ではなく、createElement()、setAttribute()、createTextNode()などのDOM操作用のメソッドやプロパティを使用する。
- [ ] **1-14**: 出力内容に応じたエスケープ処理を行う。例えばsrc属性やhref属性の値としてURLを出力する際はencodeURIComponent()を使用してURLエンコードする。

- [ ] **1-15**: 入力データをHTTPレスポンスヘッダに書き出す際、そこに含まれる改行コード（Cr および Lf）がそのまま出力されないようにする。具体的にはHTTPヘッダ内に出力する値にURLエンコーディングを施す。
- [ ] **1-16**: プログラムから別プログラムの起動を避ける。やむを得ない場合は、内部でシェルを呼び出さないAPIを使用する。
- [ ] **1-17**: 外部から取り込んだデータを元に内部コマンドや別のプログラムを起動する場合、入力文字のチェックは、入力を許可する文字のパターン（ホワイトリスト）を作成し、それ以外は拒否して処理を中止する。
- [ ] **1-18**: アップロード等で取り込んだ画像データが想定した形式であることをチェックする。
- [ ] **1-19**: ファイルを開く際、固定のディレクトリを指定し、かつファイル名にディレクトリ名が含まれないようにする。`basename()`などを利用してディレクトリ名を取り除く。
- [ ] **1-20**: ファイル名を指定した入力パラメタの値から、OSのパス名解釈でディレクトリを指定できる文字列（`/`, `../`, `..\` 等）を検出した場合は、処理を中止する。
- [ ] **1-21**: SQL文はユーザが入力したデータを定数としてSQL文中に埋め込むのではなく、バインド機構を使用する。(注: Prisma ORMはデフォルトでこれに準拠)
- [ ] **1-22**: (文字列連結でSQLを構成する場合) 特殊文字（`'`, `;` 等）を適切に処理（入力禁止またはエスケープ）する。
- [ ] **1-23**: 監査ログ出力時に、ファイル名などに含まれる改行やタブなどの特殊文字をチェックして排除する。
- [ ] **1-24**: プログラムの動作に大きな影響を与える環境変数の値がすべて所定の仕様を満たしていることを厳密に検査する。

## 2. エラー処理 (4項目)

- [ ] **2-1**: エラー発生時の理由を説明するメッセージは最小限にする。内部エラー情報をそのまま表示しない。
- [ ] **2-2**: エラーの詳細情報は、（セキュリティで保護された）監査ログにのみ記録する。ユーザには知らせない。
- [ ] **2-3**: エラーメッセージには、ユーザの存在、関数名などシステムの内部構造を示す情報は表示しない。
- [ ] **2-4**: サーバのエラー情報やスタックトレースはクライアントに送信しない。

## 3. 暗号化機能 (2項目)

- [ ] **3-1**: 暗号処理等に使用する乱数（暗号鍵や初期ベクトル等）には予測不可能な乱数を使う。
- [ ] **3-2**: 該当する送信データだけをTLSにより暗号化処理を行うのではなく、入力操作を行うログイン画面ページや問合せページからTLSによる暗号化処理を行う。

## 4. アクセス制御 (2項目)

- [ ] **4-1**: ファイルを作成する際は、そのファイルに対するアクセス権も同時に設定し、必要なユーザにのみアクセス権限を与える。
- [ ] **4-2**: Webアプリケーションにおけるアクセス認可は、1.ログイン有無、2.ページ単位の許可、3.パラメタ単位の許可の3段階の構成で行う。

## 5. セッション管理機能 (8項目)

- [ ] **5-1**: セッションIDは、同一ユーザであってもアクセスごとに異なるランダムな値（十分な長さで類推が困難）にする。
- [ ] **5-2**: 一定時間、クライアントからの通信（操作）がない場合、セッションを強制的に切断する。セッションの有効期限は有限にする。
- [ ] **5-3**: セッションIDは、Cookieに格納するか、または、POSTメソッドのhiddenフィールドに格納して受け渡す。URLパラメタに格納しない。
- [ ] **5-4**: ログインの前に発行したセッションIDは、ログイン後には使用しない。ログイン成功後に、新しくセッションを開始するようにする。
- [ ] **5-5**: セッションとユーザとの対応付けはサーバで行う。ユーザの状態・情報はセッションIDには持たせない。
- [ ] **5-6**: 通信のセッション終了時にセッションのデータは完全に削除する。処理開始時にはデータ（変数）に初期値を設定する。
- [ ] **5-7**: Cookieは必ずサーバサイドで生成する。
- [ ] **5-8**: Cookieの有効ドメインを適切に設定する。TLSの場合、セッションIDを格納するCookieにはsecureフラグを立てる。

## 6. パスワード管理機能 (1項目)

- [ ] **6-1**: パスワードは平文ではなく、ソルト付きのハッシュ値を管理する。ハッシュ値とソルトは分けて管理する。

## 7. メモリ管理 (3項目)

- [ ] **7-2**: （Node.jsではGCに依存するが原則として）確保したリソースは、使用後に必ず解放する。メモリリークに注意する。
- [ ] **7-3**: 個人情報はユーザの入力後、アプリケーションが取得し、使用後はすぐに破棄してメモリをクリアする。
- [ ] **7-4**: 大量データのある表に対し、パフォーマンスの低い検索（非インデックス列、全件検索など）を行わない。

## 8. ファイル送受信機能 (2項目)

- [ ] **8-1**: ファイルをダウンロードさせる際、一時ファイルをサーバに作成・保存せず、極力メモリ中で処理を行う。
- [ ] **8-2**: クライアントからアクセスされない拡張子は無効にする。

## 9. 情報漏洩防止対策 (5項目)

- [ ] **9-1**: 入力後の応答メッセージが認証情報の推測のヒントとならないようにする（例：「ユーザIDまたはパスワードが間違っています」）。
- [ ] **9-2**: 個人情報や機密情報はメモリ中に保持したりキャッシュせず、必要な時にデータベースなどから取り出す。
- [ ] **9-3**: ログインページ以降のページはキャッシュできないようにサーバ側でWebブラウザのキャッシュ機能を禁止する。
- [ ] **9-5**: URLのパラメタ部分に個人情報・機密情報を入れ、外部サイトへのリンクが存在する場合、リダイレクタを経由してリンク先に飛ぶようにする。
- [ ] **9-6**: ソースコードにデータベースなどへアクセスするためのユーザID、パスワード、暗号鍵などの秘密情報を埋め込まず、非公開ディレクトリ上に置いた設定ファイルから読み込む。

## 10. マルチタスク構造のプログラム (5項目)

- [ ] **10-1**: 複数プロセスが並列に動作した場合にタイミングによって処理や結果が異ならないように設計する（競合状態の防止）。
- [ ] **10-2**: (Node.jsの非同期モデルでは) 不要なグローバル変数/共有変数は使用せず、リクエストスコープのローカル変数を使用する。
- [ ] **10-3**: 共有変数を使用する必要がある場合は、情報が他の処理によって勝手に書き換えられてしまわないよう排他制御を行う。
- [ ] **10-4**: 機密情報や個人情報を格納するオブジェクト、変数は、使用前に初期化する。
- [ ] **10-5**: オブジェクト、変数では、機密情報や個人情報を格納する場合、使用前に初期化する。

## 12. UNIX/Linux全般 (1項目) (※Azure Functions/PaaS環境適用)

- [ ] **12-15**: OSコマンドインジェクション防止のため、エスケープ処理に使用する記号は、 `""`, `'`, `\` 以外の記号にする。

## 19. コンパイル (2項目)

- [ ] **19-1**: アプリケーションを本番運用用にビルドする際は、デバッグモードをオフにする。
- [ ] **19-2**: アプリケーションを本番運用用にビルドする際は、デバッグ情報(デバッグシンボル/ソースマップ)は含まない、または分離する。

## 20. Webページの設計 (4項目)

- [ ] **20-1**: ユーザに対してアドレスバー・ステータスバーを隠したり、右クリックを禁止したりせず、URL情報を表示する。
- [ ] **20-4**: Webブラウザ上の｢戻る｣機能を使用した際、個人情報等が残らないようにし、ログアウト後はログイン時を想定した機能が操作できないようにする。
- [ ] **20-5**: URLリダイレクトは原則使用しない。使用する場合はリダイレクト先URLの作成時に外部パラメタを利用しない。
- [ ] **20-6**: ヘッダの出力を直接行わず、Webアプリケーションの実行環境や言語に用意されているヘッダ出力用API を使用する。

## 21. 設計一般 (6項目)

- [ ] **21-1**: 一般ユーザの処理と特権処理を明確に分けて設計を行う。
- [ ] **21-2**: セキュリティを考慮しなければならないインタフェースと通常のインタフェースを明確に分けて設計する。
- [ ] **21-3**: 重要なデータをモジュール間で受け渡しをしない等、セキュリティを考慮したモジュール分割設計をする。
- [ ] **21-4**: 可能なら部品やモジュールは、セキュリティ設計されたもの（安全性が確認されているもの）を使用する。
- [ ] **21-5**: 処理（入力、DB出力、表示等）をモジュールや関数に分けた設計をする。
- [ ] **21-6**: プロセスがリソースを利用する場合はロック時間を出来る限り短くし、ロック順序を考慮してデッドロックを防止する。

## 23. Nodejs/React (10項目)

- [ ] **23-1**: クライアントからの入力に対して適切な検証とサニタイズが行われているか確認し、悪意のある入力と攻撃を防ぐ。
- [ ] **23-2**: 動的なコンテンツをレンダリングする際に適切なエスケープやサニタイズを行っているか確認し、クロスサイトスクリプティング（XSS）攻撃を防ぐ。
- [ ] **23-3**: データベースとのやり取りにおいて、パラメータ化されたクエリやバインド機構を使用しているか確認し、SQL インジェクション攻撃を防ぐ。(注: Prisma ORMが対応)
- [ ] **23-4**: アップロードされたファイルの種類を検証し、ファイルを安全な場所に保存することで、任意のコード実行やファイル上書き攻撃を防ぐ。
- [ ] **23-5**: ユーザー状態の変更に関与する操作に適切な CSRF 対策措置が行われているか確認する。
- [ ] **23-6**: セッション ID の生成と保存方法、およびセッションの有効性検証を確認する。
- [ ] **23-7**: データの転送と保存プロセスで適切な暗号化と保護措置が行われているか確認する。
- [ ] **23-8**: 適切なエラーハンドリングメカニズムとログ記録が実装されているか確認し、機密情報の漏洩を防ぐ。
- [ ] **23-9**: ユーザーが許可されていないリソースにアクセスできないよう、適切な権限とアクセス制御が行われているか確認する。
- [ ] **23-10**: 使用しているサードパーティライブラリのセキュリティと更新頻度を確認し、既知の脆弱性のあるライブラリを避ける。