/**
 * @fileoverview サーバ一覧ページのテーブルコンポーネント
 * @description
 * データ取得と編集処理：
 * - 契約IDが現在のログインユーザーの契約IDと一致するサーバレコードの全件をキャッシュから取得
 * - サーバ種別の日本語名称への変換（LOVテーブルのラベル値を使用）
 * - メモリ内での絞り込み、並び替え、ページ分割処理を実行
 * - 絞り込み：サーバ名、サーバ種別、管理画面URLのいずれかに対してフィルター文字列が含まれるかチェック（大文字小文字区別なし）
 *
 * ハイパーリンククリック：
 * - 管理画面URLを別タブで開く（target="_blank"）
 *
 * 列ヘッダークリック：
 * - ソート可能な列（サーバ名、種別、管理画面URL）のヘッダークリックでソート処理
 * - 同一列の場合はソート順を反転、異なる列の場合は昇順でソート開始
 *
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { ServerDataServers } from "@/app/lib/data/servers";
import Thead from "../thead";
import ServerActionsDropdownWrapper from "@/app/ui/servers/actions-dropdown-wrapper";

/**
 * サーバテーブルコンポーネント
 * @param {object} props - コンポーネントのプロパティ
 * @param {string} props.filter - サーバ名をフィルタリングするためのキーワードです。
 * @param {number} props.page - 現在表示するページ番号です。
 * @param {number} props.size - 1ページあたりの表示件数です。
 * @param {"name" | "type" | "url"} props.sort - データをソートする基準となるフィールド名です。
 * @param {"asc" | "desc"} props.order - ソートの順序（昇順または降順）です。
 * @param {boolean} props.canExportOplog - 現在ユーザーが操作ログエクスポート可能かどうか。
 * @param {number} props.maxExportDaysSpan - 最大エクスポート可能な日数。

 * @returns {Promise<JSX.Element>} サーバデータを表示するHTMLテーブル要素
 */
export default async function ServersTable({
  filter,
  page,
  size,
  sort,
  order,
  canExportOplog,
  maxExportDaysSpan,
}: {
  filter: string;
  page: number;
  size: number;
  sort: "name" | "type" | "url";
  order: "asc" | "desc";
  canExportOplog: boolean;
  maxExportDaysSpan: number;
}) {
  // キャッシュされたサーバデータに対してメモリ内での絞り込み、並び替え、ページ分割処理を実行
  // server.typeはLOVラベル値、server.typeCodeは元のtypeコード値
  const servers = await ServerDataServers.fetchFilteredServers(
    filter,
    size,
    page,
    sort,
    order,
  );

  return (
    <table className="whitespace-nowrap w-full h-full text-left text-sm text-gray-500">
      <Thead
        headers={[
          { key: "name", label: "サーバ名" },
          { key: "type", label: "種別" },
          { key: "url", label: "管理画面" },
          { key: "actions", label: "タスク", sortable: false },
        ]}
        defaultOrder="name"
        defaultSort="asc"
      />
      <tbody>
        {servers?.length !== 0 ? (
          servers!.map((server) => (
            <tr
              key={server.id}
              className="border-b odd:bg-white even:bg-gray-50 h-16"
            >
              <th
                scope="row"
                className="border-r whitespace-nowrap px-6 py-4 text-gray-900 font-medium"
              >
                {server.name}
              </th>
              <td className="border-r px-6 py-4">{server.type}</td>
              <td className="px-6 py-4">
                <a
                  target="_blank"
                  href={server.url}
                  className="font-medium text-blue-600 hover:underline"
                >
                  {server.url}
                </a>
              </td>
              <td className="border-l px-6 py-4">
                <ServerActionsDropdownWrapper
                  serverId={server.id}
                  serverName={server.name}
                  serverTypeCode={server.typeCode}
                  canExportOplog={canExportOplog}
                  maxExportDaysSpan={maxExportDaysSpan}
                />
              </td>
            </tr>
          ))
        ) : (
          <tr>
            <td colSpan={4} className="text-center py-4 text-gray-500">
              <div className="p-4">該当するサーバーがありません</div>
            </td>
          </tr>
        )}
      </tbody>
    </table>
  );
}
