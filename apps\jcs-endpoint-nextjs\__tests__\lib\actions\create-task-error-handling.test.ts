/**
 * @fileoverview createTaskAction エラーハンドリングのテスト
 * @description 予期しない例外処理のテスト
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { expect } from "@jest/globals";
import { createTask } from "@/app/lib/actions/tasks";
import { getIronSession } from "iron-session";
import { PORTAL_ERROR_MESSAGES } from "@/app/lib/definitions";

// 必要なモックを設定
jest.mock("iron-session");

// Logger モック
jest.mock("@/app/lib/utils", () => ({
  Logger: {
    info: jest.fn(),
    error: jest.fn(),
  },
  formatMessage: jest.fn((template, params) => {
    let result = template;
    params.forEach((param, index) => {
      result = result.replace(`{${index}}`, param);
    });
    return result;
  }),
}));

describe("createTaskAction エラーハンドリング", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  /**
   * 試験観点：予期しない内部例外の処理
   * 試験対象：createTaskAction の通用例外処理
   * 試験手順：
   * 1. getIronSession で予期しない例外を発生させる
   * 2. 適切なエラーメッセージが返されることを確認
   * 確認項目：
   * - EMEC0027 エラーメッセージが返されること
   * - success が false であること
   */
  it("異常系: 予期しない内部例外の処理", async () => {
    // getIronSession で予期しない例外を発生させる
    (getIronSession as jest.Mock).mockRejectedValue(new Error("Unexpected internal error"));

    const formData = new FormData();
    formData.append("taskType", "TASK_TYPE.MGMT_ITEM_IMPORT");
    formData.append("serverId", "server-1");

    const result = await createTask(formData);

    expect(result.success).toBe(false);
    expect(result.message).toBe(
      PORTAL_ERROR_MESSAGES.EMEC0027.replace("{0}", "開始")
    );
  });

  /**
   * 試験観点：セッション取得時の例外処理
   * 試験対象：createTaskAction のセッション例外処理
   * 試験手順：
   * 1. getIronSession でネットワークエラーを発生させる
   * 2. 適切なエラーメッセージが返されることを確認
   * 確認項目：
   * - EMEC0027 エラーメッセージが返されること
   */
  it("異常系: セッション取得時のネットワークエラー", async () => {
    (getIronSession as jest.Mock).mockRejectedValue(new Error("Network timeout"));

    const formData = new FormData();
    formData.append("taskType", "TASK_TYPE.MGMT_ITEM_EXPORT");
    formData.append("serverId", "server-1");

    const result = await createTask(formData);

    expect(result.success).toBe(false);
    expect(result.message).toBe(
      PORTAL_ERROR_MESSAGES.EMEC0027.replace("{0}", "開始")
    );
  });

  /**
   * 試験観点：メモリ不足例外の処理
   * 試験対象：createTaskAction のメモリ例外処理
   * 試験手順：
   * 1. getIronSession でメモリ不足エラーを発生させる
   * 2. 適切なエラーメッセージが返されることを確認
   * 確認項目：
   * - EMEC0027 エラーメッセージが返されること
   */
  it("異常系: メモリ不足例外の処理", async () => {
    (getIronSession as jest.Mock).mockRejectedValue(new Error("Out of memory"));

    const formData = new FormData();
    formData.append("taskType", "TASK_TYPE.OPLOG_EXPORT");
    formData.append("serverId", "server-1");

    const result = await createTask(formData);

    expect(result.success).toBe(false);
    expect(result.message).toBe(
      PORTAL_ERROR_MESSAGES.EMEC0027.replace("{0}", "開始")
    );
  });

  /**
   * 試験観点：null例外の処理
   * 試験対象：createTaskAction のnull例外処理
   * 試験手順：
   * 1. getIronSession でnull参照エラーを発生させる
   * 2. 適切なエラーメッセージが返されることを確認
   * 確認項目：
   * - EMEC0027 エラーメッセージが返されること
   */
  it("異常系: null参照例外の処理", async () => {
    (getIronSession as jest.Mock).mockRejectedValue(new TypeError("Cannot read property of null"));

    const formData = new FormData();
    formData.append("taskType", "TASK_TYPE.MGMT_ITEM_IMPORT");
    formData.append("serverId", "server-1");

    const result = await createTask(formData);

    expect(result.success).toBe(false);
    expect(result.message).toBe(
      PORTAL_ERROR_MESSAGES.EMEC0027.replace("{0}", "開始")
    );
  });

  /**
   * 試験観点：非同期処理例外の処理
   * 試験対象：createTaskAction の非同期例外処理
   * 試験手順：
   * 1. getIronSession で非同期処理エラーを発生させる
   * 2. 適切なエラーメッセージが返されることを確認
   * 確認項目：
   * - EMEC0027 エラーメッセージが返されること
   */
  it("異常系: 非同期処理例外の処理", async () => {
    (getIronSession as jest.Mock).mockRejectedValue(new Error("Promise rejection"));

    const formData = new FormData();
    formData.append("taskType", "TASK_TYPE.MGMT_ITEM_EXPORT");
    formData.append("serverId", "server-1");

    const result = await createTask(formData);

    expect(result.success).toBe(false);
    expect(result.message).toBe(
      PORTAL_ERROR_MESSAGES.EMEC0027.replace("{0}", "開始")
    );
  });
});
