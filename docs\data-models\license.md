# 数据模型: 许可证 (License)

*   **表名 (逻辑名)**: `License`
*   **物理表名 (Prisma Model)**: `License` (参考项目根目录下 `prisma/schema.prisma` 文件获取权威定义)
*   **对应UI界面**: 许可证信息对话框 (ライセンス情報ダイアログ) 中可能间接使用其信息（如有效期），以及系统后台权限与功能控制逻辑。
*   **主要用途**: 存储客户使用本服务的许可证或契约信息。这些信息包括许可证的业务标识、类型、有效期、授权的客户端数量上限，以及用于控制环境状态（如是否禁用或处于维护模式）和特定功能权限（如操作日志导出）的关键标志或代码。

## 1. 字段定义

| 字段名 (Prisma Model) | 数据类型 (SQL参考) | PK | FK | UQ | Nullable | Default | 中文描述 | 日文名称 (界面参考/逻辑参考) |
|--------------------|--------------|----|----|----|----------|---------|------|------------------|
| `id` | `String` | ● | - | - | - | `cuid()` | 主键。系统自动生成的唯一标识符 (CUID格式)。 | (内部ID) |
| `licenseId` | `String` | - | - | ● | - | - | **许可证/契约的业务ID**。代表客户合同或服务订阅的唯一业务编号。在Prisma Schema中定义为 `@unique`。 | 契約ID |
| `type` | `String` | - | - | - | - | - | **许可证类型代码**。标识许可证的类别。其具体值及对应显示文本通过值列表 (LOV 表中 `parentCode='LICENSE_TYPE'` 的条目) 管理。 | ライセンス種別 |
| `expiredAt` | `DateTime` | - | - | - | - | - | **许可证的过期日期和时间**。指示此许可证有效期的截止点。 | 有効期限 |
| `maxClients` | `Int` | - | - | - | - | - | **许可证允许管理的最大客户端数量**。 | 保有数 |
| `isMaintenance` | `Boolean` | - | - | - | - | `false` | **维护模式标志**。指示此许可证关联的服务环境当前是否处于计划内维护状态。默认为 `false`。 | メンテナンスフラグ |
| `isDisabled` | `Boolean` | - | - | - | - | `false` | **环境禁用标志**。指示此许可证关联的服务环境是否已被整体禁用。若为`true`，所有与此许可证关联的用户将无法登录或使用系统。默认为 `false`。 | 環境無効化フラグ |
| `basicPlan` | `String?` | - | - | - | Yes | - | **基本契约计划代码 (可选)**。存储代表客户基本契约计划的字符串代码，例如 `'STANDARD'`, `'LIGHT_A'`, `'LIGHT_B'`。此字段用于控制特定功能的可用性。Prisma Schema中定义为 `String?`。 | 基本契約プランコード (内部逻辑用) |

*PK: Primary Key, FK: Foreign Key, UQ: Unique Constraint*
*(数据类型参考项目代码库中 `prisma/schema.prisma` 文件的权威定义。SQL参考仅为示意。)*
*(Nullable列中的“Yes”基于Prisma Schema中字段类型后的 `?` 标记。)*

## 2. 关系 (Relations)

本表与其他核心数据模型的关系，已在项目代码库的 `prisma/schema.prisma` 文件中通过反向关系字段明确定义。

*   **对 `Notification` (`notifications`)**: 一对多关系 (`Notification[]`)。
*   **对 `Server` (`servers`)**: 一对多关系 (`Server[]`)。
*   **对 `OperationLog` (`operationLogs`)**: 一对多关系 (`OperationLog[]`)。
*   **对 `LicensePlan` (`licensePlans`)**: 一对多关系 (`LicensePlan[]`)。
*   **对 `Task` (`tasks`)**: 一对多关系 (`Task[]`)。
*   **用户关联 (逻辑)**: 用户与许可证的关联通过外部身份提供商 (Keycloak) 中用户的属性与本表的`licenseId`进行逻辑匹配。

## 3. 索引 (Indexes)

根据 `prisma/schema.prisma` 文件中的定义，本表包含以下索引：

*   **主键**: `id` (由 `@id` 隐式创建索引)。
*   **唯一约束索引**: `licenseId` (由 `@unique` 隐式创建索引)，确保许可证业务ID的唯一性，并支持基于此业务ID的高效查询。

*(基于前端列表页采用全量数据缓存策略，以及后端主要通过 `licenseId` 查询的场景，目前定义的索引已满足核心需求。)*

## 4. 备注 (Notes)

*   本表是系统进行许可证校验、功能授权及部分用户访问控制的核心依据。
*   `isDisabled` 标志是实现“环境禁用时用户登录抑制”功能的关键。
*   `basicPlan` 字段存储的字符串代码直接用于在应用代码中判断特定功能的可用性。其合法值和业务含义在应用程序的配置或常量中定义。