/**
 * @file layout.tsx
 * @description 
 * <AUTHOR>
 * @copyright Copyright (C) 2024, 2025, Hitachi Solutions, Ltd.
 */

"use client";

import Breadcrumb from "../ui/breadcrumb";
import Header from "../ui/header";
import Sidebar from "../ui/sidebar";

// レイアウトコンポーネント
export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <div>
      <Header />
      <div className="flex bg-gray-900 h-[calc(100vh-6rem)]">
        <main className="order-2 flex-1 w-[calc(100vw-16rem)]">
          <div className="h-full flex flex-col py-2 pe-2">
            <Breadcrumb />
            <div className="flex-1 overflow-auto mt-2 bg-gradient-to-b from-0% from-[#898989] via-[#474747] via-50% to-[#323232] to-100%">
              <div className="p-2 h-full">
                <div className="bg-white h-full">{children}</div>
              </div>
            </div>
          </div>
        </main>
        <div className="order-1 w-64 flex-none">
          <Sidebar />
        </div>
      </div>
    </div>
  );
}
