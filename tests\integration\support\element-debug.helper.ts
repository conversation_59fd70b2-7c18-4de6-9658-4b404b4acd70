/**
 * @fileoverview 元素调试辅助工具，用于自动诊断和解决元素选择器问题
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { Page, Locator } from '@playwright/test';

/**
 * 元素选择器调试器
 * 当测试中遇到元素找不到的问题时，自动尝试多种选择器策略
 */
export class ElementDebugger {
  constructor(private page: Page) {}

  /**
   * 调试并查找select元素
   * @param elementDescription 元素描述，用于日志输出
   * @returns 找到的元素或null
   */
  async findSelectElement(elementDescription: string = 'select元素'): Promise<Locator | null> {
    console.log(`=== 调试查找${elementDescription} ===`);
    
    const selectSelectors = [
      'select',                          // 通用select
      'select[id]',                      // 有id的select
      'select[class*="ml-"]',           // 包含ml-的class
      'select[class*="border"]',        // 包含border的class
      '[role="combobox"]',              // role属性
      'select:has(option)',             // 包含option的select
      '[data-testid*="select"]',        // 测试id
      '.select, .dropdown',             // 常见class名
    ];

    return await this.tryMultipleSelectors(selectSelectors, elementDescription);
  }

  /**
   * 调试并查找按钮元素
   * @param buttonText 按钮文本
   * @param elementDescription 元素描述
   * @returns 找到的元素或null
   */
  async findButtonElement(buttonText: string, elementDescription: string = '按钮'): Promise<Locator | null> {
    console.log(`=== 调试查找${elementDescription}: "${buttonText}" ===`);
    
    const buttonSelectors = [
      `button:has-text("${buttonText}")`,
      `[role="button"]:has-text("${buttonText}")`,
      `input[type="button"][value="${buttonText}"]`,
      `input[type="submit"][value="${buttonText}"]`,
      `a:has-text("${buttonText}")`,
      `[data-testid*="button"]:has-text("${buttonText}")`,
      `[class*="btn"]:has-text("${buttonText}")`,
    ];

    return await this.tryMultipleSelectors(buttonSelectors, elementDescription);
  }

  /**
   * 调试并查找表头元素
   * @param headerText 表头文本
   * @param elementDescription 元素描述
   * @returns 找到的元素或null
   */
  async findTableHeaderElement(headerText: string, elementDescription: string = '表头'): Promise<Locator | null> {
    console.log(`=== 调试查找${elementDescription}: "${headerText}" ===`);
    
    const headerSelectors = [
      `th:has-text("${headerText}")`,
      `td:has-text("${headerText}")`,
      `[role="columnheader"]:has-text("${headerText}")`,
      `thead th:has-text("${headerText}")`,
      `thead td:has-text("${headerText}")`,
      `th >> text="${headerText}"`,
      `[data-testid*="header"]:has-text("${headerText}")`,
    ];

    return await this.tryMultipleSelectors(headerSelectors, elementDescription);
  }

  /**
   * 尝试多个选择器，返回第一个找到的元素
   * @param selectors 选择器数组
   * @param elementDescription 元素描述
   * @returns 找到的元素或null
   */
  private async tryMultipleSelectors(selectors: string[], elementDescription: string): Promise<Locator | null> {
    for (const selector of selectors) {
      try {
        const element = this.page.locator(selector);
        const count = await element.count();
        console.log(`选择器 "${selector}" 找到 ${count} 个元素`);
        
        if (count > 0) {
          console.log(`✅ 使用选择器: ${selector}`);
          return element.first();
        }
      } catch (error) {
        console.log(`❌ 选择器 "${selector}" 出错:`, error.message);
      }
    }
    
    console.log(`❌ 没有找到${elementDescription}`);
    return null;
  }

  /**
   * 获取页面的基本调试信息
   */
  async getPageDebugInfo(): Promise<void> {
    console.log('=== 页面调试信息 ===');
            // 检查页面是否包含常见元素
    const commonElements = {
      '表格': 'table',
      '表单': 'form',
      '按钮': 'button',
      '输入框': 'input',
      '下拉框': 'select',
      '链接': 'a'
    };
    
    for (const [name, selector] of Object.entries(commonElements)) {
      const count = await this.page.locator(selector).count();
      console.log(`${name}: ${count}个`);
    }
    
    // 检查是否有错误消息
    const errorSelectors = ['[class*="error"]', '[class*="alert"]', 'text=/error|エラー/i'];
    for (const selector of errorSelectors) {
      const errors = await this.page.locator(selector).allTextContents();
      if (errors.length > 0) {
              }
    }
  }

  /**
   * 等待页面完全加载
   */
  async waitForPageReady(): Promise<void> {
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForTimeout(2000); // 等待React hydration
    
    // 等待表格出现（如果是列表页面）
    try {
      await this.page.waitForSelector('table', { timeout: 5000 });
      console.log('✅ 页面表格已加载');
    } catch {
          }
  }
}

/**
 * 创建元素调试器实例
 * @param page Playwright页面对象
 * @returns 元素调试器实例
 */
export function createElementDebugger(page: Page): ElementDebugger {
  return new ElementDebugger(page);
}
