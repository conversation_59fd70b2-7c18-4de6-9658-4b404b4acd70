/**
 * @fileoverview 基底データ生成器クラス
 * @description 全ての種子データ生成器の共通機能を提供する抽象基底クラス
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { PrismaClient } from '@prisma/client';
// 简化版本不使用Logger类，直接使用console
import { JapaneseFaker } from '../utils/faker-jp';

/**
 * データ生成器の基底抽象クラス
 * 全ての具体的な生成器はこのクラスを継承して実装する
 */
export abstract class BaseGenerator {
  protected prisma: PrismaClient;
  protected faker: typeof JapaneseFaker;
  protected generatorName: string;

  constructor(prisma: PrismaClient, generatorName: string) {
    this.prisma = prisma;
    this.generatorName = generatorName;
    this.faker = JapaneseFaker;
  }

  /**
   * データ生成の主要メソッド
   * 各生成器で具体的な実装を行う
   * @returns 生成されたデータの数
   */
  abstract generate(): Promise<number>;

  /**
   * 既存データのクリーンアップ
   * 各生成器で具体的な実装を行う
   */
  abstract cleanup(): Promise<void>;

  /**
   * 生成器の名前を取得する
   * @returns 生成器名
   */
  abstract getGeneratorName(): string;

  /**
   * 生成予定のデータ数を取得する
   * @returns 生成予定数
   */
  abstract getExpectedCount(): number;

  /**
   * バッチ処理でデータを生成する
   * 大量データを効率的に処理するためのヘルパーメソッド
   * @param totalCount 総生成数
   * @param batchSize バッチサイズ
   * @param generateBatch バッチ生成関数
   * @returns 生成されたデータの総数
   */
  protected async generateInBatches<T>(
    totalCount: number,
    batchSize: number,
    generateBatch: (startIndex: number, count: number) => Promise<T[]>
  ): Promise<number> {
    let generatedCount = 0;

    for (let i = 0; i < totalCount; i += batchSize) {
      const currentBatchSize = Math.min(batchSize, totalCount - i);

      try {
        const batch = await generateBatch(i, currentBatchSize);
        generatedCount += batch.length;

        console.log(`${this.getGeneratorName()}: ${generatedCount}/${totalCount}件生成済み`);
      } catch (error) {
        console.error(`バッチ処理中にエラーが発生しました (${i}-${i + currentBatchSize}):`, error);
        throw error;
      }
    }

    return generatedCount;
  }

  /**
   * 生成器の実行
   * 生成処理を実行する
   * @returns 生成されたデータの数
   */
  async execute(): Promise<number> {
    console.log(`📊 ${this.getGeneratorName()}の生成を開始します`);
    const startTime = Date.now();

    try {
      const count = await this.generate();
      const duration = Date.now() - startTime;
      console.log(`✅ ${this.getGeneratorName()}の生成が完了しました (${count}件, ${duration}ms)`);
      return count;
    } catch (error) {
      console.error(`❌ ${this.getGeneratorName()}の生成中にエラーが発生しました:`, error);
      throw error;
    }
  }

  /**
   * ランダムな日付範囲を生成する
   * @param startDaysAgo 開始日（何日前）
   * @param endDaysAgo 終了日（何日前、デフォルト0=今日）
   * @returns ランダムな日付
   */
  protected randomDateInRange(startDaysAgo: number, endDaysAgo: number = 0): Date {
    const now = new Date();
    const startTime = now.getTime() - (startDaysAgo * 24 * 60 * 60 * 1000);
    const endTime = now.getTime() - (endDaysAgo * 24 * 60 * 60 * 1000);
    const randomTime = startTime + Math.random() * (endTime - startTime);
    return new Date(randomTime);
  }

  /**
   * 重複しないランダムなIDセットを生成する
   * @param existingIds 既存のIDリスト
   * @param count 生成する数
   * @param generateId ID生成関数
   * @returns 重複しないIDの配列
   */
  protected async generateUniqueIds<T>(
    existingIds: T[],
    count: number,
    generateId: () => T
  ): Promise<T[]> {
    const existingSet = new Set(existingIds);
    const newIds: T[] = [];
    
    while (newIds.length < count) {
      const id = generateId();
      if (!existingSet.has(id) && !newIds.includes(id)) {
        newIds.push(id);
        existingSet.add(id);
      }
    }
    
    return newIds;
  }

  /**
   * 配列をランダムにシャッフルする
   * @param array シャッフルする配列
   * @returns シャッフルされた配列
   */
  protected shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  /**
   * 重み付きランダム選択
   * @param items 選択肢の配列
   * @param weights 各選択肢の重み
   * @returns 重み付きでランダムに選択された要素
   */
  protected weightedRandomChoice<T>(items: T[], weights: number[]): T {
    if (items.length !== weights.length) {
      throw new Error('アイテム数と重み数が一致しません');
    }
    
    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
    let random = Math.random() * totalWeight;
    
    for (let i = 0; i < items.length; i++) {
      random -= weights[i];
      if (random <= 0) {
        return items[i];
      }
    }
    
    return items[items.length - 1];
  }

  /**
   * 指定された確率で条件を満たすかチェック
   * @param probability 確率（0-1）
   * @returns 条件を満たすかどうか
   */
  protected shouldExecute(probability: number): boolean {
    return Math.random() < probability;
  }
}
