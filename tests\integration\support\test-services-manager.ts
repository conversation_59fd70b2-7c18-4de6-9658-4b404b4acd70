/**
 * @fileoverview 测试服务管理器 - 统一管理测试环境中的所有服务
 * @description
 * 提供统一的接口来管理测试环境中的各种服务：
 * - Azurite (Azure Storage Emulator)
 * - Mock Server (模拟外部服务)
 * - Azure Functions (标准和长时运行)
 * - 服务状态监控和日志收集
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { execSync, spawn, ChildProcess } from 'child_process';
import { MockServerHelper } from './mock-server-helper';
import path from 'path';

/**
 * 服务状态枚举
 */
enum ServiceStatus {
  STOPPED = 'stopped',
  STARTING = 'starting',
  RUNNING = 'running',
  ERROR = 'error'
}

/**
 * 服务信息接口
 */
interface ServiceInfo {
  name: string;
  status: ServiceStatus;
  process?: ChildProcess;
  logs: string[];
  port?: number;
  startTime?: Date;
}

/**
 * 测试服务管理器
 * 统一管理测试环境中的所有服务
 */
export class TestServicesManager {
  private services: Map<string, ServiceInfo> = new Map();
  private mockServerHelper: MockServerHelper;

  constructor() {
    this.mockServerHelper = new MockServerHelper();
    this.initializeServices();
  }

  /**
   * 初始化服务配置
   */
  private initializeServices() {
    this.services.set('azurite', {
      name: 'Azurite',
      status: ServiceStatus.STOPPED,
      logs: [],
      port: 10000
    });

    this.services.set('mock-server', {
      name: 'Mock Server',
      status: ServiceStatus.STOPPED,
      logs: [],
      port: 3001
    });

    this.services.set('standard-functions', {
      name: 'Standard Azure Functions',
      status: ServiceStatus.STOPPED,
      logs: [],
      port: 7072  // 与 playwright.config.ts 保持一致
    });

    this.services.set('long-running-functions', {
      name: 'Long-Running Azure Functions',
      status: ServiceStatus.STOPPED,
      logs: [],
      port: 7071  // 与 playwright.config.ts 保持一致
    });
  }

  /**
   * 获取 Mock Server Helper 实例
   */
  getMockServerHelper(): MockServerHelper {
    return this.mockServerHelper;
  }

  /**
   * 确保 Azurite 正在运行
   */
  async ensureAzuriteRunning(): Promise<void> {
    const service = this.services.get('azurite')!;

    try {
      // 检查 Azurite 是否已经在运行
      const isRunning = this.checkPortInUse(10000);

      if (isRunning) {
        console.log('✅ Azurite already running (external)');
        service.status = ServiceStatus.RUNNING;
        return;
      }

      // 清理可能占用端口的进程
      await this.checkAndKillPortProcess(10000, 'azurite');

      // 启动 Azurite
      console.log('🚀 启动 Azurite (Blob Storage 模拟器)...');
      service.status = ServiceStatus.STARTING;

      const azuriteDataDir = path.resolve(__dirname, '../azurite-data');

      const azuriteProcess = spawn('azurite', [
        '--blobHost', '0.0.0.0',
        '--location', azuriteDataDir,
        '--debug', path.join(azuriteDataDir, 'debug.log'),
        '--skipApiVersionCheck',
        '--silent'
      ], {
        cwd: path.resolve(__dirname, '../../../'),
        stdio: 'pipe',
        shell: true  // 在 Git Bash 中需要通过 shell 启动
      });

      if (!azuriteProcess.pid) {
        throw new Error('Azurite 进程启动失败');
      }

      service.process = azuriteProcess;
      service.startTime = new Date();

      console.log(`📋 Azurite 进程 PID: ${azuriteProcess.pid}`);

      // 收集日志
      azuriteProcess.stdout?.on('data', (data) => {
        const log = data.toString();
        service.logs.push(log);
        if (log.includes('successfully listening')) {
          console.log(`📤 [Azurite]: ${log.trim()}`);
        }
      });

      azuriteProcess.stderr?.on('data', (data) => {
        const log = data.toString();
        service.logs.push(log);
        console.log(`📤 [Azurite Error]: ${log.trim()}`);
      });

      azuriteProcess.on('exit', (code, signal) => {
        console.log(`🔚 Azurite 进程退出 (code: ${code}, signal: ${signal})`);
        service.status = ServiceStatus.STOPPED;
      });

      // 等待启动完成 - 只需要 Blob 服务
      console.log('⏳ 等待 Azurite Blob 服务启动...');
      await this.waitForPort(10000, 15000);
      service.status = ServiceStatus.RUNNING;
      console.log('✅ Azurite 启动成功');

    } catch (error) {
      service.status = ServiceStatus.ERROR;
      service.logs.push(`启动失败: ${error}`);
      console.log(`❌ Azurite 启动失败: ${error}`);
      throw error;
    }
  }

  /**
   * 启动 Mock Server
   */
  async startMockServer(): Promise<void> {
    const service = this.services.get('mock-server')!;

    try {
      service.status = ServiceStatus.STARTING;
      await this.mockServerHelper.startServer();
      service.status = ServiceStatus.RUNNING;
      service.startTime = new Date();
    } catch (error) {
      service.status = ServiceStatus.ERROR;
      service.logs.push(`启动失败: ${error}`);
      throw error;
    }
  }

  /**
   * 启动标准 Azure Functions
   */
  async startStandardFunctions(): Promise<void> {
    const service = this.services.get('standard-functions')!;

    try {
      service.status = ServiceStatus.STARTING;

      // 检查端口是否已被占用，如果是则终止占用进程
      if (this.checkPortInUse(7072)) {
        console.log('🔧 端口 7072 被占用，尝试终止占用进程...');
        this.killProcessOnPort(7072);
        await new Promise(resolve => setTimeout(resolve, 2000)); // 等待进程终止
      }

      const functionsProcess = spawn('func', ['start', '--port', '7072'], {
        cwd: path.resolve(__dirname, '../../../apps/jcs-backend-services-standard'),
        stdio: 'pipe',
        shell: true
      });

      service.process = functionsProcess;
      service.startTime = new Date();

      // 收集日志
      functionsProcess.stdout?.on('data', (data) => {
        const log = data.toString();
        service.logs.push(log);
      });

      functionsProcess.stderr?.on('data', (data) => {
        const log = data.toString();
        service.logs.push(log);
      });

      // 等待启动完成
      await this.waitForPort(7072, 30000);

      // 额外等待一些时间让函数完全加载并输出日志
      await new Promise(resolve => setTimeout(resolve, 3000));

      service.status = ServiceStatus.RUNNING;

    } catch (error) {
      service.status = ServiceStatus.ERROR;
      service.logs.push(`启动失败: ${error}`);
      throw error;
    }
  }

  /**
   * 启动长时运行 Azure Functions
   */
  async startLongRunningFunctions(): Promise<void> {
    const service = this.services.get('long-running-functions')!;

    try {
      service.status = ServiceStatus.STARTING;

      // 检查端口是否已被占用，如果是则终止占用进程
      if (this.checkPortInUse(7071)) {
        console.log('🔧 端口 7071 被占用，尝试终止占用进程...');
        this.killProcessOnPort(7071);
        await new Promise(resolve => setTimeout(resolve, 2000)); // 等待进程终止
      }

      const functionsProcess = spawn('func', ['start', '--port', '7071'], {
        cwd: path.resolve(__dirname, '../../../apps/jcs-backend-services-long-running'),
        stdio: 'pipe',
        shell: true
      });

      service.process = functionsProcess;
      service.startTime = new Date();

      // 收集日志
      functionsProcess.stdout?.on('data', (data) => {
        const log = data.toString();
        service.logs.push(log);
      });

      functionsProcess.stderr?.on('data', (data) => {
        const log = data.toString();
        service.logs.push(log);
      });

      // 等待启动完成
      await this.waitForPort(7071, 30000);

      // 额外等待一些时间让函数完全加载并输出日志
      await new Promise(resolve => setTimeout(resolve, 3000));

      service.status = ServiceStatus.RUNNING;

    } catch (error) {
      service.status = ServiceStatus.ERROR;
      service.logs.push(`启动失败: ${error}`);
      throw error;
    }
  }

  /**
   * 检查服务是否正在运行
   */
  isServiceRunning(serviceName: string): boolean {
    const service = this.services.get(serviceName);
    return service?.status === ServiceStatus.RUNNING;
  }

  /**
   * 获取服务日志
   */
  getServiceLogs(serviceName: string): string[] {
    const service = this.services.get(serviceName);
    return service?.logs || [];
  }

  /**
   * 停止所有服务
   */
  async stopAllServices(): Promise<void> {
    console.log('🛑 停止所有测试服务...');

    for (const [, service] of this.services) {
      if (service.status === ServiceStatus.RUNNING && service.process) {
        try {
          console.log(`🛑 停止 ${service.name}...`);

          // 先尝试优雅关闭
          service.process.kill('SIGTERM');

          // 等待一段时间让进程优雅关闭
          await new Promise(resolve => setTimeout(resolve, 2000));

          // 如果进程仍然存在，强制终止
          if (!service.process.killed) {
            console.log(`🔨 强制终止 ${service.name}...`);
            service.process.kill('SIGKILL');
          }

          service.status = ServiceStatus.STOPPED;
        } catch (error) {
          console.error(`❌ 停止 ${service.name} 失败:`, error);
        }
      }
    }

    // 额外的端口清理 - 强制清理 Azure Functions 端口
    await this.forceCleanupPorts([7072, 7071]);

    // 停止 Mock Server
    try {
      await this.mockServerHelper.stopServer();
    } catch (error) {
      console.error('❌ 停止 Mock Server 失败:', error);
    }

    console.log('✅ 所有服务已停止');
  }

  /**
   * 强制清理指定端口上的进程
   */
  private async forceCleanupPorts(ports: number[]): Promise<void> {
    for (const port of ports) {
      try {
        console.log(`🔍 检查端口 ${port}...`);
        const result = execSync(`netstat -ano | findstr :${port}`, { encoding: 'utf8' });

        if (result.trim()) {
          // 提取 PID
          const lines = result.trim().split('\n');
          for (const line of lines) {
            const parts = line.trim().split(/\s+/);
            if (parts.length >= 5) {
              const pid = parts[4];
              if (pid && pid !== '0') {
                console.log(`🔨 强制终止占用端口 ${port} 的进程 PID: ${pid}`);
                try {
                  execSync(`taskkill /PID ${pid} /F`, { encoding: 'utf8' });
                  console.log(`✅ 成功终止进程 ${pid}`);
                } catch (killError) {
                  console.log(`⚠️ 终止进程 ${pid} 失败:`, killError);
                }
              }
            }
          }
        } else {
          console.log(`✅ 端口 ${port} 未被占用`);
        }
      } catch (error) {
        console.log(`⚠️ 检查端口 ${port} 时出错:`, error);
      }
    }
  }

  /**
   * 检查端口是否被占用
   */
  private checkPortInUse(port: number): boolean {
    try {
      const result = execSync(`netstat -an | findstr :${port}`, { encoding: 'utf8' });
      return result.includes(`0.0.0.0:${port}`) || result.includes(`127.0.0.1:${port}`);
    } catch {
      return false;
    }
  }

  /**
   * 终止占用指定端口的进程
   */
  private killProcessOnPort(port: number): void {
    try {
      // 查找占用端口的进程ID
      const result = execSync(`netstat -ano | findstr :${port}`, { encoding: 'utf8' });
      const lines = result.split('\n').filter(line => line.trim());

      for (const line of lines) {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 5) {
          const pid = parts[parts.length - 1];
          if (pid && pid !== '0') {
            console.log(`🔧 终止进程 PID ${pid} (端口 ${port})`);
            execSync(`taskkill /F /PID ${pid}`, { stdio: 'ignore' });
          }
        }
      }
    } catch (error) {
      console.log(`⚠️ 无法终止端口 ${port} 的进程: ${error}`);
    }
  }

  /**
   * 等待端口可用
   */
  private async waitForPort(port: number, timeout: number = 30000): Promise<void> {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      if (this.checkPortInUse(port)) {
        return;
      }
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    throw new Error(`端口 ${port} 在 ${timeout}ms 内未启动`);
  }

  /**
   * 获取服务状态摘要
   */
  getServicesSummary(): Record<string, any> {
    const summary: Record<string, any> = {};

    for (const [serviceName, service] of this.services) {
      summary[serviceName] = {
        name: service.name,
        status: service.status,
        port: service.port,
        startTime: service.startTime,
        logCount: service.logs.length
      };
    }

    return summary;
  }

  /**
   * 停止所有服务并清理资源
   */
  async stopAllServices(): Promise<void> {
    console.log('🛑 停止所有测试服务...');

    for (const [serviceName, service] of this.services) {
      if (service.process && !service.process.killed) {
        console.log(`🔚 停止 ${service.name}...`);

        try {
          // 发送 SIGTERM 信号
          service.process.kill('SIGTERM');

          // 等待优雅退出
          await new Promise(resolve => setTimeout(resolve, 2000));

          // 如果仍未退出，强制终止
          if (!service.process.killed) {
            service.process.kill('SIGKILL');
            console.log(`💀 强制终止 ${service.name}`);
          }

          service.status = ServiceStatus.STOPPED;
        } catch (error) {
          console.log(`⚠️ 停止 ${service.name} 时出错: ${error}`);
        }
      }
    }

    // 清理 Azurite 数据目录
    await this.cleanupAzuriteData();

    console.log('✅ 所有服务已停止');
  }

  /**
   * 清理 Azurite 数据目录
   */
  private async cleanupAzuriteData(): Promise<void> {
    const fs = require('fs');
    const azuriteDataDir = path.resolve(__dirname, '../azurite-data');

    try {
      if (fs.existsSync(azuriteDataDir)) {
        // 等待一下确保文件句柄释放
        await new Promise(resolve => setTimeout(resolve, 1000));

        fs.rmSync(azuriteDataDir, { recursive: true, force: true });
        console.log('🗑️ 清理 Azurite 数据目录');
      }
    } catch (error) {
      console.log(`⚠️ 清理 Azurite 数据目录失败: ${error}`);
    }
  }

  /**
   * 启动完整的测试环境
   */
  async startFullTestEnvironment(): Promise<void> {
    console.log('🚀 启动完整测试环境...');

    try {
      // 1. 启动 Azurite (Azure Storage 模拟器)
      await this.ensureAzuriteRunning();

      // 2. 启动 Mock Server
      await this.startMockServer();

      // 3. 启动 Azure Functions (标准)
      await this.startStandardFunctions();

      // 4. 启动 Azure Functions (长时运行)
      await this.startLongRunningFunctions();

      console.log('✅ 完整测试环境启动成功');

      // 显示服务状态摘要
      const summary = this.getServicesSummary();
      console.log('📊 服务状态验证:');
      for (const [serviceName, info] of Object.entries(summary)) {
        const status = info.status === 'running' ? '✅ 运行中' : '❌ 未运行';
        console.log(`   - ${info.name}: ${status}`);
      }

    } catch (error) {
      console.log(`❌ 启动测试环境失败: ${error}`);

      // 启动失败时清理已启动的服务
      await this.stopAllServices();
      throw error;
    }
  }

  /**
   * 检查并终止占用端口的进程
   */
  private async checkAndKillPortProcess(port: number, serviceName: string): Promise<void> {
    try {
      const { execSync } = require('child_process');

      // 检查端口是否被占用
      const result = execSync(`netstat -ano | grep :${port}`, { stdio: 'pipe', encoding: 'utf8' });

      if (result.includes('LISTENING')) {
        const lines = result.split('\n');

        for (const line of lines) {
          if (line.includes('LISTENING')) {
            const parts = line.trim().split(/\s+/);
            const pid = parts[parts.length - 1];

            if (pid && pid !== '0') {
              console.log(`⚠️ 端口 ${port} 被进程 ${pid} 占用，正在终止...`);

              try {
                execSync(`cmd //c "taskkill /PID ${pid} /F"`, { stdio: 'pipe' });
                console.log(`✅ 成功终止进程 ${pid} (端口 ${port})`);

                // 等待端口释放
                await new Promise(resolve => setTimeout(resolve, 2000));
              } catch (killError) {
                console.log(`⚠️ 终止进程 ${pid} 失败: ${killError}`);
              }
            }
          }
        }
      }
    } catch (error) {
      // 端口未被占用或查询失败，继续执行
    }
  }
}