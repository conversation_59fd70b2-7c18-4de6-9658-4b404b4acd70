#!/bin/bash

# 本地集成测试环境一键启动脚本
# 并行启动所有应用，并在退出时自动、优雅地关闭所有进程。

# 解析命令行参数
TEST_MODE=false
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --test) TEST_MODE=true; shift ;;
        *) echo "Unknown parameter: $1"; exit 1 ;;
    esac
done

# 清理函数：当脚本退出时（例如按Ctrl+C），此函数将被调用
cleanup() {
    echo ""
    echo "🛑 Shutting down local environment..."
    # 使用 kill -- -$$ 来杀死所有由该脚本启动的子进程
    # 2>/dev/null 用于抑制当进程已不存在时可能出现的错误信息
    kill -- -$$ 2>/dev/null
    echo "✅ Environment shut down."
}

# 捕获退出(EXIT)、中断(INT)、终止(TERM)信号，并执行cleanup函数
trap cleanup EXIT INT TERM

# 检查是否安装了Azure Functions Core Tools
if ! command -v func &> /dev/null; then
    echo "❌ 错误: 未找到 'func' 命令。请确保已安装 Azure Functions Core Tools 并添加到系统路径中。"
    echo "   安装指南: https://learn.microsoft.com/zh-cn/azure/azure-functions/functions-run-local"
    echo "   您可以使用以下命令安装:"
    echo "   npm install -g azure-functions-core-tools@4 --unsafe-perm true"
    echo ""
    echo "   如果已安装但命令不可用，请尝试重新打开终端或重启计算机。"
    echo "⚠️ 将仅启动Next.js应用，Azure Functions将不可用。"
    FUNC_AVAILABLE=false
else
    FUNC_AVAILABLE=true
fi

# 检查是否安装了Azurite
if ! command -v azurite &> /dev/null; then
    echo "❌ 错误: 未找到 'azurite' 命令。请确保已安装 Azurite。"
    echo "   您可以使用以下命令安装:"
    echo "   npm install -g azurite"
    echo "⚠️ 将跳过Azurite启动，Azure Storage模拟将不可用。"
    AZURITE_AVAILABLE=false
else
    AZURITE_AVAILABLE=true
fi

if [ "$TEST_MODE" = true ]; then
    echo "🧪 Starting local E2E TEST environment..."
    echo "-----------------------------------------"
    echo "Using test configurations for all services"
else
    echo "🚀 Starting local development environment..."
    echo "-----------------------------------------"
fi

# 并行启动所有服务，并将其置于后台运行 (&)

# 启动Azurite (Azure Storage模拟器)
if [ "$AZURITE_AVAILABLE" = true ]; then
    echo "Starting Azurite (Azure Storage Emulator)..."
    # 创建Azurite数据目录
    mkdir -p .azurite
    # 启动Azurite，使用标准端口：Blob(10000), Queue(10001), Table(10002)
    # 添加 --skipApiVersionCheck 以避免版本兼容性问题
    azurite --silent --location .azurite --debug .azurite/debug.log --skipApiVersionCheck &
    AZURITE_PID=$!
    echo "   - Azurite Blob Storage: http://127.0.0.1:10000"
    echo "   - Azurite Queue Storage: http://127.0.0.1:10001"
    echo "   - Azurite Table Storage: http://127.0.0.1:10002"

    # 等待Azurite启动
    sleep 2
fi

# 启动Azure Automation Mock Server (仅在测试模式下)
if [ "$TEST_MODE" = true ]; then
    echo "Starting Azure Automation Mock Server..."
    (cd tests/integration && npm run start-mock-server) &
    MOCK_SERVER_PID=$!
    echo "   - Azure Automation Mock Server: http://localhost:3001"
fi

# 启动标准Function App
if [ "$FUNC_AVAILABLE" = true ]; then
    if [ "$TEST_MODE" = true ]; then
        echo "Starting standard Azure Functions with test settings..."
        (cd apps/jcs-backend-services-standard && npm run start:test -- --port 7072) &
    else
        echo "Starting standard Azure Functions..."
        (cd apps/jcs-backend-services-standard && npm run start -- --port 7072) &
    fi

    # 启动长时运行Function App
    if [ "$TEST_MODE" = true ]; then
        echo "Starting long-running Azure Functions with test settings..."
        (cd apps/jcs-backend-services-long-running && npm run start:test -- --port 7071) &
    else
        echo "Starting long-running Azure Functions..."
        (cd apps/jcs-backend-services-long-running && npm run start -- --port 7071) &
    fi
fi

# 启动Next.js应用
echo "Starting Next.js application..."
if [ "$TEST_MODE" = true ]; then
    echo "Starting Next.js with test environment (production mode)..."
    echo "Building and starting Next.js in production mode with NODE_ENV=test..."
    (cd apps/jcs-endpoint-nextjs && npm run build && NODE_ENV=test npm run start) &
else
    (cd apps/jcs-endpoint-nextjs && npm run dev) &
fi

echo "-----------------------------------------"
echo "✅ All services started in the background."
echo ""
echo "🌐 Web Services:"
echo "   - Next.js Frontend: http://localhost:3000"
if [ "$FUNC_AVAILABLE" = true ]; then
    echo "   - Standard Functions: http://localhost:7072"
    echo "   - Long-Running Functions: http://localhost:7071"
fi
echo ""
if [ "$AZURITE_AVAILABLE" = true ]; then
    echo "💾 Storage Services:"
    echo "   - Azurite Blob Storage: http://127.0.0.1:10000"
    echo "   - Azurite Queue Storage: http://127.0.0.1:10001"
    echo "   - Azurite Table Storage: http://127.0.0.1:10002"
    echo ""
fi
if [ "$TEST_MODE" = true ]; then
    echo "🧪 Test Services:"
    echo "   - Azure Automation Mock Server: http://localhost:3001"
    echo ""
    echo "✨ Your E2E TEST environment is ready."
    echo "   Database: test (isolated from development)"
    echo "   Storage: Azurite (local emulation)"
    echo "   Azure APIs: Mock Server (local simulation)"
else
    echo "✨ Your development environment is ready."
fi
echo ""
echo "Press Ctrl+C to shut down all services gracefully."
echo "-----------------------------------------"

# 等待所有后台进程结束。脚本会在此处暂停，直到用户按下Ctrl+C
wait