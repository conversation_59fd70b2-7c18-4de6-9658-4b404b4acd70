# 项目文档至DDS交付文档的迁移与创建指南

## 1. 引言

### 1.1 本指南的目的与范围

本文档旨在为“JCS端点资产与任务管理系统”项目（及类似项目）提供一套清晰、可执行的规范和最佳实践，用于指导如何以项目内部管理的最新技术信息（`docs/`目录下Markdown文档群，以下简称“SSoT文档”）为唯一事实来源 (Single Source of Truth - SSoT)，来创建或更新提交给客户或作为官方成果物的日语详细设计书（DDS）。

本文档范围涵盖从SSoT文档到DDS交付成果（主要设想为Excel或Word格式）的信息提取、翻译、内容重构、格式调整及质量保证的整个流程。

### 1.2 目标读者

*   项目开发团队成员（设计师、开发者）
*   AI编程辅助工具及其用户
*   技术文档编写者、文档管理员
*   项目经理、产品负责人

### 1.3核心原则

1.  **SSoT文档的绝对优先**: `docs/` 目录下的Markdown格式SSoT文档是所有设计信息、功能规格、技术细节的唯一且最新的权威版本。DDS交付成果应被视为此SSoT文档在特定时间点的快照、翻译以及按指定格式的重构版本。
2.  **信息的选择、转换及DDS内部自洽性**: 并非所有SSoT文档中的信息都原封不动地适用于DDS交付成果。需要根据DDS的目标读者、目的以及指定的格式（例如客户提供的Excel模板），对信息进行适当的选择、摘要、翻译和重构。DDS交付成果应能独立构成、易于理解，不应包含对项目内部SSoT文档的直接文件路径引用等。必要的定义信息（如LOV、错误消息）应在DDS内部设立专门章节（或工作表）进行描述。
3.  **日语表达与格式统一**: 所有描述应遵循日本IT行业标准详细设计书的惯例，使用专业且准确的日语（原则上采用“である調”）。术语、图表样式、章节结构和格式应严格遵守目标DDS的模板或客户的规格要求。
4.  **细颗粒度与可读性的平衡**: 在满足客户要求的“与代码一一对应的级别”的细致程度的同时，也必须确保DDS的主要读者（客户、外部评审员等）易于理解。应斟酌信息呈现方式，避免使用过于技术化、需要预备知识的内部术语或缩写（除非在DDS内部已有适当注释或定义）。
5.  **AI辅助与人工最终审核及质量保证**: AI工具可以在翻译、信息提取、初稿生成等方面提供有效支持，但DDS交付成果的最终质量（信息准确性、日语表达的恰当性、格式一致性等）由人工负责人（设计师、文档创建者）承担。所有AI生成的内容都必须经过人工的彻底审查和修正。

## 2. 准备工作

在开始创建DDS交付成果之前，请确认以下准备工作已完成：

*   **最新的SSoT文档群**:
    *   相关的组件设计文档 (例如: `docs/components/03-servers/server-list.md`)
    *   核心Server Action设计文档 (例如: `docs/components/actions/create-task-action.md`)
    *   系统架构设计文档 (`docs/architecture/system-architecture.md`)
    *   API规格书 (`docs/apis/openapi.v1.yaml` - 若适用)
    *   数据模型补充说明 (`docs/data-models/`) 及Prisma Schema (`prisma/schema.prisma`)
    *   各种定义文档 (例如: `docs/definitions/lov-definitions.md`, `docs/definitions/error-messages.md`, `docs/definitions/glossary.md`)
    *   环境变量指南 (`docs/guides/environment-variables.md`)
    确保以上所有文档均为最新版本，且内容准确性已得到确认。
*   **DDS交付成果模板**: 客户指定的Excel或Word模板文件。若无模板，可参考以往类似项目的DDS或行业标准格式，预先定义基本结构。
*   **DDS创建范围与要求的明确化**: 事先确认并明确本次DDS需要覆盖的功能范围、要求的详细程度、交付日期以及特殊事项（如客户的特别指示）。
*   **相关工具**: Markdown编辑器、Office产品（Excel, Word）、Mermaid等图表创建及验证工具。

## 3. SSoT文档至DDS主要章节的信息映射与创建指南

本节阐述了如何从SSoT文档（主要是Markdown格式的组件设计文档）的各个部分提取信息，并将其写入DDS交付成果（设想为Excel/Word格式）的主要章节或工作表中。

*(注：以下章节标题为通用示例，实际操作时应根据DDS模板或客户要求适当调整。SSoT文档内的引用路径为示例，请根据实际项目结构调整。)*

### 3.1 功能概要 (DDS章节)

*   **SSoT信息源**:
    *   对应组件设计文档 (`docs/components/.../[component-name].md`) 的“1. 概要”部分（目的、用户故事/需求等）。
    *   对应组件设计文档的“2. 功能规格”部分（主要流程概述、核心业务规则等）。
    *   系统架构设计文档 (`docs/architecture/system-architecture.md`) 的相关部分（功能定位、高层技术构成等）。
*   **DDS编写指南**:
    *   从SSoT中提取该功能的目的、背景、主要用户价值以及提供的核心功能，用简洁明确的日语进行描述。
    *   必要时，引用或参考SSoT“2.1 主要流程”中定义的高层业务流程图（若是Mermaid图，可导出为图片粘贴至DDS，或在DDS内重新绘制），以说明功能的整体概况。
    *   简要列出实现本功能所采用的主要技术要素（例如：Next.js, Prisma等）。

### 3.2 画面布局 (DDS章节)

*   **SSoT信息源**:
    *   对应组件设计文档 (`docs/components/.../[component-name].md`) 的“2.3 用户界面概述”（UI草图、主要交互点等）。
    *   (若有) 单独管理的UI设计文档或原型。
*   **DDS编写指南**:
    *   明确画面ID和画面名称。
    *   说明画面的整体构成、主要显示区域（页眉、主体、页脚等）及其布局关系。
    *   如果SSoT中有UI草图或线框图的图片引用，应将这些图片导入DDS，或在DDS内根据情况简化重绘。
    *   对导航和主要操作区域进行高层描述。
    *   详细的各画面项目定义应在后续“画面项目定义”章节中进行，本章重点在于布局的整体概念。

### 3.3 画面项目定义 (DDS章节)

*   **SSoT信息源**:
    *   对应组件设计文档 (`docs/components/.../[component-name].md`) 的“3.2 详细界面元素定义”（主画面及各模态框的UI元素定义表）。
*   **DDS编写指南**:
    *   遵循DDS模板的表格式（例如：项目编号、项目名（逻辑）、项目名（物理/ID）、类别、位数/字节数、必须、文字类型、初始显示、备注/格式等），从SSoT提取信息并转换填入。
    *   **项目名（逻辑）**: 直接使用SSoT中的“界面显示文本(日文)”。
    *   **项目名（物理/ID）**: 参考SSoT中的“建议英文ID/React key”。
    *   **类别**: 将SSoT中的“控件类型”映射为DDS中常用的日语控件类别（例如：テキストボックス、ラベル、ボタン、ドロップダウンリスト、ラジオボタン、チェックボックス、テーブル、リンク等）。
    *   **位数/字节数, 必须, 文字类型**: 从SSoT的“校验规则/显示逻辑”或“格式/备注”中提取相关信息，并按DDS的格式要求填写。若SSoT无明确记载，则填写“-”或遵循DDS惯例。
    *   **初始显示**: 从SSoT的“数据来源/状态”或“格式/备注”中提取初始显示相关描述。
    *   **备注/格式**: 将SSoT中关于“数据来源/状态(明确绑定的前端state变量名或props名)”、“主要行为/事件(触发的事件处理器函数名)”、“校验规则/显示逻辑(客户端校验规则、条件显示逻辑)”、“格式/备注(中文)”等信息整合，用日语简洁具体地描述。
        *   选项值基于LOV的情况，应明确注明其参照了本DDS内部“参照値一覧定義”章节的对应条目（例如：“本設計書『X.X 参照値一覧定義 - サーバ種別』で定義された値を表示”）。
        *   可能显示错误消息或确认消息的项，应注明其参照了本DDS内部“エラーメッセージ一覧”章节的对应错误代码。
    *   不仅主画面的项目，模态框内的项目也应同样处理，必要时设立子章节（例如：“操作ログエクスポートパラメータ入力モーダル画面項目”）进行详细定义。若SSoT中定义了“モーダルダイアログ共通項目定義”，也应在DDS中体现，并在具体模态框定义中说明差异部分。

### 3.4 画面遷移 (DDS章节)

*   **SSoT信息源**:
    *   对应组件设计文档 (`docs/components/.../[component-name].md`) 的“2.1 主要流程”及相关的事件处理描述。
    *   (若有) 系统架构设计文档 (`docs/architecture/system-architecture.md`) 的高层画面迁移图。
*   **DDS编写指南**:
    *   描述本功能范围内的主要画面间迁移，以及模态框的显示/隐藏迁移。
    *   明确引发迁移的用户操作或系统事件。
    *   必要时，附加迁移图（可将Mermaid图导出为图片，或使用DDS内工具重绘），并补充说明迁移条件及迁移前后状态。
    *   对外部系统（例如：JP1/ITDM2管理画面）的迁移也应明确说明。

### 3.5 イベント定義 (DDS章节)

*   **SSoT信息源**:
    *   对应组件设计文档 (`docs/components/.../[component-name].md`) 的“3.3 详细事件处理逻辑”。
*   **DDS编写指南**:
    *   针对SSoT中定义的每个事件（初始显示、各按钮点击、输入字段变更、任务菜单选择等），遵循DDS模板的格式（例如：事件ID、事件名、触发器、处理概要、处理详情等）进行描述。
    *   **处理详情**: 将SSoT中“前端处理步骤 (Client-side Processing Flow)”描述的步骤（状态读取、参数校验、Server Action/API调用参数构建、响应处理、UI更新等），用日语具体且按顺序地描述。
        *   客户端校验发生错误时，应明确指出将显示本DDS内部“エラーメッセージ一覧”中对应的错误代码的消息。
        *   调用Server Action时，应明确指出调用的Server Action名称（例如：`createTaskAction`）和主要的输入参数（`taskType`、`serverId`及任务特定参数）。Server Action的内部处理详情应引导读者参考后续的“バックエンドタスク受付処理詳細”章节。
        *   对Server Action的响应（`CreateTaskActionResult`）如何解读，以及如何向UI反馈（成功通知、错误通知等）的逻辑进行描述。成功/失败通知同样应参照本DDS内部“エラーメッセージ一覧”中的对应消息ID。
    *   从任务操作菜单发起的各项任务（操作日志导出、管理项目定义导入、管理项目定义导出），都应作为独立的事件进行详细描述。

### 3.6 バックエンドタスク受付処理詳細 (DDS章节 - 例如Server Action)

*   **SSoT信息源**:
    *   `createTaskAction` Server Action设计文档 (`docs/components/actions/create-task-action.md`) 全文。
    *   各特定任务组件设计文档（例如：`docs/components/03-servers/tasks/export-operation-log.md`）的“3.6.1 `createTaskAction`特定处理分支”部分。
*   **DDS编写指南**:
    *   本章详细描述由前端调用的主要后端处理逻辑（在本项目中主要是`createTaskAction` Server Action）的内部实现。
    *   **概要**: 根据SSoT，用日语准确描述`createTaskAction`的职责、输入参数（`FormData`结构及各字段定义）、返回值（`CreateTaskActionResult`结构及各字段定义）。输入/返回值参数表可参考SSoT的格式，以适合DDS的形式再现。
    *   **共通処理フローとロジック**: 使用Mermaid流程图（导出为图片或用DDS内工具重绘）和日语步骤描述，详细说明SSoT中定义的通用处理步骤（参数解析、会话验证、服务器配置获取、权限校验、DB核心事务、Service Bus发送等）。错误发生时的消息ID应参照本DDS内部“エラーメッセージ一覧”。
    *   **特定タスク種別処理ロジック**: 根据SSoT，针对每个`taskType`的分支处理（特定参数校验、文件上传、`parametersJson`构建、Service Bus消息参数构建等），分别设立子章节详细描述。
    *   **エラー処理メカニズム**: 概述SSoT中描述的错误处理策略（顶层异常捕获、预期业务错误处理、补偿逻辑等）。
    *   **依存データサービスおよび外部サービスインターフェース規約 (概要)**: 列出SSoT中提及的、`createTaskAction`所依赖的主要内部数据服务函数（来自`app/lib/data.ts`）的核心职责，以及与外部Azure服务（如Blob Storage, Service Bus）交互的概要（概念层面）。

### 3.7 主要機能シーケンス図 (DDS章节)

*   **SSoT信息源**:
    *   对应组件设计文档 (`docs/components/.../[component-name].md`) 的“3.4.3 主要交互序列图”等。
    *   (我们修正后的)“服务器列表数据显示序列图”和“服务器列表任务发起序列图”。
*   **DDS编写指南**:
    *   从SSoT中提取已完成且验证无误的Mermaid序列图代码。如果DDS不能直接支持Mermaid语法，则将其高质量地导出为图片并粘贴至DDS。
    *   在每个序列图之前，用简洁的日语说明该图所示的范围和主要场景（例如：初期表示、タスク発行正常系）。
    *   参与者（生命线）的名称使用SSoT中确定的简洁日语名（例如：ユーザー、フロントエンド、Next.jsサーバ、データベース等）。
    *   消息描述也应简洁明了，必要时参照本DDS内部的错误消息ID等。
    *   **Mermaid图的准确性**: 在最终DDS整合前，必须用Mermaid渲染工具彻底验证图表的语法正确性和显示效果。

### 3.8 テーブル定義 (DDS章节)

*   **SSoT信息源**:
    *   `prisma/schema.prisma` (物理 schema 的SSoT)。
    *   相关的数据模型补充说明 (`docs/data-models/[table-name].md`)。
*   **DDS编写指南**:
    *   根据DDS模板的格式，描述本功能直接参照或更新的主要数据库表（例如：`Server`, `Lov`）的定义。
    *   **论理名**: 从SSoT的数据模型补充说明等处获取日语的业务名称。
    *   **物理名**: 准确填写`prisma/schema.prisma`中的模型名/字段名。
    *   **データ型**: 将`prisma/schema.prisma`中定义的类型，映射为DDS中常用的SQL数据类型（例如：NVARCHAR, INTEGER, BIT, DATETIME等）（或者直接使用Prisma的类型名并加注说明）。
    *   **ディフォルト値, NOT NULL, ユニーク, 主キー**: 根据`prisma/schema.prisma`的定义准确填写。
    *   **備考**: 从SSoT的数据模型补充说明或`prisma/schema.prisma`的注释中，提取字段的业务含义、约束、具体示例等，用日语描述。若参照LOV代码，应明确指出参照本DDS内部“参照値一覧定義”。

### 3.9 データ取得クエリ定義 (DDS章节 - Prisma Client 查询示例)

*   **SSoT信息源**:
    *   对应组件设计文档 (`docs/components/.../[component-name].md`) 的“3.5.2 主要数据查询逻辑”和“3.6 关键后端逻辑/算法”中关于数据访问的描述。
    *   `app/lib/data.ts` 等实际数据访问层代码（用于理解概念）。
*   **DDS编写指南**:
    *   针对本功能中的主要数据获取和更新操作，说明其目的、主要条件，并提供使用Prisma Client的**概念性**查询示例。
    *   **Prisma查询示例**: 如SSoT中所述，这仅为概念性示例，需明确指出实际的复杂缓存策略和内存处理逻辑是在`app/lib/data.ts`等处实现的。DDS的目的是让读者大致了解数据访问的方法。
    *   查询示例中的可变部分（例如：用户ID、过滤值）应用占位符或注释说明。
    *   所有注释和解释均用日语。

### 3.10 参照値一覧定義 (DDS章节)
### 3.11 エラーメッセージ一覧 (DDS章节)
### 3.12 主要環境変数とその影響 (DDS章节)

*   **SSoT信息源**:
    *   `docs/definitions/lov-definitions.md`
    *   `docs/definitions/error-messages.md`
    *   `docs/guides/environment-variables.md`
*   **DDS编写指南**:
    *   这三个章节的内容，直接来源于我们为`server-list-detailed-design.md`准备的、已在DDS内部定义的相应章节。将这些内容（或根据DDS模板稍作格式调整后）转录至此。
    *   它们作为DDS内部的SSoT，供其他章节引用。
    *   所有信息均用日语描述。

## 4. AI辅助的最佳实践

*   **翻译辅助**: 可利用AI辅助将SSoT文档（中文）初步翻译为DDS（日语）的草稿。但人工必须对机器翻译结果进行彻底校对，确保其准确性和专业性。
*   **信息提取与结构化**: 可指示AI从SSoT的特定部分提取信息，并按照DDS模板的对应字段进行初步映射。
*   **Mermaid图初步生成**: 可让AI根据SSoT的逻辑描述生成Mermaid图的初始代码，然后由人工进行修正和验证。
*   **最终质量保证**: AI仅为辅助工具。DDS整体的逻辑一致性、信息准确性、日语表达质量、格式规范性及是否满足客户要求，必须由人工设计师/创建者负责审查和保证。

## 5. 通用注意事项与最佳实践

*   **DDS模板遵从**: 始终以提供的DDS模板（Excel/Word）的结构、格式和描述规则为最优先。本指南提供的是从SSoT向模板填充信息时的思路。
*   **图表引用**: DDS内部的图表通常会编号，并在正文中通过编号引用。若将Mermaid图转换为图片，需注意其分辨率和文件大小。
*   **版本管理**: SSoT文档和DDS交付成果均需进行适当的版本控制。DDS的修订历史中，最好能记录其所依据的SSoT文档版本。
*   **审查与反馈**: 创建完成的DDS应由团队内部，以及（若可能）客户代表进行审查，并根据反馈意见提升质量。

本指南希望能为从SSoT文档到高质量DDS交付成果的顺畅迁移与创建工作提供帮助。