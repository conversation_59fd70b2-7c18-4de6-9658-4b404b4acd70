# 缺陷报告 - 管理项目导出功能

## 缺陷概要

**缺陷ID**: DEF-001  
**发现日期**: 2025-07-31  
**发现者**: 集成测试  
**严重程度**: 高  
**优先级**: 高  

## 缺陷描述

管理项目导出功能在执行时失败，任务状态直接从 `QUEUED` 变为 `COMPLETED_ERROR`，无法正常完成导出流程。

## 错误详情

**错误代码**: EMET0008  
**错误消息**: The specifed resource name contains invalid characters.  
**系统消息**: タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0008)  

## 根本原因分析

通过集成测试发现，问题出现在任务名称生成逻辑中：

1. **任务名称格式**: `{服务器名称}-{任务类型名称}-{时间戳}`
2. **问题所在**: 任务类型名称使用了日文字符 `管理項目定義のエクスポート`
3. **影响范围**: 该任务名称被用作 Azure Storage 的资源名称，但 Azure Storage 不支持日文字符

**具体示例**:
```
任务名称: test-export-server-1753935678353-管理項目定義のエクスポート-20250731132120
```

## 重现步骤

1. 登录系统
2. 访问服务器列表页面
3. 选择一个服务器
4. 点击"管理项目导出"按钮
5. 观察任务状态变化

**预期结果**: 任务状态应该从 `QUEUED` → `RUNNING` → `COMPLETED_SUCCESS`  
**实际结果**: 任务状态直接从 `QUEUED` → `COMPLETED_ERROR`

## 测试环境

- **测试框架**: Playwright 集成测试
- **Azure Functions**: 标准服务 (端口7072) + 长时运行服务 (端口7071)
- **Mock Server**: Azure Automation Mock Server (端口3001)
- **数据库**: SQL Server (测试环境)
- **存储**: Azurite (Azure Storage 模拟器)

## 影响评估

**功能影响**:
- 管理项目导出功能完全无法使用
- 用户无法导出管理项目定义数据

**业务影响**:
- 影响用户的日常运维工作
- 可能导致数据备份和迁移困难

## 相关代码位置

**主要文件**:
- `apps/jcs-endpoint-nextjs/app/lib/actions/tasks.ts` (第224行)
- 任务名称生成逻辑: `const taskName = \`\${serverDetails.name}-\${taskTypeName}-\${ymdhms}\`;`

**相关组件**:
- Azure Functions 任务处理逻辑
- Azure Storage 集成
- LOV 数据管理

## 建议修复方案

1. **短期方案**: 修改任务名称生成逻辑，使用英文字符替代日文字符
2. **长期方案**: 建立统一的命名规范，确保所有外部系统集成都使用兼容的字符集

## 测试验证

集成测试已经能够稳定重现此问题，修复后可以通过以下测试验证：

```bash
npx playwright test final-export-test.spec.ts --grep "应该完成完整的管理项目导出流程"
```

## 附加信息

**相关日志**:
```
RequestId:85d2785a-b01a-000d-50d2-016169000000
Time:2025-07-31T04:21:23.2703282Z
```

**测试任务ID**: `8b61a8df-5b9c-4b32-a039-52e7f784455a`

---

**状态**: 待修复  
**分配给**: 开发团队  
**预计修复时间**: 1-2个工作日