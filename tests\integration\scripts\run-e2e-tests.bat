@echo off
REM 🚀 E2E 测试一键运行脚本 (Windows 版本)
REM 
REM 本脚本采用 Playwright webServer 最佳实践，自动化管理所有服务：
REM - Next.js 前端应用（生产模式）
REM - Azure Functions 标准服务
REM - Azure Functions 长时运行服务
REM 
REM 使用方法：
REM   scripts\run-e2e-tests.bat                    # 运行所有测试
REM   scripts\run-e2e-tests.bat server-list        # 运行特定测试
REM   scripts\run-e2e-tests.bat --headed           # 有头模式运行
REM   scripts\run-e2e-tests.bat --debug            # 调试模式运行

setlocal enabledelayedexpansion

REM 颜色定义（Windows 不支持颜色，使用简单文本）
set "INFO_PREFIX=ℹ️ "
set "SUCCESS_PREFIX=✅ "
set "WARNING_PREFIX=⚠️ "
set "ERROR_PREFIX=❌ "

REM 日志函数
:log_info
echo %INFO_PREFIX%%~1
goto :eof

:log_success
echo %SUCCESS_PREFIX%%~1
goto :eof

:log_warning
echo %WARNING_PREFIX%%~1
goto :eof

:log_error
echo %ERROR_PREFIX%%~1
goto :eof

REM 检查必要的工具
:check_prerequisites
call :log_info "检查必要的工具..."

REM 检查 Node.js
node --version >nul 2>&1
if errorlevel 1 (
    call :log_error "Node.js 未安装。请安装 Node.js 18+ 版本。"
    exit /b 1
)

REM 检查 npm
npm --version >nul 2>&1
if errorlevel 1 (
    call :log_error "npm 未安装。请安装 npm。"
    exit /b 1
)

REM 检查 Azure Functions Core Tools
func --version >nul 2>&1
if errorlevel 1 (
    call :log_warning "Azure Functions Core Tools 未安装。"
    call :log_warning "请运行: npm install -g azure-functions-core-tools@4 --unsafe-perm true"
    call :log_warning "Azure Functions 测试将被跳过。"
    set SKIP_AZURE_FUNCTIONS=true
)

call :log_success "工具检查完成"
goto :eof

REM 安装依赖
:install_dependencies
call :log_info "安装测试依赖..."

REM 安装集成测试依赖
if not exist "node_modules" (
    npm install
)

REM 安装 Next.js 应用依赖
if not exist "..\..\apps\jcs-endpoint-nextjs\node_modules" (
    call :log_info "安装 Next.js 应用依赖..."
    cd ..\..\apps\jcs-endpoint-nextjs
    npm install
    cd ..\..\tests\integration
)

REM 安装 Azure Functions 依赖
if not "%SKIP_AZURE_FUNCTIONS%"=="true" (
    if not exist "..\..\apps\jcs-backend-services-standard\node_modules" (
        call :log_info "安装标准 Azure Functions 依赖..."
        cd ..\..\apps\jcs-backend-services-standard
        npm install
        cd ..\..\tests\integration
    )
    
    if not exist "..\..\apps\jcs-backend-services-long-running\node_modules" (
        call :log_info "安装长时运行 Azure Functions 依赖..."
        cd ..\..\apps\jcs-backend-services-long-running
        npm install
        cd ..\..\tests\integration
    )
)

call :log_success "依赖安装完成"
goto :eof

REM 环境验证
:validate_environment
call :log_info "验证测试环境..."

REM 检查环境变量文件
if not exist "..\..\apps\jcs-endpoint-nextjs\.env.test.local" (
    call :log_error "缺少 .env.test.local 文件。请确保测试环境配置正确。"
    exit /b 1
)

REM 运行环境验证脚本
if exist "validate-config.js" (
    node validate-config.js
    if errorlevel 1 (
        call :log_error "环境验证失败"
        exit /b 1
    )
)

call :log_success "环境验证完成"
goto :eof

REM 清理旧的测试结果
:cleanup_old_results
call :log_info "清理旧的测试结果..."

REM 清理测试结果目录
if exist "..\test-results" (
    rmdir /s /q "..\test-results"
)

REM 清理 Playwright 报告
if exist "playwright-report" (
    rmdir /s /q "playwright-report"
)

REM 清理测试输出
if exist "test-results" (
    rmdir /s /q "test-results"
)

call :log_success "清理完成"
goto :eof

REM 运行测试
:run_tests
set "test_args=%*"

call :log_info "开始运行 E2E 测试..."
call :log_info "Playwright 将自动启动所有必要的服务"

REM 构建 Playwright 命令
set "playwright_cmd=npx playwright test"

REM 添加用户指定的参数
if not "%test_args%"=="" (
    set "playwright_cmd=%playwright_cmd% %test_args%"
)

call :log_info "执行命令: %playwright_cmd%"

REM 运行测试
%playwright_cmd%
if errorlevel 1 (
    call :log_error "测试运行失败"
    exit /b 1
) else (
    call :log_success "测试运行完成"
    
    REM 显示报告信息
    if exist "playwright-report" (
        call :log_info "测试报告已生成: playwright-report\index.html"
        call :log_info "运行 'npx playwright show-report' 查看详细报告"
    )
)
goto :eof

REM 主函数
:main
call :log_info "🚀 启动 E2E 测试流程"
call :log_info "========================================"

REM 确保在正确的目录
if not exist "playwright.config.ts" (
    call :log_error "请在 tests\integration 目录下运行此脚本"
    exit /b 1
)

REM 执行测试流程
call :check_prerequisites
if errorlevel 1 exit /b 1

call :install_dependencies
if errorlevel 1 exit /b 1

call :validate_environment
if errorlevel 1 exit /b 1

call :cleanup_old_results
if errorlevel 1 exit /b 1

call :run_tests %*
if errorlevel 1 exit /b 1

call :log_success "🎉 E2E 测试流程完成"
goto :eof

REM 运行主函数
call :main %*
