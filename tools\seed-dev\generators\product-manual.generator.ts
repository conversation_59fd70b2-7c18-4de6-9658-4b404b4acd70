/**
 * @fileoverview 製品マニュアルデータ生成器
 * @description 開発環境用の大量の製品マニュアルデータを生成する
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { PrismaClient } from '@prisma/client';
import { BaseGenerator } from './base.generator';

/**
 * 製品マニュアルデータ生成器クラス
 * 様々な製品の日本語マニュアルデータを大量に生成する
 */
export class ProductManualGenerator extends BaseGenerator {
  private readonly MANUAL_COUNT = 150; // 生成するマニュアル数
  private readonly BATCH_SIZE = 15;

  constructor(prisma: PrismaClient) {
    super(prisma, 'ProductManualGenerator');
  }

  /**
   * 生成器名を取得する
   * @returns 生成器名
   */
  getGeneratorName(): string {
    return '製品マニュアルデータ';
  }

  /**
   * 生成予定のデータ数を取得する
   * @returns 生成予定数
   */
  getExpectedCount(): number {
    return this.MANUAL_COUNT;
  }

  /**
   * 既存の製品マニュアルデータをクリーンアップする
   * 関連するプランマニュアルも削除する
   */
  async cleanup(): Promise<void> {
    try {
      // 関連するプランマニュアルを先に削除
      await this.prisma.planManual.deleteMany({});
      
      // 製品マニュアルデータを削除
      const deleteResult = await this.prisma.productManual.deleteMany({});
      console.log(`既存製品マニュアルデータを削除しました (${deleteResult.count}件)`);
    } catch (error) {
      console.error('製品マニュアルデータのクリーンアップに失敗しました', error);
      throw error;
    }
  }

  /**
   * 製品マニュアルデータを生成する
   * 様々な製品の日本語マニュアルを大量に生成する
   * @returns 生成されたデータの数
   */
  async generate(): Promise<number> {
    try {
      // バッチで製品マニュアルデータを生成
      return await this.generateInBatches(
        this.MANUAL_COUNT,
        this.BATCH_SIZE,
        async (startIndex: number, count: number) => {
          return await this.generateManualBatch(startIndex, count);
        }
      );
    } catch (error) {
      console.error('製品マニュアルデータ生成中にエラーが発生しました', error);
      throw error;
    }
  }

  /**
   * 製品マニュアルデータのバッチを生成する
   * @param startIndex 開始インデックス
   * @param count 生成数
   * @returns 生成された製品マニュアルデータ
   */
  private async generateManualBatch(
    startIndex: number,
    count: number
  ): Promise<any[]> {
    const manuals = [];

    for (let i = 0; i < count; i++) {
      const manualIndex = startIndex + i + 1;
      const serialNo = `MAN-${manualIndex.toString().padStart(4, '0')}`;
      
      const manual = {
        name: this.generateManualName(),
        serialNo,
        fileName: this.generateFileName(serialNo),
        size: this.generateFileSize(),
      };

      manuals.push(manual);
    }

    // バッチでデータベースに挿入
    await this.prisma.productManual.createMany({
      data: manuals,
    });

    return manuals;
  }

  /**
   * マニュアル名を生成する
   * @returns 日本語のマニュアル名
   */
  private generateManualName(): string {
    const manualTypes = [
      'インストールガイド',
      'ユーザーマニュアル',
      '管理者ガイド',
      'セットアップガイド',
      'トラブルシューティングガイド',
      'リファレンスマニュアル',
      'クイックスタートガイド',
      'アップグレードガイド',
      'セキュリティガイド',
      'バックアップ・リストアガイド',
      'パフォーマンスチューニングガイド',
      'API リファレンス',
      'コマンドリファレンス',
      '設定ガイド',
      '運用ガイド'
    ];

    const productNames = [
      'JP1/統括マネージャ',
      'JP1/中継マネージャ',
      '秘文/管理コンソール',
      'JP1/ログ管理',
      'JP1/監視システム',
      'JP1/バックアップ',
      'JP1/セキュリティ管理',
      'JP1/ネットワーク管理',
      'JP1/システム運用',
      'JP1/性能管理',
      'JP1/構成管理',
      'JP1/変更管理',
      'JP1/問題管理',
      'JP1/資産管理',
      'JP1/パッチ管理'
    ];

    const versions = ['v1.0', 'v2.0', 'v2.1', 'v3.0', 'v3.1', 'v4.0'];
    
    const productName = this.faker.randomFromArray(productNames);
    const manualType = this.faker.randomFromArray(manualTypes);
    const version = this.faker.randomFromArray(versions);
    
    return `${productName} ${manualType} ${version}`;
  }

  /**
   * ファイル名を生成する
   * @param serialNo シリアル番号
   * @returns ファイル名
   */
  private generateFileName(serialNo: string): string {
    const extensions = ['pdf', 'docx', 'html'];
    const extension = this.faker.randomFromArray(extensions);
    
    // 日本語ファイル名の場合もある
    if (this.faker.randomBoolean(0.3)) {
      const jpNames = [
        'インストール手順書',
        'ユーザー操作マニュアル',
        '管理者設定ガイド',
        'セットアップ手順',
        'トラブル対応手順書',
        'リファレンス資料',
        'クイックガイド',
        'アップグレード手順書',
        'セキュリティ設定ガイド',
        'バックアップ手順書'
      ];
      const jpName = this.faker.randomFromArray(jpNames);
      return `${jpName}_${serialNo}.${extension}`;
    } else {
      const enNames = [
        'installation_guide',
        'user_manual',
        'admin_guide',
        'setup_guide',
        'troubleshooting',
        'reference_manual',
        'quick_start',
        'upgrade_guide',
        'security_guide',
        'backup_guide'
      ];
      const enName = this.faker.randomFromArray(enNames);
      return `${enName}_${serialNo}.${extension}`;
    }
  }

  /**
   * ファイルサイズを生成する（バイト単位）
   * @returns ファイルサイズ
   */
  private generateFileSize(): number {
    // 1MB から 50MB の範囲でランダム生成
    const minSize = 1024 * 1024; // 1MB
    const maxSize = 50 * 1024 * 1024; // 50MB
    return this.faker.randomInt(minSize, maxSize);
  }

  /**
   * 製品マニュアルの統計情報を取得する
   * @returns 統計情報
   */
  async getStatistics(): Promise<{
    totalCount: number;
    totalSize: number;
    averageSize: number;
    byExtension: Record<string, number>;
    largestManual: any;
    smallestManual: any;
  }> {
    const [
      totalCount,
      manuals,
      largestManual,
      smallestManual,
    ] = await Promise.all([
      this.prisma.productManual.count(),
      this.prisma.productManual.findMany({
        select: {
          fileName: true,
          size: true,
        },
      }),
      this.prisma.productManual.findFirst({
        orderBy: { size: 'desc' },
        select: { name: true, size: true, fileName: true },
      }),
      this.prisma.productManual.findFirst({
        orderBy: { size: 'asc' },
        select: { name: true, size: true, fileName: true },
      }),
    ]);

    // 拡張子別の集計
    const byExtension: Record<string, number> = {};
    let totalSize = 0;

    manuals.forEach(manual => {
      totalSize += manual.size;
      const extension = manual.fileName.split('.').pop()?.toLowerCase() || 'unknown';
      byExtension[extension] = (byExtension[extension] || 0) + 1;
    });

    const averageSize = totalCount > 0 ? Math.round(totalSize / totalCount) : 0;

    return {
      totalCount,
      totalSize,
      averageSize,
      byExtension,
      largestManual,
      smallestManual,
    };
  }

  /**
   * 配列からランダムな要素を選択する
   * @param array 選択元の配列
   * @returns ランダムに選択された要素
   */
  private randomFromArray<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }
}
