## タスク中止タイムアウト処理関数 (TaskCancellationTimeoutFunc) 詳細設計

### 概要

#### 責務
`TaskCancellationTimeoutFunc` は、Azure Service Bus の `TaskControlQueue` に紐づくデッドレターキュー（DLQ）から、タスク中止関数`TaskCancellationFunc` の実行タイムアウトや例外の発生により
正常に処理されなかったタスク中止要求メッセージを受信し処理する Azure Functionsの関数 である。本関数の主な責務は以下の通りである。

1.  DLQから受信したメッセージを解析し、対象のタスクID (`taskId`)を取得する。
2.  データベースの `Task` テーブルから、取得した `taskId` に該当するタスクの現在のステータス (`status`) を確認する。
3.  タスクのステータスを判定し、PENDING_CANCELLATION以外の場合はログに記録して、処理を終了する。
4.  対象タスクレコードのステータスをCOMPLETED_ERRORに、タスク詳細を中止タイムアウトのメッセージに更新する。"

#### トリガー
Azure Service Bus - `TaskControlQueue` のデッドレターキュー（DLQ）メッセージ。

#### 主要入力
*   TaskControlQueue/$DeadLetterQueue` から受信するJSON形式のメッセージ。メッセージボディの構造は、元の `TaskControlQueue` メッセージと同様である。
"{
  ""taskId"": ""string (UUID)""
}"


*   Azure SQL Database のTaskテーブルからの読み取り。

#### 主要出力
Azure SQL Database のTaskテーブルのレコード更新（status, resultMessage, errorCodeフィールド）。

※DLQの名称は、TaskControlQueueの名称（環境変数 SERVICE_BUS_TASK_CONTROL_QUEUE_NAME で指定）に接尾辞 /$DeadLetterQueue を付加したものとなる。
※データベース接続には環境変数 `MSSQL_PRISMA_URL` で指定される接続文字列が使用される。

### 主要処理フロー

```mermaid
graph TD
    A["開始: TaskControlQueueの<br/>DLQメッセージ受信"] --> B["メッセージ解析・<br/>taskId取得"];
    B -- "不足/不正" --> BA["エラーログ記録"];
    B -- "正常" --> C["DBよりタスク情報取得<br/>(taskId使用)"];
    C -- "取得失敗/taskレコードなし" --> BA;
    C -- "Task取得成功" --> D{"タスクの現在のステータス<br/>(Task.status) "};
    D -- "PENDING_CANCELLATION以外" --> BA;
    D -- "PENDING_CANCELLATION" --> E["タスク情報をCOMPLETED_ERROR、<br/>EMET0006に更新"];
    
    E -- "DB更新成功" --> J["成功ログ記録"];
    E -- "DB更新失敗" --> BA;

    BA --> Z["終了"];
    J --> Z;
```

#### 処理フロー詳細

1.  Azure Service Bus の TaskControlQueue/$DeadLetterQueue から元のタスク中止要求メッセージを受信し、JSON形式から解析する。
    受信したメッセージ内容（特に `taskId`）をログに出力する。
2.  メッセージ内の `taskId` の存在とフォーマットを確認する。
    *   確認エラーの場合：エラー詳細をログに記録した後、処理を終了する。
3.  `taskId` を使用して、データベースの `Task` テーブルから該当タスクのstatus（ステータス）、updatedAt（最終更新日時） を取得する。
    *   取得失敗、またはタスクレコードが存在しない場合：エラー詳細をログに記録した後、処理を終了する。
4.  取得したタスクの現在の status に基づき、以下の分岐処理を行う。
    *   **`PENDING_CANCELLATION`の場合**:
        タスクID = 入力パラメータのtaskId、最終更新日時 = ステップ3.で取得した最終更新日時の条件で、該当 Task レコードの status（ステータス） を COMPLETED_ERROR に、resultMessage（タスク詳細）を
        EMET0006「タスクの中止がタイムアウトしました。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMET0006)」に、
        errorCode（エラーコード）をEMET0006に更新する。
        DB更新失敗/更新件数が0件の場合：エラー詳細をログに記録した後、処理を終了する。
    *   **`PENDING_CANCELLATION`以外の場合**:
        エラー詳細をログに記録した後、処理を終了する。
5.  処理成功のログを記録し、本関数の処理を終了する。

### 主要なデータ及び外部サービスとの対話詳細

#### Azure Service Bus (`TaskControlQueue/$DeadLetterQueue`) からのメッセージ受信
*   本Functionは `TaskControlQueue/$DeadLetterQueue` のメッセージをトリガーとして起動する。
*   メッセージはJSON形式であり、その主な構造は本章「主要入力」セクションを参照。
*   DLQの最大配信数は1に設定されているため、タスク中止タイムアウト関数は一回だけ起動される。タスク中止タイムアウト関数が処理成功した場合メッセージはACKされキューから削除される。タスク中止タイムアウト関数が例外やタイムアウトにより処理失敗した場合メッセージはDLQに残り続けて、システム管理者による手動削除が必要となる。

#### データベース (`Task` テーブル) との対話
*   **読み取り**: `taskId` をキーとして `Task` テーブルからレコードを一件取得する。
*   **更新**: 該当レコードの status, resultMessage, errorCode フィールドを更新する。

### エラー処理メカニズム

| エラーシナリオ | 関連エラーコード | 対処・補償ロジック |
|:---|:---|:---|
| メッセージ基本パラメータ確認エラー (`taskId`不足/不正) | - | エラーログ記録。処理終了。 |
| 対象タスク情報 (`Task`レコード) がデータベースに存在しない/DB読み取り失敗 | - | エラーログ記録。処理終了。 |
| タスクステータスがPENDING_CANCELLATION以外 | - | エラーログ記録。処理終了。 |
| DB更新失敗/更新件数が0件 | - | エラーログ記録。処理終了。 |
| 予期せぬ内部エラー | - | エラーログ記録。処理終了。 |

*   **タイムアウトについて**
    Azure Functionsがタスク中止タイムアウト関数の実行時間を監視し、host.jsonのfunctionTimeoutプロパティで設定されたタイムアウト時間（5分）になっても関数がまだ終了していない場合、Azure Functionsは関数の実行を強制的に中止する。
*   **リトライについて**
    DLQの最大配信数は1に設定されているため、タスク中止タイムアウト関数は一回だけ実行される。例外やタイムアウトにより処理失敗の場合でもリトライは行わない。この場合はシステム管理者による人的確認と対処が必要となる。