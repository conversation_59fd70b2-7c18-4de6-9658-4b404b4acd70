/**
 * @file TaskExecuteTimeoutFunc.test.ts
 * @description
 * TaskExecuteTimeoutFunc の単体テストファイルです。
 * Jest を用いて、DLQ補償処理の主要な分岐、例外処理、ディレクトリ削除・DB更新の動作を検証します。
 *
 * 【設計意図】
 * - タイムアウト・失敗タスクの補償処理チェーンを自動テスト。
 * - DB/Files等の外部依存をモックし、異常系も含めて堅牢性を担保。
 * - 100%のコードカバレッジを目指し、全ての分岐・例外処理を網羅的に検証。
 *
 * 【主なテストカバレッジ】
 * 1. メッセージ検証（null、非object、taskId不足）
 * 2. Azure Automation Job検査（存在、不存在、API失敗）
 * 3. Azure Files削除（成功、失敗）
 * 4. Task取得（成功、失敗）
 * 5. Container状態更新（成功、失敗）
 * 6. Task状態判定（PENDING_CANCELLATION、CANCELLED、その他）
 * 7. Task更新（成功、失敗）
 * 8. 全体例外処理
 *
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { TaskExecuteTimeoutFunc } from "../TaskExecuteTimeoutFunc/TaskExecuteTimeoutFunc";
import { prisma } from "../lib/prisma";
import * as azureClients from "../lib/azureClients";
import * as utils from "../lib/utils";
import { AppConstants } from "../lib/constants";

// Azure Clients のモック
jest.mock("../lib/azureClients");

// Utils のモック
jest.mock("../lib/utils", () => ({
  ...jest.requireActual("../lib/utils"),
  deleteTaskWorkspaceDirectory: jest.fn(),
}));

// Prisma のモック
jest.mock("../lib/prisma", () => {
  const { mockDeep } = require("jest-mock-extended");
  return { prisma: mockDeep() };
});

// Azure Storage File Share のモック
const shareClientMock = {
  getDirectoryClient: jest.fn().mockReturnThis(),
  deleteIfExists: jest.fn().mockResolvedValue(undefined),
  path: '/mockpath',
};

jest.mock('@azure/storage-file-share', () => ({
  ShareServiceClient: Object.assign(
    jest.fn(() => ({ getShareClient: jest.fn(() => shareClientMock) })),
    {
      fromConnectionString: jest.fn(() => ({ getShareClient: jest.fn(() => shareClientMock) }))
    }
  ),
  ShareDirectoryClient: jest.fn(() => shareClientMock),
}));

/**
 * @fileoverview TaskExecuteTimeoutFunc関数の単体テスト。
 * @description Azure FunctionsのTaskExecuteTimeoutFuncの主要分岐・例外・補償処理を網羅的に検証する。
 * 試験観点：DLQ補償処理の正常系・異常系・補償分岐網羅性、外部依存のモックによる堅牢性検証。
 * 試験対象：TaskExecuteTimeoutFunc.ts（TaskExecuteTimeoutFunc Azure Function本体）。
 */
describe("TaskExecuteTimeoutFunc 単体テスト", () => {
  let context: any;

  beforeEach(() => {
    /**
     * Azure Functions の InvocationContext をモックし、必須フィールドを全て補完
     */
    context = {
      invocationId: "test-invoke",
      functionName: "TaskExecuteTimeoutFunc",
      extraInputs: [],
      extraOutputs: [],
      bindingData: {},
      traceContext: {},
      log: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
    };

    jest.clearAllMocks();

    // Azure Clients のファクトリー関数をモック
    (azureClients.createShareServiceClient as any).mockReturnValue({
      getShareClient: jest.fn().mockReturnValue(shareClientMock)
    });

    // deleteTaskWorkspaceDirectory のデフォルトモック
    (utils.deleteTaskWorkspaceDirectory as jest.Mock).mockResolvedValue(undefined);
  });

  /**
   * 試験観点：メッセージnull分岐の検証。
   * 試験対象：TaskExecuteTimeoutFuncのメッセージ検証分岐。
   * 試験手順：
   * 1. messageがnullの場合をテスト。
   * 確認項目：
   * - context.errorに"メッセージが不正"が出力されること。
   * - getAutomationJobStatusが呼ばれないこと。
   */
  it("メッセージがnull: 処理終了", async () => {
    await TaskExecuteTimeoutFunc(null, context);
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("メッセージが不正"));
    expect(azureClients.getAutomationJobStatus).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：メッセージ型検証分岐の検証。
   * 試験対象：TaskExecuteTimeoutFuncのメッセージ検証分岐。
   * 試験手順：
   * 1. messageが文字列（非object）の場合をテスト。
   * 確認項目：
   * - context.errorに"メッセージが不正"が出力されること。
   * - getAutomationJobStatusが呼ばれないこと。
   */
  it("メッセージが非object: 処理終了", async () => {
    await TaskExecuteTimeoutFunc("invalid message", context);
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("メッセージが不正"));
    expect(azureClients.getAutomationJobStatus).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：taskId空文字列分岐の検証。
   * 試験対象：TaskExecuteTimeoutFuncのtaskId検証分岐。
   * 試験手順：
   * 1. taskIdが空文字列のメッセージを渡す。
   * 確認項目：
   * - context.errorに"taskIdが不足/不正"が出力されること。
   * - getAutomationJobStatusが呼ばれないこと。
   */
  it("taskIdが空文字列: 処理終了", async () => {
    await TaskExecuteTimeoutFunc({ taskId: "" }, context);
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("taskIdが不足/不正"));
    expect(azureClients.getAutomationJobStatus).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：taskId型検証分岐の検証。
   * 試験対象：TaskExecuteTimeoutFuncのtaskId検証分岐。
   * 試験手順：
   * 1. taskIdが数値のメッセージを渡す。
   * 確認項目：
   * - context.errorに"taskIdが不足/不正"が出力されること。
   * - getAutomationJobStatusが呼ばれないこと。
   */
  it("taskIdが非文字列: 処理終了", async () => {
    await TaskExecuteTimeoutFunc({ taskId: 123 }, context);
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("taskIdが不足/不正"));
    expect(azureClients.getAutomationJobStatus).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：Job存在時の早期終了分岐の検証。
   * 試験対象：TaskExecuteTimeoutFuncのJob存在確認分岐。
   * 試験手順：
   * 1. Azure Automation Jobが存在する場合をテスト。
   * 確認項目：
   * - context.logに"補償処理は不要"が出力されること。
   * - prisma.task.findUniqueが呼ばれないこと。
   */
  it("Azure Automation Job存在: 補償不要で終了", async () => {
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: true,
      statusCode: 200,
      jobData: { id: "job1" }
    });

    await TaskExecuteTimeoutFunc({ taskId: "task1" }, context);

    expect(azureClients.getAutomationJobStatus).toHaveBeenCalledWith("task1");
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("補償処理は不要"));
    expect(prisma.task.findUnique).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：API失敗時の早期終了分岐の検証。
   * 試験対象：TaskExecuteTimeoutFuncのAPI失敗分岐。
   * 試験手順：
   * 1. Azure Automation API呼び出しが失敗する場合をテスト。
   * 確認項目：
   * - context.errorに"API呼び出し失敗"が出力されること。
   * - prisma.task.findUniqueが呼ばれないこと。
   */
  it("Azure Automation API失敗: 処理終了", async () => {
    (azureClients.getAutomationJobStatus as jest.Mock).mockRejectedValue(
      new Error("API呼び出し失敗")
    );

    await TaskExecuteTimeoutFunc({ taskId: "task1" }, context);

    expect(azureClients.getAutomationJobStatus).toHaveBeenCalledWith("task1");
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("API呼び出し失敗"));
    expect(prisma.task.findUnique).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：Files削除失敗時の処理継続分岐の検証。
   * 試験対象：TaskExecuteTimeoutFuncのFiles削除失敗分岐。
   * 試験手順：
   * 1. Azure Files削除が失敗する場合をテスト。
   * 2. 他の処理は正常に実行される。
   * 確認項目：
   * - context.logに"削除失敗"が出力されること。
   * - 後続処理が継続されること。
   */
  it("Azure Files削除失敗: 処理継続", async () => {
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: false,
      statusCode: 404
    });
    (utils.deleteTaskWorkspaceDirectory as jest.Mock).mockRejectedValue(
      new Error("削除失敗")
    );
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.Queued,
      updatedAt: new Date(),
      targetVmName: "vm1",
      targetContainerName: "container1"
    });
    (prisma.containerConcurrencyStatus.updateMany as any).mockResolvedValue({ count: 1 });
    (prisma.task.updateMany as any).mockResolvedValue({ count: 1 });

    await TaskExecuteTimeoutFunc({ taskId: "task1" }, context);

    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("削除失敗"));
    expect(prisma.task.findUnique).toHaveBeenCalled();
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("正常に完了"));
  });

  /**
   * 試験観点：Task不存在時の早期終了分岐の検証。
   * 試験対象：TaskExecuteTimeoutFuncのTask取得失敗分岐。
   * 試験手順：
   * 1. Taskが存在しない場合をテスト。
   * 確認項目：
   * - context.errorに"存在しません"が出力されること。
   * - prisma.containerConcurrencyStatus.updateManyが呼ばれないこと。
   */
  it("Task不存在: 処理終了", async () => {
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: false,
      statusCode: 404
    });
    (prisma.task.findUnique as any).mockResolvedValue(null);

    await TaskExecuteTimeoutFunc({ taskId: "notfound" }, context);

    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("存在しません"));
    expect(prisma.containerConcurrencyStatus.updateMany).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：Container更新0件時の処理継続分岐の検証。
   * 試験対象：TaskExecuteTimeoutFuncのContainer更新分岐。
   * 試験手順：
   * 1. Container状態更新が0件の場合をテスト。
   * 確認項目：
   * - context.logに"更新件数0件"が出力されること。
   * - 後続処理が継続されること。
   */
  it("Container状態更新0件: 処理継続", async () => {
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: false,
      statusCode: 404
    });
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.Queued,
      updatedAt: new Date(),
      targetVmName: "vm1",
      targetContainerName: "container1"
    });
    (prisma.containerConcurrencyStatus.updateMany as any).mockResolvedValue({ count: 0 });
    (prisma.task.updateMany as any).mockResolvedValue({ count: 1 });

    await TaskExecuteTimeoutFunc({ taskId: "task1" }, context);

    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("更新件数0件"));
    expect(prisma.task.updateMany).toHaveBeenCalled();
  });

  /**
   * 試験観点：Container更新失敗時の処理継続分岐の検証。
   * 試験対象：TaskExecuteTimeoutFuncのContainer更新失敗分岐。
   * 試験手順：
   * 1. Container状態更新が失敗する場合をテスト。
   * 確認項目：
   * - context.logに"更新失敗"が出力されること。
   * - 後続処理が継続されること。
   */
  it("Container状態更新失敗: 処理継続", async () => {
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: false,
      statusCode: 404
    });
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.Queued,
      updatedAt: new Date(),
      targetVmName: "vm1",
      targetContainerName: "container1"
    });
    (prisma.containerConcurrencyStatus.updateMany as any).mockRejectedValue(
      new Error("Container更新失敗")
    );
    (prisma.task.updateMany as any).mockResolvedValue({ count: 1 });

    await TaskExecuteTimeoutFunc({ taskId: "task1" }, context);

    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("更新失敗"));
    expect(prisma.task.updateMany).toHaveBeenCalled();
  });

  /**
   * 試験観点：PENDING_CANCELLATION状態時のTask更新スキップ分岐の検証。
   * 試験対象：TaskExecuteTimeoutFuncのTask状態判定分岐。
   * 試験手順：
   * 1. TaskのstatusがPENDING_CANCELLATIONの場合をテスト。
   * 確認項目：
   * - context.logに"EMET0005エラー更新をスキップ"が出力されること。
   * - prisma.task.updateManyが呼ばれないこと（Task更新がスキップされる）。
   */
  it("TaskステータスPENDING_CANCELLATION: Task更新スキップ", async () => {
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: false,
      statusCode: 404
    });
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.PendingCancellation,
      updatedAt: new Date(),
      targetVmName: "vm1",
      targetContainerName: "container1"
    });
    (prisma.containerConcurrencyStatus.updateMany as any).mockResolvedValue({ count: 1 });

    await TaskExecuteTimeoutFunc({ taskId: "task1" }, context);

    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("EMET0005エラー更新をスキップ"));
    expect(prisma.task.updateMany).toHaveBeenCalledTimes(0); // Container更新のみ、Task更新はスキップ
  });

  /**
   * 試験観点：CANCELLED状態時のTask更新スキップ分岐の検証。
   * 試験対象：TaskExecuteTimeoutFuncのTask状態判定分岐。
   * 試験手順：
   * 1. TaskのstatusがCANCELLEDの場合をテスト。
   * 確認項目：
   * - context.logに"EMET0005エラー更新をスキップ"が出力されること。
   * - prisma.task.updateManyが呼ばれないこと（Task更新がスキップされる）。
   */
  it("TaskステータスCANCELLED: Task更新スキップ", async () => {
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: false,
      statusCode: 404
    });
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.Cancelled,
      updatedAt: new Date(),
      targetVmName: "vm1",
      targetContainerName: "container1"
    });
    (prisma.containerConcurrencyStatus.updateMany as any).mockResolvedValue({ count: 1 });

    await TaskExecuteTimeoutFunc({ taskId: "task1" }, context);

    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("EMET0005エラー更新をスキップ"));
    expect(prisma.task.updateMany).toHaveBeenCalledTimes(0); // Container更新のみ、Task更新はスキップ
  });

  /**
   * 試験観点：Task更新0件時の処理継続分岐の検証。
   * 試験対象：TaskExecuteTimeoutFuncのTask更新分岐。
   * 試験手順：
   * 1. Task更新が0件の場合をテスト。
   * 確認項目：
   * - context.logに"更新件数0件"が出力されること。
   * - 処理が正常に完了すること。
   */
  it("Task更新0件: 処理継続", async () => {
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: false,
      statusCode: 404
    });
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.Queued,
      updatedAt: new Date(),
      targetVmName: "vm1",
      targetContainerName: "container1"
    });
    (prisma.containerConcurrencyStatus.updateMany as any).mockResolvedValue({ count: 1 });
    (prisma.task.updateMany as any).mockResolvedValue({ count: 0 });

    await TaskExecuteTimeoutFunc({ taskId: "task1" }, context);

    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("更新件数0件"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("正常に完了"));
  });

  /**
   * 試験観点：Task更新失敗時の処理継続分岐の検証。
   * 試験対象：TaskExecuteTimeoutFuncのTask更新失敗分岐。
   * 試験手順：
   * 1. Task更新が失敗する場合をテスト。
   * 確認項目：
   * - context.logに"更新失敗"が出力されること。
   * - 処理が正常に完了すること。
   */
  it("Task更新失敗: 処理継続", async () => {
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: false,
      statusCode: 404
    });
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.Queued,
      updatedAt: new Date(),
      targetVmName: "vm1",
      targetContainerName: "container1"
    });
    (prisma.containerConcurrencyStatus.updateMany as any).mockResolvedValue({ count: 1 });
    (prisma.task.updateMany as any).mockRejectedValue(new Error("Task更新失敗"));

    await TaskExecuteTimeoutFunc({ taskId: "task1" }, context);

    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("更新失敗"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("正常に完了"));
  });

  /**
   * 試験観点：正常系フローの網羅的検証。
   * 試験対象：TaskExecuteTimeoutFuncの正常系フロー。
   * 試験手順：
   * 1. 正常系の完全なフローをテスト。
   * 確認項目：
   * - 全ての処理が正常に実行されること。
   * - 適切なログが出力されること。
   */
  it("正常系: 完全フロー", async () => {
    const testDate = new Date();
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: false,
      statusCode: 404
    });
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.Queued,
      updatedAt: testDate,
      targetVmName: "vm1",
      targetContainerName: "container1"
    });
    (prisma.containerConcurrencyStatus.updateMany as any).mockResolvedValue({ count: 1 });
    (prisma.task.updateMany as any).mockResolvedValue({ count: 1 });

    await TaskExecuteTimeoutFunc({ taskId: "task1" }, context);

    // 各ステップの確認
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("メッセージ受信"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("受信taskId: task1"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("補償処理を開始"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("削除成功"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("タスク情報取得成功"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("更新成功"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("正常に完了"));

    // API呼び出しの確認
    expect(azureClients.getAutomationJobStatus).toHaveBeenCalledWith("task1");
    expect(prisma.task.findUnique).toHaveBeenCalledWith({ where: { id: "task1" } });
    expect(prisma.containerConcurrencyStatus.updateMany).toHaveBeenCalledWith({
      where: {
        targetVmName: "vm1",
        targetContainerName: "container1",
        status: "BUSY",
        currentTaskId: "task1",
      },
      data: {
        status: "IDLE",
        currentTaskId: null,
      },
    });
    expect(prisma.task.updateMany).toHaveBeenCalledWith({
      where: {
        id: "task1",
        updatedAt: testDate
      },
      data: {
        status: AppConstants.TaskStatus.CompletedError,
        errorCode: AppConstants.ERROR_CODES.EMET0005,
        resultMessage: AppConstants.TASK_ERROR_MESSAGE.EMET0005,
      },
    });
  });

  /**
   * 試験観点：全体例外処理分岐の検証。
   * 試験対象：TaskExecuteTimeoutFuncの全体例外処理。
   * 試験手順：
   * 1. 予期せぬ例外が発生する場合をテスト。
   * 確認項目：
   * - context.errorに"予期せぬ内部エラー"が出力されること。
   * - 処理が正常に終了すること。
   */
  it("予期せぬ例外: エラーログ記録", async () => {
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: false,
      statusCode: 404
    });
    (prisma.task.findUnique as any).mockImplementation(() => {
      throw new Error("予期せぬ例外");
    });

    await TaskExecuteTimeoutFunc({ taskId: "task1" }, context);

    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("予期せぬ内部エラー"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("処理を終了"));
  });

  /**
   * 試験観点：taskId未設定時の例外処理分岐の検証。
   * 試験対象：TaskExecuteTimeoutFuncの例外処理でのtaskId分岐。
   * 試験手順：
   * 1. taskIdが設定される前に例外が発生する場合をテスト。
   * 確認項目：
   * - context.logに"unknown"が出力されること。
   */
  it("taskId未設定時の例外: unknownログ記録", async () => {
    // メッセージ解析の段階で例外を発生させる
    const invalidMessage = {};
    Object.defineProperty(invalidMessage, 'taskId', {
      get() {
        throw new Error("早期例外");
      }
    });

    await TaskExecuteTimeoutFunc(invalidMessage, context);

    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("予期せぬ内部エラー"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("unknown"));
  });

  /**
   * 試験観点：null値のContainer情報での分岐検証。
   * 試験対象：TaskExecuteTimeoutFuncのContainer更新分岐。
   * 試験手順：
   * 1. TaskのtargetVmNameとtargetContainerNameがnullの場合をテスト。
   * 確認項目：
   * - Container更新でundefinedが使用されること。
   */
  it("Container情報null: undefined使用", async () => {
    (azureClients.getAutomationJobStatus as jest.Mock).mockResolvedValue({
      exists: false,
      statusCode: 404
    });
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.Queued,
      updatedAt: new Date(),
      targetVmName: null,
      targetContainerName: null
    });
    (prisma.containerConcurrencyStatus.updateMany as any).mockResolvedValue({ count: 1 });
    (prisma.task.updateMany as any).mockResolvedValue({ count: 1 });

    await TaskExecuteTimeoutFunc({ taskId: "task1" }, context);

    expect(prisma.containerConcurrencyStatus.updateMany).toHaveBeenCalledWith({
      where: {
        targetVmName: undefined,
        targetContainerName: undefined,
        status: "BUSY",
        currentTaskId: "task1",
      },
      data: {
        status: "IDLE",
        currentTaskId: null,
      },
    });
  });
});