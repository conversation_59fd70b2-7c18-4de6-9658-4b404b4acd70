/**
 * @fileoverview Azurite测试
 * @description
 * 专门测试Azurite存储模拟器的启动和功能。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { test, expect } from '@playwright/test';
import { TestServicesManager } from '../support/test-services-manager';

/**
 * 试験観点：Azurite存储模拟器测试
 * 试験対象：Azurite服务启动和基本功能
 * 試験手順：
 * 1. 启动Azurite
 * 2. 验证健康检查
 * 3. 验证基本存储功能
 * 確認項目：
 * - Azurite能够正常启动
 * - 健康检查正确工作
 * - 存储服务正常响应
 */

test.describe('Azurite存储模拟器测试', () => {
  let servicesManager: TestServicesManager;

  test.afterEach(async () => {
    if (servicesManager) {
      await servicesManager.stopAllServices();
    }
  });

  /**
   * 试験観点：Azurite基本启动测试
   * 试験対象：Azurite服务启动和状态验证
   * 試験手順：
   * 1. 启动Azurite
   * 2. 验证服务状态
   * 3. 验证端口响应
   * 確認項目：
   * - 服务状态正确
   * - 端口正常监听
   * - 健康检查通过
   */
  test('应该能够启动Azurite并验证基本功能', async () => {
    console.log('🔍 测试Azurite启动');
    
    servicesManager = new TestServicesManager();
    
    // 启动Azurite
    await servicesManager.startAzurite();
    console.log('✅ Azurite启动完成');
    
    // 验证服务状态
    expect(servicesManager.isServiceRunning('azurite')).toBe(true);
    console.log('✅ Azurite服务状态验证成功');
    
    // 验证Blob服务端口
    const blobResponse = await fetch('http://127.0.0.1:10000');
    expect(blobResponse.status).toBe(400); // Azurite返回400是正常的
    console.log('✅ Blob服务端口验证成功');
    
    // 验证Queue服务端口
    const queueResponse = await fetch('http://127.0.0.1:10001');
    expect(queueResponse.status).toBe(400); // Azurite返回400是正常的
    console.log('✅ Queue服务端口验证成功');
    
    // 验证Table服务端口
    const tableResponse = await fetch('http://127.0.0.1:10002');
    expect(tableResponse.status).toBe(400); // Azurite返回400是正常的
    console.log('✅ Table服务端口验证成功');
    
    // 验证服务日志
    const logs = servicesManager.getServiceLogs('azurite');
    expect(logs.length).toBeGreaterThan(0);
    
    // 检查日志内容
    const allLogs = logs.join('\n');
    expect(allLogs).toContain('Azurite Blob service is successfully listening');
    expect(allLogs).toContain('Azurite Queue service is successfully listening');
    expect(allLogs).toContain('Azurite Table service is successfully listening');
    
    console.log(`✅ Azurite日志验证成功，日志行数: ${logs.length}`);
    console.log('🎉 Azurite测试完成！');
  });
});
