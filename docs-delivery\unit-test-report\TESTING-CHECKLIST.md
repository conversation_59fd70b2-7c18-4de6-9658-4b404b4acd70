# 测试检查清单 (Testing Checklist)

## 🎯 **快速检查清单**

### **开始测试前 (Pre-Testing)**
- [ ] 📖 **设计文档已仔细阅读**
- [ ] 🎯 **识别了业务关键路径**
- [ ] ⚠️ **识别了主要风险点**
- [ ] 📋 **制定了测试优先级**

### **编写测试用例时 (During Testing)**
- [ ] 📝 **遵循日文注释规范** (試験観点、試験対象、試験手順、確認項目)
- [ ] 🔄 **优先测试业务关键路径** (80%精力)
- [ ] 🚨 **重点测试异常处理** (15%精力)
- [ ] 🎭 **适当Mock外部依赖**
- [ ] ✅ **基于真实业务场景设计测试**

### **测试完成后 (Post-Testing)**
- [ ] 🧪 **所有测试用例通过**
- [ ] 📊 **业务关键路径覆盖率 ≥ 95%**
- [ ] 🐛 **发现的缺陷已记录**
- [ ] 📚 **测试文档已更新**

---

## 🎯 **业务关键路径检查**

### **数据一致性 (Data Consistency)**
- [ ] **楽観ロック制御**：并发更新时的数据保护
- [ ] **トランザクション制御**：事务边界和回滚逻辑
- [ ] **状態整合性**：状态转换的一致性

### **错误处理 (Error Handling)**
- [ ] **補償処理**：异常时的补偿逻辑
- [ ] **エラー分類**：不同错误类型的正确处理
- [ ] **復旧処理**：系统从错误中的恢复能力

### **外部集成 (External Integration)**
- [ ] **Azure Services**：Azure Files、Blob、Automation等
- [ ] **データベース操作**：Prisma操作的正确性
- [ ] **メッセージ処理**：Service Bus消息的解析和处理

---

## 📝 **测试用例质量检查**

### **注释规范检查**
```typescript
✅ 正确示例：
/**
 * 試験観点：楽観ロック制御の正確性検証（设计文档要件）
 * 試験対象：TaskExecuteFuncの楽観ロック制御分岐
 * 試験手順：
 * 1. Task読取後に他プロセスがupdatedAtを更新
 * 2. updateMany の count が 0 になることを確認
 * 確認項目：
 * - 楽観ロック失敗が正しく検出されること
 */

❌ 错误示例：
// Test optimistic lock
it("should handle optimistic lock", () => {
```

### **测试场景检查**
- [ ] **真实业务场景**：基于实际业务需求
- [ ] **完整流程测试**：不只是单个函数调用
- [ ] **边界条件覆盖**：正常边界和异常边界
- [ ] **并发场景考虑**：多用户、多进程场景

---

## 🚨 **常见问题避免**

### **❌ 避免的错误做法**
1. **为了覆盖率而测试**
   ```typescript
   // ❌ 错误：只为了覆盖if分支
   it("should return true when condition is true", () => {
     expect(someFunction(true)).toBe(true);
   });
   ```

2. **过度Mock业务逻辑**
   ```typescript
   // ❌ 错误：Mock了核心业务逻辑
   mockBusinessLogic.mockReturnValue("success");
   ```

3. **忽略设计文档要求**
   ```typescript
   // ❌ 错误：没有验证设计文档中的具体要求
   expect(result).toBeDefined(); // 太泛化
   ```

### **✅ 推荐的正确做法**
1. **基于业务场景测试**
   ```typescript
   // ✅ 正确：验证具体的业务逻辑
   it("楽観ロック失敗: 並行更新検出", async () => {
     // 模拟真实的并发更新场景
   });
   ```

2. **精确Mock外部依赖**
   ```typescript
   // ✅ 正确：只Mock外部依赖
   (prisma.task.updateMany as any).mockResolvedValue({ count: 0 });
   ```

3. **验证设计文档要求**
   ```typescript
   // ✅ 正确：验证设计文档中的具体条件
   expect(prisma.containerConcurrencyStatus.updateMany).toHaveBeenCalledWith({
     where: {
       targetVmName: "vm1",
       targetContainerName: "container1",
       status: "BUSY",
       currentTaskId: "task1"
     }
   });
   ```

---

## 📊 **覆盖率评估标准**

### **目标覆盖率**
| 组件类型 | 目标覆盖率 | 优先级 |
|---------|-----------|--------|
| 业务关键路径 | 95%+ | 🔴 必须 |
| 异常处理分支 | 80%+ | 🟡 重要 |
| 边界条件分支 | 60%+ | 🟢 可选 |
| 日志调试代码 | 不强制 | ⚪ 忽略 |

### **质量评估问题**
- [ ] **未覆盖的行是否为业务关键逻辑？**
- [ ] **测试是否发现了真正的业务缺陷？**
- [ ] **测试维护成本是否合理？**
- [ ] **测试是否能防止回归问题？**

---

## 🎯 **成功标准**

### **定量指标**
- [ ] 业务关键路径覆盖率 ≥ 95%
- [ ] 所有测试用例通过
- [ ] 测试执行时间 < 5分钟

### **定性指标**
- [ ] 测试发现了真实的业务缺陷
- [ ] 测试用例易于理解和维护
- [ ] 测试覆盖了设计文档中的所有关键要求
- [ ] 团队成员认为测试有价值

---

## 📚 **参考资源**

### **文档**
- [TESTING-GUIDELINES.md](./TESTING-GUIDELINES.md) - 详细测试指导原则
- [.augment-guidelines.md](../.augment-guidelines.md) - 代码规范
- 各功能设计文档 - 业务需求参考

### **示例代码**
- `__tests__/RunbookProcessorTimeoutFunc.test.ts` - 100%覆盖率示例
- `__tests__/TaskCancellationTimeoutFunc.test.ts` - 补偿处理示例
- `__tests__/lib/utils.test.ts` - 工具函数测试示例

---

## 🔄 **持续改进**

### **定期评估**
- [ ] **月度回顾**：测试发现的缺陷类型和数量
- [ ] **季度优化**：测试策略和优先级调整
- [ ] **年度总结**：测试方法论的改进

### **团队分享**
- [ ] **最佳实践分享**：成功的测试案例
- [ ] **经验教训总结**：失败的测试尝试
- [ ] **工具和方法更新**：新的测试工具和技术

---

**快速使用提示**: 
1. 开始测试前，快速过一遍"快速检查清单"
2. 编写测试时，参考"测试用例质量检查"
3. 完成后，使用"成功标准"进行自我评估

**最后更新**: 2025-01-10
