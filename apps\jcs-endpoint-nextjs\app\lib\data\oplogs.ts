import prisma from "@/app/lib/prisma";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { revalidateTag, unstable_cache } from "next/cache";
import { cookies } from "next/headers";
import {
  ENV,
  PORTAL_CACHE_KEY_OPLOGS
} from "../definitions";
import { LogFunctionSignature } from "../logger";
import { handleServerError } from "../portal-error";
import { formatBytes, formatDate } from "../utils";

/**
 * 操作ログ（OperationLog）関連のデータ操作を行う静的クラスです。
 * 操作ログのキャッシュ取得、ページ数計算、フィルタ・ソート処理を担当します。
 */
export class ServerDataOplogs {
  /**
   * 指定されたライセンスID・タイムゾーンに紐づく操作ログ一覧をキャッシュ付きで取得します。
   * キャッシュキーとタグにライセンスIDとタイムゾーンを含めることで、正確なキャッシュ無効化を実現します。
   *
   * @param {string} licenseId - ライセンスID
   * @param {string} tz - タイムゾーン
   * @returns {Promise<any[]>} 操作ログ情報配列
   */
  static async fetchCachedOplogs(licenseId: string, tz: string) {
    const cachedFn = unstable_cache(
      async () => {
        const oplogs = await prisma.operationLog.findMany({
          where: { licenseId },
        });
        return oplogs.map((oplog) => ({
          ...oplog,
          createdAt: formatDate(oplog.createdAt, tz),
          retentionAt: oplog.retentionAt ? formatDate(oplog.retentionAt, tz) : "",
          formattedSize: formatBytes(oplog.size),
        }));
      },
      [`${PORTAL_CACHE_KEY_OPLOGS}-${licenseId}-${tz}`],
      {
        revalidate: ENV.APP_CACHE_TTL_SECONDS,
        tags: [`${PORTAL_CACHE_KEY_OPLOGS}-${licenseId}-${tz}`],
      },
    );
    return await cachedFn();
  }

  /**
   * 操作ログ一覧のページ数を取得します。
   *
   * @param {string} filter - フィルタ文字列
   * @param {number} size - 1ページあたりの件数
   * @param {boolean} refresh - キャッシュ強制リフレッシュ有無
   * @returns {Promise<number>} ページ数
   */
  @LogFunctionSignature()
  static async fetchOplogsPages(filter: string, size: number, refresh: boolean) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);
      if (refresh) {
        revalidateTag(`${PORTAL_CACHE_KEY_OPLOGS}-${session!.user.licenseId}-${session!.user.tz}`);
      }
      const cachedOplogs = await this.fetchCachedOplogs(session!.user.licenseId, session!.user.tz);
      if (cachedOplogs) {
        if (filter) {
          const filteredOplogs = cachedOplogs.filter(
            (oplog) =>
              oplog.name.toLowerCase().includes(filter.toLowerCase()) ||
              oplog.createdAt.toLowerCase().includes(filter.toLowerCase()) ||
              oplog.retentionAt.toLowerCase().includes(filter.toLowerCase()) ||
              oplog.formattedSize.toLowerCase().includes(filter.toLowerCase()),
          );
          return Math.ceil(Number(filteredOplogs.length) / size);
        } else {
          return Math.ceil(Number(cachedOplogs.length) / size);
        }
      } else {
        return 0;
      }
    } catch (error) {
      handleServerError(error);
    }
  }

  /**
   * フィルタ・ソート・ページング済みの操作ログ一覧を取得します。
   *
   * @param {string} filter - フィルタ文字列
   * @param {number} size - 1ページあたりの件数
   * @param {number} page - ページ番号
   * @param {"name"|"createdAt"|"retentionAt"|"size"} sort - ソートキー
   * @param {"asc"|"desc"} order - ソート順
   * @returns {Promise<any[]>} 操作ログ情報配列
   */
  @LogFunctionSignature()
  static async fetchFilteredOplogs(
    filter: string,
    size: number,
    page: number,
    sort: "name" | "createdAt" | "retentionAt" | "size",
    order: "asc" | "desc",
  ) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);
      const cachedOplogs = await this.fetchCachedOplogs(session!.user.licenseId, session!.user.tz);
      if (cachedOplogs) {
        let filteredOplogs = cachedOplogs;
        if (filter) {
          filteredOplogs = cachedOplogs.filter(
            (oplog) =>
              oplog.name.toLowerCase().includes(filter.toLowerCase()) ||
              oplog.createdAt.toLowerCase().includes(filter.toLowerCase()) ||
              oplog.retentionAt.toLowerCase().includes(filter.toLowerCase()) ||
              oplog.formattedSize.toLowerCase().includes(filter.toLowerCase()),
          );
        }
        if (sort) {
          filteredOplogs.sort((a, b) => {
            if (sort === "size") {
              if (order === "asc") {
                return a[sort] - b[sort];
              } else {
                return b[sort] - a[sort];
              }
            } else {
              const aValue = a[sort].toLowerCase();
              const bValue = b[sort].toLowerCase();
              if (order === "asc") {
                return aValue.localeCompare(bValue);
              } else {
                return bValue.localeCompare(aValue);
              }
            }
          });
        }
        const startIndex = (page - 1) * size;
        const endIndex = startIndex + size;
        const paginatedOplogs = filteredOplogs.slice(startIndex, endIndex);
        return paginatedOplogs;
      } else {
        return [];
      }
    } catch (error) {
      handleServerError(error);
    }
  }
} 