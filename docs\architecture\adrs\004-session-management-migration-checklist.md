# 会话管理架构重构 - 迁移检查清单（next-auth 模式）

## 概述

本检查清单用于实施基于 next-auth 设计模式的会话管理架构重构，保留 iron-session 业务逻辑，实现与 next-auth 相同的开发体验。

## 实施前检查

### ✅ 环境确认
- [ ] 确认当前系统使用RefreshToken组件（12个位置）
- [ ] 确认现有API端点正常工作：`/api/refreshToken`、`/api/logout`
- [ ] 确认iron-session和Keycloak集成正常
- [ ] 确认JWT_MAX_AGE_SECONDS配置（默认1800秒）

### ✅ 备份准备
- [ ] 备份所有相关文件
- [ ] 确认Git版本控制状态良好
- [ ] 准备回滚计划

## 阶段1：应急优化（1-2天）

### ✅ 快速优化RefreshToken组件

**目标**：立即减少90%的API调用，为架构重构争取时间

**实施步骤**：
- [ ] 修改`app/ui/refreshToken.tsx`添加时间检查
- [ ] 测试API调用频率确实降低
- [ ] 确认所有现有功能正常
- [ ] 部署到生产环境并监控

## 阶段2：next-auth 模式架构重构（2-3周）

### ✅ 第1步：创建核心组件（1周）

**创建SessionProvider**：
- [ ] 创建`app/lib/auth/SessionProvider.tsx`
- [ ] 实现自动会话检查和刷新逻辑
- [ ] 实现跨标签页同步机制
- [ ] 创建useSession Hook

**创建会话检查API**：
- [ ] 创建`app/api/session/route.ts`
- [ ] 实现会话状态检查逻辑
- [ ] 测试API端点正常工作

**集成到根布局**：
- [ ] 修改`app/layout.tsx`添加SessionProvider
- [ ] 测试全局会话状态管理

### ✅ 第2步：页面迁移（1周）

**试点迁移（servers页面）**：
- [ ] 修改`app/dashboard/servers/page.tsx`
- [ ] 移除RefreshToken组件，使用useSession Hook
- [ ] 测试所有功能：搜索、分页、排序、任务创建
- [ ] 确认会话过期处理正常

**批量页面迁移**：
按以下顺序逐个迁移：
- [ ] `app/dashboard/tasks/page.tsx`
- [ ] `app/dashboard/medias/page.tsx`
- [ ] `app/dashboard/manuals/page.tsx`
- [ ] `app/dashboard/provided-files/page.tsx`
- [ ] `app/dashboard/support-files/page.tsx`
- [ ] `app/dashboard/oplogs/page.tsx`

**迁移模式**：
```typescript
// ❌ 旧方式
const refresh = generateSecureId(true);
return (
  <div>
    {/* 内容 */}
    <RefreshToken key={refresh} />
  </div>
);

// ✅ 新方式（完全模仿 next-auth）
const { data: session, status } = useSession();

if (status === 'loading') return <Loading />;
if (status === 'unauthenticated') redirect('/login');

return <div>{/* 内容 */}</div>;
```

### ✅ 第3步：模态窗口迁移（几天）

**迁移模态窗口**：
- [ ] `app/ui/license-modal.tsx`
- [ ] `app/ui/notification-modal.tsx`

**迁移模式**：
```typescript
// ❌ 旧方式
const [random, setRandom] = useState("1");
useEffect(() => {
  setRandom(generateSecureId());
}, [isOpen]);

return (
  <div>
    {/* 内容 */}
    <RefreshToken key={random}/>
  </div>
);

// ✅ 新方式（完全模仿 next-auth）
const { data: session } = useSession();

// 会话过期时自动关闭
if (!session) {
  onClose();
  return null;
}

return <div>{/* 内容 */}</div>;
```

### ✅ 第4步：清理和优化（几天）

**清理旧代码**：
- [ ] 删除`app/ui/refreshToken.tsx`
- [ ] 清理所有`generateSecureId`调用
- [ ] 移除相关导入语句
- [ ] 清理不再使用的工具函数

**代码审查**：
- [ ] 确认所有RefreshToken引用已移除
- [ ] 确认所有页面都使用新的会话管理方式
- [ ] 确认代码质量和一致性

## 测试和验证

### ✅ 功能测试
- [ ] 所有页面正常加载和运行
- [ ] 会话自动刷新正常工作
- [ ] 会话过期时正确重定向
- [ ] 跨标签页会话同步正常
- [ ] 模态窗口会话管理正常

### ✅ 性能测试
- [ ] API调用频率显著降低（90%+）
- [ ] 页面响应速度提升
- [ ] 用户操作更流畅

### ✅ 开发体验测试
- [ ] 新页面开发无需手动添加RefreshToken
- [ ] 使用方式与next-auth完全相同
- [ ] 代码更简洁和一致

## 成功指标

### ✅ 技术指标
- [ ] 消除12个位置的RefreshToken组件
- [ ] API调用减少90%以上
- [ ] 无功能回归问题
- [ ] 代码重复度显著降低

### ✅ 用户体验指标
- [ ] 页面操作更流畅
- [ ] 无意外的登录跳转
- [ ] 会话过期处理用户友好

### ✅ 开发效率指标
- [ ] 新页面开发效率提升50%
- [ ] 代码审查效率提升
- [ ] 新开发者上手更容易

## 回滚计划

### ✅ 分阶段回滚
```bash
# 1. 从根布局移除SessionProvider
# 恢复原始的 layout.tsx

# 2. 恢复页面组件
git checkout HEAD~1 -- app/dashboard/servers/page.tsx
# 对其他已迁移的页面重复此操作

# 3. 恢复RefreshToken组件（如果已删除）
git checkout HEAD~1 -- app/ui/refreshToken.tsx
```

### ✅ 问题排查清单
- [ ] 检查SessionProvider是否正确包装应用
- [ ] 检查useSession Hook是否在Provider内部使用
- [ ] 检查API路由是否正常工作
- [ ] 检查会话数据结构是否一致

## 最终验证

### ✅ 完整性检查
- [ ] 所有页面都已迁移并测试通过
- [ ] 所有模态窗口都已迁移并测试通过
- [ ] 旧的RefreshToken组件已完全移除
- [ ] 开发体验与next-auth完全相同

### ✅ 文档更新
- [ ] 更新开发文档
- [ ] 更新新人培训材料
- [ ] 更新故障排除指南

## 总结

完成此迁移后，系统将实现：
- **与next-auth相同的开发体验**：简单、一致、优雅
- **保留所有现有业务逻辑**：iron-session + Keycloak集成
- **技术债务清零**：消除RefreshToken模式的所有问题
- **面向未来的架构**：为复杂业务场景奠定基础

这是一个真正的架构升级，而不仅仅是性能优化。
