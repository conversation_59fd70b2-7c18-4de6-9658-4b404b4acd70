import { defineConfig, devices } from '@playwright/test';
import * as path from 'path';

/**
 * @fileoverview Playwright 集成测试最佳实践配置文件
 *
 * 本配置文件采用业界最佳实践，实现了：
 * - 多服务自动化管理（webServer）
 * - 智能重试和并发策略
 * - 全面的监控和报告
 * - 环境隔离和清理
 * - 性能优化配置
 *
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */
export default defineConfig({
  testDir: './specs',

  // 🚀 性能优化：智能并发策略
  fullyParallel: false, // 数据库操作测试需要串行执行以避免冲突
  forbidOnly: !!process.env.CI, // CI 环境禁止 .only 测试

  // 🔄 智能重试策略
  retries: process.env.CI ? 3 : 1, // CI 环境更多重试次数
  workers: process.env.CI ? 2 : 4, // 本地环境更多并发数

  // ⏱️ 超时配置优化
  timeout: 90 * 1000, // 90秒测试超时（考虑到 Azure Functions 冷启动）
  expect: {
    timeout: 15 * 1000, // 15秒断言超时
  },

  // 📊 多格式报告 - 业界标准
  reporter: [
    ['html', {
      outputFolder: '../test-results/html-report',
      open: 'never',
      attachmentsBaseURL: 'file://' + path.resolve('../test-results/'),
    }],
    ['json', { outputFile: '../test-results/test-results.json' }],
    ['junit', { outputFile: '../test-results/junit-report.xml' }],
    ['line'], // 控制台实时输出
    ['github'], // GitHub Actions 集成
  ],

  // 📁 输出目录结构化
  outputDir: '../test-results/test-output',

  // 🔧 全局测试配置
  use: {
    baseURL: 'http://localhost:3000',

    // 🎬 追踪和调试 - 最佳实践
    trace: 'retain-on-failure',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',

    // 🌐 浏览器配置优化
    headless: !!process.env.CI,
    viewport: { width: 1280, height: 720 },

    // 🚀 性能优化
    actionTimeout: 20 * 1000, // 20秒操作超时
    navigationTimeout: 45 * 1000, // 45秒导航超时（考虑到构建时间）

    // 🔐 安全和网络配置
    ignoreHTTPSErrors: true,
    bypassCSP: true, // 绕过 CSP 限制
  },
  // 🖥️ 多浏览器测试矩阵
  projects: [
    // 主要测试浏览器 - Chromium
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],
        launchOptions: {
          args: [
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--no-sandbox', // CI 环境需要
          ],
        },
      },
    },
  ],

  // 🚀 多服务自动化管理 - 业界最佳实践
  webServer: [
    // Azurite - Azure Storage 模拟器（必须最先启动）
    {
      command: process.platform === 'win32'
        ? 'npx azurite --silent --location azurite-data --debug azurite-debug.log --blobPort 10000 --queuePort 10001 --tablePort 10002'
        : 'npx azurite --silent --location azurite-data --debug azurite-debug.log --blobPort 10000 --queuePort 10001 --tablePort 10002',
      port: 10000,
      reuseExistingServer: !process.env.CI,
      timeout: 60 * 1000, // 1分钟启动超时
      stdout: 'pipe',
      stderr: 'pipe',
    },

    // Mock Server - Azure Automation 模拟器
    {
      command: 'npx ts-node scripts/start-mock-server.ts',
      port: 3001,
      reuseExistingServer: !process.env.CI,
      timeout: 30 * 1000, // 30秒启动超时
      stdout: 'pipe',
      stderr: 'pipe',
    },

    // Next.js 前端应用（要求预先构建）
    {
      command: process.platform === 'win32'
        ? 'cd ../../apps/jcs-endpoint-nextjs && (if not exist .next\\BUILD_ID (echo ❌ Next.js 未构建！请先运行: npm run build && exit 1)) && npm run start'
        : 'cd ../../apps/jcs-endpoint-nextjs && (test -f .next/BUILD_ID || (echo "❌ Next.js 未构建！请先运行: npm run build" && exit 1)) && npm run start',
      port: 3000,
      reuseExistingServer: !process.env.CI,
      timeout: 90 * 1000, // 1.5分钟启动超时
      env: {
        NODE_ENV: 'test',
        PORT: '3000',
        // 忽略 Windows 权限错误
        FORCE_COLOR: '0',
        NODE_OPTIONS: '--max-old-space-size=4096',
      },
      stdout: 'pipe',
      stderr: 'pipe', // 显示错误信息
    },

    // Azure Functions - 标准服务（要求预先构建）
    {
      command: process.platform === 'win32'
        ? 'cd ../../apps/jcs-backend-services-standard && (if not exist dist (echo ❌ Standard Functions 未构建！请先运行: npm run build && exit 1)) && copy local.settings.test.json local.settings.json && func start --port 7072'
        : 'cd ../../apps/jcs-backend-services-standard && (test -d dist || (echo "❌ Standard Functions 未构建！请先运行: npm run build" && exit 1)) && cp local.settings.test.json local.settings.json && func start --port 7072',
      port: 7072,
      reuseExistingServer: !process.env.CI,
      timeout: 90 * 1000, // 1.5分钟启动超时
      env: {
        FUNCTIONS_WORKER_RUNTIME: 'node',
        AzureWebJobsFeatureFlags: 'EnableWorkerIndexing',
      },
      stdout: 'pipe',
      stderr: 'pipe',
    },

    // Azure Functions - 长时运行服务（要求预先构建）
    {
      command: process.platform === 'win32'
        ? 'cd ../../apps/jcs-backend-services-long-running && (if not exist dist (echo ❌ Long-Running Functions 未构建！请先运行: npm run build && exit 1)) && copy local.settings.test.json local.settings.json && func start --port 7071'
        : 'cd ../../apps/jcs-backend-services-long-running && (test -d dist || (echo "❌ Long-Running Functions 未构建！请先运行: npm run build" && exit 1)) && cp local.settings.test.json local.settings.json && func start --port 7071',
      port: 7071,
      reuseExistingServer: !process.env.CI,
      timeout: 90 * 1000, // 1.5分钟启动超时
      env: {
        FUNCTIONS_WORKER_RUNTIME: 'node',
        AzureWebJobsFeatureFlags: 'EnableWorkerIndexing',
      },
      stdout: 'pipe',
      stderr: 'pipe',
    },
  ],

  // 🧹 全局钩子 - 环境准备和清理
  globalSetup: require.resolve('./support/simple-global-setup.ts'),
  globalTeardown: require.resolve('./support/global-teardown.ts'),
});