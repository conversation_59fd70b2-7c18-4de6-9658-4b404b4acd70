# 数据模型: 提供文件 (ProvidedFile)

*   **表名 (逻辑名)**: `ProvidedFile`
*   **对应UI界面**: 「提供ファイル一覧」 (Provided File List)
*   **主要用途**: 存储向用户提供的各种文件（例如配置文件、工具、脚本、证书等）的详细信息，供用户在门户上查看和下载。

## 1. 字段定义

| 字段名 (Prisma/英文) | 数据类型 (SQL参考) | PK | FK | UQ | Nullable | Default  | 描述 (中文)                                                                                              |
| :------------------- | :----------------- | :--- | :--- | :--- | :------- | :------- | :------------------------------------------------------------------------------------------------------- |
| `id`                 | VARCHAR(36)        | ●    |      |      |          | `cuid()` | 主键。CUID格式，由系统自动生成。                                                                             |
| `name`               | VARCHAR(255)       |      |      | ●    |          |          | 提供文件的业务唯一名称/标识符。例如："EndpointAgentConfig.zip"。被`PlanProvidedFile`表用作外键。对应原「機能仕様書」中的`fileName`。 |
| `description`        | NVARCHAR(MAX)      |      |      |      |          |          | 对提供文件的详细描述或说明。                                                                                 |
| `updatedAt`          | DATETIME           |      |      |      |          |          | 此提供文件记录的最后更新时间。对应原「機能仕様書」中的`lastUpdatedTimestamp`。                                         |
| `size`               | INT                |      |      |      |          |          | 提供文件的大小，单位为字节 (Bytes)。界面显示时会转换为KB/MB/GB等易读单位。                                           |
| `fileName`           | VARCHAR(255)       |      |      |      |          | `""`     | 记录上传时的原始文件名或特定别名。如果`name`字段已包含用户可见且唯一的文件名，此字段可能用于内部追踪或特定场景，否则可能冗余。       |

*PK: Primary Key, FK: Foreign Key, UQ: Unique Key*

## 2. 关系

*   **对 `PlanProvidedFile` (`planProvidedFiles`)**: 一对多关系 (`PlanProvidedFile[]`)。一个提供文件（由其业务唯一名称`name`确定）可以通过`PlanProvidedFile`表关联到多个契约计划，表示该文件可用于这些计划。

## 3. 唯一约束

*   `UNIQUE KEY (name)` (Prisma Schema中已定义 `@unique`)，确保提供文件的业务名称在系统中是唯一的。

## 4. Azure Blob Storage 路径约定 (推测)

*   提供文件在Azure Blob Storage中的实际存储路径，通常遵循预定义规则动态构建，例如基于其业务唯一名称 `name` 或 `id`。
*   表中不直接存储完整的Blob路径。

## 5. 索引 (示例)

*   `PRIMARY KEY (id)`
*   `UNIQUE KEY UQ_ProvidedFile_Name (name)` (业务唯一键)
*   `INDEX idx_providedfile_updatedat (updatedAt DESC)` (便于按更新时间排序)
*   `INDEX idx_providedfile_description (description)` (如果需要对描述进行全文搜索或模糊查询，但需注意性能)