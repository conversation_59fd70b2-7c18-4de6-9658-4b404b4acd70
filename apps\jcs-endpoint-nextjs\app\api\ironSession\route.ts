/**
 * @file route.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2024, 2025, Hitachi Solutions, Ltd.
 */

import { handleApiError } from "@/app/lib/portal-error";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";


// POSTメソッドの実装
export async function POST() {
    try {
        // セッションオブジェクトの初期化
        const data = await getIronSession<SessionData>(cookies(), sessionOptions);
        return NextResponse.json({data});
    } catch (error) {
        // エラーが発生した場合はエラーハンドリング関数に委譲
        return handleApiError(error);
    }
}