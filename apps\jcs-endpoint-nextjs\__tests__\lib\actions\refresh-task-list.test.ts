/**
 * @fileoverview refreshTaskList 機能のテスト
 * @description タスク一覧キャッシュ無効化処理のテスト
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { expect } from "@jest/globals";
import { refreshTaskList } from "@/app/lib/actions/tasks";
import { getIronSession } from "iron-session";
import { revalidateTag } from "next/cache";

// 模拟 iron-session
jest.mock("iron-session", () => ({
  getIronSession: jest.fn(),
}));

// 模拟 Next.js cache revalidation
jest.mock("next/cache", () => ({
  revalidateTag: jest.fn(),
  unstable_cache: jest.fn((fn) => fn),
}));

// 模拟 Logger
jest.mock("@/app/lib/utils", () => ({
  Logger: {
    info: jest.fn(),
  },
}));

describe("refreshTaskList 機能", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  /**
   * 試験観点：正常系のキャッシュ無効化処理
   * 試験対象：refreshTaskList 関数
   * 試験手順：
   * 1. 有効なセッション情報でrefreshTaskListを実行
   * 2. 適切なキャッシュタグで無効化が実行されることを確認
   * 確認項目：
   * - revalidateTagが正しいタグで呼び出されること
   */
  it("正常系: 有効なセッション情報でキャッシュ無効化", async () => {
    (getIronSession as jest.Mock).mockResolvedValue({
      user: {
        userId: "<EMAIL>",
        licenseId: "test-license-123",
      },
    });

    await refreshTaskList();

    expect(revalidateTag).toHaveBeenCalledWith("tasks-test-license-123");
  });

  /**
   * 試験観点：セッション情報が存在しない場合
   * 試験対象：refreshTaskList 関数のセッション検証
   * 試験手順：
   * 1. セッション情報が存在しない状況でrefreshTaskListを実行
   * 2. キャッシュ無効化がスキップされることを確認
   * 確認項目：
   * - revalidateTagが呼び出されないこと
   */
  it("異常系: セッション情報が存在しない場合", async () => {
    (getIronSession as jest.Mock).mockResolvedValue({});

    await refreshTaskList();

    expect(revalidateTag).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：ユーザー情報が不完全な場合
   * 試験対象：refreshTaskList 関数のセッション検証
   * 試験手順：
   * 1. ライセンスIDが存在しない状況でrefreshTaskListを実行
   * 2. キャッシュ無効化がスキップされることを確認
   * 確認項目：
   * - revalidateTagが呼び出されないこと
   */
  it("異常系: ライセンスIDが存在しない場合", async () => {
    (getIronSession as jest.Mock).mockResolvedValue({
      user: {
        userId: "<EMAIL>",
        // licenseId が存在しない
      },
    });

    await refreshTaskList();

    expect(revalidateTag).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：ユーザー情報が null の場合
   * 試験対象：refreshTaskList 関数のセッション検証
   * 試験手順：
   * 1. user が null の状況でrefreshTaskListを実行
   * 2. キャッシュ無効化がスキップされることを確認
   * 確認項目：
   * - revalidateTagが呼び出されないこと
   */
  it("異常系: ユーザー情報がnullの場合", async () => {
    (getIronSession as jest.Mock).mockResolvedValue({
      user: null,
    });

    await refreshTaskList();

    expect(revalidateTag).not.toHaveBeenCalled();
  });
});
