describe("エラーハンドリングの一般的なテスト", () => {
  describe("お知らせダイアログ", () => {
    it("利用者お知らせ API はサーバーエラーが発生した場合、エラーメッセージを表示する", () => {
      cy.intercept("GET", "/api/notifications", {
        statusCode: 500,
        body: {
          error:
            "サーバーに一時的に接続できません。しばらくしてから再度ポータルにアクセスしてください。",
        },
        headers: {
          "content-type": "application/json",
        },
      });
      cy.visit("/login");
      cy.get("#userId").type("hitachi.hanako.ab");
      cy.get("#password").type("changeit!@#");
      cy.get("button").contains("ログイン").click();
      cy.wait(3000);
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("お知らせ").click();

      cy.get("#message-modal").contains(
        "サーバーに一時的に接続できません。しばらくしてから再度ポータルにアクセスしてください。",
      );
    });
  });
});
