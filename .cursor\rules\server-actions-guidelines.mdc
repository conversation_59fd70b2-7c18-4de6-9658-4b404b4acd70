---
description: 
globs: 
alwaysApply: true
---
# Server Actions Guidelines (for apps/jcs-endpoint-nextjs)

This document provides guidelines for creating, using, and documenting Server Actions within the `apps/jcs-endpoint-nextjs` application. Server Actions are a primary mechanism for handling form submissions, data mutations, and invoking server-side logic directly from components.

**Primary Location:** `app/lib/actions.ts` (typically as static methods of a `ServerAction` class or exported functions).
**Primary Reference for Detailed Logic & Contract:** Component design documents in the Monorepo's `docs/components/` (especially under `actions/` subdirectories or within feature-specific task documentation).

## Core Principles

1.  **Centralization & "Use Server" Directive:**
    *   Define Server Actions primarily in `app/lib/actions.ts`.
    *   The file containing Server Actions **MUST** start with the `"use server";` directive.
2.  **Security:**
    *   Server Actions have built-in CSRF protection.
    *   **Authentication & Authorization:** Server Actions performing restricted operations **MUST** verify user authentication (e.g., by retrieving the Iron Session via `getIronSession`) and perform necessary authorization checks at the beginning of the action. Return or throw an appropriate error if checks fail.
3.  **Input Validation:**
    *   All inputs to Server Actions (e.g., `FormData` entries, direct arguments) **MUST** be rigorously validated before any processing.
    *   Use appropriate validation methods (e.g., regex from `app/lib/definitions.ts`, type checks, custom validation functions). Consider Zod for complex objects if not overkill.
    *   If validation fails, return a structured error response (e.g., `{ success: false, errors: { fieldName: "Error message" } }`) that client components can use to display inline errors.
4.  **Return Values & Client Feedback:**
    *   Server Actions **MUST** return serializable data.
    *   It is highly recommended to return an object with a consistent structure, such as:
        `{ success: boolean; message?: string; errors?: Record<string, string>; data?: any }`.
    *   This allows client components (especially those using `useFormState`) to easily handle success, display user-friendly messages, and show specific field errors.
    *   The exact contract (parameters, return value structure) for significant Server Actions **MUST** be documented in `docs/components/`.
5.  **Error Handling & Logging:**
    *   Use `try-catch` blocks to handle exceptions during execution.
    *   Log all errors with detailed context using `Logger.error()` from `app/lib/logger.ts`.
    *   For unrecoverable errors meant to be caught by Next.js error boundaries, use `handleServerError` from `app/lib/portal-error.ts` (which logs and re-throws). For errors where specific feedback needs to be returned to the client via the action's return value, construct the error object as described above.
6.  **Cache Invalidation:** If a Server Action successfully mutates data that is cached elsewhere (e.g., data fetched via `ServerData` methods using `unstable_cache`), the action **MUST** revalidate the relevant cache tags using `revalidateTag()` from `next/cache`.
7.  **Documentation (SSoT in `docs/`):** The detailed design, including input parameters (types, validation rules), expected return value structures (for success and various error states), and core internal logic steps for each significant Server Action **MUST** be documented in the Monorepo's `docs/components/` directory. This is the AI's primary reference for implementation details.
8.  **Logging Function Calls:** Use the `@LogFunctionSignature()` decorator from `app/lib/logger.ts` for Server Action methods to automatically log their invocation and arguments.

## Defining Server Actions (Example in `app/lib/actions.ts`)

```typescript
// In app/lib/actions.ts
"use server";

import Logger, { LogFunctionSignature } from "@/app/lib/logger";
import prisma from "@/app/lib/prisma";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import { SessionData, sessionOptions } from "./session"; // Adjust path if needed
import { revalidateTag } from "next/cache";
import { PORTAL_ERROR_MESSAGES } from "./definitions"; // Adjust path if needed

// Interface for a typical structured response from a Server Action
interface ActionResult<T = any> {
  success: boolean;
  message?: string;
  errors?: Record<string, string>; // For field-specific validation errors
  data?: T;
}

class ServerAction {
  @LogFunctionSignature()
  static async exampleTaskAction(
    //prevState: ActionResult | null, // Needed if used directly as useFormState action
    formData: FormData
  ): Promise<ActionResult<{ taskId: string }>> {
    const session = await getIronSession<SessionData>(cookies(), sessionOptions);
    if (!session.user || !session.user.userId) {
      return { success: false, message: PORTAL_ERROR_MESSAGES.EMEC0005 /* Or specific unauthorized */ };
    }

    const taskName = formData.get('taskName') as string;
    const taskDescription = formData.get('taskDescription') as string;

    // --- Input Validation ---
    const fieldErrors: Record<string, string> = {};
    if (!taskName || taskName.trim().length < 3) {
      fieldErrors.taskName = "Task name must be at least 3 characters.";
    }
    if (!taskDescription) {
      fieldErrors.taskDescription = "Task description is required.";
    }
    if (Object.keys(fieldErrors).length > 0) {
      return { success: false, errors: fieldErrors, message: "Validation failed." };
    }

    try {
      // --- Business Logic ---
      // const newTask = await prisma.task.create({
      //   data: { name: taskName, description: taskDescription, userId: session.user.userId },
      // });

      // --- Cache Revalidation (if applicable) ---
      // revalidateTag('tasks-for-user-' + session.user.userId);

      // return { success: true, message: "Task created successfully!", data: { taskId: newTask.id } };
      return { success: true, message: "Task created successfully!", data: { taskId: "mockTaskId123" } }; // Placeholder
    } catch (error: any) {
      Logger.error({
        message: `Failed to create task for user ${session.user.userId}`,
        error: error.message,
        stack: error.stack,
        taskName,
      });
      return { success: false, message: PORTAL_ERROR_MESSAGES.EMEC0007 /* Generic server error */ };
    }
  }
}

export default ServerAction;
```

## Invoking Server Actions from Client Components (using `useFormState`)

```tsx
// Example in a client component: app/ui/some-feature/TaskForm.tsx
// "use client";
// import ServerAction from "@/app/lib/actions"; // Assuming ServerAction class with static methods
// import { useFormState, useFormStatus } from "react-dom";
// import { PORTAL_ERROR_MESSAGES } from "@/app/lib/definitions";
//
// interface ActionResult<T = any> { // Ensure this matches the Server Action's return type
//   success: boolean;
//   message?: string;
//   errors?: Record<string, string>;
//   data?: T;
// }
//
// const initialState: ActionResult = { success: false, message: null, errors: {} };
//
// // Wrapper to match useFormState's action signature if ServerAction method isn't directly compatible
// async function wrappedExampleTaskAction(prevState: ActionResult, formData: FormData): Promise<ActionResult> {
//    return ServerAction.exampleTaskAction(formData);
// }
//
// function TaskForm() {
//   const [state, formAction] = useFormState(wrappedExampleTaskAction, initialState);
//   const { pending } = useFormStatus();
//
//   return (
//     <form action={formAction}>
//       <div>
//         <label htmlFor="taskName">Task Name:</label>
//         <input type="text" id="taskName" name="taskName" />
//         {state.errors?.taskName && <p style={{ color: 'red' }}>{state.errors.taskName}</p>}
//       </div>
//       <div>
//         <label htmlFor="taskDescription">Description:</label>
//         <textarea id="taskDescription" name="taskDescription" />
//         {state.errors?.taskDescription && <p style={{ color: 'red' }}>{state.errors.taskDescription}</p>}
//       </div>
//
//       <button type="submit" disabled={pending}>
//         {pending ? "Creating Task..." : "Create Task"}
//       </button>
//
//       {state.message && (
//         <p style={{ color: state.success ? 'green' : 'red' }}>
//           {state.message}
//         </p>
//       )}
//       {state.success && state.data && <p>New Task ID: {state.data.taskId}</p>}
//     </form>
//   );
// }
// export default TaskForm;
```

---

**NOTE TO CURSOR:**
1.  When tasked with creating new Server Actions, the definition **MUST** be placed in `app/lib/actions.ts`, adhering to the established class pattern or exported function style, and include the `"use server";` directive.
2.  **Crucially, before implementation, consult the Monorepo's `docs/components/` directory (especially any `actions/` subdirectories or feature-specific task design documents) for the detailed design, input parameters, validation rules, expected return value structure (for both success and error scenarios), and core logic steps of the required Server Action.** This documentation is the SSoT.
3.  Implement mandatory security checks: verify user authentication (Iron Session) and perform authorization if the action is restricted.
4.  Implement comprehensive input validation for all arguments and `FormData`. Return structured errors for client-side display.
5.  Use the `@LogFunctionSignature()` decorator for the action method. Log all caught errors using `Logger.error()`.
6.  If the action successfully modifies data, ensure all relevant server-side caches are revalidated using `revalidateTag()`.
7.  Structure return values consistently (e.g., `{ success, message, errors, data }`) to facilitate client-side handling with `useFormState` or direct promise resolution.

---
