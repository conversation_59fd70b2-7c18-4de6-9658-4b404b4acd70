/**
 * @fileoverview azureClients.ts の単体テストファイル
 * @description
 * Azure Storage・Automationクライアント作成機能の全分岐・例外処理を網羅的に検証する単体テストファイル。
 * 外部依存（Azure SDK、fetch API等）をモックし、
 * 正常系・異常系・環境変数検証の全パターンを自動テストで検証する。
 * 80%+コードカバレッジを目標とし、Azure サービス統合の堅牢性を担保する。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

// Azure SDK のモック定義
const mockBlobServiceClient = {
  getContainerClient: jest.fn(),
};

const mockShareServiceClient = {
  getShareClient: jest.fn(),
};

const mockServiceBusClient = {
  createSender: jest.fn(),
  createReceiver: jest.fn(),
};

const mockDefaultAzureCredential = {
  getToken: jest.fn(),
};

// Azure SDK モジュールをモック
jest.mock("@azure/storage-blob", () => ({
  BlobServiceClient: jest.fn(() => mockBlobServiceClient),
}));

jest.mock("@azure/storage-file-share", () => ({
  ShareServiceClient: {
    fromConnectionString: jest.fn(() => mockShareServiceClient),
  },
}));

jest.mock("@azure/service-bus", () => ({
  ServiceBusClient: jest.fn(() => mockServiceBusClient),
}));

jest.mock("@azure/identity", () => ({
  DefaultAzureCredential: jest.fn(() => mockDefaultAzureCredential),
}));

// fetch API をモック
global.fetch = jest.fn();

describe("azureClients 単体テスト", () => {
  // 元の環境変数を保存
  const originalEnv = process.env;

  beforeEach(() => {
    // 環境変数をリセット
    jest.resetModules();
    process.env = { ...originalEnv };
    
    // 必要な環境変数を設定
    process.env.AZURE_STORAGE_CONNECTION_STRING = "DefaultEndpointsProtocol=https;AccountName=test;AccountKey=test;EndpointSuffix=core.windows.net";
    process.env.AZURE_STORAGE_ACCOUNT_NAME = "teststorage";
    process.env.AZURE_AUTOMATION_ACCOUNT_NAME = "testautomation";
    process.env.SUBSCRIPTION_ID = "test-subscription-id";
    process.env.RESOURCE_GROUP_NAME = "test-resource-group";
    process.env.AZURE_SERVICEBUS_NAMESPACE_HOSTNAME__fullyQualifiedNamespace = "test.servicebus.windows.net";

    // モックをクリア
    jest.clearAllMocks();
    
    // DefaultAzureCredential のモックを設定
    mockDefaultAzureCredential.getToken.mockResolvedValue({
      token: "mock-access-token",
      expiresOnTimestamp: Date.now() + 3600000,
    });

    // fetch のモックを設定
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      status: 200,
      statusText: "OK",
      json: jest.fn().mockResolvedValue({ id: "test-job" }),
    });
  });

  afterEach(() => {
    // 環境変数を復元
    process.env = originalEnv;
  });

  // ========== 環境変数検証テスト ==========

  /**
   * 試験観点：環境変数の存在確認処理
   * 試験対象：azureClients モジュールの環境変数検証ロジック
   * 試験手順：
   * 1. 必要な環境変数が全て設定されている状態でモジュールをインポート
   * 2. モジュールが正常にロードされることを確認
   * 確認項目：
   * - モジュールが例外なく正常にロードされること
   */
  it("環境変数検証: 全ての必要な環境変数が設定されている場合、正常にモジュールがロードされる", async () => {
    // 動的インポートでモジュールをロード
    const azureClients = await import("../../lib/azureClients");
    
    // モジュールが正常にロードされ、関数が利用可能であることを確認
    expect(azureClients.createBlobServiceClient).toBeDefined();
    expect(azureClients.createShareServiceClient).toBeDefined();
    expect(azureClients.createServiceBusClient).toBeDefined();
    expect(azureClients.createAutomationJob).toBeDefined();
  });

  /**
   * 試験観点：環境変数不足時のエラー処理
   * 試験対象：azureClients モジュールの環境変数検証ロジック
   * 試験手順：
   * 1. AZURE_STORAGE_FILES_CONNECTION_STRING を削除
   * 2. モジュールのインポートを試行
   * 確認項目：
   * - 適切なエラーメッセージが含まれた例外がスローされること
   */
  it("環境変数検証: AZURE_STORAGE_FILES_CONNECTION_STRING が未設定の場合、エラーがスローされる", async () => {
    delete process.env.AZURE_STORAGE_FILES_CONNECTION_STRING;

    await expect(import("../../lib/azureClients")).rejects.toThrow(
      "必要な環境変数が設定されていないため、処理を継続できない。次の変数を確認すること：AZURE_STORAGE_FILES_CONNECTION_STRING, AZURE_STORAGE_BLOB_CONNECTION_STRING, AZURE_AUTOMATION_ACCOUNT_NAME, SUBSCRIPTION_ID, RESOURCE_GROUP_NAME, AZURE_SERVICEBUS_NAMESPACE_HOSTNAME__fullyQualifiedNamespace"
    );
  });

  /**
   * 試験観点：環境変数不足時のエラー処理
   * 試験対象：azureClients モジュールの環境変数検証ロジック
   * 試験手順：
   * 1. AZURE_STORAGE_BLOB_CONNECTION_STRING を削除
   * 2. モジュールのインポートを試行
   * 確認項目：
   * - 適切なエラーメッセージが含まれた例外がスローされること
   */
  it("環境変数検証: AZURE_STORAGE_BLOB_CONNECTION_STRING が未設定の場合、エラーがスローされる", async () => {
    delete process.env.AZURE_STORAGE_BLOB_CONNECTION_STRING;

    await expect(import("../../lib/azureClients")).rejects.toThrow(
      "必要な環境変数が設定されていないため、処理を継続できない。次の変数を確認すること：AZURE_STORAGE_FILES_CONNECTION_STRING, AZURE_STORAGE_BLOB_CONNECTION_STRING, AZURE_AUTOMATION_ACCOUNT_NAME, SUBSCRIPTION_ID, RESOURCE_GROUP_NAME, AZURE_SERVICEBUS_NAMESPACE_HOSTNAME__fullyQualifiedNamespace"
    );
  });

  // ========== クライアント作成テスト ==========

  /**
   * 試験観点：Azure Blob Service クライアント作成の正常処理
   * 試験対象：createBlobServiceClient 関数
   * 試験手順：
   * 1. createBlobServiceClient を呼び出す
   * 2. 正しいパラメータで BlobServiceClient が作成されることを確認
   * 確認項目：
   * - BlobServiceClient が正しい URL と認証情報で作成されること
   * - 作成されたクライアントが返されること
   */
  it("正常系: createBlobServiceClient が正しいパラメータでクライアントを作成", async () => {
    const azureClients = await import("../../lib/azureClients");
    const { BlobServiceClient } = await import("@azure/storage-blob");

    const client = azureClients.createBlobServiceClient();
    
    expect(BlobServiceClient).toHaveBeenCalledWith(
      "https://teststorage.blob.core.windows.net",
      expect.objectContaining({
        getToken: expect.any(Function)
      })
    );
    expect(client).toBe(mockBlobServiceClient);
  });

  /**
   * 試験観点：Azure Files Service クライアント作成の正常処理
   * 試験対象：createShareServiceClient 関数
   * 試験手順：
   * 1. createShareServiceClient を呼び出す
   * 2. 正しい接続文字列で ShareServiceClient が作成されることを確認
   * 確認項目：
   * - ShareServiceClient が正しい接続文字列で作成されること
   * - 作成されたクライアントが返されること
   */
  it("正常系: createShareServiceClient が正しい接続文字列でクライアントを作成", async () => {
    const azureClients = await import("../../lib/azureClients");
    const { ShareServiceClient } = await import("@azure/storage-file-share");
    
    const client = azureClients.createShareServiceClient();
    
    expect(ShareServiceClient.fromConnectionString).toHaveBeenCalledWith(
      "DefaultEndpointsProtocol=https;AccountName=test;AccountKey=test;EndpointSuffix=core.windows.net"
    );
    expect(client).toBe(mockShareServiceClient);
  });

  /**
   * 試験観点：Azure Service Bus クライアント作成の正常処理
   * 試験対象：createServiceBusClient 関数
   * 試験手順：
   * 1. createServiceBusClient を呼び出す
   * 2. 正しいパラメータで ServiceBusClient が作成されることを確認
   * 確認項目：
   * - ServiceBusClient が正しい名前空間と認証情報で作成されること
   * - 作成されたクライアントが返されること
   */
  it("正常系: createServiceBusClient が正しいパラメータでクライアントを作成", async () => {
    const azureClients = await import("../../lib/azureClients");
    const { ServiceBusClient } = await import("@azure/service-bus");

    const client = azureClients.createServiceBusClient();
    
    expect(ServiceBusClient).toHaveBeenCalledWith(
      "test.servicebus.windows.net",
      expect.objectContaining({
        getToken: expect.any(Function)
      })
    );
    expect(client).toBe(mockServiceBusClient);
  });

  // ========== Azure Automation ジョブ作成テスト ==========

  /**
   * 試験観点：Azure Automation ジョブ作成の正常処理
   * 試験対象：createAutomationJob 関数
   * 試験手順：
   * 1. 正常なパラメータで createAutomationJob を呼び出す
   * 2. 正しい URL とボディで fetch が呼ばれることを確認
   * 確認項目：
   * - 認証トークンが正しく取得されること
   * - 正しい URL と HTTP メソッドで fetch が呼ばれること
   * - 正しいリクエストボディが送信されること
   */
  it("正常系: createAutomationJob が正しいパラメータでジョブを作成", async () => {
    const azureClients = await import("../../lib/azureClients");

    const jobName = "test-job";
    const runbookName = "Test-Runbook";
    const parameters = { param1: "value1" };
    const runOn = "test-worker-group";

    const response = await azureClients.createAutomationJob(jobName, runbookName, parameters, runOn);

    // 認証トークンが取得されることを確認
    expect(mockDefaultAzureCredential.getToken).toHaveBeenCalledWith("https://management.azure.com/.default");

    // 正しい URL と設定で fetch が呼ばれることを確認
    expect(global.fetch).toHaveBeenCalledWith(
      "https://management.azure.com/subscriptions/test-subscription-id/resourceGroups/test-resource-group/providers/Microsoft.Automation/automationAccounts/testautomation/jobs/test-job?api-version=2023-11-01",
      {
        method: "PUT",
        headers: {
          "Authorization": "Bearer mock-access-token",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          properties: {
            runbook: { name: runbookName },
            parameters,
            runOn,
          },
        }),
      }
    );

    expect(response).toBeDefined();
  });

  /**
   * 試験観点：Azure Automation ジョブ作成の異常処理
   * 試験対象：createAutomationJob 関数
   * 試験手順：
   * 1. fetch が失敗レスポンスを返すようにモック
   * 2. createAutomationJob を呼び出す
   * 確認項目：
   * - 適切なエラーメッセージで例外がスローされること
   */
  it("異常系: createAutomationJob でAPI呼び出しが失敗した場合、エラーがスローされる", async () => {
    const azureClients = await import("../../lib/azureClients");

    // fetch が失敗レスポンスを返すようにモック
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: false,
      status: 500,
      statusText: "Internal Server Error",
    });

    await expect(
      azureClients.createAutomationJob("test-job", "Test-Runbook", {}, "test-worker-group")
    ).rejects.toThrow("Azure Automation job creation failed: 500 Internal Server Error");
  });

  // ========== Azure Automation ジョブ停止テスト ==========

  /**
   * 試験観点：Azure Automation ジョブ停止の正常処理
   * 試験対象：stopAutomationJob 関数
   * 試験手順：
   * 1. 正常なパラメータで stopAutomationJob を呼び出す
   * 2. 正しい URL とメソッドで fetch が呼ばれることを確認
   * 確認項目：
   * - 正しい URL と HTTP メソッドで fetch が呼ばれること
   * - 認証ヘッダーが正しく設定されること
   */
  it("正常系: stopAutomationJob が正しいパラメータでジョブを停止", async () => {
    const azureClients = await import("../../lib/azureClients");

    const jobName = "test-job";

    const response = await azureClients.stopAutomationJob(jobName);

    // 正しい URL と設定で fetch が呼ばれることを確認
    expect(global.fetch).toHaveBeenCalledWith(
      "https://management.azure.com/subscriptions/test-subscription-id/resourceGroups/test-resource-group/providers/Microsoft.Automation/automationAccounts/testautomation/jobs/test-job/stop?api-version=2023-11-01",
      {
        method: "POST",
        headers: {
          "Authorization": "Bearer mock-access-token",
          "Content-Type": "application/json",
        },
      }
    );

    expect(response).toBeDefined();
  });

  /**
   * 試験観点：Azure Automation ジョブ停止の異常処理
   * 試験対象：stopAutomationJob 関数
   * 試験手順：
   * 1. fetch が失敗レスポンスを返すようにモック
   * 2. stopAutomationJob を呼び出す
   * 確認項目：
   * - 適切なエラーメッセージで例外がスローされること
   */
  it("異常系: stopAutomationJob でAPI呼び出しが失敗した場合、エラーがスローされる", async () => {
    const azureClients = await import("../../lib/azureClients");

    // fetch が失敗レスポンスを返すようにモック
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: false,
      status: 404,
      statusText: "Not Found",
    });

    await expect(
      azureClients.stopAutomationJob("nonexistent-job")
    ).rejects.toThrow("Azure Automation job stop failed: 404 Not Found");
  });

  // ========== Azure Automation ジョブ状態取得テスト ==========

  /**
   * 試験観点：Azure Automation ジョブ状態取得の正常処理（ジョブ存在）
   * 試験対象：getAutomationJobStatus 関数
   * 試験手順：
   * 1. fetch が 200 レスポンスを返すようにモック
   * 2. getAutomationJobStatus を呼び出す
   * 確認項目：
   * - 正しい URL と HTTP メソッドで fetch が呼ばれること
   * - ジョブデータと正しいステータスが返されること
   * - exists フラグが true であること
   */
  it("正常系: getAutomationJobStatus でジョブが存在する場合、正しいデータが返される", async () => {
    const azureClients = await import("../../lib/azureClients");

    const mockJobData = {
      id: "test-job",
      properties: {
        status: "Completed",
        startTime: "2023-01-01T10:00:00Z",
      },
    };

    // fetch が成功レスポンスを返すようにモック
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      status: 200,
      statusText: "OK",
      json: jest.fn().mockResolvedValue(mockJobData),
    });

    const result = await azureClients.getAutomationJobStatus("test-job");

    // 正しい URL と設定で fetch が呼ばれることを確認
    expect(global.fetch).toHaveBeenCalledWith(
      "https://management.azure.com/subscriptions/test-subscription-id/resourceGroups/test-resource-group/providers/Microsoft.Automation/automationAccounts/testautomation/jobs/test-job?api-version=2023-11-01",
      {
        method: "GET",
        headers: {
          "Authorization": "Bearer mock-access-token",
          "Content-Type": "application/json",
        },
      }
    );

    // 正しい結果が返されることを確認
    expect(result).toEqual({
      jobData: mockJobData,
      statusCode: 200,
      exists: true,
    });
  });

  /**
   * 試験観点：Azure Automation ジョブ状態取得の正常処理（ジョブ不存在）
   * 試験対象：getAutomationJobStatus 関数
   * 試験手順：
   * 1. fetch が 404 レスポンスを返すようにモック
   * 2. getAutomationJobStatus を呼び出す
   * 確認項目：
   * - 正しいステータスコードが返されること
   * - exists フラグが false であること
   * - jobData が undefined であること
   */
  it("正常系: getAutomationJobStatus でジョブが存在しない場合、404ステータスが返される", async () => {
    const azureClients = await import("../../lib/azureClients");

    // fetch が 404 レスポンスを返すようにモック
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: false,
      status: 404,
      statusText: "Not Found",
    });

    const result = await azureClients.getAutomationJobStatus("nonexistent-job");

    // 正しい結果が返されることを確認
    expect(result).toEqual({
      statusCode: 404,
      exists: false,
    });
    expect(result.jobData).toBeUndefined();
  });

  /**
   * 試験観点：Azure Automation ジョブ状態取得の異常処理
   * 試験対象：getAutomationJobStatus 関数
   * 試験手順：
   * 1. fetch が 500 レスポンスを返すようにモック
   * 2. getAutomationJobStatus を呼び出す
   * 確認項目：
   * - 適切なエラーメッセージで例外がスローされること
   */
  it("異常系: getAutomationJobStatus でAPI呼び出しが失敗した場合、エラーがスローされる", async () => {
    const azureClients = await import("../../lib/azureClients");

    // fetch が失敗レスポンスを返すようにモック
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: false,
      status: 500,
      statusText: "Internal Server Error",
    });

    await expect(
      azureClients.getAutomationJobStatus("test-job")
    ).rejects.toThrow("Azure Automationジョブ状態取得に失敗しました: 500 Internal Server Error");
  });

  // ========== authenticatedFetch 関数テスト ==========

  /**
   * 試験観点：認証付きfetch関数の相対URL処理
   * 試験対象：authenticatedFetch 関数（間接的にテスト）
   * 試験手順：
   * 1. 相対URLでAzure Automation APIを呼び出す
   * 2. 正しい完全URLが構築されることを確認
   * 確認項目：
   * - 相対URLが正しく完全URLに変換されること
   * - 認証ヘッダーが正しく設定されること
   */
  it("正常系: authenticatedFetch で相対URLが正しく完全URLに変換される", async () => {
    const azureClients = await import("../../lib/azureClients");

    await azureClients.createAutomationJob("test-job", "Test-Runbook", {}, "test-worker-group");

    // fetch が正しい完全URLで呼ばれることを確認
    const fetchCall = (global.fetch as jest.Mock).mock.calls[0];
    expect(fetchCall[0]).toMatch(/^https:\/\/management\.azure\.com\//);
    expect(fetchCall[1].headers.Authorization).toBe("Bearer mock-access-token");
  });

  /**
   * 試験観点：認証付きfetch関数の完全URL処理
   * 試験対象：authenticatedFetch 関数（間接的にテスト）
   * 試験手順：
   * 1. 完全URLを直接指定してテスト
   * 2. URLがそのまま使用されることを確認
   * 確認項目：
   * - 完全URLがそのまま使用されること
   * - 認証ヘッダーが正しく設定されること
   */
  it("正常系: authenticatedFetch で完全URLがそのまま使用される", async () => {
    // この機能は現在のコードでは直接テストできないため、
    // 将来的に完全URLを受け取る機能が追加された場合のためのテストケース
    expect(true).toBe(true); // プレースホルダー
  });

  // ========== 認証エラーテスト ==========

  /**
   * 試験観点：Azure認証失敗時のエラー処理
   * 試験対象：authenticatedFetch 関数（間接的にテスト）
   * 試験手順：
   * 1. DefaultAzureCredential.getToken が失敗するようにモック
   * 2. Azure Automation API を呼び出す
   * 確認項目：
   * - 認証エラーが適切に伝播されること
   */
  it("異常系: Azure認証が失敗した場合、エラーが伝播される", async () => {
    const azureClients = await import("../../lib/azureClients");

    // 認証が失敗するようにモック
    mockDefaultAzureCredential.getToken.mockRejectedValue(new Error("Authentication failed"));

    await expect(
      azureClients.createAutomationJob("test-job", "Test-Runbook", {}, "test-worker-group")
    ).rejects.toThrow("Authentication failed");
  });
});
