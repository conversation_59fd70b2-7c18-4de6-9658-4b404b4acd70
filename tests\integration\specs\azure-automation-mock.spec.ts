/**
 * @fileoverview Azure Automation Mock Server集成测试
 * @description
 * 验证Azure Automation Mock Server与改造后的authenticatedFetch函数的集成。
 * 测试各种Azure Automation操作的模拟，包括作业创建、状态查询、停止等。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { test, expect } from '@playwright/test';
import { MockServerHelper, mockServerHelper } from '../support/mock-server-helper';
import { MockJobStatus } from '../support/azure-automation-mock-server';

/**
 * 试验观点：Azure Automation Mock Server集成测试
 * 试验对象：改造后的authenticatedFetch函数与Mock Server的集成
 * 试验手順：
 * 1. Mock Server启动
 * 2. 环境变量设置验证
 * 3. 各种Azure Automation API操作测试
 * 4. Mock Server停止和清理
 * 确认項目：
 * - Mock Server能够正确启动和停止
 * - authenticatedFetch能够正确连接到Mock Server
 * - 各种API操作返回预期结果
 * - 错误场景能够正确处理
 */

test.describe('Azure Automation Mock Server Integration', () => {
  
  test.beforeAll(async () => {
    // 启动Mock Server
    await mockServerHelper.startServer();
  });

  test.afterAll(async () => {
    // 停止Mock Server
    await mockServerHelper.stopServer();
  });

  test.beforeEach(async () => {
    // 每个测试前清理作业和失败场景
    await mockServerHelper.clearAllJobs();
    await mockServerHelper.clearFailureScenarios();
  });

  /**
   * 试验观点：Mock Server健康检查
   * 试验对象：Mock Server基本功能
   * 试验手順：
   * 1. 发送健康检查请求
   * 确认項目：
   * - 返回200状态码
   * - 返回健康状态信息
   */
  test('should respond to health check', async () => {
    const response = await fetch(`${mockServerHelper.getBaseUrl()}/health`);

    expect(response.status).toBe(200);

    const data = await response.json();
    expect(data.status).toBe('healthy');
    expect(data.timestamp).toBeDefined();
  });

  /**
   * 试験観点：作业创建API测试
   * 试験対象：PUT /subscriptions/.../jobs/{jobName} 端点
   * 试験手順：
   * 1. 发送作业创建请求
   * 2. 验证响应状态和内容
   * 3. 检查作业是否正确创建
   * 確認項目：
   * - 返回201状态码
   * - 作业信息正确返回
   * - 作业状态正确设置
   */
  test('should create automation job', async () => {
    const jobName = 'test-job-001';
    const runbookName = 'Test-Runbook';
    
    const requestBody = {
      properties: {
        runbook: {
          name: runbookName
        },
        parameters: {
          param1: 'value1',
          param2: 'value2'
        }
      }
    };

    const response = await fetch(
      `${mockServerHelper.getBaseUrl()}/subscriptions/test-sub/resourceGroups/test-rg/providers/Microsoft.Automation/automationAccounts/test-account/jobs/${jobName}`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      }
    );

    expect(response.status).toBe(201);
    
    const data = await response.json();
    expect(data.name).toBe(jobName);
    expect(data.properties.runbook.name).toBe(runbookName);
    expect(data.properties.status).toBe(MockJobStatus.New);
    expect(data.properties.parameters).toEqual(requestBody.properties.parameters);
  });

  /**
   * 试験観点：作业状态查询API测试
   * 试験対象：GET /subscriptions/.../jobs/{jobName} 端点
   * 试験手順：
   * 1. 创建测试作业
   * 2. 查询作业状态
   * 3. 验证返回的状态信息
   * 確認項目：
   * - 返回200状态码
   * - 作业状态信息正确
   * - 包含所有必要字段
   */
  test('should get job status', async () => {
    const jobName = 'test-job-002';
    
    // 先创建作业
    await fetch(
      `${mockServerHelper.getBaseUrl()}/subscriptions/test-sub/resourceGroups/test-rg/providers/Microsoft.Automation/automationAccounts/test-account/jobs/${jobName}`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          properties: {
            runbook: { name: 'Test-Runbook' },
            parameters: {}
          }
        })
      }
    );

    // 等待状态变化到Running
    await mockServerHelper.waitForJobStatus(jobName, MockJobStatus.Running, 2000);

    // 查询作业状态
    const response = await fetch(
      `${mockServerHelper.getBaseUrl()}/subscriptions/test-sub/resourceGroups/test-rg/providers/Microsoft.Automation/automationAccounts/test-account/jobs/${jobName}`,
      {
        method: 'GET'
      }
    );

    expect(response.status).toBe(200);
    
    const data = await response.json();
    expect(data.name).toBe(jobName);
    expect(data.properties.status).toBe(MockJobStatus.Running);
    expect(data.properties.startTime).toBeDefined();
    expect(data.properties.runbook.name).toBe('Test-Runbook');
  });

  /**
   * 试験観点：作业停止API测试
   * 试験対象：POST /subscriptions/.../jobs/{jobName}/stop 端点
   * 试験手順：
   * 1. 创建运行中的作业
   * 2. 发送停止请求
   * 3. 验证作业状态变为Stopped
   * 確認項目：
   * - 返回200状态码
   * - 作业状态正确变更为Stopped
   */
  test('should stop running job', async () => {
    const jobName = 'test-job-003';
    
    // 创建作业
    await fetch(
      `${mockServerHelper.getBaseUrl()}/subscriptions/test-sub/resourceGroups/test-rg/providers/Microsoft.Automation/automationAccounts/test-account/jobs/${jobName}`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          properties: {
            runbook: { name: 'Test-Runbook' },
            parameters: {}
          }
        })
      }
    );

    // 等待作业开始运行
    await mockServerHelper.waitForJobStatus(jobName, MockJobStatus.Running, 2000);

    // 停止作业
    const response = await fetch(
      `${mockServerHelper.getBaseUrl()}/subscriptions/test-sub/resourceGroups/test-rg/providers/Microsoft.Automation/automationAccounts/test-account/jobs/${jobName}/stop`,
      {
        method: 'POST'
      }
    );

    expect(response.status).toBe(200);
    
    const data = await response.json();
    expect(data.message).toContain(jobName);

    // 等待作业停止
    await mockServerHelper.waitForJobStatus(jobName, MockJobStatus.Stopped, 2000);
  });

  /**
   * 试験観点：不存在作业的错误处理
   * 试験対象：GET /subscriptions/.../jobs/{jobName} 端点
   * 试験手順：
   * 1. 查询不存在的作业
   * 2. 验证返回404错误
   * 確認項目：
   * - 返回404状态码
   * - 错误信息正确
   */
  test('should return 404 for non-existent job', async () => {
    const response = await fetch(
      `${mockServerHelper.getBaseUrl()}/subscriptions/test-sub/resourceGroups/test-rg/providers/Microsoft.Automation/automationAccounts/test-account/jobs/non-existent-job`,
      {
        method: 'GET'
      }
    );

    expect(response.status).toBe(404);
    
    const data = await response.json();
    expect(data.error.code).toBe('NotFound');
    expect(data.error.message).toContain('non-existent-job');
  });



  /**
   * 试験観点：作业状态手动设置测试（测试辅助功能）
   * 试験対象：PUT /mock/jobs/{jobName}/status 端点
   * 试験手順：
   * 1. 创建作业
   * 2. 手动设置作业状态
   * 3. 验证状态正确设置
   * 確認項目：
   * - 状态设置成功
   * - 作业状态正确更新
   */
  test('should allow manual job status setting', async () => {
    const jobName = 'test-job-004';
    
    // 创建作业
    await fetch(
      `${mockServerHelper.getBaseUrl()}/subscriptions/test-sub/resourceGroups/test-rg/providers/Microsoft.Automation/automationAccounts/test-account/jobs/${jobName}`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          properties: {
            runbook: { name: 'Test-Runbook' },
            parameters: {}
          }
        })
      }
    );

    // 手动设置状态为Completed
    await mockServerHelper.setJobStatus(jobName, MockJobStatus.Completed);

    // 验证状态
    const jobs = await mockServerHelper.getAllJobs();
    const job = jobs.find(j => j.name === jobName);
    expect(job).toBeDefined();
    expect(job.status).toBe(MockJobStatus.Completed);
  });

  /**
   * 试験観点：失败场景模拟测试
   * 试験対象：失败场景管理功能
   * 试験手順：
   * 1. 设置失败场景
   * 2. 发送请求验证失败响应
   * 3. 清理失败场景
   * 4. 验证正常响应恢复
   * 確認項目：
   * - 失败场景能够正确触发
   * - 返回预期的错误状态码和响应
   * - 清理后恢复正常
   */
  test('should simulate failure scenarios', async () => {
    const jobName = 'test-job-failure';
    const endpoint = `/subscriptions/test-sub/resourceGroups/test-rg/providers/Microsoft.Automation/automationAccounts/test-account/jobs/${jobName}`;

    // 设置失败场景：GET 请求返回 500 错误
    await mockServerHelper.setFailureScenario(
      endpoint,
      'GET',
      500,
      {
        error: {
          code: 'InternalServerError',
          message: 'Simulated server error'
        }
      }
    );

    // 验证失败场景被触发
    const failureResponse = await fetch(`${mockServerHelper.getBaseUrl()}${endpoint}`, {
      method: 'GET'
    });

    expect(failureResponse.status).toBe(500);
    const failureData = await failureResponse.json();
    expect(failureData.error.code).toBe('InternalServerError');
    expect(failureData.error.message).toBe('Simulated server error');

    // 清理失败场景
    await mockServerHelper.clearFailureScenarios();

    // 验证正常响应恢复（应该返回 404，因为作业不存在）
    const normalResponse = await fetch(`${mockServerHelper.getBaseUrl()}${endpoint}`, {
      method: 'GET'
    });

    expect(normalResponse.status).toBe(404);
    const normalData = await normalResponse.json();
    expect(normalData.error.code).toBe('NotFound');
  });

  /**
   * 试験観点：多种失败场景测试
   * 试験対象：不同HTTP方法的失败场景
   * 試験手順：
   * 1. 设置多个不同的失败场景
   * 2. 验证各种失败响应
   * 確認項目：
   * - 支持不同HTTP方法的失败模拟
   * - 支持不同状态码的模拟
   */
  test('should support multiple failure scenarios', async () => {
    // 设置创建作业失败场景
    await mockServerHelper.setFailureScenario(
      '/subscriptions/test-sub/resourceGroups/test-rg/providers/Microsoft.Automation/automationAccounts/test-account/jobs/fail-job',
      'PUT',
      400,
      {
        error: {
          code: 'BadRequest',
          message: 'Invalid runbook name'
        }
      }
    );

    // 设置停止作业失败场景
    await mockServerHelper.setFailureScenario(
      '/subscriptions/test-sub/resourceGroups/test-rg/providers/Microsoft.Automation/automationAccounts/test-account/jobs/fail-job/stop',
      'POST',
      403,
      {
        error: {
          code: 'Forbidden',
          message: 'Insufficient permissions'
        }
      }
    );

    // 验证创建作业失败
    const createResponse = await fetch(
      `${mockServerHelper.getBaseUrl()}/subscriptions/test-sub/resourceGroups/test-rg/providers/Microsoft.Automation/automationAccounts/test-account/jobs/fail-job`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          properties: {
            runbook: { name: 'Invalid-Runbook' }
          }
        })
      }
    );

    expect(createResponse.status).toBe(400);
    const createData = await createResponse.json();
    expect(createData.error.code).toBe('BadRequest');

    // 验证停止作业失败
    const stopResponse = await fetch(
      `${mockServerHelper.getBaseUrl()}/subscriptions/test-sub/resourceGroups/test-rg/providers/Microsoft.Automation/automationAccounts/test-account/jobs/fail-job/stop`,
      {
        method: 'POST'
      }
    );

    expect(stopResponse.status).toBe(403);
    const stopData = await stopResponse.json();
    expect(stopData.error.code).toBe('Forbidden');
  });
});
