/**
 * @fileoverview Azure Blob Storage関連サーバーアクションモジュール
 * @description
 * 管理項目定義のインポート時のAzure Blob Storageへの一時ファイルアップロード機能を提供。
 * アップロード失敗時の適切なエラー処理（EMEC0018）。
 * 受け付け処理失敗時の補償処理（アップロードしたファイルの削除）。
 *
 * Azure Blob Storageへの一時ファイルアップロード
 * パス: {currentUser.licenseId}/{AZURE_BLOB_PATHS.IMPORTS_PREFIX}/{生成したtaskId}/{FILE_NAMES.ASSETSFIELD_DEF_CSV}
 *
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { BlobSASPermissions, BlobServiceClient } from "@azure/storage-blob";
import { ENV, LOV_CODE_AZURE_STORAGE_SAS_TTL_SECONDS, PORTAL_ERROR_MESSAGES } from "../definitions";
import { ServerDataLov } from "../data/lov";
import Logger, { LogFunctionSignature } from "@/app/lib/logger";

/**
 * BlobServiceClient（接続文字列認証）のSingletonを管理するクラス
 *
 * 接続文字列を使用してBlobServiceClientインスタンスを管理し、
 * アプリケーション全体でリソース消費を最小限に抑える。
 */
class BlobServiceClientSingleton {
  private static instance: BlobServiceClient | null = null;

  /**
   * 接続文字列認証のBlobServiceClientインスタンスを取得する。
   */
  static getInstance(): BlobServiceClient {
    if (!this.instance) {
      if (!ENV.AZURE_STORAGE_CONNECTION_STRING) {
        Logger.error("Azure Storageの接続文字列が見つかりません。");
        throw new Error(PORTAL_ERROR_MESSAGES.EMEC0018);
      }
      this.instance = BlobServiceClient.fromConnectionString(ENV.AZURE_STORAGE_CONNECTION_STRING);
      Logger.info({ message: "BlobServiceClientシングルトンインスタンスを生成しました。" });
    }
    return this.instance;
  }

  static async close(): Promise<void> {
    if (this.instance) {
      // BlobServiceClientはcloseメソッドを持たないが、将来SDKが対応した場合はここで拡張すること。
      this.instance = null;
      Logger.info({ message: "BlobServiceClientシングルトンインスタンスをクローズしました。" });
    }
  }
}

/**
 * Azure Blob Storage関連のサーバーアクションを提供するクラス
 *
 * 管理項目定義のインポート (TASK_TYPE.MGMT_ITEM_IMPORT) タスクの特定処理：
 * - Azure Blob Storageへの一時ファイルアップロード
 *   パス: {currentUser.licenseId}/{AZURE_BLOB_PATHS.IMPORTS_PREFIX}/{生成したtaskId}/{FILE_NAMES.ASSETSFIELD_DEF_CSV}
 *   固定ファイル名: {FILE_NAMES.ASSETSFIELD_DEF_CSV}
 * - 失敗時: EMEC0018「ファイルのアップロードに失敗しました。時間をおいてから再度実行してください。」
 * - 補償処理: 受け付け処理失敗時のアップロードファイル削除
 *
 * ポータル画面からのファイルアップロード・ダウンロード・削除等の要求を一元的に受け付け、
 * サーバー側での厳格な入力検証・権限制御・Blob操作・補償処理を責任持って実施する。
 */
export class BlobActions {
  /**
   * Azure Blob StorageのSAS付きURLを生成する（レガシー用途のみ）。
   *
   * @param {string} containerName - コンテナ名。
   * @param {string[]} params - Blobパスの各セグメント。
   * @returns {Promise<string>} SAS付きBlob URL。
   * @throws 必要な環境変数やLOVが未設定の場合、またはBlob生成に失敗した場合はPORTAL_ERROR_MESSAGES.EMEC0007を返す。
   *
   * 注意：本メソッドは接続文字列を利用してSAS付きURLを生成する。
   */
  @LogFunctionSignature()
  static async generateBlobUrlWithSAS(containerName: string, params: string[]): Promise<string> {
    if (!ENV.AZURE_STORAGE_CONNECTION_STRING) {
      throw new Error("Azure Storageの接続文字列が見つかりません。");
    }
    const sasTtlLov = await ServerDataLov.fetchLov(
      LOV_CODE_AZURE_STORAGE_SAS_TTL_SECONDS,
    );
    if (!sasTtlLov) {
      Logger.error({
        message: `LOVでSAS設定[${LOV_CODE_AZURE_STORAGE_SAS_TTL_SECONDS}]が見つかりません。`,
      });
      throw new Error(PORTAL_ERROR_MESSAGES.EMEC0007);
    }
    // 接続文字列のシングルトンを利用
    const blobServiceClient = BlobServiceClientSingleton.getInstance();
    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blobClient = containerClient.getBlobClient(params.join("/"));
    const blobUrlWithSAS = await blobClient.generateSasUrl({
      expiresOn: new Date(Date.now() + Number(sasTtlLov.value) * 1000),
      permissions: BlobSASPermissions.parse("r"),
    });
    return blobUrlWithSAS;
  }

  /**
   * 指定されたファイルをAzure Blob Storageにアップロードする
   *
   * 管理項目定義のインポート時のAzure Blob Storageへの一時ファイルアップロード。
   * 入力パラメータのFileオブジェクトを、環境変数 AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF で指定されるコンテナへアップロード。
   * パス: {currentUser.licenseId}/{AZURE_BLOB_PATHS.IMPORTS_PREFIX}/{生成したtaskId}/{FILE_NAMES.ASSETSFIELD_DEF_CSV}
   * 固定ファイル名: {FILE_NAMES.ASSETSFIELD_DEF_CSV}
   * 失敗時: EMEC0018「ファイルのアップロードに失敗しました。時間をおいてから再度実行してください。」
   *
   * @param {File} file アップロードするファイルオブジェクト（管理項目定義CSVファイル）
   * @param {string} containerName アップロード先のコンテナ名（通常は assetsfield-def）
   * @param {string} blobName アップロード先のBlob名（パス含む）
   * @returns {Promise<string>} アップロードされたBlobのパス
   * @throws 環境変数未設定、またはアップロード失敗時はEMEC0018エラー
   */
  @LogFunctionSignature()
  static async uploadFile(file: File, containerName: string, blobName: string): Promise<string> {
    if (!ENV.AZURE_STORAGE_CONNECTION_STRING) {
      throw new Error("Azure Storageの接続文字列が見つかりません。");
    }
    // BlobServiceClientはシングルトンから取得
    const blobServiceClient = BlobServiceClientSingleton.getInstance();
    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);
    const arrayBuffer = await file.arrayBuffer();
    await blockBlobClient.uploadData(new Uint8Array(arrayBuffer), {
      blobHTTPHeaders: { blobContentType: file.type || "application/octet-stream" },
    });
    Logger.info(`ファイルアップロード成功: container=${containerName}, blob=${blobName}`);
    return blobName;
  }

  /**
   * 指定されたBlobをAzure Blob Storageから削除する
   *
   * 管理項目定義のインポートタスクの補償処理。
   * 受け付け処理失敗時の一時ファイル削除。
   * DBトランザクション失敗時またはService Bus送信失敗時に呼び出される。
   * アップロード済みファイルの確実な削除によるストレージリソース管理。
   *
   * @param {string} containerName 対象のコンテナ名
   * @param {string} blobName 削除するBlob名（パス含む）
   * @returns {Promise<void>} 削除完了時はresolve
   * @throws 環境変数未設定、または削除失敗時はEMEC0018エラー
   */
  @LogFunctionSignature()
  static async deleteBlob(containerName: string, blobName: string): Promise<void> {
    if (!ENV.AZURE_STORAGE_CONNECTION_STRING) {
      throw new Error("Azure Storageの接続文字列が見つかりません。");
    }
    // BlobServiceClientはシングルトンから取得
    const blobServiceClient = BlobServiceClientSingleton.getInstance();
    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blobClient = containerClient.getBlobClient(blobName);
    await blobClient.deleteIfExists();
    Logger.info(`Blob削除成功: container=${containerName}, blob=${blobName}`);
  }
} 