# 环境变量指南 (Environment Variables Guide)

## 1. 目的 (Purpose)

本文档旨在为“JCS 端点资产与任务管理系统”项目提供一个关于所有必需及可选环境变量的全面参考。它详细说明了每个环境变量的**名称、用途、预期格式/示例值、是否必需、哪些组件使用它、以及如何进行管理和配置**。

遵循良好的环境变量管理实践对于确保系统的可移植性、安全性、不同环境（开发、测试、生产）之间配置的一致性以及简化部署流程至关重要。

本文档是架构设计文档 (`docs/architecture/system-architecture.md`) 中关于配置管理章节的补充和详细阐述，也是各组件详细设计文档中“配置项”部分的权威参考。

## 2. 管理原则 (Management Principles)

1.  **严禁硬编码**: 任何可能随环境变化或包含敏感信息的配置值，都**不得**硬编码在应用程序代码中。必须通过环境变量注入。
2.  **生产环境敏感信息的安全配置**: 在Azure的生产和测试环境中，所有敏感信息（如数据库连接字符串、API密钥、仍需使用的连接字符串中的密钥部分、Client Secret等）**必须**通过Azure App Service或Azure Functions的“应用程序设置(Application Settings)”进行安全配置。这些设置在平台层面是加密存储的。
3.  **优先使用托管身份**: 对于支持的Azure服务（如Azure Service Bus, Azure Blob Storage），App Service和Azure Functions**必须**使用其托管身份进行认证，以避免在配置中存储和管理密钥。
4.  **本地开发环境的`.env`文件**: 本地开发时，可以使用`.env`或`.env.local`文件来存储环境变量，但这些文件**绝对不能**提交到版本控制系统 (应添加到`.gitignore`中)。本地开发访问Azure服务时，**必须**通过Azure CLI登录认证。
5.  **环境一致性与差异化**:
    *   确保所有环境（开发、测试、生产）都定义了必需的环境变量。
    *   允许不同环境使用不同的环境变量值。
    *   通过强制使用托管身份（云端）和Azure CLI登录（本地），减少环境间因连接字符串不同而引入的复杂性。
6.  **文档化**: 所有项目使用的环境变量都应在本指南中进行记录。新增或修改环境变量时，必须同步更新本文档。
7.  **命名约定**:
    *   使用大写字母和下划线 (`UPPER_SNAKE_CASE`) 作为环境变量的命名约定。

## 3. 环境变量列表 (List of Environment Variables)

以下是本项目各组件可能使用到的环境变量列表。

**图例:**
*   **必需?**: `是` - 该环境变量必须被设置，否则相关组件可能无法启动或正常工作。`否` - 该环境变量是可选的，若未设置，组件将使用内部定义的合理默认值或特定行为。
*   **敏感?**: `是` - 该变量包含敏感信息。若为连接字符串中的密钥，应通过平台安全机制配置。`否` - 该变量值非敏感。
*   **主要使用者**: 列出主要依赖此环境变量的系统组件。
*   **项目推荐/示例默认值**: 项目在设计或通用配置场景下推荐的默认值或格式示例。实际部署时可能根据环境调整。

---

### 3.1. az app service（Next.js 工程）

| 环境变量名 | 用途与说明 | 项目推荐/示例默认值 |
|------------|------------|---------------------|
| MSSQL_PRISMA_URL | Prisma ORM 连接 Azure SQL Database 的连接字符串（所有服务共用） | `sqlserver://<host>:<port>;database=<db>;user=<user>;password=******;encrypt=true;...` |
| APP_CACHE_TTL_SECONDS | Next.js 服务端缓存（unstable_cache）的默认生存时间（秒） | 7200 |
| AZURE_STORAGE_CONNECTION_STRING | 访问 Azure Storage 账户的连接字符串（主要用于兼容老接口或特殊场景） | `DefaultEndpointsProtocol=https;AccountName=...;AccountKey=...` |
| AZURE_STORAGE_ACCOUNT_NAME | 目标 Azure Storage 账户名称 | `jcsstorageprod` |
| AZURE_SERVICEBUS_NAMESPACE_HOSTNAME | 目标 Azure Service Bus 命名空间的 FQDN | `jcs-sb-prod.servicebus.windows.net` |
| AZURE_AUTOMATION_ACCOUNT_NAME | 目标 Azure Automation 账户名称 | `automation-prod` |
| JWT_MAX_AGE_SECONDS | 用户登录会话的最大有效时间（秒） | 1800 |
| LOG_LEVEL | 控制应用日志详细程度 | `info` |
| KEYCLOAK_INTERNAL_DOMAIN_NAME | Keycloak 内部网络可访问 URL | `http://keycloak.internal.example.com/auth` |
| KEYCLOAK_PUBLIC_DOMAIN_NAME | Keycloak 面向公网的 URL | `https://keycloak.example.com/auth` |
| KEYCLOAK_REALM | Keycloak Realm 名称 | `jp1endpoint` |
| KEYCLOAK_CLIENT | Next.js 在 Keycloak 注册的 Client ID | `public-web` |
| KEYCLOAK_REDIRECT_URL | Keycloak 认证成功后重定向回 Next.js 的 URL | `https://portal.example.com/api/auth/callback/keycloak` |
| KEYCLOAK_CLIENT_SECRET | Keycloak Client Secret（如为 confidential 类型） | (Sensitive Value) |
| AZURE_STORAGE_CONTAINER_OPLOGS | 操作日志导出结果 ZIP 文件的 Blob 容器名称 | `oplogs` |
| AZURE_STORAGE_CONTAINER_PRODUCT_MEDIAS | 产品媒体文件的 Blob 容器名称 | `product-medias` |
| AZURE_STORAGE_CONTAINER_PRODUCT_MANUALS | 产品手册 PDF 文件的 Blob 容器名称 | `product-manuals` |
| AZURE_STORAGE_CONTAINER_PROVIDED_FILES | 提供文件（如补丁、工具）的 Blob 容器名称 | `provided-files` |
| AZURE_STORAGE_CONTAINER_SUPPORT_FILES | 支持信息附件的 Blob 容器名称 | `support-files` |
| AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF | 管理项目定义导出/导入 CSV 的 Blob 容器名称 | `assetsfield-def` |
| SERVICE_BUS_TASK_INPUT_QUEUE_NAME | 新建后台任务消息的队列名称 | `task-input-queue` |
| SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME | Runbook 原始执行结果消息的队列名称 | `runbook-status-queue` |
| SERVICE_BUS_TASK_CONTROL_QUEUE_NAME | 任务控制命令（如取消任务）消息的队列名称 | `task-control-queue` |

---

### 3.2. az function（标准/长运行 Function 工程，合并）

| 环境变量名 | 用途与说明 | 项目推荐/示例默认值 |
|------------|------------|---------------------|
| MSSQL_PRISMA_URL | Prisma ORM 连接 Azure SQL Database 的连接字符串（所有服务共用） | `sqlserver://<host>:<port>;database=<db>;user=<user>;password=******;encrypt=true;...` |
| AZURE_STORAGE_CONNECTION_STRING | 访问 Azure Storage 账户的连接字符串 | `DefaultEndpointsProtocol=https;AccountName=...;AccountKey=...` |
| AZURE_STORAGE_ACCOUNT_NAME | 目标 Azure Storage 账户名称 | `jcsstorageprod` |
| AZURE_AUTOMATION_ACCOUNT_NAME | 目标 Azure Automation 账户名称 | `automation-prod` |
| AZURE_STORAGE_CONTAINER_OPLOGS | 操作日志导出结果 ZIP 文件的 Blob 容器名称 | `oplogs` |
| AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF | 管理项目定义导出/导入 CSV 的 Blob 容器名称 | `assetsfield-def` |
| SUBSCRIPTION_ID | Azure 订阅 ID | (Subscription Guid) |
| RESOURCE_GROUP_NAME | Azure 资源组名称 | (Resource Group Name) |
| AZURE_SERVICEBUS_NAMESPACE_HOSTNAME__fullyQualifiedNamespace | Service Bus 命名空间 FQDN（部分代码用此变量名） | `jcs-sb-prod.servicebus.windows.net` |
| SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME | Runbook 原始执行结果消息的队列名称 | `runbook-status-queue` |
| RUNBOOK_MONITOR_INTERVAL_SECONDS | Runbook 监控定时器的时间间隔（秒） | 300 |
| RUNBOOK_TIMEOUT_SECONDS | Runbook 允许的最大执行时间（秒） | 18000 |
| FUNCTION_TIMEOUT_SECONDS | Azure Functions 默认执行超时时间（秒） | 300 |
| RUNBOOK_MGMT_ITEM_IMPORT | 管理项目定义导入 Runbook 名称 | `Import-Management-Item` |
| RUNBOOK_MGMT_ITEM_EXPORT | 管理项目定义导出 Runbook 名称 | `Export-Management-Item` |
| RUNBOOK_OPLOG_EXPORT | 操作日志导出 Runbook 名称 | `Export-Operation-Log` |

## 4. 配置方法 (Configuration Methods)

### 4.1. 本地开发 (Local Development)

*   **Next.js 应用 与 Azure Functions 共用**:
    *   在项目根目录或各应用子目录（如 `apps/jcs-endpoint-nextjs/`）下使用 `.env.local` 文件（**此文件必须被 `.gitignore` 忽略**）。
    *   `local.settings.json` 主要用于Azure Functions本地调试时的特定配置，也可以包含`Values`部分来定义环境变量。

    **本地开发认证Azure服务（如Service Bus, Azure Blob Storage）的方式**:
    *   **必须通过 Azure CLI 登录**: 开发者在本地执行 `az login`。应用程序代码中的 `DefaultAzureCredential` (来自 `@azure/identity`) 会自动使用此登录凭据。
        *   访问 Service Bus: **必须配置** `AZURE_SERVICEBUS_NAMESPACE_HOSTNAME`。**不需要** Service Bus 连接字符串。
        *   访问 Azure Blob Storage: **必须配置** `AZURE_STORAGE_ACCOUNT_NAME`。如果连接本地Azurite或非标准端点，还需配置 `AZURE_STORAGE_BLOB_ENDPOINT` 等。**不需要**专门为Blob访问配置连接字符串。
        *   **注意**: `AZURE_STORAGE_CONNECTION_STRING` 仍需为本地开发时访问 **Azure Files** (例如，若使用Azurite模拟) 或其他必须用密钥的场景配置。

    示例 `.env.local` (部分):
    ```env
    # .env.local (Ensure this file is in .gitignore)

    # === Database (Current project uses connection string) ===
    MSSQL_PRISMA_URL="sqlserver://**************:65220;database=ph5;user=SA;password=yourStrong(!)Password;encrypt=false;"

    # === Azure Service Bus (Local dev: Authenticate via Azure CLI login) ===
    AZURE_SERVICEBUS_NAMESPACE_HOSTNAME="your-dev-sb-namespace.servicebus.windows.net"

    # === Azure Storage (Local dev: Authenticate Blob access via Azure CLI login) ===
    AZURE_STORAGE_ACCOUNT_NAME="yourdevstorageaccount"
    # Optional, if using Azurite or non-standard Blob endpoint for local dev:
    # AZURE_STORAGE_BLOB_ENDPOINT="http://127.0.0.1:10000/yourdevstorageaccount"
    
    # Required for Azure Files workspace access (e.g., with Azurite) 
    # or other key-based Storage access during local development.
    AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;FileEndpoint=http://127.0.0.1:10002/devstoreaccount1;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;"

    # === Azure Automation (Local dev: Connect to a dev Automation Account) ===
    AZURE_AUTOMATION_ACCOUNT_NAME="automation-dev"

    # === Azure Blob Storage Container Names (Should match section 3.2) ===
    AZURE_STORAGE_CONTAINER_OPLOGS="oplogs-dev"
    # ... other container names ...
    AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF="assetsfield-def-dev"

    # === Azure Service Bus Queue Names (Should match section 3.3) ===
    SERVICE_BUS_TASK_INPUT_QUEUE_NAME="task-input-queue-dev"
    # ... other queue names ...
    SERVICE_BUS_TASK_CONTROL_QUEUE_NAME="task-control-queue-dev"
    
    # === Keycloak (Local dev) ===
    KEYCLOAK_PUBLIC_DOMAIN_NAME="http://localhost:8080/auth"
    # ... other Keycloak variables ...
    
    # === NextAuth.js (Local dev) ===
    NEXTAUTH_URL="http://localhost:3000"
    NEXTAUTH_SECRET="aStrongRandomSecretForLocalDevelopment" 

    # === Application Behavior ===
    LOG_LEVEL="debug"
    APP_CACHE_TTL_SECONDS="60"
    # ... other behavior variables ...
    ```

### 4.2. Azure 部署环境 (Azure Deployed Environments - Test, Prod)

*   **Azure App Service (for Next.js) & Azure Functions**:
    *   **应用设置 (Application Settings)**: 所有环境变量都**必须**通过目标App Service或Function App的“配置” -> “应用程序设置”进行配置。
    *   Azure平台会安全地管理这些应用设置，并在运行时将其作为环境变量注入到应用程序中。
    *   **托管身份认证**:
        *   对于 **Azure Service Bus**：App Service/Function App **必须**启用托管身份，并在代码中使用 `DefaultAzureCredential`。**必须配置** `AZURE_SERVICEBUS_NAMESPACE_HOSTNAME`。Service Bus 连接字符串**不应配置**。
        *   对于 **Azure Blob Storage**：App Service/Function App **必须**启用托管身份，并在代码中使用 `DefaultAzureCredential` 访问Blob。**必须配置** `AZURE_STORAGE_ACCOUNT_NAME`。
        *   对于 **Azure Files** (工作区访问) 及其他必需场景：仍需配置 `AZURE_STORAGE_CONNECTION_STRING`。
        *   对于 **Azure SQL Database**: 当前项目保持使用 `MSSQL_PRISMA_URL` 连接字符串。
    *   **对于极其敏感的值（如数据库密码、存储账户密钥），即使在应用设置中，也可以考虑进一步将其存储在Azure Key Vault中，然后应用设置仅配置对Key Vault机密的引用。**

*   **Azure Automation Account**:
    *   Runbook通常通过参数接收动态值。如果Runbook需要访问固定的配置或凭据（例如访问Azure Files工作区的存储账户密钥），应使用Automation账户内的“变量 (Variables)”或“凭据 (Credentials)”资产安全地存储这些信息。

## 5. 更新与维护 (Updating and Maintaining)

*   当项目中引入新的环境变量，或现有环境变量的用途、格式发生变化时，**必须立即更新本文档**。
*   在进行代码审查时，应检查是否有未文档化的环境变量被引入。
*   定期审查此列表，确保其准确性和完整性。

---
本文档为环境变量的统一管理提供了指导。清晰、准确的环境变量文档是确保系统顺利开发、部署和运维的关键。