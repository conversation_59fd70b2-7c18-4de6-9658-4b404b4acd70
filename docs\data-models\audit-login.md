# 数据模型: 登录审计日志 (AuditLogin)

*   **表名 (逻辑名)**: `AuditLogin`
*   **对应UI界面**: N/A (主要用于后端审计，部分信息可能间接展示在用户活动相关的界面)
*   **主要用途**: 记录用户登录和登出相关的审计日志信息。根据系统设计，登录失败的详细日志由Keycloak记录。

## 1. 字段定义

| 字段名 (Prisma/英文) | 数据类型 (SQL参考) | PK | FK | UQ | Nullable | Default     | 描述 (中文)                                                                 |
| :--------------------- | :----------------- | :--- | :--- | :--- | :------- | :---------- | :-------------------------------------------------------------------------- |
| `id`                   | VARCHAR(36)        | ●    |      |      |          | `cuid()`    | 主键。CUID格式，由系统（Prisma的`@default(cuid())`机制）自动生成。              |
| `auditType`            | VARCHAR(XX)        |      |      |      |          |             | 审计事件的类型。例如："LOGIN_SUCCESS", "LOGOUT"。具体值由应用代码逻辑定义。       |
| `userId`               | VARCHAR(50)        |      |      |      |          |             | 执行此操作的用户的ID (通常来自Keycloak的用户标识)。                               |
| `loginMessage`         | NVARCHAR(MAX)      |      |      |      |          |             | 记录与审计事件相关的详细信息或消息文本。                                          |
| `createdBy`            | VARCHAR(XX)        |      |      |      |          | `"system"`  | 此审计记录的创建者。Prisma Schema中默认为 "system"。                           |
| `createdAt`            | DATETIME           |      |      |      |          | `now()`     | 审计事件发生的时间（即记录创建时间）。Prisma Schema中默认为当前时间。               |

*PK: Primary Key, FK: Foreign Key, UQ: Unique Key*
*XX表示具体长度，取决于实际的数据库Schema定义。*

## 2. 关系

*   此模型在提供的Prisma Schema中未定义与其他表（如用户表）的显式数据库外键关系。与用户的关联通过 `userId` 字段进行逻辑关联。

## 3. 索引 (示例)

*   `PRIMARY KEY (id)`
*   `INDEX idx_auditlogin_userid (userId)` (如果经常按用户ID查询)
*   `INDEX idx_auditlogin_createdat (createdAt DESC)` (便于按时间倒序查询最新的审计日志)
*   `INDEX idx_auditlogin_audittype (auditType)` (如果经常按审计类型查询)