/**
 * @fileoverview タスク実行タイムアウト処理関数 (TaskExecuteTimeoutFunc)
 * @description
 * TaskExecuteFuncの実行が例外やタイムアウトによって失敗し、TaskInputQueueのDLQへ
 * 配信されたメッセージを処理する補償関数。Azure Automationジョブの存在確認により
 * 補償の必要性を判定し、必要な場合のみタスク状態のクリーンアップとリソース解放を行う。
 *
 * @trigger Azure Service Bus - TaskInputQueue のDLQメッセージ
 * @input TaskInputQueue のDLQから受信する、元のタスク実行要求メッセージ
 * @output Azure SQL Database (Task、ContainerConcurrencyStatus) のレコード更新、
 *         Azure Files上のタスク作業ディレクトリ削除
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { app, InvocationContext } from "@azure/functions";
import { prisma } from "../lib/prisma";
import { AppConstants } from "../lib/constants";
import { createShareServiceClient, getAutomationJobStatus } from "../lib/azureClients";
import { deleteTaskWorkspaceDirectory } from "../lib/utils";

/**
 * TaskExecuteTimeoutFunc - タスク実行タイムアウト時の補償処理
 *
 * 処理ステップ:
 * 1-2. DLQメッセージ受信・解析、taskId検証
 * 3. Azure Automationジョブ存在確認（補償要否判定）
 * 4. Azure Files作業ディレクトリ削除
 * 5. タスク情報取得
 * 6. コンテナ実行状態更新（IDLE）
 * 7. タスクステータス確認（PENDING_CANCELLATION/CANCELLED判定）
 * 8. タスクテーブル更新（EMET0005、条件付き）
 * 9. 処理完了ログ記録
 *
 * エラー処理:
 * taskId不正・API呼び出し失敗・DB取得失敗時は処理終了、
 * その他の失敗時はログ記録して処理継続。
 */
export async function TaskExecuteTimeoutFunc(message: unknown, context: InvocationContext): Promise<void> {
  let taskId: string | undefined;

  try {
    // 1. Azure Service Bus の TaskInputQueue/$DeadLetterQueue からメッセージを受信し、解析する
    context.log("[TaskExecuteTimeoutFunc] TaskInputQueue/$DeadLetterQueue からメッセージ受信");

    // メッセージ基本校验
    if (!message || typeof message !== 'object') {
      context.error("[TaskExecuteTimeoutFunc] メッセージが不正です。処理を終了します。");
      return;
    }

    // 2. メッセージ内の基本パラメータ（taskId）の存在と値が空であるかを確認する
    const messageBody = message as { taskId?: string };
    const extractedTaskId = messageBody.taskId;

    if (!extractedTaskId || typeof extractedTaskId !== "string") {
      context.error("[TaskExecuteTimeoutFunc] taskIdが不足/不正です。処理を終了します。");
      return;
    }

    taskId = extractedTaskId;
    context.log(`[TaskExecuteTimeoutFunc] 受信taskId: ${taskId}`);

    // 3. Azure Automationのジョブ取得APIを呼び出して、タスク実行関数によってジョブが作成されたか確認する
    // taskId=jobName を使用して、Azure Automationのジョブ取得APIを呼び出してジョブの存在を問い合わせる
    try {
      context.log(`[TaskExecuteTimeoutFunc] Azure Automationジョブ取得API呼び出し: jobName=${taskId}`);
      const jobResult = await getAutomationJobStatus(taskId);

      if (jobResult.exists) {
        // ジョブが存在している場合はHTTPステータスコードが200（OK）のレスポンスが返却される
        // この場合は補償不要のログを出力して、処理を終了する
        context.log(`[TaskExecuteTimeoutFunc] Azure Automationジョブが存在するため補償処理は不要です: ${taskId}`);
        return;
      } else {
        // ジョブが存在しない場合はHTTPステータスコードが404のレスポンスが返却される。この場合はステップ4.に進める
        context.log(`[TaskExecuteTimeoutFunc] Azure Automationジョブが存在しないため補償処理を開始します: ${taskId}`);
      }
    } catch (getError: any) {
      // API の呼び出しに失敗した場合は、HTTPステータスコードが200（OK）または404以外のレスポンスが返却される
      // この場合はエラー詳細をログに記録した後、処理を終了する
      context.error(`[TaskExecuteTimeoutFunc] Azure Automationジョブ取得API呼び出し失敗:`, getError);
      return;
    }

    // 4. Azure Files上のタスク作業ディレクトリ（TaskWorkspaces/<taskId>/）の削除を試みる
    try {
      const shareServiceClient = createShareServiceClient();
      const shareClient = shareServiceClient.getShareClient(AppConstants.AZURE_FILES.TASK_WORKSPACES_SHARE);
      const taskDirClient = shareClient.getDirectoryClient(taskId);
      await deleteTaskWorkspaceDirectory(taskDirClient, context);
      context.log(`[TaskExecuteTimeoutFunc] Azure Files作業ディレクトリ削除成功: TaskWorkspaces/${taskId}/`);
    } catch (err: any) {
      // 削除に失敗した場合はエラーをログに記録するが、後続処理は継続する
      context.log(`[TaskExecuteTimeoutFunc] Azure Files作業ディレクトリ削除失敗: ${err.message}`);
    }

    // 5. taskId を使用して Task テーブルを検索し、タスクの情報を取得する
    const task = await prisma.task.findUnique({ where: { id: taskId } });
    if (!task) {
      // DB読み取り失敗、またはタスクレコードが存在しない場合：エラー詳細をログに出力して、処理を終了する
      context.error(`[TaskExecuteTimeoutFunc] taskId=${taskId} のタスクがデータベースに存在しません。処理を終了します。`);
      return;
    }

    context.log(`[TaskExecuteTimeoutFunc] タスク情報取得成功: ${taskId}, status: ${task.status}`);

    // 6. コンテナ実行状態ContainerConcurrencyStatus テーブルの対象コンテナレコードを更新する
    // 条件：対象VM名と対象コンテナ名がステップ5.で取得した情報と一致し、ステータスがBUSYで、使用中のタスクID（currentTaskId）が入力のtaskIdと一致する
    try {
      const updateContainerResult = await prisma.containerConcurrencyStatus.updateMany({
        where: {
          targetVmName: task.targetVmName || undefined,
          targetContainerName: task.targetContainerName || undefined,
          status: "BUSY",
          currentTaskId: taskId,
        },
        data: {
          status: "IDLE",
          currentTaskId: null,
        },
      });

      if (updateContainerResult.count === 0) {
        // 更新失敗または更新件数が0件の場合はエラーをログに記録するが、後続処理は継続する
        context.log(`[TaskExecuteTimeoutFunc] コンテナ実行状態テーブル更新件数0件: VM=${task.targetVmName}, Container=${task.targetContainerName}`);
      } else {
        context.log(`[TaskExecuteTimeoutFunc] コンテナ実行状態テーブル更新成功: VM=${task.targetVmName}, Container=${task.targetContainerName} -> IDLE`);
      }
    } catch (err: any) {
      // 更新失敗または更新件数が0件の場合はエラーをログに記録するが、後続処理は継続する
      context.log(`[TaskExecuteTimeoutFunc] コンテナ実行状態テーブル更新失敗: ${err.message}`);
    }

    // 7. タスクのステータスがPENDING_CANCELLATIONまたはCANCELLEDであるかチェックする
    if (task.status === AppConstants.TaskStatus.PendingCancellation || task.status === AppConstants.TaskStatus.Cancelled) {
      // PENDING_CANCELLATIONまたはCANCELLEDの場合：ステップ8.をスキップして、ステップ9.に進める
      context.log(`[TaskExecuteTimeoutFunc] タスクステータスが ${task.status} のため、EMET0005エラー更新をスキップします: ${taskId}`);
    } else {
      // 8. タスクTask テーブルの該当タスクレコードを更新する
      // 条件：IDが入力のtaskIdと一致し、最終更新日時がステップ5.で取得した最終更新日時と一致する
      try {
        const updateTaskResult = await prisma.task.updateMany({
          where: {
            id: taskId,
            updatedAt: task.updatedAt // 楽観ロック条件
          },
          data: {
            status: AppConstants.TaskStatus.CompletedError,
            errorCode: AppConstants.ERROR_CODES.EMET0005,
            resultMessage: AppConstants.TASK_ERROR_MESSAGE.EMET0005,
          },
        });

        if (updateTaskResult.count === 0) {
          // 更新失敗または更新件数が0件の場合はエラーをログに記録するが、後続処理は継続する
          context.log(`[TaskExecuteTimeoutFunc] タスクテーブル更新件数0件: ${taskId}`);
        } else {
          context.log(`[TaskExecuteTimeoutFunc] タスクテーブル更新成功: ${taskId} -> COMPLETED_ERROR/EMET0005`);
        }
      } catch (err: any) {
        // 更新失敗または更新件数が0件の場合はエラーをログに記録するが、後続処理は継続する
        context.log(`[TaskExecuteTimeoutFunc] タスクテーブル更新失敗: ${err.message}`);
      }
    }

    // 9. 処理完了のログを出力して、処理を終了する（メッセージACK）
    context.log(`[TaskExecuteTimeoutFunc] タスクID ${taskId} のタイムアウト補償処理が正常に完了しました。`);
  } catch (err: any) {
    // 予期せぬ内部エラー：エラーログ記録。処理終了。（メッセージACK）
    context.error(`[TaskExecuteTimeoutFunc] 予期せぬ内部エラーが発生しました:`, err);
    context.log(`[TaskExecuteTimeoutFunc] タスクID ${taskId || 'unknown'} の処理を終了します。`);
  }
}

// FunctionとService Busキューのバインド設定
app.serviceBusQueue("TaskExecuteTimeoutFunc", {
  connection: "AZURE_SERVICEBUS_NAMESPACE_HOSTNAME",
  queueName: "%SERVICE_BUS_TASK_INPUT_QUEUE_NAME%/$DeadLetterQueue",
  handler: TaskExecuteTimeoutFunc,
}); 