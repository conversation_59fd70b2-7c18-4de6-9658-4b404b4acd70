{
  "compilerOptions": {
    "target": "ES2022",
    "module": "CommonJS",
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": false,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "outDir": "./dist",
    "rootDir": "./",
    "resolveJsonModule": true,
    "types": ["node"],
    "lib": ["ES2022"]
  },
  "include": [
    "**/*.ts",
    "**/*.json"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ],

}
