/**
 * @fileoverview Server Action 統一実行フック
 * @description 認証エラーの自動処理とリダイレクトを提供する
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

"use client";

import { useState, useCallback } from "react";
import { useRouter } from "next/navigation";

/**
 * Server Action の実行結果型
 */
export interface ServerActionResult {
  success: boolean;
  message?: string;
  errors?: Record<string, string>;
  data?: any;
}

/**
 * useServerAction フックのオプション
 */
export interface UseServerActionOptions {
  onSuccess?: (result: ServerActionResult) => void;
  onError?: (error: Error) => void;
  onAuthError?: () => void;
}

/**
 * Server Action 統一実行フック
 * 
 * 認証エラーを自動的に検出し、ログインページにリダイレクトする。
 * その他のエラーは通常のエラーハンドリングを行う。
 * 
 * @param options - フックのオプション
 * @returns Server Action 実行用のオブジェクト
 */
export function useServerAction(options: UseServerActionOptions = {}) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const router = useRouter();

  const execute = useCallback(async (
    serverAction: () => Promise<ServerActionResult>
  ): Promise<ServerActionResult | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await serverAction();
      
      if (result.success) {
        options.onSuccess?.(result);
      } else {
        // Server Action が失敗を返した場合
        const error = new Error(result.message || "Server Action failed");
        setError(error);
        options.onError?.(error);
      }
      
      return result;
    } catch (error) {
      const err = error as Error;

      // 認証エラーかチェック
      if (isAuthenticationError(err)) {
        // 認証エラーコールバックを呼び出し
        options.onAuthError?.();

        // ログインページにリダイレクト
        router.push("/login");

        return null;
      }

      // その他のエラー
      setError(err);
      options.onError?.(err);

      return null;
    } finally {
      setIsLoading(false);
    }
  }, [options, router]);

  return {
    execute,
    isLoading,
    error,
  };
}

/**
 * エラーが認証エラーかどうかを判定する
 * @param error - 判定するエラー
 * @returns 認証エラーの場合 true
 */
function isAuthenticationError(error: Error): boolean {
  const errorMessage = error.message || "";

  // middleware が 401 を返した場合に発生するエラーパターンをチェック
  return (
    errorMessage.includes("Unauthorized") ||
    errorMessage.includes("401")
  );
}
