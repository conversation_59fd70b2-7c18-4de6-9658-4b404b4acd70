/**
 * LOV（List of Values）测试数据管理辅助模块
 * 用于 Playwright 测试中初始化、管理和清理 LOV 种子数据
 */

import { PrismaClient } from '@prisma/client';
import lovDataJson from './lovs.json';

// 创建 Prisma 客户端实例
const prisma = new PrismaClient();

/**
 * LOV 数据项接口
 */
export interface LovDataItem {
  code: string;
  name: string;
  value: string;
  parentCode?: string;
}

/**
 * 获取 LOV 种子数据
 * @returns LOV 数据数组
 */
export function getLovSeedData(): LovDataItem[] {
  return lovDataJson as LovDataItem[];
}

/**
 * 初始化 LOV 种子数据到测试数据库
 * 此函数会清理现有 LOV 数据并重新插入种子数据
 */
export async function initializeLovSeedData(): Promise<void> {
  try {
    console.log('🌱 开始初始化 LOV 种子数据...');
    
    // 1. 清理现有 LOV 数据
    await prisma.lov.deleteMany({});
    console.log('✅ 已清理现有 LOV 数据');
    
    // 2. 加载种子数据
    const lovData = getLovSeedData();
    console.log(`📋 加载了 ${lovData.length} 条 LOV 数据`);

    // 3. 按层级顺序插入数据（先插入父级，再插入子级）
    const parentLovs = lovData.filter((item: LovDataItem) => !item.parentCode);
    const childLovs = lovData.filter((item: LovDataItem) => item.parentCode);
    
    // 插入父级 LOV
    for (const lov of parentLovs) {
      await prisma.lov.create({
        data: {
          code: lov.code,
          name: lov.name,
          value: lov.value,
          parentCode: null,
          isEnabled: true
        }
      });
    }
    console.log(`✅ 已插入 ${parentLovs.length} 条父级 LOV 数据`);
    
    // 插入子级 LOV
    for (const lov of childLovs) {
      await prisma.lov.create({
        data: {
          code: lov.code,
          name: lov.name,
          value: lov.value,
          parentCode: lov.parentCode!,
          isEnabled: true
        }
      });
    }
    console.log(`✅ 已插入 ${childLovs.length} 条子级 LOV 数据`);
    
    console.log('🎉 LOV 种子数据初始化完成');
    
  } catch (error) {
    console.error('❌ LOV 种子数据初始化失败:', error);
    throw error;
  }
}

/**
 * 验证 LOV 数据是否正确初始化
 * @returns 验证结果
 */
export async function validateLovData(): Promise<boolean> {
  try {
    const lovData = getLovSeedData();
    const dbLovCount = await prisma.lov.count();
    
    if (dbLovCount !== lovData.length) {
      console.error(`❌ LOV 数据数量不匹配: 期望 ${lovData.length}, 实际 ${dbLovCount}`);
      return false;
    }
    
    // 验证关键的 LOV 数据是否存在
    const criticalLovs = [
      'TASK_STATUS',
      'TASK_TYPE', 
      'SERVER_TYPE',
      'TASK_STATUS.QUEUED',
      'TASK_STATUS.COMPLETED_SUCCESS',
      'TASK_TYPE.OPLOG_EXPORT'
    ];
    
    for (const code of criticalLovs) {
      const lov = await prisma.lov.findUnique({
        where: { code }
      });
      
      if (!lov) {
        console.error(`❌ 关键 LOV 数据缺失: ${code}`);
        return false;
      }
    }
    
    console.log('✅ LOV 数据验证通过');
    return true;
    
  } catch (error) {
    console.error('❌ LOV 数据验证失败:', error);
    return false;
  }
}

/**
 * 获取指定父级代码下的所有 LOV 项
 * @param parentCode 父级代码
 * @returns LOV 项数组
 */
export async function getLovsByParentCode(parentCode: string): Promise<any[]> {
  return await prisma.lov.findMany({
    where: {
      parentCode,
      isEnabled: true
    },
    orderBy: {
      code: 'asc'
    }
  });
}

/**
 * 获取指定代码的 LOV 项
 * @param code LOV 代码
 * @returns LOV 项或 null
 */
export async function getLovByCode(code: string): Promise<any | null> {
  return await prisma.lov.findUnique({
    where: {
      code,
      isEnabled: true
    }
  });
}

/**
 * 清理所有 LOV 测试数据
 */
export async function cleanupLovData(): Promise<void> {
  try {
    await prisma.lov.deleteMany({});
    console.log('✅ 已清理所有 LOV 测试数据');
  } catch (error) {
    console.error('❌ 清理 LOV 数据失败:', error);
    throw error;
  }
}

/**
 * 关闭 Prisma 客户端连接
 */
export async function disconnectLovPrisma(): Promise<void> {
  await prisma.$disconnect();
}
