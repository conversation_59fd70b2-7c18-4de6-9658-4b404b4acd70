/**
 * @fileoverview Keycloak認証コールバック処理を行うReactコンポーネント
 * @description 認証後のリダイレクト処理と契約情報の検証を実行する
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

"use client";

import clsx from "clsx";
import { useRouter } from "next/navigation"; // Next.jsのルーター
import { useEffect, useState, useRef } from "react";
import Spinner from './spinner';

// URLから許可コードを抽出する
function extractCodeFromUrl(url: string) {
    let codeIndex = url.indexOf('code=');
    if (codeIndex !== -1) {
        let code = url.substring(codeIndex + 5);
        return code;
    }
    return null;
}

/**
 * Keycloak認証コールバック処理コンポーネント
 * 認証後のリダイレクト処理と契約情報の検証を実行する
 * React厳格モードでの重複実行を防ぐため、useRefを使用した制御を実装
 * @returns JSX要素（ローディングスピナー）
 */
export default function CallBackForm() {
    const [loading, setLoading] = useState(false); // ローディング状態の管理
    const router = useRouter(); // ルーターのインスタンス取得
    const hasRun = useRef(false); // 重複実行防止フラグ

    useEffect(() => {
        // React厳格モードでの重複実行を防ぐ
        if (hasRun.current) return;
        hasRun.current = true;

        async function fetchData() {
            setLoading(true); // ローディング状態をtrueに設定
            // リダイレクト後のURLを取得
            try {
                const redirectUrl = window.location.href;
                if (!redirectUrl) {
                    throw new Error;
                }
                
                // 許可コードの抽出
                const code = extractCodeFromUrl(redirectUrl);
                if (!code) {
                    throw new Error;
                }
                
                // 解析後に取得したユーザ情報による契約情報の検証
                const callback = await fetch("/api/callback", {
                    method: "POST", // POSTリクエスト
                    headers: {
                        "Content-Type": "application/json", // JSON形式のヘッダー
                    },
                    body: JSON.stringify({
                        code: code,
                        tz: Intl.DateTimeFormat().resolvedOptions().timeZone,
                    }),
                });
                // API実行結果判定
                if (callback.ok) {
                    const result = await callback.json();
                    // 契約認証結果判定
                    if (result.status === 401) {
                        // 契約失効ログイン禁止
                        await fetch("/api/logout", {
                            method: "POST", // POSTリクエスト
                            headers: {
                                "Content-Type": "application/json", // JSON形式のヘッダー
                            }
                        });
                        router.push("/login?error05");
                    } else if (result.status === 200) {
                        router.refresh(); // ページを更新
                        router.push("/dashboard"); // ホームページにリダイレクト
                    } else {
                        throw new Error;
                    }
                } else {
                    throw new Error;
                }
            } catch (error) {
                // エラー時は実行フラグをリセットして再試行を可能にする
                hasRun.current = false;

                // ログアウト処理
                await fetch("/api/logout", {
                    method: "POST", // POSTリクエスト
                    headers: {
                        "Content-Type": "application/json", // JSON形式のヘッダー
                    }
                });

                router.push("/login?error07");
            } finally {
                // ローディング状態をfalseに設定
                setLoading(false);
            }
        }
        fetchData();
    }, [router]);
    return (
        <div>
        <Spinner
        className={clsx("inline-block mr-2", { hidden: !loading })}
        /></div>
        );
    }