/**
 * @file route.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2024, 2025, Hitachi Solutions, Ltd.
 */

import Logger from "@/app/lib/logger";
import { handleApiError } from "@/app/lib/portal-error";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";
import { ENV, PORTAL_ERROR_MESSAGES } from "../../lib/definitions";


// POSTメソッドの実装
export async function POST() {
    try {
        // セッションオブジェクトの初期化
        const session = await getIronSession<SessionData>(cookies(), sessionOptions);
        // KEYCLOAKのREALM
        const realm = ENV.KEYCLOAK_REALM;
        // KEYCLOAKのCLIENT
        const clientId = ENV.KEYCLOAK_CLIENT;
        // KEYCLOAKサービスドメイン
        const domainName = ENV.KEYCLOAK_INTERNAL_DOMAIN_NAME;
        // KEYCLOAKサーバのクライアント認証器
        const clientSecret = ENV.KEYCLOAK_CLIENT_SECRET;
        // プロファイル内容取得検証
        if (!realm || !clientId || !domainName || !clientSecret) {
            Logger.error({ message: PORTAL_ERROR_MESSAGES.EMEC0007 });
            return NextResponse.json(
                { status: 400 }
            );
        }

        // APIアクセスパラメータ
        const params = new URLSearchParams();
        // 権限タイプ
        params.append('grant_type', 'refresh_token');
        // KEYCLOAKのトークンのリフレッシュ
        params.append('refresh_token', session.user.refreshToken);
        // KEYCLOAKのCLIENT
        params.append('client_id', clientId);
        // Keycloakサーバのクライアント認証器
        params.append('client_secret', clientSecret);
        // APIアクセスアドレス
        const url = `${domainName}/realms/${realm}/protocol/openid-connect/token`;

        // オブジェクトheadersを作成する
        const headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        };
        // APIアクセス
        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: params.toString(),
        });

        if (response.ok) {
            // 取得結果json形式変換
            const data = await response.json();

            const url = `${domainName}/admin/realms/${realm}/users/${session.user.id}/logout`;
            // オブジェクトheadersを作成する
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${data.access_token}`
            };

            // APIアクセス
            await fetch(url, {
                method: 'POST',
                headers: headers,
                body: params.toString(),
            });
            
        }
        return NextResponse.json({ status: 200 });
    } catch (error) {
        // エラーが発生した場合はエラーハンドリング関数に委譲
        return handleApiError(error);
    } finally {
        // セッションオブジェクトの初期化
        const session = await getIronSession<SessionData>(cookies(), sessionOptions);
        // セッションの破棄
        await session.destroy();
    }
}
