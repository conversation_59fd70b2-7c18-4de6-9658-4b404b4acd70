---
type: "always_apply"
---

# `jcs-endpoint-monorepo` 项目开发指南

## 摘要

这些是 `jcs-endpoint-monorepo` 项目的权威指南，适用于 `apps/` 目录下的所有应用，包括 `jcs-endpoint-nextjs`, `jcs-backend-services-standard` 和 `jcs-backend-services-long-running`。所有由 AI 生成或修改的代码都必须严格遵守这些规则。核心原则包括：遵循 Airbnb 编码风格、编写高质量的日文文档、坚持项目既定架构、并确保代码的可维护性和一致性。

**核心信息来源 (SSoT - Single Source of Truth):**
*   **设计文档 (最终权威):** Monorepo 根目录下的 `docs-delivery/` 目录是所有功能、组件、API 和架构设计的 **最终权威来源**。这些是经过客户评审和修订的日文交付文档。
*   **数据模型:** `apps/jcs-endpoint-nextjs/prisma/schema.prisma` 是数据库结构的唯一真实来源。
*   **核心定义:** `apps/jcs-endpoint-nextjs/app/lib/definitions.ts` 是 Next.js 应用核心 TypeScript 类型、常量和枚举的唯一真实来源。

---

## 目录
- [`jcs-endpoint-monorepo` 项目开发指南](#jcs-endpoint-monorepo-项目开发指南)
  - [摘要](#摘要)
  - [目录](#目录)
  - [1. 代码风格 (Airbnb JavaScript Style Guide)](#1-代码风格-airbnb-javascript-style-guide)
  - [2. 注释规范 (关键要求)](#2-注释规范-关键要求)
    - [2.1. 通用规则](#21-通用规则)
    - [2.2. 文件头注释](#22-文件头注释)
    - [2.3. JSDoc 规范](#23-jsdoc-规范)
    - [2.4. JSX 注释 (仅限 Next.js, 绝对规则)](#24-jsx-注释-仅限-nextjs-绝对规则)
    - [2.5. 单元测试注释 (Jest)](#25-单元测试注释-jest)
    - [2.6. 内容与交付文档对齐及自包含原则](#26-内容与交付文档对齐及自包含原则)
  - [3. 项目结构与命名约定](#3-项目结构与命名约定)
  - [4. 通用工作流与命令执行 (关键原则)](#4-通用工作流与命令执行-关键原则)
  - [5. 技术栈与核心原则](#5-技术栈与核心原则)
  - [6. 数据管理 (Prisma)](#6-数据管理-prisma)
  - [7. 后端逻辑与端点](#7-后端逻辑与端点)
    - [7.1. Next.js (Server Actions \& API Routes)](#71-nextjs-server-actions--api-routes)
    - [7.2. Azure Functions](#72-azure-functions)
  - [8. 错误处理与日志记录](#8-错误处理与日志记录)
    - [8.1. Next.js 应用](#81-nextjs-应用)
    - [8.2. Azure Functions 应用](#82-azure-functions-应用)
  - [9. UI 组件与样式 (仅限 Next.js)](#9-ui-组件与样式-仅限-nextjs)
  - [10. 单元测试规范 (Jest)](#10-单元测试规范-jest)
    - [10.1. 开发过程中的测试执行](#101-开发过程中的测试执行)
    - [10.2. Mocking](#102-mocking)
  - [11. 对 AI 的核心指令](#11-对-ai-的核心指令)

---

## 1. 代码风格 (Airbnb JavaScript Style Guide)

*   **强制要求:** 所有 JavaScript 和 TypeScript 代码 **必须** 严格遵守 [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)。
*   **工具执行:** 项目已配置 ESLint 和 Prettier 来自动强制执行此代码风格。AI 生成的代码必须无缝集成，不得产生任何 linting 或 formatting 错误。

## 2. 注释规范 (关键要求)

注释是本项目的关键交付物之一，必须严格遵循以下规则。

### 2.1. 通用规则
*   **语言:** 所有注释（包括 JSDoc、文件头、行内注释）**必须** 使用 **日语 (日本語)**。
*   **文体:** **必须** 使用 "である調" (简体)。

### 2.2. 文件头注释
*   所有 `.ts` 和 `.tsx` 文件（配置文件除外）**必须** 在文件顶部包含一个 JSDoc 风格的文件头注释。
*   **职责区分**:
    *   文件头注释（`@fileoverview`）的核心职责是提供**文件级别的、高度概括的总结**，说明该文件在整个模块或应用中扮演的角色。
    *   具体的业务逻辑、处理步骤、算法或复杂的实现细节，**必须** 记录在文件内部对应的**函数、方法或类的 JSDoc 注释**中。
    *   **严禁**将函数或类的详细注释内容简单复制到文件头，反之亦然。必须避免内容冗余，确保不同层级的注释各司其职。
*   此注释中各标签的职责如下：
    *   `@fileoverview`: **必须**。用一两句话简明扼要地概括文件的核心职责。
    *   `@description`: **推荐**。当文件职责复杂，一句话不足以概括时，可用于补充说明。
    *   `<AUTHOR> **必须**。
    *   `@copyright Copyright © 2025 Hitachi Solutions, Ltd.`: **必须**。
*   对于测试文件 (`*.test.ts`)，包含上述标签的、位于最外层 `describe` 块之前的 JSDoc 注释块，即作为文件头。

### 2.3. JSDoc 规范
*   所有函数、类、方法和类型定义 **必须** 使用 JSDoc3 规范进行注释。
*   **必须** 正确使用 `@param`、`@returns`、`@throws` 等标签，并提供清晰的日文说明。

### 2.4. JSX 注释 (仅限 Next.js, 绝对规则)
*   **此规则仅适用于 `jcs-endpoint-nextjs` 应用中的 `.tsx` 文件。**
*   **严禁在返回的 JSX 块内部添加任何注释。** 在 `return (` 和 `);` 之间的 JSX 结构中，**绝对不允许** 出现任何形式的注释（包括 `//`、`/** */` 或 `{/* */}`）。
*   **正确位置:** 所有与返回的 JSX 相关的解释说明，**必须** 作为标准的 `//` 或 `/** */` 注释，放置在 `return` 语句 **之前** 的行上。

### 2.5. 单元测试注释 (Jest)
*   测试用例的注释 **必须** 结构化，以便从中提取报告信息。
*   `it` 或 `test` 块之前的注释 **必须** 使用 JSDoc 块 (`/** ... */`)。
*   **必须** 包含以下日文关键词：`試験観点：`, `試験対象：`, `試験手順：`, `確認項目：`。

### 2.6. 内容与交付文档对齐及自包含原则
*   **内容对齐**: 所有 JSDoc 和代码注释中的业务逻辑描述、功能说明、处理步骤和术语，其**内容**和**措辞**必须与 `docs-delivery/` 目录中对应的日文设计文档（機能設計書等）保持高度一致。
*   **自包含原则 (Self-Contained Principle)**: 注释本身必须是**可以独立理解的**，以最大化方便代码评审人员的工作。
    *   **信息内化**: 应将设计文档中与代码实现直接相关的逻辑、规则和关键步骤，**直接在注释中进行清晰说明**。评审人员不应为了理解代码意图而被强制要求去查阅外部文档。
    *   **禁止外部引用**: **严禁**在注释中出现指向外部文档的具体章节号、标题或页码等引用信息。例如，`「※詳細は機能設計書 3.1.2章を参照」`这类表述是**绝对禁止**的。所有必要信息都必须包含在注释本身之内。

## 3. 项目结构与命名约定

*   **Monorepo 根目录:** `jcs-endpoint-monorepo`
*   **应用目录 (`apps/`):**
    *   `jcs-endpoint-nextjs/`: Next.js 前端门户应用。
    *   `jcs-backend-services-standard/`: 标准超时 (5分钟) 的 Azure Functions 应用。
    *   `jcs-backend-services-long-running/`: 长时间运行 (30分钟) 的 Azure Functions 应用。
*   **函数应用内部结构:** 函数源文件 (`*.ts`) 和其对应的单元测试文件 (`*.test.ts`) **必须** 并列存放在同一个函数目录下。
*   **命名约定:**
    *   **组件 (React):** `PascalCase`
    *   **Hooks:** `use-kebab-case.ts`
    *   **其他所有目录和文件:** `kebab-case`

## 4. 通用工作流与命令执行 (关键原则)

**这是一个针对整个 Monorepo 的强制性核心原则。**

*   **原则:** 在终端中执行**任何**命令（包括 `npm install`, `npm run build`, `npm test`, `func ...` 等）之前，开发者**必须**首先确认其当前工作目录是正确的应用子目录（例如 `apps/jcs-endpoint-nextjs/`）。
*   **原因:** 每个应用都有其独立的 `package.json`、依赖项和配置。在错误的目录（如 Monorepo 根目录）执行命令将导致依赖安装错误、构建失败或部署错误的应用。
*   **如何确认:** 可以使用 `pwd` (Linux/macOS) 或 `Get-Location` (Windows PowerShell) 命令来验证，或直接观察终端提示符中显示的路径。

**示例:**
```bash
# 正确操作：为标准版 Function App 安装依赖
cd apps/jcs-backend-services-standard
# (确认路径正确后)
npm install

# 错误操作：在 Monorepo 根目录执行
cd jcs-endpoint-monorepo/
npm install # 错误! 这会使用根目录的 package.json, 而不是目标应用的。
```
*   **对 AI 的要求:** AI 助手在提供任何需要终端执行的命令时，也应首先提醒用户切换到正确的目录。

## 5. 技术栈与核心原则

*   **通用:** TypeScript, Node.js, Jest (单元测试)。
*   **Next.js 应用:** React, Tailwind CSS, Flowbite, SWR, Server Actions, Iron Session, Prisma.
*   **Azure Functions 应用:** v4 编程模型, Prisma (用于数据库访问)。
*   **核心原则:** 代码 **必须** 模块化，遵循关注点分离。

## 6. 数据管理 (Prisma)

此规则适用于所有需要访问数据库的应用。

*   **必须** 使用 `apps/jcs-endpoint-nextjs/app/lib/prisma.ts` 中导出的单例 Prisma Client。通过模块解析或相对路径引用。
*   查询时 **必须** 使用 `select` 或 `include` 只选择必要的字段。
*   涉及多个写操作的业务 **必须** 使用 `prisma.$transaction()` 保证原子性。

## 7. 后端逻辑与端点

### 7.1. Next.js (Server Actions & API Routes)
*   **Server Actions (`app/lib/actions.ts`):**
    *   文件 **必须** 以 `"use server";` 开头。
    *   **必须** 在执行前进行认证和授权检查（使用 Iron Session）。
    *   **必须** 对所有输入进行严格验证。
    *   **必须** 返回结构一致的对象，如 `{ success: boolean; ... }`。
    *   成功修改数据后 **必须** 调用 `revalidateTag()` 使 Next.js 缓存失效。
*   **API 路由 (`app/api/`):** 受保护的资源 **必须** 检查会话。

### 7.2. Azure Functions
*   **逻辑封装:** 复杂的业务逻辑应封装在函数内部的 `lib/` 目录中，以保持主函数文件的清晰。
*   **环境变量:** **必须** 通过 `local.settings.json`（本地）或应用服务配置（云端）来管理，**严禁** 硬编码。

## 8. 错误处理与日志记录

### 8.1. Next.js 应用
*   **错误处理:** **必须** 使用 `app/lib/portal-error.ts` 中的 `handleApiError` 和 `handleServerError` 进行统一处理。
*   **日志记录:** **必须** 使用 `app/lib/logger.ts` 中的 `Logger` 类或 `@LogFunctionSignature` 装饰器。

### 8.2. Azure Functions 应用
*   **错误处理:** **必须** 在函数代码中使用 `try...catch` 块来捕获和处理异常。
*   **日志记录:** **必须** 使用 `context.log` 对象进行日志记录 (例如 `context.log.info(...)`, `context.log.error(...)`)。这是 Azure Functions 的标准做法。
*   **安全:** **严禁** 在任何应用中记录敏感信息（如密码、原始令牌）。

## 9. UI 组件与样式 (仅限 Next.js)

*   **此部分规则仅适用于 `jcs-endpoint-nextjs` 应用。**
*   **样式:** **必须** 优先使用 Tailwind CSS 工具类，并遵循 `tailwind.config.js` 中定义的自定义主题。
*   **组件 (`app/ui/`):** 需要交互的组件 **必须** 使用 `"use client";` 指令。

## 10. 单元测试规范 (Jest)

此规则适用于 `apps/` 目录下的所有应用。

*   **框架:** **必须** 使用 Jest进行单元测试。
*   **位置:** 测试文件 (`*.test.ts`) **必须** 与其测试的源文件 (`*.ts`) 位于同一目录下。
*   **模式:** 所有测试用例 **必须** 遵循 `Arrange-Act-Assert (AAA)` 模式。

### 10.1. 开发过程中的测试执行
为提高开发效率，在修改代码并需要频繁验证时，**不推荐**直接运行 `npm test`，因为它会执行整个测试套件并生成覆盖率报告，过程较慢。推荐使用以下更高效的方法（前提是已根据第4节规则进入正确的应用目录）：

*   **方法A (首选): 使用观察者模式 (Watcher Mode)**
    *   **命令:** `npm run test:watch`
    *   **说明:** 这是在开发过程中最推荐的方法。它会启动 Jest 的观察者模式，在您保存文件时，自动重新运行与已更改文件相关的测试用例，提供即时反馈。

*   **方法B: 运行单个测试文件**
    *   **命令:** `npx jest <文件路径>` (例如: `npx jest TaskExecuteFunc/TaskExecuteFunc.test.ts`)
    *   **说明:** 当您只想专注于单个文件的测试，或者不想启动观察者模式时，此方法非常有用。

### 10.2. Mocking
*   **必须** Mock 所有外部依赖（如数据库、第三方服务）。
*   对于 Prisma，**必须** 使用 `jest-mock-extended` 或类似工具在测试中 mock Prisma Client。
*   对于 Azure Functions，**必须** mock `HttpRequest` 和 `InvocationContext` 对象来模拟不同的输入场景。
*   **断言:** **必须** 使用 `expect` 来验证函数的输出、状态变化或 mock 函数的调用情况。

## 11. 对 AI 的核心指令

1.  **最终权威来源:** 你在实现任何功能时，**必须** 以 `docs-delivery/` 目录下的日文文档作为最终且唯一的逻辑、需求和设计规约。
2.  **注释对齐:** 你生成的所有代码注释，其内容、术语和逻辑描述 **必须** 与 `docs-delivery/` 文档严格对齐。
3.  **遵循所有规则:** 本文档中定义的所有其他规则，特别是关于**注释语言 (日语)**、**JSX 内部无注释**和**单元测试注释结构**的规则，都必须严格遵守。
4.  **清晰定位:** 在接收指令时，如果指令不够明确，你应该能够根据功能描述和文件结构，推断出需要修改的具体文件路径（例如 `apps/jcs-backend-services-standard/TaskExecuteFunc/TaskExecuteFunc.ts`）。
