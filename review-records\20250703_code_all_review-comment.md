## TaskExecuteFunc.ts
```typescript
/*														
 * @file TaskExecuteFunc.ts														
 * @description														
 * このファイルはTaskExecuteFunc Azure Functionの実装です。														
 * Service BusのTaskInputQueueからタスク実行要求メッセージを受信し、														
 * DB/Files/Blob/Automation API/補償/クリーンアップ等の全責務を担います。														
 *														
 * 【設計意図】														
 * - タスク実行の全体フローを一元管理し、失敗時の補償処理やリソースクリーンアップも自動化。														
 * - 外部依存や並列実行制御、タイムアウト、エラー補償など運用性・堅牢性を重視。														
 *														
 * 【主な処理フロー】														
 * 1. メッセージ解析・taskId検証														
 * 2. Taskテーブル参照・QUEUED判定														（对于所有代码）AI自动生成的comment只留下必要的
 * 3. ContainerConcurrencyStatus参照・IDLE判定														不然在RV时会被细究
 * 4. DBトランザクション開始、コンテナBUSY化・Task RUNBOOK_SUBMITTED化														
 * 5. Azure Files作業ディレクトリ作成・サブディレクトリ作成														
 * 6. インポートタスク時はBlob→Filesへのファイルコピー														
 * 7. Automation API呼び出し・Runbookジョブ作成														
 * 8. トランザクションコミット・成功ログ														
 * 9. クリーンアップ処理（保持件数超過時の古いタスク/ファイル/ログ削除）														
 * 10. 各種エラー時はcatchで補償処理（ディレクトリ削除・ジョブ停止・Taskエラー更新等）														
 * 11. すべての分岐でcontext.logに詳細な日本語ログ出力														
 *														
 * <AUTHOR>														
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.														
 */														
                            
import { app, InvocationContext } from "@azure/functions";														
import { prisma } from "../lib/prisma";														
import {														
  shareServiceClient,														
  createAutomationJob,														
  stopAutomationJob,														
  blobServiceClient,														
} from "../lib/azureClients";														
import { cleanupOldTasks } from "../lib/cleanup";														
import { AppConstants } from "../lib/constants";														
import { formatTaskErrorMessage } from "../lib/utils";														
                            
// タスクをエラー状態で更新する補助関数														
/**														
 * タスクをエラー状態（COMPLETED_ERROR）に更新する。														
 * @param {string} taskId - 対象タスクID														
 * @param {string} errorCode - エラーコード（EMETxxxx）														
 * @param {string} resultMessage - ユーザー向け日本語メッセージ														
 * @param {Error} [systemError] - システムエラー詳細（任意）														
 */														
async function updateTaskToError(														
  taskId: string,														
  errorCode: string,														需要追加最終更新日時updatedAt
  resultMessage: string,														
  systemError?: Error														
) {														
  await prisma.task.update({														
    where: { id: taskId },														where的条件追加最終更新日時updatedAt
    data: {														
      status: AppConstants.TaskStatus.CompletedError,														
      errorCode,														
      resultMessage,														
      errorMessage: systemError?.message,														
    },														
  });														更新出错，或者更新件数0时要throw(retry)
}														
                            
/**														
 * タスク実行関数（TaskExecuteFunc）														
 *														
 * @param {unknown} message - Service Busから受信したメッセージ														
 * @param {InvocationContext} context - Azure Functionsの実行コンテキスト														
 * @returns {Promise<void>} 処理の非同期完了を示すPromise														
 * @throws {Error} タイムアウトまたは処理中の例外														
 *														
 * 主な処理分岐：														
 *  - メッセージ解析・taskId検証														
 *  - Taskテーブル参照・QUEUED判定														
 *  - ContainerConcurrencyStatus参照・IDLE判定														
 *  - DBトランザクション開始、コンテナBUSY化・Task RUNBOOK_SUBMITTED化														
 *  - Azure Files作業ディレクトリ作成・サブディレクトリ作成														
 *  - インポートタスク時はBlob→Filesへのファイルコピー														
 *  - Automation API呼び出し・Runbookジョブ作成														
 *  - トランザクションコミット・成功ログ														
 *  - クリーンアップ処理（保持件数超過時の古いタスク/ファイル/ログ削除）														
 *  - 各種エラー時はcatchで補償処理（ディレクトリ削除・ジョブ停止・Taskエラー更新等）														
 *  - すべての分岐でcontext.logに詳細な日本語ログ出力														
 */														
export async function TaskExecuteFunc(														
  message: unknown,														
  context: InvocationContext														
): Promise<void> {														
  // 以前はソフトタイムアウト制御（Promise.race）を実装していたが、														
  // APP単位でFunctionごとにタイムアウトを分離できるため、ここでは不要となった。														
                            
  // すべての補償・catchで参照する変数を関数スコープで宣言														
  let taskId: string | undefined;														
  let task: any = null;														
  let workspaceCreated = false;														
  let jobCreated = false;														
  let originalTaskUpdatedAt: Date | null = null;														
  let originalContainerStatusUpdatedAt: Date | null = null;														
                            
  try {														
    // 1. Service Busメッセージを解析し、taskIdを取得														
    const messageBody = message as { taskId: string };														从message里取taskId的写法不统一，统一一下比较好
    taskId = messageBody.taskId;														
    context.log(`[TaskExecuteFunc] TaskExecuteFunc: taskId受信:`, taskId);														
    if (!taskId) {														
      context.error("[TaskExecuteFunc] 致命的: メッセージにtaskIdが存在しない。処理を中断する。");														
      return;														
    }														
                            
    // 2. 処理に必要な変数を初期化														
    task = await prisma.task.findUnique({ where: { id: taskId! } });														
    if (!task) {														
      context.error(`[TaskExecuteFunc] タスクID ${taskId} がDBに存在しない。処理を中断する。`);														
      return;														
    }														
    originalTaskUpdatedAt = task.updatedAt;														
                            
    // タスクの必須情報（タイプ、サーバーIDなど）を検証														
    if (														
      !task.taskType ||														
      !task.targetServerId ||														
      !task.targetVmName ||														
      !task.targetContainerName														task.targetHRWGroupName也check
    ) {														
      await updateTaskToError(														处理失败时有在try里调updateTaskToError的写法，有在try里throw然后在catch里调updateTaskToError的写法，统一成后者比较好
        taskId!,														
        AppConstants.ERROR_CODES.EMET0009,														
        formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0009)														
      );														
      context.error(`[TaskExecuteFunc] タスクID ${taskId} の構成情報が不足している。`);														
      return;														
    }														
                            
    // タスクのステータスが「QUEUED」であるかを確認														
    if (task.status !== AppConstants.TaskStatus.Queued) {														
      context.warn(														
        `[TaskExecuteFunc] タスクID ${taskId} のステータスがQUEUEDではない（現状: ${task.status}）。処理対象外。`														
      );														
      return;														
    }														
                            
    // 4. 並行実行を防ぐため、コンテナの現在の状態を事前チェック														
    const containerStatusRecord =														
      await prisma.containerConcurrencyStatus.findUnique({														
        where: {														
          targetVmName_targetContainerName: {														
            targetVmName: task.targetVmName,														
            targetContainerName: task.targetContainerName,														
          },														
        },														
        select: { status: true, updatedAt: true },														
      });														
    originalContainerStatusUpdatedAt = containerStatusRecord?.updatedAt ?? null;														
                            containerStatusRecord或containerStatusRecord.status或containerStatusRecord.updatedAt为空时更新task为EMET0009
    // 状態が「BUSY」の場合は早期終了														
    if (containerStatusRecord && containerStatusRecord.status === "BUSY") {														    if (containerStatusRecord.status === "BUSY") {
      await updateTaskToError(														
        taskId!,														
        AppConstants.ERROR_CODES.EMET0001,														
        formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0001, [task.targetServerName || "対象サーバ"])														
      );														
      context.warn(`[TaskExecuteFunc] タスクID ${taskId} のコンテナがBUSY状態。早期終了。`);														
      return;														
    }														
                            
    // 5. Azure Filesの作業ディレクトリを作成														
    const shareClient = shareServiceClient.getShareClient("taskworkspaces");														设计书里是"TaskWorkspaces"
    await shareClient.createIfNotExists();														
    const taskDirClient = shareClient.getDirectoryClient(taskId!);														
    await taskDirClient.createIfNotExists();														
    await taskDirClient.getDirectoryClient("imports").createIfNotExists();														
    await taskDirClient.getDirectoryClient("exports").createIfNotExists();														
    workspaceCreated = true;														
    context.log(`[TaskExecuteFunc] 作業ディレクトリ作成: ${taskDirClient.path}`);														
                            
    // 6. インポートタスクの場合、Blob→Filesへのファイルコピー														
    if (task.taskType === AppConstants.TaskType.MgmtItemImport) {														
      const params = JSON.parse(task.parametersJson || "{}");														
      const blobPath = params.importedFileBlobPath;														
      if (!blobPath)														
        throw new Error(														
          `${AppConstants.ERROR_CODES.EMET0009}: importedFileBlobPathが未設定`														
        );														
      const containerName = process.env.AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF!;														
      const blobClient = blobServiceClient														
        .getContainerClient(containerName)														
        .getBlobClient(blobPath);														
      const fileClient = taskDirClient														
        .getDirectoryClient("imports")														
        .getFileClient("assetsfield_def.csv");														
      const downloadResponse = await blobClient.downloadToBuffer();														
      await fileClient.uploadData(downloadResponse);														
      context.log(`[TaskExecuteFunc] インポートファイルをBlobからFilesへコピー完了: タスクID ${task.id}`);														
    }														
                            
    // 7. Azure Automationジョブを投入														
    const runbookParams = {														
      taskId: task.id,														
      targetContainerName: task.targetContainerName,														
      ...JSON.parse(task.parametersJson || "{}"),														
    };														
                            
    // タスクタイプに応じてRunbook名を決定														
    let runbookName: string;														
    switch (task.taskType) {														
      case AppConstants.TaskType.MgmtItemImport:														
        runbookName = process.env.RUNBOOK_MGMT_ITEM_IMPORT!;														
        break;														
      case AppConstants.TaskType.MgmtItemExport:														
        runbookName = process.env.RUNBOOK_MGMT_ITEM_EXPORT!;														
        break;														
      case AppConstants.TaskType.OpLogExport:														
        runbookName = process.env.RUNBOOK_OPLOG_EXPORT!;														
        break;														
      default:														
        throw new Error(`未定義のタスクタイプ: ${task.taskType}`);														
    }														
    if (!runbookName) {														
      throw new Error(`Runbook名の環境変数が未設定です: taskType=${task.taskType}`);														
    }														
                            
    await createAutomationJob(														
      task.id,														
      runbookName,														
      runbookParams,														
      task.targetHRWGroupName!														
    );														
    jobCreated = true;														
    context.log(`[TaskExecuteFunc] Automationジョブ作成完了: タスクID ${task.id}`);														
                            
    // 8. 全外部操作成功後、DBをトランザクションで更新														
    await prisma.$transaction(async (tx: any) => {														
      // 8a. コンテナ状態をBUSYに更新（楽観ロック）														
      const updateContainerResult =														
        await tx.containerConcurrencyStatus.updateMany({														
          where: {														
            targetVmName: task!.targetVmName!,														
            targetContainerName: task!.targetContainerName!,														
            updatedAt: originalContainerStatusUpdatedAt, // 楽観ロック条件														
          },														
          data: {														
            status: "BUSY",														
            currentTaskId: task!.id,														
          },														
        });														
                            
      // 競合時はエラー														
      if (updateContainerResult.count === 0 && containerStatusRecord) {														      if (updateContainerResult.count === 0) {
        const customError = new Error(formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0001, [task!.targetServerName || "対象サーバ"]));														
        (customError as any).code = AppConstants.ERROR_CODES.EMET0001;														
        throw customError;														
      }														
                            
      // ステータスレコードが未存在なら新規作成														容器记录做成的处理在createTaskAction里已有，删掉这里
      if (!containerStatusRecord) {														
        await tx.containerConcurrencyStatus.create({														
          data: {														
            targetVmName: task!.targetVmName!,														
            targetContainerName: task!.targetContainerName!,														
            status: "BUSY",														
            currentTaskId: task!.id,														
          },														
        });														
      }														
      context.log(`[TaskExecuteFunc] コンテナロック取得: タスクID ${task!.id}`);														
                            
      // 8b. タスク状態をRUNBOOK_SUBMITTEDに更新（楽観ロック）														
      const updateTaskResult = await tx.task.updateMany({														
        where: {														
          id: task!.id,														
          updatedAt: originalTaskUpdatedAt, // 楽観ロック条件														
        },														
        data: {														
          status: AppConstants.TaskStatus.RunbookSubmitted,														
          startedAt: new Date(),														
        },														
      });														
                            
      // 他プロセスによる変更時は中断														
      if (updateTaskResult.count === 0) {														
        throw new Error("他プロセスによりタスクが変更・キャンセルされたため中断");														
      }														
    });														
                            
    context.log(`[TaskExecuteFunc] タスクID ${taskId} のメイントランザクション完了。`);														
                            
    // 9. 古いタスクのクリーンアップ														
    await cleanupOldTasks(task.targetServerId, context);														
  } catch (error) {														
    // --- エラー処理ブロック ---														
    context.error(`[TaskExecuteFunc] タスクID ${taskId} の処理中にエラー発生:`, error);														
                            
    // 外部リソースの補償処理														
    const compensationActions: Promise<any>[] = [];														
    if (workspaceCreated) {														
      const shareClient = shareServiceClient.getShareClient("taskworkspaces");														
      const taskDirClient = shareClient.getDirectoryClient(taskId!);														
      const importsDirClient = taskDirClient.getDirectoryClient("imports");														
      const exportsDirClient = taskDirClient.getDirectoryClient("exports");														
      const importedFileClient = importsDirClient.getFileClient("assetsfield_def.csv");														
      compensationActions.push(														
        Promise.all([														
          importedFileClient.deleteIfExists(),														
          importsDirClient.deleteIfExists(),														
          exportsDirClient.deleteIfExists(),														
        ])														
          .then(() => { return taskDirClient.deleteIfExists(); })														
          .catch((e) =>														
            context.error(														
              `[TaskExecuteFunc] 補償処理: 作業ディレクトリ削除失敗: タスクID ${taskId}:`,														
              e														
            )														
          )														
      );														
    }														
    if (jobCreated) {														
      compensationActions.push(														
        stopAutomationJob(taskId!).catch((e) =>														
          context.error(														
            `[TaskExecuteFunc] 補償処理: Automationジョブ停止失敗: タスクID ${taskId}:`,														
            e														
          )														
        )														
      );														
    }														
    await Promise.all(compensationActions);														
                            
    // エラー種別ごとに適切なエラーコード・メッセージでタスクを更新														
    if (														
      error instanceof Error &&														
      "code" in error &&														"code" in error和(error as any).code两种写法混用，统一一下比较好
      (error as any).code === AppConstants.ERROR_CODES.EMET0001														
    ) {														
      await updateTaskToError(														
        taskId!,														
        AppConstants.ERROR_CODES.EMET0001,														
        formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0001, [task?.targetServerName || "対象サーバ"])														
      );														
    } else if (														
      error && (error as any).code && typeof (error as any).code === "string" && (error as any).code.startsWith("P")) {														"1.只有这个if里不是error instanceof Error，统一一下比较好
2.用(error as any).code.startsWith(""P"")来判断数据库操作（prisma）的error，但是不直观，改进一下写法比较好"
      await updateTaskToError(														
        taskId!,														
        AppConstants.ERROR_CODES.EMET0007,														
        formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0007),														
        error instanceof Error ? error : undefined														
      );														
    } else if (														
      error instanceof Error &&														
      error.message.includes("他プロセスによりタスクが変更・キャンセルされたため中断")														
    ) {														
      context.warn(														
        `[TaskExecuteFunc] タスクID ${taskId} はキャンセルまたは変更されたため中断。ステータス更新不要。`														
      );														
      return;														
    } else if (														
      error instanceof Error &&														
      "isAxiosError" in error &&														
      (error as any).isAxiosError														
    ) {														
      await updateTaskToError(														
        taskId!,														
        AppConstants.ERROR_CODES.EMET0014,														调用Azure Automation API出错是EMET0013
        formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0014),														
        error														
      );														
    } else if (														
      error instanceof Error &&														
      (("code" in error &&														
        typeof (error as any).code === "string" &&														
        ((error as any).code.startsWith("Share") ||														Azure Files操作出错是EMET0002
          (error as any).code.includes("Blob"))) ||														Azure Blob Storage操作出错是EMET0003
        error.message.includes(AppConstants.ERROR_CODES.EMET0009))														判断包含EMET0009但之后更新为EMET0002，不符合逻辑
    ) {														
      await updateTaskToError(														
        taskId!,														
        AppConstants.ERROR_CODES.EMET0002,														
        formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0002),														
        error														
      );														
    } else {														
      await updateTaskToError(														
        taskId!,														
        AppConstants.ERROR_CODES.EMET0008,														
        formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0008),														
        error instanceof Error ? error : undefined														
      );														
    }														
                            
    // その他のエラーは再スローし、Service Busのリトライをトリガー														
    throw error;														异常处理能正常执行完，不throw（retry）
  }														
}														
                            
// FunctionとService Busキューのバインド設定														
app.serviceBusQueue("TaskExecuteFunc", {														
  connection: "AZURE_SERVICEBUS_NAMESPACE_HOSTNAME",														
  queueName: "%SERVICE_BUS_TASK_INPUT_QUEUE_NAME%",														
  handler: TaskExecuteFunc,														
});														

```

## TaskExecuteTimeoutFunc.ts
```typescript
/*														
 * @file TaskExecuteTimeoutFunc.ts														
 * @description														
 * 本ファイルはTaskExecuteTimeoutFunc Azure Functionの実装です。														
 * Service BusのTaskInputQueue Dead-Letter Queue(DLQ)をトリガーに、														
 * 失敗・タイムアウトしたタスクの補償処理（DBエラー更新・作業ディレクトリ削除）を厳密に行います。														
 *														
 * 【設計意図】														
 * - タスク実行失敗やタイムアウト時のリカバリを自動化し、システムの健全性・運用性を担保。														
 * - DLQに溜まった異常タスクを検知し、DB・ファイルの一貫性を保つ。														
 *														
 * 【主な処理フロー】														
 * 1. メッセージからtaskId抽出														
 * 2. Taskテーブル参照・必須情報検証														
 * 3. status=QUEUED以外は処理対象外														
 * 4. Azure Files作業ディレクトリ削除														
 * 5. TaskをCOMPLETED_ERROR/EMET0005で更新														
 * 6. 例外時はEMET0008で補償														
 *														
 * <AUTHOR>														
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.														
 */														
                            
import { app, InvocationContext } from "@azure/functions";														
import { prisma } from "../lib/prisma";														
import { AppConstants } from "../lib/constants";														
import { shareServiceClient } from "../lib/azureClients";														
import { formatTaskErrorMessage } from "../lib/utils";														
                            
/**														
 * TaskInputQueueのDLQメッセージを受信し、タスクタイムアウト補償処理を実施する関数														
 *														
 * @param {any} message - Service Bus DLQから受信したメッセージ														TaskExecuteFunc.ts的参数message的类型是unknown，统一一下比较好
 * @param {InvocationContext} context - Azure Functionsの実行コンテキスト														
 * @returns {Promise<void>} 非同期で補償処理を実行														
 *														
 * 主な処理分岐：														
 *  - taskId不足/不正、DB取得失敗はログ記録のみ														
 *  - タスク情報不足はEMET0009でエラー更新														
 *  - status≠QUEUEDはログのみ														
 *  - Azure Files作業ディレクトリ削除（失敗時も継続）														
 *  - TaskをCOMPLETED_ERROR/EMET0005で更新														
 *  - 例外時はEMET0008で補償														
 */														
export async function TaskExecuteTimeoutFunc(message: any, context: InvocationContext): Promise<void> {														
  context.log("[TaskExecuteTimeoutFunc] DLQメッセージ受信");														
  let taskId: string | undefined;														
  let originalUpdatedAt: Date | undefined;														
  try {														
    // 1. メッセージからtaskId抽出														
    taskId = message?.taskId;														从message里取taskId的写法不统一，统一一下比较好
    context.log(`[TaskExecuteTimeoutFunc] 受信taskId: ${taskId}`);														
    if (!taskId || typeof taskId !== "string") {														
      context.error("[TaskExecuteTimeoutFunc] taskIdが不足/不正。処理終了");														
      return;														
    }														
                            
    // 2. Taskテーブル参照														
    const task = await prisma.task.findUnique({ where: { id: taskId } });														
    if (!task) {														
      context.error(`[TaskExecuteTimeoutFunc] taskId=${taskId} のタスクがDBに存在しない。処理終了`);														
      return;														
    }														
    originalUpdatedAt = task.updatedAt ?? undefined;														
    // 必須情報検証														
    if (!task.status || !task.updatedAt) {														删除，与设计书保持一致
      context.error(`[TaskExecuteTimeoutFunc] タスク情報不足。EMET0009でエラー更新`);														
      await prisma.task.update({														
        where: { id: taskId },														
        data: {														
          status: AppConstants.TaskStatus.CompletedError,														
          errorCode: AppConstants.ERROR_CODES.EMET0009,														
          resultMessage: AppConstants.TASK_ERROR_MESSAGE.EMET0009,														
        },														
      });														
      return;														
    }														
                            
    // 3. status=QUEUED以外は処理対象外														
    if (task.status !== AppConstants.TaskStatus.Queued) {														
      context.warn(`[TaskExecuteTimeoutFunc] status=${task.status} のため補償不要。処理終了`);														
      return;														
    }														
                            需要添加调用get job API判断job是否已被做成，是就调stop job API的处理，参见设计书
    // 4. Azure Files作業ディレクトリ削除														
    try {														
      const shareClient = shareServiceClient.getShareClient("taskworkspaces");														设计书里是"TaskWorkspaces"
      const dirClient = shareClient.getDirectoryClient(taskId);														
      await dirClient.deleteIfExists();														
      context.log(`[TaskExecuteTimeoutFunc] 作業ディレクトリ TaskWorkspaces/${taskId} 削除成功`);														
    } catch (err: any) {														
      context.warn(`[TaskExecuteTimeoutFunc] 作業ディレクトリ削除失敗: ${err.message}`);														
      // 続行														
    }														
                            
    // 5. TaskをCOMPLETED_ERROR/EMET0005で更新														
    try {														
      const updateResult = await prisma.task.updateMany({														
        where: { id: taskId, updatedAt: originalUpdatedAt },														
        data: {														
          status: AppConstants.TaskStatus.CompletedError,														
          errorCode: AppConstants.ERROR_CODES.EMET0005,														
          resultMessage: AppConstants.TASK_ERROR_MESSAGE.EMET0005,														
        },														
      });														
      if (updateResult.count === 0) {														
        context.warn(`[TaskExecuteTimeoutFunc] DB更新件数0件。既に更新済みまたは競合。`);														
      } else {														
        context.log(`[TaskExecuteTimeoutFunc] タスク${taskId}をCOMPLETED_ERROR/EMET0005で更新完了`);														
      }														
    } catch (err: any) {														
      context.error(`[TaskExecuteTimeoutFunc] DB更新失敗: ${err.message}`);														
    }														
    context.log("[TaskExecuteTimeoutFunc] 補償処理完了");														
  } catch (err: any) {														
    context.error(`[TaskExecuteTimeoutFunc] 致命的例外: ${err.message}`);														
    // EMET0008で補償														不需要更新task为EMET0008，输出完log结束就行
    if (taskId) {														
      try {														
        await prisma.task.update({														
          where: { id: taskId },														
          data: {														
            status: AppConstants.TaskStatus.CompletedError,														
            errorCode: AppConstants.ERROR_CODES.EMET0008,														
            resultMessage: AppConstants.TASK_ERROR_MESSAGE.EMET0008,														
          },														
        });														
        context.log(`[TaskExecuteTimeoutFunc] EMET0008で補償更新完了`);														
      } catch (e: any) {														
        context.error(`[TaskExecuteTimeoutFunc] EMET0008補償も失敗: ${e.message}`);														
      }														
    }														
  }														
}														
                            
// FunctionとService Busキューのバインド設定														
app.serviceBusQueue("TaskExecuteTimeoutFunc", {														
  connection: "AZURE_SERVICEBUS_NAMESPACE_HOSTNAME",														
  queueName: "%SERVICE_BUS_TASK_INPUT_QUEUE_NAME%/$DeadLetterQueue",														
  handler: TaskExecuteTimeoutFunc,														
}); 														

```

## RunbookMonitorFunc.ts
```typescript
/*														
 * @file RunbookMonitorFunc.ts														
 * @description														
 * Azure Functionsタイマートリガーで定期実行されるRunbook監視関数。														
 * RUNBOOK_SUBMITTED状態のタスクをDBから取得し、														
 * 各タスクのジョブ状態・タイムアウト・異常分岐を判定し、														
 * 必要に応じてDB更新・ServiceBus通知を行う。														
 *														
 * 【設計意図】														
 * - 外部依存（DB, Automation API, ServiceBus）や大量タスク処理での長時間ブロックを防ぐため、														
 *   Azure FunctionsのAPP単位でタイムアウトを管理する。														
 * - これにより、定期バッチの健全性・リソース効率・運用性を担保。														
 *														
 * 【主な処理フロー】														
 * 1. RUNBOOK_SUBMITTEDタスク全件取得。なければ即終了。														
 * 2. 各タスクについて：														
 *    - startedAt未設定はスキップ。														
 *    - タイムアウト超過なら即Timeout扱い。														
 *    - Automation APIでジョブ状態取得。終了/実行中/未知で分岐。														
 *    - 終了状態ならDB更新＋ServiceBus通知。実行中はスキップ。未知はログのみ。														
 *    - DB/ServiceBus/外部API例外も個別catchし、全体処理は継続。														
 * 3. 全体で致命的例外が発生した場合もcatchし、ログ記録。														
 *														
 * <AUTHOR>														
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.														
 */														
                            
import { app, InvocationContext, Timer } from "@azure/functions";														
import { prisma } from "../lib/prisma";														
import { AppConstants } from "../lib/constants";														
import { serviceBusClient } from "../lib/azureClients";														
import { automationApi } from "../lib/azureClients";														
                            
// 環境変数の取得と初期化														
const monitorInterval = parseInt(process.env.RUNBOOK_MONITOR_INTERVAL_SECONDS || "30", 10);														
const runbookTimeout = parseInt(process.env.RUNBOOK_TIMEOUT_SECONDS || String(5 * 3600), 10); // デフォルト5時間														
const automationAccount = process.env.AZURE_AUTOMATION_ACCOUNT_NAME!;														
const subscriptionId = process.env.SUBSCRIPTION_ID!;														
const resourceGroupName = process.env.RESOURCE_GROUP_NAME!;														
const runbookStatusQueueName = process.env.SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME!;														
                            
if (!automationAccount || !subscriptionId || !resourceGroupName || !runbookStatusQueueName) {														
  throw new Error("RunbookMonitorFuncに必要な環境変数が不足しています");														
}														
                            
// Service Bus クライアント														
const sender = serviceBusClient.createSender(runbookStatusQueueName);														
                            
// 終了・実行中ステータス定義														
const END_STATUSES = [														
  "Completed", "Failed", "Removing", "Resuming", "Stopped", "Stopping", "Suspended", "Suspending"														
];														
const ACTIVE_STATUSES = [														
  "New", "Activating", "Running", "Blocked", "Disconnected"														
];														
                            
/**														
 * RUNBOOK_SUBMITTEDタスクを監視し、状態遷移・通知を行うメインハンドラ														
 *														
 * @param {Timer} timer - タイマートリガー情報														
 * @param {InvocationContext} context - 実行コンテキスト（ログ出力等）														
 * @returns {Promise<void>} 非同期で監視・通知処理を実行														
 * @throws {Error} 必須環境変数不足・致命的例外														
 */														
export async function runbookMonitorHandler(timer: Timer, context: InvocationContext) {														
  /**														
   * RUNBOOK_SUBMITTEDタスク監視のメイン処理。														
   * 主要分岐・catch・外部依存呼び出し前後に詳細な日本語ログ・注釈を付与。														
   */														
  try {														
    context.log("[RunbookMonitorFunc] RUNBOOK_SUBMITTEDタスクの監視開始");														（以下log处同样）正常log是否有必要输出。因为监视函数每30s就执行一次，有可能输出大量的正常log导致异常log不好找/被冲掉
    /**														
     * 1. RUNBOOK_SUBMITTEDタスク全件取得。なければ即return。														
     */														
    const tasks = await prisma.task.findMany({														
      where: { status: AppConstants.TaskStatus.RunbookSubmitted },														
    });														
    if (!tasks.length) {														
      context.log("[RunbookMonitorFunc] 監視対象タスクなし");														
      return;														
    }														
    context.log(`[RunbookMonitorFunc] 監視対象タスク数: ${tasks.length}`);														
    for (const task of tasks) {														
      try {														
        /**														
         * startedAt未設定タスクはスキップ。														
         */														
        if (!task.startedAt) {														
          context.warn(`[RunbookMonitorFunc] タスク${task.id}はstartedAt未設定のためスキップ`);														
          continue;														
        }														
        const now = new Date();														
        const startedAt = new Date(task.startedAt!);														
        const elapsed = (now.getTime() - startedAt.getTime()) / 1000;														
        let automationJobStatus = "";														
        let exception = "";														
        let shouldProcess = false;														
                            
        /**														
         * タイムアウト判定。超過時はTimeout扱い。														
         */														
        if (elapsed > runbookTimeout) {														
          automationJobStatus = "Timeout";														
          shouldProcess = true;														
          context.warn(`[RunbookMonitorFunc] タスク${task.id}はタイムアウト(${elapsed}s > ${runbookTimeout}s)`);														
        } else {														
          /**														
           * Automation APIでジョブ状態取得。失敗時はwarnログ・スキップ。														
           */														
          const jobName = task.id; // jobName=taskIdの前提														
          const url = `/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}/providers/Microsoft.Automation/automationAccounts/${automationAccount}/jobs/${jobName}?api-version=2023-11-01`;														
          let jobStatus = "";														
          try {														
            const resp = await automationApi.get(url);														
            jobStatus = resp.data?.properties?.status || "";														
            exception = resp.data?.properties?.exception || "";														
            context.log(`[RunbookMonitorFunc] タスク${task.id}のジョブ状態: ${jobStatus}`);														
          } catch (err: any) {														
            context.warn(`[RunbookMonitorFunc] タスク${task.id}のジョブ状態取得失敗: ${err.message}`);														
            continue; // API失敗時はスキップ														
          }														
          /**														
           * 終了/異常状態ならshouldProcess、実行中/準備中はスキップ、未知はwarn。														
           */														
          if (END_STATUSES.includes(jobStatus)) {														
            automationJobStatus = jobStatus;														
            shouldProcess = true;														
          } else if (ACTIVE_STATUSES.includes(jobStatus)) {														
            // 実行中・準備中はスキップ														
            continue;														
          } else {														
            context.warn(`[RunbookMonitorFunc] タスク${task.id}の未知ジョブ状態: ${jobStatus}`);														
            continue;														
          }														
        }														
                            
        if (!shouldProcess) continue;														
                            
        /**														
         * DBトランザクションで状態更新＋ServiceBus通知。														
         * 失敗時はerrorログ・スキップ。														
         */														
        try {														
          await prisma.$transaction(async (tx) => {														
            await tx.task.update({														
              where: { id: task.id },														where添加最终更新日时一致的条件。更新0件时不向bus发消息
              data: { status: AppConstants.TaskStatus.RunbookProcessing },														
            });														
            const message = {														根据实机验证，DB操作以外的处理放在事务里容易导致事务超时，同时事务提交本身也有可能失败，所以把向bus发消息放在事务外
              body: {														
                taskId: task.id,														
                automationJobStatus,														
                exception,														
              },														
            };														
            await sender.sendMessages(message);														
            context.log(`[RunbookMonitorFunc] タスク${task.id}の状態更新・通知完了: ${automationJobStatus}`);														
          });														
        } catch (err: any) {														
          context.error(`[RunbookMonitorFunc] タスク${task.id}のDB更新/ServiceBus送信失敗: ${err.message}`);														
          continue;														
        }														
      } catch (err: any) {														
        context.error(`[RunbookMonitorFunc] タスク${task.id}処理中の例外: ${err.message}`);														
        continue;														
      }														
    }														
    context.log("[RunbookMonitorFunc] 監視処理終了");														
  } catch (err: any) {														
    context.error(`[RunbookMonitorFunc] 致命的例外: ${err.message}`);														
  }														
}														
                            
// Azure Functionsタイマートリガー登録														
app.timer("RunbookMonitorFunc", {														
  schedule: `*/${monitorInterval} * * * * *`, // 秒単位の定期実行														
  handler: runbookMonitorHandler,														
}); 														

```

## RunbookProcessorFunc.ts
```typescript
/*														
 * @file RunbookProcessorFunc.ts														
 * @description														
 * このファイルはRunbookProcessorFunc Azure Functionの実装です。														
 * Service BusのRunbookStatusQueueからRunbookの実行結果メッセージを受信し、														
 * その結果に応じて成果物のコピー、DBの最終ステータス更新、リソースクリーンアップ等の責務を担います。														
 *														
 * 【設計意図】														
 * - Runbookの非同期実行結果を一元的にハンドリングし、タスクの最終的な状態（成功/エラー）を確定させる。														
 * - 成功時の成果物永続化から、失敗時の詳細なエラー分類、リソースの確実なクリーンアップまで、タスク完了に関わる全ての後処理を担う。														
 *														
 * 【主な処理フロー】														
 * 1. メッセージ解析・必須パラメータ（taskId, automationJobStatus）検証														
 * 2. Taskテーブル参照・RUNBOOK_PROCESSING判定														
 * 3. automationJobStatusに基づき、外部リソース操作を先行して実行														
 *    - "Completed": FilesからBlobへ成果物ファイルをコピー														
 *    - "Failed": Filesからエラー詳細ファイル(errordetail.txt)を読み取り														
 *    - "Suspended" / "Timeout" 等: Automation APIを呼び出しジョブを停止														
 * 4. 全ての外部操作が完了した後、高速な単一DBトランザクションを開始														
 * 5. トランザクション内で、OperationLog作成、Task最終ステータス更新、ContainerConcurrencyStatusのIDLE化をアトミックに実行														
 * 6. トランザクションをコミット														
 * 7. Azure Files上の一時作業ディレクトリを再帰的に完全削除														
 * 8. 予期せぬエラー発生時はcatchでリトライをトリガー														
 *														
 * <AUTHOR>														
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.														
 */														
                            
import { app, InvocationContext } from "@azure/functions";														
import { Prisma, Task } from "@prisma/client";														是否需要？Task是endpoint自己定义的，能否从@prisma/client里import?
import { prisma } from "../lib/prisma";														
import {														
  blobServiceClient,														
  shareServiceClient,														
  stopAutomationJob,														
} from "../lib/azureClients";														
import { AppConstants } from "../lib/constants";														
import { ShareDirectoryClient } from "@azure/storage-file-share";														
import { formatTaskErrorMessage } from "../lib/utils";														
                            
// --- 型定義 ---														
/**														
 * RunbookStatusQueueから受信するメッセージの型定義														
 */														
interface RunbookStatusMessage {														
  taskId: string;														
  automationJobStatus: string;														
  exception?: string;														
}														
                            
// --- 定数定義 ---														
// 成果物（資産フィールド定義）を保存するBlobコンテナ名														正确的名称是"管理項目定義"，不必要的注释可以删
const ASSETS_CONTAINER = process.env.AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF!;														
// 成果物（操作ログ）を保存するBlobコンテナ名														
const OPLOGS_CONTAINER = process.env.AZURE_STORAGE_CONTAINER_OPLOGS!;														
                            
// --- 補助関数 ---														
/**														
 * Azure Filesのディレクトリを再帰的に削除する														
 * @param directoryClient 削除対象のディレクトリクライアント														
 * @param context ロギング用のFunctionコンテキスト														
 */														
async function deleteDirectoryRecursive(														ShareDirectoryClient的删除文件夹的方法确实需要先把文件夹清空才能删，故用ShareDirectoryClient的话确实需要自己写递归删除方法。而ShareClient的删除方法并没有说要先清空，可以尝试，可行的话就不用自己写。
  directoryClient: ShareDirectoryClient,														
  context: InvocationContext														
) {														
  try {														
    const list = directoryClient.listFilesAndDirectories();														
    for await (const item of list) {														
      if (item.kind === "directory") {														
        const subDirectoryClient = directoryClient.getDirectoryClient(														
          item.name														
        );														
        await deleteDirectoryRecursive(subDirectoryClient, context);														
      } else if (item.kind === "file") {														
        const fileClient = directoryClient.getFileClient(item.name);														
        await fileClient.deleteIfExists();														
      }														
    }														
    // 日語ログ: ディレクトリ削除成功														如需保留这个方法，需要在这里删除directoryClient本身
    context.log(														
      `[RunbookProcessorFunc] ディレクトリ削除成功: ${directoryClient.path}`														
    );														
  } catch (error) {														
    // 日語ログ: ディレクトリ削除中にエラー発生														
    context.error(														
      `[RunbookProcessorFunc] ディレクトリ削除中にエラー発生: ${directoryClient.path}`,														
      error														
    );														
  }														
}														
                            
// --- メインのAzure Function ---														
export async function RunbookProcessorFunc(														
  message: unknown,														参数message的类型有unknown有any，统一一下比较好
  context: InvocationContext														
): Promise<void> {														
  /**														
   * RunbookProcessorFuncのメイン処理。														
   * - 主要分岐・catch・外部依存呼び出し前後に詳細な日本語ログ・注釈を付与。														
   * - すべてのログは[RunbookProcessorFunc]前缀、レベル分け。														
   */														
  // 1. Service Busメッセージを解析し、必須パラメータを抽出														
  const { taskId, automationJobStatus, exception } =														从message里取taskId的写法不统一，统一一下比较好
    message as RunbookStatusMessage;														
  context.log(`[RunbookProcessorFunc] taskId受信: ${taskId}, status: ${automationJobStatus}`);														
                            
  // 必須パラメータの存在チェック。不足時はリトライをトリガー														
  if (!taskId || !automationJobStatus) {														
    const errorMsg = "[RunbookProcessorFunc] 致命的: taskIdまたはautomationJobStatusがメッセージに存在しない。処理を中断する。";														
    context.error(errorMsg);														
    throw new Error(errorMsg);														
  }														
                            
  let task: Task | null = null;														
                            
  try {														
    // 2. taskIdを使用してTaskテーブルを検索し、事前検証を行う														
    task = await prisma.task.findUnique({ where: { id: taskId } });														
                            
    // タスクが存在しない場合はリトライ														
    if (!task) {														
      const errorMsg = `[RunbookProcessorFunc] タスクID ${taskId} がDBに存在しない。処理を中断する。`;														
      context.error(errorMsg);														
      throw new Error(errorMsg);														
    }														
                            
    // タスクのステータスが「実行中」でない場合は処理対象外とし、正常終了														
    if (task.status !== AppConstants.TaskStatus.RunbookProcessing) {														
      context.warn(														
        `[RunbookProcessorFunc] タスクID ${taskId} のステータスがRUNBOOK_PROCESSINGではない（現状: ${task.status}）。処理対象外。`														
      );														
      return;														
    }														
                            
    // 3. 処理結果を保持する変数を初期化														
    let taskUpdateData: Prisma.TaskUpdateInput = {};														
    const operationLogsToCreate: Prisma.OperationLogCreateManyInput[] = [];														
                            
    // 4. Runbookの実行結果ステータスに基づき、成果物の処理やタスク状態の更新準備を行う														
    switch (automationJobStatus) {														
      case "Completed":														
        // Runbookが正常終了した場合の処理														
        switch (task.taskType) {														
          // 4a. 操作ログのエクスポートタスクの場合														
          case AppConstants.TaskType.OpLogExport:														
            try {														
              const oplogExportPrefix = `${task.licenseId}/${taskId}/`;														
              const oplogContainer = OPLOGS_CONTAINER;														
              const oplogWorkspaceDir = shareServiceClient														
                .getShareClient("taskworkspaces")														设计书里是"TaskWorkspaces"，下同
                .getDirectoryClient(`${taskId}/exports`);														
                            
              let fileSequence = 1;														
              for await (const item of oplogWorkspaceDir.listFilesAndDirectories()) {														
                if (														
                  item.kind === "file" &&														
                  item.name.startsWith("exportoplog_") &&														
                  item.name.endsWith(".zip")														
                ) {														
                  const fileClient = oplogWorkspaceDir.getFileClient(item.name);														
                  const newBlobName = `${task.taskName}_${fileSequence++}.zip`;														文件的序号要用源文件的，而且是001的格式（补0）
                  const downloadResponse = await fileClient.downloadToBuffer();														
                            
                  const blockBlobClient = blobServiceClient														
                    .getContainerClient(oplogContainer)														
                    .getBlobClient(`${oplogExportPrefix}${newBlobName}`)														
                    .getBlockBlobClient();														
                  await blockBlobClient.upload(														
                    downloadResponse,														
                    downloadResponse.length														
                  );														
                            
                  operationLogsToCreate.push({														
                    name: newBlobName,														
                    size: downloadResponse.length,														
                    retentionAt: null,														
                    licenseId: task.licenseId,														
                    fileName: newBlobName,														
                    generatedByTaskId: taskId,														需要添加登录日时createdAt
                  });														
                }														
              }														
                            
              if (operationLogsToCreate.length > 0) {														
                taskUpdateData = {														
                  status: AppConstants.TaskStatus.CompletedSuccess,														
                  endedAt: new Date(),														
                  // 操作ログエクスポート成功時のメッセージ														
                  resultMessage: "操作ログ一覧画面で操作ログファイルをダウンロードしてください。ログ名は{タスク名}_{連番}です。",														这个message被定义为EMET0014了
                };														
              } else {														
                taskUpdateData = {														
                  status: AppConstants.TaskStatus.CompletedSuccess,														
                  endedAt: new Date(),														更新成ERROR
                  // エクスポート対象なし														
                  resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0013),														EMET0015
                };														
              }														
            } catch (copyError) {														
              taskUpdateData = {														
                status: AppConstants.TaskStatus.CompletedError,														
                endedAt: new Date(),														
                resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0003),														
                errorMessage:														
                  copyError instanceof Error														
                    ? copyError.message														
                    : String(copyError),														
                errorCode: AppConstants.ERROR_CODES.EMET0003,														
              };														
            }														
            break;														
          // 4b. 管理項目定義のエクスポートタスクの場合														
          case AppConstants.TaskType.MgmtItemExport:														
            try {														
              const assetsExportPrefix = `${task.licenseId}/exports/${taskId}/`;														
              const assetsContainer = ASSETS_CONTAINER;														
              const assetsWorkspaceDir = shareServiceClient														
                .getShareClient("taskworkspaces")														
                .getDirectoryClient(`${taskId}/exports`);														
              const sourceFileClient = assetsWorkspaceDir.getFileClient(														
                "assetsfield_def.csv"														
              );														目标文件不存在时更新task为ERROR，EMET0015
              const downloadResponse =														
                await sourceFileClient.downloadToBuffer();														
              const blockBlobClient = blobServiceClient														
                .getContainerClient(assetsContainer)														
                .getBlobClient(`${assetsExportPrefix}assetsfield_def.csv`)														
                .getBlockBlobClient();														
              await blockBlobClient.upload(														
                downloadResponse,														
                downloadResponse.length														
              );														
              taskUpdateData = {														
                status: AppConstants.TaskStatus.CompletedSuccess,														
                endedAt: new Date(),														
                // 管理項目定義エクスポート成功時のメッセージ														
                resultMessage: "管理項目定義一覧画面で管理項目定義ファイルをダウンロードしてください。",														不需要更新resultMessage
              };														
            } catch (copyError) {														
              taskUpdateData = {														
                status: AppConstants.TaskStatus.CompletedError,														
                endedAt: new Date(),														
                resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0003),														
                errorMessage:														
                  copyError instanceof Error														
                    ? copyError.message														
                    : String(copyError),														
                errorCode: AppConstants.ERROR_CODES.EMET0003,														
              };														
            }														
            break;														
          // 4c. 管理項目定義のインポートタスクの場合														
          case AppConstants.TaskType.MgmtItemImport:														
            taskUpdateData = {														
              status: AppConstants.TaskStatus.CompletedSuccess,														
              endedAt: new Date(),														
              // インポート成功時のメッセージ														
              resultMessage: "インポートに成功しました。",														不需要更新resultMessage
            };														
            break;														
        }														
        break;														
                            
      case "Failed":														
        // Runbookが失敗した場合の処理														
        let errorDetail = "";														
        const errorFileClient = shareServiceClient														
          .getShareClient("taskworkspaces")														
          .getDirectoryClient(`${taskId}/exports`)														
          .getFileClient("errordetail.txt");														
        if (await errorFileClient.exists()) {														
          const downloadResponse = await errorFileClient.downloadToBuffer();														
          errorDetail = downloadResponse.toString("utf-8");														
        }														
        if (errorDetail) {														
          taskUpdateData = {														
            status: AppConstants.TaskStatus.CompletedError,														
            endedAt: new Date(),														
            resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0011, [errorDetail]),														
            errorMessage: exception,														
            errorCode: AppConstants.ERROR_CODES.EMET0011,														
          };														
        } else {														
          taskUpdateData = {														
            status: AppConstants.TaskStatus.CompletedError,														
            endedAt: new Date(),														
            resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0012),														
            errorMessage: exception,														
            errorCode: AppConstants.ERROR_CODES.EMET0012,														
          };														
        }														
        break;														
                            
      // メンテナンスや手動停止など、エラー以外の理由でジョブが停止した場合の処理														
      case "Removing":														
      case "Stopped":														
      case "Stopping":														
        context.warn(`[RunbookProcessorFunc] タスクID ${taskId} のジョブがメンテナンス/手動停止状態。`);														
        taskUpdateData = {														
          status: AppConstants.TaskStatus.CompletedError,														
          endedAt: new Date(),														
          resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0010),														
          errorCode: AppConstants.ERROR_CODES.EMET0010,														
        };														
        break;														
                            
      // ジョブが中断またはタイムアウトした場合の処理（ジョブ停止APIの呼び出しが必要）														
      case "Resuming":														
      case "Suspended":														
      case "Suspending":														
        try {														
          await stopAutomationJob(taskId);														
          context.log(`[RunbookProcessorFunc] タスクID ${taskId} のジョブ停止API呼び出し成功。`);														
          taskUpdateData = {														
            status: AppConstants.TaskStatus.CompletedError,														
            endedAt: new Date(),														
            resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0010),														
            errorCode: AppConstants.ERROR_CODES.EMET0010,														
          };														
        } catch (stopError) {														
          context.error(`[RunbookProcessorFunc] ジョブ停止API呼び出し失敗: タスクID ${taskId}:`, stopError);														
          throw stopError;														
        }														
        break;														
      case "Timeout":														
        try {														
          await stopAutomationJob(taskId);														
          context.log(`[RunbookProcessorFunc] タスクID ${taskId} のタイムアウト補償: ジョブ停止API呼び出し成功。`);														
          taskUpdateData = {														
            status: AppConstants.TaskStatus.CompletedError,														
            endedAt: new Date(),														
            resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0005),														
            errorCode: AppConstants.ERROR_CODES.EMET0005,														
          };														
        } catch (stopError) {														
          context.error(`[RunbookProcessorFunc] タイムアウト補償: ジョブ停止API呼び出し失敗: タスクID ${taskId}:`, stopError);														
          throw stopError;														
        }														
        break;														
    }														
                            
    // 5. 外部操作の結果を、単一の高速なDBトランザクションで確定させる														
    await prisma.$transaction(async (tx) => {														
      // 操作ログがあれば作成														
      if (operationLogsToCreate.length > 0) {														
        await tx.operationLog.createMany({ data: operationLogsToCreate });														
        context.log(`[RunbookProcessorFunc] タスクID ${taskId} のOperationLogレコード作成完了。`);														
      }														
                            
      // タスクの最終状態を更新														
      if (Object.keys(taskUpdateData).length > 0) {														
        await tx.task.update({ where: { id: taskId }, data: taskUpdateData });														where添加最终更新日时一致的条件。更新0件时retry
        context.log(`[RunbookProcessorFunc] タスクID ${taskId} の最終状態をDBに反映。`);														
      }														
                            
      // コンテナのロックを解放														
      await tx.containerConcurrencyStatus.updateMany({														
        where: {														
          targetVmName: task!.targetVmName!,														
          targetContainerName: task!.targetContainerName!,														where添加currentTaskId = taskId。更新0件时retry
        },														
        data: { status: "IDLE", currentTaskId: null },														
      });														
      context.log(`[RunbookProcessorFunc] タスクID ${taskId} のコンテナロック解放。`);														
    });														
                            
    // 6. 最後に、使用した一時作業ディレクトリをクリーンアップする														
    context.log(														放到DB事务前面
      `[RunbookProcessorFunc] タスクID ${taskId} の作業ディレクトリクリーンアップ開始。`														
    );														
    const taskDirClient = shareServiceClient														
      .getShareClient("taskworkspaces")														
      .getDirectoryClient(taskId);														
    await deleteDirectoryRecursive(taskDirClient, context);														
    context.log(`[RunbookProcessorFunc] タスクID ${taskId} の処理完了。`);														
  } catch (error) {														
    // 7. 予期せぬエラーが発生した場合、ログに記録し、リトライをトリガーする														
    context.error(														
      `[RunbookProcessorFunc] タスクID ${taskId} の処理中にエラー発生:`,														
      error														
    );														
    throw error;														
  }														
}														
                            
// 8. FunctionとService Busキューのバインド設定														
app.serviceBusQueue("RunbookProcessorFunc", {														
  connection: "SERVICE_BUS_CONNECTION_STRING",														
  queueName: "%SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME%",														
  handler: RunbookProcessorFunc,														
});														

```

## RunbookProcessorTimeoutFunc.ts
```typescript
/*														
 * @file RunbookProcessorTimeoutFunc.ts														
 * @fileoverview Runbookジョブ処理タイムアウト関数の実装である。														
 * @description														
 * 本関数はRunbookStatusQueueのDLQメッセージをトリガーに、														
 * タイムアウトしたRunbookジョブの補償処理（DBエラー更新・ロック解除・ファイル削除）を厳密に行う。														
 * 本設計の意図・処理フロー・エラー補償は本設計に準拠する。														
 * <AUTHOR>														
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.														
 */														
                            
import { app, InvocationContext } from "@azure/functions";														
import { prisma } from "../lib/prisma";														
import { AppConstants } from "../lib/constants";														
import { blobServiceClient, shareServiceClient } from "../lib/azureClients";														
                            
const ASSETS_CONTAINER = process.env.AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF!;														
const OPLOGS_CONTAINER = process.env.AZURE_STORAGE_CONTAINER_OPLOGS!;														
                            
/**														
 * RunbookStatusQueueのDLQメッセージを受信し、ジョブタイムアウト補償処理を実施する。														
 * @param {any} message - Service Bus DLQから受信したメッセージ														
 * @param {InvocationContext} context - Azure Functionsの実行コンテキスト														
 * @returns {Promise<void>} 非同期で補償処理を実行														
 * @description														
 * - taskId抽出、Task参照、RUNBOOK_PROCESSING判定、DB補償、ファイル削除、catch例外処理を一貫して行う。														
 * - すべての分岐・catch・外部依存呼び出し前後に詳細な日本語JSDoc注釈を付与する。														
 */														
export async function RunbookProcessorTimeoutFunc(message: any, context: InvocationContext): Promise<void> {														参数message的类型有unknown有any，统一一下比较好
  context.log("[RunbookProcessorTimeoutFunc] DLQメッセージ受信");														
  let taskId: string | undefined;														
  try {														
    /**														
     * @description メッセージからtaskIdを抽出し、存在しない場合は警告ログを出力して補償処理を終了する。														
     */														
    taskId = message?.body?.taskId || message?.taskId;														从message里取taskId的写法不统一，统一一下比较好
    context.log(`[RunbookProcessorTimeoutFunc] 受信taskId: ${taskId}`);														
    if (!taskId || typeof taskId !== "string") {														
      context.warn("[RunbookProcessorTimeoutFunc] taskIdが不足/不正。補償処理終了");														
      return;														
    }														
                            
    /**														
     * @description Taskテーブルから該当タスクを参照し、存在しない場合は警告ログを出力して補償処理を終了する。														
     */														
    const task = await prisma.task.findUnique({ where: { id: taskId } });														
    if (!task) {														
      context.warn(`[RunbookProcessorTimeoutFunc] taskId=${taskId} のタスクがDBに存在しない。補償処理終了`);														
      return;														
    }														
    /**														
     * @description タスクがRUNBOOK_PROCESSING以外の状態の場合は補償不要としてログ出力し、補償処理を終了する。														
     */														
    if (task.status !== AppConstants.TaskStatus.RunbookProcessing) {														
      context.log(`[RunbookProcessorTimeoutFunc] タスク${taskId}は既に最終状態(${task.status})。補償不要。補償処理終了`);														
      return;														
    }														
                            
    /**														
     * @description DBトランザクションでコンテナロック解放とTask状態更新をアトミックに実施する。失敗時はcatchでエラーログを出力し、処理を継続する。														
     */														
    try {														
      await prisma.$transaction(async (tx) => {														
        /**														不需要，删除
         * @description Server参照→ContainerConcurrencyStatusロック解放。BUSYかつcurrentTaskId一致時のみIDLE化。該当しない場合はログ出力。														
         */														
        let lockReleased = false;														
        if (task.targetServerId) {														
          const server = await tx.server.findUnique({ where: { id: task.targetServerId } });														
          if (server && server.azureVmName && server.dockerContainerName) {														
            const ccs = await tx.containerConcurrencyStatus.findUnique({														
              where: {														
                targetVmName_targetContainerName: {														
                  targetVmName: server.azureVmName!,														
                  targetContainerName: server.dockerContainerName!,														
                },														
              },														
            });														
            if (ccs && ccs.currentTaskId === taskId && ccs.status === "BUSY") {														
              await tx.containerConcurrencyStatus.update({														
                where: {														
                  targetVmName_targetContainerName: {														
                    targetVmName: server.azureVmName!,														
                    targetContainerName: server.dockerContainerName!,														where添加currentTaskId = taskId。更新0件时写log
                  },														
                },														
                data: { status: "IDLE", currentTaskId: null },														
              });														
              lockReleased = true;														不需要，删除
              context.log(`[RunbookProcessorTimeoutFunc] コンテナロック解放: ${server.azureVmName}/${server.dockerContainerName}`);														不需要，删除
            }														
          }														
        }														
        if (!lockReleased) {														不需要，删除
          context.log(`[RunbookProcessorTimeoutFunc] コンテナロック解放不要または失敗`);														不需要，删除
        }														不需要，删除
        /**														
         * @description Task状態をCOMPLETED_ERROR/EMET0005で更新する。														
         */														
        await tx.task.update({														
          where: { id: taskId },														where添加最终更新日时updatedAt一致。更新0件时写log
          data: {														
            status: AppConstants.TaskStatus.CompletedError,														
            errorCode: AppConstants.ERROR_CODES.EMET0005,														
            resultMessage:														
              "タスクの完了が確認できないため、システムによってタスクを中止しました。タスクが操作ログのエクスポートの場合、エクスポートするログの期間を短くして再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMET0005)",														AppConstants.ERROR_CODES.EMET0005
            endedAt: new Date(),														
          },														
        });														
        context.log(`[RunbookProcessorTimeoutFunc] タスク${taskId}をCOMPLETED_ERROR/EMET0005で更新`);														不需要，删除
      });														
      context.log(`[RunbookProcessorTimeoutFunc] トランザクション完了`);														不需要，删除
    } catch (err: any) {														
      /**														
       * @description DBトランザクション失敗時はエラーログを出力し、補償処理を継続する。														
       */														
      context.error(`[RunbookProcessorTimeoutFunc] DBトランザクション失敗: ${err.message}`);														
    }														
                            
    /**														
     * @description Azure Files作業ディレクトリ削除。失敗時は警告ログを出力し、補償処理を継続する。														
     */														
    try {														
      const shareClient = shareServiceClient.getShareClient("taskworkspaces");														设计书里是"TaskWorkspaces"
      const dirClient = shareClient.getDirectoryClient(taskId);														
      await dirClient.deleteIfExists();														ShareDirectoryClient的删除文件夹的方法确实需要先把文件夹清空才能删，故用ShareDirectoryClient的话确实需要自己写递归删除方法。而ShareClient的删除方法并没有说要先清空，可以尝试，可行的话就不用自己写。
      context.log(`[RunbookProcessorTimeoutFunc] 作業ディレクトリ TaskWorkspaces/${taskId} 削除成功`);														不需要，删除
    } catch (err: any) {														
      context.warn(`[RunbookProcessorTimeoutFunc] 作業ディレクトリ削除失敗: ${err.message}`);														
    }														
    context.log("[RunbookProcessorTimeoutFunc] 補償処理完了");														
  } catch (err: any) {														
    /**														
     * @description 予期せぬ致命的例外発生時はエラーログを出力する。														
     */														
    context.error(`[RunbookProcessorTimeoutFunc] 致命的例外: ${err.message}`);														
  }														
}														
                            
// FunctionとService Busキューのバインド設定														
app.serviceBusQueue("RunbookProcessorTimeoutFunc", {														
  connection: "SERVICE_BUS_CONNECTION_STRING",														
  queueName: "%SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME%/$DeadLetterQueue",														
  handler: RunbookProcessorTimeoutFunc,														
}); 														

```

## TaskCancellationFunc.ts
```typescript
/**														
 * タスク中止処理のエントリポイントである。														
 * @param message Service Busから受信したメッセージ（taskIdを含む）														
 * @param context Azure Functionsの実行コンテキスト														
 * @returns void														
 * @throws 致命的なパラメータエラーやDBエラー時に例外をthrowする														
 */														
export async function TaskCancellationFunc(message: any, context: any): Promise<void> {														参数message的类型有unknown有any，统一一下比较好。其他的函数的context是InvocationContext类型
  // taskId型チェック（非文字列は致命的エラー）														
  if (!message || typeof message.taskId !== "string") {														
    if (context && typeof context.error === "function") {														不需要，删除
      context.error("[TaskCancellationFunc] 致命的エラー：taskIdが文字列でない、または未指定である。");														不需要，删除
    }														不需要，删除
    throw new Error("[TaskCancellationFunc] 致命的エラー：taskIdが文字列でない、または未指定である。");														
  }														
  const { taskId } = message;														从message里取taskId的写法不统一，统一一下比较好
  try {														
    // 1. メッセージ解析・taskId取得														
    context.log(`[TaskCancellationFunc] taskId受信: ${taskId}`);														
    if (!taskId) {														
      context.error("[TaskCancellationFunc] 致命的: メッセージにtaskIdが存在しない。処理を中断する。");														
      throw new Error("taskIdが存在しない");														
    }														
                            
    // 2. DBからタスク情報取得														
    const task = await prisma.task.findUnique({ where: { id: taskId } });														
    if (!task) {														
      context.error(`[TaskCancellationFunc] 中止対象のタスク(ID: ${taskId})がDBに存在しない。`);														
      throw new Error("中止対象タスクが存在しない");														
    }														
                            
    // 3. タスク状態に応じた分岐処理														
    switch (task.status) {														
      case AppConstants.TaskStatus.PendingCancellation:														
        // PENDING_CANCELLATION→CANCELLEDへ														
        await prisma.task.update({														
          where: { id: taskId },														where添加currentTaskId = taskId。更新0件时写log，throw（retry）
          data: {														
            status: AppConstants.TaskStatus.Cancelled,														
            resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0004),														
            endedAt: new Date(),														
          },														
        });														
        context.log(`[TaskCancellationFunc] タスクID ${taskId} をCANCELLED/EMET0004で更新。`);														
        break;														
      case AppConstants.TaskStatus.RunbookSubmitted:														PENDING_CANCELLATION以外都是写log，throw（retry）
      case AppConstants.TaskStatus.RunbookProcessing:														
        // 実行中は中止不可														
        await prisma.task.update({														
          where: { id: taskId },														
          data: {														
            resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0011),														
          },														
        });														
        context.warn(`[TaskCancellationFunc] タスクID ${taskId} は実行中のため中止不可。EMET0011でresultMessageのみ更新。`);														
        break;														
      case AppConstants.TaskStatus.CompletedSuccess:														
      case AppConstants.TaskStatus.CompletedError:														
      case AppConstants.TaskStatus.Cancelled:														
        // 既に最終状態→冪等スキップ														
        context.log(`[TaskCancellationFunc] タスクID ${taskId} は既に最終状態(${task.status})。冪等スキップ。`);														
        break;														
      case AppConstants.TaskStatus.Queued:														
        // 実行待ち→CANCELLEDへ														
        await prisma.task.update({														
          where: { id: taskId },														
          data: {														
            status: AppConstants.TaskStatus.Cancelled,														
            resultMessage: formatTaskErrorMessage(AppConstants.ERROR_CODES.EMET0004),														
            endedAt: new Date(),														
          },														
        });														
        context.log(`[TaskCancellationFunc] タスクID ${taskId} (QUEUED) をCANCELLED/EMET0004で更新。`);														
        break;														
      default:														
        // 不明な状態														
        context.warn(`[TaskCancellationFunc] タスクID ${taskId} の状態が不明(${task.status})。DB更新せず警告のみ。`);														
        break;														
    }														
  } catch (error) {														
    // 重大な例外はエラーログ出力し再スロー														
    context.error(`[TaskCancellationFunc] タスクID ${taskId} の処理中にエラー発生:`, error);														
    throw error;														
  }														
}														
                            
// FunctionとService Busキューのバインド設定														
app.serviceBusQueue("TaskCancellationFunc", {														
  connection: "AZURE_SERVICEBUS_NAMESPACE_HOSTNAME",														
  queueName: "%SERVICE_BUS_TASK_CONTROL_QUEUE_NAME%",														
  handler: TaskCancellationFunc,														
}); 														

```

## TaskCancellationTimeoutFunc.ts
```typescript
/**														
 * @fileoverview タスク中止タイムアウト補償処理を行うAzure Functionである。														
 * @description Service BusのDLQから中止要求メッセージを受信し、タスク状態を補償的に更新する。主にPENDING_CANCELLATION等のタスクをCANCELLED/EMET0004等で補償し、各分岐・異常時に詳細なログを出力する。														
 * <AUTHOR>														
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.														
 */														
import { prisma } from "../lib/prisma";														
import { AppConstants } from "../lib/constants";														
import { formatTaskErrorMessage } from "../lib/utils";														
import { app } from "@azure/functions";														
                            
/**														
 * タスク中止タイムアウト補償処理のエントリポイントである。														
 * @param {unknown} message - Service Bus DLQから受信したメッセージ														
 * @param {any} context - Azure Functionsの実行コンテキスト														
 * @returns {Promise<void>} 非同期完了を示すPromise														
 */														
export async function TaskCancellationTimeoutFunc(message: unknown, context: any): Promise<void> {														参数message的类型有unknown有any，统一一下比较好。其他的函数的context是InvocationContext类型
  let taskId: string | undefined;														
  let task: any = null;														
  try {														
    // 1. メッセージ解析・taskId取得														
    const messageBody = message as { taskId: string; DeadLetterReason?: string; DeadLetterErrorDescription?: string };														不需要DeadLetterReason和DeadLetterErrorDescription
    taskId = messageBody.taskId;														从message里取taskId的写法不统一，统一一下比较好
    context.log(`[TaskCancellationTimeoutFunc] DLQメッセージ受信: taskId=${taskId}`);														
    if (messageBody.DeadLetterReason || messageBody.DeadLetterErrorDescription) {														不需要，删除
      context.log(`[TaskCancellationTimeoutFunc] DeadLetterReason=${messageBody.DeadLetterReason}, DeadLetterErrorDescription=${messageBody.DeadLetterErrorDescription}`);														不需要，删除
    }														不需要，删除
    if (!taskId) {														
      context.error("[TaskCancellationTimeoutFunc] 致命的: メッセージにtaskIdが存在しない。処理を中断する。");														
      return;														
    }														
                            
    // 2. DBからタスク情報取得														
    task = await prisma.task.findUnique({ where: { id: taskId } });														
    if (!task) {														
      context.error(`[TaskCancellationTimeoutFunc] タスクID ${taskId} がDBに存在しない。補償処理中断。`);														
      return;														
    }														
                            
    // 3. タスク状態に応じた補償分岐														
    switch (task.status) {														
      case AppConstants.TaskStatus.PendingCancellation:														
        // PENDING_CANCELLATION → CANCELLED, resultMessage: EMET0004, endedAt更新														
        await prisma.task.update({														
          where: { id: taskId },														where添加currentTaskId = taskId。更新0件时写log，终了
          data: {														
            status: AppConstants.TaskStatus.Cancelled,														更新成ERROR
            resultMessage: formatTaskErrorMessage("EMET0004"),														EMET0006
            endedAt: new Date(),														
          },														
        });														
        context.log(`[TaskCancellationTimeoutFunc] タスクID ${taskId} をCANCELLED/EMET0004で補償更新。`);														EMET0006
        break;														
      case AppConstants.TaskStatus.RunbookSubmitted:														PENDING_CANCELLATION以外都是写log，终了
      case AppConstants.TaskStatus.RunbookProcessing:														
        // 実行中: status/endedAt不変, resultMessage: EMET0006														
        await prisma.task.update({														
          where: { id: taskId },														
          data: {														
            resultMessage: formatTaskErrorMessage("EMET0006"),														
          },														
        });														
        context.log(`[TaskCancellationTimeoutFunc] タスクID ${taskId} 実行中: EMET0006でresultMessageのみ補償更新。`);														
        break;														
      case AppConstants.TaskStatus.CompletedSuccess:														
      case AppConstants.TaskStatus.CompletedError:														
      case AppConstants.TaskStatus.Cancelled:														
        // 最終状態: DB更新なし、infoログ														
        context.log(`[TaskCancellationTimeoutFunc] タスクID ${taskId} は既に最終状態(${task.status})。補償不要。`);														
        break;														
      case AppConstants.TaskStatus.Queued:														
        // QUEUED: status→CANCELLED, resultMessage: EMET0004, endedAt更新														
        await prisma.task.update({														
          where: { id: taskId },														
          data: {														
            status: AppConstants.TaskStatus.Cancelled,														
            resultMessage: formatTaskErrorMessage("EMET0004"),														
            endedAt: new Date(),														
          },														
        });														
        context.log(`[TaskCancellationTimeoutFunc] タスクID ${taskId} (QUEUED) をCANCELLED/EMET0004で補償更新。`);														
        break;														
      default:														
        // 不明な状態: 警告ログ														
        context.warn(`[TaskCancellationTimeoutFunc] タスクID ${taskId} の状態が不明(${task.status})。補償DB更新せず警告のみ。`);														
        break;														
    }														
  } catch (err: any) {														
    context.error(`[TaskCancellationTimeoutFunc] タスクID ${taskId} の補償処理中にエラー発生:`, err);														
    // DLQなので再throwせずreturn（DLQに残す）														
    return;														
  }														
} 														
                            
// FunctionとService Busキューのバインド設定														
app.serviceBusQueue("TaskCancellationTimeoutFunc", {														
  connection: "AZURE_SERVICEBUS_NAMESPACE_HOSTNAME",														
  queueName: "%SERVICE_BUS_TASK_CONTROL_QUEUE_NAME%/$DeadLetterQueue",														
  handler: TaskCancellationTimeoutFunc,														
}); 														

```

## azureClients.ts
```typescript
/*														
 * @file azureClients.ts														
 * @description														
 * Azure Storage・Automation API等のクライアント初期化・認証・API呼び出しを一元管理するモジュール。														
 * 環境変数から設定値を取得し、Blob/File/Automation REST APIの認証済みクライアントを提供。														
 *														
 * 【設計意図】														
 * - Azureリソースへのアクセス・認証・API呼び出しの共通処理を集約し、各Functionからの利用を簡素化。														
 * - 環境変数の検証・認証トークン自動付与・APIエンドポイントの一元管理で、運用性・保守性を向上。														
 *														
 * <AUTHOR>														
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.														
 */														
import { BlobServiceClient } from "@azure/storage-blob";														
import { ShareServiceClient } from "@azure/storage-file-share";														
import { DefaultAzureCredential } from "@azure/identity";														
import axios from "axios";														之前的endpoint工程里没有用过的库，是否有必要新引进需要检讨
import { ServiceBusClient } from "@azure/service-bus";														
                            
// 1. 環境変数から設定値を取得する														
const storageConnectionString = process.env.AZURE_STORAGE_CONNECTION_STRING;														
const storageAccountName = process.env.AZURE_STORAGE_ACCOUNT_NAME;														
const automationAccount = process.env.AZURE_AUTOMATION_ACCOUNT_NAME;														
const subscriptionId = process.env.SUBSCRIPTION_ID;														
const resourceGroupName = process.env.RESOURCE_GROUP_NAME;														
const fullyQualifiedNamespace = process.env.AZURE_SERVICEBUS_NAMESPACE_HOSTNAME__fullyQualifiedNamespace;														
                            
// 2. 重要な環境変数が設定されているか実行時にチェックする														
if (														
  !storageConnectionString ||														
  !storageAccountName ||														
  !automationAccount ||														
  !subscriptionId ||														
  !resourceGroupName ||														
  !fullyQualifiedNamespace														
) {														
  throw new Error(														
    "必要な環境変数が設定されていないため、処理を継続できない。次の変数を確認すること：" +														
      "AZURE_STORAGE_CONNECTION_STRING, AZURE_STORAGE_ACCOUNT_NAME, AZURE_AUTOMATION_ACCOUNT_NAME, " +														
      "SUBSCRIPTION_ID, RESOURCE_GROUP_NAME, AZURE_SERVICEBUS_NAMESPACE_HOSTNAME__fullyQualifiedNamespace"														
  );														
}														
                            
// Azure Storageクライアントの初期化（BlobServiceClientは托管标识を使用）														
export const blobServiceClient = new BlobServiceClient(														
  `https://${storageAccountName}.blob.core.windows.net`,														
  new DefaultAzureCredential()														
);														
// Azure Storageクライアントの初期化（ShareServiceClientは接続文字列を使用）														
export const shareServiceClient = ShareServiceClient.fromConnectionString(														
  storageConnectionString														
);														
                            
// Azure Automation REST APIクライアントの初期化 (Axiosを使用)														
export const automationApi = axios.create({														
  baseURL: "https://management.azure.com",														
  timeout: 10000,														
});														
                            
// リクエストインターセプター：各API呼び出しの前に自動的に認証トークンを付与する														
automationApi.interceptors.request.use(async (config) => {														
  const credential = new DefaultAzureCredential();														
  const { token } = await credential.getToken(														
    "https://management.azure.com/.default"														
  );														
  config.headers.Authorization = `Bearer ${token}`;														
  return config;														
});														
                            
// Service Bus クライアント（托管标识认证）														
export const serviceBusClient = new ServiceBusClient(														
  fullyQualifiedNamespace,														
  new DefaultAzureCredential()														
);														
                            
/**														
 * 指定された情報でAzure Automationジョブを作成する関数														
 * @param jobName ジョブの一意な名前 (通常はtaskId)														
 * @param runbookName 実行するRunbookの名前														
 * @param parameters Runbookに渡すパラメータ														
 * @param runOn ジョブを実行するHybrid Worker Groupの名前														
 */														
export async function createAutomationJob(														
  jobName: string,														
  runbookName: string,														
  parameters: any,														
  runOn: string														
) {														
  const url = `/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}/providers/Microsoft.Automation/automationAccounts/${automationAccount}/jobs/${jobName}?api-version=2023-11-01`;														
  const body = {														
    properties: {														
      runbook: { name: runbookName },														
      parameters,														
      runOn,														
    },														
  };														
  return automationApi.put(url, body);														
}														
                            
/**														
 * 指定されたAzure Automationジョブを停止する関数														
 * @param jobName 停止するジョブの名前														
 */														
export async function stopAutomationJob(jobName: string) {														
  const url = `/subscriptions/${subscriptionId}/resourceGroups/${resourceGroupName}/providers/Microsoft.Automation/automationAccounts/${automationAccount}/jobs/${jobName}/stop?api-version=2023-11-01`;														
  return automationApi.post(url);														
}														

```

## tasks.ts
```typescript
/**														
 * @fileoverview タスク関連のサーバーアクションを提供するモジュールである。														
 * @description サーバー一覧画面からのバックグラウンドタスク要求（操作ログエクスポート、管理項目定義インポート・エクスポート等）を一元的に受け付け、														
 *              厳格なパラメータ検証、DBトランザクション、ServiceBus連携、エラー処理を実装する。														
 * <AUTHOR>														
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.														
 */														
                            
import Logger, { LogFunctionSignature } from "@/app/lib/logger";														
import prisma from "@/app/lib/prisma";														
import { v4 as uuidv4 } from "uuid";														
import {														
  CreateTaskActionResult,														
  ENV,														
  PORTAL_ERROR_MESSAGES,														
  TASK_ACTION_NAME,														
  TASK_CONFIG,														
  TASK_STATUS,														
  TASK_TYPE,														
  CONTAINER_STATUS,														
} from "../definitions";														
import ServerData from "../data";														
import { getIronSession } from "iron-session";														
import { cookies } from "next/headers";														
import { SessionData, sessionOptions } from "../session";														
import { revalidatePath } from "next/cache";														
import { formatMessage } from "../utils";														
import { BlobActions } from "./blob";														
import { ServiceBusActions } from "../service-bus";														
                            
/**														
 * タスク関連のサーバーアクションを提供するクラスである。														
 * 本クラスは、ポータル画面からの各種バックグラウンドタスク要求（操作ログエクスポート、管理項目定義インポート/エクスポート等）を一元的に受け付け、														
 * サーバー側での厳格な入力検証・権限制御・DBトランザクション・ServiceBus連携・補償処理を責任持って実施するために設計されている。														
 * これにより、業務要件の堅牢な実装と、将来的なタスク種別追加・仕様変更への拡張性を両立する。														
 */														
export class TaskActions {														
  /**														
   * サーバー一覧画面からのタスク作成要求を受け付け、パラメータ検証・DB登録・ServiceBus送信を行うサーバーアクションである。														
   * @param {FormData} formData クライアントから送信されたフォームデータ														
   * @returns {Promise<CreateTaskActionResult>} タスク受付処理の結果														
   * <AUTHOR>														
   * @copyright Copyright © 2025 Hitachi Solutions, Ltd.														
   */														
  @LogFunctionSignature()														
  static async createTaskAction(formData: FormData): Promise<CreateTaskActionResult> {														
    // 受信したFormDataの内容を詳細にログ出力する（監査・トラブルシュート用）														
    Logger.info({														
      message: "createTaskAction invoked",														
      formData: formDataToObject(formData),														
    });														
    // 共通パラメータ検証														
    let uploadedBlobPath: string | undefined = undefined;														
    const taskType = formData.get("taskType") as string;														
    const serverId = formData.get("serverId") as string;														
    if (!taskType) {														
      return {														
        success: false,														
        message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0016, ["タスク種別"]),														EMEC0021
      };														
    }														
    if (!serverId) {														
      return {														
        success: false,														
        message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0016, ["サーバー"]),														EMEC0021
      };														
    }														
    const validTaskTypes = Object.values(TASK_TYPE);														
    if (!validTaskTypes.includes(taskType as any)) {														
      Logger.error({														
        message: `サポートされていないタスク種別を受け付けました: ${taskType}`,														受け付けました→受信しました
        source: "createTaskAction",														
      });														
      return {														
        success: false,														
        message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0021, [TASK_ACTION_NAME.START]),														
      };														
    }														
    const session = await getIronSession<SessionData>(cookies(), sessionOptions);														
    if (!session.user?.userId || !session.user.licenseId) {														
      Logger.error({														
        message: "ユーザーセッションが見つからないか、無効です。",														
        source: "createTaskAction",														
      });														
      return {														
        success: false,														
        message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0021, [TASK_ACTION_NAME.START]),														
      };														
    }														
    const serverDetails = await ServerData.getServerDetailsForTask(serverId);														
    if (!serverDetails) {														
      Logger.error({														
        message: `ライセンスID ${session.user.licenseId} に対して、サーバーID ${serverId} が見つかりません。`,														
        source: "createTaskAction",														
        serverId,														
        licenseId: session.user.licenseId,														
      });														
      return {														
        success: false,														
        message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0021, [TASK_ACTION_NAME.START]),														
      };														
    }														
    if (!serverDetails.azureVmName || !serverDetails.dockerContainerName || !serverDetails.hrwGroupName) {														
      Logger.error({														
        message: `サーバー ${serverId} はタスク実行用に構成されていません。`,														
        source: "createTaskAction",														
        serverDetails,														
      });														
      return {														
        success: false,														
        message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0021, [TASK_ACTION_NAME.START]),														
      };														
    }														
    if (serverDetails.licenseId !== session.user.licenseId) {														不需要，删除
      Logger.error({														
        message: `権限がありません。ユーザー ${session.user.userId} (ライセンスID: ${session.user.licenseId}) が、ライセンスID ${serverDetails.licenseId} に属するサーバー ${serverId} でタスクを実行しようとしました。`,														
        source: "createTaskAction",														
        sessionUser: session.user,														
        serverLicense: serverDetails.licenseId,														
      });														
      return {														
        success: false,														
        message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0021, [TASK_ACTION_NAME.START]),														
      };														
    }														
    let taskId = uuidv4();														同时生成taskName
    let taskSpecificParamsForDb: any = {};														
    switch (taskType) {														
      case TASK_TYPE.OPLOG_EXPORT: {														
        /**														
         * 操作ログエクスポート用パラメータ検証。														
         * - exportStartDate: 必須、YYYY-MM-DD形式。														
         * - exportEndDate: 必須、YYYY-MM-DD形式。														
         * - maxExportDaysSpan: 必須、数値。														
         * - exportEndDate >= exportStartDate。														
         * - 最大日数超過チェック。														
         */														
        const exportStartDate = formData.get("exportStartDate") as string;														
        if (!exportStartDate) {														
          return {														
            success: false,														
            message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0016, ["開始日"]),														
          };														
        }														
        const exportEndDate = formData.get("exportEndDate") as string;														
        if (!exportEndDate) {														
          return {														
            success: false,														
            message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0016, ["終了日"]),														
          };														
        }														
        let maxExportDaysSpan: number | undefined = undefined;														
        const maxExportDaysSpanStr = formData.get("maxExportDaysSpan") as string;														formData里没有最大期间，需要去LOV表里取
        if (maxExportDaysSpanStr && !isNaN(Number(maxExportDaysSpanStr))) {														
          maxExportDaysSpan = Number(maxExportDaysSpanStr);														
        } else {														
          // LOVから取得														
          const lov = await ServerData.fetchCachedLov("OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN");														
          if (lov && !isNaN(Number(lov.value))) {														
            maxExportDaysSpan = Number(lov.value);														
          }														
        }														
        if (!maxExportDaysSpan) {														
          return {														
            success: false,														
            message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0016, ["最大日数"]),														EMEC0021
          };														
        }														
        // 日付先後チェック														
        const start = new Date(exportStartDate);														
        const end = new Date(exportEndDate);														
        if (end < start) {														
          return {														
            success: false,														
            message: PORTAL_ERROR_MESSAGES.EMEC0024,														
          };														
        }														
        // 最大日数超過チェックを行う														
        const diffDays = Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;														没有日期相减的方法吗？
        if (diffDays > maxExportDaysSpan) {														
          return {														
            success: false,														
            message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0020, [maxExportDaysSpan.toString(), maxExportDaysSpan.toString()]),														
          };														
        }														
        taskSpecificParamsForDb = { exportStartDate, exportEndDate };														
        // ここでreturnしない。以降の共通処理に進む。														
        break;														
      }														
      case TASK_TYPE.MGMT_ITEM_IMPORT: {														
        // 管理項目定義インポート用パラメータ検証・ファイルアップロード														
        const importFile = formData.get("importFile") as File | null;														
        const originalFileName = formData.get("originalFileName") as string | null;														
        if (!importFile || !originalFileName || importFile.size === 0) {														
          // ファイル未指定・空ファイル時のエラー														
          return {														
            success: false,														
            message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0016, ["ファイル"]),														
          };														
        }														
        const isCsv = importFile.type === "text/csv" || (originalFileName && originalFileName.endsWith(".csv"));														这个条件的话只要文件扩展名是csv就行，需要判断实际的文件格式（前半部分）
        if (!isCsv) {														
          // CSV以外のファイル形式エラー														
          return {														
            success: false,														
            message: PORTAL_ERROR_MESSAGES.EMEC0017,														
          };														
        }														
        let uploadedBlobPath = "";														
        try {														
          // ファイルをBlobストレージへ一時アップロード														
          uploadedBlobPath = await BlobActions.uploadFile(														
            importFile,														
            ENV.AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF!,														
            `${session.user.licenseId}/imports/${uuidv4()}/assetsfield_def.csv`,														uuidv4()→taskId
          );														
        } catch (e) {														
          // アップロード失敗時の補償処理・エラー応答														
          Logger.error({ message: "管理項目定義インポート: Blobアップロード失敗。" + (e as Error).message, error: e });														
          return {														
            success: false,														
            message: PORTAL_ERROR_MESSAGES.EMEC0018,														
          };														
        }														
        // パラメータをDB用に構築														
        taskSpecificParamsForDb = {														
          importedFileBlobPath: uploadedBlobPath,														
          originalFileName,														
        };														
        break;														
      }														
      case TASK_TYPE.MGMT_ITEM_EXPORT:														
        // 管理項目定義エクスポートは追加パラメータなし														
        taskSpecificParamsForDb = {};														
        break;														
      default:														69行已经判断过对象外的task种别了，这里不需要
        // サポート外タスク種別														
        return {														
          success: false,														
          message: `サポートされていないタスク種別です: ${taskType}`,														
        };														
    }														
                            
    // 5. コンテナ同時実行状態確認														"放在126行之前
需要判断该容器的记录是否存在，不存在的话在下面的DB事务里创建"
    // Azure VM名とDockerコンテナ名を複合キーにContainerConcurrencyStatusテーブルからステータス取得。														
    // BUSYならEMEC0022でエラー応答。														
    const containerStatus = await ServerData.getContainerStatus(														
      serverDetails.azureVmName!,														
      serverDetails.dockerContainerName!,														
    );														
    if (containerStatus === CONTAINER_STATUS.BUSY) {														
      // BUSY時はEMEC0022でエラー応答														
      return {														
        success: false,														
        message: formatMessage(PORTAL_ERROR_MESSAGES.EMEC0022, [														
          serverDetails.name,														
        ]),														
      };														
    }														
                            
    // 6. DBトランザクション開始・タスクレコード作成・古いタスク削除														
    // トランザクション内でContainerConcurrencyStatusレコード新規作成（必要時）、Taskレコード作成、保持ポリシーに基づく古いタスク削除を実施。														
    let taskCreatedInDb = false;														
    try {														
      await prisma.$transaction(async (tx) => {														
        // 保持ポリシーに基づく古いタスクの削除														超过10件的task记录的删除处理放在TaskExecuteFunc里了
        const maxRetentionCount = await ServerData.getLovValue(														
          TASK_CONFIG.MAX_RETENTION_COUNT,														
          10														
        );														
        const tasksToDelete = await ServerData.getTasksForRetentionCheck(														
          tx,														
          serverId,														
          maxRetentionCount,														
        );														
        for (const task of tasksToDelete) {														
          await ServerData.deleteTaskAndAssociatedResourcesInTx(tx, task);														
        }														
        // タスクレコード作成														
        const parametersJson = JSON.stringify(taskSpecificParamsForDb);														
        const newTaskPayload = {														
          id: taskId,														
          taskType,														
          status: TASK_STATUS.QUEUED,														
          submittedByUserId: session.user!.userId,														
          licenseId: session.user!.licenseId,														
          targetServerId: serverId,														
          targetServerName: serverDetails.name,														
          targetVmName: serverDetails.azureVmName,														
          parametersJson,														添加taskName，targetContainerName，targetHRWGroupName
        };														
        const createdTask = await tx.task.create({ data: newTaskPayload });														
        taskCreatedInDb = true;														
        Logger.info(`タスクDB登録成功: taskId=${taskId}`);														容器执行状态表里不存在对象容器记录的话创建
        taskId = createdTask.id;														不需要，删除
      });														
    } catch (e) {														
      // 7. DBトランザクション失敗時の補償処理														
      // インポートタスクの場合はアップロード済みファイル削除														
      Logger.error({ message: "タスク作成トランザクション失敗", error: e });														
      if (taskType === TASK_TYPE.MGMT_ITEM_IMPORT && uploadedBlobPath) {														
        try {														
          await BlobActions.deleteBlob(														
            ENV.AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF!,														
            uploadedBlobPath,														
          );														
        } catch (delErr) {														
          Logger.error({ message: "補償処理: アップロード済みファイル削除失敗", error: delErr });														
        }														
      }														
      return {														
        success: false,														
        message: PORTAL_ERROR_MESSAGES.EMEC0006,														EMEC0021
      };														
    }														
    try {														
      const queueName = ENV.SERVICE_BUS_TASK_INPUT_QUEUE_NAME;														
      if (!queueName) {														
        throw new Error("タスク実行キュー名が未設定です。");														
      }														
      await ServiceBusActions.sendMessage(queueName, { taskId });														
      Logger.info(`Service Busへのメッセージ送信成功: taskId=${taskId}, queue=${queueName}`);														不需要，删除
    } catch (error) {														
      if (taskType === TASK_TYPE.MGMT_ITEM_IMPORT && taskSpecificParamsForDb?.importedFileBlobPath) {														删blob文件的逻辑跟上面不一样，跟上面统一
        try {														
          await BlobActions.deleteBlob(ENV.AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF!, taskSpecificParamsForDb.importedFileBlobPath);														
        } catch {}														
      }														添加如果taskCreatedInDb是true，删除已创建的task记录的处理
      Logger.error({ message: "Service Bus送信失敗", error });														
      return {														
        success: false,														
        message: PORTAL_ERROR_MESSAGES.EMEC0019,														
      };														
    }														
    Logger.info(`タスクID ${taskId} のPath再検証を開始`);														不需要，删除
    revalidatePath("/dashboard/tasks");														操作Log出力task的话同时revalidatePath("/dashboard/operation-logs");
    Logger.info(`タスクID ${taskId} のPath再検証が完了`);														不需要，删除
    Logger.info(`タスクID ${taskId} の処理が正常に完了、レスポンスを返します`);														不需要，删除
    revalidatePath("/dashboard/servers");														不需要，删除
    const taskTypeName =														
      {														
        [TASK_TYPE.OPLOG_EXPORT]: "操作ログのエクスポート",														
        [TASK_TYPE.MGMT_ITEM_IMPORT]: "管理項目定義のインポート",														
        [TASK_TYPE.MGMT_ITEM_EXPORT]: "管理項目定義のエクスポート",														
      }[taskType] || taskType;														
    return {														
      success: true,														
      message: formatMessage(														
        PORTAL_ERROR_MESSAGES.EMEC0025,														
        [serverDetails.name, taskTypeName]														
      ),														整体再包一层try，catch里返回EMEC0027
    };														
  }														
                            
  /**														在task-control.ts里实装的话删掉这里
   * タスク中止要求アクションである。														
   * @param {string} taskId - 中止要求対象のタスクID。														
   * @returns {Promise<CreateTaskActionResult>} 中止要求の結果（成功/失敗、メッセージ等）を返すPromise。														
   * @throws サーバー内部エラー発生時はPORTAL_ERROR_MESSAGES.EMEC0007を返す。														
   */														
  static async requestTaskCancellation(taskId: string): Promise<CreateTaskActionResult> {														
    // 未実装: 必要に応じて実装してください														
    throw new Error('requestTaskCancellation is not implemented.');														
  }														
}														
                            
/**														
 * FormDataを普通のオブジェクトに変換するユーティリティ関数である。														
 *														
 * @param {FormData} formData - 入力フォームデータ。														
 * @returns {Record<string, any>} 変換後のオブジェクト。														
 */														
function formDataToObject(formData: FormData): Record<string, any> {														
  const obj: Record<string, any> = {};														
  // FormDataの全エントリを走査し、File型はファイル名のみ記録する														
  Array.from(formData.entries()).forEach(([key, value]) => {														
    if (typeof File !== 'undefined' && value instanceof File) {														
      obj[key] = `[File: ${value.name}]`;														
    } else {														
      obj[key] = value;														
    }														
  });														
  return obj;														
} 														

```

## task-control.ts
```typescript
/**														
 * @fileoverview タスク制御関連サーバーアクションモジュールである。														
 * @description タスクの中止・再実行等の制御機能を集約し、入力検証・権限制御・DBトランザクション・ServiceBus連携・補償処理等を一元的に実装する。														
 * <AUTHOR>														
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.														
 */														
                            
import Logger, { LogFunctionSignature } from "@/app/lib/logger";														
import prisma from "@/app/lib/prisma";														
import { ServiceBusActions } from "../service-bus";														
import {														
  CreateTaskActionResult,														改为TaskCancellationActionResult
  ENV,														
  PORTAL_ERROR_MESSAGES,														
  TASK_STATUS,														
} from "../definitions";														
import { getIronSession } from "iron-session";														
import { cookies } from "next/headers";														
import { SessionData, sessionOptions } from "../session";														
import { revalidatePath } from "next/cache";														
                            
/**														
 * タスク制御関連のサーバーアクションを提供するクラスである。														
 * 本クラスは、ポータル画面からのタスク制御要求（中止・再実行等）を一元的に受け付け、														
 * サーバー側での厳格な入力検証・権限制御・DBトランザクション・ServiceBus連携・補償処理を責任持って実施する。														
 * これにより、業務要件の堅牢な実装と、将来的なタスク制御機能追加・仕様変更への拡張性を両立する。														
 */														
export class TaskControlActions {														
  /**														
   * タスクの中止要求を処理する。														
   *														
   * @param {string} taskId - 中止対象のタスクID。														
   * @returns {Promise<CreateTaskActionResult>} タスク中止要求の結果（成功/失敗・日本語メッセージ）。														改为TaskCancellationActionResult
   * @throws サーバー内部エラーや環境変数未設定時等はPORTAL_ERROR_MESSAGES.EMEC0023を返す。														不正确的注释，删除
   *														
   * なぜ：ユーザー操作に基づくタスク中止要求を安全かつ正確に受付・登録し、														
   * DB・ServiceBus等の外部リソースと連携しつつ、失敗時は補償処理も徹底することで、														
   * システムの信頼性・一貫性を担保するためである。														
   */														
  @LogFunctionSignature()														
  static async requestTaskCancellation(														
    taskId: string,														
  ): Promise<CreateTaskActionResult> {														改为TaskCancellationActionResult
    Logger.info("requestTaskCancellation: 開始");														
    const session = await getIronSession<SessionData>(cookies(), sessionOptions);														
    if (!session.user?.userId || !session.user.licenseId) {														
      Logger.error({														
        message: "ユーザーセッションが見つからないか、無効です。",														
        source: "requestTaskCancellation",														
        taskId,														
      });														
      return {														
        success: false,														
        message: PORTAL_ERROR_MESSAGES.EMEC0023,														EMEC0021
      };														
    }														
                            先判断一下taskId是不是空，是的话返回EMEC0021
    // タスクの存在と状態を確認														
    const task = await prisma.task.findUnique({														
      where: { id: taskId },														
    });														
                            
    if (!task) {														
      Logger.error({														
        message: `タスク ${taskId} が見つかりません。`,														
        source: "requestTaskCancellation",														
      });														
      return {														
        success: false,														
        message: PORTAL_ERROR_MESSAGES.EMEC0023,														EMEC0021
      };														
    }														
                            
    // タスクが中止可能な状態かチェック														
    if (														 QUEUED以外全部
      task.status === TASK_STATUS.COMPLETED_SUCCESS ||														
      task.status === TASK_STATUS.COMPLETED_ERROR ||														
      task.status === TASK_STATUS.CANCELLED ||														
      task.status === TASK_STATUS.PENDING_CANCELLATION														
    ) {														
      Logger.error({														
        message: `タスク ${taskId} は中止できない状態です。status=${task.status}`,														
        source: "requestTaskCancellation",														
      });														
      return {														
        success: false,														
        message: PORTAL_ERROR_MESSAGES.EMEC0023,														
      };														
    }														
                            
    try {														DB更新和bus送信的try分开，需要返回不同的message
      // タスク状態を中止要求中に更新														
      await prisma.task.update({														
        where: { id: taskId },														where添加最终更新日时updatedAt一致。
        data: { status: TASK_STATUS.PENDING_CANCELLATION },														
      });														更新0件时返回EMEC0023，更新失败时返回EMEC0021
                            
      // Service Busに中止メッセージを送信														
      const queueName = ENV.SERVICE_BUS_TASK_CONTROL_QUEUE_NAME;														
      if (!queueName) {														
        throw new Error("Service Busキュー名が未設定です。");														
      }														
      await ServiceBusActions.sendMessage(queueName, {														
        taskId,														
        action: "CANCEL",														不需要，删除
      });														送信失败时把task状态更新回QUEUED（条件是taskId和最终更新日时），并返回EMEC0019
                            
      Logger.info(`タスク ${taskId} の中止要求を受け付けました。`);														
      revalidatePath("/dashboard/tasks");														设计书里没写
                            
      return {														
        success: true,														
        message: "タスクの中止要求を受け付けました。",														EMEC0026
      };														
    } catch (error) {														
      Logger.error({														
        message: `タスク ${taskId} の中止要求処理中にエラーが発生しました。`,														
        errorMessage: (error as Error).message,														
        stack: (error as Error).stack,														
      });														
      return {														
        success: false,														
        message: PORTAL_ERROR_MESSAGES.EMEC0023,														EMEC0027
      };														
    }														
  }														
} 														

```

