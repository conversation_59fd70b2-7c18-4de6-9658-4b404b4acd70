
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for jcs-backend-services-standard/TaskExecuteTimeoutFunc/TaskExecuteTimeoutFunc.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">jcs-backend-services-standard/TaskExecuteTimeoutFunc</a> TaskExecuteTimeoutFunc.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/57</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/23</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/57</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * @fileoverview タスク実行タイムアウト処理関数 (TaskExecuteTimeoutFunc)
 * @description
 * TaskExecuteFuncの実行が例外やタイムアウトによって失敗し、TaskInputQueueのDLQへ
 * 配信されたメッセージを処理する補償関数。Azure Automationジョブの存在確認により
 * 補償の必要性を判定し、必要な場合のみタスク状態のクリーンアップとリソース解放を行う。
 *
 * @trigger Azure Service Bus - TaskInputQueue のDLQメッセージ
 * @input TaskInputQueue のDLQから受信する、元のタスク実行要求メッセージ
 * @output Azure SQL Database (Task、ContainerConcurrencyStatus) のレコード更新、
 *         Azure Files上のタスク作業ディレクトリ削除
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */
&nbsp;
<span class="cstat-no" title="statement not covered" >import { app, InvocationContext } from "@azure/functions";</span>
<span class="cstat-no" title="statement not covered" >import { prisma } from "../lib/prisma";</span>
<span class="cstat-no" title="statement not covered" >import { AppConstants } from "../lib/constants";</span>
<span class="cstat-no" title="statement not covered" >import { createShareServiceClient, getAutomationJobStatus } from "../lib/azureClients";</span>
<span class="cstat-no" title="statement not covered" >import { deleteTaskWorkspaceDirectory } from "../lib/utils";</span>
&nbsp;
/**
 * TaskExecuteTimeoutFunc - タスク実行タイムアウト時の補償処理
 *
 * 処理ステップ:
 * 1-2. DLQメッセージ受信・解析、taskId検証
 * 3. Azure Automationジョブ存在確認（補償要否判定）
 * 4. Azure Files作業ディレクトリ削除
 * 5. タスク情報取得
 * 6. コンテナ実行状態更新（IDLE）
 * 7. タスクステータス確認（PENDING_CANCELLATION/CANCELLED判定）
 * 8. タスクテーブル更新（EMET0005、条件付き）
 * 9. 処理完了ログ記録
 *
 * エラー処理:
 * taskId不正・API呼び出し失敗・DB取得失敗時は処理終了、
 * その他の失敗時はログ記録して処理継続。
 */
<span class="cstat-no" title="statement not covered" >export a</span>sync function <span class="fstat-no" title="function not covered" >TaskExecuteTimeoutFunc(</span>message: unknown, context: InvocationContext): Promise&lt;void&gt; {
  let taskId: string | undefined;
&nbsp;
<span class="cstat-no" title="statement not covered" >  try {</span>
    // 1. Azure Service Bus の TaskInputQueue/$DeadLetterQueue からメッセージを受信し、解析する
<span class="cstat-no" title="statement not covered" >    context.log("[TaskExecuteTimeoutFunc] TaskInputQueue/$DeadLetterQueue からメッセージ受信");</span>
&nbsp;
    // メッセージ基本校验
<span class="cstat-no" title="statement not covered" >    <span class="missing-if-branch" title="if path not taken" >I</span>if (!message || typeof message !== 'object') {</span>
<span class="cstat-no" title="statement not covered" >      context.error("[TaskExecuteTimeoutFunc] メッセージが不正です。処理を終了します。");</span>
<span class="cstat-no" title="statement not covered" >      return;</span>
    }
&nbsp;
    // 2. メッセージ内の基本パラメータ（taskId）の存在と値が空であるかを確認する
    const messageBody = <span class="cstat-no" title="statement not covered" >message as { taskId?: string };</span>
    const extractedTaskId = <span class="cstat-no" title="statement not covered" >messageBody.taskId;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    <span class="missing-if-branch" title="if path not taken" >I</span>if (!extractedTaskId || typeof extractedTaskId !== "string") {</span>
<span class="cstat-no" title="statement not covered" >      context.error("[TaskExecuteTimeoutFunc] taskIdが不足/不正です。処理を終了します。");</span>
<span class="cstat-no" title="statement not covered" >      return;</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    taskId = extractedTaskId;</span>
<span class="cstat-no" title="statement not covered" >    context.log(`[TaskExecuteTimeoutFunc] 受信taskId: ${taskId}`);</span>
&nbsp;
    // 3. Azure Automationのジョブ取得APIを呼び出して、タスク実行関数によってジョブが作成されたか確認する
    // taskId=jobName を使用して、Azure Automationのジョブ取得APIを呼び出してジョブの存在を問い合わせる
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      context.log(`[TaskExecuteTimeoutFunc] Azure Automationジョブ取得API呼び出し: jobName=${taskId}`);</span>
      const jobResult = <span class="cstat-no" title="statement not covered" >await getAutomationJobStatus(taskId);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (jobResult.exists) {</span>
        // ジョブが存在している場合はHTTPステータスコードが200（OK）のレスポンスが返却される
        // この場合は補償不要のログを出力して、処理を終了する
<span class="cstat-no" title="statement not covered" >        context.log(`[TaskExecuteTimeoutFunc] Azure Automationジョブが存在するため補償処理は不要です: ${taskId}`);</span>
<span class="cstat-no" title="statement not covered" >        return;</span>
      } else {
        // ジョブが存在しない場合はHTTPステータスコードが404のレスポンスが返却される。この場合はステップ4.に進める
<span class="cstat-no" title="statement not covered" >        context.log(`[TaskExecuteTimeoutFunc] Azure Automationジョブが存在しないため補償処理を開始します: ${taskId}`);</span>
      }
    } catch (getError: any) {
      // API の呼び出しに失敗した場合は、HTTPステータスコードが200（OK）または404以外のレスポンスが返却される
      // この場合はエラー詳細をログに記録した後、処理を終了する
<span class="cstat-no" title="statement not covered" >      context.error(`[TaskExecuteTimeoutFunc] Azure Automationジョブ取得API呼び出し失敗:`, getError);</span>
<span class="cstat-no" title="statement not covered" >      return;</span>
    }
&nbsp;
    // 4. Azure Files上のタスク作業ディレクトリ（TaskWorkspaces/&lt;taskId&gt;/）の削除を試みる
<span class="cstat-no" title="statement not covered" >    try {</span>
      const shareServiceClient = <span class="cstat-no" title="statement not covered" >createShareServiceClient();</span>
      const shareClient = <span class="cstat-no" title="statement not covered" >shareServiceClient.getShareClient(AppConstants.AZURE_FILES.TASK_WORKSPACES_SHARE);</span>
      const taskDirClient = <span class="cstat-no" title="statement not covered" >shareClient.getDirectoryClient(taskId);</span>
<span class="cstat-no" title="statement not covered" >      await deleteTaskWorkspaceDirectory(taskDirClient, context);</span>
<span class="cstat-no" title="statement not covered" >      context.log(`[TaskExecuteTimeoutFunc] Azure Files作業ディレクトリ削除成功: TaskWorkspaces/${taskId}/`);</span>
    } catch (err: any) {
      // 削除に失敗した場合はエラーをログに記録するが、後続処理は継続する
<span class="cstat-no" title="statement not covered" >      context.log(`[TaskExecuteTimeoutFunc] Azure Files作業ディレクトリ削除失敗: ${err.message}`);</span>
    }
&nbsp;
    // 5. taskId を使用して Task テーブルを検索し、タスクの情報を取得する
    const task = <span class="cstat-no" title="statement not covered" >await prisma.task.findUnique({ where: { id: taskId } });</span>
<span class="cstat-no" title="statement not covered" >    <span class="missing-if-branch" title="if path not taken" >I</span>if (!task) {</span>
      // DB読み取り失敗、またはタスクレコードが存在しない場合：エラー詳細をログに出力して、処理を終了する
<span class="cstat-no" title="statement not covered" >      context.error(`[TaskExecuteTimeoutFunc] taskId=${taskId} のタスクがデータベースに存在しません。処理を終了します。`);</span>
<span class="cstat-no" title="statement not covered" >      return;</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    context.log(`[TaskExecuteTimeoutFunc] タスク情報取得成功: ${taskId}, status: ${task.status}`);</span>
&nbsp;
    // 6. コンテナ実行状態ContainerConcurrencyStatus テーブルの対象コンテナレコードを更新する
    // 条件：対象VM名と対象コンテナ名がステップ5.で取得した情報と一致し、ステータスがBUSYで、使用中のタスクID（currentTaskId）が入力のtaskIdと一致する
<span class="cstat-no" title="statement not covered" >    try {</span>
      const updateContainerResult = <span class="cstat-no" title="statement not covered" >await prisma.containerConcurrencyStatus.updateMany({</span>
        where: {
          targetVmName: task.targetVmName || undefined,
          targetContainerName: task.targetContainerName || undefined,
          status: "BUSY",
          currentTaskId: taskId,
        },
        data: {
          status: "IDLE",
          currentTaskId: null,
        },
      });
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (updateContainerResult.count === 0) {</span>
        // 更新失敗または更新件数が0件の場合はエラーをログに記録するが、後続処理は継続する
<span class="cstat-no" title="statement not covered" >        context.log(`[TaskExecuteTimeoutFunc] コンテナ実行状態テーブル更新件数0件: VM=${task.targetVmName}, Container=${task.targetContainerName}`);</span>
      } else {
<span class="cstat-no" title="statement not covered" >        context.log(`[TaskExecuteTimeoutFunc] コンテナ実行状態テーブル更新成功: VM=${task.targetVmName}, Container=${task.targetContainerName} -&gt; IDLE`);</span>
      }
    } catch (err: any) {
      // 更新失敗または更新件数が0件の場合はエラーをログに記録するが、後続処理は継続する
<span class="cstat-no" title="statement not covered" >      context.log(`[TaskExecuteTimeoutFunc] コンテナ実行状態テーブル更新失敗: ${err.message}`);</span>
    }
&nbsp;
    // 7. タスクのステータスがPENDING_CANCELLATIONまたはCANCELLEDであるかチェックする
<span class="cstat-no" title="statement not covered" >    if (task.status === AppConstants.TaskStatus.PendingCancellation || task.status === AppConstants.TaskStatus.Cancelled) {</span>
      // PENDING_CANCELLATIONまたはCANCELLEDの場合：ステップ8.をスキップして、ステップ9.に進める
<span class="cstat-no" title="statement not covered" >      context.log(`[TaskExecuteTimeoutFunc] タスクステータスが ${task.status} のため、EMET0005エラー更新をスキップします: ${taskId}`);</span>
    } else {
      // 8. タスクTask テーブルの該当タスクレコードを更新する
      // 条件：IDが入力のtaskIdと一致し、最終更新日時がステップ5.で取得した最終更新日時と一致する
<span class="cstat-no" title="statement not covered" >      try {</span>
        const updateTaskResult = <span class="cstat-no" title="statement not covered" >await prisma.task.updateMany({</span>
          where: {
            id: taskId,
            updatedAt: task.updatedAt // 楽観ロック条件
          },
          data: {
            status: AppConstants.TaskStatus.CompletedError,
            errorCode: AppConstants.ERROR_CODES.EMET0005,
            resultMessage: AppConstants.TASK_ERROR_MESSAGE.EMET0005,
          },
        });
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (updateTaskResult.count === 0) {</span>
          // 更新失敗または更新件数が0件の場合はエラーをログに記録するが、後続処理は継続する
<span class="cstat-no" title="statement not covered" >          context.log(`[TaskExecuteTimeoutFunc] タスクテーブル更新件数0件: ${taskId}`);</span>
        } else {
<span class="cstat-no" title="statement not covered" >          context.log(`[TaskExecuteTimeoutFunc] タスクテーブル更新成功: ${taskId} -&gt; COMPLETED_ERROR/EMET0005`);</span>
        }
      } catch (err: any) {
        // 更新失敗または更新件数が0件の場合はエラーをログに記録するが、後続処理は継続する
<span class="cstat-no" title="statement not covered" >        context.log(`[TaskExecuteTimeoutFunc] タスクテーブル更新失敗: ${err.message}`);</span>
      }
    }
&nbsp;
    // 9. 処理完了のログを出力して、処理を終了する（メッセージACK）
<span class="cstat-no" title="statement not covered" >    context.log(`[TaskExecuteTimeoutFunc] タスクID ${taskId} のタイムアウト補償処理が正常に完了しました。`);</span>
  } catch (err: any) {
    // 予期せぬ内部エラー：エラーログ記録。処理終了。（メッセージACK）
<span class="cstat-no" title="statement not covered" >    context.error(`[TaskExecuteTimeoutFunc] 予期せぬ内部エラーが発生しました:`, err);</span>
<span class="cstat-no" title="statement not covered" >    context.log(`[TaskExecuteTimeoutFunc] タスクID ${taskId || 'unknown'} の処理を終了します。`);</span>
  }
}
&nbsp;
// FunctionとService Busキューのバインド設定
<span class="cstat-no" title="statement not covered" >app.serviceBusQueue("TaskExecuteTimeoutFunc", {</span>
  connection: "AZURE_SERVICEBUS_NAMESPACE_HOSTNAME",
  queueName: "%SERVICE_BUS_TASK_INPUT_QUEUE_NAME%/$DeadLetterQueue",
  handler: TaskExecuteTimeoutFunc,
}); </pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-30T07:06:17.797Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    