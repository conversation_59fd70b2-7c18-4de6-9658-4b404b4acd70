# 数据模型: 计划可用支持信息 (PlanSupport)

*   **表名 (逻辑名)**: `PlanSupport`
*   **对应UI界面**: N/A (主要由系统内部用于定义契约计划与支持信息之间的可用关系)
*   **主要用途**: 作为 `Plan` (契约计划信息) 表和 `SupportFile` (支持信息) 表之间的多对多关联（中间）表。它记录了特定契约计划下，哪些支持信息（由其唯一序列号`serialNo`标识）是可见或适用的。

## 1. 字段定义

| 字段名 (Prisma/英文) | 数据类型 (SQL参考) | PK | FK | UQ | Nullable | Default  | 描述 (中文)                                                                                   |
| :------------------- | :----------------- | :--- | :--- | :--- | :------- | :------- | :-------------------------------------------------------------------------------------------- |
| `id`                 | VARCHAR(36)        | ●    |      |      |          | `cuid()` | 主键。CUID格式，由系统自动生成。                                                                  |
| `serialNo`           | INT                |      | ●    |      |          |          | **外键**。关联到 `SupportFile` 表的 `serialNo` (唯一自增序列号)。表示关联的具体支持信息。           |
| `planId`             | VARCHAR(XX)        |      | ●    |      | Yes      |          | **可选外键**。关联到 `Plan` 表的 `planId` (业务唯一键)。表示此支持信息适用性条目属于哪个契约计划。       |

*PK: Primary Key, FK: Foreign Key, UQ: Unique Key*
*XX表示具体长度，取决于实际的数据库Schema定义。*

## 2. 关系

*   **对 `Plan` (`plan`)**: 可选的多对一关系。通过 `planId` 字段关联到 `Plan` 表的 `planId` 唯一键。一个支持信息适用性条目可以属于一个契约计划，或者在 `planId` 为 NULL 的情况下不直接属于任何特定计划（需确认此业务逻辑是否适用）。
*   **对 `SupportFile` (`supportFile`)**: 多对一关系。通过 `serialNo` 字段关联到 `SupportFile` 表的 `serialNo` 唯一键。

## 3. 索引

*   `PRIMARY KEY (id)`
*   `INDEX idx_planId_plan_support (planId)` (Prisma Schema已定义，用于优化按计划ID的查询)
*   `INDEX idx_serialNo_plan_support (serialNo)` (Prisma Schema已定义，用于优化按支持信息序列号的查询，并支持外键约束)
*   (可选) `UNIQUE KEY UQ_PlanSupport_Plan_SerialNo (planId, serialNo)` (如果业务要求一个计划下每个支持信息的关联是唯一的)