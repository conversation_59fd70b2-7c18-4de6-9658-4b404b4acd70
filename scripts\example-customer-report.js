#!/usr/bin/env node

/**
 * 客户测试报告生成示例
 * 演示如何使用generate-customer-test-report.js生成报告
 */

const { generateTestReport, CONFIG } = require('./generate-customer-test-report');
const fs = require('fs');
const path = require('path');

console.log('🎯 客户测试报告生成示例');
console.log('=====================================');

// 显示配置信息
console.log('📋 当前配置:');
console.log(`  输入JSON: ${CONFIG.INPUT_JSON}`);
console.log(`  模板文件: ${CONFIG.TEMPLATE_FILE}`);
console.log(`  输出文件: ${CONFIG.OUTPUT_FILE}`);
console.log('');

// 显示项目映射
console.log('🗂️ 项目章节映射:');
Object.entries(CONFIG.PROJECT_MAPPING).forEach(([key, info]) => {
  console.log(`  ${key} → ${info.chapter}章.${info.title}`);
});
console.log('');

// 检查文件存在性
console.log('🔍 文件检查:');
const files = [
  { path: CONFIG.INPUT_JSON, name: '测试用例JSON' },
  { path: CONFIG.TEMPLATE_FILE, name: 'Excel模板' }
];

files.forEach(file => {
  const exists = fs.existsSync(file.path);
  console.log(`  ${file.name}: ${exists ? '✅ 存在' : '❌ 不存在'}`);
  if (!exists) {
    console.log(`    路径: ${file.path}`);
  }
});

console.log('');
console.log('🚀 运行命令:');
console.log('  npm run generate-customer-report');
console.log('');
console.log('📖 生成的报告将包含:');
console.log('  1. 更新的目次工作表');
console.log('  2. 统计信息工作表');
console.log('  3. 每个小項目的详细测试用例工作表');
console.log('');

// 如果测试数据存在，显示预览信息
if (fs.existsSync(CONFIG.INPUT_JSON)) {
  try {
    const testData = JSON.parse(fs.readFileSync(CONFIG.INPUT_JSON, 'utf8'));
    
    console.log('📊 数据预览:');
    console.log(`  总测试用例: ${testData.statistics.totalTestCases}`);
    console.log(`  总项目数: ${testData.statistics.totalProjects}`);
    console.log(`  总大項目数: ${testData.statistics.totalMajorCategories}`);
    console.log(`  总小項目数: ${testData.statistics.totalMinorCategories}`);
    console.log('');
    
    console.log('📋 将生成的工作表:');
    Object.entries(CONFIG.PROJECT_MAPPING).forEach(([projectKey, projectInfo]) => {
      const projectData = testData.testCases[projectKey];
      if (!projectData) return;
      
      let majorIndex = 1;
      Object.entries(projectData.categories).forEach(([categoryKey, categoryData]) => {
        const sheetName = `${projectInfo.chapter}.${majorIndex}`;
        const testCount = Object.values(categoryData.subCategories)
          .reduce((sum, subCat) => sum + subCat.testCases.length, 0);
        
        console.log(`  工作表 "${sheetName}": ${categoryData.displayName} (${testCount}个测试用例)`);
        majorIndex++;
      });
    });
    
  } catch (error) {
    console.log('⚠️ 无法读取测试数据预览:', error.message);
  }
}

console.log('=====================================');