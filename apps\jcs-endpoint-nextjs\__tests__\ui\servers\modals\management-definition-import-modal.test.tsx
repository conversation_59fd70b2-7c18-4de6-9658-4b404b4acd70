/**
 * @jest-environment jsdom
 */

// Web API polyfills
Object.defineProperty(global, 'Response', {
  value: class Response {
    constructor(_body?: any, _init?: any) {}
    static json(data: any) { return new Response(JSON.stringify(data)); }
  }
});
Object.defineProperty(global, 'Request', {
  value: class Request {
    constructor(_input: any, _init?: any) {}
  }
});

// Azure関連の依存を完全にモックし、ESM構文エラーを防止する
jest.mock("@azure/storage-blob", () => ({}));
jest.mock("@azure/service-bus", () => ({}));
jest.mock("@azure/msal-node", () => ({}));
jest.mock("@azure/identity", () => ({}));
jest.mock("next/server", () => ({}));

// Next.js関連のモック
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    replace: jest.fn(),
    push: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    refresh: jest.fn(),
  }),
  usePathname: () => "/dashboard/servers",
  useSearchParams: () => ({
    get: jest.fn(),
    set: jest.fn(),
    entries: () => [],
    keys: () => [],
    values: () => [],
    toString: () => "",
  }),
}));

// Flowbite Tooltipのモック
jest.mock("flowbite", () => ({
  Tooltip: jest.fn().mockImplementation(() => ({
    show: jest.fn(),
    hide: jest.fn(),
    destroy: jest.fn(),
  })),
}));

// 定数のモック
jest.mock("@/app/lib/definitions", () => ({
  PORTAL_ERROR_MESSAGES: {
    EMEC0016: "{0}を指定してください。",
    EMEC0017: "無効なファイル形式です。CSVファイルを指定してください。",
  },
  FILE_VALIDATION: {
    CSV: {
      ALLOWED_EXTENSIONS: ['.csv'],
      ALLOWED_MIME_TYPES: ['text/csv', 'application/csv'],
      MAX_SIZE_MB: 10,
    },
  },
  FORM_FIELD_NAMES: {
    MGMT_ITEM_CSV_FILE: "管理項目定義CSVファイル",
  },
}));

// ユーティリティ関数のモック
jest.mock("@/app/lib/utils", () => ({
  formatMessage: jest.fn((template: string, params: string[]) => {
    let result = template;
    params.forEach((param, index) => {
      result = result.replace(`{${index}}`, param);
    });
    return result;
  }),
}));

import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import ManagementDefinitionImportModal from "@/app/ui/servers/modals/management-definition-import-modal";

/**
 * @fileoverview 管理項目定義インポートモーダルコンポーネントのテスト
 * @description 管理項目定義インポートパラメータ入力モーダルコンポーネントのテストである。ファイル選択、バリデーション、ツールチップ表示、送信機能を検証する。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

describe("ManagementDefinitionImportModal", () => {
  const mockProps = {
    isOpen: true,
    onSubmit: jest.fn(),
    onClose: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // DOM要素のモック
    document.getElementById = jest.fn((_id: string) => {
      const mockElement = {
        value: "",
        classList: {
          add: jest.fn(),
          remove: jest.fn(),
        },
      };
      return mockElement as any;
    });
  });

  /**
   * 試験観点：モーダル非表示時の動作確認
   * 試験対象：ManagementDefinitionImportModal コンポーネントの表示制御
   * 試験手順：
   * 1. isOpen=falseでコンポーネントをレンダリング
   * 確認項目：
   * - モーダルが表示されないこと
   */
  it("正常系: モーダル非表示時はnullを返す", () => {
    const { container } = render(
      <ManagementDefinitionImportModal {...mockProps} isOpen={false} />
    );
    expect(container.firstChild).toBeNull();
  });

  /**
   * 試験観点：モーダル基本UI要素の表示確認
   * 試験対象：ManagementDefinitionImportModal コンポーネントの基本UI表示機能
   * 試験手順：
   * 1. 正常なpropsでコンポーネントをレンダリング
   * 確認項目：
   * - タイトルが表示されること
   * - 閉じるボタンが表示されること
   * - ファイルアップロード領域が表示されること
   * - 必須入力ヒントが表示されること
   * - OKボタンとキャンセルボタンが表示されること
   */
  it("正常系: モーダル基本UI要素の表示", async () => {
    render(<ManagementDefinitionImportModal {...mockProps} />);

    expect(screen.getByText("管理項目定義のインポート")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /Close modal/i })).toBeInTheDocument();
    expect(screen.getByText("ファイル：")).toBeInTheDocument();
    expect(screen.getByText("ファイルを選択してください")).toBeInTheDocument();
    expect(screen.getAllByText("*").length).toBeGreaterThanOrEqual(1);
    expect(screen.getByRole("button", { name: "OK" })).toBeEnabled();
    expect(screen.getByRole("button", { name: "キャンセル" })).toBeEnabled();
    expect(screen.getByText("* 必須入力")).toBeInTheDocument();
  });

  /**
   * 試験観点：閉じるボタンクリック時の動作確認
   * 試験対象：ManagementDefinitionImportModal コンポーネントの閉じる機能
   * 試験手順：
   * 1. 閉じるボタンをクリック
   * 確認項目：
   * - onCloseコールバックが呼び出されること
   */
  it("正常系: ×ボタンクリック時にモーダルが閉じる", async () => {
    render(<ManagementDefinitionImportModal {...mockProps} />);

    await userEvent.click(screen.getByRole("button", { name: /Close modal/i }));

    expect(mockProps.onClose).toHaveBeenCalledTimes(1);
  });

  /**
   * 試験観点：キャンセルボタンクリック時の動作確認
   * 試験対象：ManagementDefinitionImportModal コンポーネントのキャンセル機能
   * 試験手順：
   * 1. キャンセルボタンをクリック
   * 確認項目：
   * - onCloseコールバックが呼び出されること
   */
  it("正常系: キャンセルボタンクリック時にモーダルが閉じる", async () => {
    render(<ManagementDefinitionImportModal {...mockProps} />);

    await userEvent.click(screen.getByRole("button", { name: "キャンセル" }));

    expect(mockProps.onClose).toHaveBeenCalledTimes(1);
  });

  /**
   * 試験観点：必須入力バリデーション（ファイル未選択）
   * 試験対象：ManagementDefinitionImportModal コンポーネントの入力検証機能
   * 試験手順：
   * 1. ファイルを選択せずにOKボタンをクリック
   * 確認項目：
   * - バリデーションエラーが発生すること
   * - onSubmitコールバックが呼び出されないこと
   */
  it("異常系: ファイル未選択時のバリデーションエラー", async () => {
    render(<ManagementDefinitionImportModal {...mockProps} />);

    await userEvent.click(screen.getByRole("button", { name: "OK" }));

    expect(mockProps.onSubmit).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：ファイル形式バリデーション
   * 試験対象：ManagementDefinitionImportModal コンポーネントのファイル形式検証機能
   * 試験手順：
   * 1. CSV以外のファイルを選択
   * 2. OKボタンをクリック
   * 確認項目：
   * - ファイル形式バリデーションエラーが発生すること
   * - onSubmitコールバックが呼び出されないこと
   */
  it("異常系: 無効なファイル形式のバリデーションエラー", async () => {
    render(<ManagementDefinitionImportModal {...mockProps} />);

    const invalidFile = new File(['test content'], 'test.txt', { type: 'text/plain' });
    const fileInput = document.getElementById("importDefFileInput") as HTMLInputElement;
    if (fileInput) {
      Object.defineProperty(fileInput, 'files', {
        value: [invalidFile],
        writable: false,
      });
    }

    await userEvent.click(screen.getByRole("button", { name: "OK" }));

    expect(mockProps.onSubmit).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：CSVファイル指示文の表示確認
   * 試験対象：ManagementDefinitionImportModal コンポーネントの指示文表示機能
   * 試験手順：
   * 1. モーダルをレンダリング
   * 確認項目：
   * - CSVファイル指示文が正しく表示されること
   */
  it("正常系: CSVファイル指示文の表示", () => {
    render(<ManagementDefinitionImportModal {...mockProps} />);

    expect(screen.getByText("管理項目定義のCSVファイルを指定してください。")).toBeInTheDocument();
  });

  /**
   * 試験観点：ファイル入力のaccept属性確認
   * 試験対象：ManagementDefinitionImportModal コンポーネントのファイル入力制御
   * 試験手順：
   * 1. モーダルをレンダリング
   * 確認項目：
   * - ファイル入力がCSVファイルを受け入れること
   */
  it("正常系: ファイル入力のCSV受け入れ確認", () => {
    const { container } = render(<ManagementDefinitionImportModal {...mockProps} />);

    const fileInput = container.querySelector("#importDefFileInput") as HTMLInputElement;
    expect(fileInput).toHaveAttribute("accept", ".csv");
  });

  /**
   * 試験観点：必須テキストの表示確認
   * 試験対象：ManagementDefinitionImportModal コンポーネントの必須テキスト表示
   * 試験手順：
   * 1. コンポーネントをレンダリング
   * 2. 必須テキストがすべて表示されていることを確認
   * 確認項目：
   * - 必須テキストがすべて表示されていること
   */
  it("必須テキストがすべて表示されること", () => {
    render(<ManagementDefinitionImportModal {...mockProps} />);

    // 必須テキスト
    const requiredTexts = [
      "管理項目定義のインポート",
      "管理項目定義のCSVファイルを指定してください。",
      "ファイル：",
      "*",
      "ファイルを選択してください",
      "* 必須入力",
      "OK",
      "キャンセル",
    ];

    const bodyText = document.body.textContent || "";

    // 必須テキストがすべて存在することを確認
    requiredTexts.forEach(requiredText => {
      expect(bodyText).toContain(requiredText);
    });
  });

  /**
   * 試験観点：許可されたテキストのみ表示確認
   * 試験対象：ManagementDefinitionImportModal コンポーネントのテキスト表示制御
   * 試験手順：
   * 1. コンポーネントをレンダリング
   * 2. 許可されたテキストのみが表示されていることを確認
   * 確認項目：
   * - 許可されていないテキストが表示されていないこと
   */
  it("許可されたテキストのみ表示されること", () => {
    render(<ManagementDefinitionImportModal {...mockProps} />);

    // 許可されたテキストの完全リスト
    const allowedTexts = [
      "管理項目定義のインポート",
      "Close modal",
      "管理項目定義のCSVファイルを指定してください。",
      "ファイル：",
      "*",
      "ファイルを選択してください",
      "* 必須入力",
      "必須入力",
      "OK",
      "キャンセル",
    ];

    // 実際のDOM内のテキストを取得
    const bodyText = document.body.textContent || "";

    // 許可されたテキストをすべて除去
    let remainingText = bodyText;
    allowedTexts.forEach(allowedText => {
      remainingText = remainingText.replace(new RegExp(allowedText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), '');
    });

    // 残ったテキストから空白文字を除去
    const unauthorizedText = remainingText.replace(/[\s\n\r\t]+/g, '').trim();

    // 許可されていないテキストがないことを確認
    expect(unauthorizedText).toBe('');
  });

  /**
   * 試験観点：初期値の動的更新確認
   * 試験対象：initialValuesプロパティの変更時の状態更新
   * 試験手順：
   * 1. 初期値なしでコンポーネントをレンダリング
   * 2. 初期値ありでpropsを更新
   * 3. 初期値なしでpropsを更新
   * 確認項目：
   * - 初期値が設定された時に状態が更新されること
   * - 初期値がクリアされた時に状態がクリアされること
   */
  it("初期値の動的更新が正しく動作すること", () => {
    // 初期値なしでレンダリング
    const { rerender } = render(<ManagementDefinitionImportModal {...mockProps} />);

    // 初期状態では空であることを確認
    const fileDisplay = screen.getByLabelText("ファイルを選択してください");
    expect(fileDisplay).toHaveTextContent("ファイルを選択してください");

    // 初期値ありでpropsを更新
    const testFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
    const initialValues = {
      file: testFile,
      originalFileName: 'test.csv'
    };
    rerender(<ManagementDefinitionImportModal {...mockProps} initialValues={initialValues} />);

    // 初期値が反映されることを確認
    expect(fileDisplay).toHaveTextContent("test.csv");

    // 初期値をクリア
    rerender(<ManagementDefinitionImportModal {...mockProps} initialValues={undefined} />);

    // 状態がクリアされることを確認
    expect(fileDisplay).toHaveTextContent("ファイルを選択してください");
  });
});
