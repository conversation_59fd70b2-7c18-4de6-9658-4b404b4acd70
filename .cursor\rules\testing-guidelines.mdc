---
description: 
globs: 
alwaysApply: true
---
# Testing Guidelines (for apps/jcs-endpoint-nextjs)

This document provides guidelines for writing and organizing tests within the `apps/jcs-endpoint-nextjs` application, leveraging the project's standard testing frameworks: Jest and Cypress. Comprehensive testing is crucial for ensuring code quality, reliability, and preventing regressions.

**Standard Testing Frameworks & Tools:**

*   **Unit Testing:**
    *   **Jest** (configured in `jest.config.js`, setup in `jest.setup.js`): The primary framework for unit testing:
        *   JavaScript/TypeScript modules (e.g., functions and classes in `app/lib/`, custom hooks in `app/hooks/`).
        *   Business logic within API Route handlers in `app/api/` (with HTTP request/response objects and external dependencies like Prisma or session management mocked).
    *   **Cypress (Component Testing)** (configured in `cypress.config.ts`, tests in `cypress/component/`): Used for unit/fine-grained integration testing of individual UI components (from `app/ui/`, or page-level components from `app/dashboard/`, `app/login/` etc.) in isolation. This involves mounting the component in a browser-like environment to test its rendering based on props, internal state changes, user interactions within the component, and emitted events or callbacks.

*   **Integration Testing:**
    *   **Jest:** Can be utilized for integration testing backend modules that have direct, close collaborations (e.g., a function in `app/lib/data.ts` that uses several utilities from `app/lib/utils.ts`). In such cases, the direct dependencies might not be mocked to test their actual interaction, but external systems (like Prisma making a database call, or a third-party API call) **MUST** still be mocked. These Jest integration tests should remain focused on a small set of closely interacting server-side modules.
    *   **Cypress (E2E Testing)** (configured in `cypress.config.ts`, tests in `cypress/e2e/`): The primary framework for broader integration testing, specifically focusing on **End-to-End (E2E) user flows** through the frontend application. This involves testing how multiple pages, UI components, and client-side routing work together to achieve user goals. All backend API calls (`app/api/`) made during these E2E tests **MUST** be stubbed/mocked using `cy.intercept()` to isolate frontend behavior and ensure test determinism and speed.

*   **Supporting Testing Libraries:**
    *   `@testing-library/react` (for rendering React components and hooks in Jest tests, and for interacting with components in Cypress component tests).
    *   `@testing-library/jest-dom` (for custom Jest matchers that enhance DOM assertions).
    *   `jest-mock-extended` (for creating type-safe deep mocks in Jest, especially for the Prisma client).
*   **Code Coverage:** `nyc` (using Jest's built-in coverage capabilities and `@cypress/code-coverage` plugin) is used to generate and combine coverage reports from both Jest and Cypress tests.

**Primary References:**
*   Existing tests in `__tests__/` (for Jest) and `cypress/` (for Cypress).
*   `package.json` for predefined test scripts (e.g., `test:jest`, `test:ut:front` for Cypress component tests, `test:e2e` for Cypress E2E tests).
*   Official Jest Documentation.
*   Official Cypress Documentation.
*   `@testing-library` Documentation.
*   The Monorepo's `docs/guides/` directory may contain further testing strategies or conventions.

## General Testing Principles

1.  **Test What Matters:** Focus tests on core functionality, critical user paths, complex business logic, and areas most susceptible to regressions. Aim for a pragmatic balance between achieving high, meaningful coverage and the effort required for test creation and maintenance.
2.  **Readable and Maintainable Tests:** Tests **MUST** be written to be easily understood, debugged, and updated. Use clear, descriptive names for test suites (`describe(...)`) and individual test cases (`it(...)` or `test(...)`). Consistently apply the Arrange-Act-Assert (AAA) pattern.
3.  **Independent and Isolated Tests:** Each test case (`it`/`test`) **MUST** be independent and capable of running in any order without affecting or being affected by other tests. Ensure proper setup (`beforeEach`, `beforeAll`) and teardown (`afterEach`, `afterAll`) for tests or suites if shared context is absolutely necessary, though minimizing shared state is preferred.
4.  **Arrange-Act-Assert (AAA) Pattern:**
    *   **Arrange:** Set up all preconditions for the test. This includes preparing test data (using fixtures for Cypress where appropriate), mocking dependencies, and instantiating objects or mounting components.
    *   **Act:** Execute the specific function, method, or user interaction that is the subject of the test.
    *   **Assert:** Verify that the actual outcome (return values, state changes, UI updates, mock calls) matches the expected outcome using Jest's `expect` API or Cypress's built-in assertion commands (`.should()`, `.and()`).
5.  **Effective Mocking Strategies:**
    *   **Jest:** Utilize `jest.mock()`, `jest.fn()`, `jest.spyOn()`, and `jest-mock-extended`. Mocks should be type-safe and accurately represent the contract of the dependency being replaced.
    *   **Cypress:** Use `cy.intercept()` to define stubs for all network (API) requests. For component tests, pass mock functions and data via props.
6.  **Use Fixtures for Cypress Test Data:** For Cypress E2E and component tests that require consistent, predefined data structures (e.g., for API responses or component props), use JSON fixtures located in `cypress/fixtures/`.

## Jest: Unit & Backend Integration Tests (`__tests__/`)

*   **Location:** Test files **MUST** be located in the `__tests__/` directory, generally mirroring the structure of the `app/` directory for the code they are testing.
    *   Example: Tests for `app/lib/utils.ts` are in `__tests__/lib/utils.test.ts`.
*   **Filename Convention:** `*.test.ts` or `*.test.tsx`.
*   **Primary Scope (Jest):**
    *   **Unit Tests:**
        *   Individual functions, classes, and modules within `app/lib/` (e.g., `utils.ts`, `logger.ts`).
        *   Methods within `app/lib/data.ts` or `app/lib/actions.ts` with their external dependencies (like Prisma, other services) thoroughly mocked.
        *   Custom React Hooks from `app/hooks/` using `@testing-library/react`'s `renderHook`.
        *   Business logic within API Route handlers in `app/api/`, where `NextRequest`, `NextResponse`, and all external calls (Prisma, session management, etc.) are mocked. `node-mocks-http` can assist in creating mock request/response objects.
    *   **Backend Integration Tests (Limited Scope):**
        *   Testing the direct interaction between a small set of closely coupled server-side modules where mocking every internal call would be counterproductive. For example, testing if a service method correctly calls several of its own private helper methods or utility functions from the same library.
        *   External system dependencies (database, file system, third-party APIs) **MUST** still be mocked.
*   **Mocking Prisma in Jest:** The Prisma client **MUST** be mocked in all Jest tests to prevent actual database operations. `jest-mock-extended` is the recommended tool.
    ```typescript
    // Example: __tests__/lib/some-data-service.test.ts
    // import { mockDeep, DeepMockProxy } from 'jest-mock-extended';
    // import { PrismaClient } from '@prisma/client';
    // import prismaClientInstance from '@/app/lib/prisma'; // Actual exported instance
    //
    // jest.mock('@/app/lib/prisma', () => ({
    //   __esModule: true,
    //   default: mockDeep<PrismaClient>(), // Create a deep mock of PrismaClient
    // }));
    //
    // const mockPrisma = prismaClientInstance as unknown as DeepMockProxy<PrismaClient>;
    //
    // describe('SomeDataService', () => {
    //   beforeEach(() => {
    //     // Example: Reset specific mock before each test
    //     // mockPrisma.user.findUnique.mockReset();
    //   });
    //
    //   it('should retrieve a user by ID', async () => {
    //     const fakeUser = { id: 'user1', email: '<EMAIL>', name: 'Test User' };
    //     mockPrisma.user.findUnique.mockResolvedValue(fakeUser);
    //
    //     // const result = await SomeDataService.getUserById('user1');
    //     // expect(result).toEqual(fakeUser);
    //     // expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({ where: { id: 'user1' } });
    //   });
    // });
    ```

## Cypress: Component & End-to-End Integration Tests (`cypress/`)

### Cypress Component Tests (`cypress/component/`)
*   **Location:** `cypress/component/`.
*   **Filename Convention:** `*.cy.tsx`.
*   **Scope (UI Unit / Fine-grained UI Integration):** Test individual UI components from `app/ui/` or page entry components (e.g., from `app/dashboard/servers/page.tsx`) in isolation. Focus on:
    *   Correct rendering based on various props.
    *   Internal state management and changes triggered by user interactions within the component.
    *   Invocation of callback props with correct arguments.
    *   Accessibility attributes and basic visual consistency (though primarily functional).
*   **Methodology:** Mount components using `cy.mount()`. Pass necessary props, including mock functions for callbacks. Interact with the component's DOM elements using Cypress commands. Assert on rendering, behavior, and state changes.
*   **Mocking for Component Tests:** Primarily mock dependencies via props. If a component under test makes its own direct API calls (less ideal for pure UI components but possible in page components), these **MUST** be stubbed using `cy.intercept()`.

### Cypress End-to-End (E2E) / Frontend Integration Tests (`cypress/e2e/`)
*   **Location:** `cypress/e2e/`. Organize test files into subdirectories based on application features or user flows (e.g., `authentication.cy.ts`, `dashboard/manuals-page.cy.ts`).
*   **Filename Convention:** `*.cy.ts`.
*   **Scope (Frontend Integration & User Flows):** Test complete user journeys through the application. This involves interactions across multiple pages, UI components, client-side navigation, and forms. This verifies the integration of various frontend parts.
*   **API Call Mocking (`cy.intercept()`):** All backend API calls (`app/api/`) made by the application during E2E tests **MUST** be intercepted and stubbed using `cy.intercept()`. Use JSON fixtures from `cypress/fixtures/` to define consistent and predictable response bodies for these stubs. This is crucial for making E2E tests reliable, fast, and independent of the actual backend state.

## Running Tests & Coverage

*   Utilize the predefined scripts in `package.json` (e.g., `npm run test:jest`, `npm run test:ut:front` which runs Cypress component tests, `npm run test:e2e`) to execute the respective test suites.
*   Code coverage reports from Jest and Cypress are combined using `nyc`. Review coverage reports to identify untested areas of the codebase.

---

**NOTE TO CURSOR:**
1.  When generating new business logic in `app/lib/` or API routes in `app/api/`, AI **SHOULD** also generate corresponding **Jest unit/integration tests** in the `__tests__/` directory. All external dependencies, especially Prisma client interactions, **MUST** be mocked.
2.  When creating new UI components in `app/ui/` or significant page components, AI **SHOULD** generate corresponding **Cypress component tests** in `cypress/component/` to verify rendering, props handling, and basic internal interactions.
3.  For new major user flows or critical path functionalities, AI **SHOULD** assist in creating or updating **Cypress E2E tests** in `cypress/e2e/`. All backend API calls made by the application during these E2E tests **MUST** be intercepted and mocked using `cy.intercept()` and appropriate fixtures.
4.  Ensure all generated tests are clear, maintainable, and consistently follow the Arrange-Act-Assert (AAA) pattern.

---