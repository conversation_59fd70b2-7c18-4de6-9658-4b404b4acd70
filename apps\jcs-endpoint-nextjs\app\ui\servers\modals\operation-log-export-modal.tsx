/**
 * @fileoverview 操作ログエクスポート用パラメータ入力モーダルコンポーネント
 * @description
 * 操作ログのエクスポート選択時の処理フロー：
 *   1. パラメータ入力モーダル表示：対象サーバ名、最大日数の表示
 *   2. パラメータ入力と検証：開始日・終了日の入力、クライアントサイド検証
 *   3. 最終確認モーダル表示：入力パラメータの確認
 *
 * バリデーション規則：
 *   - 開始日の必須入力チェック：未入力時「開始日を指定してください。」（EMEC0016）
 *   - 終了日の必須入力チェック：未入力時「終了日を指定してください。」（EMEC0016）
 *   - 終了日の正当性チェック：終了日が開始日より前の場合「終了日は開始日以降の日付を指定してください。」（EMEC0024）
 *   - 最大日数超過チェック：指定期間が最大日数を超過した場合「{0}日を超える期間が指定されました。{0}日以内の期間を指定して再実行してください。」（EMEC0020）
 *   - エラー時は該当入力ボックスを赤枠表示
 *
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import { PORTAL_ERROR_MESSAGES, FORM_FIELD_NAMES, DATE_FORMATS } from "@/app/lib/definitions";
import { formatMessage, formatDateToSlashWithSpaces } from "@/app/lib/utils";
import { FaRegCalendarCheck } from "react-icons/fa6";
import { Tooltip, TooltipInterface } from "flowbite";

interface OperationLogExportModalProps {
  isOpen: boolean;
  maxExportDaysSpan: number;
  initialValues?: { exportStartDate: string; exportEndDate: string };
  onSubmit: (params: {
    exportStartDate: string;
    exportEndDate: string;
  }) => void;
  onClose: () => void;
}



const OperationLogExportModal: React.FC<OperationLogExportModalProps> = ({
  isOpen,
  maxExportDaysSpan,
  initialValues,
  onSubmit,
  onClose,
}) => {
  // 日付入力値
  const [startDate, setStartDate] = useState<string>(
    initialValues?.exportStartDate || "",
  );
  const [endDate, setEndDate] = useState<string>(
    initialValues?.exportEndDate || "",
  );
  // 各フィールドのref
  const startDateInputRef = useRef<HTMLInputElement>(null);
  const endDateInputRef = useRef<HTMLInputElement>(null);
  // 各フィールドのエラーメッセージ
  const [startDateError, setStartDateError] = useState<string>("");
  const [endDateError, setEndDateError] = useState<string>("");
  // Tooltipインスタンス
  const [startDateTooltip, setStartDateTooltip] = useState<TooltipInterface>();
  const [endDateTooltip, setEndDateTooltip] = useState<TooltipInterface>();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // 初期値が変更された時に状態を更新
  useEffect(() => {
    if (initialValues) {
      setStartDate(initialValues.exportStartDate || "");
      setEndDate(initialValues.exportEndDate || "");
    } else {
      // initialValuesがnullまたはundefinedの場合は状態をクリア
      setStartDate("");
      setEndDate("");
    }
  }, [initialValues]);

  // 日付選択器を開く関数
  const openDatePicker = (inputRef: React.RefObject<HTMLInputElement>) => {
    const input = inputRef.current;
    if (input) {
      if (
        "showPicker" in input &&
        typeof (input as any).showPicker === "function"
      ) {
        try {
          (input as any).showPicker();
        } catch (error) {
          // showPickerが失敗した場合はclickメソッドにフォールバック
          input.click();
        }
      } else {
        // 旧ブラウザではclickメソッドにフォールバック
        input.click();
      }
    }
  };

  // Tooltip初期化
  const initTooltip = useCallback(() => {
    if (startDateTooltip) {
      startDateTooltip.hide();
    } else {
      setStartDateTooltip(
        new Tooltip(
          document.getElementById("startDateTooltip"),
          document.getElementById("opLogStartDateButton"),
          { placement: "right", triggerType: "none" },
          { id: "startDateTooltip", override: true },
        ),
      );
    }

    if (endDateTooltip) {
      endDateTooltip.hide();
    } else {
      setEndDateTooltip(
        new Tooltip(
          document.getElementById("endDateTooltip"),
          document.getElementById("opLogEndDateButton"),
          { placement: "right", triggerType: "none" },
          { id: "endDateTooltip", override: true },
        ),
      );
    }
  }, [startDateTooltip, endDateTooltip]);

  // モーダル閉じる時にtooltipをクリア
  useEffect(() => {
    if (!isOpen) {
      setStartDateTooltip(undefined);
      setEndDateTooltip(undefined);
    }
  }, [isOpen]);

  // フォーム初期化（エラー状態のみクリア、値はuseEffectで処理）
  const initForm = useCallback(() => {
    // エラー状態をクリア
    setStartDateError("");
    setEndDateError("");
  }, []);

  useEffect(() => {
    if (isOpen) {
      initTooltip();
      initForm();
    }
  }, [isOpen, initTooltip, initForm]);

  /**
   * 日付範囲バリデーション
   * 日付順序、最大日数超過を判定し、エラーメッセージをセット
   * @returns {boolean} バリデーション成功時true
   */
  const validate = (): boolean => {
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      if (end < start) {
        setEndDateError(PORTAL_ERROR_MESSAGES.EMEC0024);
        endDateTooltip?.show();
        return false;
      } else {
        const daySpan =
          Math.floor(
            (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24),
          ) + 1;
        if (daySpan > maxExportDaysSpan) {
          setEndDateError(
            formatMessage(PORTAL_ERROR_MESSAGES.EMEC0020, [
              String(maxExportDaysSpan),
              String(maxExportDaysSpan),
            ]),
          );
          endDateTooltip?.show();
          return false;
        }
      }
    }
    return true;
  };

  // フォーム送信処理
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // 必須入力チェック
    let hasEmptyFields = false;

    if (!startDate) {
      setStartDateError(
        formatMessage(PORTAL_ERROR_MESSAGES.EMEC0016, [FORM_FIELD_NAMES.START_DATE]),
      );
      startDateTooltip?.show();
      hasEmptyFields = true;
    } else {
      setStartDateError("");
      startDateTooltip?.hide();
    }

    if (!endDate) {
      setEndDateError(
        formatMessage(PORTAL_ERROR_MESSAGES.EMEC0016, [FORM_FIELD_NAMES.END_DATE]),
      );
      endDateTooltip?.show();
      hasEmptyFields = true;
    } else {
      setEndDateError("");
      endDateTooltip?.hide();
    }

    if (hasEmptyFields) {
      return;
    }

    // 詳細バリデーション
    if (!validate()) return;
    onSubmit({ exportStartDate: startDate, exportEndDate: endDate });
  };

  if (!isOpen) return null;

  // 二次確認モーダル、遮蔽レイヤー、ツールチップ、モーダル本体を含む操作ログエクスポートパラメータ入力画面をレンダリング
  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 z-40" />
      <>
        {isClient && (
          <>
            <div
              id="startDateTooltip"
              role="tooltip"
              className="max-w-md break-words whitespace-pre-line tooltip invisible fixed z-[9999] inline-block rounded-lg bg-red-700 px-3 py-2 text-sm font-medium text-white opacity-0 shadow-sm transition-opacity duration-300"
            >
              {startDateError}
              <div className="tooltip-arrow" data-popper-arrow></div>
            </div>
            <div
              id="endDateTooltip"
              role="tooltip"
              className="max-w-md break-words whitespace-pre-line tooltip invisible fixed z-[9999] inline-block rounded-lg bg-red-700 px-3 py-2 text-sm font-medium text-white opacity-0 shadow-sm transition-opacity duration-300"
            >
              {endDateError}
              <div className="tooltip-arrow" data-popper-arrow></div>
            </div>
          </>
        )}
        <div className="fixed left-1/2 top-1/2 z-50 -translate-x-1/2 -translate-y-1/2 w-full max-w-lg max-h-full">
          <form
            className="relative rounded shadow bg-gray-600"
            onSubmit={handleSubmit}
          >
            <div className="flex items-center justify-between p-4 border-b rounded-t bg-gradient-header">
              <h3 className="text-lg font-semibold text-white">
                操作ログのエクスポート
              </h3>
              <button
                type="button"
                className="text-gray-400 bg-transparent rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center hover:bg-gray-600 hover:text-white"
                onClick={onClose}
              >
                <svg
                  className="w-3 h-3"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 14 14"
                >
                  <path
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                  />
                </svg>
                <span className="sr-only">Close modal</span>
              </button>
            </div>
            <div className="px-8 py-4 bg-gray-100 text-base font-medium">
              <div className="text-sm font-normal">
                エクスポートする期間を{maxExportDaysSpan}
                日間以内で指定してください。
              </div>
            </div>
            <div className="px-8 py-4 space-y-4 bg-white text-base font-medium">
              <div className="flex items-center justify-between overflow-hidden">
                <div className="flex-1 min-w-0 flex items-center justify-between">
                  <label
                    htmlFor="opLogStartDateInput"
                    className="block text-sm font-medium text-gray-900"
                  >
                    開始日：
                  </label>
                  <label className="text-red-500 me-2">*</label>
                </div>
                <div
                  className="flex items-center min-w-0 relative"
                  style={{ flex: 2 }}
                >
                  <div
                    id="opLogStartDateDisplay"
                    className={[
                      "flex-1 rounded-l border bg-gray-50 p-2 text-sm text-gray-900 shadow-inner cursor-pointer overflow-hidden text-ellipsis whitespace-nowrap",
                      startDateError ? "border-red-500" : "border-gray-300",
                    ].join(" ")}
                    onClick={() => openDatePicker(startDateInputRef)}
                    tabIndex={0}
                    role="button"
                    aria-label="開始日を選択してください"
                  >
                    {startDate ? formatDateToSlashWithSpaces(startDate) : DATE_FORMATS.DISPLAY_WITH_SPACES}
                  </div>
                  <button
                    id="opLogStartDateButton"
                    type="button"
                    className="rounded-r bg-gray-200 border-t border-b border-r border-gray-300 px-3 py-2 flex items-center hover:bg-gray-300 focus:outline-none cursor-pointer"
                    onClick={() => openDatePicker(startDateInputRef)}
                    tabIndex={-1}
                    aria-label="開始日選択"
                  >
                    <FaRegCalendarCheck className="w-5 h-5 text-gray-600" />
                  </button>
                  <input
                    id="opLogStartDateInput"
                    type="date"
                    ref={startDateInputRef}
                    className="sr-only"
                    value={startDate}
                    onChange={(e) => {
                      setStartDate(e.target.value);
                      setStartDateError("");
                      startDateTooltip?.hide();
                    }}
                    tabIndex={-1}
                    aria-label="開始日入力"
                  />
                </div>
              </div>
              <div className="flex items-center justify-between overflow-hidden">
                <div className="flex-1 min-w-0 flex items-center justify-between">
                  <label
                    htmlFor="opLogEndDateInput"
                    className="block text-sm font-medium text-gray-900"
                  >
                    終了日：
                  </label>
                  <label className="text-red-500 me-2">*</label>
                </div>
                <div
                  className="flex items-center min-w-0 relative"
                  style={{ flex: 2 }}
                >
                  <div
                    id="opLogEndDateDisplay"
                    className={[
                      "flex-1 rounded-l border bg-gray-50 p-2 text-sm text-gray-900 shadow-inner cursor-pointer overflow-hidden text-ellipsis whitespace-nowrap",
                      endDateError ? "border-red-500" : "border-gray-300",
                    ].join(" ")}
                    onClick={() => openDatePicker(endDateInputRef)}
                    tabIndex={0}
                    role="button"
                    aria-label="終了日を選択してください"
                  >
                    {endDate ? formatDateToSlashWithSpaces(endDate) : DATE_FORMATS.DISPLAY_WITH_SPACES}
                  </div>
                  <button
                    id="opLogEndDateButton"
                    type="button"
                    className="rounded-r bg-gray-200 border-t border-b border-r border-gray-300 px-3 py-2 flex items-center hover:bg-gray-300 focus:outline-none cursor-pointer"
                    onClick={() => openDatePicker(endDateInputRef)}
                    tabIndex={-1}
                    aria-label="終了日選択"
                  >
                    <FaRegCalendarCheck className="w-5 h-5 text-gray-600" />
                  </button>
                  <input
                    id="opLogEndDateInput"
                    type="date"
                    ref={endDateInputRef}
                    className="sr-only"
                    value={endDate}
                    onChange={(e) => {
                      setEndDate(e.target.value);
                      setEndDateError("");
                      endDateTooltip?.hide();
                    }}
                    tabIndex={-1}
                    aria-label="終了日入力"
                  />
                </div>
              </div>
            </div>
            <div className="flex items-center justify-between p-4 border-t rounded-b bg-gradient-header">
              <div className="text-sm font-normal text-white">* 必須入力</div>
              <div className="flex flex-row-reverse items-center">
                <button
                  type="button"
                  onClick={onClose}
                  className="w-28 ms-3 rounded bg-gradient-blue px-3 py-2 text-center text-xs font-medium text-white drop-shadow-blue shadow-inner hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
                >
                  キャンセル
                </button>
                <button
                  type="submit"
                  className="w-28 rounded bg-gradient-dark px-3 py-2 text-center text-xs font-medium text-white drop-shadow-dark shadow-dark hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
                >
                  OK
                </button>
              </div>
            </div>
          </form>
        </div>
      </>
    </>
  );
};

export default OperationLogExportModal;
