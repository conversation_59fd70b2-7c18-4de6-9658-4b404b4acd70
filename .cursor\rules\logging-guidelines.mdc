---
description: 
globs: 
alwaysApply: true
---
# Logging Guidelines (for apps/jcs-endpoint-nextjs)

This document outlines the standards and best practices for implementing logging within the `apps/jcs-endpoint-nextjs` application. Consistent and informative logging is crucial for debugging, monitoring, and understanding application behavior.

**Primary Logging Utility & Configuration:**
The application uses a centralized **Winston** logger configured in `app/lib/logger.ts`. All logging **MUST** utilize the `Logger` class or the `@LogFunctionSignature` decorator provided by this module. The log output is structured as JSON.

**Primary References:**
*   `app/lib/logger.ts` (for `Logger` class, `@LogFunctionSignature` decorator, and Winston configuration).
*   `app/lib/definitions.ts` (for `ENV.LOG_LEVEL`).
*   Error handling utilities in `app/lib/portal-error.ts` (which also perform logging).

## Core Principles

1.  **Consistency:** All logs **MUST** be generated via the `Logger` class or the `@LogFunctionSignature` decorator to ensure a standard format and approach.
2.  **Context is Key:**
    *   The `@LogFunctionSignature` decorator automatically logs the decorated function's name and its arguments.
    *   For manual logging (`Logger.info()`, `Logger.error()`, etc.), **MUST** include sufficient context:
        *   A descriptive `message`.
        *   Relevant identifiers (e.g., `userId`, `licenseId`, `fileName`, `operationName`).
        *   Key data points that help understand the state or flow.
        *   Optionally, a `source` field indicating the file or module (e.g., `source: "api/callback/route.ts"`).
3.  **Appropriate Log Levels:** Use log levels correctly as defined by the `Logger` class and configured via `ENV.LOG_LEVEL`.
    *   **`Logger.error(logObject | string)`:** For actual errors, exceptions, and critical failures that prevent normal operation or indicate a significant problem. **MUST** include `error.message` and `error.stack` when logging caught exceptions. (Note: `handleApiError` and `handleServerError` in `app/lib/portal-error.ts` already do this).
    *   **`Logger.info(logObject | string)`:** For general operational information, key lifecycle events (e.g., application start, successful significant operations like user login/logout - see `api/audit-login-logs/route.ts`). This is the default level for `@LogFunctionSignature`.
    *   **`Logger.debug(logObject | string)`:** For detailed information useful for active debugging, such as specific variable values, detailed steps within a complex process, or Prisma query logs (as enabled in `app/lib/prisma.ts`). Debug logs are typically more verbose.
    *   **(If `warn` level is used/configured in Winston): `Logger.warn(logObject | string)`:** For potential issues or unexpected situations that do not immediately cause an error but might lead to problems later.
4.  **Performance:** Avoid excessive logging in performance-critical loops or frequently called functions, especially at `info` or `error` levels. Use `debug` for such scenarios if detailed tracing is needed during development.
5.  **Security - Avoid Sensitive Data:** **NEVER** log sensitive personal information (PII) such as passwords, raw API keys, unencrypted session tokens, or full financial details. If context requires user-specific data, use non-sensitive identifiers (e.g., `userId`, `licenseId`).
6.  **Structured Logging (JSON):** The Winston setup outputs logs in JSON. When providing `logObject` to `Logger.info()`, `Logger.error()`, etc., the properties of the object will become part of the structured JSON log entry.
    ```typescript
    Logger.info({
      message: "User login successful", // Main message
      userId: "user123",
      licenseId: "licABC",
      component: "LoginFlow" // Additional context
    });
    ```
7.  **Log Aggregation (Environment Dependent):** Console logs (which Winston outputs to) are typically collected by the hosting environment (e.g., Azure App Service via Azure Monitor Log Analytics or Application Insights) for production monitoring and analysis.

## Usage Examples

1.  **Logging Function Entry and Arguments (Recommended for most server-side functions/methods):**
    ```typescript
    // In app/lib/data.ts or app/lib/actions.ts
    import { LogFunctionSignature } from "@/app/lib/logger"; // Assuming Logger class is default export, LogFunctionSignature is named

    class MyService {
      @LogFunctionSignature() // Defaults to 'info' level
      // OR @LogFunctionSignature("debug") for more verbose logging during development
      async processData(userId: string, recordId: string): Promise<void> {
        // Function entry and arguments (userId, recordId) will be logged automatically.
        // ... implementation ...
      }
    }
    ```

2.  **Manual Informational Logging:**
    ```typescript
    import Logger from "@/app/lib/logger";

    // Example from api/callback/route.ts
    // ServerAction.createAuditEvent("LOGIN", payload.preferred_username, "");
    // Logger.info({
    //   message: "User login audit event created.",
    //   userId: payload.preferred_username,
    //   auditType: "LOGIN"
    // });
    ```
    *(Note: `createAuditEvent` itself logs. The above is an example of additional logging if needed).*

3.  **Manual Error Logging (Often handled by centralized error handlers):**
    ```typescript
    import Logger from "@/app/lib/logger";

    try {
      // ... some operation that might fail ...
      // if (somethingFailed) throw new Error("Specific failure reason");
    } catch (error: any) {
      Logger.error({
        message: `Data processing failed for item ${itemId}`,
        itemId: itemId,        // Contextual information
        errorMessage: error.message, // Error's own message
        stack: error.stack,    // Stack trace
        // any other relevant context
      });
      // Then, typically re-throw or use handleApiError/handleServerError
    }
    ```
    *Reminder: `app/lib/portal-error.ts` utilities (`handleApiError`, `handleServerError`) already perform error logging.*

4.  **Prisma Query Logging:**
    *   Prisma query logging is enabled at the `debug` level in `app/lib/prisma.ts`.
    *   To view these logs, the application's `ENV.LOG_LEVEL` environment variable **MUST** be set to `debug`.
    *   The logs include the query, parameters, and duration.

---

**NOTE TO CURSOR:**
1.  When adding new functions or methods, especially in server-side code (`app/lib/`, `app/api/`, Server Components), **strongly consider** applying the `@LogFunctionSignature()` decorator for automatic entry/argument logging. This is the preferred method for tracing function calls.
2.  For explicit logging points beyond function signatures (e.g., specific events within a function, detailed states), use the static methods of the `Logger` class (`Logger.info()`, `Logger.error()`, `Logger.debug()`).
3.  Always provide meaningful context in log objects.
4.  Critically, **NEVER** log sensitive information like passwords, raw tokens, or excessive PII.
5.  When handling caught exceptions, always log `error.message` and `error.stack`.

---