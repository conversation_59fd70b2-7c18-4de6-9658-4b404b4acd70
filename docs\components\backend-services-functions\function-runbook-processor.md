# 组件：Runbook作业处理函数 (Runbook Processor Function)

## 1. 概要 (Overview)

### 1.1. 目的 (Purpose)
本Azure Function (`RunbookProcessorFunc`) 负责处理从[`RunbookMonitorFunc`](./function-runbook-monitor.md)（通过`RunbookStatusQueue`队列）发送过来的、关于Azure Automation Runbook作业的原始执行结果。其核心职责包括：解析结果消息，根据作业的成功、失败或超时状态，执行最终的任务状态更新（例如，`COMPLETED_SUCCESS`, `COMPLETED_ERROR`），处理Runbook输出（如文件归档到Azure Blob Storage、在`OperationLog`表创建记录），释放目标容器的并发锁，清理为该任务创建的Azure Files临时工作区，并在必要时（如作业因被监控器检测到超时或处于异常挂起状态）调用Azure Automation API尝试停止作业。

### 1.2. 范围 (Scope)
本文档详细描述`RunbookProcessorFunc`的技术设计，包括其触发机制、核心处理逻辑、与数据库（`Task`, `ContainerConcurrencyStatus`, `OperationLog`表）、Azure Files、Azure Blob Storage以及Azure Automation API的交互、错误处理机制及相关配置项。它处理所有类型后台任务的最终结果。

### 1.3. 组件类型 (Component Type)
*   Azure Function (Node.js/TypeScript 运行时)
*   触发器: Azure Service Bus Queue Trigger (监听 `RunbookStatusQueue`)

### 1.4. 名词定义 (Glossary References)
*   **RunbookStatusQueue**: Azure Service Bus队列，用于接收包含Runbook原始执行结果的消息。其名称由环境变量 `SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME` 指定。
*   **Azure Files工作区**: 为每个任务在Azure Files上创建的临时工作目录，路径格式为 `TaskWorkspaces/{taskId}/`。本函数负责清理此工作区。
*   **最终成果物存储 (Azure Blob Storage)**:
    *   操作日志导出: 存储在由环境变量 `AZURE_STORAGE_CONTAINER_OPLOGS` 指定的容器。
    *   管理项目定义导出: 存储在由环境变量 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` 指定的容器。
*   其他相关术语请参考项目核心术语表 [`项目术语表`](../../../definitions/glossary.md)。
*   本文档中所有用户可见的错误和提示信息，均引用自项目核心错误消息定义文档 [`docs/definitions/error-messages.md`](../../../definitions/error-messages.md)。
*   本文档中所有环境变量的定义，均参考项目核心环境变量指南 [`docs/guides/environment-variables.md`](../../../guides/environment-variables.md)。
*   任务状态码常量（如 `TASK_STATUS_RUNBOOK_PROCESSING_CODE`, `TASK_STATUS_COMPLETED_SUCCESS_CODE`, `TASK_STATUS_COMPLETED_ERROR_CODE`）及其他内部常量定义在 `app/lib/definitions.ts`。

## 2. 功能规格 (Functional Specifications)

### 2.1. 主要流程 (Main Process Flow)

```mermaid
graph TD
    A["RunbookStatusQueue接收到原始结果消息<br/>(taskId, automationStatus, processedStatusForQueue, resultData)"] --> B{RunbookProcessorFunc被触发};
    B --> C["1. 解析消息, 获取taskId及结果详情<br/>记录日志: 开始处理Runbook结果"];
    C --> D["2. 查询Task表获取任务当前状态与详情<br/>(使用taskId)"];
    D -- "任务不存在或状态非RUNBOOK_PROCESSING<br/>(或已是终态)" --> D_Error_InvalidTask["2.1 处理无效/已处理任务<br/>(记录日志, 幂等终止)"];
    D -- "任务有效 (状态RUNBOOK_PROCESSING)" --> E_ProcessResult{"3. 根据消息中的processedStatusForQueue<br/>或runbookJobStatusFromAutomation处理结果"};

    subgraph E_ProcessResult_Sub ["3. 根据结果状态处理"]
        direction LR
        E_ProcessResult -- "COMPLETED_FROM_AUTOMATION (成功)" --> F_Success["3.1 处理成功完成的作业"];
        F_Success --> F_HandleOutput{"3.1.1 处理Runbook输出<br/>(如归档文件到Blob, 创建OperationLog记录)"};
        F_HandleOutput --> F_UpdateDbSuccess["3.1.2 更新Task状态为COMPLETED_SUCCESS<br/>记录endedAt, resultMessage (成功提示)"];
        
        E_ProcessResult -- "FAILED_FROM_AUTOMATION (失败)" --> G_Failure["3.2 处理失败的作业"];
        G_Failure --> G_HandleErrorOutput{"3.2.1 提取错误信息<br/>(从resultData.errorStreams或exports/errordetail.txt)"};
        G_HandleErrorOutput --> G_UpdateDbFailure["3.2.2 更新Task状态为COMPLETED_ERROR<br/>记录endedAt, resultMessage (EMET0012+详情 或 EMET0008)"];
        
        E_ProcessResult -- "TIMEOUT_DETECTED_BY_MONITOR (超时)" --> H_Timeout["3.3 处理监控器检测到的超时"];
        H_Timeout --> H_StopJob_Timeout["3.3.1 (尝试)调用Automation API停止作业"];
        H_StopJob_Timeout --> H_UpdateDbTimeout["3.3.2 更新Task状态为COMPLETED_ERROR<br/>记录endedAt, resultMessage (EMET0005)"];
        
        E_ProcessResult -- "STOPPED_ETC_FROM_AUTOMATION<br/>(Stopped, Suspended等异常结束)" --> I_Stopped["3.4 处理其他异常结束的作业"];
        I_Stopped --> I_StopJob_Stopped["3.4.1 (尝试)调用Automation API停止作业 (如果适用)"];
        I_StopJob_Stopped --> I_UpdateDbStopped["3.4.2 更新Task状态为COMPLETED_ERROR<br/>记录endedAt, resultMessage (EMET0010)"];
    end

    F_UpdateDbSuccess --> J_Finalize;
    G_UpdateDbFailure --> J_Finalize;
    H_UpdateDbTimeout --> J_Finalize;
    I_UpdateDbStopped --> J_Finalize;
    E_ProcessResult -- "未知或无法处理的状态" --> J_Error_UnknownStatus["记录错误, 更新Task为ERROR (EMET0008)<br/>尝试资源回收"];
    J_Error_UnknownStatus --> J_Finalize;

    J_Finalize["4. 统一资源回收 (无论Runbook成功与否)"];
    J_Finalize --> J1_ReleaseLock["4.1 释放容器并发锁<br/>(更新ContainerConcurrencyStatus为IDLE)"];
    J1_ReleaseLock --> J2_CleanWorkspace["4.2 清理Azure Files工作区<br/>(删除 TaskWorkspaces/{taskId}/)"];
    J2_CleanWorkspace --> K_End["5. 记录成功处理日志, 结束"];
    
    D_Error_InvalidTask --> Z_End["结束处理"];
```
**图 2.1: RunbookProcessorFunc 主要处理流程图**

### 2.2. 功能点描述 (Functional Points)
1.  **消息触发与解析**: 函数由`RunbookStatusQueue`中的新消息触发。消息体包含`taskId`、`runbookJobStatusFromAutomation` (Azure Automation原始作业状态)、`processedStatusForQueue` (由[`RunbookMonitorFunc`](./function-runbook-monitor.md)处理后的状态分类)、以及`resultData` (包含输出流、错误流等)。
2.  **任务详情与状态校验**:
    *   根据`taskId`从数据库`Task`表查询完整的任务记录。
    *   **校验任务存在性与当前状态**:
        *   若任务记录不存在，或其当前`status`不是 `TASK_STATUS_RUNBOOK_PROCESSING_CODE` ("RUNBOOK_PROCESSING")，则记录错误并终止处理（幂等性考虑，可能任务已被其他实例处理或状态异常）。若任务已是终态，则仅记录日志并结束。
3.  **根据作业结果状态执行相应处理**:
    *   **若 `processedStatusForQueue` 为 `'COMPLETED_FROM_AUTOMATION'` (作业成功完成)**:
        *   **处理Runbook输出**:
            *   **操作日志导出任务 (`TASK_TYPE.OPLOG_EXPORT`)**:
                *   从Azure Files工作区的 `exports/` 目录（Runbook应将导出的ZIP文件放在此处，文件名可能包含序号如 `exportoplog_{連番}.zip`）读取所有导出的操作日志ZIP文件。
                *   将每个ZIP文件上传到Azure Blob Storage的最终成果物容器（由环境变量 `AZURE_STORAGE_CONTAINER_OPLOGS` 指定），路径格式为 `{licenseId}/{taskId}/{Task.taskName}_{連番}.zip`。
                *   对于每个成功上传的ZIP文件，在`OperationLog`数据库表中创建一条记录，包含文件名、大小、关联的`taskId`、`licenseId`等元数据。
                *   `Task.resultMessage` 更新为操作日志已导出成功的通用提示。
            *   **管理项目定义导出任务 (`TASK_TYPE.MGMT_ITEM_EXPORT`)**:
                *   从Azure Files工作区的 `exports/assetsfield_def.csv` 读取导出的管理项目定义文件 (文件名固定为 `assetsfield_def.csv`)。
                *   将该CSV文件上传到Azure Blob Storage的最终成果物容器（由环境变量 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF` 指定），路径格式为 `{licenseId}/exports/{taskId}/assetsfield_def.csv`。
                *   将此Blob的相对路径更新到 `Task.outputBlobPath` 字段。
                *   `Task.resultMessage` 更新为下载链接提示或文件名。
            *   **管理项目定义导入任务 (`TASK_TYPE.MGMT_ITEM_IMPORT`)**:
                *   通常无特定输出文件需要归档。
                *   `Task.resultMessage` 更新为空或成功导入的提示。
        *   **更新任务数据库状态**: 将`Task.status`更新为 `TASK_STATUS_COMPLETED_SUCCESS_CODE` ("COMPLETED_SUCCESS")，记录`Task.endedAt`为当前时间。
    *   **若 `processedStatusForQueue` 为 `'FAILED_FROM_AUTOMATION'` (作业执行失败)**:
        *   **提取错误信息**: 尝试从`resultData.errorStreams` (原始错误流)、`resultData.errorMessageFromFile` (如果Runbook约定将错误写入 `exports/errordetail.txt`) 或 `resultData.rawErrorMessageFromJob` 获取详细错误信息。
        *   **更新任务数据库状态**: 将`Task.status`更新为 `TASK_STATUS_COMPLETED_ERROR_CODE` ("COMPLETED_ERROR")，记录`Task.endedAt`。将提取到的错误信息（或其摘要）更新到`Task.resultMessage` (例如，使用消息键 `EMET0012` 并填充错误详情；若无详细错误，则使用通用错误键 `EMET0008`)。
    *   **若 `processedStatusForQueue` 为 `'TIMEOUT_DETECTED_BY_MONITOR'` (监控器检测到超时)**:
        *   **尝试停止作业**: 调用Azure Automation API，尝试强制停止该Runbook作业（使用`taskId`作为作业ID）。此操作应容错，停止失败不应阻止后续处理。
        *   **更新任务数据库状态**: 将`Task.status`更新为 `TASK_STATUS_COMPLETED_ERROR_CODE`，记录`Task.endedAt`。`Task.resultMessage` 更新为超时错误消息（消息键 `EMET0005`）。
    *   **若 `processedStatusForQueue` 为 `'STOPPED_ETC_FROM_AUTOMATION'` (作业被停止、挂起等异常状态)**:
        *   **尝试停止作业 (如果适用)**: 根据`runbookJobStatusFromAutomation`的具体值，如果状态表示作业可能仍在消耗资源（如`Suspended`），则调用Azure Automation API尝试停止作业。
        *   **更新任务数据库状态**: 将`Task.status`更新为 `TASK_STATUS_COMPLETED_ERROR_CODE`，记录`Task.endedAt`。`Task.resultMessage` 更新为相应的错误消息（消息键 `EMET0010`）。
    *   **若为其他未知或无法处理的状态**: 记录严重错误日志，并将`Task.status`更新为 `TASK_STATUS_COMPLETED_ERROR_CODE`，`resultMessage` 使用通用错误键 `EMET0008`。
4.  **统一资源回收 (无论Runbook成功与否，只要任务达到终态)**:
    *   **释放容器并发锁**: 将`ContainerConcurrencyStatus`表中对应容器（通过`Task`记录中的`targetVmName`, `targetContainerName`关联）的`status`更新为`IDLE`，并清除`currentTaskId`。
    *   **清理Azure Files工作区**: 删除为该任务在Azure Files上创建的整个临时工作区目录 (`TaskWorkspaces/{taskId}/`)。
5.  **日志记录**: 详细记录所有接收消息、解析结果、文件操作、数据库更新、API调用及错误。

### 2.3. 业务规则 (Business Rules)
*   本函数是所有后台任务生命周期的最终处理节点。
*   根据Runbook的原始执行结果（成功、失败、超时等），执行不同的后续处理分支。
*   操作日志导出任务成功时，必须在`OperationLog`表为每个导出的文件创建元数据记录。
*   管理项目定义导出任务成功时，必须更新`Task.outputBlobPath`。
*   所有任务结束后，必须释放并发锁并清理临时工作区。
*   对于因超时或异常状态被停止的作业，应尽力调用Azure Automation API确保其停止。

### 2.4. 前提条件 (Preconditions)
*   Azure Service Bus (`RunbookStatusQueue`) 正常运行。
*   Azure SQL Database (`Task`, `ContainerConcurrencyStatus`, `OperationLog`等表) 正常运行且可访问。
*   Azure Files 服务正常运行，Function App具有读写删权限。
*   Azure Blob Storage 服务正常运行，Function App具有对相关容器的读写删权限。
*   Azure Automation 服务正常运行，Function App的托管身份或配置的服务主体具有调用Azure Automation API停止作业的权限 (如果需要)。
*   所有必需的环境变量已正确设置。

### 2.5. 制约事项 (Constraints)
*   本函数执行时间受Azure Functions的超时限制。文件上传到Blob Storage、大量`OperationLog`记录的创建等操作如果耗时过长，可能导致函数超时。
*   对Azure Storage (Files, Blob) 和 Automation API的操作性能可能受网络和服务本身限制。
*   清理非常大的Azure Files工作区可能耗时。

### 2.6. 注意事项 (Notes)
*   **幂等性**: 必须确保重复处理来自`RunbookStatusQueue`的同一条消息（例如，由于Service Bus的至少一次传递保证或函数重试）不会导致重复创建`OperationLog`记录、重复上传文件或重复释放锁等副作用。检查`Task.status`是否已处于终态是实现幂等性的关键。
*   **错误处理与资源回收的原子性**: 即使在处理输出或更新DB时发生错误，也应尽力完成后续的资源回收步骤（释放锁、清理工作区）。可以考虑将资源回收逻辑放在`finally`块或一个独立的、容错的步骤中。
*   **大文件处理**: 对于操作日志导出这类可能产生大体积ZIP文件的任务，从Azure Files读取并上传到Azure Blob Storage时，应使用流式处理以优化内存消耗。
*   **部分成功/失败**: 如果Runbook执行部分成功（例如，导出了部分日志文件但随后出错），本函数当前设计是基于Automation作业的最终整体状态（成功/失败）进行处理。不支持更细粒度的部分成功结果处理。

---

## 3. 技术设计与实现细节 (Technical Design & Implementation)

### 3.1. 技术栈与依赖 (Technology Stack & Dependencies)
*   **运行时**: Azure Functions (Node.js/TypeScript)。
*   **触发器**: Azure Service Bus Queue Trigger。
    *   队列名称: 由环境变量 `SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME` 指定。
*   **数据库交互**: Prisma ORM。
*   **文件存储交互**: `@azure/storage-file-share` (Azure Files), `@azure/storage-blob` (Azure Blob Storage)。
*   **Azure Automation交互**: `@azure/arm-automation` (或REST API)。
*   **消息队列SDK**: `@azure/service-bus`。
*   **日志**: `app/lib/logger.ts`。
*   **核心定义与常量**: `app/lib/definitions.ts`。

### 3.2. 详细界面元素定义 (Detailed UI Element Definitions)
*   本组件为后端Azure Function，无直接用户界面。

### 3.3. 详细事件处理逻辑 (Detailed Event Handling)
*   本组件由Service Bus消息触发，其核心处理逻辑见3.6节。

### 3.4. 数据结构与API/Server Action交互 (Data Structures and API/Server Action Interaction)

#### 3.4.1. 输入消息结构 (from `RunbookStatusQueue`)
参考`RunbookMonitorFunc`文档中定义的 `RunbookJobResultForQueue` 接口。消息体包含`taskId`, `runbookJobStatusFromAutomation`, `processedStatusForQueue`, `resultData` (含可能的`outputStreams`, `errorStreams`, `errorMessageFromFile`)。

#### 3.4.2. 与数据库的交互
详见3.5节。

#### 3.4.3. 与Azure Files的交互 (SDK层面)
*   `ShareDirectoryClient.listFilesAndDirectories()`: 列出工作区`exports/`目录下的文件 (用于操作日志导出)。
*   `ShareFileClient.downloadToBuffer()` / `ShareFileClient.createReadStream()`: 读取Runbook输出的文件内容。
*   `ShareDirectoryClient.deleteIfExists()`: 清理整个任务工作区 (`TaskWorkspaces/{taskId}/`)。

#### 3.4.4. 与Azure Blob Storage的交互 (SDK层面)
*   `BlobClient.uploadStream()` / `BlockBlobClient.uploadData()`: 上传文件到最终成果物容器。
    *   操作日志: 目标容器 `AZURE_STORAGE_CONTAINER_OPLOGS`，路径 `{licenseId}/{taskId}/{Task.taskName}_{連番}.zip`。
    *   管理项目定义导出: 目标容器 `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF`，路径 `{licenseId}/exports/{taskId}/assetsfield_def.csv`。
*   `BlobClient.deleteIfExists()`: (较少直接使用，主要由保留策略逻辑在`TaskExecuteFunc`中调用)。

#### 3.4.5. 与Azure Automation的交互 (SDK层面 - `AutomationClient` 或 REST API)
*   `automationClient.job.stop()`: 在处理超时或特定异常状态时，尝试停止Runbook作业。

#### 3.4.6. 序列图 (Mermaid `sequenceDiagram`)

```mermaid
sequenceDiagram
    participant SBStatusQ as "Azure Service Bus (RunbookStatusQueue)"
    participant ProcessorFunc as "RunbookProcessorFunc"
    participant Database as "Azure SQL Database"
    participant AzFiles as "Azure Files (Workspaces)"
    participant AzBlob as "Azure Blob Storage (Final Output)"
    participant AzAutomation as "Azure Automation API"

    SBStatusQ->>ProcessorFunc: 消息: { taskId, status, resultData }
    activate ProcessorFunc
    ProcessorFunc->>ProcessorFunc: 1. 解析消息
    ProcessorFunc->>Database: 2. 查询Task详情 (taskId)
    activate Database
    Database-->>ProcessorFunc: Task对象 (status: 'RUNBOOK_PROCESSING')
    deactivate Database

    alt 根据消息中的 processedStatusForQueue 处理
        ProcessorFunc->>ProcessorFunc: 3.1 (成功分支)
        ProcessorFunc->>AzFiles: 3.1.1 读取工作区输出文件 (exports/*)
        activate AzFiles
        AzFiles-->>ProcessorFunc: 文件内容/列表
        deactivate AzFiles
        ProcessorFunc->>AzBlob: 3.1.2 上传文件到最终Blob容器
        activate AzBlob
        AzBlob-->>ProcessorFunc: 上传成功
        deactivate AzBlob
        opt TaskType is OPLOG_EXPORT
            ProcessorFunc->>Database: 3.1.3 创建OperationLog记录
        end
        ProcessorFunc->>Database: 3.1.4 更新Task状态为COMPLETED_SUCCESS, outputBlobPath (若适用)
    else processedStatusForQueue 为 FAILED
        ProcessorFunc->>ProcessorFunc: 3.2 (失败分支)
        ProcessorFunc->>AzFiles: (可选) 3.2.1 尝试读取 exports/errordetail.txt
        ProcessorFunc->>Database: 3.2.2 更新Task状态为COMPLETED_ERROR, resultMessage
    else processedStatusForQueue 为 TIMEOUT
        ProcessorFunc->>ProcessorFunc: 3.3 (超时分支)
        ProcessorFunc->>AzAutomation: (尝试) 3.3.1 停止Automation作业 (taskId)
        ProcessorFunc->>Database: 3.3.2 更新Task状态为COMPLETED_ERROR (EMET0005)
    else processedStatusForQueue 为 STOPPED_ETC
        ProcessorFunc->>ProcessorFunc: 3.4 (其他异常结束分支)
        ProcessorFunc->>AzAutomation: (尝试) 3.4.1 停止Automation作业 (taskId)
        ProcessorFunc->>Database: 3.4.2 更新Task状态为COMPLETED_ERROR (EMET0010)
    end

    ProcessorFunc->>Database: 4.1 释放容器并发锁 (ContainerConcurrencyStatus)
    activate Database
    Database-->>ProcessorFunc: 更新成功
    deactivate Database
    ProcessorFunc->>AzFiles: 4.2 清理工作区 (TaskWorkspaces/taskId/)
    activate AzFiles
    AzFiles-->>ProcessorFunc: 清理成功
    deactivate AzFiles
    
    ProcessorFunc->>ProcessorFunc: 5. 记录处理完成日志
    deactivate ProcessorFunc
```

### 3.5. 数据库设计与访问详情 (Database Design and Access Details)

#### 3.5.1. 相关表引用
*   **`Task` 表**: 读取任务详情；更新最终`status`, `endedAt`, `resultMessage`, `outputBlobPath` (仅管理项目定义导出)。
*   **`ContainerConcurrencyStatus` 表**: 更新容器`status`为`IDLE`，清除`currentTaskId`。
*   **`OperationLog` 表**: (仅操作日志导出任务) 创建新记录。

#### 3.5.2. 主要数据查询/变更逻辑

1.  **查询任务详情**:
    ```typescript
    prisma.task.findUnique({ where: { id: taskId } });
    ```
2.  **更新任务最终状态 (成功示例)**:
    ```typescript
    prisma.task.update({
      where: { id: taskId },
      data: {
        status: TASK_STATUS_COMPLETED_SUCCESS_CODE,
        endedAt: new Date(),
        resultMessage: successMessage, // e.g., "操作ログをエクスポートしました。"
        outputBlobPath: (taskType === 'TASK_TYPE.MGMT_ITEM_EXPORT') ? finalBlobPath : undefined,
      },
    });
    ```
3.  **更新任务最终状态 (失败示例)**:
    ```typescript
    prisma.task.update({
      where: { id: taskId },
      data: {
        status: TASK_STATUS_COMPLETED_ERROR_CODE,
        endedAt: new Date(),
        resultMessage: errorMessage, // e.g., "EMET0012: {details}"
      },
    });
    ```
4.  **创建`OperationLog`记录 (在事务内, 针对操作日志导出的每个文件)**:
    ```typescript
    // 伪代码, 假设 uploadedFiles 是一个包含 {name, size, licenseId, generatedByTaskId} 的数组
    await prisma.operationLog.createMany({
      data: uploadedFiles.map(file => ({
        name: file.blobNameInStorage, // 或原始文件名，取决于约定
        size: file.size,
        licenseId: task.licenseId,
        generatedByTaskId: task.id,
        // createdAt 默认 now()
      })),
    });
    ```
5.  **释放并发锁**:
    ```typescript
    prisma.containerConcurrencyStatus.update({
      where: { targetVmName_targetContainerName: { targetVmName: task.targetVmName, targetContainerName: task.dockerContainerName } },
      data: { status: 'IDLE', currentTaskId: null, lastStatusChangeAt: new Date() }
    });
    ```
*   **事务管理**:
    *   对于操作日志导出任务，将文件上传到Blob和创建`OperationLog`记录的操作，如果涉及多个文件，应考虑将所有相关的DB `OperationLog.createMany` 和最终的 `Task.update` 放在一个事务中，以确保文件元数据的一致性。但单个Blob文件上传通常是独立操作。
    *   释放并发锁和清理工作区是独立的清理步骤，其失败不应影响任务最终状态的标记。

### 3.6. 关键后端逻辑/算法 (Key Backend Logic/Algorithms)

`RunbookProcessorFunc`的核心算法是基于从`RunbookStatusQueue`接收到的消息中的`processedStatusForQueue`和`runbookJobStatusFromAutomation`字段进行的多分支处理：

1.  **消息解析与任务加载**:
    *   安全解析JSON消息。
    *   根据`taskId`从数据库加载`Task`记录。校验任务是否存在且状态为`RUNBOOK_PROCESSING`。若不满足，则执行幂等逻辑（记录日志并结束）。
2.  **成功处理 (`processedStatusForQueue === 'COMPLETED_FROM_AUTOMATION'`)**:
    *   根据`task.taskType`执行特定的输出处理：
        *   **`TASK_TYPE.OPLOG_EXPORT`**:
            a.  列出Azure Files工作区`exports/`目录下的所有 `.zip` 文件。
            b.  对每个文件：上传到`AZURE_STORAGE_CONTAINER_OPLOGS`容器的`{licenseId}/{taskId}/{Task.taskName}_{連番}.zip`路径。
            c.  在数据库`OperationLog`表创建对应记录。
            d.  若任一文件处理失败，整体任务可标记为部分成功或失败（当前设计为整体成功/失败）。
        *   **`TASK_TYPE.MGMT_ITEM_EXPORT`**:
            a.  从工作区`exports/assetsfield_def.csv`读取文件。
            b.  上传到`AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF`容器的`{licenseId}/exports/{taskId}/assetsfield_def.csv`路径。
            c.  更新`Task.outputBlobPath`。
        *   **`TASK_TYPE.MGMT_ITEM_IMPORT`**: 通常无输出文件。
    *   更新`Task`状态为`COMPLETED_SUCCESS`，设置`endedAt`, `resultMessage`。
3.  **失败处理 (`processedStatusForQueue === 'FAILED_FROM_AUTOMATION'`)**:
    *   尝试从`resultData.errorStreams`或工作区`exports/errordetail.txt`提取详细错误。
    *   更新`Task`状态为`COMPLETED_ERROR`，设置`endedAt`, `resultMessage` (含错误详情，如`EMET0012`，或通用`EMET0008`)。
4.  **超时处理 (`processedStatusForQueue === 'TIMEOUT_DETECTED_BY_MONITOR'`)**:
    *   调用Azure Automation API尝试停止作业 (`job.stop`)。
    *   更新`Task`状态为`COMPLETED_ERROR`，设置`endedAt`, `resultMessage` (`EMET0005`)。
5.  **其他异常结束处理 (`processedStatusForQueue === 'STOPPED_ETC_FROM_AUTOMATION'`)**:
    *   如果需要，调用Azure Automation API尝试停止作业。
    *   更新`Task`状态为`COMPLETED_ERROR`，设置`endedAt`, `resultMessage` (`EMET0010`)。
6.  **资源回收 (在`finally`块或确保执行的步骤中)**:
    *   调用数据库服务释放并发锁。
    *   调用Azure Files服务删除任务工作区。
    这些操作应尽可能容错，其失败不应阻止函数将消息标记为已处理（如果主要业务逻辑已完成）。

### 3.7. 错误处理详情 (Detailed Error Handling)

| #  | 错误场景描述 (中文) | 触发位置 | 引用的消息ID/消息键 (用户可见部分, 更新到`Task.resultMessage`) | 系统内部处理及日志记录建议 (中文) | 日志级别 |
|----|-----------------|----------|------------------------------------|---------------------------------|-----------|
| 1  | Service Bus消息格式无效或`taskId`缺失 | Function触发，消息解析阶段 | (内部错误，不更新Task) | 记录错误。消息可能进入DLQ。 | ERROR |
| 2  | 根据`taskId`未找到任务，或任务状态非`RUNBOOK_PROCESSING` (或已终态) | 查询`Task`表后 | (内部错误，若任务状态异常，按原样保留或标记为新错误；若不存在/已终态，则幂等处理) | 记录错误/警告。安全结束。 | WARN/ERROR |
| 3  | 读取Azure Files工作区文件失败 (如`exports/`目录或文件不存在) | 文件输出处理阶段 | `EMET0002` (或更具体的，如果能区分) | 更新`Task.status`为`ERROR`。记录详细的Azure Files SDK错误。 | ERROR |
| 4  | 上传文件到Azure Blob Storage失败 | 文件输出处理阶段 | `EMET0003` | 更新`Task.status`为`ERROR`。记录详细的Azure Blob SDK错误。 | ERROR |
| 5  | 创建`OperationLog`记录失败 | 操作日志导出成功处理分支 | `EMET0007` (或更具体的) | 更新`Task.status`为`ERROR` (即使文件已上传也标记任务失败，保证数据一致性)。记录DB错误。 | ERROR |
| 6  | 调用Azure Automation API停止作业失败 | 超时/异常结束处理分支 | (不直接更新`resultMessage`为此错误，但记录日志) | 记录详细API错误。任务状态仍按超时/异常逻辑更新。 | WARN |
| 7  | 释放并发锁失败 | 资源回收阶段 | (不更新`resultMessage`为此错误，但记录日志) | 记录DB错误。这是严重问题，可能导致后续任务阻塞，需告警。 | CRITICAL |
| 8  | 清理Azure Files工作区失败 | 资源回收阶段 | (不更新`resultMessage`为此错误，但记录日志) | 记录Files SDK错误。可能导致存储空间浪费，需关注。 | WARN |
| 9  | 数据库更新`Task`最终状态失败 (通用) | 各处理分支的DB更新点 | `EMET0007` (若无更具体的`resultMessage`可设置) | 记录DB错误。这是一个关键失败，可能导致任务状态不一致。消息可能重试或进入DLQ。 | ERROR |
| 10 | Function执行超时 (超出`FUNCTION_TIMEOUT_SECONDS`) | Azure Functions运行时 | (不直接更新，但消息会进入`RunbookStatusQueue`的DLQ，由[`RunbookProcessorTimeoutFunc`](./function-runbook-processor-timeout.md)处理并可能更新为`EMET0005`) | Azure平台记录超时。消息进入DLQ。 | ERROR |

### 3.8. 配置项 (Configuration)

#### 3.8.1. 值列表 (LOV) 定义 (源自 `docs/definitions/lov-definitions.md`)
*   `TASK_STATUS` (父级代码): 主要使用 `TASK_STATUS_RUNBOOK_PROCESSING_CODE`, `TASK_STATUS_COMPLETED_SUCCESS_CODE`, `TASK_STATUS_COMPLETED_ERROR_CODE`。
*   `TASK_TYPE` (父级代码): 用于判断如何处理Runbook输出。

#### 3.8.2. 环境变量 (源自 `docs/guides/environment-variables.md`)
*   `SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME`: 本Function监听的队列名称。
*   `MSSQL_PRISMA_URL`
*   `AZURE_STORAGE_CONNECTION_STRING`
*   `AZURE_STORAGE_FILE_SHARE_NAME` (或等效配置)
*   `AZURE_STORAGE_CONTAINER_OPLOGS`
*   `AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF`
*   `AZURE_AUTOMATION_ACCOUNT_NAME`, `AZURE_AUTOMATION_RESOURCE_GROUP_NAME` (或等效配置)
*   `LOG_LEVEL`
*   `FUNCTION_TIMEOUT_SECONDS` (若在`host.json`中通过此配置)

#### 3.8.3. 错误与提示消息 (源自 `docs/definitions/error-messages.md`)
本函数内部主要使用以下消息键来更新`Task.resultMessage`：
*   `EMET0002` (工作区访问失败 - 若读取输出失败)
*   `EMET0003` (产物存储失败 - 若上传Blob失败)
*   `EMET0005` (任务超时 - 当处理由监控器标记为超时的消息时)
*   `EMET0007` (数据库更新错误 - 若最终更新Task状态失败)
*   `EMET0008` (未预期内部错误 - 作业失败但无详细错误时)
*   `EMET0010` (系统中止任务 - 作业被停止/挂起时)
*   `EMET0012` (Runbook脚本执行失败 - 作业失败有详细错误时)
*   (成功时，通常是特定于任务类型的成功提示，或无特定消息仅状态变更)

### 3.9. 注意事项与其他 (Notes/Miscellaneous)
*   **资源清理的顺序与容错**: 释放并发锁应优先于清理工作区。两者都应设计为即使前面的业务逻辑（如文件上传、DB更新）失败，也要尝试执行。它们的执行结果（成功/失败）应被记录，但不应覆盖任务本身因业务逻辑失败而设置的错误状态和消息。
*   **死信队列 (DLQ) 处理**: 需要有相应的[`RunbookProcessorTimeoutFunc`](./function-runbook-processor-timeout.md)来监听 `RunbookStatusQueue` 的DLQ，以处理本函数因执行超时或其他持久性错误而无法成功消费的消息。该超时处理函数的核心职责是尝试完成资源回收（释放锁、清理工作区）并将任务标记为错误。
*   **大文件列表处理 (操作日志)**: 如果操作日志导出一次性产生大量ZIP文件，在列出、上传和创建`OperationLog`记录时，要注意避免Function执行超时。可以考虑分批处理或优化SDK调用方式。
