/**
 * @file use-license.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

import { fetcher } from "@/app/lib/utils"; // データ取得関数
import useSWR from "swr"; // SWRフック

// ライセンス情報取得のためのカスタムフック
const useLicense = () => {
  const url = `/api/licenses/current`; // ライセンス情報APIのエンドポイント
  const { data, error, isLoading, mutate } = useSWR(url, fetcher); // SWRフックを使用してデータ取得

  // データ、エラー、読み込み中の状態、データ更新関数を返す
  return {
    data,
    error,
    isLoading,
    mutate,
  };
};

// カスタムフックをエクスポート
export default useLicense;
