# 数据模型: 通知 (Notification)

*   **表名 (逻辑名)**: `Notification`
*   **对应UI界面**: 「お知らせ情報表示」 (Notification Display Area/Popup)
*   **主要用途**: 管理向用户展示的通知信息。通知可以针对所有用户（共通）、特定契约计划、特定契约（许可证）或特定用户。

## 1. 字段定义

| 字段名 (Prisma/英文) | 数据类型 (SQL参考) | PK | FK | UQ | Nullable | Default          | 描述 (中文)                                                                                                                                                             |
| :------------------- | :----------------- | :--- | :--- | :--- | :------- | :--------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `id`                 | VARCHAR(36)        | ●    |      |      |          | `cuid()`         | 主键。CUID格式，由系统自动生成。                                                                                                                                          |
| `content`            | NVARCHAR(4000)     |      |      |      |          |                  | 通知的具体文本内容。数据库层面限制了最大长度为4000个字符 (`@db.NVarChar(4000)`)。                                                                                              |
| `publishedAt`        | VARCHAR(XX)        |      |      |      |          |                  | 通知的发布日期/时间。**注意：Prisma Schema中此字段类型为String，实际存储格式和排序需关注。** 原对应「機能仕様書」中的 `publishDate`。                                                              |
| `type`               | VARCHAR(50)        |      |      |      |          |                  | 通知的类型。例如："COMMON" (共通), "PLAN" (契约计划特定), "CONTRACT" (契约特定), "USER" (用户特定)。原对应「機能仕様書」中的 `notificationType`。                                      |
| `userId`             | VARCHAR(50)        |      |      |      | Yes      |                  | **可选**。如果通知是针对特定用户的 (`type`="USER")，此字段存储该用户的ID (来自Keycloak)。                                                                                             |
| `licenseId`          | VARCHAR(XX)        |      | ●    |      | Yes      |                  | **可选外键**。如果通知是针对特定契约/许可证的 (`type`="CONTRACT")，此字段关联到 `License` 表的 `licenseId` (业务键)。                                                                             |
| `planId`             | VARCHAR(XX)        |      | ●    |      | Yes      |                  | **可选外键**。如果通知是针对特定契约计划的 (`type`="PLAN")，此字段关联到 `Plan` 表的 `planId` (业务键)。                                                                                   |
| `serialNo`           | INT                |      |      | ●    |          | `autoincrement()`| 通知的序列号或通番。在数据库中是自增的唯一整数值，可用于排序或内部引用。                                                                                                                |

*PK: Primary Key, FK: Foreign Key, UQ: Unique Key*
*XX表示具体长度，取决于实际的数据库Schema定义。*

## 2. 关系

*   **对 `License` (`license`)**: 可选的多对一关系。通过 `licenseId` 字段关联到 `License` 表的 `licenseId` 唯一键。`onDelete: NoAction, onUpdate: NoAction` 表示数据库层面不执行级联操作。一个通知可以关联到一个许可证，也可以不关联（例如共通通知）。
*   **对 `Plan` (`plan`)**: 可选的多对一关系。通过 `planId` 字段关联到 `Plan` 表的 `planId` 唯一键。`onDelete: NoAction, onUpdate: NoAction` 表示数据库层面不执行级联操作。一个通知可以关联到一个契约计划，也可以不关联。
*   **与用户 (User) 的逻辑关联**: 通过 `userId` 字段与Keycloak中的用户进行逻辑关联，Prisma Schema中未定义直接的数据库外键。

## 3. 索引 (示例)

*   `PRIMARY KEY (id)`
*   `UNIQUE KEY (serialNo)` (确保序列号唯一，Prisma Schema已定义)
*   `INDEX idx_notification_type (type)` (便于按类型筛选通知)
*   `INDEX idx_notification_userid (userId)` (如果经常需要查询特定用户的通知)
*   `INDEX idx_notification_licenseid (licenseId)` (如果经常需要查询特定许可证的通知)
*   `INDEX idx_notification_planid (planId)` (如果经常需要查询特定契约计划的通知)
*   `INDEX idx_notification_publishedat (publishedAt)` (注意：基于字符串的排序)