#!/usr/bin/env node

/**
 * 测试端口清理脚本
 * 清理所有测试相关的端口
 */

const { execSync } = require('child_process');

console.log('🧹 开始清理所有测试端口...\n');

const testPorts = [3000, 3001, 7071, 7072, 10000, 10001, 10002];

async function cleanupPort(port) {
  try {
    console.log(`🔍 检查端口 ${port}...`);
    
    // 检查端口是否被占用
    const result = execSync(`netstat -ano | grep :${port}`, { stdio: 'pipe', encoding: 'utf8' });
    
    if (result.includes('LISTENING')) {
      const lines = result.split('\n');
      const pidsToKill = new Set();
      
      for (const line of lines) {
        if (line.includes('LISTENING')) {
          const parts = line.trim().split(/\s+/);
          const pid = parts[parts.length - 1];
          
          if (pid && pid !== '0') {
            pidsToKill.add(pid);
          }
        }
      }
      
      for (const pid of pidsToKill) {
        try {
          console.log(`🔪 终止进程 ${pid} (端口 ${port})`);
          execSync(`cmd //c "taskkill /PID ${pid} /F"`, { stdio: 'pipe' });
          console.log(`✅ 成功终止进程 ${pid}`);
        } catch (killError) {
          console.log(`⚠️ 终止进程 ${pid} 失败`);
        }
      }
      
      // 等待端口释放
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 再次检查
      try {
        const checkResult = execSync(`netstat -ano | grep :${port}`, { stdio: 'pipe', encoding: 'utf8' });
        if (checkResult.includes('LISTENING')) {
          console.log(`⚠️ 端口 ${port} 仍被占用`);
        } else {
          console.log(`✅ 端口 ${port} 已释放`);
        }
      } catch (e) {
        console.log(`✅ 端口 ${port} 已释放`);
      }
    } else {
      console.log(`✅ 端口 ${port} 未被占用`);
    }
  } catch (error) {
    console.log(`✅ 端口 ${port} 未被占用`);
  }
  
  console.log('');
}

async function cleanupAllPorts() {
  for (const port of testPorts) {
    await cleanupPort(port);
  }
  
  console.log('🎉 所有端口清理完成！');
  
  // 最终验证
  console.log('\n📊 最终端口状态验证:');
  try {
    const finalCheck = execSync(`netstat -ano | grep -E ":(${testPorts.join('|')})"`, { stdio: 'pipe', encoding: 'utf8' });
    if (finalCheck.trim()) {
      console.log('⚠️ 仍有端口被占用:');
      console.log(finalCheck);
    } else {
      console.log('✅ 所有测试端口都已清理干净');
    }
  } catch (error) {
    console.log('✅ 所有测试端口都已清理干净');
  }
}

cleanupAllPorts().catch(console.error);