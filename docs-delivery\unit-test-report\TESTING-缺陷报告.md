# JCS Endpoint NextJS 测试缺陷报告

## 概述

本报告记录了在修复 `apps/jcs-endpoint-nextjs/__tests__` 目录下测试用例过程中发现的缺陷。这些缺陷是通过对照 `docs-delivery/detailed-design-specifications/server-list-detailed-design/` 和 `docs-delivery/detailed-design-specifications/task-list-detailed-design/` 设计规格文档发现的，而不是迎合业务代码实现。

**报告日期**: 2025-01-15  
**测试环境**: Node.js + Jest + React Testing Library  
**测试范围**: UI 组件测试  

## 缺陷分类

| 缺陷ID | 缺陷类型 | 严重程度 | 影响范围 | 状态 |
|--------|----------|----------|----------|------|
| DEF-001 | UI 渲染 | 中 | ConfirmModal 组件 | 待修复 |
| DEF-002 | 状态管理 | 低 | ServerActionsDropdown 组件 | 待修复 |
| DEF-003 | 数据处理 | 中 | TaskActions 组件 | 待修复 |

## 详细缺陷描述

### DEF-001: ConfirmModal 组件按钮文本渲染问题

**描述**:  
ConfirmModal 组件的确认按钮实际渲染的 accessible name 为 "Loading... OK"，而不是设计规格中指定的 "OK"。这是因为 Spinner 组件包含了 `<span className="sr-only">Loading...</span>` 文本，导致按钮的 accessible name 变成了 "Loading... OK"。

**重现步骤**:
1. 渲染 ConfirmModal 组件
2. 检查确认按钮的 accessible name

**预期结果**:  
按钮的 accessible name 应该是 "OK"（或自定义的 confirmText）

**实际结果**:  
按钮的 accessible name 是 "Loading... OK"（或 "Loading... " + confirmText）

**设计规格参考**:  
根据 `docs-delivery/detailed-design-specifications/server-list-detailed-design/02-画面項目定義.md` 中的规定，确认按钮应该显示 "OK" 文本。

**修复建议**:  
修改 Spinner 组件，使其在按钮内部使用时不添加 "Loading..." 文本，或者使用 aria-label 属性来覆盖按钮的 accessible name。

### DEF-002: ServerActionsDropdown 组件客户端渲染问题

**描述**:  
ServerActionsDropdown 组件使用 `isClient` 状态来控制按钮的渲染，但这导致在测试环境中需要等待状态更新才能看到按钮。这种实现方式不符合 React 最佳实践，应该使用 `useEffect` 仅用于副作用，而不是控制渲染。

**重现步骤**:
1. 渲染包含 ServerActionsDropdown 的组件
2. 尝试立即查找 "タスクを選択" 按钮

**预期结果**:  
按钮应该立即可用，或者有明确的加载状态指示器

**实际结果**:  
按钮在 `isClient` 状态更新前不可见，导致测试需要使用 setTimeout 或 waitFor 来等待

**设计规格参考**:  
设计规格中没有明确指定组件的渲染行为，但根据 React 最佳实践，组件应该避免在 useEffect 中控制主要 UI 元素的渲染。

**修复建议**:  
重构 ServerActionsDropdown 组件，使用 React.Suspense 或其他模式来处理客户端渲染，而不是依赖 `isClient` 状态。

### DEF-003: TaskActions 组件中的操作日志导出消息处理

**描述**:  
TaskActions 组件在操作日志导出完成时显示的是 `task.resultMessage`，而不是硬编码的消息。这导致测试需要提供正确的 `resultMessage` 属性，而不是直接检查预期的文本内容。

**重现步骤**:
1. 渲染 TaskActions 组件，传入 COMPLETED_SUCCESS 状态和 OPLOG_EXPORT 类型的任务
2. 检查是否显示了操作日志导出完成的消息

**预期结果**:  
组件应该显示标准化的操作日志导出完成消息，不依赖于 `task.resultMessage`

**实际结果**:  
组件直接显示 `task.resultMessage`，导致消息内容可能不一致

**设计规格参考**:  
根据 `docs-delivery/detailed-design-specifications/task-list-detailed-design/02-画面項目定義.md`，操作日志导出完成时应该显示标准化的消息。

**修复建议**:  
修改 TaskActions 组件，为不同类型的任务定义标准化的消息模板，而不是直接使用 `task.resultMessage`。

## 其他观察

1. **测试覆盖率问题**:  
   虽然所有测试都通过了，但测试覆盖率仍然较低，特别是 actions-dropdown.tsx 的函数覆盖率只有 17.65%。建议增加更多测试用例来提高覆盖率。

2. **测试依赖问题**:  
   许多测试依赖于组件的内部实现细节，如 DOM 结构和状态管理方式，这使得测试容易因组件实现变化而失败。建议重构测试，使其更关注组件的行为而不是实现细节。

3. **异步渲染处理**:  
   多个组件使用 setTimeout 和状态更新来控制渲染，这导致测试需要使用 waitFor 或 setTimeout 来等待渲染完成。建议重构组件，使用更现代的 React 模式来处理异步渲染。

## 结论与建议

1. **修复优先级**:  
   - 高优先级: 无
   - 中优先级: DEF-001, DEF-003
   - 低优先级: DEF-002

2. **测试改进建议**:  
   - 使用 React Testing Library 的 `findBy*` 查询而不是 `getBy*` + waitFor 来处理异步渲染
   - 使用 `userEvent` 而不是 `fireEvent` 来模拟用户交互
   - 使用 `screen.debug()` 来调试测试失败

3. **组件改进建议**:  
   - 重构 ConfirmModal 组件，使其按钮文本符合设计规格
   - 重构 ServerActionsDropdown 组件，改进客户端渲染逻辑
   - 标准化 TaskActions 组件中的消息处理

---

**报告作者**: WSST  
**报告版本**: v1.0
