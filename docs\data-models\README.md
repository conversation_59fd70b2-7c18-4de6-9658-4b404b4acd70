# 数据模型 (Data Models)

## 概要 (Overview)

本目录包含了项目中所有核心数据实体（主要是通过 **Prisma ORM** 管理的数据库表）的详细定义和说明。每个Markdown文件对应Prisma schema (`schema.prisma`) 中的一个`model`，并详细描述了该模型的结构、字段、关系以及相关的业务含义和约束。

这些文档是理解系统数据持久化层、进行数据库设计审查、以及辅助后端开发（特别是与数据存取相关的逻辑）的关键参考。AI编程助手也会利用这些定义来生成和理解与数据操作相关的代码。

## 文件命名约定 (File Naming Convention)

为了保持与项目代码（特别是Prisma schema）的一致性和清晰性，本目录下的文件命名遵循以下约定：

*   **Prisma 模型名到文件名**:
    *   Prisma schema中的模型名（通常为PascalCase，如 `MyExampleTable`）将被转换为**小写kebab-case**作为Markdown文件名。
    *   例如：
        *   Prisma模型 `AuditLogin` 对应文件 `audit-login.md`
        *   Prisma模型 `ContainerConcurrencyStatus` 对应文件 `container-concurrency-status.md`
        *   Prisma模型 `LicensePlan` 对应文件 `license-plan.md`
*   **文件扩展名**: 统一使用 `.md`。

## 内容结构 (Content Structure of Each Model File)

每个数据模型（表）的Markdown文件通常包含以下部分：

1.  **表名 (Table Name)**: 逻辑表名和对应的物理表名（通常与Prisma模型名一致）。
2.  **概要 (Overview/Description)**: 简要描述该表在系统中的作用和存储的数据类型。
3.  **列定义 (Column Definitions)**:
    *   使用Markdown表格详细列出每个字段（列）的技术属性，包括：
        *   逻辑名 (Logical Name / 中文名)
        *   物理名 (Physical Name / Prisma字段名)
        *   数据类型 (Data Type / Prisma类型及底层SQL类型)
        *   是否可空 (Nullable)
        *   主键 (PK) / 外键 (FK) 标识
        *   默认值 (Default Value)
        *   约束 (Constraints / 例如：`@unique`, `@id`, 长度限制, 枚举值范围等)
        *   描述/备注 (Description / Remarks) - 解释字段的业务含义和特殊规则。
4.  **索引 (Indexes)**: 列出为该表定义的数据库索引及其包含的列和类型（如 `@index`, `@@index`）。
5.  **关系 (Relationships)**:
    *   描述该表与其他表之间的关系（一对一, 一对多, 多对多），明确指出关联的字段和外键约束。
    *   对应Prisma schema中的关系字段 (`@relation`)。
6.  **(可选) 示例数据 (Sample Data)**: 提供少量示例数据，帮助理解表的实际内容。
7.  **(可选) 注意事项 (Notes/Considerations)**: 其他与该表相关的设计决策或特殊说明。

## 如何使用 (How to Use)

*   **开发者**: 在进行后端开发、数据库迁移或需要理解特定数据结构时，请参考相应的数据模型文件。确保您的代码实现与这些定义保持一致。
*   **AI编程助手**: AI助手将使用这些Markdown文件作为理解数据库结构和生成相关查询（如Prisma Client操作）的主要依据。在提问时，您可以直接引用这些文件名，例如：“请根据 `docs/data-models/task.md` 中的定义，生成一个创建新任务的Prisma操作。”
*   **设计与审查**: 在进行新功能设计或现有功能变更时，数据模型文档是讨论和审查数据影响的重要基础。

## 模型列表 (List of Models)

(此列表可以手动维护，或通过脚本根据目录内容自动生成，作为快速导航)

*   [AuditLogin](./audit-login.md) - 登录审计日志表
*   [ContainerConcurrencyStatus](./container-concurrency-status.md) - 容器并发状态表
*   [License](./license.md) - 许可证信息表
*   [LicensePlan](./license-plan.md) - 许可证与套餐关联表
*   [Lov](./lov.md) - 值列表 (List of Values) 表
*   [Notification](./notification.md) - 通知信息表
*   [OperationLog](./operation-log.md) - 操作日志元数据表
*   [Plan](./plan.md) - 套餐信息表
*   [PlanManual](./plan-manual.md) - 套餐与手册关联表
*   [PlanProduct](./plan-product.md) - 套餐与产品媒体关联表
*   [PlanProvidedFile](./plan-provided-file.md) - 套餐与提供文件关联表
*   [PlanSupport](./plan-support.md) - 套餐与支持信息关联表
*   [ProductManual](./product-manual.md) - 产品手册信息表
*   [ProductMedia](./product-media.md) - 产品媒体信息表
*   [ProvidedFile](./provided-file.md) - 提供文件信息表
*   [Server](./server.md) - 服务器信息表
*   [SupportFile](./support-file.md) - 支持文件信息表
*   [Task](./task.md) - 后台任务信息表
*   [User](./user.md) - 用户信息表 (注意: 实际用户认证和核心信息管理由Keycloak负责，此表可能仅用于特定场景或历史兼容)
