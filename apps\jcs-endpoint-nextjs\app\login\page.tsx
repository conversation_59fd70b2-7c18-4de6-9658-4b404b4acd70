/**
 * @file page.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

import Form from "../ui/login-form";
import NotificationList from "../ui/notification-list";

// ログイン画面
export default function Login() {
  return (
    <div className="flex flex-col relative isolate h-screen bg-gradient-to-b from-[#111827] from-45% to-[#81C7EB]">
      <div className="flex flex-col justify-center flex-grow max-w-md ml-64 mb-2">
        <Form />
      </div>
      <div className="absolute w-full h-[70%] inset-x-0 top-[30%] -z-20 transform-gpu overflow-hidden bg-no-repeat bg-top bg-[url('/login_wave.png')]"></div>
      <div className="w-full h-40 md:h-40 lg:h-48 xl:h-52 2xl:h-80 mx-auto p-4 lg:p-2 2xl:p-4 bg-gray-50/50 text-gray-900 flex flex-col">
        <div className="text-sm lg:text-sm 2xl:text-base font-semibold mb-2 lg:mb-3 2xl:mb-2">
          お知らせ
        </div>
        <div className="flex-grow text-xs lg:text-xs 2xl:text-sm font-normal overflow-y-auto">
          <NotificationList api="/api/notifications/system" />
        </div>
      </div>
      <div className="w-full mx-auto p-4 md:flex md:items-center md:justify-between">
        <ul></ul>
        <span className="text-sm sm:text-center text-white">
          Copyright (C) 2024, 2025, Hitachi, Ltd.
          <br/>
          Copyright (C) 2024, 2025, Hitachi Solutions, Ltd.
        </span>
      </div>
    </div>
  );
}
