# 组件：任务列表页面 (Task List Page)

## 1. 概要 (Overview)

### 1.1. 目的 (Purpose)
本页面旨在为资产分发管理服务的用户（顾客系统管理者）提供一个集中的界面，用以查看和管理由其（或同一许可证下的其他用户）通过“服务器列表”等功能发起的各类后台任务（如操作ログのエクスポート、管理項目定義のインポート・エクスポート）的当前执行状态、历史记录、最终结果以及相关的详细信息。用户还可以通过本页面对处于特定状态（“実行待ち”）的任务执行中止操作。

### 1.2. 范围 (Scope)
本组件设计文档覆盖“任务列表”页面的前端用户界面、用户交互逻辑、与后端的数据交互（通过Server Actions获取任务数据、发起任务中止请求等）、以及相关的配置和错误处理。

### 1.3. 名词定义 (Glossary References)
*   **后台任务 (Background Task)**: 指用户通过门户发起，在后端异步执行的操作。
*   **任务状态 (Task Status)**: 指后台任务在其生命周期中所处的阶段，如“実行待ち”、“実行中”、“正常終了”等。其内部状态码与界面显示文本的映射关系，请参考项目核心值列表定义文档 `docs/definitions/lov-definitions.md` 中 `parentCode='TASK_STATUS'` 的定义及本项目功能规格书。
*   其他相关术语请参考项目核心术语表 `docs/definitions/glossary.md`。
*   本文档中所有用户可见的错误和提示信息，均引用自项目核心错误消息定义文档 `docs/definitions/error-messages.md`。
*   本文档中所有环境变量的定义，均参考项目核心环境变量指南 `docs/guides/environment-variables.md`。

## 2. 功能规格 (Functional Specifications)

### 2.1. 主要流程 (Main Process Flow)

```mermaid
graph TD
    A[用户登录系统] --> B{导航至任务列表};
    B -- "点击侧边栏“タスク一覧”菜单" --> C[显示任务列表页面];
    C --> D{用户操作选择};
    D -- "查看列表" --> E["浏览任务信息<br/>(任务名, 状态, 时间等)"];
    D -- "执行筛选" --> F[输入筛选条件];
    F -- "点击搜索按钮" --> G[列表根据条件刷新];
    F -- "点击清除按钮" --> C;
    D -- "执行排序" --> H[点击列表头进行排序];
    H --> C;
    D -- "执行分页" --> I[点击分页控件];
    I --> C;
    D -- "点击“中止”按钮<br/>(仅对“実行待ち”状态任务)" --> J["发起任务中止请求<br/>(点击“中止”按钮)"];
    J -- "系统处理" --> K["更新任务状态<br/>(可能为“中止待ち”或提示无法中止)"];
    K --> C;
    D -- "点击“エラー詳細を表示”链接<br/>(对“エラー”状态任务)" --> L[显示错误详情弹窗];
    D -- "点击“ダウンロード”链接<br/>(对特定“正常終了”任务)" --> M[下载任务产出文件];
    D -- "点击右上角“更新”按钮" --> N[手动刷新任务列表];
    N --> C;

    subgraph "系统后台 (异步更新)"
        direction LR
        S1[Azure Functions 更新任务状态] -.-> S2(Task表);
    end
    S2 -.-> |数据源| C;
```
**图 2.1: 任务列表页面主要用户流程图 (高层抽象)**

### 2.2. 功能点描述 (Functional Points)

#### 2.2.1. 访问与进入
1.  用户成功登录门户系统后。
2.  用户通过点击侧边导航栏中的“タスク一覧”菜单项，即可导航并显示任务列表页面。

#### 2.2.2. 任务信息展示
1.  **数据范围**: 默认情况下，页面加载时展示该用户所属许可证 (`License.licenseId`) 下的所有用户发起的后台任务。
2.  **列表列项**: 任务列表以表格形式展示以下信息列：
    *   **タスク名 (任务名)**: 系统自动生成的任务名称，格式为 `{ServerName}-{タスク種別}-{YYYYMMDDHHmmss}`。
    *   **ステータス (状态)**: 任务的当前状态，显示为日文文本（如“実行待ち”, “実行中”, “正常終了”, “エラー”, “中止待ち”, “中止”）。其值来源于`Task.status`字段，并通过`LOV`表 (parentCode=`TASK_STATUS`) 映射。
    *   **開始日時 (开始时间)**: 任务的`Task.startedAt`时间。
    *   **終了日時 (结束时间)**: 任务的`Task.endedAt`时间（仅当任务达到终态时显示）。
    *   **サーバ名 (服务器名)**: 任务执行的目标服务器名称 (`Task.targetServerName`)。
    *   **タスク種別 (任务类型)**: 任务的业务类型，显示为日文文本（如“操作ログのエクスポート”, “管理項目定義のインポート”, “管理項目定義のエクスポート”）。其值来源于`Task.taskType`字段，并通过`LOV`表 (parentCode=`TASK_TYPE`) 映射。
    *   **実行ユーザー (执行用户)**: 发起任务的用户名 (`Task.submittedByUserId` 对应用户的显示名)。
    *   **タスク結果詳細 (任务结果详细)**: 根据任务状态和类型显示不同的信息或操作控件：
        *   **ステータス: `実行待ち` (`QUEUED`)**: 显示“中止”按钮。
        *   **ステータス: `実行中` (`RUNBOOK_SUBMITTED`, `RUNBOOK_PROCESSING`) 或 `中止待ち` (`PENDING_CANCELLATION`)**:
            *   通常显示为空白。
            *   若用户中止请求因超时未被处理，则显示消息 `EMET0006` (“タスクの中止がタイムアウトしました。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMET0006)”)。
            *   若用户尝试中止已开始的任务，则显示消息 `EMET0011` (“タスクの実行がすでに開始したため中止できませんでした。”)。
        *   **ステータス: `正常終了` (`COMPLETED_SUCCESS`)**:
            *   操作日志导出任务: 显示文本“操作ログ一覧画面で操作ログファイルをダウロードしてください。ログ名は{タスク名}\_{連番}となる。”。
            *   管理项目定义导入任务: 显示为空白。
            *   管理项目定义导出任务: 显示为可点击的“ダウンロード”链接。
        *   **ステータス: `エラー` (`COMPLETED_ERROR`)**: 显示为可点击的“エラー詳細を表示”链接。
        *   **ステータス: `中止` (`CANCELLED`)**: 显示文本 `EMET0004` (“ユーザーによってタスクが中止されました。”)。
3.  **数据刷新**: 任务列表默认显示页面加载或上次刷新时的数据。用户可以通过点击页面右上角的“更新”图标按钮手动刷新列表，获取最新的任务信息。

#### 2.2.3. 筛选功能
1.  页面顶部提供一个文本输入框（标签为“フィルター”）用于输入筛选关键词。
2.  提供一个“搜索”（放大镜图标）按钮和一个“清除”（叉号图标）按钮。
3.  用户在筛选框输入文本后，点击“搜索”按钮，任务列表将仅显示所有列中（`タスク名`, `ステータス`(日文显示值), `開始日時`(格式化后), `終了日時`(格式化后), `サーバ名`, `タスク種別`(日文显示值), `実行ユーザー`, `タスク結果詳細`(纯文本部分)) 至少有一项包含输入关键词（不区分大小写部分匹配）的任务记录。
4.  点击“清除”按钮，将清空筛选输入框的内容，并重置筛选条件，列表恢复显示所有符合当前分页和排序的任务。

#### 2.2.4. 排序功能
1.  任务列表的以下列标题支持点击排序：`タスク名`, `ステータス`, `開始日時`, `終了日時`, `サーバ名`, `タスク種別`, `実行ユーザー`。`タスク結果詳細`列不支持排序。
2.  **默认排序**: 页面初次加载时，任务列表默认按以下规则排序：
    *   第一排序条件: `開始日時` 降序 (最新的任务在前)。
    *   第二排序条件: `タスク名` 升序 (字典序 A-Z, 0-9)。
3.  **用户点击排序**:
    *   点击当前未参与排序的列标题，列表将按该列**升序**重新排序，并清除之前的排序条件。该列成为第一排序条件，第二排序条件固定为`タスク名`升序（除非第一排序条件已是`タスク名`）。
    *   点击当前已作为第一排序条件的列标题（带有▲▼指示符），则切换该列的排序方向（升序/降序）。第二排序条件（若有）保持不变。
    *   列标题旁应有视觉指示符（如▲▼图标）标明当前排序的列和方向。

#### 2.2.5. 分页功能
1.  列表底部提供分页控件。
2.  用户可以选择每页显示的行数，选项为 “10”, “30”, “50”。默认每页显示10行。
3.  分页控件显示总页数和当前页码，并提供前进、后退、跳转到指定页面的功能。

#### 2.2.6. 中止任务操作
1.  仅当任务的`ステータス`为“実行待ち” (`QUEUED`) 时，其在“タスク結果詳細”列会显示一个“中止”按钮。
2.  用户点击“中止”按钮：
    *   系统前端向后端发起中止任务的请求。
    *   **成功接收中止请求**: 向用户显示提示消息 `EMEC0026` (“タスクの中止を受け付けました。最新の状態はタスク一覧をご確認ください。\nタスク名：{0}”，其中{0}为任务名)。此时任务在界面上的状态（如果前端做了即时更新）或后端实际状态会变为“中止待ち” (`PENDING_CANCELLATION`)。
    *   **任务已开始无法中止**: 如果在用户点击“中止”按钮时，该任务的实际状态已不再是`QUEUED`（例如已变为`RUNBOOK_SUBMITTED`），则向用户显示提示消息 `EMEC0023` (“タスクの実行がすでに開始したため中止できませんでした。”)。
    *   **其他用户已中止**: 如果在用户点击“中止”按钮时，该任务已被其他用户中止（状态已为`PENDING_CANCELLATION`或`CANCELLED`），则仍向用户显示成功接收的提示消息 (`EMEC0026`)，但后端不会重复发送中止指令。
3.  中止操作的最终结果（任务是否成功变为“中止”状态）将通过后续的任务列表刷新反映出来。

#### 2.2.7. 查看错误详情
1.  当任务的`ステータス`为“エラー” (`COMPLETED_ERROR`) 时，其在“タスク結果詳細”列会显示一个“エラー詳細を表示”链接。
2.  用户点击此链接，系统应弹出一个模态对话框，显示该任务的详细错误信息（通常来源于`Task.resultDetails`字段，其中可能包含来自`error-messages.md`的消息）。

#### 2.2.8. 下载任务产出文件
1.  当任务的`ステータス`为“正常終了” (`COMPLETED_SUCCESS`) 且任务类型为“管理項目定義のエクスポート” (`TASK_TYPE.MGMT_ITEM_EXPORT`) 时，其在“タスク結果詳細”列会显示一个“ダウンロード”链接。
2.  用户点击此链接，浏览器应开始下载该任务导出的CSV文件（固定文件名为 `assetsfield_def.csv`）。下载路径来源于`Task.outputBlobPath`字段。

### 2.3. 业务规则 (Business Rules)
1.  用户只能看到其所属`licenseId`下的任务。
2.  任务名称 (`Task.taskName`) 由系统按 `{Server.name}-{TaskType.name}-{YYYYMMDDHHmmss}` 格式自动生成。
3.  任务状态的流转和含义严格遵循项目定义（见`docs/definitions/lov-definitions.md`中`TASK_STATUS`的定义）。
4.  “中止”操作仅对`ステータス`为“実行待ち”的任务有效。
5.  “管理項目定義のエクスポート”任务成功后，提供的下载文件名固定为 `assetsfield_def.csv`。
6.  “操作ログのエクスポート”任务成功后，提示用户去操作日志列表下载，文件名格式为 `{タスク名}_{連番}.zip`。
7.  列表项和筛选关键词匹配不区分大小写。

### 2.4. 前提条件 (Preconditions)
1.  用户必须已成功登录到门户系统。
2.  相关的后端服务（Azure Functions, Azure Service Bus, Azure SQL DB, Azure Automation, Azure Blob Storage）必须可用并正常运行，以保证任务数据的准确获取和任务操作的正确执行。
3.  数据库中的`Task`表，以及`Lov`表中关于`TASK_TYPE`和`TASK_STATUS`的定义必须正确配置。

### 2.5. 制约事项 (Constraints)
*   无特殊制约事项。

### 2.6. 注意事项 (Notes)
*   任务列表的实时性依赖于用户的刷新操作或页面设定的自动刷新机制（如果未来引入）。
*   后台任务的执行状态由Azure Functions (`TaskExecuteFunc`, `RunbookMonitorFunc`, `RunbookProcessorFunc`等) 负责更新到`Task`数据库表。本页面仅负责读取和展示这些数据。

### 2.7. 相关功能参考 (Related Functional References)
*   服务器列表 (发起任务的源头)
*   操作日志列表 (部分任务结果的查阅点)
*   `docs/definitions/lov-definitions.md` (任务类型、状态定义)
*   `docs/definitions/error-messages.md` (用户提示消息定义)
*   `docs/data-models/task.md` (Task表结构)
*   `docs/architecture/system-architecture.md` (整体架构和任务处理流程)

---

## 3. 技术设计与实现细节 (Technical Design & Implementation)

### 3.1. 技术栈与依赖 (Technology Stack & Dependencies)
*   **核心框架**: Next.js (v13.5.6+, App Router)。
*   **主要语言**: TypeScript。
*   **数据获取**:
    *   主要通过 React Server Components (RSC) 在服务端直接调用 `app/lib/data.ts` (别名 `ServerData`) 中的数据服务函数获取任务列表的主数据。
    *   特定的客户端交互（如发起任务中止）通过 Server Actions (`app/lib/actions.ts`) 实现。
*   **状态管理**:
    *   URL `searchParams` 用于管理列表的筛选条件、分页状态、排序状态，由服务端组件读取。
    *   客户端组件内部的瞬时状态（如筛选输入框的当前值、模态框的显示状态）通过 React Hooks (`useState`, `useReducer`) 管理。
*   **UI**:
    *   自定义React组件，大量使用 Tailwind CSS 进行样式定制。
    *   依赖的通用UI组件 (位于 `app/ui/` 目录): `Search.tsx` (筛选框与按钮), `Pagination.tsx` (分页控件), `PageSize.tsx` (每页行数选择), `Thead.tsx` (可排序表头), `MessageModal.tsx` (通用消息/确认/错误弹窗)。
*   **数据服务**: `app/lib/data.ts` (`ServerData`)，封装所有与 Prisma 和数据库 (`Task`, `Lov` 表等) 的交互逻辑。
    *   新增以下函数：
        *   `ServerData.fetchCachedTasks(licenseId: string, tz: string): Promise<TaskForList[]>`
        *   `ServerData.fetchTasksPages(filter: string, size: number, licenseId: string, tz: string, refresh?: boolean): Promise<number>`
        *   `ServerData.fetchFilteredTasks(filter: string, size: number, page: number, sort: string, order: string, licenseId: string, tz: string): Promise<TaskForList[]>`
        *   `ServerData.getTaskById(taskId: string): Promise<Task | null>`
*   **Server Actions**: `app/lib/actions.ts` (`ServerAction` 类)。
    *   新增以下Action：
        *   `ServerAction.requestTaskCancellation(taskId: string, currentUserId: string): Promise<TaskActionResult>`
*   **API Routes**:
    *   新增API Route用于处理文件下载: `app/dashboard/tasks/[taskId]/download/route.ts`。
*   **核心定义与常量**:
    *   `app/lib/definitions.ts`: 包含核心TypeScript类型定义 (如 `TaskForList`, `TaskActionResult`) 以及项目中使用的常量 (如 `MESSAGE_KEYS` 引用自 `docs/definitions/error-messages.md`, `TASK_STATUS` 内部代码常量等)。

### 3.2. 详细界面元素定义 (Detailed UI Element Definitions)

#### 3.2.1. 任务列表主页面 (`app/dashboard/tasks/page.tsx` 及其子组件)

| #  | 控件内部逻辑名(中文) | 界面显示文本(日文) | 控件类型 (HTML/React组件名) | 建议英文ID/key | 数据来源/状态 (前端state/props) | 主要行为/事件 (事件处理器名) | 校验规则/显示逻辑 | 格式/备注(中文) |
|----|-----------------|----------------|-----------------------------|----------------|---------------------------------|---------------------------|---------------|-------------|
| 1  | 筛选输入框        | (Placeholder: フィルター) | `app/ui/Search.tsx` | `taskFilterInput` | `inputValue` (内部state) / `searchParams.filter` (URL驱动) | `handleInputChange`, `handleSearchSubmit` |  | 用户输入筛选关键词。 |
| 2  | 搜索按钮          | (放大镜图标)     | `app/ui/Search.tsx` | `taskSearchButton` | - | `handleSearchSubmit` |  | 触发列表筛选。 |
| 3  | 清除筛选按钮      | (叉号图标)       | `app/ui/Search.tsx` | `taskClearFilterButton` | - | `handleClearFilter` |  | 清除筛选条件。 |
| 4  | 刷新按钮          | (刷新图标)       | `<button>` (自定义图标组件) | `taskRefreshButton` | - | `handleRefreshList` |  | 手动刷新任务列表。位于页面右上角。 |
| 5  | 任务表格          | -              | `<table>` (封装于 `app/ui/tasks/table.tsx`) | `tasksTable` | `tasksData` (props, 来自服务端获取) | - |  | 显示任务数据。 |
| 6  | 表头-任务名       | タスク名         | `app/ui/Thead.tsx` th | `th-taskName` | `sortConfig.key === 'taskName'` | `handleSort('taskName')` | 可排序 |  |
| 7  | 表头-状态         | ステータス       | `app/ui/Thead.tsx` th | `th-status` | `sortConfig.key === 'statusDisplay'` | `handleSort('statusDisplay')` | 可排序 |  |
| 8  | 表头-开始时间     | 開始日時         | `app/ui/Thead.tsx` th | `th-startedAt` | `sortConfig.key === '_originalStartedAt'` | `handleSort('_originalStartedAt')` | 可排序 | 默认降序 |
| 9  | 表头-结束时间     | 終了日時         | `app/ui/Thead.tsx` th | `th-endedAt` | `sortConfig.key === '_originalEndedAt'` | `handleSort('_originalEndedAt')` | 可排序 |  |
| 10 | 表头-服务器名     | サーバ名         | `app/ui/Thead.tsx` th | `th-targetServerName` | `sortConfig.key === 'targetServerName'` | `handleSort('targetServerName')` | 可排序 |  |
| 11 | 表头-任务类型     | タスク種別       | `app/ui/Thead.tsx` th | `th-taskType` | `sortConfig.key === 'taskTypeDisplay'` | `handleSort('taskTypeDisplay')` | 可排序 |  |
| 12 | 表头-执行用户     | 実行ユーザー     | `app/ui/Thead.tsx` th | `th-submittedByUserId` | `sortConfig.key === 'submittedByUserName'` | `handleSort('submittedByUserName')` | 可排序 |  |
| 13 | 表头-任务结果详细 | タスク結果詳細   | `<th>` (不可排序) | `th-resultDetails` | - | - | 不可排序 |  |
| 14 | 任务行-中止按钮   | 中止             | `<button>` | `cancelTaskButton-${taskId}` | - | `handleOpenCancelConfirmModal(taskId, taskName)` | 仅当`task.status`为`TASK_STATUS_QUEUED_CODE`时显示 |  |
| 15 | 任务行-下载链接   | ダウンロード     | `<a>` | `downloadTaskLink-${taskId}` | `task.outputBlobPath` (用于构建href) | (直接导航) | 仅当`task.status`为`TASK_STATUS_COMPLETED_SUCCESS_CODE`且`task.taskType`为`TASK_TYPE_MGMT_ITEM_EXPORT_CODE`时显示 |  |
| 16 | 任务行-错误详情链接 | エラー詳細を表示 | `<a>` (或 `<button>`) | `errorDetailsLink-${taskId}` | `task.resultDetails` (用于弹窗内容) | `handleOpenErrorModal(task.resultDetails)` | 仅当`task.status`为`TASK_STATUS_COMPLETED_ERROR_CODE`时显示 |  |
| 17 | 分页控件          | -              | `app/ui/Pagination.tsx` | `tasksPagination` | `currentPage`, `totalPages` (props) | `handlePageChange` (通过URL更新) |  |  |
| 18 | 每页行数选择      | 行数/ページ:     | `app/ui/PageSize.tsx` | `tasksPageSizeSelect` | `currentSize` (props) | `handlePageSizeChange` (通过URL更新) | 选项: 10, 30, 50 |  |

#### 3.2.2. 中止确认弹窗 (`app/ui/message-modal.tsx` 配置实例)

| #  | 控件内部逻辑名(中文) | 界面显示文本(日文) | 控件类型 (HTML/React组件名) | 建议英文ID/key | 数据来源/状态 (前端state/props) | 主要行为/事件 (事件处理器名) | 校验规则/显示逻辑 | 格式/备注(中文) |
|----|-----------------|----------------|-----------------------------|----------------|---------------------------------|---------------------------|---------------|-------------|
| 1  | 弹窗标题          | 確認             | (Props of `MessageModal`) | `cancelConfirmModalTitle` | "確認" (固定文本) | - | `isCancelConfirmModalOpen` 为 true 时显示 |  |
| 2  | 确认消息文本      | タスクを中止してもよろしいですか？<br/>タスク名: {taskName} | (Props of `MessageModal`) | `cancelConfirmModalMessage` | `currentTaskToCancel.name` (state) | - |  | `{taskName}` 为动态替换的任务名。 |
| 3  | OK按钮            | OK               | (Props of `MessageModal`) | `cancelConfirmModalOkButton` | - | `handleConfirmCancelTask` |  |  |
| 4  | 取消按钮          | キャンセル       | (Props of `MessageModal`) | `cancelConfirmModalCancelButton` | - | `handleCloseCancelConfirmModal` |  |  |
| 5  | 关闭图标按钮      | (叉号图标)       | (Props of `MessageModal`) | `cancelConfirmModalCloseIcon` | - | `handleCloseCancelConfirmModal` |  |  |

#### 3.2.3. 错误详情弹窗 (`app/ui/message-modal.tsx` 配置实例)

| #  | 控件内部逻辑名(中文) | 界面显示文本(日文) | 控件类型 (HTML/React组件名) | 建议英文ID/key | 数据来源/状态 (前端state/props) | 主要行为/事件 (事件处理器名) | 校验规则/显示逻辑 | 格式/备注(中文) |
|----|-----------------|----------------|-----------------------------|----------------|---------------------------------|---------------------------|---------------|-------------|
| 1  | 弹窗标题          | エラー詳細       | (Props of `MessageModal`) | `errorDetailModalTitle` | "エラー詳細" (固定文本) | - | `isErrorModalOpen` 为 true 时显示 |  |
| 2  | 错误图标          | (错误图标)       | (Props of `MessageModal`) | `errorDetailModalIcon` |  | - |  |  |
| 3  | 错误消息文本      | {errorMessage}   | (Props of `MessageModal`) | `errorDetailModalMessage` | `currentErrorMessage` (state) | - |  | `{errorMessage}` 为动态替换的错误文本。 |
| 4  | 关闭按钮          | 閉じる           | (Props of `MessageModal`) | `errorDetailModalCloseButton` | - | `handleCloseErrorModal` |  |  |
| 5  | 关闭图标按钮      | (叉号图标)       | (Props of `MessageModal`) | `errorDetailModalCloseIcon` | - | `handleCloseErrorModal` |  |  |

#### 3.2.4. 通用提示信息弹窗 (`app/ui/message-modal.tsx` 配置实例)

| #  | 控件内部逻辑名(中文) | 界面显示文本(日文) | 控件类型 (HTML/React组件名) | 建议英文ID/key | 数据来源/状态 (前端state/props) | 主要行为/事件 (事件处理器名) | 校验规则/显示逻辑 | 格式/备注(中文) |
|----|-----------------|----------------|-----------------------------|----------------|---------------------------------|---------------------------|---------------|-------------|
| 1  | 弹窗标题          | 情報 / エラー  | (Props of `MessageModal`) | `infoModalTitle` | `infoModalTitle` (state) | - | `isInfoModalOpen` 为 true 时显示 |  |
| 2  | 信息/错误图标     | (信息/错误图标)  | (Props of `MessageModal`) | `infoModalIcon` | 根据消息类型动态设置 | - |  |  |
| 3  | 消息文本          | {messageText}    | (Props of `MessageModal`) | `infoModalMessage` | `currentInfoMessage` (state) | - |  | `{messageText}` 为动态替换的文本。 |
| 4  | 关闭按钮          | 閉じる           | (Props of `MessageModal`) | `infoModalCloseButton` | - | `handleCloseInfoModal` |  |  |
| 5  | 关闭图标按钮      | (叉号图标)       | (Props of `MessageModal`) | `infoModalCloseIcon` | - | `handleCloseInfoModal` |  |  |

### 3.3. 详细事件处理逻辑 (Detailed Event Handling)

#### 3.3.1. 页面加载与初始化数据获取
*   **触发条件**: 用户导航至 `/dashboard/tasks`。
*   **前端处理步骤**:
    1.  Next.js App Router在服务端渲染`app/dashboard/tasks/page.tsx` (RSC)。
    2.  `page.tsx`解析URL `searchParams`获取`filter`, `page`, `size`, `sort`, `order`。
    3.  从用户会话获取`licenseId`和`tz`。
    4.  调用`ServerData.fetchTasksPages(filter, size, licenseId, tz, refreshFlag)`获取总页数。`refreshFlag`初始可为`false`或不传。
    5.  调用`ServerData.fetchFilteredTasks(filter, size, page, sort, order, licenseId, tz)`获取当前页任务数据。
    6.  将数据传递给客户端组件进行渲染。

#### 3.3.2. 筛选操作
*   **触发条件**: 用户在`taskFilterInput`输入后点击`taskSearchButton`或按回车。
*   **前端处理步骤 (`app/ui/Search.tsx` 或其父组件)**:
    1.  `handleSearchSubmit()`: 读取`taskFilterInput`的值。构建新的URL `searchParams`，设置`filter`为输入值，`page`为`1`。使用`router.push()`导航到新URL。
*   **触发条件**: 用户点击`taskClearFilterButton`。
*   **前端处理步骤**:
    1.  `handleClearFilter()`: 清空`taskFilterInput`。构建新的URL `searchParams`，移除`filter`，`page`为`1`。使用`router.push()`导航。

#### 3.3.3. 排序操作
*   **触发条件**: 用户点击可排序的列表头。
*   **前端处理步骤 (`app/ui/Thead.tsx` 或其父组件)**:
    1.  `handleSort(columnKey)`: 根据`columnKey`和当前排序状态计算新的`sort`键和`order`方向。构建新的URL `searchParams`，更新`sort`, `order`，`page`为`1`。使用`router.push()`导航。

#### 3.3.4. 分页操作
*   **触发条件**: 用户与`tasksPagination`或`tasksPageSizeSelect`交互。
*   **前端处理步骤 (`app/ui/Pagination.tsx`, `app/ui/PageSize.tsx` 或其父组件)**:
    1.  `handlePageChange(newPageNumber)`: 构建新的URL `searchParams`，更新`page`。使用`router.push()`导航。
    2.  `handlePageSizeChange(newPageSize)`: 构建新的URL `searchParams`，更新`size`，`page`为`1`。使用`router.push()`导航。

#### 3.3.5. 手动刷新列表
*   **触发条件**: 用户点击`taskRefreshButton`。
*   **前端处理步骤**:
    1.  `handleRefreshList()`: 调用Next.js `router.refresh()`。这将触发服务端RSC重新获取数据（`ServerData`层应通过`revalidateTag`使缓存失效）。

#### 3.3.6. 点击“中止”按钮
*   **触发条件**: 用户点击`cancelTaskButton-${taskId}`。
*   **前端处理步骤**:
    1.  `handleOpenCancelConfirmModal(taskId, taskName)`: 设置`currentTaskToCancel`, `isCancelConfirmModalOpen = true`。
    2.  `handleConfirmCancelTask()` (用户在确认弹窗点OK):
        a.  `isCancelConfirmModalOpen = false`, `isLoading = true`。
        b.  调用`ServerAction.requestTaskCancellation(currentTaskToCancel.id, currentUser.id)`。
        c.  根据`TaskActionResult`的`success`和`messageKey`设置`currentInfoMessage`, `infoModalTitle`, `isInfoModalOpen = true`。
        d.  如果`success`为`true`，调用`router.refresh()`。
        e.  `isLoading = false`。
    3.  `handleCloseCancelConfirmModal()`: `isCancelConfirmModalOpen = false`。

#### 3.3.7. 点击“エラー詳細を表示”链接
*   **触发条件**: 用户点击`errorDetailsLink-${taskId}`。
*   **前端处理步骤**:
    1.  `handleOpenErrorModal(errorMessage)`: 设置`currentErrorMessage = errorMessage`, `isErrorModalOpen = true`。
    2.  `handleCloseErrorModal()`: `isErrorModalOpen = false`。

#### 3.3.8. 点击“ダウンロード”链接
*   **触发条件**: 用户点击`downloadTaskLink-${taskId}`。
*   **前端处理步骤**: 无需额外JS。链接的`href`直接指向`/dashboard/tasks/${taskId}/download`。浏览器自动导航。

### 3.4. 数据结构与API/Server Action交互 (Data Structures and API/Server Action Interaction)

#### 3.4.1. 前端核心状态管理 (Client-side Core State Management)
*   `filterInputValue: string`
*   `isCancelConfirmModalOpen: boolean`
*   `currentTaskToCancel: { id: string, name: string } | null`
*   `isErrorModalOpen: boolean`
*   `currentErrorMessage: string | null`
*   `isInfoModalOpen: boolean`
*   `infoModalTitle: string`
*   `currentInfoMessage: { key: string, params?: Record<string, string> } | null`
*   `isLoading: boolean`

#### 3.4.2. 服务端数据获取函数 (in `app/lib/data.ts`)

##### *******. `ServerData.fetchCachedTasks(licenseId: string, tz: string): Promise<TaskForList[]>`
*   **职责**: 获取并缓存指定许可证下所有任务，并转换。
*   **输入**: `licenseId: string`, `tz: string`。
*   **返回值**: `Promise<TaskForList[]>`。
    *   `TaskForList` 类型定义 (`app/lib/definitions.ts`):
        ```typescript
        export type TaskForList = {
          id: string;
          taskName: string | null;
          status: string; // 内部状态码
          statusDisplay: string; // 日文显示状态
          startedAt: string | null; // 格式化后
          endedAt: string | null;   // 格式化后
          targetServerName: string | null;
          taskType: string; // 内部类型码
          taskTypeDisplay: string; // 日文显示类型
          submittedByUserId: string;
          submittedByUserName?: string; // (可选) 用户显示名
          resultDetails: string | null;
          outputBlobPath: string | null;
          _originalStartedAt: Date | null;
          _originalEndedAt: Date | null;
        };
        ```

##### *******. `ServerData.fetchTasksPages(filter: string, size: number, licenseId: string, tz: string, refresh?: boolean): Promise<number>`
*   **职责**: 基于缓存计算总页数。
*   **输入**: `filter: string`, `size: number`, `licenseId: string`, `tz: string`, `refresh?: boolean`。
*   **返回值**: `Promise<number>` (总页数)。

##### 3.4.2.3. `ServerData.fetchFilteredTasks(filter: string, size: number, page: number, sort: string, order: string, licenseId: string, tz: string): Promise<TaskForList[]>`
*   **职责**: 基于缓存进行内存筛选、排序、分页。
*   **输入**: `filter: string`, `size: number`, `page: number`, `sort: string` (对应`TaskForList`的键名), `order: string` (`asc`|`desc`), `licenseId: string`, `tz: string`。
*   **返回值**: `Promise<TaskForList[]>`。

##### 3.4.2.4. `ServerData.getTaskById(taskId: string): Promise<Task | null>`
*   **职责**: 获取单个任务原始记录。
*   **输入**: `taskId: string`。
*   **返回值**: `Promise<Task | null>`。

#### 3.4.3. Server Action (in `app/lib/actions.ts`)

##### *******. `ServerAction.requestTaskCancellation(taskId: string, currentUserId: string): Promise<TaskActionResult>`
*   **职责**: 处理任务中止请求。
*   **输入**: `taskId: string`, `currentUserId: string`。
*   **返回值**: `Promise<TaskActionResult>`。
    *   `TaskActionResult` 类型定义 (`app/lib/definitions.ts`):
        ```typescript
        export type TaskActionResult = {
          success: boolean;
          messageKey: string;
          messageParams?: Record<string, string>;
          updatedTaskStatus?: string;
        };
        ```

#### 3.4.4. API Route (文件下载)

##### *******. `GET /dashboard/tasks/[taskId]/download`
*   **路径**: `app/dashboard/tasks/[taskId]/download/route.ts`
*   **职责**: 为“管理项目定义导出”任务生成SAS下载链接并重定向。
*   **输入 (路径参数)**: `taskId: string`。
*   **核心逻辑**: 验证权限，获取`Task.outputBlobPath`，调用`ServerAction.generateBlobUrlWithSAS`，返回307重定向。

#### 3.4.5. 主要交互序列图 (Mermaid `sequenceDiagram`)

##### 场景1: 用户加载任务列表页面 (包含筛选/排序/分页)

```mermaid
sequenceDiagram
    actor User as 用户
    participant Client as "浏览器 (Next.js客户端)"
    participant AppServer as "Next.js应用服务器"
    participant Database as "Azure SQL Database"
    participant Cache as "Next.js Cache"

    User->>Client: 访问 /dashboard/tasks?filter=...&page=...
    Client->>AppServer: HTTP GET /dashboard/tasks (RSC 页请求)
    AppServer->>AppServer: (内部) 调用 app/lib/data.fetchTasksPages(filter, size, licenseId, tz, refresh)
    AppServer->>Cache: (data.ts内部) 检查缓存: fetchCachedTasks(licenseId, tz)
    alt 缓存未命中或refresh=true
        AppServer->>Database: (data.ts内部) Prisma: task.findMany + lov.findMany
        Database-->>AppServer: 原始数据 (Tasks, LOVs)
        AppServer->>AppServer: (data.ts内部) 数据转换与映射
        AppServer->>Cache: (data.ts内部) 存储转换后数据到缓存
        Cache-->>AppServer: TaskForList[] (来自缓存)
    else 缓存命中
        Cache-->>AppServer: TaskForList[] (来自缓存)
    end
    AppServer->>AppServer: (data.ts内部) 内存筛选 (计算总页数)
    AppServer-->>AppServer: (返回) totalPages
    
    AppServer->>AppServer: (内部) 调用 app/lib/data.fetchFilteredTasks(filter, size, page, sort, order, licenseId, tz)
    AppServer->>Cache: (data.ts内部) 检查缓存: fetchCachedTasks(licenseId, tz)
    Note over AppServer, Cache: (同上, 可能命中缓存)
    AppServer->>AppServer: (data.ts内部) 内存筛选、排序、分页
    AppServer-->>AppServer: (返回) tasksForCurrentPage: TaskForList[]

    AppServer-->>Client: HTML (RSC渲染结果, 含任务列表)
    Client->>User: 显示任务列表页面
```

##### 场景2: 用户点击“中止”按钮

```mermaid
sequenceDiagram
    actor User as 用户
    participant ClientUI as "浏览器 (任务列表React组件)"
    participant AppServer as "Next.js应用服务器"
    participant Database as "Azure SQL Database"
    participant ServiceBus as "Azure Service Bus (TaskControlQueue)"

    User->>ClientUI: 点击任务taskId1的“中止”按钮 (打开确认弹窗)
    User->>ClientUI: 在确认弹窗中点击“OK”
    ClientUI->>AppServer: 调用 ServerAction: requestTaskCancellation(taskId1, userId1)
    AppServer->>AppServer: (Action内部) 调用 app/lib/data.getTaskById(taskId1)
    AppServer->>Database: (data.ts内部) Prisma: task.findUnique()
    Database-->>AppServer: Task对象
    AppServer-->>AppServer: (返回) Task对象
    alt 任务状态为 QUEUED
        AppServer->>Database: (Action内部) Prisma: task.update(status='PENDING_CANCELLATION')
        Database-->>AppServer: 更新成功
        AppServer->>ServiceBus: (Action内部) 发送中止消息 {taskId: taskId1}
        ServiceBus-->>AppServer: 消息发送成功 (ack)
        AppServer-->>ClientUI: ServerAction响应: {success: true, messageKey: 'EMEC0026'}
    else 任务状态不为 QUEUED
        AppServer-->>ClientUI: ServerAction响应: {success: false, messageKey: 'EMEC0023'}
    end
    ClientUI->>ClientUI: 显示提示弹窗, 调用 router.refresh()
```

### 3.5. 数据库设计与访问详情 (Database Design and Access Details - 主要通过 `app/lib/data.ts` 间接访问)

#### 3.5.1. 相关表引用
*   **`Task` 表**: 存储所有后台任务的详细信息。
*   **`Lov` 表**: 存储任务类型 (`TASK_TYPE`) 和任务状态 (`TASK_STATUS`) 的代码及其对应的日文显示名称。

#### 3.5.2. 主要数据查询/变更逻辑 (由 `app/lib/data.ts` 中的函数实现)

##### 3.5.2.1. `ServerData.fetchCachedTasks(licenseId: string, tz: string)`
*   **职责**: 获取并缓存指定许可证下的所有任务数据，并进行初步转换。
*   **Prisma 操作**:
    1.  `prisma.task.findMany({ where: { licenseId }, orderBy: [{ startedAt: 'desc' }, { taskName: 'asc' }] })` 获取原始任务。
    2.  并行调用 `ServerData.fetchCachedLovList('TASK_STATUS')` 和 `ServerData.fetchCachedLovList('TASK_TYPE')` 获取LOV定义。
    3.  在代码中对原始任务数据进行遍历，映射状态和类型为日文显示文本，格式化日期。

##### 3.5.2.2. `ServerData.getTaskById(taskId: string)`
*   **Prisma 操作**: `prisma.task.findUnique({ where: { id: taskId } })`。

##### 3.5.2.3. `ServerAction.requestTaskCancellation` 中涉及的数据库操作
*   读取: 调用 `ServerData.getTaskById(taskId)`。
*   更新: 若符合条件，调用 `prisma.task.update({ where: { id: taskId }, data: { status: 'PENDING_CANCELLATION' } })`。

#### 3.5.3. 数据库层面性能考量
*   `Task` 表的 `licenseId` 字段应有索引。
*   `Lov` 表的 `code` 和 `parentCode` 字段应有索引。
*   全量加载与缓存策略适用于当前“列表总量不是太大”的预期。

### 3.6. 关键后端逻辑/算法 (Key Backend Logic/Algorithms - 主要在 `app/lib/actions.ts` 中)

#### 3.6.1. `ServerAction.requestTaskCancellation(taskId: string, currentUserId: string): Promise<TaskActionResult>`
**职责**: 处理用户发起的任务中止请求。
**详细处理流程**:
1.  记录日志。
2.  获取用户会话，获取`licenseId`。
3.  调用 `ServerData.getTaskById(taskId)` 获取任务。处理未找到任务或数据库错误。
4.  校验任务的`licenseId`与当前用户的`licenseId`是否匹配。不匹配则返回未授权错误。
5.  根据`task.status`：
    *   若为 `TASK_STATUS_QUEUED_CODE`:
        a.  更新`Task`表状态为 `TASK_STATUS_PENDING_CANCELLATION_CODE`。处理数据库更新错误。
        b.  发送消息 `{ taskId }` 到`TaskControlQueue`。处理消息发送错误并尝试回滚状态。
        c.  返回成功结果 `{ success: true, messageKey: 'EMEC0026', params: { '0': task.taskName }, updatedTaskStatus: TASK_STATUS_PENDING_CANCELLATION_CODE }`。
    *   若为 `TASK_STATUS_PENDING_CANCELLATION_CODE` 或 `TASK_STATUS_CANCELLED_CODE`: 返回成功结果 `{ success: true, messageKey: 'EMEC0026', params: { '0': task.taskName } }` (幂等处理)。
    *   若为其他运行中或已完成状态: 返回失败结果 `{ success: false, messageKey: 'EMEC0023' }`。
    *   未知状态: 返回通用失败结果 `{ success: false, messageKey: 'EMEC0019' }`。
6.  全局`try-catch`处理未知异常，返回通用失败结果。

### 3.7. 错误处理详情 (Detailed Error Handling)

| #  | 错误场景描述 (中文) | 触发位置 | 引用的消息ID/消息键 | 系统内部处理及日志记录建议 (中文) | 日志级别 |
|----|-----------------|----------|-----------------------|---------------------------------|-----------|
| 1  | 列表数据加载失败 (RSC) | `page.tsx` (服务端) | `EMEC0006` / `EMEC0007` | 服务端记录详细错误。Next.js渲染`error.tsx`。 | ERROR |
| 2  | Server Action调用网络失败 | 前端调用Server Action | `EMEC0019` | 前端显示错误弹窗。记录到浏览器控制台。 | ERROR |
| 3  | 中止任务 - Server Action内部通用错误 | `ServerAction.requestTaskCancellation` | `EMEC0019` (返回给前端) | 后端记录详细错误。前端显示错误弹窗。 | ERROR |
| 4  | 中止任务 - 任务已开始 | `ServerAction.requestTaskCancellation` | `EMEC0023` (返回给前端) | 后端记录提示。前端显示特定错误弹窗。 | INFO |
| 5  | 中止任务 - Service Bus消息发送失败 | `ServerAction.requestTaskCancellation` | `EMEC0019` (返回给前端) | 后端记录严重错误，尝试回滚状态。前端显示错误。 | CRITICAL |
| 6  | 下载文件 - API Route内部错误 | 下载API Route | `EMEC0007` (API Route响应) | 后端记录详细错误。浏览器显示文本错误或下载失败。 | ERROR |
| 7  | 用户会话失效/权限变更 | Server Action / API Route | `EMEC0005` / `EMEC0007` | 后端拒绝操作。前端引导重新登录。 | WARN/ERROR |

### 3.8. 配置项 (Configuration)

#### 3.8.1. 值列表 (LOV) 定义 (源自 `docs/definitions/lov-definitions.md`)
*   `TASK_STATUS`: 用于状态码与显示文本映射。
*   `TASK_TYPE`: 用于类型码与显示文本映射。
*   `AZURE_STORAGE.SAS_TTL_SECONDS`: 下载链接SAS Token有效时长。

#### 3.8.2. 环境变量 (源自 `docs/guides/environment-variables.md`)
*   `SERVICE_BUS_TASK_CONTROL_QUEUE_NAME`: 中止任务消息队列名。
*   `AZURE_STORAGE_CONTAINER_MANAGER_FILES_NAME`: 管理项目定义导出文件容器名。
*   `APP_CACHE_TTL_SECONDS`: 服务端数据缓存TTL。

#### 3.8.3. 错误与提示消息 (源自 `docs/definitions/error-messages.md`)
*   `EMEC0019`, `EMEC0023`, `EMEC0026`, `EMET0004`, `EMET0006`, `EMET0011`, `EMEC0007`, `EMEC0005`等。

### 3.9. 注意事项与其他 (Notes/Miscellaneous)
*   **数据缓存策略**: 当前全量缓存和内存操作策略依赖于任务总量不宜过大的前提。
*   **时区处理**: 所有显示时间均需按用户时区格式化。
*   **用户体验**: 提供加载指示器，考虑中止操作的乐观更新（当前未采用）。
*   **安全性**: 中止和下载操作均有基于`licenseId`的权限校验。
*   **日志记录**: 所有关键操作和流程节点均需详细日志。
