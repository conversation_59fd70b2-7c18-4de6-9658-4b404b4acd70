#!/usr/bin/env node

/**
 * 客户测试报告生成脚本
 * 基于test-cases.json和客户模板生成完整的单体测试报告
 */

const fs = require('fs');
const path = require('path');
const ExcelJS = require('exceljs');

// 配置
const CONFIG = {
  INPUT_JSON: 'docs-delivery/unit-test-report/test-cases.json',
  TEMPLATE_FILE: 'docs-delivery/unit-test-report/UT動作確認チェックリスト_template.xlsx',
  OUTPUT_FILE: 'docs-delivery/unit-test-report/UT動作確認チェックリスト_generated.xlsx',
  
  // 项目映射到章节 - 基于test-cases.json中的displayName
  PROJECT_MAPPING: {
    'jcs-endpoint-nextjs': {
      chapter: 2,
      title: 'ポータル',
      displayName: 'ポータル'
    },
    'jcs-backend-services-standard': {
      chapter: 3,
      title: 'タスク Function App',
      displayName: 'タスク Function App'
    },
    'jcs-backend-services-long-running': {
      chapter: 4,
      title: 'Runbook ジョブ Function App',
      displayName: 'Runbook ジョブ Function App'
    }
  }
};

/**
 * メイン処理
 */
async function main() {
  try {
    console.log('🚀 客户测试报告生成开始...');
    
    // 1. 读取测试用例数据
    console.log('📖 读取测试用例数据...');
    const testData = JSON.parse(fs.readFileSync(CONFIG.INPUT_JSON, 'utf8'));
    
    // 2. 加载模板
    console.log('📋 加载Excel模板...');
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(CONFIG.TEMPLATE_FILE);
    
    // 3. 生成报告
    console.log('⚙️ 生成测试报告...');
    await generateTestReport(workbook, testData);
    
    // 4. 保存文件
    console.log('💾 保存生成的报告...');
    await workbook.xlsx.writeFile(CONFIG.OUTPUT_FILE);
    
    console.log('✅ 客户测试报告生成完成！');
    console.log(`📄 输出文件: ${CONFIG.OUTPUT_FILE}`);
    
    // 5. 显示统计信息
    displayStatistics(testData);
    
  } catch (error) {
    console.error('❌ 生成失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

/**
 * 生成测试报告
 */
async function generateTestReport(workbook, testData) {
  // 1. 更新目次工作表
  await updateTableOfContents(workbook, testData);
  
  // 2. 更新统计信息工作表
  await updateStatisticsSheet(workbook, testData);
  
  // 3. 为每个大項目创建工作表
  await createMajorCategorySheets(workbook, testData);
}

/**
 * 更新目次工作表
 */
async function updateTableOfContents(workbook, testData) {
  const tocSheet = workbook.getWorksheet('目次');
  if (!tocSheet) {
    console.warn('⚠️ 未找到目次工作表');
    return;
  }
  
  console.log('📝 更新目次...');
  
  // 基于模板分析：第4行是2章，第6行是3章
  // 更新章节标题
  tocSheet.getCell('A4').value = '2章.ポータル';
  tocSheet.getCell('A6').value = '3章.タスク Function App';
  
  // 添加4章
  tocSheet.getCell('A8').value = '4章.Runbook ジョブ Function App';
  
  // 添加大項目子项
  let currentRow = 5;
  
  Object.entries(CONFIG.PROJECT_MAPPING).forEach(([projectKey, projectInfo]) => {
    const projectData = testData.testCases[projectKey];
    if (!projectData) return;
    
    let majorIndex = 1;
    Object.entries(projectData.categories).forEach(([categoryKey, categoryData]) => {
      const majorTitle = `${projectInfo.chapter}.${majorIndex} ${categoryData.displayName}`;
      tocSheet.getCell(`A${currentRow}`).value = majorTitle;
      currentRow++;
      majorIndex++;
    });
    
    currentRow++; // 跳过章节行，为下一章做准备
  });
}

/**
 * 更新统计信息工作表
 */
async function updateStatisticsSheet(workbook, testData) {
  const statsSheet = workbook.getWorksheet('1.試験観点・テスト結果集計シート');
  
  if (!statsSheet) {
    console.warn('⚠️ 未找到统计信息工作表');
    return;
  }
  
  console.log('📊 更新统计信息...');
  
  // 需要先分析统计工作表的实际结构
  // 暂时跳过，专注于工作表创建
}

/**
 * 为每个大項目创建工作表
 */
async function createMajorCategorySheets(workbook, testData) {
  console.log('📄 创建大項目工作表...');
  
  // 遍历所有项目
  Object.entries(CONFIG.PROJECT_MAPPING).forEach(([projectKey, projectInfo]) => {
    const projectData = testData.testCases[projectKey];
    if (!projectData) return;
    
    let majorIndex = 1;
    
    // 遍历大項目
    Object.entries(projectData.categories).forEach(([categoryKey, categoryData]) => {
      const sheetName = `${projectInfo.chapter}.${majorIndex}`;
      
      // 检查工作表是否已存在
      let worksheet = workbook.getWorksheet(sheetName);
      if (!worksheet) {
        // 创建新工作表
        worksheet = workbook.addWorksheet(sheetName);
      } else {
        // 清空现有工作表内容（保留格式）
        clearWorksheetContent(worksheet);
      }
      
      // 设置工作表结构
      setupMajorCategorySheet(worksheet, projectInfo, categoryData);
      
      // 收集该大項目下所有小項目的测试用例
      const allTestCases = [];
      Object.entries(categoryData.subCategories).forEach(([subCategoryKey, subCategoryData]) => {
        subCategoryData.testCases.forEach(testCase => {
          allTestCases.push({
            ...testCase,
            subCategory: subCategoryData.displayName
          });
        });
      });
      
      // 填充测试用例数据
      fillTestCases(worksheet, allTestCases);
      
      majorIndex++;
    });
  });
}

/**
 * 清空工作表内容但保留格式
 */
function clearWorksheetContent(worksheet) {
  // 清空从第4行开始的内容（保留标题和表头）
  const rowCount = worksheet.rowCount;
  for (let row = 4; row <= rowCount; row++) {
    for (let col = 1; col <= 10; col++) {
      const cell = worksheet.getCell(row, col);
      cell.value = null;
    }
  }
}

/**
 * 设置大項目工作表的基本结构
 */
function setupMajorCategorySheet(worksheet, projectInfo, categoryData) {
  // 基于模板分析：第1行B列是章节标题
  worksheet.getCell('B1').value = `${projectInfo.chapter}章.${projectInfo.title}`;
  
  // 第2行B-E列是工作表标识
  const sheetId = worksheet.name; // 直接使用工作表名称
  worksheet.getCell('B2').value = sheetId;
  worksheet.getCell('C2').value = sheetId;
  worksheet.getCell('D2').value = sheetId;
  worksheet.getCell('E2').value = sheetId;
  
  // 第3行B列开始是表头
  const headers = [
    '項番',
    '試験観点', 
    '試験対象',
    '試験手順',
    '確認項目',
    '実施日',
    '実施者',
    '再鑑者',
    '試験結果',
    '備考'
  ];
  
  headers.forEach((header, index) => {
    const cell = worksheet.getCell(3, index + 2); // 从B列开始
    cell.value = header;
    cell.font = { bold: true };
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };
    cell.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    };
  });
  
  // 设置列宽
  worksheet.getColumn(2).width = 8;   // 項番
  worksheet.getColumn(3).width = 30;  // 試験観点
  worksheet.getColumn(4).width = 25;  // 試験対象
  worksheet.getColumn(5).width = 40;  // 試験手順
  worksheet.getColumn(6).width = 40;  // 確認項目
  worksheet.getColumn(7).width = 12;  // 実施日
  worksheet.getColumn(8).width = 12;  // 実施者
  worksheet.getColumn(9).width = 12;  // 再鑑者
  worksheet.getColumn(10).width = 12; // 試験結果
  worksheet.getColumn(11).width = 20; // 備考
}

/**
 * 填充测试用例数据
 */
function fillTestCases(worksheet, testCases) {
  let currentRow = 4; // 从第4行开始（第3行是表头）
  
  testCases.forEach((testCase, index) => {
    // 項番 (B列)
    worksheet.getCell(currentRow, 2).value = index + 1;
    
    // 試験観点 (C列) - 使用テストケース名
    worksheet.getCell(currentRow, 3).value = testCase.テストケース名;
    
    // 試験対象 (D列)
    worksheet.getCell(currentRow, 4).value = testCase.試験対象;
    
    // 試験手順 (E列) - 处理换行符
    worksheet.getCell(currentRow, 5).value = testCase.試験手順.replace(/\\r\\n/g, '\n');
    
    // 確認項目 (F列) - 处理换行符
    worksheet.getCell(currentRow, 6).value = testCase.確認項目.replace(/\\r\\n/g, '\n');
    
    // 设置单元格样式
    for (let col = 2; col <= 11; col++) {
      const cell = worksheet.getCell(currentRow, col);
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      cell.alignment = { 
        vertical: 'top',
        wrapText: true 
      };
    }
    
    // 设置行高以适应内容
    const procedureLines = (testCase.試験手順.match(/\\r\\n/g) || []).length + 1;
    const confirmationLines = (testCase.確認項目.match(/\\r\\n/g) || []).length + 1;
    const maxLines = Math.max(procedureLines, confirmationLines, 1);
    worksheet.getRow(currentRow).height = Math.max(20, maxLines * 15);
    
    currentRow++;
  });
}

/**
 * 显示统计信息
 */
function displayStatistics(testData) {
  console.log('\n📈 生成统计信息:');
  console.log('=====================================');
  console.log(`总项目数: ${testData.statistics.totalProjects}`);
  console.log(`总大項目数: ${testData.statistics.totalMajorCategories}`);
  console.log(`总小項目数: ${testData.statistics.totalMinorCategories}`);
  console.log(`总测试用例数: ${testData.statistics.totalTestCases}`);
  console.log('');
  
  Object.entries(testData.statistics.byProject).forEach(([key, stats]) => {
    const projectInfo = CONFIG.PROJECT_MAPPING[key];
    console.log(`${projectInfo.chapter}章.${projectInfo.title} (${stats.displayName}):`);
    console.log(`  - 大項目数: ${stats.majorCategories}`);
    console.log(`  - 小項目数: ${stats.minorCategories}`);
    console.log(`  - 测试用例数: ${stats.testCases}`);
  });
  console.log('=====================================');
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  generateTestReport,
  CONFIG
};