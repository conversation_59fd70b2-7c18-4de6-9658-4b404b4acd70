/**
 * @file route.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

import { NotificationType } from "@/app/lib/definitions";
import { handleApiError } from "@/app/lib/portal-error";
import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";

// ユーザーに関連するお知らせの GET リクエストの処理
export async function GET(req: Request) {
  try {
    // 通知を最新順で取得
    const notifications = await prisma.notification.findMany({
      where: {
        type: NotificationType.SYSTEM,
      },
      orderBy: {
        publishedAt: "desc",
      },
    });

    // 取得した通知をJSONレスポンスで返す
    return NextResponse.json(notifications);
  } catch (error) {
    return handleApiError(error);
  }
}

export const revalidate = 0;
