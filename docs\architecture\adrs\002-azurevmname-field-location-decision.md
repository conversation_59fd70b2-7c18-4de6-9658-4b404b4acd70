# ADR-002: azureVmName 字段的归属决策

**状态 (Status)**: 已接受 (Accepted)

**决策者 (Deciders)**: chen

**决策日期 (Date)**: 2025-06-04

## 上下文 (Context)

在JCS端点资产与任务管理系统中，后台任务（由Azure Automation Runbook执行）需要在特定的目标Azure虚拟机（VM）上运行，这些VM上部署了Hybrid Runbook Worker (HRW) Agent和目标Docker容器。因此，系统在发起任务时，必须能够准确地确定该任务应该在哪个VM上执行。这个目标VM的标识信息（我们称之为`azureVmName`）的存储位置，对数据模型的清晰性和任务路由逻辑有直接影响。

最初的功能规格书 (`fs.v1.md`) 在描述数据库表时，其`[ライセンス] (License)`表（许可证表）的说明中曾提及“関連するAzure VM名などが含まれている”（包含关联的Azure VM名等信息）。然而，一个许可证（契约）理论上可能覆盖部署在多个不同Azure VM上的多个服务器实例（例如，同一个客户可能有多个JP1/ITDM2中继管理器分布在不同的VM上，但都属于同一个许可证合同）。如果`azureVmName`仅与`License`关联，那么当一个许可证下有多个VM时，将无法精确地将针对某个特定服务器实例（如UI上选择的某个“サーバ一覧”中的条目）的任务路由到其所在的具体VM。

另一方面，`fs.v1.md`在其最新的数据库表描述中，`[サーバ] (Server)`表（服务器表）的定义明确包含了“バックグラウンドタスクを実行するのに必要な**Azure VM名とDockerコンテナ名**などが含まれている”（包含执行后台任务所需的Azure VM名和Docker容器名等信息），并且补充说明“1つのサーバは1つのAzure VMに関連付けられる”（一个服务器关联一个Azure VM）。

这表明，将目标VM的标识直接与代表具体服务实例的`Server`记录关联，是更符合业务逻辑和系统实现需求的。

## 决策 (Decision)

我们决定将**`azureVmName`字段（用于标识后台任务执行的目标Azure虚拟机名称）明确地作为`Server`数据表的一个属性进行存储。**

相应的，`License`数据表将**不再包含**直接关联Azure VM名称的字段。一个`License`可以关联多个`Server`记录，而每个`Server`记录则通过其自身的`azureVmName`字段指明其运行所在的具体VM。

这意味着：
1.  当用户从门户界面选择一个特定的“服务器”并发起后台任务时，系统将直接从该“服务器”对应的`Server`表记录中获取其`azureVmName`和`dockerContainerName`。
2.  这两个信息（`azureVmName` 和 `dockerContainerName`）将被用于：
    *   在`ContainerConcurrencyStatus`表中进行并发锁定（其主键或唯一约束依赖于这两个值的组合）。
    *   传递给`TaskExecuteFunc`，并最终用于确定Azure Automation作业应在哪个Hybrid Runbook Worker Group（约定为`hrwg-<azureVmName>`）上执行，以及在目标VM上操作哪个Docker容器。

## 后果 (Consequences)

### 正面影响:

*   **数据模型更准确地反映业务现实**: 一个服务器实例（如一个Docker化的JP1/ITDM2管理器）必然运行在一个特定的VM上。将`azureVmName`放在`Server`表，直接体现了这种一对一或多对一（多个Server实例可以运行在同一个VM上，但每个Server实例只在一个VM上）的物理部署关系。
*   **任务路由逻辑简化且明确**: `TaskExecuteFunc`在确定任务的目标VM时，可以直接从传入的`targetServerId`对应的`Server`记录中读取`azureVmName`，无需再通过`License`表进行间接查找或依赖复杂的映射逻辑。
*   **更好地支持“单一许可证，多VM部署”场景**: 如果一个客户的许可证允许他们在多个不同的VM上部署多个受管服务器实例，此设计能够清晰地区分并定位到每一个实例。
*   **与最新的功能规格书 (`fs.v1.md`) 描述一致**: `fs.v1.md`在其最新的`[サーバ] (Server)`表定义中已明确包含Azure VM名。此决策确保了架构设计与功能规格在此关键数据点上的一致性。
*   **并发控制上下文清晰**: `ContainerConcurrencyStatus`表的锁定目标`(targetVmName, targetContainerName)`直接来源于同一个`Server`记录，逻辑更内聚。

### 负面影响与风险:

*   **数据冗余 (轻微)**: 如果多个`Server`记录碰巧运行在同一个Azure VM上，那么这个VM的名称（`azureVmName`）会在`Server`表中存储多次。但考虑到`Server`表的记录总数预计不会极其巨大，且`azureVmName`本身是字符串，这种程度的冗余通常是可以接受的，其带来的查询便利性和逻辑清晰性远大于存储开销。
*   **历史数据迁移 (如果旧系统存在)**: 如果系统是从一个`azureVmName`存储在`License`表的旧模型迁移而来，需要进行数据迁移和相关的代码逻辑调整。但对于新系统设计，此风险不存在。
*   **文档和代码的同步更新**: 需要确保所有相关的架构文档、数据模型定义（如Prisma Schema）、以及代码中获取`azureVmName`的逻辑都已更新以反映此变更。

## 备选方案 (Considered Options)

1.  **`azureVmName` 存储在 `License` 表**:
    *   **优点**: 如果一个许可证严格对应一个VM（早期可能存在的简化假设），此方案可行。
    *   **缺点**: 无法处理一个许可证下多个VM部署服务器实例的场景。任务路由到具体VM的逻辑不清晰或依赖外部约定。与`fs.v1.md`最新描述不符。

2.  **通过外部配置或HRW Group命名约定间接映射**:
    *   **优点**: 数据模型可能更简单（`Server`和`License`都不直接存VM名）。
    *   **缺点**: 任务目标VM的确定依赖于系统外部的配置或命名约定（例如，HRW Group名称中包含许可证ID或服务器标识），这使得系统逻辑不够内聚和透明，增加了配置管理的复杂性和出错风险。

**最终选择将`azureVmName`置于`Server`表，是基于其能够最清晰、最直接地支持系统核心功能（将任务路由到正确的VM和容器）并与功能规格书保持一致的考量。**

## 更多信息 (More Information)

*   此决策与`fs.v1.md`中对`[サーバ] (Server)`表和`[ライセンス] (License)`表的最新定义相符。
*   `TaskExecuteFunc` 和 `ContainerConcurrencyStatus` 表的设计将直接依赖此决策。

---
