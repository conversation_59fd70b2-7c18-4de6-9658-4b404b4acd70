/**
 * @fileoverview cleanup.ts の単体テストファイル
 * @description
 * タスク保持ポリシーに基づく古いタスクおよび関連ファイルの自動クリーンアップ処理の全分岐・例外処理を網羅的に検証する単体テストファイル。
 * 外部依存（Prisma、Azure Blob Storage等）をモックし、
 * 正常系・異常系・境界値の全パターンを自動テストで検証する。
 * 60%+コードカバレッジを目標とし、クリーンアップ処理の堅牢性を担保する。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

// 環境変数を最初に設定
process.env.AZURE_STORAGE_CONTAINER_ASSETSFIELD_DEF = "assets-container";
process.env.AZURE_STORAGE_CONTAINER_OPLOGS = "oplogs-container";

import { InvocationContext } from "@azure/functions";
import { AppConstants } from "../../lib/constants";

// モジュールをモック
jest.mock("../../lib/prisma", () => ({
  prisma: {
    lov: {
      findFirst: jest.fn(),
    },
    task: {
      count: jest.fn(),
      findMany: jest.fn(),
      delete: jest.fn(),
    },
    operationLog: {
      deleteMany: jest.fn(),
    },
  },
}));

jest.mock("../../lib/azureClients", () => ({
  createBlobServiceClient: jest.fn(),
}));

// テスト用のモック参照を取得
import { prisma } from "../../lib/prisma";
import { createBlobServiceClient } from "../../lib/azureClients";
import { cleanupOldTasks } from "../../lib/cleanup";

// モック関数の型アサーション
const mockPrisma = prisma as any;
const mockCreateBlobServiceClient = createBlobServiceClient as jest.MockedFunction<typeof createBlobServiceClient>;

// Azure Blob Service モック
const mockBlobServiceClient = {
  getContainerClient: jest.fn(),
};

const mockContainerClient = {
  listBlobsFlat: jest.fn(),
  deleteBlob: jest.fn(),
};

describe("cleanup 単体テスト", () => {
  let context: InvocationContext;

  beforeEach(() => {
    // InvocationContext のモックを作成
    context = {
      log: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
    } as any;

    // モックをクリア
    jest.clearAllMocks();

    // デフォルトのモック設定
    mockCreateBlobServiceClient.mockReturnValue(mockBlobServiceClient as any);
    mockBlobServiceClient.getContainerClient.mockReturnValue(mockContainerClient as any);
    mockContainerClient.listBlobsFlat.mockReturnValue([]);
    mockContainerClient.deleteBlob.mockResolvedValue(undefined);
  });

  // ========== 正常系テスト ==========

  /**
   * 試験観点：タスク件数が上限内の場合のクリーンアップ不要処理
   * 試験対象：cleanupOldTasks 関数
   * 試験手順：
   * 1. 最大保持件数を10件に設定
   * 2. 現在のタスク件数を5件に設定
   * 3. cleanupOldTasks を呼び出す
   * 確認項目：
   * - クリーンアップ不要のログが出力されること
   * - タスク削除処理が実行されないこと
   */
  it("正常系: タスク件数が上限内の場合、クリーンアップ不要で処理終了", async () => {
    const targetServerId = "server-001";

    // 最大保持件数を10件に設定
    mockPrisma.lov.findFirst.mockResolvedValue({
      code: AppConstants.TaskConfig.maxRetentionCount,
      value: "10",
    });

    // 現在のタスク件数を5件に設定
    mockPrisma.task.count.mockResolvedValue(5);

    await cleanupOldTasks(targetServerId, context);

    // 適切なログが出力されることを確認
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("クリーンアップ処理を開始する（サーバID: server-001）")
    );
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("最大保持件数は10件である")
    );
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("タスク件数（5件）は上限内であるため、クリーンアップ不要")
    );

    // タスク削除処理が実行されないことを確認
    expect(mockPrisma.task.findMany).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：削除対象タスクが存在しない場合の処理
   * 試験対象：cleanupOldTasks 関数
   * 試験手順：
   * 1. 最大保持件数を5件に設定
   * 2. 現在のタスク件数を10件に設定
   * 3. 削除対象タスクを0件に設定
   * 4. cleanupOldTasks を呼び出す
   * 確認項目：
   * - 削除対象タスクが存在しないログが出力されること
   * - 削除処理が実行されないこと
   */
  it("正常系: 削除対象タスクが存在しない場合、処理終了", async () => {
    const targetServerId = "server-002";

    // 最大保持件数を5件に設定
    mockPrisma.lov.findFirst.mockResolvedValue({
      code: AppConstants.TaskConfig.maxRetentionCount,
      value: "5",
    });

    // 現在のタスク件数を10件に設定
    mockPrisma.task.count.mockResolvedValue(10);

    // 削除対象タスクを0件に設定
    mockPrisma.task.findMany.mockResolvedValue([]);

    await cleanupOldTasks(targetServerId, context);

    // 適切なログが出力されることを確認
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("削除対象となる完了済みタスクは存在しない")
    );

    // 削除処理が実行されないことを確認
    expect(mockPrisma.operationLog.deleteMany).not.toHaveBeenCalled();
    expect(mockPrisma.task.delete).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：MgmtItemImportタスクの正常削除処理
   * 試験対象：cleanupOldTasks 関数
   * 試験手順：
   * 1. 最大保持件数を1件に設定
   * 2. 現在のタスク件数を3件に設定
   * 3. MgmtItemImportタスクを削除対象に設定
   * 4. cleanupOldTasks を呼び出す
   * 確認項目：
   * - 操作ログが削除されること
   * - Blobファイルが削除されること
   * - タスクレコードが削除されること
   */
  it("正常系: MgmtItemImportタスクの正常削除処理", async () => {
    const targetServerId = "server-003";
    const taskToDelete = {
      id: "task-001",
      licenseId: "license-001",
      taskType: "TASK_TYPE.MGMT_ITEM_IMPORT",
    };

    // 最大保持件数を1件に設定
    mockPrisma.lov.findFirst.mockResolvedValue({
      code: AppConstants.TaskConfig.maxRetentionCount,
      value: "1",
    });

    // 現在のタスク件数を3件に設定
    mockPrisma.task.count.mockResolvedValue(3);

    // 削除対象タスクを1件に設定
    mockPrisma.task.findMany.mockResolvedValue([taskToDelete]);

    // 操作ログ削除の成功をモック
    mockPrisma.operationLog.deleteMany.mockResolvedValue({ count: 2 });

    // Blobファイルの存在をモック（非同期イテレータとして）
    const mockBlobs = [
      { name: "license-001/imports/task-001/file1.csv" },
      { name: "license-001/imports/task-001/file2.csv" },
    ];

    // 非同期イテレータを正しくモック
    const asyncIterator = {
      async *[Symbol.asyncIterator]() {
        for (const blob of mockBlobs) {
          yield blob;
        }
      }
    };

    mockContainerClient.listBlobsFlat.mockReturnValue(asyncIterator);

    // タスク削除の成功をモック
    mockPrisma.task.delete.mockResolvedValue(taskToDelete);

    await cleanupOldTasks(targetServerId, context);

    // 操作ログ削除が実行されることを確認
    expect(mockPrisma.operationLog.deleteMany).toHaveBeenCalledWith({
      where: { generatedByTaskId: "task-001" },
    });

    // Blobコンテナクライアントが正しく取得されることを確認
    expect(mockBlobServiceClient.getContainerClient).toHaveBeenCalledWith("assets-container");

    // Blobファイル削除が実行されることを確認
    expect(mockContainerClient.deleteBlob).toHaveBeenCalledWith("license-001/imports/task-001/file1.csv");
    expect(mockContainerClient.deleteBlob).toHaveBeenCalledWith("license-001/imports/task-001/file2.csv");

    // タスクレコード削除が実行されることを確認
    expect(mockPrisma.task.delete).toHaveBeenCalledWith({
      where: { id: "task-001" },
    });

    // 適切なログが出力されることを確認
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("タスクID: task-001 の関連データ削除を開始する")
    );
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("タスクレコード（ID: task-001）の削除が完了した")
    );
  });

  /**
   * 試験観点：MgmtItemExportタスクの正常削除処理
   * 試験対象：cleanupOldTasks 関数
   * 試験手順：
   * 1. MgmtItemExportタスクを削除対象に設定
   * 2. cleanupOldTasks を呼び出す
   * 確認項目：
   * - 正しいBlobプレフィックスでファイル削除が実行されること
   * - assetsコンテナが使用されること
   */
  it("正常系: MgmtItemExportタスクの正常削除処理", async () => {
    const targetServerId = "server-004";
    const taskToDelete = {
      id: "task-002",
      licenseId: "license-002",
      taskType: "TASK_TYPE.MGMT_ITEM_EXPORT",
    };

    // 基本設定
    mockPrisma.lov.findFirst.mockResolvedValue({ value: "1" });
    mockPrisma.task.count.mockResolvedValue(2);
    mockPrisma.task.findMany.mockResolvedValue([taskToDelete]);
    mockPrisma.operationLog.deleteMany.mockResolvedValue({ count: 0 });
    mockPrisma.task.delete.mockResolvedValue(taskToDelete);

    // Blobファイルの存在をモック（非同期イテレータとして）
    const mockBlobs = [{ name: "license-002/exports/task-002/result.xlsx" }];

    const asyncIterator = {
      async *[Symbol.asyncIterator]() {
        for (const blob of mockBlobs) {
          yield blob;
        }
      }
    };

    mockContainerClient.listBlobsFlat.mockReturnValue(asyncIterator);

    await cleanupOldTasks(targetServerId, context);

    // 正しいコンテナが使用されることを確認
    expect(mockBlobServiceClient.getContainerClient).toHaveBeenCalledWith("assets-container");

    // 正しいプレフィックスでBlobリストが取得されることを確認
    expect(mockContainerClient.listBlobsFlat).toHaveBeenCalledWith({
      prefix: "license-002/exports/task-002/",
    });

    // Blobファイル削除が実行されることを確認
    expect(mockContainerClient.deleteBlob).toHaveBeenCalledWith("license-002/exports/task-002/result.xlsx");
  });

  /**
   * 試験観点：OpLogExportタスクの正常削除処理
   * 試験対象：cleanupOldTasks 関数
   * 試験手順：
   * 1. OpLogExportタスクを削除対象に設定
   * 2. cleanupOldTasks を呼び出す
   * 確認項目：
   * - 正しいBlobプレフィックスでファイル削除が実行されること
   * - oplogsコンテナが使用されること
   */
  it("正常系: OpLogExportタスクの正常削除処理", async () => {
    const targetServerId = "server-005";
    const taskToDelete = {
      id: "task-003",
      licenseId: "license-003",
      taskType: "TASK_TYPE.OPLOG_EXPORT",
    };

    // 基本設定
    mockPrisma.lov.findFirst.mockResolvedValue({ value: "1" });
    mockPrisma.task.count.mockResolvedValue(2);
    mockPrisma.task.findMany.mockResolvedValue([taskToDelete]);
    mockPrisma.operationLog.deleteMany.mockResolvedValue({ count: 1 });
    mockPrisma.task.delete.mockResolvedValue(taskToDelete);

    // Blobファイルの存在をモック（非同期イテレータとして）
    const mockBlobs = [{ name: "license-003/task-003/oplog.json" }];

    const asyncIterator = {
      async *[Symbol.asyncIterator]() {
        for (const blob of mockBlobs) {
          yield blob;
        }
      }
    };

    mockContainerClient.listBlobsFlat.mockReturnValue(asyncIterator);

    await cleanupOldTasks(targetServerId, context);

    // 正しいコンテナが使用されることを確認
    expect(mockBlobServiceClient.getContainerClient).toHaveBeenCalledWith("oplogs-container");

    // 正しいプレフィックスでBlobリストが取得されることを確認
    expect(mockContainerClient.listBlobsFlat).toHaveBeenCalledWith({
      prefix: "license-003/task-003/",
    });

    // Blobファイル削除が実行されることを確認
    expect(mockContainerClient.deleteBlob).toHaveBeenCalledWith("license-003/task-003/oplog.json");
  });

  /**
   * 試験観点：licenseIdが未設定のタスクのスキップ処理
   * 試験対象：cleanupOldTasks 関数
   * 試験手順：
   * 1. licenseIdが未設定のタスクを削除対象に設定
   * 2. cleanupOldTasks を呼び出す
   * 確認項目：
   * - Blobフォルダ削除がスキップされること
   * - 適切な警告ログが出力されること
   * - タスクレコードは削除されること
   */
  it("正常系: licenseId未設定タスクのBlobフォルダ削除スキップ処理", async () => {
    const targetServerId = "server-006";
    const taskToDelete = {
      id: "task-004",
      licenseId: null,
      taskType: "TASK_TYPE.MGMT_ITEM_IMPORT",
    };

    // 基本設定
    mockPrisma.lov.findFirst.mockResolvedValue({ value: "1" });
    mockPrisma.task.count.mockResolvedValue(2);
    mockPrisma.task.findMany.mockResolvedValue([taskToDelete]);
    mockPrisma.operationLog.deleteMany.mockResolvedValue({ count: 0 });
    mockPrisma.task.delete.mockResolvedValue(taskToDelete);

    await cleanupOldTasks(targetServerId, context);

    // Blobフォルダ削除スキップの警告ログが出力されることを確認
    expect(context.warn).toHaveBeenCalledWith(
      expect.stringContaining("タスク種別（TASK_TYPE.MGMT_ITEM_IMPORT）はBlobフォルダ削除不要、またはlicenseId未設定のためスキップする")
    );

    // Blobコンテナクライアントが呼ばれないことを確認
    expect(mockBlobServiceClient.getContainerClient).not.toHaveBeenCalled();

    // タスクレコードは削除されることを確認
    expect(mockPrisma.task.delete).toHaveBeenCalledWith({
      where: { id: "task-004" },
    });
  });

  /**
   * 試験観点：削除対象Blobファイルが存在しない場合の処理
   * 試験対象：cleanupOldTasks 関数
   * 試験手順：
   * 1. Blobファイルが存在しないタスクを削除対象に設定
   * 2. cleanupOldTasks を呼び出す
   * 確認項目：
   * - 削除対象ファイルが存在しないログが出力されること
   * - タスクレコードは削除されること
   */
  it("正常系: 削除対象Blobファイルが存在しない場合の処理", async () => {
    const targetServerId = "server-007";
    const taskToDelete = {
      id: "task-005",
      licenseId: "license-005",
      taskType: "TASK_TYPE.MGMT_ITEM_IMPORT",
    };

    // 基本設定
    mockPrisma.lov.findFirst.mockResolvedValue({ value: "1" });
    mockPrisma.task.count.mockResolvedValue(2);
    mockPrisma.task.findMany.mockResolvedValue([taskToDelete]);
    mockPrisma.operationLog.deleteMany.mockResolvedValue({ count: 0 });
    mockPrisma.task.delete.mockResolvedValue(taskToDelete);

    // Blobファイルが存在しない状況をモック（空の非同期イテレータ）
    const emptyAsyncIterator = {
      async *[Symbol.asyncIterator]() {
        // 空のイテレータ - 何もyieldしない
      }
    };

    mockContainerClient.listBlobsFlat.mockReturnValue(emptyAsyncIterator);

    await cleanupOldTasks(targetServerId, context);

    // 削除対象ファイルが存在しないログが出力されることを確認
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("license-005/imports/task-005/配下に削除対象ファイルは存在しない")
    );

    // Blobファイル削除が実行されないことを確認
    expect(mockContainerClient.deleteBlob).not.toHaveBeenCalled();

    // タスクレコードは削除されることを確認
    expect(mockPrisma.task.delete).toHaveBeenCalledWith({
      where: { id: "task-005" },
    });
  });

  // ========== 異常系テスト ==========

  /**
   * 試験観点：個別タスク削除処理でのエラーハンドリング
   * 試験対象：cleanupOldTasks 関数
   * 試験手順：
   * 1. 操作ログ削除でエラーが発生するようにモック
   * 2. cleanupOldTasks を呼び出す
   * 確認項目：
   * - エラーログが出力されること
   * - 処理が継続されること（他のタスクの処理に影響しない）
   */
  it("異常系: 個別タスク削除処理でエラーが発生した場合、エラーログ出力後に処理継続", async () => {
    const targetServerId = "server-008";
    const taskToDelete = {
      id: "task-006",
      licenseId: "license-006",
      taskType: "TASK_TYPE.MGMT_ITEM_IMPORT",
    };

    // 基本設定
    mockPrisma.lov.findFirst.mockResolvedValue({ value: "1" });
    mockPrisma.task.count.mockResolvedValue(2);
    mockPrisma.task.findMany.mockResolvedValue([taskToDelete]);

    // 操作ログ削除でエラーを発生させる
    mockPrisma.operationLog.deleteMany.mockRejectedValue(new Error("Database connection failed"));

    await cleanupOldTasks(targetServerId, context);

    // エラーログが出力されることを確認
    expect(context.error).toHaveBeenCalledWith(
      expect.stringContaining("タスクID: task-006 の削除処理に失敗した。エラー内容: Database connection failed。処理を継続する")
    );
  });

  /**
   * 試験観点：クリーンアップ処理全体での致命的エラーハンドリング
   * 試験対象：cleanupOldTasks 関数
   * 試験手順：
   * 1. 最大保持件数取得でエラーが発生するようにモック
   * 2. cleanupOldTasks を呼び出す
   * 確認項目：
   * - 致命的エラーログが出力されること
   * - 例外がスローされないこと
   */
  it("異常系: クリーンアップ処理全体で致命的エラーが発生した場合、エラーログ出力", async () => {
    const targetServerId = "server-009";

    // 最大保持件数取得でエラーを発生させる
    mockPrisma.lov.findFirst.mockRejectedValue(new Error("Database unavailable"));

    // 例外がスローされないことを確認
    await expect(cleanupOldTasks(targetServerId, context)).resolves.not.toThrow();

    // 致命的エラーログが出力されることを確認
    expect(context.error).toHaveBeenCalledWith(
      expect.stringContaining("サーバID: server-009 のクリーンアップ処理中に致命的なエラーが発生した。エラー内容: Database unavailable")
    );
  });

  // ========== 境界値・設定値テスト ==========

  /**
   * 試験観点：最大保持件数のデフォルト値処理
   * 試験対象：cleanupOldTasks 関数
   * 試験手順：
   * 1. 最大保持件数の設定が存在しない状況をモック
   * 2. cleanupOldTasks を呼び出す
   * 確認項目：
   * - デフォルト値（10件）が使用されること
   * - 適切なログが出力されること
   */
  it("境界値: 最大保持件数の設定が存在しない場合、デフォルト値10件が使用される", async () => {
    const targetServerId = "server-010";

    // 最大保持件数の設定が存在しない状況をモック
    mockPrisma.lov.findFirst.mockResolvedValue(null);

    // 現在のタスク件数を5件に設定（デフォルト値10件未満）
    mockPrisma.task.count.mockResolvedValue(5);

    await cleanupOldTasks(targetServerId, context);

    // デフォルト値が使用されることを確認
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("最大保持件数は10件である")
    );

    // クリーンアップ不要のログが出力されることを確認
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("タスク件数（5件）は上限内であるため、クリーンアップ不要")
    );
  });

  /**
   * 試験観点：複数タスクの一括削除処理
   * 試験対象：cleanupOldTasks 関数
   * 試験手順：
   * 1. 複数の削除対象タスクを設定
   * 2. cleanupOldTasks を呼び出す
   * 確認項目：
   * - 全てのタスクが順次処理されること
   * - 各タスクの削除ログが出力されること
   */
  it("境界値: 複数タスクの一括削除処理", async () => {
    const targetServerId = "server-011";
    const tasksToDelete = [
      {
        id: "task-007",
        licenseId: "license-007",
        taskType: "TASK_TYPE.MGMT_ITEM_IMPORT",
      },
      {
        id: "task-008",
        licenseId: "license-008",
        taskType: "TASK_TYPE.MGMT_ITEM_EXPORT",
      },
    ];

    // 基本設定
    mockPrisma.lov.findFirst.mockResolvedValue({ value: "1" });
    mockPrisma.task.count.mockResolvedValue(3);
    mockPrisma.task.findMany.mockResolvedValue(tasksToDelete);
    mockPrisma.operationLog.deleteMany.mockResolvedValue({ count: 0 });
    mockPrisma.task.delete.mockResolvedValue({});

    // Blobファイルが存在しない状況をモック（空の非同期イテレータ）
    const emptyAsyncIterator = {
      async *[Symbol.asyncIterator]() {
        // 空のイテレータ - 何もyieldしない
      }
    };

    mockContainerClient.listBlobsFlat.mockReturnValue(emptyAsyncIterator);

    await cleanupOldTasks(targetServerId, context);

    // 削除対象タスク数のログが出力されることを確認
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("削除対象タスクは2件である")
    );

    // 各タスクの削除開始ログが出力されることを確認
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("タスクID: task-007 の関連データ削除を開始する")
    );
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("タスクID: task-008 の関連データ削除を開始する")
    );

    // 各タスクの削除完了ログが出力されることを確認
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("タスクレコード（ID: task-007）の削除が完了した")
    );
    expect(context.log).toHaveBeenCalledWith(
      expect.stringContaining("タスクレコード（ID: task-008）の削除が完了した")
    );

    // 各タスクの削除処理が実行されることを確認
    expect(mockPrisma.task.delete).toHaveBeenCalledWith({ where: { id: "task-007" } });
    expect(mockPrisma.task.delete).toHaveBeenCalledWith({ where: { id: "task-008" } });
  });
});
