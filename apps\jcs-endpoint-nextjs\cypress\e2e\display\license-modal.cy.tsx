import fixture from "../../fixtures/License.json";

describe("初期化表示のテスト", () => {
  describe("ライセンス情報ダイアログ", () => {
    const validCredentials = {
      userId: "hitachi.taro.aa",
      password: "changeit!@#",
      licenseId: "hisol",
    };
    const end2023jpCredentials = {
      userId: "end2023jp.hanako.ab",
      password: "changeit!@#",
      licenseId: "end2023jp",
    };
    const end2023cnCredentials = {
      userId: "end2023cn.hanako.ab",
      password: "changeit!@#",
      licenseId: "end2023cn",
    };
    const maxclient0Credentials = {
      userId: "maxclient0.hanako.ab",
      password: "changeit!@#",
      licenseId: "maxclient0",
    };
    // @ts-ignore
    const getLicense = (licenseId) =>
      fixture.License.find((l) => l.licenseId === licenseId);

    it("ライセンス情報が正しく表示される", () => {
      const license = getLicense(validCredentials.licenseId);
      cy.visit("/login");
      cy.get("#userId").type(validCredentials.userId);
      cy.get("#password").type(validCredentials.password);
      cy.get("button").contains("ログイン").click();
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("ライセンス").click();
      cy.get(".h-40 > .w-full > tbody > :nth-child(1) > td.px-6").contains(
        "製品版",
      );
      cy.get(".h-40 > .w-full > tbody > :nth-child(2) > td.px-6").contains(
        // @ts-ignore
        new Date(license.expiredAt).toLocaleDateString(),
      );
      cy.get(".h-40 > .w-full > tbody > :nth-child(3) > td.px-6").contains(
        // @ts-ignore
        license?.maxClients,
      );
    });

    it("ライセンス保有数が0での表示", () => {
      const license = getLicense(maxclient0Credentials.licenseId);
      cy.visit("/login");
      cy.get("#userId").type(maxclient0Credentials.userId);
      cy.get("#password").type(maxclient0Credentials.password);
      cy.get("button").contains("ログイン").click();
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("ライセンス").click();
      cy.get(".h-40 > .w-full > tbody > :nth-child(1) > td.px-6").contains(
        "評価版",
      );

      cy.get(".h-40 > .w-full > tbody > :nth-child(3) > td.px-6").contains(0);
    });

    it("2023年末において、東京の時間帯で、有効期限の日付を正確に表示する", () => {
      const license = getLicense(end2023jpCredentials.licenseId);
      cy.visit("/login");
      cy.get("#userId").type(end2023jpCredentials.userId);
      cy.get("#password").type(end2023jpCredentials.password);
      cy.get("button").contains("ログイン").click();
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("ライセンス").click();
      cy.get(".h-40 > .w-full > tbody > :nth-child(1) > td.px-6").contains(
        "評価版",
      );
      cy.get(".h-40 > .w-full > tbody > :nth-child(2) > td.px-6").contains(
        "2023/12/31",
      );
      cy.get(".h-40 > .w-full > tbody > :nth-child(3) > td.px-6").contains(
        "9999999",
      );
    });

    it("2023年末において、北京の時間帯で、有効期限の日付を正確に表示する", () => {
      const license = getLicense(end2023cnCredentials.licenseId);
      cy.visit("/login");
      cy.get("#userId").type(end2023cnCredentials.userId);
      cy.get("#password").type(end2023cnCredentials.password);
      cy.get("button").contains("ログイン").click();
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("ライセンス").click();
      cy.get(".h-40 > .w-full > tbody > :nth-child(1) > td.px-6").contains(
        "評価版",
      );
      cy.get(".h-40 > .w-full > tbody > :nth-child(2) > td.px-6").contains(
        "2024/1/1",
      );
      cy.get(".h-40 > .w-full > tbody > :nth-child(3) > td.px-6").contains(
        "9999999",
      );
    });
  });
});
