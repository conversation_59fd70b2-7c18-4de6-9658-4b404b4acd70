/**
 * @file TaskCancellationFunc.test.ts
 * @description
 * TaskCancellationFunc の単体テストファイルです。
 * Jest を用いて、タスク中止関数の主要な分岐、例外処理、楽観ロック制御の動作を検証します。
 *
 * 【設計意図】
 * - タスク中止要求関数の主要な分岐・例外・楽観ロック制御の網羅的なテストを自動化。
 * - データベース等の外部依存をモックし、異常系も含めて堅牢性を担保。
 * - 100%のコードカバレッジを目指し、全ての分岐・例外処理を網羅的に検証。
 *
 * 【主なテストカバレッジ】
 * 1. メッセージ検証（null、非object、taskId不足・不正）
 * 2. Task取得（成功、失敗）
 * 3. Task状態判定（PENDING_CANCELLATION、その他）
 * 4. DB更新（成功、楽観ロック失敗、例外）
 * 5. 全体例外処理
 *
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { TaskCancellationFunc } from "../TaskCancellationFunc/TaskCancellationFunc";
import { prisma } from "../lib/prisma";
import { AppConstants } from "../lib/constants";
import { formatTaskErrorMessage } from "../lib/utils";

// Prisma のモック
jest.mock("../lib/prisma", () => {
  const { mockDeep } = require("jest-mock-extended");
  return { prisma: mockDeep() };
});

// Utils のモック
jest.mock("../lib/utils", () => ({
  formatTaskErrorMessage: jest.fn(),
}));

/**
 * @fileoverview TaskCancellationFunc関数の単体テスト。
 * @description Azure FunctionsのTaskCancellationFuncの主要分岐・例外・楽観ロック制御を網羅的に検証する。
 * 試験観点：タスク中止要求関数の正常系・異常系・楽観ロック制御の分岐網羅性、外部依存のモックによる堅牢性検証。
 * 試験対象：TaskCancellationFunc.ts（TaskCancellationFunc Azure Function本体）。
 */
describe("TaskCancellationFunc 単体テスト", () => {
  let context: any;

  beforeEach(() => {
    /**
     * Azure Functions の InvocationContext をモックし、必須フィールドを全て補完
     */
    context = {
      invocationId: "test-invoke",
      functionName: "TaskCancellationFunc",
      extraInputs: [],
      extraOutputs: [],
      bindingData: {},
      traceContext: {},
      log: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
    };

    jest.clearAllMocks();
  });

  /**
   * 試験観点：メッセージnull分岐の検証。
   * 試験対象：TaskCancellationFuncのメッセージ検証分岐。
   * 試験手順：
   * 1. messageがnullの場合をテスト。
   * 確認項目：
   * - context.errorに"メッセージが不正"が出力されること。
   * - 例外がthrowされること。
   */
  it("メッセージがnull: エラーthrow", async () => {
    await expect(TaskCancellationFunc(null, context)).rejects.toThrow("メッセージが不正");
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("メッセージが不正"));
  });

  /**
   * 試験観点：メッセージ型検証分岐の検証。
   * 試験対象：TaskCancellationFuncのメッセージ検証分岐。
   * 試験手順：
   * 1. messageが文字列（非object）の場合をテスト。
   * 確認項目：
   * - context.errorに"メッセージが不正"が出力されること。
   * - 例外がthrowされること。
   */
  it("メッセージが非object: エラーthrow", async () => {
    await expect(TaskCancellationFunc("invalid", context)).rejects.toThrow("メッセージが不正");
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("メッセージが不正"));
  });

  /**
   * 試験観点：taskId不足分岐の検証。
   * 試験対象：TaskCancellationFuncのtaskId検証分岐。
   * 試験手順：
   * 1. taskIdが不足している場合をテスト。
   * 確認項目：
   * - context.errorに"taskIdが不正"が出力されること。
   * - 例外がthrowされること。
   */
  it("taskId不足: エラーthrow", async () => {
    await expect(TaskCancellationFunc({}, context)).rejects.toThrow("taskIdが不正");
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("taskIdが不正"));
  });

  /**
   * 試験観点：taskId型検証分岐の検証。
   * 試験対象：TaskCancellationFuncのtaskId検証分岐。
   * 試験手順：
   * 1. taskIdが非文字列型の場合をテスト。
   * 確認項目：
   * - context.errorに"taskIdが不正"が出力されること。
   * - 例外がthrowされること。
   */
  it("taskId非文字列: エラーthrow", async () => {
    await expect(TaskCancellationFunc({ taskId: 123 }, context)).rejects.toThrow("taskIdが不正");
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("taskIdが不正"));
  });

  /**
   * 試験観点：taskId空文字列分岐の検証。
   * 試験対象：TaskCancellationFuncのtaskId検証分岐。
   * 試験手順：
   * 1. taskIdが空文字列の場合をテスト。
   * 確認項目：
   * - context.errorに"taskIdが不正"が出力されること。
   * - 例外がthrowされること。
   */
  it("taskId空文字列: エラーthrow", async () => {
    await expect(TaskCancellationFunc({ taskId: "" }, context)).rejects.toThrow("taskIdが不正");
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("taskIdが不正"));
  });

  /**
   * 試験観点：Task不存在分岐の検証。
   * 試験対象：TaskCancellationFuncのTask取得失敗分岐。
   * 試験手順：
   * 1. Taskが存在しない場合をテスト。
   * 確認項目：
   * - context.errorに"存在しない"が出力されること。
   * - 例外がthrowされること。
   */
  it("Task不存在: エラーthrow", async () => {
    (prisma.task.findUnique as any).mockResolvedValue(null);

    await expect(TaskCancellationFunc({ taskId: "notfound" }, context)).rejects.toThrow("中止対象タスクが存在しない");
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("存在しない"));
  });

  /**
   * 試験観点：ステータス不正分岐の検証。
   * 試験対象：TaskCancellationFuncのステータス判定分岐。
   * 試験手順：
   * 1. PENDING_CANCELLATION以外のステータスの場合をテスト。
   * 確認項目：
   * - context.errorに"PENDING_CANCELLATION ではない"が出力されること。
   * - 例外がthrowされること。
   */
  it("PENDING_CANCELLATION以外のステータス: エラーthrow", async () => {
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.RunbookSubmitted,
      updatedAt: new Date(),
    });

    await expect(TaskCancellationFunc({ taskId: "task1" }, context))
      .rejects.toThrow("タスクステータスが PENDING_CANCELLATION ではない");
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("PENDING_CANCELLATION ではない"));
  });

  /**
   * 試験観点：正常系フローの検証。
   * 試験対象：TaskCancellationFuncの正常系フロー。
   * 試験手順：
   * 1. PENDING_CANCELLATIONのタスクを正常に更新する場合をテスト。
   * 確認項目：
   * - prisma.task.updateManyが正しいパラメータで呼ばれること。
   * - context.logに成功メッセージが出力されること。
   */
  it("PENDING_CANCELLATION正常更新: 成功", async () => {
    const testDate = new Date();
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.PendingCancellation,
      updatedAt: testDate,
    });
    (prisma.task.updateMany as any).mockResolvedValue({ count: 1 });
    (formatTaskErrorMessage as any).mockReturnValue("中止されました");

    await TaskCancellationFunc({ taskId: "task1" }, context);

    expect(prisma.task.updateMany).toHaveBeenCalledWith({
      where: {
        id: "task1",
        updatedAt: testDate
      },
      data: {
        status: AppConstants.TaskStatus.Cancelled,
        resultMessage: "中止されました",
      },
    });
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("CANCELLED/EMET0004で更新完了"));
  });

  /**
   * 試験観点：楽観ロック失敗分岐の検証。
   * 試験対象：TaskCancellationFuncの楽観ロック制御分岐。
   * 試験手順：
   * 1. 楽観ロック失敗（0件更新）の場合をテスト。
   * 確認項目：
   * - context.errorに"楽観ロック失敗"が出力されること。
   * - 例外がthrowされること。
   */
  it("楽観ロック失敗: エラーthrow", async () => {
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.PendingCancellation,
      updatedAt: new Date(),
    });
    (prisma.task.updateMany as any).mockResolvedValue({ count: 0 }); // 楽観ロック失敗
    (formatTaskErrorMessage as any).mockReturnValue("中止されました");

    await expect(TaskCancellationFunc({ taskId: "task1" }, context))
      .rejects.toThrow("楽観ロック失敗：タスクが他プロセスにより変更された");
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("楽観ロック失敗"));
  });

  /**
   * 試験観点：DB更新例外分岐の検証。
   * 試験対象：TaskCancellationFuncのDB更新例外分岐。
   * 試験手順：
   * 1. DB更新で例外が発生する場合をテスト。
   * 確認項目：
   * - context.errorに"エラー発生"が出力されること。
   * - 例外がre-throwされること。
   */
  it("DB更新例外: エラーre-throw", async () => {
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.PendingCancellation,
      updatedAt: new Date(),
    });
    (prisma.task.updateMany as any).mockRejectedValue(new Error("DB更新失敗"));
    (formatTaskErrorMessage as any).mockReturnValue("中止されました");

    await expect(TaskCancellationFunc({ taskId: "task1" }, context))
      .rejects.toThrow("DB更新失敗");
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("エラー発生"), expect.any(Error));
  });

  /**
   * 試験観点：formatTaskErrorMessage例外分岐の検証。
   * 試験対象：TaskCancellationFuncのformatTaskErrorMessage例外分岐。
   * 試験手順：
   * 1. formatTaskErrorMessageで例外が発生する場合をテスト。
   * 確認項目：
   * - context.errorに"エラー発生"が出力されること。
   * - 例外がre-throwされること。
   */
  it("formatTaskErrorMessage例外: エラーre-throw", async () => {
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.PendingCancellation,
      updatedAt: new Date(),
    });
    (formatTaskErrorMessage as any).mockImplementation(() => {
      throw new Error("format error");
    });

    await expect(TaskCancellationFunc({ taskId: "task1" }, context))
      .rejects.toThrow("format error");
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("エラー発生"), expect.any(Error));
  });

  /**
   * 試験観点：Task取得例外分岐の検証。
   * 試験対象：TaskCancellationFuncのTask取得例外分岐。
   * 試験手順：
   * 1. Task取得で例外が発生する場合をテスト。
   * 確認項目：
   * - context.errorに"エラー発生"が出力されること。
   * - 例外がre-throwされること。
   */
  it("Task取得例外: エラーre-throw", async () => {
    (prisma.task.findUnique as any).mockRejectedValue(new Error("DB接続失敗"));

    await expect(TaskCancellationFunc({ taskId: "task1" }, context))
      .rejects.toThrow("DB接続失敗");
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("エラー発生"), expect.any(Error));
  });

  /**
   * 試験観点：正常系フローの網羅的検証。
   * 試験対象：TaskCancellationFuncの正常系フロー。
   * 試験手順：
   * 1. 正常系の完全なフローをテスト。
   * 確認項目：
   * - 全ての処理が正常に実行されること。
   * - 適切なログが出力されること。
   */
  it("正常系: 完全フロー", async () => {
    const testDate = new Date();
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.PendingCancellation,
      updatedAt: testDate,
    });
    (prisma.task.updateMany as any).mockResolvedValue({ count: 1 });
    (formatTaskErrorMessage as any).mockReturnValue("タスクが中止されました");

    await TaskCancellationFunc({ taskId: "task1" }, context);

    // 各ステップの確認
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("受信taskId: task1"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("CANCELLED/EMET0004で更新完了"));

    // API呼び出しの確認
    expect(prisma.task.findUnique).toHaveBeenCalledWith({
      where: { id: "task1" },
      select: { status: true, updatedAt: true }
    });
    expect(prisma.task.updateMany).toHaveBeenCalledWith({
      where: {
        id: "task1",
        updatedAt: testDate
      },
      data: {
        status: AppConstants.TaskStatus.Cancelled,
        resultMessage: "タスクが中止されました",
      },
    });
    expect(formatTaskErrorMessage).toHaveBeenCalledWith(AppConstants.ERROR_CODES.EMET0004);
  });
});