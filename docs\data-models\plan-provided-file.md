# 数据模型: 计划可用提供文件 (PlanProvidedFile)

*   **表名 (逻辑名)**: `PlanProvidedFile`
*   **对应UI界面**: N/A (主要由系统内部用于定义契约计划与提供文件之间的可用关系)
*   **主要用途**: 作为 `Plan` (契约计划信息) 表和 `ProvidedFile` (提供文件) 表之间的多对多关联（中间）表。它记录了特定契约计划下，哪些提供文件（由其业务唯一名称`name`标识）是可用的。

## 1. 字段定义

| 字段名 (Prisma/英文) | 数据类型 (SQL参考) | PK | FK | UQ | Nullable | Default  | 描述 (中文)                                                                                   |
| :------------------- | :----------------- | :--- | :--- | :--- | :------- | :------- | :-------------------------------------------------------------------------------------------- |
| `id`                 | VARCHAR(36)        | ●    |      |      |          | `cuid()` | 主键。CUID格式，由系统自动生成。                                                                  |
| `name`               | VARCHAR(255)       |      | ●    |      |          |          | **外键**。关联到 `ProvidedFile` 表的 `name` (唯一业务名称)。表示关联的具体提供文件。                    |
| `planId`             | VARCHAR(XX)        |      | ●    |      | Yes      |          | **可选外键**。关联到 `Plan` 表的 `planId` (业务唯一键)。表示此提供文件可用性条目属于哪个契约计划。         |

*PK: Primary Key, FK: Foreign Key, UQ: Unique Key*
*XX表示具体长度，取决于实际的数据库Schema定义。*

## 2. 关系

*   **对 `Plan` (`plan`)**: 可选的多对一关系。通过 `planId` 字段关联到 `Plan` 表的 `planId` 唯一键。一个提供文件可用性条目可以属于一个契约计划，或者在 `planId` 为 NULL 的情况下不直接属于任何特定计划（需确认此业务逻辑是否适用）。
*   **对 `ProvidedFile` (`providedFile`)**: 多对一关系。通过 `name` 字段关联到 `ProvidedFile` 表的 `name` 唯一键。

## 3. 索引

*   `PRIMARY KEY (id)`
*   `INDEX idx_planId_plan_provided_file (planId)` (Prisma Schema已定义，用于优化按计划ID的查询)
*   `INDEX idx_name_plan_provided_file (name)` (Prisma Schema已定义，用于优化按提供文件名称的查询，并支持外键约束)
*   (可选) `UNIQUE KEY UQ_PlanProvidedFile_Plan_Name (planId, name)` (如果业务要求一个计划下每个提供文件的关联是唯一的)