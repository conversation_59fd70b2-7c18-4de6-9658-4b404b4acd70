/**
 * @fileoverview タスクデータ生成器
 * @description 開発環境用の大量のタスクデータを生成する
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { PrismaClient } from '@prisma/client';
import { BaseGenerator } from './base.generator';

/**
 * タスクデータ生成器クラス
 * 様々なタイプと状態のタスクデータを大量に生成する
 */
export class TaskGenerator extends BaseGenerator {
  private readonly TASK_COUNT = 500; // 生成するタスク数
  private readonly BATCH_SIZE = 25;

  constructor(prisma: PrismaClient) {
    super(prisma, 'TaskGenerator');
  }

  /**
   * 生成器名を取得する
   * @returns 生成器名
   */
  getGeneratorName(): string {
    return 'タスクデータ';
  }

  /**
   * 生成予定のデータ数を取得する
   * @returns 生成予定数
   */
  getExpectedCount(): number {
    return this.TASK_COUNT;
  }

  /**
   * 既存のタスクデータをクリーンアップする
   * 関連するデータも削除する
   */
  async cleanup(): Promise<void> {
    try {
      // 関連する操作ログを先に削除
      await this.prisma.operationLog.deleteMany({});
      
      // タスクデータを削除
      const deleteResult = await this.prisma.task.deleteMany({});
      console.log(`既存タスクデータを削除しました (${deleteResult.count}件)`);
    } catch (error) {
      console.error('タスクデータのクリーンアップに失敗しました', error);
      throw error;
    }
  }

  /**
   * タスクデータを生成する
   * 様々なタイプと状態のタスクを大量に生成する
   * @returns 生成されたデータの数
   */
  async generate(): Promise<number> {
    try {
      // 必要なマスターデータを取得
      const [servers, taskTypes, taskStatuses] = await Promise.all([
        this.getAvailableServers(),
        this.getTaskTypes(),
        this.getTaskStatuses(),
      ]);

      if (servers.length === 0) {
        throw new Error('利用可能なサーバーが見つかりません。先にサーバーデータを生成してください。');
      }

      console.log(
        `${servers.length}台のサーバー、${taskTypes.length}種類のタスクタイプ、${taskStatuses.length}種類のステータスを使用します`
      );

      // バッチでタスクデータを生成
      return await this.generateInBatches(
        this.TASK_COUNT,
        this.BATCH_SIZE,
        async (startIndex: number, count: number) => {
          return await this.generateTaskBatch(startIndex, count, servers, taskTypes, taskStatuses);
        }
      );
    } catch (error) {
      console.error('タスクデータ生成中にエラーが発生しました', error);
      throw error;
    }
  }

  /**
   * タスクデータのバッチを生成する
   * @param startIndex 開始インデックス
   * @param count 生成数
   * @param servers 利用可能なサーバー
   * @param taskTypes タスクタイプ
   * @param taskStatuses タスクステータス
   * @returns 生成されたタスクデータ
   */
  private async generateTaskBatch(
    startIndex: number,
    count: number,
    servers: any[],
    taskTypes: any[],
    taskStatuses: any[]
  ): Promise<any[]> {
    const tasks = [];

    for (let i = 0; i < count; i++) {
      const server = servers[Math.floor(Math.random() * servers.length)];
      const taskType = taskTypes[Math.floor(Math.random() * taskTypes.length)];
      const taskStatus = this.selectTaskStatus(taskStatuses);
      
      const submittedAt = this.faker.pastDate(90); // 過去90日以内
      const task = {
        taskName: this.generateTaskName(taskType.value),
        taskType: taskType.code,
        status: taskStatus.code,
        submittedAt,
        startedAt: this.generateStartedAt(submittedAt, taskStatus.code),
        endedAt: this.generateEndedAt(submittedAt, taskStatus.code),
        submittedByUserId: this.generateUserId(),
        licenseId: server.licenseId,
        targetServerId: server.id,
        targetServerName: server.name,
        targetContainerName: server.dockerContainerName,
        targetHRWGroupName: server.hrwGroupName,
        targetVmName: server.azureVmName,
        parametersJson: this.generateParametersJson(taskType.code),
        resultMessage: this.generateResultMessage(taskStatus.code),
        errorMessage: this.generateErrorMessage(taskStatus.code),
        errorCode: this.generateErrorCode(taskStatus.code),
      };

      tasks.push(task);
    }

    // バッチでデータベースに挿入
    await this.prisma.task.createMany({
      data: tasks,
    });

    return tasks;
  }

  /**
   * タスク名を生成する
   * @param taskTypeValue タスクタイプの値
   * @returns タスク名
   */
  private generateTaskName(taskTypeValue: string): string {
    const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const randomSuffix = this.faker.randomInt(1000, 9999);
    return `${taskTypeValue}_${timestamp}_${randomSuffix}`;
  }

  /**
   * タスクステータスを重み付きで選択する
   * @param taskStatuses 利用可能なタスクステータス
   * @returns 選択されたタスクステータス
   */
  private selectTaskStatus(taskStatuses: any[]): any {
    // 重み付き選択（正常終了が多く、エラーは少なく）
    const weights = taskStatuses.map(status => {
      if (status.code.includes('COMPLETED_SUCCESS')) return 50;
      if (status.code.includes('QUEUED')) return 20;
      if (status.code.includes('PROCESSING') || status.code.includes('SUBMITTED')) return 15;
      if (status.code.includes('COMPLETED_ERROR')) return 10;
      if (status.code.includes('CANCELLED')) return 5;
      return 1;
    });

    return this.weightedRandomChoice(taskStatuses, weights);
  }

  /**
   * 開始時刻を生成する
   * @param submittedAt 投入時刻
   * @param status タスクステータス
   * @returns 開始時刻またはnull
   */
  private generateStartedAt(submittedAt: Date, status: string): Date | null {
    if (status.includes('QUEUED')) {
      return null; // 実行待ちは開始時刻なし
    }
    
    // 投入から数分後に開始
    const startDelay = this.faker.randomInt(1, 30) * 60 * 1000; // 1-30分
    return new Date(submittedAt.getTime() + startDelay);
  }

  /**
   * 終了時刻を生成する
   * @param submittedAt 投入時刻
   * @param status タスクステータス
   * @returns 終了時刻またはnull
   */
  private generateEndedAt(submittedAt: Date, status: string): Date | null {
    if (status.includes('QUEUED') || status.includes('PROCESSING') || status.includes('SUBMITTED')) {
      return null; // 実行中は終了時刻なし
    }
    
    // 投入から数時間後に終了
    const endDelay = this.faker.randomInt(30, 240) * 60 * 1000; // 30分-4時間
    return new Date(submittedAt.getTime() + endDelay);
  }

  /**
   * ユーザーIDを生成する
   * @returns ユーザーID
   */
  private generateUserId(): string {
    const userIds = [
      'admin', 'operator1', 'operator2', 'manager1', 'manager2',
      'dev-user1', 'dev-user2', 'test-user1', 'test-user2', 'system'
    ];
    return userIds[Math.floor(Math.random() * userIds.length)];
  }

  /**
   * パラメータJSONを生成する
   * @param taskType タスクタイプ
   * @returns パラメータJSON文字列
   */
  private generateParametersJson(taskType: string): string {
    const baseParams = {
      executedBy: this.generateUserId(),
      priority: this.faker.randomInt(1, 5),
      timeout: this.faker.randomInt(300, 3600),
    };

    if (taskType.includes('OPLOG_EXPORT')) {
      return JSON.stringify({
        ...baseParams,
        startDate: this.faker.pastDate(30).toISOString(),
        endDate: new Date().toISOString(),
        format: 'CSV',
        compression: true,
      });
    } else if (taskType.includes('MGMT_ITEM_EXPORT')) {
      return JSON.stringify({
        ...baseParams,
        includeHistory: true,
        format: 'EXCEL',
        categories: ['SERVER', 'APPLICATION', 'DATABASE'],
      });
    } else if (taskType.includes('MGMT_ITEM_IMPORT')) {
      return JSON.stringify({
        ...baseParams,
        fileName: `import_${Date.now()}.xlsx`,
        validateOnly: false,
        overwriteExisting: true,
      });
    }

    return JSON.stringify(baseParams);
  }

  /**
   * 結果メッセージを生成する
   * @param status タスクステータス
   * @returns 結果メッセージまたはnull
   */
  private generateResultMessage(status: string): string | null {
    if (status.includes('COMPLETED_SUCCESS')) {
      const messages = [
        'タスクが正常に完了しました。',
        'エクスポート処理が成功しました。ファイルサイズ: 2.5MB',
        'データ処理が完了しました。処理件数: 1,234件',
        'バックアップ処理が正常に終了しました。',
        'システム更新が成功しました。',
      ];
      return messages[Math.floor(Math.random() * messages.length)];
    }
    
    return null;
  }

  /**
   * エラーメッセージを生成する
   * @param status タスクステータス
   * @returns エラーメッセージまたはnull
   */
  private generateErrorMessage(status: string): string | null {
    if (status.includes('COMPLETED_ERROR')) {
      const messages = [
        'データベース接続エラーが発生しました。',
        'ファイルアクセス権限が不足しています。',
        'メモリ不足のため処理を中断しました。',
        'ネットワークタイムアウトが発生しました。',
        '必要なファイルが見つかりません。',
      ];
      return messages[Math.floor(Math.random() * messages.length)];
    }
    
    return null;
  }

  /**
   * エラーコードを生成する
   * @param status タスクステータス
   * @returns エラーコードまたはnull
   */
  private generateErrorCode(status: string): string | null {
    if (status.includes('COMPLETED_ERROR')) {
      const codes = ['E001', 'E002', 'E003', 'E404', 'E500', 'E503', 'E999'];
      return codes[Math.floor(Math.random() * codes.length)];
    }
    
    return null;
  }

  /**
   * 利用可能なサーバーを取得する
   * @returns サーバーの配列
   */
  private async getAvailableServers(): Promise<any[]> {
    return await this.prisma.server.findMany({
      select: {
        id: true,
        name: true,
        licenseId: true,
        azureVmName: true,
        dockerContainerName: true,
        hrwGroupName: true,
      },
    });
  }

  /**
   * タスクタイプのLOVデータを取得する
   * @returns タスクタイプの配列
   */
  private async getTaskTypes(): Promise<any[]> {
    return await this.prisma.lov.findMany({
      where: {
        parentCode: 'TASK_TYPE',
        isEnabled: true,
      },
      select: {
        code: true,
        name: true,
        value: true,
      },
    });
  }

  /**
   * タスクステータスのLOVデータを取得する
   * @returns タスクステータスの配列
   */
  private async getTaskStatuses(): Promise<any[]> {
    return await this.prisma.lov.findMany({
      where: {
        parentCode: 'TASK_STATUS',
        isEnabled: true,
      },
      select: {
        code: true,
        name: true,
        value: true,
      },
    });
  }
}
