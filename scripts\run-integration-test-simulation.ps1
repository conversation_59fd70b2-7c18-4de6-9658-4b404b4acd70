# run-integration-test-simulation.ps1
#
# このスクリプトは、RunbookProcessorFuncの統合テスト用に、
# 3種類のタスク（操作ログエクスポート、管理項目定義エクスポート、管理項目定義インポート）の
# 出力ファイルをシミュレートします。
#
# 使用方法:
# .\run-integration-test-simulation.ps1
#

# 挂载的Azure Files路径（Z盘）
$baseWorkspacePath = "Z:\"

# 既存のタスクディレクトリがあれば削除
$taskDirs = Get-ChildItem -Path $baseWorkspacePath -Directory -ErrorAction SilentlyContinue
foreach ($dir in $taskDirs) {
    Write-Host "ディレクトリ $($dir.FullName) を削除中..."
    Remove-Item -Path $dir.FullName -Recurse -Force
    Write-Host "削除完了"
}

# タスクIDを生成（現在時刻をベースにしたユニークなID）
$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$oplogTaskId = "oplog-$timestamp"
$exportTaskId = "export-$timestamp"
$importTaskId = "import-$timestamp"

Write-Host "===== 統合テスト用Runbook出力シミュレーション開始 ====="
Write-Host ""

# 1. 操作ログエクスポートタスク
Write-Host "1. 操作ログエクスポートタスクのシミュレーション"
Write-Host "   タスクID: $oplogTaskId"
& .\simulate-runbook-execution.ps1 -taskId $oplogTaskId -targetContainerName "container1" -exportStartDate "2025-01-01" -exportEndDate "2025-01-31"
Write-Host ""

# 2. 管理項目定義エクスポートタスク
Write-Host "2. 管理項目定義エクスポートタスクのシミュレーション"
Write-Host "   タスクID: $exportTaskId"
& .\simulate-runbook-execution.ps1 -taskId $exportTaskId -targetContainerName "container2"
Write-Host ""

# 3. 管理項目定義インポートタスク - インポートファイルの準備
Write-Host "3. 管理項目定義インポートタスクのシミュレーション"
Write-Host "   タスクID: $importTaskId"

# インポートファイルのディレクトリを作成
$importTaskPath = Join-Path -Path $baseWorkspacePath -ChildPath $importTaskId
$importTaskImportsPath = Join-Path -Path $importTaskPath -ChildPath "imports"
New-Item -Path $importTaskImportsPath -ItemType Directory -Force | Out-Null

# インポートファイルを作成
$importFilePath = Join-Path -Path $importTaskImportsPath -ChildPath "assetsfield_def.csv"
$importFileContent = @"
"項目ID","項目名","データ型","必須","デフォルト値","備考"
"server_id","サーバーID","string","true","","サーバーの一意識別子"
"server_name","サーバー名","string","true","","サーバーの表示名"
"ip_address","IPアドレス","string","true","","サーバーのIPアドレス"
"os_type","OS種別","string","true","Windows","Windows/Linux"
"cpu_cores","CPUコア数","number","true","4","物理CPUコア数"
"memory_gb","メモリ容量(GB)","number","true","16","物理メモリ容量"
"disk_gb","ディスク容量(GB)","number","true","500","ディスク総容量"
"status","ステータス","string","true","active","active/inactive/maintenance"
"created_at","作成日時","datetime","true","","YYYY-MM-DD HH:MM:SS形式"
"updated_at","更新日時","datetime","true","","YYYY-MM-DD HH:MM:SS形式"
"@
Set-Content -Path $importFilePath -Value $importFileContent -Encoding UTF8
Write-Host "   インポートファイル assetsfield_def.csv を作成しました。"

# インポートタスクを実行
& .\simulate-runbook-execution.ps1 -taskId $importTaskId -targetContainerName "container3" -importedFileBlobPath "license123/imports/$importTaskId/assetsfield_def.csv"
Write-Host ""

Write-Host "===== シミュレーション完了 ====="
Write-Host ""
Write-Host "生成されたディレクトリ構造:"
Write-Host "Z:\"
Write-Host "├── $oplogTaskId/    # 操作ログエクスポートタスク"
Write-Host "│   ├── exports/     # 操作ログZIPファイル"
Write-Host "│   └── imports/"
Write-Host "├── $exportTaskId/    # 管理項目定義エクスポートタスク"
Write-Host "│   ├── exports/     # assetsfield_def.csv"
Write-Host "│   └── imports/"
Write-Host "└── $importTaskId/    # 管理項目定義インポートタスク"
Write-Host "    ├── exports/     # 成功マーカーファイル"
Write-Host "    └── imports/     # インポート用assetsfield_def.csv"
Write-Host ""
Write-Host "これらのファイルを使用して、RunbookProcessorFuncの統合テストを実行できます。"
Write-Host "各タスクIDをテストパラメータとして使用してください。"