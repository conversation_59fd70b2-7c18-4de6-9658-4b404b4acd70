#!/usr/bin/env node

/**
 * 缺陷分析脚本 - 全面审查2025年7月4日以来的所有业务代码变更
 * 
 * 按照缺陷調査手法と経験教訓.md的要求，系统性地分析所有业务代码文件
 * 识别所有实质性的代码变更，包括：
 * 1. 业务逻辑变更
 * 2. UI/UX改善
 * 3. 架构改善
 * 4. 安全性改善
 * 5. 配置改善
 * 6. 新功能实现
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 业务代码文件路径配置
const BUSINESS_CODE_PATHS = [
  'apps/jcs-backend-services-standard',
  'apps/jcs-backend-services-long-running', 
  'apps/jcs-endpoint-nextjs/app'
];

// 排除的文件和目录
const EXCLUDE_PATTERNS = [
  '__tests__',
  '*.test.ts',
  '*.spec.ts',
  'node_modules',
  'dist',
  'coverage',
  '*.md',
  'package.json',
  'package-lock.json',
  'tsconfig.json',
  '.git'
];

// 缺陷分析结果
const defectAnalysis = {
  totalFiles: 0,
  analyzedFiles: 0,
  defects: [],
  summary: {
    businessLogic: 0,
    uiUx: 0,
    architecture: 0,
    security: 0,
    configuration: 0,
    newFeatures: 0
  }
};

/**
 * 检查文件是否应该被排除
 */
function shouldExclude(filePath) {
  return EXCLUDE_PATTERNS.some(pattern => {
    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace('*', '.*'));
      return regex.test(filePath);
    }
    return filePath.includes(pattern);
  });
}

/**
 * 获取所有业务代码文件
 */
function getBusinessCodeFiles() {
  const files = [];
  
  function scanDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) return;
    
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const relativePath = path.relative(process.cwd(), fullPath);
      
      if (shouldExclude(relativePath)) continue;
      
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath);
      } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.tsx'))) {
        files.push(relativePath);
      }
    }
  }
  
  BUSINESS_CODE_PATHS.forEach(scanDirectory);
  return files;
}

/**
 * 分析单个文件的缺陷模式
 */
function analyzeFileForDefects(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const defects = [];
    
    // 1. 楽観ロック制御の検出
    if (content.includes('updateMany') && content.includes('updatedAt')) {
      defects.push({
        type: 'businessLogic',
        pattern: '楽観ロック制御の実装',
        file: filePath,
        evidence: 'updateMany with updatedAt condition'
      });
    }
    
    // 2. 型安全性改善の検出
    if (content.includes('message: unknown') || content.includes('payload?.')) {
      defects.push({
        type: 'businessLogic', 
        pattern: '型安全性の改善',
        file: filePath,
        evidence: 'unknown type or optional chaining'
      });
    }
    
    // 3. セキュリティ改善の検出
    if (content.includes('crypto.randomUUID') || content.includes('window.crypto') || content.includes('generateSecureId')) {
      defects.push({
        type: 'security',
        pattern: 'セキュリティ改善（暗号学的乱数生成）',
        file: filePath,
        evidence: 'cryptographic random generation'
      });
    }
    
    // 4. エラーハンドリング改善の検出
    if (content.includes('EMET0') || content.includes('formatTaskErrorMessage') || content.includes('handleApiError')) {
      defects.push({
        type: 'businessLogic',
        pattern: 'エラーハンドリング改善',
        file: filePath,
        evidence: 'error code or error handling functions'
      });
    }
    
    // 5. Next.js App Router対応の検出
    if (content.includes('NextRequest') || content.includes('NextResponse')) {
      defects.push({
        type: 'architecture',
        pattern: 'Next.js App Router対応',
        file: filePath,
        evidence: 'NextRequest/NextResponse usage'
      });
    }
    
    // 6. ファイル検証の検出
    if (content.includes('FILE_VALIDATION') || content.includes('ALLOWED_EXTENSIONS') || content.includes('MAX_FILE_SIZE')) {
      defects.push({
        type: 'security',
        pattern: 'ファイル検証強化',
        file: filePath,
        evidence: 'file validation constants'
      });
    }
    
    // 7. データアクセス層分離の検出
    if (content.includes('ServerDataServers') || content.includes('ServerDataLov') || content.includes('ServerDataTasks')) {
      defects.push({
        type: 'architecture',
        pattern: 'データアクセス層責務分離',
        file: filePath,
        evidence: 'separated data access classes'
      });
    }
    
    // 8. UI状態管理改善の検出
    if (content.includes('loading') && content.includes('useState') && content.includes('disabled')) {
      defects.push({
        type: 'uiUx',
        pattern: 'UI状態管理改善',
        file: filePath,
        evidence: 'loading state management'
      });
    }
    
    // 9. 表单验证改善の検出
    if (content.includes('validation') || content.includes('border-red-500') || content.includes('tooltip')) {
      defects.push({
        type: 'uiUx',
        pattern: '表单验证・エラー表示改善',
        file: filePath,
        evidence: 'form validation or error display'
      });
    }
    
    // 10. 環境変数処理改善の検出
    if (content.includes('ENV.') || content.includes('process.env') && content.includes('||')) {
      defects.push({
        type: 'configuration',
        pattern: '環境変数処理改善',
        file: filePath,
        evidence: 'environment variable with fallback'
      });
    }
    
    return defects;
    
  } catch (error) {
    console.error(`Error analyzing file ${filePath}:`, error.message);
    return [];
  }
}

/**
 * 生成缺陷分析报告
 */
function generateReport() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportPath = `docs-delivery/unit-test-report/缺陷分析報告_${timestamp}.md`;
  
  let report = `# 缺陷分析報告_全面審查版\n\n`;
  report += `## 概要\n\n`;
  report += `本報告書は、2025年07月04日以降の全業務コード変更を対象とした包括的な缺陷分析結果です。\n\n`;
  report += `- **分析対象ファイル数**: ${defectAnalysis.analyzedFiles}\n`;
  report += `- **発見缺陷数**: ${defectAnalysis.defects.length}\n`;
  report += `- **分析日時**: ${new Date().toLocaleString('ja-JP')}\n\n`;
  
  report += `## 缺陷分類統計\n\n`;
  report += `| 分類 | 件数 | 割合 |\n`;
  report += `|------|------|------|\n`;
  report += `| 業務ロジック改善 | ${defectAnalysis.summary.businessLogic} | ${((defectAnalysis.summary.businessLogic / defectAnalysis.defects.length) * 100).toFixed(1)}% |\n`;
  report += `| UI/UX改善 | ${defectAnalysis.summary.uiUx} | ${((defectAnalysis.summary.uiUx / defectAnalysis.defects.length) * 100).toFixed(1)}% |\n`;
  report += `| アーキテクチャ改善 | ${defectAnalysis.summary.architecture} | ${((defectAnalysis.summary.architecture / defectAnalysis.defects.length) * 100).toFixed(1)}% |\n`;
  report += `| セキュリティ改善 | ${defectAnalysis.summary.security} | ${((defectAnalysis.summary.security / defectAnalysis.defects.length) * 100).toFixed(1)}% |\n`;
  report += `| 設定改善 | ${defectAnalysis.summary.configuration} | ${((defectAnalysis.summary.configuration / defectAnalysis.defects.length) * 100).toFixed(1)}% |\n`;
  report += `| 新機能実装 | ${defectAnalysis.summary.newFeatures} | ${((defectAnalysis.summary.newFeatures / defectAnalysis.defects.length) * 100).toFixed(1)}% |\n\n`;
  
  report += `## 詳細缺陷一覧\n\n`;
  
  defectAnalysis.defects.forEach((defect, index) => {
    report += `### DEF-${String(index + 1).padStart(3, '0')}: ${defect.pattern}\n`;
    report += `- **ファイル**: ${defect.file}\n`;
    report += `- **分類**: ${defect.type}\n`;
    report += `- **根拠**: ${defect.evidence}\n\n`;
  });
  
  fs.writeFileSync(reportPath, report);
  console.log(`\n📊 缺陷分析報告を生成しました: ${reportPath}`);
  
  return reportPath;
}

/**
 * メイン実行関数
 */
function main() {
  console.log('🔍 缺陷分析スクリプト開始...\n');
  console.log('📋 分析対象:');
  BUSINESS_CODE_PATHS.forEach(path => console.log(`   - ${path}`));
  console.log('\n' + '='.repeat(80));
  
  // 業務コードファイルを取得
  const files = getBusinessCodeFiles();
  defectAnalysis.totalFiles = files.length;
  
  console.log(`\n📁 発見された業務コードファイル: ${files.length}件`);
  
  // 各ファイルを分析
  for (const file of files) {
    console.log(`🔍 分析中: ${file}`);
    const fileDefects = analyzeFileForDefects(file);
    
    defectAnalysis.defects.push(...fileDefects);
    defectAnalysis.analyzedFiles++;
    
    // 分類別統計を更新
    fileDefects.forEach(defect => {
      switch (defect.type) {
        case 'businessLogic': defectAnalysis.summary.businessLogic++; break;
        case 'uiUx': defectAnalysis.summary.uiUx++; break;
        case 'architecture': defectAnalysis.summary.architecture++; break;
        case 'security': defectAnalysis.summary.security++; break;
        case 'configuration': defectAnalysis.summary.configuration++; break;
        case 'newFeatures': defectAnalysis.summary.newFeatures++; break;
      }
    });
  }
  
  console.log('\n' + '='.repeat(80));
  console.log(`✅ 分析完了!`);
  console.log(`📊 総缺陷数: ${defectAnalysis.defects.length}件`);
  
  // 報告書生成
  const reportPath = generateReport();
  
  console.log(`\n🎯 次のステップ: ${reportPath} を確認し、詳細な缺陷報告書を作成してください。`);
}

// スクリプト実行
if (require.main === module) {
  main();
}

module.exports = { analyzeFileForDefects, getBusinessCodeFiles };
