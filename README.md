# JCS 端点资产与任务管理系统

这是一个基于Monorepo架构的JCS端点资产与任务管理系统项目。

## 项目结构

- `apps/`: 包含所有应用程序
  - `jcs-endpoint-nextjs/`: Next.js前端应用
  - `jcs-backend-services/`: Azure Functions后端服务
- `automation/`: Azure Automation Runbooks和模块
- `scripts/`: 构建和部署脚本
- `docs/`: 项目核心技术文档 (详情参见 [`docs/README.md`](./docs/README.md))
- `docs-delivery/`: 交付文档（日文）

## 开发环境设置

1.  克隆仓库
2.  安装依赖：
    ```bash
    pnpm install
    ```
3.  设置环境变量（参考各应用目录下的说明，以及 [`docs/guides/environment-variables.md`](./docs/guides/environment-variables.md)）

## 部署说明

详细的部署说明请参考 [`部署流程指南`](./docs/guides/deployment-procedures.md)。

## 核心文档入口

*   **项目文档结构与协作指南**: [`docs/README.md`](./docs/README.md) - **强烈建议首先阅读此文档以了解整个文档库的组织和使用方法。**
*   **系统架构**: [`系统架构设计文档`](./docs/architecture/system-architecture.md)
*   **Monorepo项目实施指南**: [`Monorepo项目实施指南`](./docs/guides/monorepo-structure-and-deployment-workflow.md)
*   **AI协作与文档规范**: [`AI协作与文档规范指南`](./docs/guides/ai-collaboration-and-documentation-standards.md)
*   **开发环境设置指南**: [`开发环境设置指南`](./docs/guides/development-setup.md)
*   **环境变量管理**: [`环境变量指南`](./docs/guides/environment-variables.md)
*   **(若使用ADR)** 架构决策记录: 参见 `docs/architecture/adrs/` 目录。

## 贡献

请参考项目内部的贡献指南和编码标准（位于 `docs/guides/` 目录下）。