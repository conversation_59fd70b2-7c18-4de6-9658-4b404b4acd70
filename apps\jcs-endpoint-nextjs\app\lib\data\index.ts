/**
 * @fileoverview リファクタリング互換レイヤーとして、各データアクセスクラスのメソッドを統一されたServerDataオブジェクトとして再エクスポートする。
 * @description 既存コードとの互換性を保ちながら、新しいコードでは各専用クラスを直接使用できるようにする。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { ServerDataLov } from "./lov";
import { ServerDataManuals } from "./manuals";
import { ServerDataMedias } from "./medias";
import { ServerDataOplogs } from "./oplogs";
import { ServerDataProvidedFiles } from "./provided-files";
import { ServerDataServers } from "./servers";
import { ServerDataSupportFiles } from "./support-files";
import { ServerDataTasks } from "./tasks";

/**
 * リファクタリング互換レイヤー：各データアクセスクラスのメソッドを統一されたServerDataオブジェクトとして再エクスポートする。
 * 既存コードとの互換性を保ちながら、新しいコードでは各専用クラスを直接使用できるようにする。
 */
const ServerData = {
  // ServerDataManualsからエクスポート
  fetchCachedManuals: ServerDataManuals.fetchCachedManuals,
  fetchProductManualPages: ServerDataManuals.fetchProductManualPages,

  // ServerDataMediasからエクスポート
  fetchCachedMedias: ServerDataMedias.fetchCachedMedias,
  fetchProductMediaPages: ServerDataMedias.fetchProductMediaPages,

  // ServerDataOplogsからエクスポート
  fetchCachedOplogs: ServerDataOplogs.fetchCachedOplogs,
  fetchOplogsPages: ServerDataOplogs.fetchOplogsPages,

  // ServerDataProvidedFilesからエクスポート
  fetchCachedProvidedFiles: ServerDataProvidedFiles.fetchCachedProvidedFiles,
  fetchProvidedFilePages: ServerDataProvidedFiles.fetchProvidedFilePages,

  // ServerDataSupportFilesからエクスポート
  fetchCachedSupportFiles: ServerDataSupportFiles.fetchCachedSupportFiles,
  fetchSupportFilePages: ServerDataSupportFiles.fetchSupportFilePages,

  // ServerDataLovからエクスポート
  fetchLovList: ServerDataLov.fetchLovList,
  fetchLov: ServerDataLov.fetchLov,

  // ServerDataServersからエクスポート
  fetchCachedServers: ServerDataServers.fetchCachedServers,
  fetchServersPages: ServerDataServers.fetchServersPages,
  getServerDetailsForTask: ServerDataServers.getServerDetailsForTask,

  // ServerDataTasksからエクスポート
  fetchCachedTasks: ServerDataTasks.fetchCachedTasks,
  fetchTasksPages: ServerDataTasks.fetchTasksPages,
};

export default ServerData;
