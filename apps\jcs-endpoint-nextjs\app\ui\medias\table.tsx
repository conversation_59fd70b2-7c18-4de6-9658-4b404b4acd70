/**
 * @file table.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

import { ServerDataMedias } from "@/app/lib/data/medias";
import Thead from "../thead";

// 製品媒体テーブルコンポーネント
export default async function ProductMediasTable({
  filter,
  page,
  size,
  sort,
  order,
  preferSort,
}: {
  filter: string;
  page: number;
  size: number;
  sort:
    | "name"
    | "productCode"
    | "version"
    | "os"
    | "releasedAt"
    | "mediaName"
    | "mediaSize"
    | "documentName"
    | "documentSize";
  order: "asc" | "desc";
  preferSort:
    | "name"
    | "productCode"
    | "version"
    | "os"
    | "releasedAt"
    | "mediaName"
    | "mediaSize"
    | "documentName"
    | "documentSize";
}) {
  const medias = await ServerDataMedias.fetchFilteredProductMedias(
    filter,
    size,
    page,
    sort,
    order,
    preferSort,
  );

  return (
    <table className="whitespace-nowrap w-full text-left text-sm text-gray-500">
      <Thead
        headers={[
          { key: "name", label: "製品名" },
          { key: "productCode", label: "製品形名" },
          { key: "version", label: "バージョン" },
          { key: "os", label: "OS" },
          { key: "releasedAt", label: "リリース日" },
          { key: "mediaName", label: "製品媒体" },
          { key: "mediaSize", label: "製品媒体のサイズ" },
          { key: "documentName", label: "ドキュメント" },
          { key: "documentSize", label: "ドキュメントのサイズ" },
        ]}
        defaultOrder="releasedAt"
        defaultSort="desc"
      />
      <tbody>
        {medias?.length !== 0 ? (
          medias!.map((media) => (
            <tr
              key={media.id}
              className="border-b odd:bg-white even:bg-gray-50"
            >
              <th
                scope="row"
                className="border-r text-gray-900 whitespace-nowrap px-6 py-4 font-medium"
              >
                {media.name}
              </th>
              <td className="border-r px-6 py-4">{media.productCode}</td>
              <td className="border-r px-6 py-4">{media.version}</td>
              <td className="border-r px-6 py-4">{media.os}</td>
              <td className="border-r px-6 py-4">{media.releasedAt}</td>
              <td className="border-r px-6 py-4">
                <a
                  target="_blank"
                  href={`medias/${media.productCode}/${media.version}/${media.mediaName}`}
                  className="font-medium text-blue-600 hover:underline"
                >
                  {media.mediaName}
                </a>
              </td>
              <td className="border-r px-6 py-4 text-right">
                {media.formattedMediaSize}
              </td>
              <td className="border-r px-6 py-4">
                <a
                  target="_blank"
                  href={`medias/${media.productCode}/${media.version}/${media.documentName}`}
                  className="font-medium text-blue-600 hover:underline"
                >
                  {media.documentName}
                </a>
              </td>
              <td className="px-6 py-4 text-right">
                {media.formattedDocumentSize}
              </td>
            </tr>
          ))
        ) : (
          <tr>
            <td>
              <div className="p-4"></div>
            </td>
          </tr>
        )}
      </tbody>
    </table>
  );
}
