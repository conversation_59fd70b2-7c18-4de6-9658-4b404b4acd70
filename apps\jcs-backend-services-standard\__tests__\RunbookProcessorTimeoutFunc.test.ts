/**
 * @file RunbookProcessorTimeoutFunc.test.ts
 * @description
 * RunbookProcessorTimeoutFunc の単体テストファイルです。
 * Jest を用いて、Runbookジョブ処理タイムアウト補償関数の主要な分岐、例外処理、ファイル削除・DB更新・ロック解放の動作を検証します。
 *
 * 【設計意図】
 * - Runbookジョブ処理タイムアウト補償関数の主要な分岐・例外・楽観ロック制御の網羅的なテストを自動化。
 * - データベース/Azure Files等の外部依存をモックし、異常系も含めて堅牢性を担保。
 * - 100%のコードカバレッジを目指し、全ての分岐・例外処理を網羅的に検証。
 *
 * 【主なテストカバレッジ】
 * 1. メッセージ検証（null、非object、taskId不足・不正）
 * 2. Task取得（成功、失敗）
 * 3. Task状態判定（RUNBOOK_PROCESSING、その他）
 * 4. DBトランザクション（成功、Container更新失敗、Task更新失敗）
 * 5. Azure Files削除（成功、失敗）
 * 6. 全体例外処理
 *
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { RunbookProcessorTimeoutFunc } from "../RunbookProcessorTimeoutFunc/RunbookProcessorTimeoutFunc";
import { prisma } from "../lib/prisma";
import * as utils from "../lib/utils";
import * as azureClients from "../lib/azureClients";
import { AppConstants } from "../lib/constants";

// Prisma のモック
jest.mock("../lib/prisma", () => {
  const { mockDeep } = require("jest-mock-extended");
  return { prisma: mockDeep() };
});

// Azure Clients のモック
jest.mock("../lib/azureClients", () => ({
  createShareServiceClient: jest.fn(),
}));

// Utils のモック
jest.mock("../lib/utils", () => ({
  ...jest.requireActual("../lib/utils"),
  deleteTaskWorkspaceDirectory: jest.fn(),
  formatTaskErrorMessage: jest.fn(),
}));

/**
 * @fileoverview RunbookProcessorTimeoutFunc関数の単体テスト。
 * @description Azure FunctionsのRunbookProcessorTimeoutFuncの主要分岐・例外・補償処理を網羅的に検証する。
 * 試験観点：Runbookジョブ処理タイムアウト補償関数の正常系・異常系・補償分岐網羅性、外部依存のモックによる堅牢性検証。
 * 試験対象：RunbookProcessorTimeoutFunc.ts（RunbookProcessorTimeoutFunc Azure Function本体）。
 */
describe("RunbookProcessorTimeoutFunc 単体テスト", () => {
  let context: any;

  beforeEach(() => {
    /**
     * Azure Functions の InvocationContext をモックし、必須フィールドを全て補完
     */
    context = {
      invocationId: "test-invoke",
      functionName: "RunbookProcessorTimeoutFunc",
      extraInputs: [],
      extraOutputs: [],
      bindingData: {},
      traceContext: {},
      log: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
    };

    jest.clearAllMocks();

    // Azure Clients のモック設定
    const mockShareClient = {
      getDirectoryClient: jest.fn().mockReturnValue({})
    };
    (azureClients.createShareServiceClient as jest.Mock).mockReturnValue({
      getShareClient: jest.fn().mockReturnValue(mockShareClient)
    });

    // deleteTaskWorkspaceDirectory のデフォルトモック
    (utils.deleteTaskWorkspaceDirectory as jest.Mock).mockResolvedValue(undefined);

    // formatTaskErrorMessage のデフォルトモック
    (utils.formatTaskErrorMessage as jest.Mock).mockReturnValue("タイムアウトエラー");
  });

  /**
   * 試験観点：メッセージnull分岐の検証。
   * 試験対象：RunbookProcessorTimeoutFuncのメッセージ検証分岐。
   * 試験手順：
   * 1. messageがnullの場合をテスト。
   * 確認項目：
   * - context.errorに"メッセージが不正"が出力されること。
   * - 処理が終了すること。
   */
  it("メッセージがnull: エラーログ記録", async () => {
    await RunbookProcessorTimeoutFunc(null, context);
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("メッセージが不正"));
    expect(prisma.task.findUnique).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：メッセージ型検証分岐の検証。
   * 試験対象：RunbookProcessorTimeoutFuncのメッセージ検証分岐。
   * 試験手順：
   * 1. messageが文字列（非object）の場合をテスト。
   * 確認項目：
   * - context.errorに"メッセージが不正"が出力されること。
   * - 処理が終了すること。
   */
  it("メッセージが非object: エラーログ記録", async () => {
    await RunbookProcessorTimeoutFunc("invalid", context);
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("メッセージが不正"));
    expect(prisma.task.findUnique).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：taskId不足分岐の検証。
   * 試験対象：RunbookProcessorTimeoutFuncのtaskId検証分岐。
   * 試験手順：
   * 1. taskIdが不足している場合をテスト。
   * 確認項目：
   * - context.errorに"taskIdが特定できません"が出力されること。
   * - 処理が終了すること。
   */
  it("taskId不足: エラーログ記録", async () => {
    await RunbookProcessorTimeoutFunc({}, context);
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("taskIdが特定できません"));
    expect(prisma.task.findUnique).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：taskId型検証分岐の検証。
   * 試験対象：RunbookProcessorTimeoutFuncのtaskId検証分岐。
   * 試験手順：
   * 1. taskIdが非文字列型の場合をテスト。
   * 確認項目：
   * - context.errorに"taskIdが特定できません"が出力されること。
   * - 処理が終了すること。
   */
  it("taskId非文字列: エラーログ記録", async () => {
    await RunbookProcessorTimeoutFunc({ taskId: 123 }, context);
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("taskIdが特定できません"));
    expect(prisma.task.findUnique).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：taskId空文字列分岐の検証。
   * 試験対象：RunbookProcessorTimeoutFuncのtaskId検証分岐。
   * 試験手順：
   * 1. taskIdが空文字列の場合をテスト。
   * 確認項目：
   * - context.errorに"taskIdが特定できません"が出力されること。
   * - 処理が終了すること。
   */
  it("taskId空文字列: エラーログ記録", async () => {
    await RunbookProcessorTimeoutFunc({ taskId: "" }, context);
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("taskIdが特定できません"));
    expect(prisma.task.findUnique).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：Task不存在分岐の検証。
   * 試験対象：RunbookProcessorTimeoutFuncのTask取得失敗分岐。
   * 試験手順：
   * 1. Taskが存在しない場合をテスト。
   * 確認項目：
   * - context.errorに"存在しません"が出力されること。
   * - 処理が終了すること。
   */
  it("Task不存在: エラーログ記録", async () => {
    (prisma.task.findUnique as any).mockResolvedValue(null);

    await RunbookProcessorTimeoutFunc({ taskId: "notfound" }, context);
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("存在しません"));
    expect(prisma.$transaction).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：ステータス不正分岐の検証。
   * 試験対象：RunbookProcessorTimeoutFuncのステータス判定分岐。
   * 試験手順：
   * 1. RUNBOOK_PROCESSING以外のステータスの場合をテスト。
   * 確認項目：
   * - context.logに"処理対象外です"が出力されること。
   * - 処理が終了すること。
   */
  it("RUNBOOK_PROCESSING以外のステータス: 警告ログ記録", async () => {
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.RunbookSubmitted,
      targetVmName: "vm1",
      targetContainerName: "container1"
    });

    await RunbookProcessorTimeoutFunc({ taskId: "task1" }, context);
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("処理対象外です"));
    expect(prisma.$transaction).not.toHaveBeenCalled();
  });

  /**
   * 試験観点：正常系フローの検証。
   * 試験対象：RunbookProcessorTimeoutFuncの正常系フロー。
   * 試験手順：
   * 1. RUNBOOK_PROCESSINGのタスクを正常に処理する場合をテスト。
   * 確認項目：
   * - DBトランザクションが実行されること。
   * - context.logに成功メッセージが出力されること。
   */
  it("RUNBOOK_PROCESSING正常処理: 成功", async () => {
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.RunbookProcessing,
      targetVmName: "vm1",
      targetContainerName: "container1",
      updatedAt: new Date()
    });
    (prisma.containerConcurrencyStatus.updateMany as any).mockResolvedValue({ count: 1 });
    (prisma.task.updateMany as any).mockResolvedValue({ count: 1 });
    (prisma.$transaction as any).mockImplementation((fn: any) => fn(prisma));

    await RunbookProcessorTimeoutFunc({ taskId: "task1" }, context);

    expect(prisma.$transaction).toHaveBeenCalled();
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("DBトランザクション成功"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("正常に完了"));
  });

  /**
   * 試験観点：DBトランザクション失敗分岐の検証。
   * 試験対象：RunbookProcessorTimeoutFuncのDBトランザクション失敗分岐。
   * 試験手順：
   * 1. DBトランザクションが失敗する場合をテスト。
   * 確認項目：
   * - context.errorに"DBトランザクション失敗"が出力されること。
   * - 処理が継続されること。
   */
  it("DBトランザクション失敗: エラーログ記録", async () => {
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.RunbookProcessing,
      targetVmName: "vm1",
      targetContainerName: "container1",
      updatedAt: new Date()
    });
    (prisma.$transaction as any).mockRejectedValue(new Error("DBトランザクション失敗"));

    await RunbookProcessorTimeoutFunc({ taskId: "task1" }, context);
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("DBトランザクション失敗"));
  });

  /**
   * 試験観点：Azure Files削除失敗分岐の検証。
   * 試験対象：RunbookProcessorTimeoutFuncのAzure Files削除失敗分岐。
   * 試験手順：
   * 1. Azure Files削除が失敗する場合をテスト。
   * 確認項目：
   * - context.errorに"削除失敗"が出力されること。
   * - 後続処理が継続されること。
   */
  it("Azure Files削除失敗: 処理継続", async () => {
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.RunbookProcessing,
      targetVmName: "vm1",
      targetContainerName: "container1",
      updatedAt: new Date()
    });
    (utils.deleteTaskWorkspaceDirectory as jest.Mock).mockRejectedValue(new Error("削除失敗"));
    (prisma.containerConcurrencyStatus.updateMany as any).mockResolvedValue({ count: 1 });
    (prisma.task.updateMany as any).mockResolvedValue({ count: 1 });
    (prisma.$transaction as any).mockImplementation((fn: any) => fn(prisma));

    await RunbookProcessorTimeoutFunc({ taskId: "task1" }, context);
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("削除失敗"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("正常に完了"));
  });

  /**
   * 試験観点：Task取得例外分岐の検証。
   * 試験対象：RunbookProcessorTimeoutFuncのTask取得例外分岐。
   * 試験手順：
   * 1. Task取得で例外が発生する場合をテスト。
   * 確認項目：
   * - context.errorに"予期せぬ内部エラー"が出力されること。
   * - 処理が終了すること。
   */
  it("Task取得例外: エラーログ記録", async () => {
    (prisma.task.findUnique as any).mockRejectedValue(new Error("DB接続失敗"));

    await RunbookProcessorTimeoutFunc({ taskId: "task1" }, context);
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("予期せぬ内部エラー"));
  });

  /**
   * 試験観点：Container更新0件分岐の検証。
   * 試験対象：RunbookProcessorTimeoutFuncのContainer更新0件分岐。
   * 試験手順：
   * 1. Container更新件数が0件の場合をテスト。
   * 確認項目：
   * - context.errorに"コンテナ実行状態テーブル更新失敗"が出力されること。
   * - トランザクションがロールバックされること。
   */
  it("Container更新0件: エラーログ記録", async () => {
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.RunbookProcessing,
      targetVmName: "vm1",
      targetContainerName: "container1",
      updatedAt: new Date()
    });
    (prisma.containerConcurrencyStatus.updateMany as any).mockResolvedValue({ count: 0 });
    (prisma.$transaction as any).mockImplementation((fn: any) => fn(prisma));

    await RunbookProcessorTimeoutFunc({ taskId: "task1" }, context);
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("コンテナ実行状態テーブル更新失敗"));
  });

  /**
   * 試験観点：Task更新0件分岐の検証。
   * 試験対象：RunbookProcessorTimeoutFuncのTask更新0件分岐。
   * 試験手順：
   * 1. Task更新件数が0件の場合をテスト。
   * 確認項目：
   * - context.errorに"タスクテーブル更新失敗"が出力されること。
   * - トランザクションがロールバックされること。
   */
  it("Task更新0件: エラーログ記録", async () => {
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.RunbookProcessing,
      targetVmName: "vm1",
      targetContainerName: "container1",
      updatedAt: new Date()
    });
    (prisma.containerConcurrencyStatus.updateMany as any).mockResolvedValue({ count: 1 });
    (prisma.task.updateMany as any).mockResolvedValue({ count: 0 });
    (prisma.$transaction as any).mockImplementation((fn: any) => fn(prisma));

    await RunbookProcessorTimeoutFunc({ taskId: "task1" }, context);
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("タスクテーブル更新失敗"));
  });

  /**
   * 試験観点：楽観ロック制御の正確性検証（設計文書6章の要件）。
   * 試験対象：RunbookProcessorTimeoutFuncの楽観ロック制御分岐。
   * 試験手順：
   * 1. 楽観ロック失敗の具体的なシナリオをテスト（設計文書の重要要件）。
   * 確認項目：
   * - Task読取後に他プロセスがupdatedAtを更新した場合
   * - updateMany の count が 0 になること
   * - トランザクションがロールバックされること
   */
  it("楽観ロック失敗: 並行更新検出", async () => {
    const originalUpdatedAt = new Date('2023-01-01T10:00:00Z');
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.RunbookProcessing,
      targetVmName: "vm1",
      targetContainerName: "container1",
      updatedAt: originalUpdatedAt
    });
    (prisma.containerConcurrencyStatus.updateMany as any).mockResolvedValue({ count: 1 });
    // 楽観ロック失敗をシミュレート：他プロセスがupdatedAtを更新済み
    (prisma.task.updateMany as any).mockResolvedValue({ count: 0 });
    (prisma.$transaction as any).mockImplementation((fn: any) => fn(prisma));

    await RunbookProcessorTimeoutFunc({ taskId: "task1" }, context);

    // 楽観ロック制御の確認
    expect(prisma.task.updateMany).toHaveBeenCalledWith({
      where: {
        id: "task1",
        updatedAt: originalUpdatedAt // 元の更新日時で更新を試行
      },
      data: expect.objectContaining({
        status: AppConstants.TaskStatus.CompletedError,
        errorCode: AppConstants.ERROR_CODES.EMET0005
      })
    });
    expect(context.error).toHaveBeenCalledWith(expect.stringContaining("タスクテーブル更新失敗"));
  });

  /**
   * 試験観点：Container更新条件の正確性検証（設計文書の具体的要件）。
   * 試験対象：RunbookProcessorTimeoutFuncのContainer更新WHERE条件。
   * 試験手順：
   * 1. Container更新の具体的な条件をテスト（設計文書5章の要件）。
   * 確認項目：
   * - WHERE条件が設計通りに設定されること
   * - targetVmName, targetContainerName, status=BUSY, currentTaskId=taskId
   */
  it("Container更新条件: 設計仕様準拠", async () => {
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.RunbookProcessing,
      targetVmName: "test-vm",
      targetContainerName: "test-container",
      updatedAt: new Date()
    });
    (prisma.containerConcurrencyStatus.updateMany as any).mockResolvedValue({ count: 1 });
    (prisma.task.updateMany as any).mockResolvedValue({ count: 1 });
    (prisma.$transaction as any).mockImplementation((fn: any) => fn(prisma));

    await RunbookProcessorTimeoutFunc({ taskId: "task1" }, context);

    // 設計文書通りのWHERE条件確認
    expect(prisma.containerConcurrencyStatus.updateMany).toHaveBeenCalledWith({
      where: {
        targetVmName: "test-vm",
        targetContainerName: "test-container",
        status: "BUSY",
        currentTaskId: "task1"
      },
      data: {
        status: "IDLE",
        currentTaskId: null
      }
    });
  });

  /**
   * 試験観点：正常系フローの網羅的検証。
   * 試験対象：RunbookProcessorTimeoutFuncの正常系フロー。
   * 試験手順：
   * 1. 正常系の完全なフローをテスト。
   * 確認項目：
   * - 全ての処理が正常に実行されること。
   * - 適切なログが出力されること。
   */
  it("正常系: 完全フロー", async () => {
    (prisma.task.findUnique as any).mockResolvedValue({
      id: "task1",
      status: AppConstants.TaskStatus.RunbookProcessing,
      targetVmName: "vm1",
      targetContainerName: "container1",
      updatedAt: new Date()
    });
    (prisma.containerConcurrencyStatus.updateMany as any).mockResolvedValue({ count: 1 });
    (prisma.task.updateMany as any).mockResolvedValue({ count: 1 });
    (prisma.$transaction as any).mockImplementation((fn: any) => fn(prisma));

    await RunbookProcessorTimeoutFunc({ taskId: "task1" }, context);

    // 各ステップの確認
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("メッセージ受信"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("受信taskId: task1"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("DBトランザクション成功"));
    expect(context.log).toHaveBeenCalledWith(expect.stringContaining("正常に完了"));

    // API呼び出しの確認
    expect(prisma.task.findUnique).toHaveBeenCalledWith({
      where: { id: "task1" },
      select: { status: true, targetVmName: true, targetContainerName: true, updatedAt: true }
    });
    expect(utils.formatTaskErrorMessage).toHaveBeenCalledWith(AppConstants.ERROR_CODES.EMET0005);
  });
});