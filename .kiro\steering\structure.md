# 项目结构与组织

## 目录结构

```
jcs-endpoint-monorepo/
├── apps/                     # 应用程序代码
│   ├── jcs-endpoint-nextjs/  # Next.js前端应用
│   ├── jcs-backend-services-standard/    # 标准 Azure Functions 后端服务
│   └── jcs-backend-services-long-running/ # 长时运行 Azure Functions 后端服务
├── automation/               # Azure Automation 相关代码
│   ├── modules/              # PowerShell 模块
│   └── runbooks/             # Azure Automation Runbooks
├── docs/                     # 核心技术文档 (中文内容, 英文文件名)
│   ├── architecture/         # 系统架构、设计原则、ADRs
│   ├── apis/                 # API规范 (OpenAPI)
│   ├── components/           # 各核心组件/功能的详细说明
│   ├── data-models/          # 数据模型详细说明
│   ├── guides/               # 开发、部署、迁移等指南
│   └── definitions/          # 系统级定义与参考列表
├── docs-delivery/            # 交付文档（日文）
│   ├── detailed-design-specifications/ # 詳細設計書
│   ├── functional-specifications/      # 機能仕様書
│   └── unit-test-report/               # 单元测试报告
├── scripts/                  # 构建和部署脚本
├── tests/                    # 测试代码
│   └── integration/          # 集成测试
└── .kiro/                    # Kiro AI 助手配置
    └── steering/             # 项目指导文件
```

## 应用结构

### Next.js 前端应用

前端应用采用 Next.js App Router 架构，主要结构如下：

```
jcs-endpoint-nextjs/
├── app/                      # App Router 路由和页面组件
│   ├── api/                  # API 路由
│   ├── auth/                 # 认证相关页面
│   ├── servers/              # 服务器管理页面
│   └── tasks/                # 任务管理页面
├── components/               # 共享组件
├── lib/                      # 工具函数和库
├── prisma/                   # Prisma 数据库模型和迁移
│   └── schema.prisma         # 数据库模型定义
└── public/                   # 静态资源
```

### Azure Functions 后端服务

后端服务分为两个应用：

1. **标准服务** (jcs-backend-services-standard)：处理常规 API 请求
2. **长时运行服务** (jcs-backend-services-long-running)：处理需要长时间执行的任务

每个 Function App 的结构如下：

```
jcs-backend-services-*/
├── src/                      # 源代码
│   ├── functions/            # 各个函数定义
│   └── shared/               # 共享代码
├── local.settings.json       # 本地设置
└── host.json                 # 主机配置
```

## 文档结构

项目文档采用"单一事实来源"(SSoT)原则，所有技术文档都存储在 `docs/` 目录下的 Markdown 文件中。详细的文档结构请参考 `docs/README.md`。

## 代码组织原则

1. **功能模块化**：相关功能代码应组织在同一目录下
2. **共享代码抽象**：跨多个组件使用的代码应放在共享目录中
3. **清晰的职责分离**：UI 组件、业务逻辑、数据访问应有明确的分层
4. **一致的命名约定**：
   - 文件名使用小写连字符格式 (kebab-case)
   - React 组件使用大驼峰命名 (PascalCase)
   - 函数和变量使用小驼峰命名 (camelCase)
   - 常量使用大写下划线格式 (UPPER_SNAKE_CASE)

## 测试组织

测试代码组织在 `tests/` 目录下，按测试类型分类：

```
tests/
├── integration/              # 集成测试
│   ├── specs/                # 测试用例
│   └── support/              # 测试辅助模块
└── e2e/                      # 端到端测试 (Playwright)
```