import { FullConfig } from '@playwright/test';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

/**
 * @fileoverview 简化的全局测试环境设置
 * 
 * 本文件提供简化的测试环境设置，主要用于验证配置。
 * 不进行复杂的服务启动和健康检查，假设服务已手动启动。
 * 
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

async function simpleGlobalSetup(config: FullConfig) {
  console.log('🚀 开始简化的测试环境设置...');
  
  try {
    // 1. 🔧 加载环境变量
    await loadEnvironmentVariables();
    
    // 2. 📁 创建测试结果目录
    await createTestDirectories();

    // 3. 📊 初始化测试数据
    await initializeTestData();

    console.log('✅ 简化的测试环境设置完成');
    
  } catch (error) {
    console.error('❌ 简化的测试环境设置失败:', error);
    throw error;
  }
}

/**
 * 加载测试环境变量
 */
async function loadEnvironmentVariables() {
  console.log('🔧 加载测试环境变量...');
  
  // 加载 Next.js 测试环境变量
  const nextjsEnvPath = path.resolve(__dirname, '../../../apps/jcs-endpoint-nextjs/.env.test.local');
  
  if (fs.existsSync(nextjsEnvPath)) {
    const result = dotenv.config({ path: nextjsEnvPath });
    
    if (result.error) {
      console.log(`⚠️ 加载环境变量时出现警告: ${result.error.message}`);
    } else {
      console.log('✅ 成功加载 Next.js 测试环境变量');
    }
  } else {
    console.log(`⚠️ 未找到 Next.js 环境变量文件: ${nextjsEnvPath}`);
  }
  
  // 设置 NODE_ENV 为 test（如果尚未设置）
  if (!process.env.NODE_ENV) {
    process.env.NODE_ENV = 'test';
    console.log('✅ 设置 NODE_ENV=test');
  }
  
  console.log('✅ 环境变量加载完成');
}

/**
 * 创建测试结果目录
 */
async function createTestDirectories() {
  console.log('📁 创建测试结果目录...');
  
  const testResultsDir = path.resolve(__dirname, '../../test-results');
  const htmlReportDir = path.join(testResultsDir, 'html-report');
  const testOutputDir = path.join(testResultsDir, 'test-output');
  
  // 创建目录
  const dirs = [testResultsDir, htmlReportDir, testOutputDir];
  
  for (const dir of dirs) {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`✅ 创建目录: ${dir}`);
    }
  }
  
  console.log('✅ 测试结果目录创建完成');
}

/**
 * 初始化测试数据
 */
async function initializeTestData() {
  console.log('📊 初始化测试数据...');

  try {
    // 初始化 LOV 种子数据
    await initializeLovData();

    console.log('✅ 测试数据初始化完成');
  } catch (error) {
    console.error('❌ 测试数据初始化失败:', error);
    // 暂时跳过 LOV 数据初始化错误，继续测试
    console.log('⚠️ 跳过 LOV 数据初始化，继续测试...');
  }
}

/**
 * 初始化 LOV 种子数据
 */
async function initializeLovData() {
  console.log('🔄 开始初始化 LOV 种子数据...');

  // 动态导入 Prisma Client
  const { PrismaClient } = require('@prisma/client');
  const prisma = new PrismaClient();

  try {
    // 读取 LOV 数据
    const lovDataPath = path.resolve(__dirname, './lovs.json');
    const lovDataJson = JSON.parse(fs.readFileSync(lovDataPath, 'utf-8'));

    // 清理现有 LOV 数据
    await prisma.lov.deleteMany({});
    console.log('🗑️ 清理现有 LOV 数据完成');

    // 按层级插入 LOV 数据（先插入父级，再插入子级）
    const parentItems = lovDataJson.filter((item: any) => !item.parentCode);
    const childItems = lovDataJson.filter((item: any) => item.parentCode);

    // 先插入父级数据
    for (const item of parentItems) {
      await prisma.lov.create({
        data: {
          code: item.code,
          name: item.name,
          value: item.value,
          parentCode: null,
        },
      });
    }
    console.log(`✅ 成功插入 ${parentItems.length} 条父级 LOV 数据`);

    // 再插入子级数据
    for (const item of childItems) {
      await prisma.lov.create({
        data: {
          code: item.code,
          name: item.name,
          value: item.value,
          parentCode: item.parentCode,
        },
      });
    }
    console.log(`✅ 成功插入 ${childItems.length} 条子级 LOV 数据`);
    console.log(`✅ 总共插入 ${lovDataJson.length} 条 LOV 数据`);

  } finally {
    await prisma.$disconnect();
  }
}

export default simpleGlobalSetup;
