// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "sqlserver"
  url      = env("MSSQL_PRISMA_URL") // uses connection pooling
}

model AuditLogin {
  id           String   @id @default(cuid())
  auditType    String
  userId       String
  loginMessage String
  createdBy    String   @default("system")
  createdAt    DateTime @default(now())
}

model Notification {
  id          String   @id @default(cuid())
  content     String   @db.NVarChar(4000)
  publishedAt String
  type        String
  userId      String?
  licenseId   String?
  license     License? @relation(fields: [licenseId], references: [licenseId], onDelete: NoAction, onUpdate: NoAction)
  planId      String?
  plan        Plan?    @relation(fields: [planId], references: [planId], onDelete: NoAction, onUpdate: NoAction)
  serialNo    Int      @unique @default(autoincrement())
}

model License {
  id            String         @id @default(cuid())
  licenseId     String         @unique
  type          String
  expiredAt     DateTime
  maxClients    Int
  isMaintenance Boolean        @default(false)
  isDisabled    Boolean        @default(false)
  basicPlan     String?

  notifications Notification[]
  servers       Server[]
  operationLogs OperationLog[]
  licensePlans  LicensePlan[]
  tasks         Task[]
}

model Plan {
  id                String             @id @default(cuid())
  name              String
  planId            String             @unique
  planProducts      PlanProduct[]
  planManuals       PlanManual[]
  PlanProvidedFiles PlanProvidedFile[]
  notifications     Notification[]
  planSupports      PlanSupport[]
  licensePlans      LicensePlan[]
}

model LicensePlan {
  id        String  @id @default(cuid())
  licenseId String
  planId    String
  plan      Plan    @relation(fields: [planId], references: [planId])
  license   License @relation(fields: [licenseId], references: [licenseId])

  @@index([licenseId, planId], name: "idx_license_plan")
}

model OperationLog {
  id                String    @id @default(cuid())
  name              String
  size              Int
  createdAt         DateTime  @default(now())
  retentionAt       DateTime?
  licenseId         String
  generatedByTaskId String?
  fileName          String    @default("")

  license          License @relation(fields: [licenseId], references: [licenseId], onDelete: NoAction, onUpdate: NoAction)
  generatingTask   Task?   @relation(fields: [generatedByTaskId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([licenseId])
  @@index([generatedByTaskId])
}

model PlanProduct {
  id           String       @id @default(cuid())
  productCode  String
  version      String
  plan         Plan?        @relation(fields: [planId], references: [planId])
  planId       String?
  productMedia ProductMedia @relation(fields: [productCode, version], references: [productCode, version])

  @@index([planId], name: "idx_planId_plan_product")
  @@index([productCode, version], name: "idx_productCode_version_plan_product")
}

model PlanManual {
  id            String        @id @default(cuid())
  serialNo      String
  plan          Plan?         @relation(fields: [planId], references: [planId])
  planId        String?
  productManual ProductManual @relation(fields: [serialNo], references: [serialNo])

  @@index([planId], name: "idx_planId_plan_manual")
  @@index([serialNo], name: "idx_serialNo_plan_manual")
}

model PlanProvidedFile {
  id           String       @id @default(cuid())
  name         String
  plan         Plan?        @relation(fields: [planId], references: [planId])
  planId       String?
  providedFile ProvidedFile @relation(fields: [name], references: [name])

  @@index([planId], name: "idx_planId_plan_provided_file")
  @@index([name], name: "idx_name_plan_provided_file")
}

model PlanSupport {
  id          String      @id @default(cuid())
  serialNo    Int
  plan        Plan?       @relation(fields: [planId], references: [planId])
  planId      String?
  supportFile SupportFile @relation(fields: [serialNo], references: [serialNo])

  @@index([planId], name: "idx_planId_plan_support")
  @@index([serialNo], name: "idx_serialNo_plan_support")
}

model ProductMedia {
  id           String        @id @default(cuid())
  name         String
  productCode  String
  version      String
  os           String
  releasedAt   String
  mediaName    String
  mediaSize    Int
  bigMediaSize BigInt?
  documentName String
  documentSize Int
  planProducts PlanProduct[]

  @@unique([productCode, version])
}

model ProductManual {
  id          String       @id @default(cuid())
  name        String
  serialNo    String       @unique
  fileName    String
  size        Int
  planManuals PlanManual[]
}

model ProvidedFile {
  id                String             @id @default(cuid())
  name              String             @unique
  description       String
  updatedAt         DateTime
  size              Int
  fileName          String             @default("")
  planProvidedFiles PlanProvidedFile[]
}

model SupportFile {
  id           String        @id @default(cuid())
  serialNo     Int           @unique @default(autoincrement())
  title        String
  productName  String
  importance   String
  publishedAt  String
  updatedAt    String
  fileName     String
  planSupports PlanSupport[]
}

model Server {
  id                  String    @id @default(cuid())
  name                String
  type                String
  url                 String
  hrwGroupName        String?
  licenseId           String
  azureVmName         String?
  dockerContainerName String?

  license             License   @relation(fields: [licenseId], references: [licenseId], onDelete: NoAction, onUpdate: NoAction)
  tasks               Task[]
}

model Lov {
  id         String  @id @default(cuid())
  code       String  @unique
  name       String
  parentCode String?
  parent     Lov?    @relation("ChildLoves", fields: [parentCode], references: [code], onDelete: NoAction, onUpdate: NoAction)
  value      String
  isEnabled  Boolean @default(true)
  children   Lov[]   @relation("ChildLoves")
}

model Task {
  id                  String    @id @default(uuid())
  taskName            String?
  taskType            String
  status              String
  submittedAt         DateTime  @default(now())
  startedAt           DateTime?
  endedAt             DateTime?
  updatedAt           DateTime  @updatedAt
  submittedByUserId   String
  licenseId           String
  targetContainerName String?
  targetHRWGroupName  String?
  targetServerId      String
  targetServerName    String?
  targetVmName        String?
  parametersJson      String?   @db.NVarChar(max)
  resultMessage       String?   @db.NVarChar(max)
  errorMessage        String?   @db.NVarChar(max)
  errorCode           String?

  license          License        @relation(fields: [licenseId], references: [licenseId], onDelete: NoAction, onUpdate: NoAction)
  targetServer     Server         @relation(fields: [targetServerId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  operationLogs    OperationLog[]

  @@index([status])
  @@index([targetServerId, submittedAt])
}

model ContainerConcurrencyStatus {
  targetVmName        String
  targetContainerName String
  status              String
  currentTaskId       String?
  updatedAt           DateTime @updatedAt

  @@id([targetVmName, targetContainerName])
}