# 数据模型: 容器并发状态 (ContainerConcurrencyStatus)

*   **表名 (逻辑名)**: `ContainerConcurrencyStatus`
*   **物理表名 (Prisma Model)**: `ContainerConcurrencyStatus` (参考项目根目录下 `prisma/schema.prisma` 文件获取权威定义)
*   **对应UI界面**: 无 (纯后端逻辑表，不直接对应用户界面，其状态间接影响任务能否被创建)
*   **主要用途**: 跟踪和管理每个目标Azure虚拟机上特定Docker容器的并发访问状态。确保在任何给定时间，只有一个后台任务可以操作同一个容器，从而防止因并发操作导致的资源冲突和数据不一致。

## 1. 字段定义

| 字段名 (Prisma Model) | 数据类型 (SQL参考) | PK | FK | UQ | Nullable | Default | 中文描述 | 日文名称 (逻辑参考) |
|--------------------|--------------|----|----|----|----------|---------|------|-------------|
| `targetVmName` | `String` | ● | - | - | - | - | **(复合主键之一) 目标Azure VM名称**。此名称来源于任务关联的 `Server` 表记录中的 `azureVmName` 字段。与 `targetContainerName` 共同唯一标识一个受并发控制的容器实例。 | 対象VM名 (内部) |
| `targetContainerName` | `String` | ● | - | - | - | - | **(复合主键之一) 目标Docker容器名称**。此名称来源于任务关联的 `Server` 表记录中的 `dockerContainerName` 字段。与 `targetVmName` 共同唯一标识一个受并发控制的容器实例。 | 対象コンテナ名 (内部) |
| `status` | `String` | - | - | - | - | - | **容器并发状态**。表示容器当前的并发可用性。合法值为： <br> - **`IDLE`**: 容器当前空闲，可接受新的后台任务。<br> - **`BUSY`**: 容器当前正在被一个后台任务占用，不可接受新的任务。 | コンテナ状態 (内部) |
| `currentTaskId` | `String` | - | - | - | Yes | - | **当前占用任务ID (逻辑外键)**。当 `status` 为 `BUSY` 时，此字段存储当前正在占用该容器的任务的 `Task.id`。当 `status` 为 `IDLE` 时，此字段为 `NULL`。 | 使用中タスクID (内部) |
| `updatedAt` | `DateTime` | - | - | - | - | `@updatedAt` | **记录最后更新时间**。每当此容器状态记录在数据库中被修改时，此时间戳会自动更新为当前时间。(由Prisma自动管理) | 更新日時 (内部監査用) |

*PK: Primary Key, FK: Foreign Key, UQ: Unique Constraint*
*(数据类型参考项目代码库中 `prisma/schema.prisma` 文件的权威定义。SQL参考仅为示意。)*

## 2. 关系 (Relations)

*   **对 `Task` (逻辑关联)**: 通过 `currentTaskId` 字段逻辑上可以关联到 `Task` 表的 `id` 主键，指明当前是哪个任务占用了该容器。此关联的引用完整性和一致性由应用层逻辑负责管理。

## 3. 记录的生命周期与管理

本表中的记录用于实现对特定 `(targetVmName, targetContainerName)` 组合的独占访问。

*   **记录创建**:
    *   当门户后端 `createTaskAction` 在处理用户发起的后台任务请求，并对特定 `(targetVmName, targetContainerName)` 组合进行并发状态检查时，如果数据库中不存在对应的并发状态记录，则 `createTaskAction` **负责创建一条新的 `ContainerConcurrencyStatus` 记录**。
    *   新创建记录的初始状态为 `status = 'IDLE'`, `currentTaskId = NULL`。
    *   如果记录已存在，`createTaskAction` 仅进行只读检查。

*   **状态更新 (获取与释放锁)**:
    *   **获取锁 (`TaskExecuteFunc`)**: 在提交Runbook作业前，`TaskExecuteFunc` 原子地（在数据库事务内）尝试将目标容器记录（此时记录应已由`createTaskAction`创建或之前已存在）的 `status` 从 `IDLE` 更新为 `BUSY`，并设置 `currentTaskId` 为当前任务ID。若获取失败（例如记录状态已为 `BUSY`），则任务创建失败或`TaskExecuteFunc`处理失败。
    *   **释放锁 (`RunbookProcessorFunc` 及相关超时处理函数)**: 当任务处理完成（成功或失败）或因超时等异常中止时，负责的后端组件（主要是 `RunbookProcessorFunc`，以及 `TaskExecuteTimeoutFunc`, `RunbookProcessorTimeoutFunc` 等）将对应容器记录的 `status` 从 `BUSY` 更新回 `IDLE`，并清除 `currentTaskId`。此操作应设计为幂等。

*   **记录删除**:
    *   本表中的记录通常不主动进行物理删除。

## 4. 索引 (Indexes)

*   **复合主键索引**: 由 `(targetVmName, targetContainerName)` 构成，通过 `@@id([targetVmName, targetContainerName])` 在Prisma Schema中定义。此索引确保了基于这两个字段组合进行精确查找的效率，是本表最主要的查询方式。
*(基于当前核心业务流程分析，本表目前不需要其他辅助索引。)*

## 5. 备注 (Notes)

*   本表是实现后台任务对目标Docker容器独占访问控制的核心机制。
*   `targetVmName` 和 `targetContainerName` 的值最初来源于关联`Task`的`Server`记录中的`azureVmName`和`dockerContainerName`字段，并由门户后端 `createTaskAction` 在创建`Task`记录时一并准备和传递。
*   所有对本表`status`字段的更新操作必须设计为原子性的，以避免竞态条件。
*   系统的错误处理和补偿逻辑必须确保在任务非正常结束时，尝试释放可能被占用的容器锁。