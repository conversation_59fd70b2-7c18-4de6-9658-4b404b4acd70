/**
 * @file jest.config.js
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

const nextJest = require("next/jest");

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: "./",
});

// Add any custom config to be passed to Jest
const customJestConfig = {
  setupFilesAfterEnv: ["<rootDir>/jest.setup.js", "<rootDir>/singleton.ts"],
  testEnvironment: "node",
  collectCoverage: true,
  coverageDirectory: "jest-coverage",
  moduleNameMapper: {
    "^@/app/(.*)$": "<rootDir>/app/$1",
  },
};

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig);
