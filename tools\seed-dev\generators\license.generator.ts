/**
 * @fileoverview ライセンスデータ生成器
 * @description hisolライセンスを含む開発環境用のライセンスデータを生成する
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { PrismaClient } from '@prisma/client';
import { BaseGenerator } from './base.generator';
import hisolLicenseData from '../data/hisol-license.json';

/**
 * ライセンスデータ生成器クラス
 * hisolライセンスを含む開発環境用のライセンスデータを生成する
 */
export class LicenseGenerator extends BaseGenerator {
  private readonly ADDITIONAL_LICENSE_COUNT = 10; // hisol以外の追加ライセンス数

  constructor(prisma: PrismaClient) {
    super(prisma, 'LicenseGenerator');
  }

  /**
   * 生成器名を取得する
   * @returns 生成器名
   */
  getGeneratorName(): string {
    return 'ライセンスデータ';
  }

  /**
   * 生成予定のデータ数を取得する
   * @returns 生成予定数（hisol + 追加ライセンス）
   */
  getExpectedCount(): number {
    return 1 + this.ADDITIONAL_LICENSE_COUNT;
  }

  /**
   * 既存のライセンスデータをクリーンアップする
   * 関連するデータも含めて削除する
   */
  async cleanup(): Promise<void> {
    try {
      // 関連データを先に削除（外部キー制約のため）
      await this.prisma.licensePlan.deleteMany({});
      await this.prisma.server.deleteMany({});
      await this.prisma.task.deleteMany({});
      await this.prisma.operationLog.deleteMany({});
      await this.prisma.notification.deleteMany({});
      
      // ライセンスデータを削除
      const deleteResult = await this.prisma.license.deleteMany({});
      console.log(`既存ライセンスデータを削除しました (${deleteResult.count}件)`);
    } catch (error) {
      console.error('ライセンスデータのクリーンアップに失敗しました', error);
      throw error;
    }
  }

  /**
   * ライセンスデータを生成する
   * hisolライセンスと追加の開発用ライセンスを生成する
   * @returns 生成されたデータの数
   */
  async generate(): Promise<number> {
    try {
      let generatedCount = 0;

      // hisolライセンスを生成
      await this.generateHisolLicense();
      generatedCount++;
      console.log('hisolライセンスを生成しました');

      // 追加の開発用ライセンスを生成
      const additionalCount = await this.generateAdditionalLicenses();
      generatedCount += additionalCount;

      console.log(`合計 ${generatedCount} 件のライセンスを生成しました`);
      return generatedCount;
    } catch (error) {
      console.error('ライセンスデータ生成中にエラーが発生しました', error);
      throw error;
    }
  }

  /**
   * hisolライセンスを生成する
   * 設定ファイルからhisolライセンスデータを読み込んで生成する
   */
  private async generateHisolLicense(): Promise<void> {
    try {
      const hisolLicense = await this.prisma.license.create({
        data: {
          licenseId: hisolLicenseData.licenseId,
          type: hisolLicenseData.type,
          expiredAt: new Date(hisolLicenseData.expiredAt),
          maxClients: hisolLicenseData.maxClients,
          isMaintenance: hisolLicenseData.isMaintenance,
          isDisabled: hisolLicenseData.isDisabled,
          basicPlan: hisolLicenseData.basicPlan,
        },
      });

      console.log(`hisolライセンスを作成しました (ID: ${hisolLicense.licenseId})`);
    } catch (error) {
      console.error('hisolライセンス生成に失敗しました', error);
      throw error;
    }
  }

  /**
   * 追加の開発用ライセンスを生成する
   * 様々なタイプのライセンスを生成してテスト環境を充実させる
   * @returns 生成されたライセンス数
   */
  private async generateAdditionalLicenses(): Promise<number> {
    const licenses = [];

    // ライセンスタイプのLOVデータを取得
    const licenseTypes = await this.getLicenseTypes();

    for (let i = 1; i <= this.ADDITIONAL_LICENSE_COUNT; i++) {
      const licenseType = this.selectLicenseType(licenseTypes);
      const company = this.faker.company();
      const isExpired = this.faker.randomBoolean(0.2); // 20%の確率で期限切れ
      
      const license = {
        licenseId: `dev-${i.toString().padStart(3, '0')}`,
        type: licenseType.code,
        expiredAt: isExpired 
          ? this.faker.pastDate(30) // 過去30日以内の期限切れ
          : this.faker.futureDate(365), // 未来365日以内の有効期限
        maxClients: this.getRandomMaxClients(licenseType.code),
        isMaintenance: this.faker.randomBoolean(0.1), // 10%の確率でメンテナンス中
        isDisabled: this.faker.randomBoolean(0.05), // 5%の確率で無効
        basicPlan: this.getRandomBasicPlan(),
      };
      
      licenses.push(license);
    }

    // バッチで作成
    await this.prisma.license.createMany({
      data: licenses,
    });

    console.log(`${licenses.length}件の追加ライセンスを生成しました`);
    return licenses.length;
  }

  /**
   * ライセンスタイプに応じた最大クライアント数を取得する
   * @param licenseType ライセンスタイプ
   * @returns 最大クライアント数
   */
  private getRandomMaxClients(licenseType: string): number {
    if (licenseType === 'LICENSE_TYPE.TRIAL') {
      // 評価版は少ないクライアント数
      return this.faker.randomInt(5, 50);
    } else {
      // 製品版はより多いクライアント数
      const options = [100, 250, 500, 1000, 2000, 5000];
      return options[Math.floor(Math.random() * options.length)];
    }
  }

  /**
   * ランダムな基本プランを取得する
   * @returns 基本プラン名
   */
  private getRandomBasicPlan(): string {
    const plans = ['LIGHT_A', 'LIGHT_B', 'STANDARD', 'PREMIUM', 'ENTERPRISE'];
    return plans[Math.floor(Math.random() * plans.length)];
  }

  /**
   * ライセンスの統計情報を取得する
   * @returns 統計情報
   */
  async getStatistics(): Promise<{
    totalCount: number;
    prodCount: number;
    trialCount: number;
    expiredCount: number;
    maintenanceCount: number;
    disabledCount: number;
    hisolExists: boolean;
  }> {
    const [
      totalCount,
      prodCount,
      trialCount,
      expiredCount,
      maintenanceCount,
      disabledCount,
      hisolLicense,
    ] = await Promise.all([
      this.prisma.license.count(),
      this.prisma.license.count({ where: { type: 'PROD' } }),
      this.prisma.license.count({ where: { type: 'TRIAL' } }),
      this.prisma.license.count({ where: { expiredAt: { lt: new Date() } } }),
      this.prisma.license.count({ where: { isMaintenance: true } }),
      this.prisma.license.count({ where: { isDisabled: true } }),
      this.prisma.license.findUnique({ where: { licenseId: 'hisol' } }),
    ]);

    return {
      totalCount,
      prodCount,
      trialCount,
      expiredCount,
      maintenanceCount,
      disabledCount,
      hisolExists: !!hisolLicense,
    };
  }

  /**
   * hisolライセンスを取得する
   * @returns hisolライセンスデータまたはnull
   */
  async getHisolLicense(): Promise<any | null> {
    return await this.prisma.license.findUnique({
      where: { licenseId: 'hisol' },
      include: {
        servers: true,
        tasks: true,
        operationLogs: true,
        notifications: true,
        licensePlans: {
          include: {
            plan: true,
          },
        },
      },
    });
  }

  /**
   * ライセンスタイプのLOVデータを取得する
   * @returns ライセンスタイプの配列
   */
  private async getLicenseTypes(): Promise<any[]> {
    return await this.prisma.lov.findMany({
      where: {
        parentCode: 'LICENSE_TYPE',
        isEnabled: true,
      },
      select: {
        code: true,
        name: true,
        value: true,
      },
    });
  }

  /**
   * ライセンスタイプを選択する
   * @param licenseTypes ライセンスタイプの配列
   * @returns 選択されたライセンスタイプ
   */
  private selectLicenseType(licenseTypes: any[]): any {
    if (licenseTypes.length === 0) {
      // デフォルト値
      return { code: 'LICENSE_TYPE.PROD', value: '製品版' };
    }

    // 70%の確率で製品版、30%で評価版
    const prodType = licenseTypes.find(type => type.code === 'LICENSE_TYPE.PROD');
    const trialType = licenseTypes.find(type => type.code === 'LICENSE_TYPE.TRIAL');

    if (this.faker.randomBoolean(0.7) && prodType) {
      return prodType;
    } else if (trialType) {
      return trialType;
    } else {
      return licenseTypes[0];
    }
  }

  /**
   * 有効なライセンスの一覧を取得する
   * @returns 有効なライセンスの配列
   */
  async getActiveLicenses(): Promise<any[]> {
    return await this.prisma.license.findMany({
      where: {
        isDisabled: false,
        expiredAt: { gt: new Date() },
      },
      orderBy: {
        licenseId: 'asc',
      },
    });
  }
}
