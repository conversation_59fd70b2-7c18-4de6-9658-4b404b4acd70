describe("認証と認可のテスト", () => {
  const validCredentials = {
    userId: "hitachi.taro.aa",
    password: "changeit!@#",
  };

  const invalidCredentials = {
    userId: "invaliduser",
    password: "invalidpassword",
  };

  it("有効なユーザー名とパスワードでログインできること", () => {
    cy.visit("/login");

    cy.get("#userId").type(validCredentials.userId);
    cy.get("#password").type(validCredentials.password);

    cy.get("button").contains("ログイン").click();
    cy.wait(1000);

    cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/servers");
  });

  it("無効なユーザー名とパスワードではログインできないこと", () => {
    cy.visit("/login");

    cy.get("#userId").type(invalidCredentials.userId);
    cy.get("#password").type(invalidCredentials.password);

    cy.get("button").contains("ログイン").click();

    cy.contains("ユーザIDもしくはパスワードに誤りがあります。").should(
      "be.visible",
    );
  });

  it("ログアウトが正常に動作すること", () => {
    cy.visit("/login");
    cy.get("#userId").type(validCredentials.userId);
    cy.get("#password").type(validCredentials.password);

    cy.get("button").contains("ログイン").click();
    cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/servers");

    cy.get("button").contains("ログアウト").click();
    cy.get(
      "#logout-modal > .w-full > .relative > .flex-row-reverse > button.bg-gradient-dark",
    ).click();

    cy.url().should("eq", Cypress.config().baseUrl + "/login");
  });

  it("セッションがない場合、サーバ一覧画面にリダイレクトされること", () => {
    cy.visit("/dashboard/servers");

    cy.url().should("eq", Cypress.config().baseUrl + "/login");
  });

  it("セッションがある場合、サーバ一覧画面にアクセスできること", () => {
    cy.visit("/login");
    cy.get("#userId").type(validCredentials.userId);
    cy.get("#password").type(validCredentials.password);
    cy.get("button").contains("ログイン").click();
    cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/servers");

    cy.visit("/");

    cy.contains("サーバ一覧").should("be.visible");
  });

  it("ブラウザを閉じて再度開いた場合、セッションが無効でログインが必要", () => {
    cy.visit("/login");
    cy.get("#userId").type(validCredentials.userId);
    cy.get("#password").type(validCredentials.password);
    cy.get("button").contains("ログイン").click();
    cy.url().should("eq", Cypress.config().baseUrl + "/dashboard/servers");

    cy.wait(3000);
    cy.clearCookies();
    cy.clearLocalStorage();

    cy.visit("/");

    cy.url().should("include", "/login");

    cy.contains("ログイン").should("be.visible");
  });
});
