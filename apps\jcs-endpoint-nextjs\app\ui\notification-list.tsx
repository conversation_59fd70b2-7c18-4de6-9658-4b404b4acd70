/**
 * @file notification-list.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

"use client";

import { Notification } from "@prisma/client";
import useNotifications from "../hooks/use-notifications";
import Spinner from "./spinner";
import { useEffect } from "react";
import { PORTAL_ERROR_MESSAGES } from "../lib/definitions";

interface NotificationListProps {
  api: string;
  onError?: (error: string) => void;
}

// お知らせコンポーネント
export default function NotificationList({
  api,
  onError,
}: NotificationListProps) {
  const {
    data: fetchedNotifications = [],
    isLoading,
    error,
  } = useNotifications(api); // お知らせデータの取得
  useEffect(() => {
    if (fetchedNotifications && fetchedNotifications.error) {
      onError && onError(fetchedNotifications.error);
    } else if (error) {
      onError && onError(PORTAL_ERROR_MESSAGES.EMEC0007);
    }
  }, [isLoading]);
  return (
    <>
      {isLoading ? (
        <Spinner />
      ) : fetchedNotifications.length === 0 || fetchedNotifications.error ? (
        // お知らせがない場合の表示
        <p>お知らせはありません。</p>
      ) : (
        // お知らせがある場合の表示
        <>
          {fetchedNotifications.map((notification: Notification) => (
            <div
              key={notification.id}
              className="whitespace-pre-line"
              dangerouslySetInnerHTML={{
                __html: `${notification.publishedAt} ${notification.content}`,
              }}
            />
          ))}
        </>
      )}
    </>
  );
}
