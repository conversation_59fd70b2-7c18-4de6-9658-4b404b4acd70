# 数据模型: 操作日志 (OperationLog)

*   **表名 (逻辑名)**: `OperationLog`
*   **物理表名 (Prisma Model)**: `OperationLog` (参考项目根目录下 `prisma/schema.prisma` 文件获取权威定义)
*   **对应UI界面**: 操作日志列表 (操作ログ一覧)
*   **主要用途**: 存储可供用户下载的操作日志文件的元数据。这包括由后台任务自动导出的日志，以及可能存在的其他类型（如手动上传）的操作日志。

## 1. 字段定义

| 字段名 (Prisma Model) | 数据类型 (SQL参考) | PK | FK | UQ | Nullable | Default | 中文描述 | 日文名称 (界面参考) |
|--------------------|--------------|----|----|----|----------|---------|------|-------------|
| `id` | `String` | ● | - | - | - | `cuid()` | 主键。CUID格式，由系统自动生成。 | (内部ID) |
| `name` | `String` | - | - | - | - | - | 用户在界面上看到的日志文件名，也作为在Azure Blob Storage中存储时的文件名。 <br> **格式 (任务生成时)**: `{Task.taskName}_{sequence}.zip`。其中 `{Task.taskName}` 通常为 `{Server.name}-{TaskType.name}-{YYYYMMDDHHmmss}`，`{sequence}` 为序数。 <br> **格式 (非任务生成时)**: 需另行约定，例如 `oplog_{開始年月日YYYYMMDD}-{終了年月日YYYYMMDD}_{シーケンス番号}.zip`。 | ログ名 |
| `size` | `Int` | - | - | - | - | - | 日志文件的大小，单位为字节 (Bytes)。 | サイズ |
| `createdAt` | `DateTime` | - | - | - | - | `now()` | 此操作日志元数据记录在系统中**创建**的时间。 | 登録日時 |
| `retentionAt` | `DateTime?` | - | - | - | Yes | - | **可选的保管期限**。主要用于**非任务自动生成**的操作日志（此时 `generatedByTaskId` 为 `NULL`），定义其可供下载的截止日期和时间。对于由后台任务自动导出的操作日志，此字段通常为 `NULL`，其保留由关联任务的保留策略决定。 | 保管期限 |
| `licenseId` | `String` | - | ● | - | - | - | **外键 (逻辑)**。关联到 `License` 表的 `licenseId` (业务键)。 | (内部关联) |
| `generatedByTaskId` | `String?` | - | ● | - | Yes | - | **可选外键**。关联到 `Task` 表的 `id` (主键)。如果此操作日志文件是由后台任务生成的，则此字段存储该任务的ID。如果是非任务生成的日志（例如手动上传），则此字段为 `NULL`。 | (内部关联) |
| `fileName` | `String` | - | - | - | - | `""` | 用于记录上传时的原始文件名或特定别名。对于由系统后台任务自动导出的操作日志，此字段通常不使用或保持默认空字符串。 | (内部使用/旧字段) |

*PK: Primary Key, FK: Foreign Key, UQ: Unique Constraint*
*(数据类型参考项目代码库中 `prisma/schema.prisma` 文件的权威定义。SQL参考仅为示意。)*
*(Nullable列中的“Yes”基于Prisma Schema中字段类型后的 `?` 标记。)*

## 2. 关系 (Relations)

本表与其他核心数据模型的关系，已在项目代码库的 `prisma/schema.prisma` 文件中通过关系字段明确定义。

*   **对 `License` (`license`)**: 多对一关系。通过 `licenseId` 字段（外键，引用 `License.licenseId` 唯一键）与`License`表关联。Prisma Schema中定义为 `@relation(fields: [licenseId], references: [licenseId], onDelete: NoAction, onUpdate: NoAction)`。
*   **对 `Task` (`generatingTask`)**: 可选的多对一关系。通过 `generatedByTaskId` 字段（可选外键，引用 `Task.id` 主键）与`Task`表关联。Prisma Schema中定义为 `Task? @relation(fields: [generatedByTaskId], references: [id], onDelete: NoAction, onUpdate: NoAction)`。

**关于外键约束策略的说明**: `onDelete: NoAction, onUpdate: NoAction` 表示如果关联的 `License` 或 `Task` 记录被删除或其被引用的键被更新，数据库层面不会自动对 `OperationLog` 表做任何操作。数据完整性的维护由应用层面逻辑负责。

## 3. Azure Blob Storage 路径约定

*   操作日志文件在Azure Blob Storage中的实际存储路径遵循预定义规则动态构建。
*   **当日志由任务生成时 (`generatedByTaskId` 非空)**:
    *   容器名: 由环境变量 `AZURE_STORAGE_CONTAINER_OPLOGS_NAME` 定义 (例如 `oplogs`)。
    *   路径结构: `{licenseId}/{generatedByTaskId}/{OperationLog.name}`
    *   示例完整路径: `oplogs/hisol/task_cuidabcdefg12345/MyServer01-操作ログのエクスポート-20231026153000_001.zip`
*   **对于非任务生成的日志 (`generatedByTaskId` 为空)**:
    *   容器名: 由环境变量 `AZURE_STORAGE_CONTAINER_OPLOGS_NAME` 定义 (例如 `oplogs`)。
    *   路径结构: `{licenseId}/{OperationLog.name}` (或其他约定的不含任务ID的路径)
    *   示例完整路径: `oplogs/hisol/oplog_20231110-125959_005.zip`

## 4. 索引 (Indexes)

根据 `prisma/schema.prisma` 文件中的定义，本表包含以下索引：

*   **主键**: `id` (由 `@id` 隐式创建索引)。
*   **辅助索引**:
    *   `@@index([licenseId])`: 支持按许可证ID查询操作日志。
    *   `@@index([generatedByTaskId])`: 支持快速查找由特定任务生成的所有日志文件，或所有非任务生成的日志文件 (当 `generatedByTaskId` 为 `NULL` 时，取决于数据库对可空列索引的处理方式和查询写法)。

## 5. 备注 (Notes)
*   本表记录的生命周期管理需区分情况：
    *   **由任务生成的日志 (`generatedByTaskId` 非空)**: 其保留和删除完全依赖于其关联的 `Task` 记录的保留策略。当关联的 `Task` 记录因达到保留上限而被系统自动清理时，本表中所有由该`Task`生成的`OperationLog`记录以及它们在Azure Blob Storage中对应的文件也应被联动删除。此联动删除逻辑由应用层（具体为`TaskExecuteFunc`中执行的保留策略清理逻辑）负责实现。此时，这些 `OperationLog` 记录的 `retentionAt` 字段应为 `NULL`。
    *   **非任务生成的日志 (`generatedByTaskId` 为空)**: 其保留期限由本记录的 `retentionAt` 字段控制。系统需要有独立的后台作业或机制来定期清理已超过 `retentionAt` 的此类日志及其关联的Blob文件。