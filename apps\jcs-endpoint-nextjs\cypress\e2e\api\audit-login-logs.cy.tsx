describe("API エンドポイントの可用性のテスト", () => {
  describe("API - ログイン監査ログの記録", () => {
    const validCredentials = {
      userId: "hitachi.taro.aa",
      password: "changeit!@#",
    };

    describe("セッションが存在しない場合", () => {
      it("セッションが存在せず、かつ誤ったタイプの場合、エラーが返される", () => {
        cy.request({
          method: "POST",
          url: "/api/audit-login-logs",
          body: {
            userId: "user123",
            auditType: "INVALID_TYPE",
          },
          failOnStatusCode: false,
        }).then((response) => {
          expect(response.status).to.eq(401);
        });
      });
    });

    describe("セッションが存在する場合", () => {
      // @ts-ignore
      let cookies;

      beforeEach(() => {
        Cypress.Cookies.debug(true);
        cy.visit("/login");
        cy.get("#userId").type(validCredentials.userId);
        cy.get("#password").type(validCredentials.password);
        cy.get("button").click();

        cy.wait(3000);
        cy.getCookies()
          .should("have.length.gt", 0)
          .then((cookiesArray) => {
            cookies = cookiesArray;
          });
      });

      it("正常にログアウトイベントが記録される", () => {
        cy.request({
          method: "POST",
          url: "/api/audit-login-logs",
          headers: {
            // @ts-ignore
            Cookie: `${cookies[0].name}=${cookies[0].value}`,
          },
          body: {
            auditType: "LOGOUT",
            loginMessage: "CT テストメッセージ",
          },
        }).then((response) => {
          expect(response.status).to.eq(200);

          expect(response.body.message).to.eq(
            "監査ログが正常に記録されました。",
          );
        });
      });

      it("無効なパラメーターが提供された場合、エラーが返される", () => {
        cy.request({
          method: "POST",
          url: "/api/audit-login-logs",
          headers: {
            // @ts-ignore
            Cookie: `${cookies[0].name}=${cookies[0].value}`,
          },
          body: {
            auditType: "INVALID_TYPE",
          },
          failOnStatusCode: false,
        }).then((response) => {
          expect(response.status).to.eq(400);
          expect(response.body.error).to.eq("Params Error");
        });
      });
    });
  });
});
