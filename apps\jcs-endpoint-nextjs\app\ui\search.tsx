/**
 * @file search.tsx
 * @description 
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { HiOutlineSearch, HiX } from "react-icons/hi";

// フィルターコンポーネント
export default function Search() {
  const searchParams = useSearchParams();
  const { replace } = useRouter();
  const pathname = usePathname();
  const [filter, setFilter] = useState("");

  useEffect(() => {
    setFilter(searchParams.get("filter")?.toString() || "");
  }, [searchParams]);

  const handleSearch = () => {
    const params = new URLSearchParams(searchParams);

    params.set("page", "1");

    if (filter) {
      params.set("filter", filter);
    } else {
      params.delete("filter");
    }
    replace(`${pathname}?${params.toString()}`);
  };

  const handleReset = () => {
    const params = new URLSearchParams(searchParams);

    setFilter("");
    params.set("page", "1");
    params.delete("filter");
    replace(`${pathname}?${params.toString()}`);
  };

  return (
    <div className="flex justify-center">
      <label htmlFor="table-search" className="sr-only">
        Search
      </label>
      <div className="relative mr-2 inline-block">
        <input
          type="text"
          id="small-input"
          className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2 text-gray-900 focus:border-gray-300 focus:ring-gray-300 sm:text-xs"
          placeholder="フィルター"
          value={filter}
          onChange={(e) => {
            setFilter(e.target.value);
          }}
        ></input>
      </div>
      <button
        type="button"
        className="toggle-full-view mr-2 flex h-9 w-9 items-center justify-center rounded-lg border border-gray-200 bg-white text-xs font-medium text-gray-700 hover:bg-gray-100 hover:text-blue-700 focus:ring-gray-300"
        onClick={() => {
          handleSearch();
        }}
      >
        <HiOutlineSearch className="text-lg text-gray-500" />
        <span className="sr-only">search</span>
      </button>
      <button
        type="button"
        className="toggle-full-view mr-2 flex h-9 w-9 items-center justify-center rounded-lg border border-gray-200 bg-white text-xs font-medium text-gray-700 hover:bg-gray-100 hover:text-blue-700 focus:ring-gray-300"
        onClick={() => {
          handleReset();
        }}
      >
        <HiX className="text-lg text-gray-500" />
        <span className="sr-only">clear</span>
      </button>
    </div>
  );
}
