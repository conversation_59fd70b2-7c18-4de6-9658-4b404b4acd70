## タスク中止関数 (TaskCancellationFunc) 詳細設計

### 概要

#### 責務
TaskCancellationFunc は、Azure Service Bus の TaskControlQueue からタスク中止要求メッセージを受信し処理するAzure Functionsの関数である。本関数の主な責務は以下の通りである。

1.  受信したメッセージを解析し、中止対象のタスクID (`taskId`) を取得する。
2.  データベースの `Task` テーブルから、取得した `taskId` に該当するタスクの現在のステータス (Task.status) を確認する。
3.  タスクの現在の状態に基づき、中止処理を実行する。
    *   ステータスが「中止待ち」(`PENDING_CANCELLATION`) の場合：タスクのステータスを「中止」(`CANCELLED`) に更新し、タスク詳細 (`resultMessage`) に中止成功を示すメッセージを設定する。
    *   ステータスが「中止待ち」(`PENDING_CANCELLATION`) 以外の場合：エラーログを記録して、処理を終了する。
4.  データベース操作等でエラーが発生した場合は、エラーログを記録し、Service Busの再配信メカニズムによってリトライする。"

#### トリガー
Azure Service Bus - TaskControlQueue キューメッセージ。 

#### 主要入力
*   TaskControlQueue から受信するJSON形式のメッセージ。メッセージボディの主な構造は以下の通り。
"{
  ""taskId"": ""string (UUID)""
}"


*   Azure SQL Database のTaskテーブルからの読み取り。

#### 主要出力
Azure SQL Database のTaskテーブルのレコード更新（status, resultMessageフィールド）。

※TaskControlQueue の名称は、環境変数 SERVICE_BUS_TASK_CONTROL_QUEUE_NAME によって構成される。
※データベース接続には環境変数 `MSSQL_PRISMA_URL` で指定される接続文字列が使用される。

### 主要処理フロー

```mermaid
graph TD
    A["開始: TaskControlQueueメッセージ受信<br/>(taskId)"] --> B["メッセージ解析・<br/>taskId取得"];
    B -- "不足/不正" --> BA["エラーログ記録<br/>例外throwでリトライ"];
    B -- "正常" --> C["DBよりタスク情報取得<br/>(taskId使用)"];
    C -- "取得失敗/taskレコードなし" --> BA;
    C -- "Task取得成功" --> D{"タスクの現在のステータス<br/>(Task.status) "};
    D -- "PENDING_CANCELLATION以外" --> DA["エラーログ記録、例外throw、処理終了"];
    D -- "PENDING_CANCELLATION" --> E["タスク情報をCANCELLED、<br/>EMET0004に更新"];
    
    E -- "DB更新成功" --> J["成功ログ記録"];
    E -- "DB更新失敗" --> BA;

    DA --> Z["終了"];
    BA --> Z;
    J --> Z;
```

#### 処理フロー詳細

1.  Azure Service Bus の `TaskControlQueue` からタスク中止要求メッセージを受信し、JSON形式から解析する。受信したメッセージ内容（特に `taskId`）をログに出力する。
2.  メッセージ内の `taskId` の存在とフォーマットを確認する。
    *   確認エラーの場合：エラー詳細をログに記録した後、例外をthrowして処理を終了する。キューメッセージが再配信され、本関数がリトライする。
3.  `taskId` を使用して、データベースの `Task` テーブルから該当タスクのstatus（ステータス）、updatedAt（最終更新日時） を取得する。
    *   取得失敗、またはタスクレコードが存在しない場合：エラー詳細をログに記録した後、例外をthrowして処理を終了する。キューメッセージが再配信され、本関数がリトライする。
4.  取得したタスクの現在の status に基づき、以下の分岐処理を行う。
    *   **`PENDING_CANCELLATION`の場合**:
        タスクID = 入力パラメータのtaskId、最終更新日時 = ステップ3.で取得した最終更新日時の条件で、該当 Task レコードの status（ステータス） を CANCELLED に、resultMessage（タスク詳細）を
        EMET0004「ユーザーによってタスクが中止されました。」に更新する。
        DB更新失敗/更新件数が0件の場合：エラー詳細をログに記録した後、例外をthrowして処理を終了する。キューメッセージが再配信され、本関数がリトライする。
    *   **`PENDING_CANCELLATION`以外の場合**:
        エラー詳細をログに記録した後、例外をthrowして処理を終了する。キューメッセージが再配信され、本関数がリトライする。
5.  処理成功のログを記録し、本関数の処理を終了する。

### 主要なデータ及び外部サービスとの対話詳細

#### Azure Service Bus (`TaskControlQueue`) からのメッセージ受信
*   本Functionは `TaskControlQueue` のメッセージをトリガーとして起動する。
*   メッセージはJSON形式であり、その主な構造は本章「主要入力」セクションを参照。
*   タスク中止関数が処理成功した場合メッセージは完了確認（ACK）が取れて、キューから削除される。 タスク中止関数が例外やタイムアウトによって処理失敗した場合はメッセージが破棄され、Azure Service Busによって再配信されて、タスク中止関数が再度起動される。TaskControlQueueに設定されている最大配信数の3回になってもタスク中止関数が処理失敗の場合メッセージはTaskControlQueueのDLQへ送られる。

#### データベース (`Task` テーブル) との対話
*   **読み取り**: `taskId` をキーとして `Task` テーブルからレコードを一件取得する。
*   **更新**: 該当レコードの `status`, `resultMessage` フィールドを更新する。

### エラー処理メカニズム

| エラーシナリオ | 関連エラーコード | 対処・補償ロジック |
|:---|:---|:---|
| メッセージ基本パラメータ確認エラー (`taskId`不足/不正) | - | エラーログ記録。例外throw。処理終了。 |
| 対象タスク情報 (`Task`レコード) がデータベースに存在しない/DB読み取り失敗 | - | エラーログ記録。例外throw。処理終了。 |
| タスクステータスがPENDING_CANCELLATION以外 | - | エラーログ記録。例外throw。処理終了。 |
| DB更新失敗/更新件数が0件 | - | エラーログ記録。例外throw。処理終了。 |
| 予期せぬ内部エラー | - | エラーログ記録。例外throw。処理終了。 |

*   **タイムアウトについて**
    Azure Functionsがタスク中止関数の実行時間を監視し、host.jsonのfunctionTimeoutプロパティで設定されたタイムアウト時間（5分）になっても関数がまだ終了していない場合、Azure Functionsは関数の実行を強制的に中止する。
*   **リトライについて**
    Azure Functionsがタスク中止関数に例外が発生したこと（catchされていない例外）或いはタスク中止関数がタイムアウトしたことを検知した場合はトリガーとなったメッセージを破棄する。その後メッセージはAzure Service Busによって再配信され、タスク中止関数が再度起動される。TaskControlQueueに設定されている最大配信数の3回（2回までリトライ）になってもタスク中止関数が処理失敗の場合メッセージはTaskControlQueueのDLQへ送られて、タスク中止タイムアウト関数が起動される。