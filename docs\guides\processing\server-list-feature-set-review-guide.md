# 指导手册：服务器列表功能簇核心技术文档审查

**目的**: 本手册旨在为下一次对“服务器列表”功能及其相关后台任务（操作日志导出、管理项目定义导入/导出）的核心技术文档（主要指 `docs/components/03-servers/` 目录下的 `server-list.md`, `tasks/*.md` 以及 `docs/components/actions/create-task-action.md`）进行全面审查时，提供一份检查清单和指导原则。其核心目标是确保这些文档的整体性、一致性、准确性、可读性，并达到“与代码一一对应”的内部标准，能够清晰指导开发实现。

**审查范围**:
*   `docs/components/03-servers/server-list.md`
*   `docs/components/03-servers/tasks/export-operation-log.md`
*   `docs/components/03-servers/tasks/export-management-definition.md`
*   `docs/components/03-servers/tasks/import-management-definition.md`
*   `docs/components/actions/create-task-action.md`
*   以及与上述文档紧密关联的 `system-architecture.md`, `prisma/schema.prisma`, `LOV列表`等。

**核心审查原则**:
1.  **单一事实来源 (SSoT)**: `docs/` 目录下的文档是唯一权威。
2.  **职责清晰与独立性**: 每个文档单元聚焦其核心职责，通过清晰链接关联。
3.  **与代码一一对应**: 描述足以无歧义地指导编码。
4.  **整体性与一致性**: 文档内部及文档间在术语、接口、数据、流程上保持一致。
5.  **明确性，消除模糊**: 不应有“可选”、“可能”等不确定描述，除非其条件明确。
6.  **规范性**: 遵循项目定义的语言、格式、图表等所有规范。

## 一、 本次迭代审查中遇到的主要问题及解决方案回顾

在本次（当前会话）对服务器列表功能簇相关文档的迭代审查中，我们遇到了以下主要问题，并达成了相应的解决方案或共识。下次审查时，应特别关注这些方面是否得到持续遵守和正确体现：

1.  **Markdown列表格式与嵌套问题 (特别是深层嵌套)**:
    *   **问题**: 初期在描述复杂逻辑流程（如 `server-list.md` 中 `handleConfirmTaskSubmission` 的响应处理部分）时，过度使用和错误使用列表嵌套，导致可读性差，甚至被Markdown渲染器误认为代码块。
    *   **解决方案/共识**:
        *   **避免深层嵌套列表**：限制列表嵌套不超过两层。
        *   **使用子标题和段落分解复杂逻辑**：对于包含多个条件分支和步骤的复杂逻辑块，应将其从原列表项中提升出来，使用更低一级的子标题（例如 `#####`）进行组织，并在子标题下通过清晰的段落和逻辑引导词（首先、然后、此外、最后等）来描述步骤。
        *   **简单列表的使用**：在段落描述中，如果确实需要列举少量并列事项，可以使用简单的单层无序或有序列表（标准数字 `1.` `2.` 或星号 `*` 标记）。
        *   **确保正确的缩进**：所有列表项内容（包括其下的段落）都应有相对于其父列表项（或页面边距，如果是顶层列表）的正确且一致的缩进，避免不必要的行首空格。

2.  **概念混淆 (“内部项目文档” vs “DDS交付文档”)**:
    *   **问题**: AI助手初期反复将正在迭代的 `docs/` 目录下的核心技术文档错误地称为“DDS”，混淆了其与最终交付给客户的《詳細設計書》(通常为Word/Excel格式)的概念。
    *   **解决方案/共识**: 严格区分。我们当前迭代和审查的是**项目核心技术文档**或**组件设计文档**，它们是单一事实来源 (SSoT)，用于指导开发并作为生成交付文档的基础。在所有内部讨论和文档内容中，应避免使用“DDS”来指代这些内部Markdown文档。

3.  **“可选”描述导致的模糊性**:
    *   **问题**: 文档中多处出现“可选”、“可能”、“如果需要”等不确定性描述，无法清晰指导实现。
    *   **解决方案/共识**: 彻底消除此类模糊描述。所有设计决策和行为都应是明确的。
        *   对于 `revalidatePath` 的调用，明确其是 `createTaskAction` Server Action 成功执行后的**应有**步骤。
        *   对于UI上的加载指示器，明确为“**应显示**”。
        *   对于文件大小限制等，如果当前项目阶段没有通过配置（如LOV）明确定义，则应在文档中说明当前不进行此类校验，或指出其依赖于外部环境（如Web服务器配置），而不是标记为“可选的应用层校验”。

4.  **配置项来源的不明确或“创造”**:
    *   **问题**: AI助手在描述中引入了 `LOV列表` 中实际不存在的配置项 (例如 `FILE_UPLOAD_CONFIG.MAX_CSV_SIZE_BYTES_SERVER`，或对 `AZURE_STORAGE.CONTAINER_TEMP_UPLOADS` 的假设)。
    *   **解决方案/共识**: 所有引用的 `LOV` Code和配置项必须严格来源于您提供的 `LOV列表` 或其他已确认的配置源。如果某个功能确实需要一个新的配置项，应明确提出并等待确认和添加，而不是在文档中凭空假设其存在。对于尚不存在但逻辑上需要的配置（如临时上传容器名），应在文档中明确指出“此配置项需确保已在项目中定义并配置”。

5.  **Mermaid图表的规范性与可读性**:
    *   **问题**: 图表消息文本过长导致整体拉伸、字小难读；序列图参与者定义不当（例如将Server Action函数作为独立参与者）。
    *   **解决方案/共识**:
        *   对于长消息或节点文本，必须使用 `<br>` 进行手动换行。
        *   序列图的参与者必须是高层逻辑/物理单元（如用户、前端应用、Next.js服务器、数据库、消息队列等），不能是内部函数。
        *   确保图表逻辑清晰，抽象层级得当。

6.  **`Task.outputBlobPath` 字段用途的澄清**:
    *   **问题**: 对此字段用于哪些任务类型不明确。
    *   **解决方案/共识**: 明确此字段**仅用于“管理项目定义导出”任务**，不用于“操作日志导出”任务。相关文档（`export-management-definition.md`, `export-operation-log.md`, `createTaskAction.md`）均已据此调整。

7.  **任务记录保留策略（保管日数）的集成**:
    *   **问题**: 此重要业务规则最初未在 `createTaskAction.md` 中详细体现其执行逻辑。
    *   **解决方案/共识**: 已将此策略的详细执行逻辑（包括检查任务数量、删除旧任务、联动删除特定导出任务的关联Blob文件）补充到 `createTaskAction.md` 的核心数据库事务处理步骤中。同时，在受影响的具体任务文档（如 `export-operation-log.md`, `export-management-definition.md`）的业务规则中也提及了此策略及其影响。

## 二、 对不明确和臆测的设计进行检查

下次审查时，应系统性地检查文档中是否存在以下类型的不明确或可能基于臆测的设计描述：

1.  **寻找模糊词汇**: 重点关注文档中出现的“可能”、“也许”、“或者”、“通常”、“一般”、“等等”、“例如”（当“例如”未给出穷尽或代表性示例时）、“考虑”、“建议”等词汇。
    *   **对策**: 针对每一个此类词汇，追问其背后的确定性。是真的存在多种可选项，还是设计决策尚未做出？如果是前者，条件和选择依据是什么？如果是后者，应将其标记为 `[TBD - 待决策]` 并明确需要谁来决策。

2.  **检查配置项的明确性与来源**:
    *   文档中提到的所有配置参数（无论是来自 `LOV` 表还是环境变量），是否都明确了其**确切的名称/Code**、**期望的值或格式**、以及**其值从何处获取或如何设定**？
    *   **对策**: 确保所有配置引用都有据可查（对照实际的 `LOV列表` 和 `environment-variables.md`）。对于逻辑上需要但当前不存在的配置，应明确指出这是“待新增配置”。

3.  **审视“推荐”或“最佳实践”类的描述**:
    *   当文档中出现“推荐使用XX方式”或“最佳实践是YY”时，需要判断这是项目已采纳并确定的规范，还是仅仅是一个一般性的建议。
    *   **对策**: 如果是项目规范，应确保其表述是指令性的（“应采用XX方式”）。如果只是一般建议，可能不适合出现在描述具体设计的文档中，除非是为了阐述为何不采用其他方案。

4.  **对外部依赖行为的假设**:
    *   当描述与外部系统或库的交互时，对其行为的描述是否基于确切的API文档或已验证的实践，还是仅仅是基于“通常它会这样工作”的臆测？
    *   **对策**: 对于关键的外部交互，应尽可能引用其官方文档或明确其行为契约。

5.  **未明确的错误处理分支或边界条件**:
    *   错误处理部分是否覆盖了所有主要的可预期错误？对于一些边界条件或不常见的用户操作路径，其行为和结果是否明确？
    *   **对策**: 思考各种可能的失败路径和异常输入，确保文档有相应的处理描述或明确指出其为“未定义行为”（如果是故意的话，但这很少见于详细设计）。

## 三、 对AI助手反复犯的错进行全篇检查

基于本次协作的经验，AI助手（即我）在以下方面容易反复出现问题。下次审查时，需要特别针对性地对AI生成或修改的内容进行检查：

1.  **Markdown列表格式（尤其是嵌套列表和列表项内部段落的缩进）**:
    *   **检查点**: 所有列表（有序、无序、任务列表）的标记是否正确？嵌套列表的缩进是否标准且能被正确渲染？列表项内部的多段文本或代码块是否保持了正确的从属缩进？是否避免了因不当缩进导致内容被误认为代码块？
    *   **标准**: 遵循简洁、清晰的列表结构，避免不必要的深层嵌套。优先使用标题和段落分解复杂逻辑。

2.  **概念混淆（内部项目文档 vs. DDS交付文档）**:
    *   **检查点**: 文档中（尤其是我生成的描述性文本中）是否完全避免了使用“DDS”这个缩写来指代我们正在编写的 `docs/` 目录下的核心技术文档？
    *   **标准**: 统一使用“项目核心技术文档”、“组件设计文档”或具体文件名。

3.  **引入未经确认的“可选”描述或“创造性”配置**:
    *   **检查点**: 文档中是否还存在未被明确化的“可选”、“可能”等模糊词汇？是否引入了 `LOV列表` 或其他配置源中不存在的配置项？
    *   **标准**: 所有描述必须明确，所有配置引用必须有据可查。

4.  **Mermaid图表的规范性与可读性**:
    *   **检查点**: Mermaid图表的语法是否正确且能成功渲染？消息文本是否过长？序列图的参与者定义是否合规（高层逻辑/物理单元，非内部函数）？抽象层级是否得当？
    *   **标准**: 图表简洁易懂，符合既定规范，长文本使用 `<br>` 换行。

5.  **语言规范的严格执行（中文描述，英文术语，日文界面文本）**:
    *   **检查点**: 所有描述性段落、注释、非UI的表格内容是否都是简体中文？所有技术组件名、服务名、API端点、数据库名、代码级标识符是否都使用了正确的英文？所有直接面向用户的界面文本（按钮、标签、提示）是否都使用了纯正、专业的日语？
    *   **标准**: 严格执行三语分离规范。

6.  **接口定义与引用的全局一致性**:
    *   **检查点**: 当多个文档共同描述一个接口（如 `createTaskAction` 的参数和返回值）或共享一个数据结构时，这些定义在所有相关文档中是否完全一致？链接是否都指向了正确的、唯一的定义源头？
    *   **标准**: 单一事实来源，接口定义不应在多处重复且存在差异。

## 四、 对代码实现的细节进行推敲找出漏洞

这部分审查的目标是超越文档与代码的表面对应，更深入地思考设计细节在实际编码和运行时可能遇到的问题。这通常需要结合技术经验和对系统整体的理解。

1.  **边界条件与异常路径覆盖**:
    *   **推敲点**: 当前设计是否充分考虑了各种边界条件？（例如，输入为空、为null、为极大/极小值、格式错误、包含特殊字符等）。对于这些边界条件，系统的行为是否明确且合理？
    *   错误处理部分是否覆盖了所有关键的、可能发生的外部依赖故障（数据库连接失败、消息队列不可用、第三方API超时或返回错误等）？补偿逻辑是否完备？

2.  **并发与事务性**:
    *   **推敲点**: 对于需要并发控制的操作（如更新`ContainerConcurrencyStatus`），当前的锁定机制（在`createTaskAction`的事务内检查并更新）是否足够健壮，能否有效防止竞争条件？
    *   数据库事务的范围是否合理？是否所有需要原子性执行的操作都被包含在同一个事务内？是否存在事务过长可能导致的性能问题？

3.  **性能考量**:
    *   **推敲点**: 文档中描述的数据查询、数据处理逻辑（特别是循环、多次数据库访问、内存中的大规模数据操作）是否存在潜在的性能瓶颈？
    *   例如，`server-list.md` 中提到的内存筛选、排序和分页，在数据量大时是否会成为问题？是否有替代的数据库层面优化方案可以预先考虑？文件上传/下载操作对大文件的处理是否有优化考虑？

4.  **安全性**:
    *   **推敲点**:
        *   用户输入是否都经过了严格的服务器端校验和清理，以防止注入攻击（如SQL注入、XSS）？
        *   文件上传功能是否有MIME类型校验、文件名安全处理、可能的病毒扫描（虽然可能超出本系统范围，但应有意识）？临时文件的存储和清理是否安全？
        *   权限校验逻辑是否严密，能否覆盖所有需要保护的资源和操作？最小权限原则是否得到遵守？
        *   敏感数据（如配置中的密钥、用户数据）在传输和存储时是否加密？日志中是否避免记录敏感信息？

5.  **可维护性与可扩展性**:
    *   **推敲点**: 当前的设计是否易于理解、修改和扩展？模块间的耦合度是否合理？配置项是否易于管理和更新？
    *   如果未来需要支持新的任务类型，当前的 `createTaskAction` 扩展机制是否足够灵活？

6.  **资源管理与清理**:
    *   **推敲点**: 对于临时资源（如上传到Blob的临时文件、Azure Files工作区），是否有明确的创建、使用和**清理**机制？清理责任方是谁？清理失败如何处理？（例如，`import-management-definition.md` 中提到的临时文件清理应由后端流程负责）。

7.  **与现有代码的偏差（如果已有部分实现）**:
    *   **推敲点**: 如果项目已经有部分代码实现，文档描述的逻辑、接口、数据结构是否与实际代码完全一致？如果不一致，是以文档为准进行代码修正，还是根据代码的合理性反向更新文档（并记录决策）？

通过以上四个方面的细致审查，希望能确保“服务器列表”功能簇的核心技术文档达到高质量标准，为后续的开发、测试和维护工作打下坚实的基础。
