# 后端服务托管标识实施规范

## 1. 引言与目标

本规范为 "JCS 端点资产与任务管理系统" 项目中的后端服务（`jcs-backend-services-standard` 和 `jcs-backend-services-long-running`）定义了一套标准的、安全的资源访问策略。核心目标是全面采用 **Azure 托管标识 (Managed Identity)**，以消除在应用程序配置或代码中存储任何形式的连接字符串、密钥或机密信息，从而提升系统的整体安全性与可维护性。

本规范是项目核心技术文档 (`docs/`) 的一部分，作为开发团队和AI编程助手在实施和维护后端服务时关于身份验证与授权的单一事实来源 (SSoT)。

**核心原则**:
*   **无密钥原则**: 严禁在代码或应用配置中使用任何形式的明文连接字符串或密钥来访问Azure资源。
*   **最小权限原则**: 为每个Function App的托管标识精确地授予其完成工作所必需的最小权限集。
*   **流程标准化**: 为本地开发、调试、以及最终部署提供一套统一、可重复的配置流程。

**目标读者**: 项目开发团队成员，AI编程助手。

## 2. 核心概念：托管标识

托管标识是 Microsoft Entra ID 的一项功能，它为 Azure 服务（如我们的 Function App）提供了一个自动管理的身份。此身份可用于向任何支持 Microsoft Entra 身份验证的 Azure 服务（如 Storage, Service Bus, Automation）证明自己的身份，而无需管理任何凭据。

### 2.1. 标识类型推荐

本项目推荐优先使用 **系统分配 (System-assigned)** 的托管标识。

*   **优点**: 它由 Azure 平台自动创建和管理，其生命周期与 Function App 资源完全绑定，简化了管理。
*   **适用场景**: 适用于我们的两个 Function App，因为它们的身份不需要被其他服务共享。

## 3. 标准实施流程

为后端服务配置对任一Azure资源的访问权限，必须遵循以下三个步骤。

### 步骤 1: 在 Function App 上启用托管标识 (基础设施准备)

此为一次性操作，通常在通过Azure门户或脚本创建 Function App 资源时完成。

1.  导航至目标 Function App 资源 (例如 `jcs-func-std-prod-japaneast-001`)。
2.  在左侧菜单“设置”下，选择“**标识 (Identity)**”。
3.  在“**系统分配**”选项卡中，将状态切换为“**开 (On)**”，然后点击“保存”。

### 步骤 2: 为托管标识授予 IAM 角色 (权限分配)

这是最关键的安全步骤。您必须进入**目标资源**（例如，要访问的存储账户），为Function App的托管标识分配访问权限。

下表列出了本项目中各种场景所需的标准角色：

| 访问目标 (资源类型) | 所需角色 (英文名 / 中文名) | 适用场景与说明 |
| :--- | :--- | :--- |
| **Function App 自身 (`AzureWebJobsStorage`)** | `Storage Blob Data Contributor` (存储 Blob 数据参与者) <br/> `Storage Queue Data Contributor` (存储队列数据参与者) | **两个Function App都必需**。用于平台自身的触发器管理、日志和单例锁。 |
| **Service Bus (队列/主题)** | `Azure Service Bus Data Receiver` (Azure Service Bus 数据接收者) <br/> `Azure Service Bus Data Sender` (Azure Service Bus 数据发送者) | 分别用于 Service Bus 触发器（接收）和在代码中发送消息。按需分配给需要交互的Function App。 |
| **Blob Storage (业务数据)** | `Storage Blob Data Contributor` (存储 Blob 数据参与者) | 允许在代码中对业务 Blob 进行上传、下载、删除等操作。 |
| **Azure Files (业务文件)** | **(创建自定义角色)** 或 <br/> `Storage File Data Privileged Contributor` (存储文件数据特权参与者) | 由于标准的数据平面访问角色在本环境中不可用，必须采用以下方案之一：<br/>**A) (推荐)** 创建一个仅包含文件读/写/删权限的自定义角色。<br/>**B) (备用)** 使用此高权限角色，并记录其可绕过NTFS权限的安全风险。 |
| **Automation Account (Runbook)** | `Automation Contributor` (自动化参与者) | 允许创建、查询、启动和停止 Runbook 作业。 |

**操作流程**:
1.  导航至**目标资源**。
2.  进入其“**访问控制 (IAM)**”页面。
3.  点击“添加角色分配”，并选择上表中定义的、权限最小的合适角色。
4.  在“成员”步骤中，选择“托管标识”，然后找到并选择需要此权限的 Function App。

### 步骤 3: 在 Function App 中配置应用设置

此步骤将代码中的连接与托管标识关联起来。进入 Function App 的“**配置 (Configuration)**”页面，添加以下格式的应用设置：

**`CONNECTION_NAME__<PROPERTY_TYPE>`**

**示例配置**:
```
# 用于 Service Bus 连接 (连接名为 "ServiceBusConnection")
ServiceBusConnection__fullyQualifiedNamespace = "jcs-sb-prod-japaneast-001.servicebus.windows.net"

# 用于存储账户连接 (连接名为 "StorageConnection")
StorageConnection__blobServiceUri = "jcsstorageprod001.blob.core.windows.net"
StorageConnection__fileServiceUri = "jcsstorageprod001.file.core.windows.net"

# 用于 Function App 自身的 AzureWebJobsStorage
AzureWebJobsStorage__blobServiceUri = "jcsfuncstorageprod001.blob.core.windows.net"
AzureWebJobsStorage__queueServiceUri = "jcsfuncstorageprod001.queue.core.windows.net"
```

## 4. 本地开发与调试工作流

本地开发时，Azure SDK 的 `DefaultAzureCredential` 将自动回退到使用您的本地开发者凭据。

1.  **本地身份验证**: 必须在终端中运行 `az login` 并登录 Azure。
2.  **开发者权限**: **您登录的开发者账户必须拥有与Function App托管标识完全相同的IAM角色**。
3.  **本地配置文件 (`local.settings.json`)**:
    *   必须**完全复制**云端 `CONNECTION_NAME__<PROPERTY_TYPE>` 格式的配置。
    *   对于 `AzureWebJobsStorage`，本地开发推荐使用 Azurite，设置为 `"AzureWebJobsStorage": "UseDevelopmentStorage=true"`。

**`apps/jcs-backend-services-standard/local.settings.json` 示例:**```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "node",
    "ServiceBusConnection__fullyQualifiedNamespace": "jcs-sb-prod-japaneast-001.servicebus.windows.net"
  }
}
```

## 5. 故障排查

*   **问题**: 部署后，门户中看不到函数列表 (静默失败)。
    *   **根源**: `AzureWebJobsStorage` 的托管标识配置或权限问题导致Function Host无法启动。
    *   **行动**: 严格按照本规范第3节，检查其IAM角色和应用设置。

*   **问题**: 部署卡在 `Checking the app health.......`。
    *   **根源**: 您的应用代码在启动时崩溃。通常是由于业务逻辑所需的环境变量在云端缺失或错误。
    *   **行动**: 对比云端和本地的应用配置。查看 Application Insights 的日志以定位启动错误。

## 6. AI编程助手的使用

*   当接收到需要访问新的Azure资源的开发指令时，AI助手应参考本规范。
*   **指令清晰化**:
    *   当指令涉及访问Azure Files时，应明确提示开发者需要创建自定义角色或使用`Storage File Data Privileged Contributor`。
    *   AI助手在生成代码后，应主动提示开发者需要完成的**步骤2（权限分配）**和**步骤3（应用配置）**。