# 组件：任务执行超时处理函数 (Task Execute Timeout Function)

## 1. 概要 (Overview)

### 1.1. 目的 (Purpose)
本Azure Function (`TaskExecuteTimeoutFunc`) 旨在处理那些因为[`TaskExecuteFunc`](./function-task-execute.md)执行超时或其他持久性错误而未能成功处理、最终进入`TaskInputQueue`对应死信队列 (DLQ) 的新任务请求消息。其主要职责是：尝试停止可能已被[`TaskExecuteFunc`](./function-task-execute.md)启动（但在其超时前未能完成后续步骤）的Azure Automation Runbook作业，更新数据库中对应`Task`记录的状态为错误，并尝试释放可能已被占用的容器并发锁和清理相关的Azure Files工作区，以确保系统资源的最终一致性和可用性。

### 1.2. 范围 (Scope)
本文档详细描述`TaskExecuteTimeoutFunc`的技术设计，包括其触发机制、核心补偿逻辑、与数据库、Azure Automation API、Azure Files的交互、错误处理机制以及相关的配置项。

### 1.3. 组件类型 (Component Type)
*   Azure Function (Node.js/TypeScript 运行时)
*   触发器: Azure Service Bus Queue Trigger (监听 `TaskInputQueue` 的 Dead-Letter Queue)

### 1.4. 名词定义 (Glossary References)
*   **TaskInputQueue DLQ**: `TaskInputQueue`的死信队列，存储无法被`TaskExecuteFunc`正常处理的新任务消息。
*   **容器并发锁**: 通过数据库表 `ContainerConcurrencyStatus` 实现的机制。
*   **Azure Files工作区**: 任务的临时工作目录 `TaskWorkspaces/{taskId}/`。
*   其他相关术语请参考项目核心术语表 [`项目术语表`](../../../definitions/glossary.md)。
*   本文档中所有用户可见的错误和提示信息，均引用自项目核心错误消息定义文档 [`docs/definitions/error-messages.md`](../../../definitions/error-messages.md)。
*   本文档中所有环境变量的定义，均参考项目核心环境变量指南 [`docs/guides/environment-variables.md`](../../../guides/environment-variables.md)。
*   任务状态码常量定义在 `app/lib/definitions.ts`。

## 2. 功能规格 (Functional Specifications)

### 2.1. 主要流程 (Main Process Flow)

```mermaid
graph TD
    A["TaskInputQueue DLQ接收到任务消息<br/>(taskId, 来自原TaskExecuteFunc超时)"] --> B{TaskExecuteTimeoutFunc被触发};
    B --> C["1. 解析消息, 获取taskId<br/>记录日志: '处理TaskInputQueue DLQ消息<br/>(原TaskExecuteFunc超时)'"];
    C --> D["2. 查询Task表获取任务详情<br/>(使用taskId)"];
    D -- "任务不存在或已是终态" --> D_Error_InvalidTask["2.1 处理无效/已处理任务<br/>(记录日志, 幂等终止)"];
    D -- "任务存在且非终态" --> E_StopJob["3. (尝试)调用Automation API停止作业<br/>(使用Task.id作为JobId查询并停止)"];
    E_StopJob -- "停止成功或作业本不存在/已结束" --> F_UpdateDb;
    E_StopJob -- "停止API调用失败" --> F_UpdateDb_ApiFail["记录API停止失败日志 (WARN)<br/>继续后续清理"];
    F_UpdateDb["4. 更新Task状态为COMPLETED_ERROR<br/>记录endedAt, resultMessage (EMET0005)"];
    F_UpdateDb_ApiFail --> F_UpdateDb;
    F_UpdateDb -- "DB更新失败" --> G_Error_Db["记录DB更新错误 (ERROR)<br/>仍继续尝试后续清理"];
    F_UpdateDb -- "DB更新成功" --> H_ReleaseLock;
    G_Error_Db --> H_ReleaseLock;
    H_ReleaseLock["5. (尝试)释放容器并发锁<br/>(更新ContainerConcurrencyStatus为IDLE)"];
    H_ReleaseLock -- "释放锁失败" --> I_Error_Lock["记录释放锁失败日志 (WARN)<br/>继续后续清理"];
    H_ReleaseLock -- "释放锁成功" --> J_CleanWorkspace;
    I_Error_Lock --> J_CleanWorkspace;
    J_CleanWorkspace["6. (尝试)清理Azure Files工作区<br/>(删除 TaskWorkspaces/{taskId}/)"];
    J_CleanWorkspace -- "清理失败" --> K_Error_Workspace["记录清理工作区失败日志 (WARN)"];
    J_CleanWorkspace -- "清理成功" --> L_End;
    K_Error_Workspace --> L_End;
    L_End["7. 记录成功处理DLQ消息日志, 结束"];
    D_Error_InvalidTask --> Z_End["结束处理"];
```
**图 2.1: TaskExecuteTimeoutFunc 主要处理流程图**

### 2.2. 功能点描述 (Functional Points)
1.  **DLQ消息触发与解析**: 函数由`TaskInputQueue`的DLQ中的新消息触发。消息体与原`TaskInputQueue`消息相同，核心是`taskId`。
2.  **记录超时上下文**: 首先，记录一条明确的日志，表明这是一个由于[`TaskExecuteFunc`](./function-task-execute.md)处理超时（或其他持久性错误）而进入DLQ的消息，并包含`taskId`和可能的死信原因。
3.  **任务状态检查**:
    *   根据`taskId`从数据库`Task`表查询任务记录。
    *   如果任务不存在，或其状态已经是终态（`COMPLETED_SUCCESS`, `COMPLETED_ERROR`, `CANCELLED`），则记录日志并幂等终止，不进行后续操作。
4.  **尝试停止Runbook作业**:
    *   如果任务记录存在且非终态，调用Azure Automation API尝试停止与此`Task.id`对应的Runbook作业。
    *   此操作是“尽力而为”的，因为`TaskExecuteFunc`超时时，作业可能尚未提交，或者已经执行完毕。API调用失败（例如作业不存在）不应阻止后续的清理步骤，但应记录警告。
5.  **更新任务状态为错误**:
    *   将`Task.status`更新为 `TASK_STATUS_COMPLETED_ERROR_CODE` ("COMPLETED_ERROR")。
    *   将`Task.resultMessage`更新为消息键 `EMET0005` 对应的日文消息 ("タスクの完了が確認できないため、系统によってタスクを中止しました...。")。
    *   记录`Task.endedAt`为当前时间。
    *   数据库更新失败应记录错误，但仍尝试继续后续的资源回收。
6.  **尝试释放并发锁**:
    *   如果`Task`记录中包含有效的`targetVmName`和`dockerContainerName`，则尝试将`ContainerConcurrencyStatus`表中对应容器的`status`更新为`IDLE`，并清除`currentTaskId`。
    *   此操作是“尽力而为”的，释放失败应记录警告，不阻止后续清理。
7.  **尝试清理Azure Files工作区**:
    *   如果`Task`记录存在，尝试删除Azure Files上为该任务创建的临时工作区目录 (`TaskWorkspaces/{taskId}/`)。
    *   此操作是“尽力而为”的，清理失败应记录警告。
8.  **日志记录**: 详细记录所有补偿操作的执行情况和结果。

### 2.3. 业务规则 (Business Rules)
*   本函数是对`TaskExecuteFunc`未能成功处理（主要是由于执行超时）的新任务请求的最终补偿。
*   其核心目标是将失控的任务标记为错误，并尽最大努力回收可能被其占用的资源（并发锁、工作区）。
*   所有补偿操作（停止作业、释放锁、清理工作区）均设计为容错的，单个补偿操作的失败不应中断其他补偿操作的尝试。

### 2.4. 前提条件 (Preconditions)
*   Azure Service Bus (`TaskInputQueue`的DLQ) 正常运行。
*   Azure SQL Database (`Task`, `ContainerConcurrencyStatus`表) 正常运行且可访问。
*   Azure Automation 服务正常运行，Function App具有停止作业的权限。
*   Azure Files 服务正常运行，Function App具有删除目录的权限。
*   所有必需的环境变量已正确设置。

### 2.5. 制约事项 (Constraints)
*   本函数无法保证100%成功停止正在运行的Runbook作业或回收所有资源，特别是如果`TaskExecuteFunc`在记录关键上下文信息（如`targetVmName`）到`Task`表之前就超时。
*   其有效性依赖于`Task`表中任务记录的准确性和完整性。

### 2.6. 注意事项 (Notes)
*   **幂等性**: 设计上需要考虑DLQ消息可能被重复处理的情况。通过检查任务是否已处于终态，可以实现幂等性。
*   **日志级别**: 对于补偿操作的失败，应使用适当的日志级别（如`WARN`），以便监控和必要时人工干预，但避免因此将消息重新送回DLQ（除非是DB更新`Task`状态的核心操作失败）。
*   **告警**: DLQ中出现消息本身就是一个值得告警的事件，表明主流程`TaskExecuteFunc`存在问题。

---

## 3. 技术设计与实现细节 (Technical Design & Implementation)

### 3.1. 技术栈与依赖 (Technology Stack & Dependencies)
*   **运行时**: Azure Functions (Node.js/TypeScript)。
*   **触发器**: Azure Service Bus Queue Trigger (监听`TaskInputQueue`的DLQ)。
*   **数据库交互**: Prisma ORM。
*   **Azure Automation交互**: `@azure/arm-automation` (或REST API)。
*   **Azure Files交互**: `@azure/storage-file-share`。
*   **日志**: `app/lib/logger.ts`。
*   **核心定义与常量**: `app/lib/definitions.ts`。

### 3.2. 详细界面元素定义 (Detailed UI Element Definitions)
*   本组件为后端Azure Function，无直接用户界面。

### 3.3. 详细事件处理逻辑 (Detailed Event Handling)
*   本组件由Service Bus DLQ消息触发，其核心处理逻辑见3.6节。

### 3.4. 数据结构与API/Server Action交互 (Data Structures and API/Server Action Interaction)

#### 3.4.1. 输入消息结构 (from `TaskInputQueue/$DeadLetterQueue`)
消息体与原`TaskInputQueue`消息相同，核心是：
```typescript
{
  "taskId": "cuid-or-uuid-of-the-task"
}
```
Service Bus消息的元数据（如`DeadLetterReason`）也可能用于日志记录。

#### 3.4.2. 与数据库的交互
详见3.5节。

#### 3.4.3. 与Azure Automation API的交互
*   `Job_Stop` (或等效的SDK方法): 尝试停止与`taskId`关联的Runbook作业。

#### 3.4.4. 与Azure Files的交互
*   `ShareDirectoryClient.deleteIfExists()`: 删除`TaskWorkspaces/{taskId}/`目录。

#### 3.4.5. 序列图 (Mermaid `sequenceDiagram`)

```mermaid
sequenceDiagram
    participant DLQ as "Azure Service Bus (TaskInputQueue/$DeadLetterQueue)"
    participant TimeoutFunc as "TaskExecuteTimeoutFunc"
    participant Database as "Azure SQL Database"
    participant AzAutomation as "Azure Automation API"
    participant AzFiles as "Azure Files (Workspaces)"

    DLQ->>TimeoutFunc: DLQ消息: { taskId: "task123", DeadLetterReason }
    activate TimeoutFunc
    TimeoutFunc->>TimeoutFunc: 1. 解析 taskId, 记录DLQ上下文
    TimeoutFunc->>Database: 2. 查询Task详情 (task123)
    activate Database
    Database-->>TimeoutFunc: Task对象 (或 null/已终态)
    deactivate Database

    alt 任务存在且非终态
        TimeoutFunc->>AzAutomation: (尝试) 3. 停止Automation作业 (JobId: task123)
        activate AzAutomation
        AzAutomation-->>TimeoutFunc: 停止结果 (成功/失败/不存在)
        deactivate AzAutomation
        TimeoutFunc->>Database: 4. 更新Task状态为COMPLETED_ERROR (EMET0005)
        activate Database
        Database-->>TimeoutFunc: DB更新结果
        deactivate Database
        TimeoutFunc->>Database: (尝试) 5. 释放并发锁 (ContainerConcurrencyStatus)
        activate Database
        Database-->>TimeoutFunc: 释放锁结果
        deactivate Database
        TimeoutFunc->>AzFiles: (尝试) 6. 清理工作区 (TaskWorkspaces/task123/)
        activate AzFiles
        AzFiles-->>TimeoutFunc: 清理结果
        deactivate AzFiles
    else 任务不存在或已是终态
        TimeoutFunc->>TimeoutFunc: 记录日志 (幂等处理)
    end
    
    TimeoutFunc->>TimeoutFunc: 7. 记录处理完成日志
    deactivate TimeoutFunc
```

### 3.5. 数据库设计与访问详情 (Database Design and Access Details)

#### 3.5.1. 相关表引用
*   **`Task` 表**: 读取任务详情；更新最终`status`, `endedAt`, `resultMessage`。
*   **`ContainerConcurrencyStatus` 表**: 更新容器`status`为`IDLE`，清除`currentTaskId`。

#### 3.5.2. 主要数据查询/变更逻辑

1.  **查询任务详情**:
    ```typescript
    const task = await prisma.task.findUnique({ where: { id: taskId } });
    ```
2.  **更新任务状态为错误**:
    ```typescript
    await prisma.task.update({
      where: { id: taskId },
      data: {
        status: TASK_STATUS_COMPLETED_ERROR_CODE,
        endedAt: new Date(),
        resultMessage: getErrorMessageByKey(PORTAL_ERROR_MESSAGES_KEYS.EMET0005),
      },
    });
    ```
3.  **释放并发锁 (如果`task`对象中包含`targetVmName`和`dockerContainerName`)**:
    ```typescript
    if (task?.targetVmName && task?.dockerContainerName) {
      await prisma.containerConcurrencyStatus.updateMany({ // 使用updateMany以防记录不存在时不抛错
        where: { 
          targetVmName: task.targetVmName, 
          targetContainerName: task.dockerContainerName,
          currentTaskId: taskId // 确保只释放与本任务关联的锁
        },
        data: { status: 'IDLE', currentTaskId: null, lastStatusChangeAt: new Date() }
      });
    }
    ```
*   **事务管理**: 核心的`Task`状态更新可以是一个独立操作。释放锁和清理工作区是补偿性的，它们的失败不应阻止`Task`状态的更新。

### 3.6. 关键后端逻辑/算法 (Key Backend Logic/Algorithms)

核心逻辑是按顺序执行一系列“尽力而为”的补偿操作：

1.  **加载任务**: 从数据库获取任务详细信息。如果任务不存在或已完成，则记录并退出。
2.  **停止作业**: 调用Azure Automation API停止作业。记录API调用结果，不因失败而中止。
3.  **更新任务状态**: 将数据库中的任务状态标记为 `COMPLETED_ERROR`，结果消息为 `EMET0005`。如果此步失败，记录严重错误，但仍尝试继续。
4.  **释放锁**: 更新 `ContainerConcurrencyStatus` 表，尝试将对应容器状态设为 `IDLE`。记录操作结果，不因失败而中止。
5.  **清理工作区**: 调用Azure Files API删除任务工作区。记录操作结果。
6.  所有操作均需详细记录日志，并包含DLQ上下文。

### 3.7. 错误处理详情 (Detailed Error Handling)

| #  | 错误场景描述 (中文) | 触发位置 | 引用的消息ID/消息键 (用户可见部分, 更新到`Task.resultMessage`) | 系统内部处理及日志记录建议 (中文) | 日志级别 |
|----|-----------------|----------|------------------------------------|---------------------------------|-----------|
| 1  | DLQ消息格式无效或`taskId`缺失 | Function触发，消息解析阶段 | (内部错误，不更新Task) | 记录错误，包含原始DLQ消息属性。 | ERROR |
| 2  | 根据`taskId`未找到任务，或任务已是终态 | 查询`Task`表后 | (内部错误，不更新Task) | 记录日志（幂等处理或无效任务）。安全结束。 | INFO/WARN |
| 3  | 调用Azure Automation API停止作业失败 | `Job_Stop`调用时 | (不直接更新`resultMessage`为此错误) | 记录详细API错误。补偿流程继续。 | WARN |
| 4  | 更新`Task`表状态为`ERROR`失败 | `Task.update`调用时 | (内部错误，原`resultMessage`可能未被`EMET0005`覆盖) | 记录严重DB错误。这是核心补偿步骤失败，需关注。补偿流程仍尝试继续。 | ERROR |
| 5  | 释放并发锁失败 | `ContainerConcurrencyStatus.update`调用时 | (不直接更新`resultMessage`为此错误) | 记录DB错误。可能导致后续任务阻塞，需告警。 | CRITICAL |
| 6  | 清理Azure Files工作区失败 | `ShareDirectoryClient.deleteIfExists`调用时 | (不直接更新`resultMessage`为此错误) | 记录Files SDK错误。可能导致存储空间浪费。 | WARN |
| 7  | 本Function执行超时 (极罕见) | Azure Functions运行时 | (内部错误) | Azure平台记录超时。消息可能保留在DLQ，需人工排查。 | CRITICAL |

### 3.8. 配置项 (Configuration)

#### 3.8.1. 值列表 (LOV) 定义 (源自 `docs/definitions/lov-definitions.md`)
*   `TASK_STATUS` (父级代码): 主要将任务状态更新为 `TASK_STATUS_COMPLETED_ERROR_CODE`。

#### 3.8.2. 环境变量 (源自 `docs/guides/environment-variables.md`)
*   `SERVICE_BUS_TASK_INPUT_QUEUE_NAME`: 用于在`function.json`中正确配置DLQ的触发器绑定。
*   `MSSQL_PRISMA_URL`
*   `AZURE_STORAGE_CONNECTION_STRING` (用于Azure Files)
*   `AZURE_STORAGE_FILE_SHARE_NAME` (或等效配置)
*   `AZURE_AUTOMATION_ACCOUNT_NAME`, `AZURE_AUTOMATION_RESOURCE_GROUP_NAME` (或等效配置)
*   `LOG_LEVEL`
*   `FUNCTION_TIMEOUT_SECONDS` (若在`host.json`中通过此配置)

#### 3.8.3. 错误与提示消息 (源自 `docs/definitions/error-messages.md`)
本函数主要使用以下消息键更新`Task.resultMessage`：
*   `EMET0005`: "タスクの完了が確認できないため、系统によってタスクを中止しました...。"

### 3.9. 注意事项与其他 (Notes/Miscellaneous)
*   **“尽力而为”原则**: 本函数的核心设计原则是尽最大努力清理资源和更新状态，但不保证所有操作都能成功。日志记录对于追踪其行为和潜在问题至关重要。
*   **依赖`Task`记录的完整性**: 如果[`TaskExecuteFunc`](./function-task-execute.md)在将`targetVmName`等关键信息写入`Task`表之前就超时，本函数在释放锁和清理工作区方面的能力会受限。
*   **告警的必要性**: DLQ中有消息并被本函数处理，强烈指示主流程[`TaskExecuteFunc`](./function-task-execute.md)存在问题，应配置相关告警。
