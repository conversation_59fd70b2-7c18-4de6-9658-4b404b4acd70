describe("エラーハンドリングの一般的なテスト", () => {
  describe("ログアウトダイアログ", () => {
    it("ログアウト監査ログ API はサーバーエラーが発生した場合、通常通りログアウトが行われる", () => {
      cy.intercept("POST", "api/audit-login-logs", {
        statusCode: 500,
        body: {
          error:
            "サーバーに一時的に接続できません。しばらくしてから再度ポータルにアクセスしてください。",
        },
        headers: {
          "content-type": "application/json",
        },
      });
      cy.visit("/login");
      cy.get("#userId").type("hitachi.hanako.ab");
      cy.get("#password").type("changeit!@#");
      cy.get("button").contains("ログイン").click();
      cy.wait(3000);
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("ログアウト").click();
      cy.get(
        "#logout-modal > .w-full > .relative > .flex-row-reverse > button.bg-gradient-dark",
      ).click();
      cy.wait(3000);

      cy.url().should("eq", Cypress.config().baseUrl + "/login");
    });

    it("ログアウト API はサーバーエラーが発生した場合、エラーメッセージを表示する", () => {
      cy.intercept("POST", "/api/auth/signout", {
        statusCode: 500,
        body: "Server Error",
        headers: {
          "content-type": "text/plain",
        },
      });
      cy.visit("/login");
      cy.get("#userId").type("hitachi.hanako.ab");
      cy.get("#password").type("changeit!@#");
      cy.get("button").contains("ログイン").click();
      cy.wait(3000);
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("ログアウト").click();
      cy.get(
        "#logout-modal > .w-full > .relative > .flex-row-reverse > button.bg-gradient-dark",
      ).click();
      cy.wait(3000);

      cy.contains(
        "サーバーに一時的に接続できません。しばらくしてから再度ポータルにアクセスしてください。",
      );
    });
  });
});
