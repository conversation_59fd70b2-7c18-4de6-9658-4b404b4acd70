import prisma from "@/app/lib/prisma";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { revalidateTag, unstable_cache } from "next/cache";
import { cookies } from "next/headers";
import {
  ENV,
  PORTAL_CACHE_KEY_PROVIDED_FILES
} from "../definitions";
import { LogFunctionSignature } from "../logger";
import { handleServerError } from "../portal-error";
import { formatBytes, formatDate } from "../utils";

/**
 * 提供ファイル（ProvidedFile）関連のデータ操作を行う静的クラスです。
 * 提供ファイルのキャッシュ取得、ページ数計算、フィルタ・ソート処理を担当します。
 */
export class ServerDataProvidedFiles {
  /**
   * 指定されたライセンスID・タイムゾーンに紐づく提供ファイル一覧をキャッシュ付きで取得します。
   * キャッシュキーとタグにライセンスIDとタイムゾーンを含めることで、正確なキャッシュ無効化を実現します。
   *
   * @param {string} licenseId - ライセンスID
   * @param {string} tz - タイムゾーン
   * @returns {Promise<any[]>} 提供ファイル情報配列
   */
  static async fetchCachedProvidedFiles(licenseId: string, tz: string) {
    const cachedFn = unstable_cache(
      async () => {
        const plansWithLicense = await prisma.license.findUnique({
          where: { licenseId },
          select: {
            licensePlans: { select: { planId: true } },
          },
        });
        const planFiles = await prisma.planProvidedFile.findMany({
          where: {
            planId: {
              in: plansWithLicense!.licensePlans.map((plan) => plan.planId),
            },
          },
          distinct: ["name"],
          include: { providedFile: true },
        });
        return planFiles.map((planFile) => {
          const { providedFile } = planFile;
          return {
            ...providedFile,
            updatedAt: formatDate(providedFile.updatedAt, tz),
            formattedSize: formatBytes(providedFile.size),
          };
        });
      },
      [`${PORTAL_CACHE_KEY_PROVIDED_FILES}-${licenseId}-${tz}`],
      {
        revalidate: ENV.APP_CACHE_TTL_SECONDS,
        tags: [`${PORTAL_CACHE_KEY_PROVIDED_FILES}-${licenseId}-${tz}`],
      },
    );
    return await cachedFn();
  }

  /**
   * 提供ファイル一覧のページ数を取得します。
   *
   * @param {string} filter - フィルタ文字列
   * @param {number} size - 1ページあたりの件数
   * @param {boolean} refresh - キャッシュ強制リフレッシュ有無
   * @returns {Promise<number>} ページ数
   */
  @LogFunctionSignature()
  static async fetchProvidedFilePages(filter: string, size: number, refresh: boolean) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);
      if (refresh) {
        revalidateTag(`${PORTAL_CACHE_KEY_PROVIDED_FILES}-${session!.user.licenseId}-${session!.user.tz}`);
      }
      const cachedFiles = await this.fetchCachedProvidedFiles(session!.user.licenseId, session!.user.tz);
      if (cachedFiles) {
        if (filter) {
          const filteredFiles = cachedFiles.filter(
            (file) =>
              file.name.toLowerCase().includes(filter.toLowerCase()) ||
              file.description.toLowerCase().includes(filter.toLowerCase()) ||
              file.updatedAt.toLowerCase().includes(filter.toLowerCase()) ||
              file.formattedSize.toLowerCase().includes(filter.toLowerCase()),
          );
          return Math.ceil(Number(filteredFiles.length) / size);
        } else {
          return Math.ceil(Number(cachedFiles.length) / size);
        }
      } else {
        return 0;
      }
    } catch (error) {
      handleServerError(error);
    }
  }

  /**
   * フィルタ・ソート・ページング済みの提供ファイル一覧を取得します。
   *
   * @param {string} filter - フィルタ文字列
   * @param {number} size - 1ページあたりの件数
   * @param {number} page - ページ番号
   * @param {"name"|"description"|"updatedAt"|"size"} sort - ソートキー
   * @param {"asc"|"desc"} order - ソート順
   * @param {string} [preferSort] - 優先ソートキー
   * @returns {Promise<any[]>} 提供ファイル情報配列
   */
  @LogFunctionSignature()
  static async fetchFilteredProvidedFiles(
    filter: string,
    size: number,
    page: number,
    sort: "name" | "description" | "updatedAt" | "size",
    order: "asc" | "desc",
    preferSort?: "name" | "description" | "updatedAt" | "size",
  ) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);
      const cachedProvidedFiles = await this.fetchCachedProvidedFiles(session!.user.licenseId, session!.user.tz);
      if (cachedProvidedFiles) {
        let filteredProvidedFiles = cachedProvidedFiles;
        if (filter) {
          filteredProvidedFiles = cachedProvidedFiles.filter(
            (file) =>
              file.name.toLowerCase().includes(filter.toLowerCase()) ||
              file.description.toLowerCase().includes(filter.toLowerCase()) ||
              file.updatedAt.toLowerCase().includes(filter.toLowerCase()) ||
              file.formattedSize.toLowerCase().includes(filter.toLowerCase()),
          );
        }
        if (sort) {
          filteredProvidedFiles.sort((a, b) => {
            if (sort === "size") {
              if (order === "asc") {
                return a[sort] - b[sort];
              } else {
                return b[sort] - a[sort];
              }
            } else {
              const aValue = a[sort].toLowerCase();
              const bValue = b[sort].toLowerCase();
              if (!preferSort || sort === preferSort) {
                if (order === "asc") {
                  return aValue.localeCompare(bValue);
                } else {
                  return bValue.localeCompare(aValue);
                }
              } else if (preferSort !== "size") {
                let firstComparison;
                if (order === "asc") {
                  firstComparison = aValue.localeCompare(bValue);
                } else {
                  firstComparison = bValue.localeCompare(aValue);
                }
                if (firstComparison !== 0) {
                  return firstComparison;
                }
                return a[preferSort].localeCompare(b[preferSort]);
              } else {
                return 0;
              }
            }
          });
        }
        const startIndex = (page - 1) * size;
        const endIndex = startIndex + size;
        const paginatedProvidedFiles = filteredProvidedFiles.slice(startIndex, endIndex);
        return paginatedProvidedFiles;
      } else {
        return [];
      }
    } catch (error) {
      handleServerError(error);
    }
  }
} 