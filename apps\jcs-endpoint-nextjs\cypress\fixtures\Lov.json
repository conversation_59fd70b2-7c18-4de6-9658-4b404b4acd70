{"Lov": [{"id": "clqnagty208i3bjboo7l3silq", "code": "SERVER_TYPE", "name": "サーバータイプ", "parentCode": null, "value": "", "isEnabled": 1}, {"id": "clqnagty208i4bjbo0ddj98y0", "code": "SERVER_TYPE.GENERAL_MANAGER", "name": "統括マネージャ", "parentCode": "SERVER_TYPE", "value": "JP1/ITDM2(統括マネージャ)", "isEnabled": 1}, {"id": "clqnagty208i5bjboykhubzu0", "code": "SERVER_TYPE.RELAY_MANAGER", "name": "中継マネージャ", "parentCode": "SERVER_TYPE", "value": "JP1/ITDM2(中継マネージャ)", "isEnabled": 1}, {"id": "clqnagty208i6bjbowxcuhcl6", "code": "SERVER_TYPE.HIBUN_CONSOLE", "name": "管理コンソー", "parentCode": "SERVER_TYPE", "value": "秘文(管理コンソール)", "isEnabled": 1}, {"id": "clqnagty208i7bjboercpeg49", "code": "LICENSE_TYPE", "name": "ライセンスタイプ", "parentCode": null, "value": "", "isEnabled": 1}, {"id": "clqnagty208i8bjbo8199lp6o", "code": "LICENSE_TYPE.PROD", "name": "製品版表示名", "parentCode": "LICENSE_TYPE", "value": "製品版", "isEnabled": 1}, {"id": "clqnagty208i9bjbo733z7lzp", "code": "LICENSE_TYPE.TRIAL", "name": "評価版表示名", "parentCode": "LICENSE_TYPE", "value": "評価版", "isEnabled": 1}, {"id": "clqnagty208iabjbojr8dirvi", "code": "OS_TYPE", "name": "OSタイプ", "parentCode": null, "value": "", "isEnabled": 1}, {"id": "clqnagty208ibbjboi934hwgj", "code": "OS_TYPE.WIN", "name": "Windows表示名", "parentCode": "OS_TYPE", "value": "Windows", "isEnabled": 1}, {"id": "clqnagty208icbjbo3ajugma8", "code": "OS_TYPE.LINUX", "name": "Linux表示名", "parentCode": "OS_TYPE", "value": "Linux", "isEnabled": 1}, {"id": "clqnagty208idbjborfvu9zf9", "code": "OS_TYPE.AIX", "name": "AIX表示名", "parentCode": "OS_TYPE", "value": "AIX", "isEnabled": 1}, {"id": "clqnagty208iebjboh6lmx1at", "code": "OS_TYPE.SOLARIS", "name": "Solaris表示名", "parentCode": "OS_TYPE", "value": "Solar<PERSON>", "isEnabled": 1}, {"id": "clqnagty208ifbjbo6lwfhfq1", "code": "OS_TYPE.HPUX", "name": "HP-UX表示名", "parentCode": "OS_TYPE", "value": "HP-UX", "isEnabled": 1}, {"id": "clqnagty208igbjbo1ib1tng7", "code": "OS_TYPE.MACOS", "name": "Mac表示名", "parentCode": "OS_TYPE", "value": "<PERSON>", "isEnabled": 1}, {"id": "clqnagty208ihbjbo03y0zvs7", "code": "SUPPORT_IMPORTANCE", "name": "サポート情報の重要度", "parentCode": null, "value": "", "isEnabled": 1}, {"id": "clqnagty208iibjbosyoy1jb6", "code": "SUPPORT_IMPORTANCE.NONE", "name": "重要度なし(予防保守情報や使用時の注意事項など)", "parentCode": "SUPPORT_IMPORTANCE", "value": "-", "isEnabled": 1}, {"id": "clqnagty208ijbjbob6zltpd0", "code": "SUPPORT_IMPORTANCE.AAA", "name": "業務システムの運用が停止し、発生頻度が高い", "parentCode": "SUPPORT_IMPORTANCE", "value": "AAA", "isEnabled": 1}, {"id": "clqnagty208ikbjbowv0yvlbh", "code": "SUPPORT_IMPORTANCE.AA", "name": "業務システムの運用が停止する可能性がある", "parentCode": "SUPPORT_IMPORTANCE", "value": "AA", "isEnabled": 1}, {"id": "clqnagty208ilbjbo0urzwrwp", "code": "SUPPORT_IMPORTANCE.A", "name": "業務システムの運用が停止する可能性は低い", "parentCode": "SUPPORT_IMPORTANCE", "value": "A", "isEnabled": 1}, {"id": "clqnagty208imbjbovbsrp0h3", "code": "SUPPORT_IMPORTANCE.B", "name": "業務システムの運用に与える影響が少ない", "parentCode": "SUPPORT_IMPORTANCE", "value": "B", "isEnabled": 1}, {"id": "clqnagty208inbjboccceqlmj", "code": "SUPPORT_IMPORTANCE.C", "name": "業務システムの運用に与える影響は殆ど無い", "parentCode": "SUPPORT_IMPORTANCE", "value": "C", "isEnabled": 1}, {"id": "clqnagty208iobjboscpq66i0", "code": "LOCKOUT", "name": "ロックアウト設定", "parentCode": null, "value": "", "isEnabled": 1}, {"id": "clqnagty208ipbjboteq5svi1", "code": "LOCKOUT.MAX_FAILED_TIMES", "name": "ロックアウト失敗最大回数", "parentCode": "LOCKOUT", "value": "10", "isEnabled": 1}, {"id": "clqnagty208iqbjbo27humtu9", "code": "LOCKOUT.TIME_SECONDS", "name": "ロックアウト時間", "parentCode": "LOCKOUT", "value": "1800", "isEnabled": 1}, {"id": "clqnagty208irbjbo63b9kgkc", "code": "AZURE_STORAGE", "name": "Azure Blob 設定", "parentCode": null, "value": "", "isEnabled": 1}, {"id": "clqnagty208isbjbotxnz8lpb", "code": "AZURE_STORAGE.CONTAINER_OPLOGS", "name": "操作ログコンテナ名", "parentCode": "AZURE_STORAGE", "value": "oplogs", "isEnabled": 1}, {"id": "clqnagty208itbjbo6bd8fjds", "code": "AZURE_STORAGE.CONTAINER_PRODUCT_MEDIAS", "name": "製品媒体コンテナ名", "parentCode": "AZURE_STORAGE", "value": "product-medias", "isEnabled": 1}, {"id": "clqnagty208iubjboo99qokj7", "code": "AZURE_STORAGE.CONTAINER_PRODUCT_MANUALS", "name": "マニュアルコンテナ名", "parentCode": "AZURE_STORAGE", "value": "product-manuals", "isEnabled": 1}, {"id": "clqnagty208ivbjbonvn21g4c", "code": "AZURE_STORAGE.CONTAINER_PROVIDED_FILES", "name": "提供ファイルコンテナ名", "parentCode": "AZURE_STORAGE", "value": "provided-files", "isEnabled": 1}, {"id": "clqnagty208iwbjboj5obldt3", "code": "AZURE_STORAGE.CONTAINER_SUPPORT_FILES", "name": "サポートファイルコンテナ名", "parentCode": "AZURE_STORAGE", "value": "support-files", "isEnabled": 1}, {"id": "clqnagty208ixbjbotxhyu6qf", "code": "AZURE_STORAGE.SAS_TTL_SECONDS", "name": "SAS有効期限", "parentCode": "AZURE_STORAGE", "value": "7200", "isEnabled": 1}]}