#!/bin/bash

# JCS Endpoint Monorepo セキュリティ監査スクリプト
# 全プロジェクトの依存関係脆弱性をチェックし、レポートを生成する

set -e

echo "=== JCS Endpoint Monorepo セキュリティ監査開始 ==="
echo "実行日時: $(date)"
echo ""

# 監査結果を保存するディレクトリ
AUDIT_DIR="security-audit-reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_DIR="${AUDIT_DIR}/${TIMESTAMP}"

mkdir -p "${REPORT_DIR}"

# 各プロジェクトの監査実行
PROJECTS=(
    "apps/jcs-endpoint-nextjs"
    "apps/jcs-backend-services-standard" 
    "apps/jcs-backend-services-long-running"
)

echo "=== 依存関係脆弱性スキャン開始 ==="
echo ""

for project in "${PROJECTS[@]}"; do
    echo "--- ${project} の監査実行中 ---"
    
    if [ -d "${project}" ] && [ -f "${project}/package.json" ]; then
        cd "${project}"
        
        # npm auditの実行（JSONフォーマットで出力）
        echo "npm audit実行中..."
        npm audit --audit-level=moderate --json > "../../${REPORT_DIR}/${project//\//_}_audit.json" 2>/dev/null || true
        
        # 人間が読みやすい形式でも出力
        echo "詳細レポート生成中..."
        npm audit --audit-level=moderate > "../../${REPORT_DIR}/${project//\//_}_audit.txt" 2>/dev/null || true
        
        # 高・重大な脆弱性のみを抽出
        echo "高リスク脆弱性抽出中..."
        npm audit --audit-level=high --json > "../../${REPORT_DIR}/${project//\//_}_high_risk.json" 2>/dev/null || true
        
        cd - > /dev/null
        echo "✓ ${project} 監査完了"
    else
        echo "⚠ ${project} が見つからないか、package.jsonが存在しません"
    fi
    echo ""
done

# サマリーレポート生成
echo "=== サマリーレポート生成中 ==="

SUMMARY_FILE="${REPORT_DIR}/security_audit_summary.md"

cat > "${SUMMARY_FILE}" << EOF
# JCS Endpoint Monorepo セキュリティ監査レポート

**実行日時**: $(date)  
**監査対象**: 全プロジェクト (Next.js + Azure Functions)

## 監査結果サマリー

EOF

# 各プロジェクトの結果を集計
for project in "${PROJECTS[@]}"; do
    audit_file="${REPORT_DIR}/${project//\//_}_audit.json"
    
    if [ -f "${audit_file}" ]; then
        echo "### ${project}" >> "${SUMMARY_FILE}"
        echo "" >> "${SUMMARY_FILE}"
        
        # JSONから脆弱性数を抽出（jqが利用可能な場合）
        if command -v jq &> /dev/null; then
            vulnerabilities=$(jq -r '.metadata.vulnerabilities | to_entries[] | "\(.key): \(.value)"' "${audit_file}" 2>/dev/null || echo "解析エラー")
            echo "**脆弱性統計**:" >> "${SUMMARY_FILE}"
            echo '```' >> "${SUMMARY_FILE}"
            echo "${vulnerabilities}" >> "${SUMMARY_FILE}"
            echo '```' >> "${SUMMARY_FILE}"
        else
            echo "**注意**: jqコマンドが見つからないため、詳細な統計は手動で確認してください。" >> "${SUMMARY_FILE}"
        fi
        
        echo "" >> "${SUMMARY_FILE}"
        echo "**詳細レポート**: \`${project//\//_}_audit.txt\`" >> "${SUMMARY_FILE}"
        echo "**高リスク脆弱性**: \`${project//\//_}_high_risk.json\`" >> "${SUMMARY_FILE}"
        echo "" >> "${SUMMARY_FILE}"
    fi
done

cat >> "${SUMMARY_FILE}" << EOF

## 推奨アクション

1. **高・重大な脆弱性の確認**: 各プロジェクトの \`*_high_risk.json\` ファイルを確認
2. **依存関係の更新**: \`npm update\` または \`npm audit fix\` の実行を検討
3. **手動修正**: 自動修正できない脆弱性の手動対応
4. **定期実行**: 月次でこのスクリプトを実行

## ファイル一覧

- \`security_audit_summary.md\`: このサマリーレポート
- \`*_audit.txt\`: 各プロジェクトの詳細監査結果
- \`*_audit.json\`: 各プロジェクトの機械可読な監査結果
- \`*_high_risk.json\`: 各プロジェクトの高リスク脆弱性のみ

---
**次回実行推奨日**: $(date -d "+1 month" 2>/dev/null || date -v+1m 2>/dev/null || echo "1ヶ月後")
EOF

echo "✓ サマリーレポート生成完了: ${SUMMARY_FILE}"
echo ""

echo "=== セキュリティ監査完了 ==="
echo "結果は以下のディレクトリに保存されました:"
echo "  ${REPORT_DIR}/"
echo ""
echo "サマリーレポートを確認してください:"
echo "  cat ${SUMMARY_FILE}"
echo ""
echo "高リスク脆弱性がある場合は、速やかに対応を検討してください。"
