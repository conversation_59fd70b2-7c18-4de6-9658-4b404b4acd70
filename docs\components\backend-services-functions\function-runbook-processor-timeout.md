# 组件：Runbook作业处理超时函数 (Runbook Processor Timeout Function)

## 1. 概要 (Overview)

### 1.1. 目的 (Purpose)
本Azure Function (`RunbookProcessorTimeoutFunc`) 旨在处理那些因为[`RunbookProcessorFunc`](./function-runbook-processor.md)执行超时或其他持久性错误而未能成功处理、最终进入`RunbookStatusQueue`对应死信队列 (DLQ) 的Runbook原始结果消息。其主要职责是：更新数据库中对应`Task`记录的状态为错误，并尽最大努力释放可能仍被占用的容器并发锁和清理相关的Azure Files工作区，以确保系统资源的最终一致性和可用性。由于此时Runbook作业理论上已在Azure Automation层面结束（其结果消息未能被[`RunbookProcessorFunc`](./function-runbook-processor.md)成功处理），本函数通常不直接与Azure Automation API交互停止作业。

### 1.2. 范围 (Scope)
本文档详细描述`RunbookProcessorTimeoutFunc`的技术设计，包括其触发机制、核心补偿逻辑、与数据库及Azure Files的交互、错误处理机制以及相关的配置项。

### 1.3. 组件类型 (Component Type)
*   Azure Function (Node.js/TypeScript 运行时)
*   触发器: Azure Service Bus Queue Trigger (监听 `RunbookStatusQueue` 的 Dead-Letter Queue)

### 1.4. 名词定义 (Glossary References)
*   **RunbookStatusQueue DLQ**: `RunbookStatusQueue`的死信队列，存储无法被`RunbookProcessorFunc`正常处理的Runbook结果消息。
*   **容器并发锁**: 通过数据库表 `ContainerConcurrencyStatus` 实现的机制。
*   **Azure Files工作区**: 任务的临时工作目录 `TaskWorkspaces/{taskId}/`。
*   其他相关术语请参考项目核心术语表 [`项目术语表`](../../../definitions/glossary.md)。
*   本文档中所有用户可见的错误和提示信息，均引用自项目核心错误消息定义文档 [`docs/definitions/error-messages.md`](../../../definitions/error-messages.md)。
*   本文档中所有环境变量的定义，均参考项目核心环境变量指南 [`docs/guides/environment-variables.md`](../../../guides/environment-variables.md)。
*   任务状态码常量定义在 `app/lib/definitions.ts`。

## 2. 功能规格 (Functional Specifications)

### 2.1. 主要流程 (Main Process Flow)

```mermaid
graph TD
    A["RunbookStatusQueue DLQ接收到结果消息<br/>(taskId, ..., 来自原RunbookProcessorFunc超时)"] --> B{RunbookProcessorTimeoutFunc被触发};
    B --> C["1. 解析消息, 获取taskId<br/>记录日志: '处理RunbookStatusQueue DLQ消息<br/>(原RunbookProcessorFunc超时)'"];
    C --> D["2. 查询Task表获取任务详情<br/>(使用taskId)"];
    D -- "任务不存在或已是终态" --> D_Error_InvalidTask["2.1 处理无效/已处理任务<br/>(记录日志, 幂等终止)"];
    D -- "任务存在且非终态" --> F_UpdateDb["3. 更新Task状态为COMPLETED_ERROR<br/>记录endedAt, resultMessage (EMET0005)"];
    F_UpdateDb -- "DB更新失败" --> G_Error_Db["记录DB更新错误 (ERROR)<br/>仍继续尝试后续清理"];
    F_UpdateDb -- "DB更新成功" --> H_ReleaseLock;
    G_Error_Db --> H_ReleaseLock;
    H_ReleaseLock["4. (尝试)释放容器并发锁<br/>(更新ContainerConcurrencyStatus为IDLE)"];
    H_ReleaseLock -- "释放锁失败" --> I_Error_Lock["记录释放锁失败日志 (WARN)<br/>继续后续清理"];
    H_ReleaseLock -- "释放锁成功" --> J_CleanWorkspace;
    I_Error_Lock --> J_CleanWorkspace;
    J_CleanWorkspace["5. (尝试)清理Azure Files工作区<br/>(删除 TaskWorkspaces/{taskId}/)"];
    J_CleanWorkspace -- "清理失败" --> K_Error_Workspace["记录清理工作区失败日志 (WARN)"];
    J_CleanWorkspace -- "清理成功" --> L_End;
    K_Error_Workspace --> L_End;
    L_End["6. 记录成功处理DLQ消息日志, 结束"];
    D_Error_InvalidTask --> Z_End["结束处理"];
```
**图 2.1: RunbookProcessorTimeoutFunc 主要处理流程图**

### 2.2. 功能点描述 (Functional Points)
1.  **DLQ消息触发与解析**: 函数由`RunbookStatusQueue`的DLQ中的新消息触发。消息体与原`RunbookStatusQueue`消息相同，核心是`taskId`和原始作业结果。
2.  **记录超时上下文**: 首先，记录日志，表明这是一个由于[`RunbookProcessorFunc`](./function-runbook-processor.md)处理超时（或其他持久性错误）而进入DLQ的消息，并包含`taskId`和可能的死信原因。
3.  **任务状态检查**:
    *   根据`taskId`从数据库`Task`表查询任务记录。
    *   如果任务不存在，或其状态已经是终态（`COMPLETED_SUCCESS`, `COMPLETED_ERROR`, `CANCELLED`），则记录日志并幂等终止。
4.  **更新任务状态为错误**:
    *   将`Task.status`更新为 `TASK_STATUS_COMPLETED_ERROR_CODE` ("COMPLETED_ERROR")。
    *   将`Task.resultMessage`更新为消息键 `EMET0005` 对应的日文消息 ("タスクの完了が確認できないため、系统によってタスクを中止しました...。")，因为原始的成功/失败结果未能被`RunbookProcessorFunc`妥善处理。
    *   记录`Task.endedAt`为当前时间。
    *   数据库更新失败应记录错误，但仍尝试继续后续的资源回收。
5.  **尝试释放并发锁**:
    *   如果`Task`记录中包含有效的`targetVmName`和`dockerContainerName`，则尝试将`ContainerConcurrencyStatus`表中对应容器的`status`更新为`IDLE`，并清除`currentTaskId`。
    *   此操作是“尽力而为”的。
6.  **尝试清理Azure Files工作区**:
    *   如果`Task`记录存在，尝试删除Azure Files上为该任务创建的临时工作区目录 (`TaskWorkspaces/{taskId}/`)。
    *   此操作是“尽力而为”的。
7.  **日志记录**: 详细记录所有补偿操作的执行情况和结果。

### 2.3. 业务规则 (Business Rules)
*   本函数是对`RunbookProcessorFunc`未能成功处理（主要是由于执行超时）的Runbook结果消息的最终补偿。
*   其核心目标是将未能被正确后处理的任务标记为错误，并尽最大努力回收可能被其占用的资源（并发锁、工作区）。
*   由于进入此流程意味着`RunbookProcessorFunc`未能处理原始的成功/失败结果，因此本函数通常统一将任务标记为`EMET0005`超时错误，而不是尝试解析原始结果。

### 2.4. 前提条件 (Preconditions)
*   Azure Service Bus (`RunbookStatusQueue`的DLQ) 正常运行。
*   Azure SQL Database (`Task`, `ContainerConcurrencyStatus`表) 正常运行且可访问。
*   Azure Files 服务正常运行，Function App具有删除目录的权限。
*   所有必需的环境变量已正确设置。

### 2.5. 制约事项 (Constraints)
*   本函数无法恢复`RunbookProcessorFunc`未能成功处理的Runbook输出（如文件归档、`OperationLog`记录创建）。这些产物可能会丢失或未被正确记录。
*   其有效性依赖于`Task`表中任务记录的准确性和完整性。

### 2.6. 注意事项 (Notes)
*   **幂等性**: 通过检查任务是否已处于终态来实现。
*   **告警**: DLQ中出现消息本身就是一个值得告警的事件，表明主流程`RunbookProcessorFunc`存在问题。

---

## 3. 技术设计与实现细节 (Technical Design & Implementation)

### 3.1. 技术栈与依赖 (Technology Stack & Dependencies)
*   **运行时**: Azure Functions (Node.js/TypeScript)。
*   **触发器**: Azure Service Bus Queue Trigger (监听`RunbookStatusQueue`的DLQ)。
*   **数据库交互**: Prisma ORM。
*   **Azure Files交互**: `@azure/storage-file-share`。
*   **日志**: `app/lib/logger.ts`。
*   **核心定义与常量**: `app/lib/definitions.ts`。

### 3.2. 详细界面元素定义 (Detailed UI Element Definitions)
*   本组件为后端Azure Function，无直接用户界面。

### 3.3. 详细事件处理逻辑 (Detailed Event Handling)
*   本组件由Service Bus DLQ消息触发，其核心处理逻辑见3.6节。

### 3.4. 数据结构与API/Server Action交互 (Data Structures and API/Server Action Interaction)

#### 3.4.1. 输入消息结构 (from `RunbookStatusQueue/$DeadLetterQueue`)
消息体与原`RunbookStatusQueue`消息相同，即`RunbookJobResultForQueue`接口定义的结构。

#### 3.4.2. 与数据库的交互
详见3.5节。

#### 3.4.3. 与Azure Files的交互
*   `ShareDirectoryClient.deleteIfExists()`: 删除`TaskWorkspaces/{taskId}/`目录。

#### 3.4.4. 序列图 (Mermaid `sequenceDiagram`)

```mermaid
sequenceDiagram
    participant DLQ as "Azure Service Bus (RunbookStatusQueue/$DeadLetterQueue)"
    participant TimeoutFunc as "RunbookProcessorTimeoutFunc"
    participant Database as "Azure SQL Database"
    participant AzFiles as "Azure Files (Workspaces)"

    DLQ->>TimeoutFunc: DLQ消息: { taskId, ..., DeadLetterReason }
    activate TimeoutFunc
    TimeoutFunc->>TimeoutFunc: 1. 解析 taskId, 记录DLQ上下文
    TimeoutFunc->>Database: 2. 查询Task详情 (taskId)
    activate Database
    Database-->>TimeoutFunc: Task对象 (或 null/已终态)
    deactivate Database

    alt 任务存在且非终态
        TimeoutFunc->>Database: 3. 更新Task状态为COMPLETED_ERROR (EMET0005)
        activate Database
        Database-->>TimeoutFunc: DB更新结果
        deactivate Database
        TimeoutFunc->>Database: (尝试) 4. 释放并发锁 (ContainerConcurrencyStatus)
        activate Database
        Database-->>TimeoutFunc: 释放锁结果
        deactivate Database
        TimeoutFunc->>AzFiles: (尝试) 5. 清理工作区 (TaskWorkspaces/taskId/)
        activate AzFiles
        AzFiles-->>TimeoutFunc: 清理结果
        deactivate AzFiles
    else 任务不存在或已是终态
        TimeoutFunc->>TimeoutFunc: 记录日志 (幂等处理)
    end
    
    TimeoutFunc->>TimeoutFunc: 6. 记录处理完成日志
    deactivate TimeoutFunc
```

### 3.5. 数据库设计与访问详情 (Database Design and Access Details)

#### 3.5.1. 相关表引用
*   **`Task` 表**: 读取任务详情；更新最终`status`, `endedAt`, `resultMessage`。
*   **`ContainerConcurrencyStatus` 表**: 更新容器`status`为`IDLE`，清除`currentTaskId`。

#### 3.5.2. 主要数据查询/变更逻辑
与 `TaskExecuteTimeoutFunc` (3.5.2节) 中的数据库操作类似，但更新`Task.resultMessage`时使用`EMET0005`。

1.  **查询任务详情**:
    ```typescript
    const task = await prisma.task.findUnique({ where: { id: taskId } });
    ```
2.  **更新任务状态为错误**:
    ```typescript
    await prisma.task.update({
      where: { id: taskId },
      data: {
        status: TASK_STATUS_COMPLETED_ERROR_CODE,
        endedAt: new Date(),
        resultMessage: getErrorMessageByKey(PORTAL_ERROR_MESSAGES_KEYS.EMET0005),
      },
    });
    ```
3.  **释放并发锁 (如果`task`对象中包含`targetVmName`和`dockerContainerName`)**:
    ```typescript
    if (task?.targetVmName && task?.dockerContainerName) {
      await prisma.containerConcurrencyStatus.updateMany({
        where: { 
          targetVmName: task.targetVmName, 
          targetContainerName: task.dockerContainerName,
          currentTaskId: taskId 
        },
        data: { status: 'IDLE', currentTaskId: null, lastStatusChangeAt: new Date() }
      });
    }
    ```

### 3.6. 关键后端逻辑/算法 (Key Backend Logic/Algorithms)

核心逻辑是按顺序执行一系列“尽力而为”的补偿操作：

1.  **加载任务**: 从数据库获取任务详细信息。如果任务不存在或已完成，则记录并退出。
2.  **更新任务状态**: 将数据库中的任务状态标记为 `COMPLETED_ERROR`，结果消息为 `EMET0005`。如果此步失败，记录严重错误，但仍尝试继续。
3.  **释放锁**: 更新 `ContainerConcurrencyStatus` 表。
4.  **清理工作区**: 调用Azure Files API删除任务工作区。
5.  所有操作均需详细记录日志，并包含DLQ上下文。

### 3.7. 错误处理详情 (Detailed Error Handling)
与 `TaskExecuteTimeoutFunc` (3.7节) 中的错误处理场景类似，但上下文是`RunbookProcessorFunc`超时。

| #  | 错误场景描述 (中文) | 触发位置 | 引用的消息ID/消息键 (用户可见部分, 更新到`Task.resultMessage`) | 系统内部处理及日志记录建议 (中文) | 日志级别 |
|----|-----------------|----------|------------------------------------|---------------------------------|-----------|
| 1  | DLQ消息格式无效或`taskId`缺失 | Function触发，消息解析阶段 | (内部错误) | 记录错误。 | ERROR |
| 2  | 根据`taskId`未找到任务，或任务已是终态 | 查询`Task`表后 | (内部错误) | 记录日志（幂等/无效任务）。 | INFO/WARN |
| 3  | 更新`Task`表状态为`ERROR`失败 | `Task.update`调用时 | (内部错误，原`resultMessage`可能未被`EMET0005`覆盖) | 记录严重DB错误。 | ERROR |
| 4  | 释放并发锁失败 | `ContainerConcurrencyStatus.update`调用时 | (不更新`resultMessage`) | 记录DB错误。可能导致后续任务阻塞，需告警。 | CRITICAL |
| 5  | 清理Azure Files工作区失败 | `ShareDirectoryClient.deleteIfExists`调用时 | (不更新`resultMessage`) | 记录Files SDK错误。 | WARN |
| 6  | 本Function执行超时 | Azure Functions运行时 | (内部错误) | Azure平台记录超时。消息可能保留在DLQ，需人工排查。 | CRITICAL |

### 3.8. 配置项 (Configuration)

#### 3.8.1. 值列表 (LOV) 定义 (源自 `docs/definitions/lov-definitions.md`)
*   `TASK_STATUS` (父级代码): 主要将任务状态更新为 `TASK_STATUS_COMPLETED_ERROR_CODE`。

#### 3.8.2. 环境变量 (源自 `docs/guides/environment-variables.md`)
*   `SERVICE_BUS_RUNBOOK_STATUS_QUEUE_NAME`: 用于配置DLQ的触发器绑定。
*   `MSSQL_PRISMA_URL`
*   `AZURE_STORAGE_CONNECTION_STRING` (用于Azure Files)
*   `AZURE_STORAGE_FILE_SHARE_NAME` (或等效配置)
*   `LOG_LEVEL`
*   `FUNCTION_TIMEOUT_SECONDS` (若在`host.json`中通过此配置)

#### 3.8.3. 错误与提示消息 (源自 `docs/definitions/error-messages.md`)
本函数主要使用以下消息键更新`Task.resultMessage`：
*   `EMET0005`: "タスクの完了が確認できないため、系统によってタスクを中止しました...。"

### 3.9. 注意事项与其他 (Notes/Miscellaneous)
*   与`TaskExecuteTimeoutFunc`类似，本函数核心是“尽力而为”的补偿。
*   DLQ中出现消息同样指示主流程[`RunbookProcessorFunc`](./function-runbook-processor.md)存在问题，应配置相关告警。
