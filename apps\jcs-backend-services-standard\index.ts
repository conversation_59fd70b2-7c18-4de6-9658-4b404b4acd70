/**
 * @file Main entry point for the Function App.
 * @description This file's sole purpose is to import all function files.
 * By importing them, the registration code within each file (e.g., app.serviceBusQueue)
 * is executed, making the functions discoverable by the Azure Functions Host.
 */

import './RunbookMonitorFunc/RunbookMonitorFunc';
import './RunbookProcessorTimeoutFunc/RunbookProcessorTimeoutFunc';
import './TaskCancellationFunc/TaskCancellationFunc'
import './TaskCancellationTimeoutFunc/TaskCancellationTimeoutFunc'
import './TaskExecuteFunc/TaskExecuteFunc';
import './TaskExecuteTimeoutFunc/TaskExecuteTimeoutFunc';
