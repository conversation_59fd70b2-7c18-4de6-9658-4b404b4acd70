# 数据模型: 产品媒体 (ProductMedia)

*   **表名 (逻辑名)**: `ProductMedia`
*   **对应UI界面**: 「製品媒体一覧」 (Product Media List)
*   **主要用途**: 存储产品媒体（例如安装包、ISO文件等）及其关联文档（例如版本说明、手册）的详细信息，供用户在门户上查看和下载。

## 1. 字段定义

| 字段名 (Prisma/英文) | 数据类型 (SQL参考) | PK | FK | UQ | Nullable | Default  | 描述 (中文)                                                                                                                               |
| :------------------- | :----------------- | :--- | :--- | :--- | :------- | :------- | :---------------------------------------------------------------------------------------------------------------------------------------- |
| `id`                 | VARCHAR(36)        | ●    |      |      |          | `cuid()` | 主键。CUID格式，由系统自动生成。                                                                                                            |
| `name`               | NVARCHAR(255)      |      |      |      |          |          | 产品的显示名称。例如："Super Product Suite"。对应原「機能仕様書」中的`productName`。                                                               |
| `productCode`        | VARCHAR(XX)        |      |      | △    |          |          | 产品的唯一代码或型号。与`version`字段共同构成业务唯一标识。                                                                                          |
| `version`            | VARCHAR(50)        |      |      | △    |          |          | 产品的版本号。例如："12.0.1"。与`productCode`字段共同构成业务唯一标识。                                                                                 |
| `os`                 | VARCHAR(50)        |      |      |      |          |          | 产品媒体适用的操作系统。例如："Windows", "Linux"。其值可能来自`Lov`表。对应原「機能仕様書」中的`osType`。                                                           |
| `releasedAt`         | VARCHAR(XX)        |      |      |      |          |          | 产品媒体的发布日期/时间。**注意：Prisma Schema中此字段类型为String，实际存储格式和排序需关注。** 对应原「機能仕様書」中的`releaseDate`。                                            |
| `mediaName`          | VARCHAR(255)       |      |      |      |          |          | 产品媒体文件的名称（例如安装包文件名）。例如："installer_x64.exe"。对应原「機能仕様書」中的`mediaFileName`。                                                   |
| `mediaSize`          | INT                |      |      |      |          |          | 产品媒体文件的大小，单位为字节 (Bytes)。                                                                                                       |
| `bigMediaSize`       | BIGINT             |      |      |      | Yes      |          | **可选**。用于存储超过标准`Int`类型范围的大型产品媒体文件的大小（例如大于2GB的文件）。如果文件大小在`Int`范围内，此字段可为NULL。                                           |
| `documentName`       | VARCHAR(255)       |      |      |      |          |          | 与此产品媒体关联的文档压缩包文件名（例如版本说明、手册等的集合）。例如："docs_bundle.zip"。对应原「機能仕様書」中的`documentFileName`。                                       |
| `documentSize`       | INT                |      |      |      |          |          | 关联文档压缩包文件的大小，单位为字节 (Bytes)。                                                                                                 |

*PK: Primary Key, FK: Foreign Key, UQ: Unique Key, △: Part of Unique Key*
*XX表示具体长度，取决于实际的数据库Schema定义。*

## 2. 关系

*   **对 `PlanProduct` (`planProducts`)**: 一对多关系 (`PlanProduct[]`)。一个产品媒体记录（由`productCode`和`version`唯一确定）可以通过`PlanProduct`表关联到多个契约计划，表示该产品媒体可用于这些计划。

## 3. 唯一约束

*   `UNIQUE KEY (productCode, version)` (Prisma Schema中已定义 `@@unique([productCode, version])`)，确保同一产品的同一版本只有一条记录。

## 4. Azure Blob Storage 路径约定 (推测)

*   产品媒体文件及关联文档文件在Azure Blob Storage中的实际存储路径，通常遵循预定义规则动态构建，例如基于 `productCode`, `version`, `os`, `mediaName` 或 `documentName`。
*   表中不直接存储完整的Blob路径。

## 5. 索引 (示例)

*   `PRIMARY KEY (id)`
*   `UNIQUE KEY UQ_ProductMedia_CodeVersion (productCode, version)` (业务唯一键)
*   `INDEX idx_productmedia_name (name)`
*   `INDEX idx_productmedia_os (os)`
*   `INDEX idx_productmedia_releasedat (releasedAt)` (注意：基于字符串的排序)