# 组件：提供文件列表 (Provided Files List)

## 1. 概要 (Overview)

### 1.1 目的 (Purpose)
本组件为“JCS 端点资产与任务管理系统”的已登录用户（特指顾客系统管理员）提供一个集中的界面，用于浏览和下载由服务方提供的各类辅助文件，例如配置文件模板、实用工具、脚本或其他与服务相关的补充材料。

### 1.2 用户故事/需求 (User Stories/Requirements)
- 作为一名顾客系统管理员，我希望能够查看一个包含所有我当前契约下可用的“提供文件”的列表。
- 作为一名顾客系统管理员，我希望能看到每个提供文件的名称、简要说明、最后更新时间以及文件大小。
- 作为一名顾客系统管理员，我希望能方便地从列表中下载我需要的提供文件。
- 作为一名顾客系统管理员，我希望能根据文件名或说明中的关键字快速筛选和查找我需要的提供文件。

### 1.3 高层依赖与交互 (High-Level Dependencies & Interactions)
- **用户认证与授权**: 用户的身份（特别是关联的契约计划ID）决定了其能在列表中看到哪些提供文件。
- **主界面 (Main Screen)**: 本组件的入口点是[主界面](./02-main-screen.md)侧边栏的“提供ファイル一覧 (提供文件列表)”菜单项。
- **数据存储**:
    - 提供文件的元数据（文件名、说明、最后更新时间、大小、实际存储的文件名等）存储在Azure SQL Database的 `ProvidedFile` 表中。
    - 用户可访问的提供文件范围可能通过 `PlanProvidedFile` 表（关联契约计划ID和文件名）进行控制。
- **文件存储与下载**:
    - 提供文件实际存储在Azure Blob Storage的特定容器（如 `provided-files`）中。
    - 用户下载文件时，前端通过后端API获取一个安全的、有时限的共享访问签名 (SAS) 链接指向Blob Storage中的文件。
- **后端API (Next.js API Routes)**: 前端通过调用后端API来获取用户可见的提供文件列表和生成文件下载链接。

## 2. 功能规格 (Functional Specifications)

### 2.1 主要流程 (High-Level Flow)
1.  用户成功登录系统后，通过侧边导航栏访问“提供ファイル一覧 (提供文件列表)”菜单项。
2.  系统加载并向用户展示其有权访问的提供文件列表，每条记录包含文件名、说明、最后更新时间、文件大小等信息。文件名本身作为可点击的下载链接。
3.  用户可以使用界面提供的筛选框输入关键字，点击搜索或按回车后，列表将根据匹配结果刷新。
4.  用户可以点击列表表头对提供文件信息进行排序。
5.  用户可以点击“文件名”列中的文件名链接来下载对应的文件。
    ```mermaid
    sequenceDiagram
        participant User as 👤 用户 (Browser)
        participant ProvidedFileListScreen as 📄 提供文件列表界面
        participant NextJsApiRoutes as 🌐 Next.js API Routes
        participant ProvidedFileDB as 💾 ProvidedFile/PlanProvidedFile表 (SQL DB)
        participant AzureBlobStorage as ☁️ Azure Blob Storage

        User->>ProvidedFileListScreen: 打开提供文件列表
        ProvidedFileListScreen->>NextJsApiRoutes: 请求提供文件列表数据 (GET /api/provided-files)
        NextJsApiRoutes->>ProvidedFileDB: 查询用户可见的提供文件元数据 (基于用户契约计划ID)
        ProvidedFileDB-->>NextJsApiRoutes: 返回提供文件元数据列表
        NextJsApiRoutes-->>ProvidedFileListScreen: 显示提供文件列表

        alt 用户下载提供文件
            User->>ProvidedFileListScreen: 点击文件A的下载链接
            ProvidedFileListScreen->>NextJsApiRoutes: 请求文件A的下载URL (GET /api/provided-files/A_id/download-url)
            NextJsApiRoutes->>AzureBlobStorage: 为文件A生成SAS Token
            AzureBlobStorage-->>NextJsApiRoutes: 返回带SAS Token的URL
            NextJsApiRoutes-->>ProvidedFileListScreen: 返回安全的下载URL
            ProvidedFileListScreen->>User: (浏览器)开始下载文件
        end
    ```
6.  用户可以使用分页控件浏览更多的提供文件记录。
7.  用户可以点击界面右上角的“更新”图标按钮，手动刷新整个提供文件列表。

### 2.2 业务规则 (Business Rules)
-   **信息来源与可见性**:
    *   提供文件的元数据从门户数据库的 `ProvidedFile` 表中获取。
    *   用户可见的提供文件范围由其契约计划ID通过 `PlanProvidedFile` 表（或类似机制）关联确定。
-   **只读显示**: 列表中的所有提供文件信息均为只读展示。
-   **文件存储路径**: 提供文件存储在Azure Blob Storage的 `provided-files` 容器下。`fs.md` 未明确其内部目录结构，可能直接存放或按某种规则组织。
-   **下载机制**: 用户下载文件时，后端API为目标Blob生成一个具有读取权限且有时间限制（默认为2小时，可由`LOV`表 `AZURE_STORAGE.SAS_TTL_SECONDS` 配置）的共享访问签名 (SAS) Token。
-   **日期时间显示**: 所有日期和时间（如最后更新时间）的显示应遵循用户浏览器的时区设置（格式 `YYYY/MM/DD hh:mm:ss`）。
-   **默认排序** (依据 `fs.md` 对提供ファイル一覧的ソート规则):
    1.  主要排序键：“最終更新日時 (最后更新时间)”，降序（最新的在前）。
    2.  次要排序键：“ファイル名 (文件名)”，字典序升序（A→Z, 0→9）。
-   **文件大小显示**: 文件大小应以合适的单位（KB, MB, GB）显示。

### 2.3 用户界面概述 (User Interface Overview)

-   **界面草图/核心区域**: (参考 `fs.md` 图4.11.6 (1) 作为高层概念)
    1.  **筛选区**: 位于列表上方，提供文本输入框进行关键字筛选，以及搜索和清除按钮。
    2.  **提供文件列表表格**:
        *   列：ファイル名 (文件名，兼作下载链接)、説明 (说明)、最終更新日時 (最后更新时间)、サイズ (文件大小)。
        *   每行代表一个可用的提供文件。
    3.  **分页控件**: 位于列表下方。
    4.  **更新按钮**: 通常位于列表右上角。
-   **主要交互点**:
    *   用户在筛选框中输入文本，进行列表筛选。
    *   用户点击可排序的列标题，进行升序/降序排序。
    *   用户点击“ファイル名”列中的文件名链接，浏览器开始下载对应文件。
    *   用户使用分页控件浏览更多记录。
    *   用户点击“更新”按钮，重新加载列表。
-   **画面項目 (Screen Items - High Level)**:
    *   输入：筛选关键字。
    *   显示：文件名 (可下载)、文件说明、文件最后更新日期和时间、文件大小。
    *   用户可操作：筛选、排序、分页、下载文件。
-   **Tab键顺序** (依据 `fs.md` 描述):
    *   Tab键焦点在列表区域时，应按“ファイル名”列从上到下的顺序遍历下载链接。

### 2.4 前提条件 (Preconditions)
-   用户必须已通过[登录功能](./01-login.md)完成身份验证，并拥有有效的活动会话。
-   系统管理员已在 `ProvidedFile` 数据库表中正确录入了提供文件的元数据，并通过 `PlanProvidedFile` 表（或类似机制）配置了用户契约计划可访问的提供文件范围。
-   对应的提供文件已上传到Azure Blob Storage的 `provided-files` 容器中，并与数据库元数据中的路径一致。
-   后端用于获取提供文件列表和生成下载链接的API端点必须可用。

### 2.5 制约事项 (Constraints/Limitations)
-   **浏览器兼容性**: 仅正式支持 Microsoft Edge 和 Google Chrome 浏览器。
-   **语言支持**: 界面显示语言仅支持日语。
-   **列表性能**: 如果可提供文件数量非常庞大，列表的加载、筛选和分页性能可能会受到影响。

### 2.6 注意事项 (Notes/Considerations)
-   “说明”字段应提供对文件用途的清晰、简洁的描述。
-   “最后更新时间”有助于用户判断文件的时效性。

### 2.7 错误处理概述 (Error Handling Overview)
-   **列表加载失败**: 如果后端API调用失败导致无法获取提供文件列表，界面应向用户显示通用的错误提示信息（例如，“提供文件列表加载失败，请稍后重试。”），并记录详细技术错误。
-   **文件下载失败**:
    *   如果请求下载链接时后端API出错，应提示用户“无法获取下载链接，请重试。”
    *   如果生成的下载链接无效（SAS Token过期、文件在Blob Storage中不存在或无权限），用户点击下载后浏览器层面会报错或下载失败。
-   **无权访问文件**: 如果用户因权限配置问题理论上不应看到任何提供文件，列表应显示为空或特定提示（例如，“当前没有您可访问的提供文件。”）。

### 2.8 相关功能参考 (Related Functional References)
*   **主要入口**: [主界面 (Main Screen)](./02-main-screen.md) - 侧边栏的“提供ファイル一覧”菜单是访问本功能的途径。
*   **认证关联**: [登录 (Login)](./01-login.md) - 用户的登录身份（特别是契约计划ID）决定了其能看到的提供文件范围。
*   **系统整体架构**: `../../architecture/system-architecture.md` - 描述了提供文件数据如何从存储到呈现给用户的流程。
*   **核心数据模型**:
    *   `../../data-models/provided-file-table.md` (假设文件名) - 定义了提供文件元数据的主要来源表结构。
    *   `../../data-models/plan-provided-file-table.md` (假设文件名) - 定义了用户契约计划与可访问提供文件之间的关联。
*   **配置参考**: `../../data-models/lov-table.md` (假设文件名) - 可能包含与SAS Token有效期 (`AZURE_STORAGE.SAS_TTL_SECONDS`) 等相关的配置。
*   **源功能规格**: `fs.md` (通常位于 `docs-delivery/機能仕様書/` 或项目指定的源文档位置) - 本组件功能规格的原始日文描述，特别是其“4.11 提供ファイル一覧”章节。
