/**
 * @file route.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

import { redirect } from "next/navigation";
import {
  LOV_CODE_AZURE_STORAGE_CONTAINER_PROVIDED_FILES,
  PORTAL_ERROR_MESSAGES,
} from "@/app/lib/definitions";
import { BlobActions } from "@/app/lib/integrations/azure-blob";
import Logger from "@/app/lib/logger";
import { ServerDataLov } from "@/app/lib/data/lov";

export async function GET(
  request: Request,
  { params }: { params: { fileName: string } },
) {
  let blobUrlWithSAS;
  const { fileName } = params;

  try {
    const containerLov = await ServerDataLov.fetchLov(
      LOV_CODE_AZURE_STORAGE_CONTAINER_PROVIDED_FILES,
    );

    if (!containerLov) {
      Logger.error({
        message: `LOVでコンテナ設定[${LOV_CODE_AZURE_STORAGE_CONTAINER_PROVIDED_FILES}]が見つかりません。`,
      });

      return new Response(PORTAL_ERROR_MESSAGES.EMEC0007, { status: 500 });
    }

    blobUrlWithSAS = await BlobActions.generateBlobUrlWithSAS(
      containerLov.value,
      [fileName],
    );
  } catch (error: any) {
    Logger.error({ message: error.message, stack: error.stack });

    return new Response(PORTAL_ERROR_MESSAGES.EMEC0007, { status: 500 });
  }
  // SAS トークンを含む Blob URL へのリダイレクト
  redirect(blobUrlWithSAS);
}
