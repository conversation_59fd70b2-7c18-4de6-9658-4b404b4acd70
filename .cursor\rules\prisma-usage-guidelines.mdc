---
description: 
globs: 
alwaysApply: true
---
# Prisma Usage Guidelines (for apps/jcs-endpoint-nextjs)

This document provides guidelines for using Prisma ORM effectively and consistently within the `apps/jcs-endpoint-nextjs` application for all database interactions. Proper Prisma usage is key to data integrity, performance, and maintainable code.

**Primary References & Tools:**
*   `prisma/schema.prisma`: The Single Source of Truth (SSoT) for the database schema, defining all models, fields, relations, and enums.
*   `app/lib/prisma.ts`: Contains the singleton Prisma Client instantiation and configuration for query logging.
*   `app/lib/data.ts`: The `ServerData` class is the primary consumer of Prisma for complex read operations and cached data fetching.
*   `app/lib/actions.ts`: Server Actions use Prisma for data mutations (create, update, delete).
*   Prisma Official Documentation: For detailed API reference and advanced usage patterns.
*   Data model explanations and business context in the Monorepo's `docs/data-models/`.

## Core Principles

1.  **Singleton Prisma Client:** **MUST** always use the shared Prisma Client instance exported from `app/lib/prisma.ts`. Do not instantiate `PrismaClient` directly in other files.
    ```typescript
    import prisma from "@/app/lib/prisma";
    ```
2.  **Schema as SSoT (`prisma/schema.prisma`):** All database schema changes (models, fields, relations, enums, attributes like `@unique`, `@default`) **MUST** be made exclusively in the `prisma/schema.prisma` file. This file dictates the database structure.
3.  **Migrations with Prisma Migrate:** All schema changes **MUST** be applied to the database using Prisma Migrate.
    *   **Development:** Use `npx prisma migrate dev` to create and apply migrations.
    *   **Production:** Follow Prisma's recommended deployment strategy for migrations (typically `npx prisma migrate deploy`).
4.  **Leverage Prisma-Generated Types:** **MUST** use the TypeScript types automatically generated by Prisma for query results, model inputs (e.g., `Prisma.UserCreateInput`), and model representations. Avoid using `any` where a Prisma type is available to ensure type safety.
5.  **Query Efficiency and Best Practices:**
    *   **Select Only Necessary Fields:** Use the `select` or `include` options to retrieve only the data fields required by the specific operation. Avoid fetching entire objects if only a few fields are needed. This minimizes data transfer and can improve database performance.
        ```typescript
        // Good: Select only 'id' and 'email'
        // const userSubset = await prisma.user.findUnique({
        //   where: { id: userId },
        //   select: { id: true, email: true }
        // });
        ```
    *   **Minimize N+1 Queries:** When fetching related data, be mindful of potential N+1 query problems. Use `include` strategically. For complex scenarios with deep relations or large datasets, evaluate if separate batched queries or more optimized raw queries (as a last resort) might be necessary.
    *   **Transactions for Atomic Operations:** For operations involving multiple dependent database writes that must succeed or fail together, **MUST** use Prisma's interactive transactions (`prisma.$transaction(async (tx) => { ... })`) to ensure data atomicity and consistency.
6.  **Centralized Read Operations (`app/lib/data.ts`):**
    *   For most read operations, especially those that benefit from server-side caching (`unstable_cache`), complex filtering, or pagination, Prisma queries should be encapsulated within methods of the `ServerData` class in `app/lib/data.ts`. This promotes reusability and centralizes caching logic.
    *   Direct Prisma read queries in Server Components or API Routes should be limited to very simple, non-cached scenarios, or when `ServerData` methods are not suitable.
7.  **Mutations (Create, Update, Delete):**
    *   Data mutations **MUST** primarily reside within Server Actions (`app/lib/actions.ts`).
    *   Before executing any mutation, **MUST** perform:
        *   User authentication and authorization checks.
        *   Rigorous input validation.
    *   After a successful mutation that affects data displayed elsewhere, **MUST** ensure relevant Next.js server-side caches are revalidated using `revalidateTag()` from `next/cache`.
8.  **Error Handling for Prisma Operations:**
    *   All Prisma calls that can potentially fail (which is most of them) **MUST** be wrapped in `try-catch` blocks.
    *   Prisma throws specific error types (e.g., `Prisma.PrismaClientKnownRequestError`, `Prisma.PrismaClientValidationError`). Catch these for more granular error handling if specific error codes (like `P2002` for unique constraint violations) need distinct user feedback.
    *   Use `isPrismaError(error)` from `app/lib/portal-error.ts` for a general check if an error originated from Prisma.
    *   Log all Prisma-related errors with detailed context using `Logger.error()` from `app/lib/logger.ts`. Refer to `Error Handling Guidelines`.
9.  **Query Logging (Development):**
    *   Prisma query logging is enabled at the `debug` level in `app/lib/prisma.ts`. This is useful for observing executed queries during development. Ensure `ENV.LOG_LEVEL` is not set to `debug` in production environments unless actively troubleshooting performance issues.

## Common Prisma Operations (Illustrative Examples)

1.  **Fetching a Single Record by ID:**
    ```typescript
    // const user = await prisma.user.findUnique({
    //   where: { id: userId },
    // });
    ```
2.  **Fetching Multiple Records with Conditions:**
    ```typescript
    // const activeUsers = await prisma.user.findMany({
    //   where: { isActive: true, role: 'ADMIN' },
    //   orderBy: { createdAt: 'desc' },
    // });
    ```
3.  **Creating a New Record:**
    ```typescript
    // const newLicense = await prisma.license.create({
    //   data: {
    //     licenseId: generateNewLicenseId(), // Ensure unique
    //     type: LicenseType.PROD,
    //     expiredAt: calculateExpiryDate(),
    //     maxClients: 100,
    //     // ... other required fields from schema.prisma
    //   },
    // });
    ```
4.  **Updating an Existing Record:**
    ```typescript
    // const updatedProfile = await prisma.userProfile.update({
    //   where: { userId: userId },
    //   data: { bio: "New bio content" },
    // });
    ```
5.  **Deleting a Record:**
    ```typescript
    // await prisma.oplog.delete({
    //   where: { id: oplogId },
    // });
    ```
6.  **Fetching Records with Relations (using `include`):**
    ```typescript
    // const licenseWithPlans = await prisma.license.findUnique({
    //   where: { licenseId: currentLicenseId },
    //   include: {
    //     licensePlans: { // Relation name from schema.prisma
    //       include: {
    //         plan: true // Include the related Plan record
    //       }
    //     }
    //   }
    // });
    ```

---

**NOTE TO CURSOR:**
1.  Always import and use the shared Prisma Client instance from `app/lib/prisma.ts`.
2.  When generating or modifying database queries, prioritize type safety by using Prisma-generated types and select only the fields necessary for the operation.
3.  For read operations intended for UI display, first check if a suitable data-fetching method already exists in `ServerData` (`app/lib/data.ts`) before adding new direct Prisma calls.
4.  Place all data mutation logic (create, update, delete) within Server Actions (`app/lib/actions.ts`). These actions **MUST** include proper input validation, authorization checks, comprehensive error handling (logging and user feedback), and cache revalidation logic.
5.  The `prisma/schema.prisma` file is the definitive source for all model names, field names, types, and relations. Consult it directly.
6.  Refer to the Monorepo's `docs/data-models/` directory for additional business context and detailed explanations related to the Prisma models.


---