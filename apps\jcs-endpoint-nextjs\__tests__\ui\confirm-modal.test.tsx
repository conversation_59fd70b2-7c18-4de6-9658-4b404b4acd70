/**
 * @jest-environment jsdom
 */

// Web API polyfills
Object.defineProperty(global, 'Response', {
  value: class Response {
    constructor(_body?: any, _init?: any) {}
    static json(data: any) { return new Response(JSON.stringify(data)); }
  }
});
Object.defineProperty(global, 'Request', {
  value: class Request {
    constructor(_input: any, _init?: any) {}
  }
});

import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import ConfirmModal from "@/app/ui/ConfirmModal";

/**
 * @fileoverview ConfirmModal コンポーネントの単体テスト
 * @description 通用二次確認モーダルコンポーネントの表示制御、ボタン機能、テキスト内容検証を行う。
 * モーダルの基本UI要素表示、カスタムボタンテキスト、確認・キャンセル動作、許可テキスト制御を検証する。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

describe("ConfirmModal", () => {
  const mockProps = {
    isOpen: true,
    title: "確認",
    message: "この操作を実行しますか？",
    onConfirm: jest.fn(),
    onCancel: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  /**
   * 試験観点：モーダル非表示時の動作確認
   * 試験対象：ConfirmModal コンポーネントの表示制御
   * 試験手順：
   * 1. isOpen=falseでコンポーネントをレンダリング
   * 確認項目：
   * - モーダルが表示されないこと
   */
  it("正常系: モーダル非表示時はnullを返す", () => {
    const { container } = render(
      <ConfirmModal {...mockProps} isOpen={false} />
    );
    expect(container.firstChild).toBeNull();
  });

  /**
   * 試験観点：モーダル基本UI要素の表示確認
   * 試験対象：ConfirmModal コンポーネントの基本UI表示機能
   * 試験手順：
   * 1. 正常なpropsでコンポーネントをレンダリング
   * 確認項目：
   * - タイトルが表示されること
   * - メッセージが表示されること
   * - OKボタンが表示されること
   * - キャンセルボタンが表示されること
   */
  it("正常系: モーダル基本UI要素の表示", () => {
    render(<ConfirmModal {...mockProps} />);

    expect(screen.getByText("確認")).toBeInTheDocument();
    expect(screen.getByText("この操作を実行しますか？")).toBeInTheDocument();

    // ボタンをテキストで検索（Spinnerの隠しテキストを考慮）
    expect(screen.getByRole("button", { name: /OK/ })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /キャンセル/ })).toBeInTheDocument();
  });

  /**
   * 試験観点：カスタムボタンテキストの表示確認
   * 試験対象：ConfirmModal コンポーネントのカスタムボタンテキスト表示
   * 試験手順：
   * 1. カスタムボタンテキストを指定してレンダリング
   * 確認項目：
   * - カスタムボタンテキストが表示されること
   */
  it("正常系: カスタムボタンテキストの表示", () => {
    render(
      <ConfirmModal
        {...mockProps}
        confirmText="実行"
        cancelText="中止"
      />
    );

    // ボタンをテキストで検索（Spinnerの隠しテキストを考慮）
    expect(screen.getByRole("button", { name: /実行/ })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /中止/ })).toBeInTheDocument();
  });

  /**
   * 試験観点：確認ボタンクリック時の動作確認
   * 試験対象：ConfirmModal コンポーネントの確認機能
   * 試験手順：
   * 1. 確認ボタンをクリック
   * 確認項目：
   * - onConfirmコールバックが呼び出されること
   */
  it("正常系: 確認ボタンクリック時の動作", async () => {
    render(<ConfirmModal {...mockProps} />);

    // ボタンを正しい方法で検索してクリック（Spinnerの隠しテキストを考慮）
    await userEvent.click(screen.getByRole("button", { name: /OK/ }));

    expect(mockProps.onConfirm).toHaveBeenCalledTimes(1);
  });

  /**
   * 試験観点：キャンセルボタンクリック時の動作確認
   * 試験対象：ConfirmModal コンポーネントのキャンセル機能
   * 試験手順：
   * 1. キャンセルボタンをクリック
   * 確認項目：
   * - onCancelコールバックが呼び出されること
   */
  it("正常系: キャンセルボタンクリック時の動作", async () => {
    render(<ConfirmModal {...mockProps} />);

    await userEvent.click(screen.getByRole("button", { name: /キャンセル/ }));

    expect(mockProps.onCancel).toHaveBeenCalledTimes(1);
  });

  /**
   * 試験観点：必須テキストの表示確認
   * 試験対象：ConfirmModal コンポーネントの必須テキスト表示
   * 試験手順：
   * 1. コンポーネントをレンダリング
   * 2. 必須テキストがすべて表示されていることを確認
   * 確認項目：
   * - 必須テキストがすべて表示されていること
   */
  it("必須テキストがすべて表示されること", () => {
    render(<ConfirmModal {...mockProps} />);

    // 必須テキスト
    const requiredTexts = [
      "確認",
      "この操作を実行しますか？",
      "OK",
      "キャンセル",
    ];

    const bodyText = document.body.textContent || "";
    
    // 必須テキストがすべて存在することを確認
    requiredTexts.forEach(requiredText => {
      expect(bodyText).toContain(requiredText);
    });
  });

  /**
   * 試験観点：許可されたテキストのみ表示確認
   * 試験対象：ConfirmModal コンポーネントのテキスト表示制御
   * 試験手順：
   * 1. コンポーネントをレンダリング
   * 2. 許可されたテキストのみが表示されていることを確認
   * 確認項目：
   * - 許可されていないテキストが表示されていないこと
   */
  it("許可されたテキストのみ表示されること", () => {
    render(<ConfirmModal {...mockProps} />);

    // 許可されたテキストの完全リスト（Spinnerの隠しテキストを含む）
    const allowedTexts = [
      "確認",
      "この操作を実行しますか？",
      "OK",
      "キャンセル",
      "Loading...", // Spinnerコンポーネント内のsr-onlyテキスト
      "モーダルを閉じる",
    ];

    const bodyText = document.body.textContent || "";
    
    // 許可されたテキストをすべて除去
    let remainingText = bodyText;
    allowedTexts.forEach(allowedText => {
      remainingText = remainingText.replace(new RegExp(allowedText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), '');
    });
    
    // 残ったテキストから空白文字を除去
    const unauthorizedText = remainingText.replace(/[\s\n\r\t]+/g, '').trim();
    
    // 許可されていないテキストがある場合はテストを失敗させる
    if (unauthorizedText.length > 0) {
      expect(`許可されていないテキストが検出されました: "${unauthorizedText}"`).toBe('');
    }
  });
});
