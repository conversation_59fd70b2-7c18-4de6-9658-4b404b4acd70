/**
 * @file route.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

import { redirect } from "next/navigation";
import {
  LOV_CODE_AZURE_STORAGE_CONTAINER_PRODUCT_MEDIAS,
  PORTAL_ERROR_MESSAGES,
} from "@/app/lib/definitions";
import { BlobActions } from "@/app/lib/integrations/azure-blob";
import Logger from "@/app/lib/logger";
import { ServerDataLov } from "@/app/lib/data/lov";

export async function GET(
  request: Request,
  {
    params,
  }: { params: { productCode: string; version: string; fileName: string } },
) {
  let blobUrlWithSAS;
  const { productCode, version, fileName } = params;

  try {
    const containerLov = await ServerDataLov.fetchLov(
      LOV_CODE_AZURE_STORAGE_CONTAINER_PRODUCT_MEDIAS,
    );

    if (!containerLov) {
      Logger.error({
        message: `LOVでコンテナ設定[${LOV_CODE_AZURE_STORAGE_CONTAINER_PRODUCT_MEDIAS}]が見つかりません。`,
      });

      return new Response(PORTAL_ERROR_MESSAGES.EMEC0007, { status: 500 });
    }

    blobUrlWithSAS = await BlobActions.generateBlobUrlWithSAS(
      containerLov.value,
      [productCode, version, fileName],
    );
  } catch (error: any) {
    Logger.error({ message: error.message, stack: error.stack });

    return new Response(PORTAL_ERROR_MESSAGES.EMEC0007, { status: 500 });
  }
  // SAS トークンを含む Blob URL へのリダイレクト
  redirect(blobUrlWithSAS);
}
