# 技术栈与构建系统

## 核心技术栈

- **前端**: Next.js (React框架)
- **后端**: Azure Functions (Node.js)
- **自动化**: Azure Automation Runbooks (PowerShell)
- **数据库**: 使用 Prisma ORM 进行数据库访问
- **包管理**: pnpm (Monorepo 包管理工具)

## 开发环境

### 必要工具

- Node.js
- pnpm
- Azure Functions Core Tools
- PowerShell (用于自动化脚本)

## 常用命令

### 项目设置

```bash
# 安装依赖
pnpm install
```

### 本地开发环境启动

```bash
# Windows CMD
scripts\start-local-env.bat

# Windows PowerShell
scripts\start-local-env.ps1

# Linux/macOS
scripts/start-local-env.sh
```

### 测试环境启动 (E2E测试)

```bash
# Windows PowerShell
scripts\start-local-env.ps1 -Test

# Linux/macOS
scripts/start-local-env.sh --test
```

### 构建与部署

```powershell
# 构建和打包 Azure Functions
scripts\build-and-package-functions.ps1

# 部署 Runbooks 到本地环境
scripts\deploy-runbooks-locally.ps1
```

### 文档开发

```bash
# 启动文档开发服务器
npm run docs:dev
```

### 测试工具

```bash
# 生成测试Excel
npm run generate-test-excel

# 创建测试模板
npm run create-test-template

# 生成客户测试报告
npm run generate-customer-report
```

## 环境变量管理

- Next.js 应用使用 `.env.local` 文件进行本地开发配置
- Azure Functions 使用 `local.settings.json` 进行本地配置
- 测试环境使用 `.env.test` 和测试专用的 `local.settings.json` 配置

详细的环境变量说明请参考 `docs/guides/environment-variables.md`。