/**
 * @fileoverview Playwright配置 - 仅Mock Server测试
 * @description
 * 专门用于测试Mock Server功能的简化Playwright配置。
 * 不启动Next.js和Azure Functions，只测试Mock Server本身。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright配置 - 仅Mock Server
 */
export default defineConfig({
  testDir: './specs',
  
  // 全局设置
  fullyParallel: false, // 禁用并行执行，避免端口冲突
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: 1, // 单线程执行
  
  // 报告设置 - 不自动打开HTML报告
  reporter: [
    ['html', { outputFolder: '../test-results/html-report', open: 'never' }],
    ['json', { outputFile: '../test-results/results.json' }],
    ['junit', { outputFile: '../test-results/junit.xml' }],
    ['list']
  ],
  
  // 输出设置
  outputDir: '../test-results/artifacts',
  
  // 全局测试配置
  use: {
    // 基础URL（Mock Server测试不需要）
    // baseURL: 'http://localhost:3001',
    
    // 浏览器设置
    headless: true,
    viewport: { width: 1280, height: 720 },
    ignoreHTTPSErrors: true,
    
    // 截图和视频
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    
    // 跟踪
    trace: 'retain-on-failure',
    
    // 超时设置
    actionTimeout: 10000,
    navigationTimeout: 30000,
  },
  
  // 项目配置
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
      testMatch: [
        '**/azure-automation-mock.spec.ts',
        '**/build-check.spec.ts',
        '**/beforeall-services-demo.spec.ts',
        '**/step-by-step-validation.spec.ts',
        '**/azurite-test.spec.ts',
        '**/complete-export-beforeall.spec.ts'
      ]
    },
  ],
  
  // 不启动任何Web服务器
  // webServer: undefined,
  
  // 全局设置和清理
  globalSetup: undefined,
  globalTeardown: undefined,
  
  // 超时设置
  timeout: 60000, // 单个测试60秒超时
  expect: {
    timeout: 10000, // 断言10秒超时
  },
});
