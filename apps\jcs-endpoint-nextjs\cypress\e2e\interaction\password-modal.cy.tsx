describe("画面操作のテスト", () => {
  describe("パスワード変更ダイアログ", () => {
    const validCredentials = {
      userId: "hitachi.hanako.ab",
      password: "changeit!@#",
      newPassword: "pass1234",
    };

    const threeKindsCredentials = {
      userId: "hitachi.hanako.ab",
      password: "changeit!@#",
      newPassword: "pass1234!@",
    };

    beforeEach(() => {
      cy.visit("/login");
      cy.get("#userId").type(validCredentials.userId);
      cy.get("#password").type(validCredentials.password);
      cy.get("button").click();
    });

    it("キャンセルボタンをクリックすると、ダイアログが閉じられる", () => {
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password-modal button").contains("キャンセル").click();
      cy.get("#password-modal").should("have.class", "hidden");
    });

    it("✖アイコンをクリックすると、ダイアログが閉じられる", () => {
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password-modal button svg.h-3").click();
      cy.get("#password-modal").should("have.class", "hidden");
    });

    it("OKボタンをクリックすると、成功のメッセージが表示される", () => {
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password").type(validCredentials.password);
      cy.get("#newPassword").type(validCredentials.newPassword);
      cy.get("#confirmPassword").type(validCredentials.newPassword);
      cy.get("#password-modal button").contains("OK").click();
      cy.contains("パスワードを変更しました。");
      cy.visit("/dashboard/servers");
      cy.title().should("eq", "サーバ一覧");
      cy.get("button").contains("ログアウト").click();
      cy.get("#logout-modal button").contains("OK").click();
      cy.url().should("eq", Cypress.config().baseUrl + "/login");
      cy.get("#userId").type(validCredentials.userId);
      cy.get("#password").type(validCredentials.newPassword);
      cy.get("button").click();
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password").type(validCredentials.newPassword);
      cy.get("#newPassword").type(validCredentials.password);
      cy.get("#confirmPassword").type(validCredentials.password);
      cy.get("#password-modal button").contains("OK").click();
      cy.contains("パスワードを変更しました。");
    });

    it("3種類以上の文字の組み合わせパスワードが変更できる", () => {
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password").type(threeKindsCredentials.password);
      cy.get("#newPassword").type(threeKindsCredentials.newPassword);
      cy.get("#confirmPassword").type(threeKindsCredentials.newPassword);
      cy.get("#password-modal button").contains("OK").click();
      cy.contains("パスワードを変更しました。");
      cy.get("#message-modal button").contains("OK").click();
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password").type(threeKindsCredentials.newPassword);
      cy.get("#newPassword").type(threeKindsCredentials.password);
      cy.get("#confirmPassword").type(threeKindsCredentials.password);
      cy.get("#password-modal button").contains("OK").click();
      cy.contains("パスワードを変更しました。");
    });

    it("パスワードを2回連続で変更する", () => {
      cy.contains("サーバ一覧").should("be.visible");
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password").type(validCredentials.password);
      cy.get("#newPassword").type(validCredentials.newPassword);
      cy.get("#confirmPassword").type(validCredentials.newPassword);
      cy.get("#password-modal button").contains("OK").click();
      cy.contains("パスワードを変更しました。");
      cy.get("#message-modal button").contains("OK").click();
      cy.get("button").contains("パスワード変更").click();
      cy.get("#password").type(validCredentials.newPassword);
      cy.get("#newPassword").type(validCredentials.password);
      cy.get("#confirmPassword").type(validCredentials.password);
      cy.get("#password-modal button").contains("OK").click();
      cy.contains("パスワードを変更しました。");
    });
  });
});
