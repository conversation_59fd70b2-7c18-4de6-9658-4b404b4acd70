# AI助手操作指南：将 `server-list-detailed-design.md` 更新至 `server-list-detailed-design.xlsx` (修订版)

**核心任务**: 根据提供的源Markdown文件 (`server-list-detailed-design.md`) 内容，更新目标Excel文件 (`server-list-detailed-design.xlsx`) 中对应的Sheet页和单元格。假设Markdown内容已经是最终审查确认的版本，AI的主要工作是数据的准确提取和按结构填充。**采用小步快走的方式，分段交付并请求确认。**

**通用指令**:

1.  **严格一对一映射**: 你的首要任务是确保Markdown中的每一个信息点都准确无误地反映在Excel的对应位置。不要对内容本身进行修改、增删或主观解读。
2.  **遵循Excel结构与层次体现**:
    *   严格按照目标Excel文件预设的Sheet页名称、表格列标题和顺序进行数据填充。
    *   **对于Markdown中的列表或层级结构（例如，事件定义下的处理步骤），在Excel中应参考现有表格的行/列布局来体现层次。如果Excel模板中没有明确的多级列结构，可以将下一级内容填充到当前行相邻的、更靠右的列的单元格中，以视觉上形成层级。如果无法通过这种方式清晰表达，请明确指出，并请求用户指导如何在Excel中组织这些层级内容。**
3.  **格式保真**:
    *   Markdown表格单元格内的换行符 (`<br>`) 应转换为Excel单元格内的换行。
    *   代码块内容（如Prisma查询示例）应以纯文本形式填充到指定单元格，或按Excel中预设的格式要求处理。
4.  **分段交付与确认**: **在处理完Markdown文档中的一个逻辑完整的“小块”内容（例如一个完整的表格定义、一个主要章节的一个子节的所有项目、一个事件的完整处理步骤）后，立即将该部分的Excel填充结果（或生成的数据/指令）提供给用户，并请求确认。得到用户确认后，再继续处理下一部分。**

**具体操作步骤**:

**1. 打开并准备源Markdown文件和目标Excel文件。**

**2. 逐个处理Markdown文件中的主要章节，并将其内容填充到Excel对应的Sheet页。处理完每个逻辑子块后，输出结果并等待用户确认。**

   以下是主要章节与Excel Sheet页的对应关系及处理要点：

   *   **Markdown: `## 機能概要` (及其子章节 `### 基本情報`, `### 機能説明`)**
      *   **Excel Sheet**: 通常名为 `機能概要` 或类似。
      *   **操作**:
          *   **交付块1**: 提取`### 基本情報`表格中的“項目”和“内容”，输出用于填充Excel对应两列表格的数据。**【等待用户确认】**
          *   **交付块2**: 提取`### 機能説明`下的段落文本，输出用于填充Excel功能描述对应单元格的文本。**【等待用户确认】**
          *   **交付块3**: 提取“本機能の主要な技術構成要素を以下に示す”表格的内容，输出用于填充Excel技术构成表格区域的数据。**【等待用户确认】**

   *   **Markdown: `## 画面レイアウト` (及其子章节 `### 基本情報`)**
      *   **Excel Sheet**: 通常名为 `画面レイアウト` 或 `画面一覧`。
      *   **操作**:
          *   **交付块**: 提取`### 基本情報`表格中的“項目”和“内容”，以及备注（星号部分），输出用于填充Excel的数据。**【等待用户确认】**

   *   **Markdown: `## 画面項目定義` (及其子章节 `### ヘッダ部情報`, `### ボディ部情報`, `### モーダルダイアログ共通項目定義`, 各具体モーダル定義)**
      *   **Excel Sheet**: 通常名为 `画面項目定義`。
      *   **操作**:
          *   **针对每个Markdown表格 (例如“ヘッダ部情報”表格) 分别处理并交付**:
              *   **交付块 (例如，“ヘッダ部情報”数据)**: 将该Markdown表格的每一行数据，严格按照Excel列名映射，输出用于填充Excel对应区域表格行的数据。特别注意“フォーマット/備考”列的换行。**【等待用户确认】**
          *   (对“ボディ部情報”表格重复此交付模式，然后对每个模态框定义表格也重复此交付模式)

   *   **Markdown: `## 参照値一覧定義` (及其子章节 `### サーバ種別`, `### タスク種別` 等)**
      *   **Excel Sheet**: 通常名为 `コード定義` 或 `参照値一覧`。
      *   **操作**:
          *   **针对每个参照值类别 (例如“サーバ種別”) 分别处理并交付**:
              *   **交付块 (例如，“サーバ種別”数据)**: 将其表格内容按Excel列映射输出。**【等待用户确认】**
          *   (对其他参照值类别重复此交付模式)

   *   **Markdown: `## エラーメッセージ一覧`**
      *   **Excel Sheet**: 通常名为 `メッセージ一覧` 或 `エラーメッセージ`。
      *   **操作**:
          *   **交付块**: 将Markdown中的整个错误消息表格内容，按Excel列映射输出。**【等待用户确认】**

   *   **Markdown: `## イベント定義` (及其子章节，如 `### イベント1：初期表示`)**
      *   **Excel Sheet**: 通常名为 `イベント定義`。
      *   **操作**:
          *   **针对每个“イベント”分别处理并交付**:
              *   **交付块 (例如，“イベント1：初期表示”数据)**: 提取事件ID（若有）、事件名、触发器，并将“データ取得要求”和“画面表示”的步骤描述（注意层级关系，可使用相邻右列单元格表示子步骤）按Excel列映射输出。**【等待用户确认】**
          *   (对其他事件重复此交付模式)

   *   **Markdown: `## タスク受付処理詳細 (`createTaskAction` Server Action)` (及其所有子章节)**
      *   **Excel Sheet**: 通常名为 `サーバ処理詳細_createTaskAction` 或类似。
      *   **操作 (分块交付)**:
          *   **交付块1 (概要、输入、输出)**: 提取`### 概要` (`#### 責務`, `#### 入力パラメータ`, `#### 返り値`) 的表格和文本描述，输出用于填充Excel对应区域的数据。**【等待用户确认】**
          *   **交付块2 (共通処理フローMermaid图占位)**: *提示用户：“请将Markdown中的createTaskAction通用处理流程Mermaid图导出为图片，并将其粘贴到Excel的[Sheet名]的[单元格范围或位置说明]。”* **【等待用户确认】**
          *   **交付块3 (共通処理手順)**: 将编号的通用处理步骤描述，按Excel格式（注意层级）输出。**【等待用户确认】**
          *   **针对每个`### 特定タスク種別処理ロジック` 分别处理并交付**:
              *   **交付块 (例如，操作日志导出特定逻辑)**: 提取其特有参数校验、`parametersJson`构造、Service Bus消息参数构造等描述，输出用于填充Excel的数据。**【等待用户确认】**
          *   **交付块4 (エラー処理メカニズム)**: 提取描述文本，输出。**【等待用户确认】**
          *   **交付块5 (依存データサービス)**: 提取列表信息，输出。**【等待用户确认】**

   *   **Markdown: `## 主要環境変数とその影響`**
      *   **Excel Sheet**: 通常名为 `環境変数一覧`。
      *   **操作**:
          *   **交付块**: 将Markdown中的环境变数表格内容，按Excel列映射输出。**【等待用户确认】**

   *   **Markdown: `## 主要機能シーケンス図` (及其三个子序列图)**
      *   **Excel Sheet**: 通常名为 `シーケンス図`。
      *   **操作**:
          *   **针对每个序列图分别处理并交付**:
              *   **交付块 (例如，サーバ一覧 データ表示序列图占位)**: *提示用户：“请将Markdown中的‘サーバ一覧 データ表示シーケンス図’导出为图片，并将其粘贴到Excel的[Sheet名]中，配上标题‘サーバ一覧 データ表示シーケンス図’。”* **【等待用户确认】**
          *   (对其他序列图重复此交付模式)

   *   **Markdown: `## テーブル定義` (及其子表格 `### テーブル1：サーバ (Server)`, `### テーブル2：値の一覧 (Lov)`)**
      *   **Excel Sheet**: 通常名为 `テーブル定義一覧`。
      *   **操作**:
          *   **针对每个表定义分别处理并交付**:
              *   **交付块 (例如，“テーブル1：サーバ (Server)”数据)**: 将其表格的每一行数据按Excel列映射输出。**【等待用户确认】**
          *   (对其他表定义重复此交付模式)

   *   **Markdown: `## データ取得クエリ定義` (及其子章节)**
      *   **Excel Sheet**: 通常在“処理詳細”或“備考”部分。
      *   **操作**:
          *   **交付块1 (目的与考虑事项)**: 提取“目的”和“主要な考慮事項”的文本内容，输出。**【等待用户确认】**
          *   **交付块2 (Prisma查询示例)**: 提取Typescript代码块内容和其前的注释，输出为纯文本。**【等待用户确认】**

**3. 所有分块内容均获用户确认后，表示本次迁移更新任务完成。**

**AI助手执行时的注意事项 (重申)**:

*   **处理单元格合并**: 如果Excel模板中存在合并单元格，你需要知道如何将Markdown的单行或多行内容正确填充进去。如果无法直接操作，请明确指出需要人工调整的合并单元格。
*   **长文本处理**: 对于Markdown备注中较长的文本，确保在Excel单元格中能正确显示（例如，通过自动换行）。
*   **保持耐心和精确**: 严格按照用户的反馈进行修正，确保每一小块交付的准确性。
