import prisma from "@/app/lib/prisma";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { revalidateTag, unstable_cache } from "next/cache";
import { cookies } from "next/headers";
import {
  ENV,
  PORTAL_CACHE_KEY_MANUALS
} from "../definitions";
import { LogFunctionSignature } from "../logger";
import { handleServerError } from "../portal-error";
import { formatBytes } from "../utils";

/**
 * マニュアル（ProductManual）関連のデータ操作を行う静的クラスです。
 * マニュアルのキャッシュ取得、ページ数計算、フィルタ・ソート処理を担当します。
 */
export class ServerDataManuals {
  /**
   * 指定されたライセンスIDに紐づくマニュアル一覧をキャッシュ付きで取得します。
   * キャッシュキーとタグにライセンスIDを含めることで、正確なキャッシュ無効化を実現します。
   *
   * @param {string} licenseId - ライセンスID
   * @returns {Promise<any[]>} マニュアル情報配列
   */
  static async fetchCachedManuals(licenseId: string) {
    const cachedFn = unstable_cache(
      async () => {
        const plansWithLicense = await prisma.license.findUnique({
          where: { licenseId },
          select: {
            licensePlans: { select: { planId: true } },
          },
        });
        const planManuals = await prisma.planManual.findMany({
          where: {
            planId: {
              in: plansWithLicense!.licensePlans.map((plan) => plan.planId),
            },
          },
          distinct: ["serialNo"],
          include: { productManual: true },
        });
        return planManuals.map((planManual) => {
          const { productManual } = planManual;
          return {
            ...productManual,
            formattedSize: formatBytes(productManual.size),
          };
        });
      },
      [`${PORTAL_CACHE_KEY_MANUALS}-${licenseId}`],
      {
        revalidate: ENV.APP_CACHE_TTL_SECONDS,
        tags: [`${PORTAL_CACHE_KEY_MANUALS}-${licenseId}`],
      },
    );
    return await cachedFn();
  }

  /**
   * マニュアル一覧のページ数を取得します。
   *
   * @param {string} filter - フィルタ文字列
   * @param {number} size - 1ページあたりの件数
   * @param {boolean} refresh - キャッシュ強制リフレッシュ有無
   * @returns {Promise<number>} ページ数
   */
  @LogFunctionSignature()
  static async fetchProductManualPages(filter: string, size: number, refresh: boolean) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);
      if (refresh) {
        revalidateTag(`${PORTAL_CACHE_KEY_MANUALS}-${session!.user.licenseId}`);
      }
      const cachedManuals = await this.fetchCachedManuals(session!.user.licenseId);
      if (cachedManuals) {
        if (filter) {
          const filteredManuals = cachedManuals.filter(
            (manual) =>
              manual.name.toLowerCase().includes(filter.toLowerCase()) ||
              manual.serialNo.toLowerCase().includes(filter.toLowerCase()) ||
              manual.formattedSize.toLowerCase().includes(filter.toLowerCase()),
          );
          return Math.ceil(Number(filteredManuals.length) / size);
        } else {
          return Math.ceil(Number(cachedManuals.length) / size);
        }
      } else {
        return 0;
      }
    } catch (error) {
      handleServerError(error);
    }
  }

  /**
   * フィルタ・ソート・ページング済みのマニュアル一覧を取得します。
   *
   * @param {string} filter - フィルタ文字列
   * @param {number} size - 1ページあたりの件数
   * @param {number} page - ページ番号
   * @param {"name"|"serialNo"|"size"} sort - ソートキー
   * @param {"asc"|"desc"} order - ソート順
   * @returns {Promise<any[]>} マニュアル情報配列
   */
  @LogFunctionSignature()
  static async fetchFilteredProductManuals(
    filter: string,
    size: number,
    page: number,
    sort: "name" | "serialNo" | "size",
    order: "asc" | "desc",
  ) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);
      const cachedManuals = await this.fetchCachedManuals(session!.user.licenseId);
      if (cachedManuals) {
        let filteredManuals = cachedManuals;
        if (filter) {
          filteredManuals = cachedManuals.filter(
            (manual) =>
              manual.name.toLowerCase().includes(filter.toLowerCase()) ||
              manual.serialNo.toLowerCase().includes(filter.toLowerCase()) ||
              manual.formattedSize.toLowerCase().includes(filter.toLowerCase()),
          );
        }
        if (sort) {
          filteredManuals.sort((a, b) => {
            if (sort === "size") {
              if (order === "asc") {
                return a[sort] - b[sort];
              } else {
                return b[sort] - a[sort];
              }
            } else {
              const aValue = a[sort].toLowerCase();
              const bValue = b[sort].toLowerCase();
              if (order === "asc") {
                return aValue.localeCompare(bValue);
              } else {
                return bValue.localeCompare(aValue);
              }
            }
          });
        }
        const startIndex = (page - 1) * size;
        const endIndex = startIndex + size;
        const paginatedManuals = filteredManuals.slice(startIndex, endIndex);
        return paginatedManuals;
      } else {
        return [];
      }
    } catch (error) {
      handleServerError(error);
    }
  }
} 