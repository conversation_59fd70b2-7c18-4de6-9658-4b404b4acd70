// 创建一个简单的模拟对象
export const prismaMock = {
  containerConcurrencyStatus: {
    findUnique: jest.fn(),
  },
  task: {
    findMany: jest.fn(),
  },
  lov: {
    findMany: jest.fn(),
    findFirst: jest.fn(),
  },
  server: {
    findUnique: jest.fn(),
  },
  license: {
    findUnique: jest.fn(),
  },
} as any;

// 添加一个空的测试用例以避免Jest报错
describe("test-utils", () => {
  it("should export prismaMock", () => {
    expect(prismaMock).toBeDefined();
  });
});
