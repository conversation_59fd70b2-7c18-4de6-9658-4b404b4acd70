# 系统错误与消息定义 (Error and Message Definitions)

本文档定义了“JCS 端点资产与任务管理系统”中核心的错误消息。这些错误消息主要源自项目的功能需求和用户交互场景中需要向用户提供的明确反馈，旨在为用户在遇到问题或操作成功/需要确认时提供必要的指导。
它是项目内部开发、AI编程以及测试团队理解和使用系统错误消息的主要参考。

**核心原则**:
*   **SSoT (Single Source of Truth)**: 本文档是系统中用户可见的、已规范化管理的错误消息及其相关定义的唯一事实来源。其内容的设计依据源自项目的功能需求和客户确认的规格。
*   **消息ID (Message ID)**: 代码中应通过消息ID直接引用这些错误消息，以便于维护和查找。所有需要在代码中引用的、已规范化的错误消息都必须在此文档中拥有一个唯一的“消息ID”。
*   **用户界面语言**: 尽管本文档以中文描述为主，但“用户提示文本 (日文)”列提供了最终显示给用户的日文界面文本。
*   **描述独立性**: 中文场景描述应尽可能独立易懂，避免过多依赖读者对系统内部架构（如特定函数名或表名）的预先了解。

## 1. 门户端错误与提示消息 (Portal Error and Notification Messages)
*(这些消息主要在用户与门户界面直接交互时产生，严格遵循客户评审结论。)*

| 消息ID (Message ID) | 用户提示文本 (日文) | 中文场景描述与说明 (Scenario Description - Chinese) |
|-------------------|-------------|--------------------------------------------|
| `EMEC0002` | 確認用パスワードが正しくありません。 | **密码确认不匹配**: 用户在修改密码操作中，输入的“新密码”与“确认用密码”不一致。 |
| `EMEC0003` | 現在のパスワードが正しくありません。 | **当前密码无效**: 用户在修改密码操作中，输入的“当前密码”不符合系统规定的格式或验证失败（例如，长度不在8至128字符范围内，或包含不允许的字符）。 |
| `EMEC0005` | 現在ポータルは利用できません。 | **门户服务不可用 (环境禁用)**: 系统检测到当前用户所属的许可证或整体服务环境已被管理员设置为禁用状态 (`License.isDisabled` 为 `true`)，导致无法登录或使用门户。 |
| `EMEC0006` | データベースに一時的に接続できません。しばらくしてから再度ポータルにアクセスしてください。 | **数据库连接临时失败 (门户后端)**: 门户系统后端在尝试访问其核心数据库（Azure SQL Database）以读取或写入数据时遇到连接问题。 |
| `EMEC0007` | サーバに一時的に接続できません。しばらくしてから再度ポータルにアクセスしてください。 | **外部服务连接临时失败 (门户后端)**: 门户系统后端在尝试连接其依赖的某个关键外部服务（例如，Azure Blob Storage 进行文件操作，或 Keycloak 进行身份验证相关操作）时发生网络或连接错误。 |
| `EMEC0008` | パスワードを変更しました。 | **密码修改成功提示**: 用户的密码已成功修改。 |
| `EMEC0009` | 新しいパスワードは8文字以上、128文字以下のパスワードを入力してください。 | **新密码长度无效**: 用户在设置新密码时，输入的密码长度不符合系统规定的8至128个字符的范围。 |
| `EMEC0010` | 新しいパスワードには2種類以上の文字の組み合わせを入力してください。 | **新密码字符类型不足**: 用户在设置新密码时，未包含系统要求的多种字符类型的组合（具体组合规则由Keycloak密码策略定义，通常指大小写字母、数字、特殊符号中的至少两种）。 |
| `EMEC0011` | 新しいパスワードにはユーザーIDと異なる文字列を入力してください。 | **新密码与用户ID相同**: 用户在设置新密码时，输入的新密码与其当前的登录用户ID相同。 |
| `EMEC0012` | 新しいパスワードには現在のパスワードと異なる文字列を入力してください。 | **新密码与当前密码相同**: 用户在设置新密码时，输入的新密码与其当前的密码相同。 |
| `EMEC0013` | パスワード変更に失敗しました。 | **密码修改失败 (通用)**: 用户的密码修改请求因某种原因未能成功完成（可能是后端处理错误或Keycloak通信问题）。 |
| `EMEC0014` | ワンタイムコードが正しくありません。 | **OTP无效 (格式或位数)**: 用户在进行多因素认证时输入的一次性密码（OTP）包含非数字内容，或其位数不符合Keycloak配置的预期位数（通常为6位）。 |
| `EMEC0015` | 現在のパスワードまたはワンタイムコードが正しくありません。 | **凭证或OTP无效 (认证失败)**: 用户尝试登录或执行需要强认证的操作时，提供的用户ID与当前密码的组合不正确，或者输入的一次性密码（OTP）与移动认证应用中显示的不一致或已失效。 |
| `EMEC0016` | {0}を指定してください。 | **字段必填**: 用户在提交表单或发起操作时，某个必需的输入字段 `{0}` 未被填写。`{0}` 将被替换为该字段的日文界面名称。 |
| `EMEC0017` | 無効なファイル形式です。CSVファイルを指定してください。 | **无效文件类型 (特指CSV)**: 用户在执行“管理项目定义导入”功能时，尝试上传了一个非CSV格式的文件。 |
| `EMEC0018` | ファイルのアップロードに失敗しました。時間をおいてから再度実行してください。 | **文件上传失败**: 用户在门户界面尝试上传文件（例如，管理项目定义导入）时，由于网络错误或服务器端接收处理错误，文件未能成功上传到Azure Blob Storage。 |
| `EMEC0019` | サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0019) | **任务/取消请求提交后端失败**: 门户后端（Next.js API Route / Server Action）在处理用户发起的后台任务或任务中止请求时，未能成功将其发送到消息队列（Azure Service Bus）。`{0}` 将被替换为“開始”或“中止”。 |
| `EMEC0020` | {0} 日を超える期間が指定されました。{0} 日以内の期間を指定して再度実行してください。 | **日期范围超限 (操作日志导出)**: 用户在请求导出操作日志时，选择的日期范围天数超过了值列表（LOV 表中 `OPERATION_LOG_CONFIG.MAX_EXPORT_DAYS_SPAN`）配置的最大允许天数。`{0}` 将被替换为该最大天数值。 |
| `EMEC0021` | サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0021) | **资源状态检查或参数校验失败 (门户后端)**: 门户在发起任务或取消任务前的预处理（例如，Next.js API Route / Server Action 通过Prisma查询数据库以确认目标服务器/容器状态，或进行参数校验）时发生失败。`{0}` 将被替换为“開始”或“中止”。 |
| `EMEC0022` | {0}に対するタスクを実行中のため実行できません。<br/>実行中のタスクが完了してから再度実行してください。 | **目标服务器繁忙 (门户反馈)**: 用户尝试为名为 `{0}` 的服务器发起新的后台任务时，系统通过查询 `ContainerConcurrencyStatus` 表检测到该服务器对应的容器当前已有其他任务正在执行中。 `{0}` 将被替换为目标服务器名称。 |
| `EMEC0023` | タスクの実行がすでに開始したため中止できませんでした。 | **任务已开始无法中止 (门户反馈)**: 用户尝试中止一个先前状态为 `QUEUED` (界面显示“実行待ち”) 的任务，但在后端处理该中止请求时，发现该任务的状态已变为 `RUNBOOK_SUBMITTED` (作业已提交) 或更后的阶段，因此无法中止。 |
| `EMEC0024` | 終了日は開始日以降の日付を指定してください。 | **结束日期早于开始日期**: 用户在操作日志导出任务中选择日期范围时，指定的结束日期早于开始日期。 |
| `EMEC0025` | タスクの実行を受け付けました。実行状態はタスク一覧でご確認ください。<br/>対象サーバ：{0}<br/>タスク種別：{1} | **任务接收成功提示**: 系统已成功接收用户发起的后台任务执行请求，并已将任务信息发送到后端处理队列。`{0}` 将被替换为目标服务器名称，`{1}` 将被替换为任务类型名称。 |
| `EMEC0026` | タスクの中止を受け付けました。最新の状態はタスク一覧をご確認ください。<br/>タスク名：{0} | **任务中止接收成功提示**: 系统已成功接收用户发起的任务中止请求，并已将中止指令发送到后端处理队列。`{0}` 将被替换为目标任务的名称。 |
| `EMEC0027` | サーバの接続に失敗したため、タスクを{0}できませんでした。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMEC0027) | **未预期的内部错误 (门户后端)**: 门户后端（Next.js API Route / Server Action）在处理用户发起的后台任务或任务中止请求时，遇到了一个未被分类的内部程序性或逻辑性错误。`{0}` 将被替换为“開始”或“中止”。 |

## 2. 后端任务处理错误与消息 (Task Processing Errors and Messages - Azure Function/Runbook related)
*(这些消息通常在后台任务执行过程中发生，并通过任务列表的“タスク詳細”功能展示给用户，严格遵循客户评审结论。)*

| 消息ID (Message ID) | 用户提示文本 (日文) | 中文场景描述与说明 (Scenario Description - Chinese) |
|-------------------|-------------|--------------------------------------------|
| `EMET0001` | {0}に対するタスクを実行中のため実行できません。<br/>実行中のタスクが完了してから再度実行してください。 | **容器繁忙 (后端检测)**: 后端服务（如`TaskExecuteFunc`）在尝试获取并发锁时，检测到指定的目标容器（通过查询`ContainerConcurrencyStatus`表，其`status`为`BUSY`）当前正在执行其他任务，因此无法处理新的任务请求。`{0}` 将被替换为目标服务器名称。 |
| `EMET0002` | タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0002) | **临时工作区访问失败 (后端执行期间)**: 后台任务在执行过程中访问Azure Files存储区（用于临时文件或工作数据）时失败（例如，目录或文件创建、读取、写入或删除操作遇到权限或连接问题）。 |
| `EMET0003` | タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0003) | **最终产物或临时文件存储/访问失败 (后端执行期间)**: 后台任务在执行过程中访问Azure Blob Storage时失败（例如，上传管理项目定义导入的CSV文件，下载导入文件到工作区，或归档最终导出结果时遇到权限或连接问题）。 |
| `EMET0004` | ユーザーによってタスクが中止されました。 | **任务被用户中止 (最终状态)**: 先前已提交的任务已被用户成功中止（由`TaskCancellationFunc`处理完成）。 |
| `EMET0005` | タスクの完了が確認できないため、系统によってタスクを中止しました。タスクが操作ログのエクスポートの場合、エクスポートするログの期間を短くして再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMET0005) | **任务执行超时 (系统中止)**: Azure Functions (`TaskExecuteFunc`, `RunbookProcessorFunc` 等) 的执行时间超出了配置的最大允许时长 (`FUNCTION_TIMEOUT_SECONDS`)，或者Runbook作业的执行时间超出了配置的最大允许时长 (`RUNBOOK_TIMEOUT_SECONDS`)，导致任务被系统判断为超时并中止。 |
| `EMET0006` | タスクの中止がタイムアウトしました。時間をおいてから再度実行してください。同じメッセージが表示される場合は、サポートサービスにお問い合わせください。(EMET0006) | **取消请求处理超时**: Azure Functions的`TaskCancellationFunc`在处理任务中止请求时，其自身执行时间超出了配置的最大允许时长 (`FUNCTION_TIMEOUT_SECONDS`)。此消息也可能用于`TaskCancellationTimeoutFunc`处理原`PENDING_CANCELLATION`状态超时的情况。 |
| `EMET0007` | タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0007) | **数据库更新错误 (后端通用)**: 后台任务处理过程中，与Azure SQL Database进行交互（例如，记录任务状态、更新数据、管理并发锁）时发生失败。 |
| `EMET0008` | タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0008) | **未预期的内部错误 (后端通用)**: 后台系统（Azure Functions或相关服务）在执行任务时遇到了一个未被分类的内部程序性或逻辑性错误。 |
| `EMET0009` | タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0009) | **执行任务所需配置或参数缺失/无效 (后端准备阶段)**: 后台服务（如`TaskExecuteFunc`）在准备执行任务时，未能从`Task`记录的参数中获取到必要的配置信息（例如，目标VM名称、HRW组名或容器名称），或这些信息无效。 |
| `EMET0010` | システムによりタスクの実行を中止しました。タスクの実行中にサービスのメンテナンスが開始された可能性があります。サービスのメンテナンス終了後に再度実行してください。 | **系统中止任务 (维护或运维操作)**: 由于端点管理服务进行维护，或服务运维人员因紧急故障响应等原因，通过Azure Automation层面或其他机制强制中止了正在执行的Runbook作业。 |
| `EMET0011` | タスクの実行がすでに開始したため中止できませんでした。 | **任务已开始无法中止 (后端检测)**: 用户针对一个状态为 `PENDING_CANCELLATION` (或极少数情况下的 `QUEUED`) 的任务发起了中止操作，但在后端`TaskCancellationFunc`实际处理该中止请求时，任务的状态已经变更为 `RUNBOOK_SUBMITTED`, `RUNBOOK_PROCESSING`, 或已是终态，因此无法中止。 |
| `EMET0012` | タスクの実行に失敗しました。<br/>エラー詳細：<br/>{0} | **Runbook脚本执行失败 (有详细错误)**: 后台执行的Runbook（基盘脚本）在执行过程中发生错误，并输出了详细的错误信息。可能的错误原因包括业务错误（如导入的CSV文件格式错误）或内部错误（如参数不正确、文件访问失败、内存不足、数据库访问错误、目标服务（如JP1/ITDM2 Manager）停止等）。`{0}` 将被替换为从Runbook错误流或工作区内`errordetail.txt`文件中获取的基盘脚本错误消息。 |
| `EMET0013` | タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0013) | **Runbook作业启动失败 (后端)**: `TaskExecuteFunc`在调用Azure Automation API提交Runbook作业时失败，导致基盘脚本未能启动。 |
| `EMET0014` | タスクの実行に失敗しました。サポートサービスにお問い合わせください。(EMET0014) | **Azure Automation API调用失败 (后端通用)**: 后端服务（如`TaskExecuteFunc`, `RunbookMonitorFunc`, `RunbookProcessorFunc`, 或各超时处理函数）在调用Azure Automation API（例如创建作业、获取作业状态、停止作业）时发生失败。 |