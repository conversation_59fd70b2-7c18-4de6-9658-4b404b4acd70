# 資産配布管理システム

このプロジェクトは、Next.js と NextAuth.js を使用したセキュリティ認証と通知管理アプリケーションです。

## 特徴

- 認証と通知管理機能を備えた、Next.js で構築されたフロントエンドアプリケーションです。
- ユーザー認証とセッション管理には NextAuth.js を使用しています。
- フロントエンドとバックエンドの通信は、安全な API とデータベースを介してデータのやり取りを行います。
- 通知管理ページでは最新の通知とログイン監査ログを表示します。
- データベース操作には Prisma を使用し、通知の内容とログイン監査ログを管理します。
- データの取得と管理には SWR を使用し、データのリアルタイム性とパフォーマンスを確保します。

## ファイル構造

プロジェクトのファイル構造は以下の通りです：

```
├── components/         # 再利用可能な UI コンポーネントを保存
├── hooks/              # 再利用可能なカスタムフックを保存
├── lib/                # 汎用ユーティリティやロジックを保存
├── app/                # Next.js ページのディレクトリ
├── public/             # 画像や静的ファイルなどの公開リソースを保存
├── .env.example        # 環境変数の例示ファイル
├── README.md           # プロジェクトの説明ファイル
└── package.json        # プロジェクトの設定ファイル
```

## インストールと実行

1. プロジェクトリポジトリをローカルにクローンします：
```bash
git clone https://github/your-username/your-project.git
```


2. プロジェクトディレクトリに移動し、依存関係をインストールします：

```bash
cd your-project
npx pnpm install
```

3. `.env.example` ファイルを `.env.local` としてコピーし、環境変数を設定します：

```bash
MSSQL_PRISMA_URL=sqlserver://*************:1433;database=jp2;user=SA;password=Uknowit1^_^;encrypt=DANGER_PLAINTEXT
NEXTAUTH_SECRET=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
... 他の環境変数
```

4. 開発サーバーを起動します：
```bash
npm run dev
```


5. ブラウザで http://localhost:3000 にアクセスしてアプリケーションを表示します。

## Prisma データベース操作

Prisma を使用してデータベース操作を行います。データベースのテーブルやデータモデルに対応する Prisma モデルを作成し、そのモデルを使用してクエリを実行します。データベースのマイグレーションやデータのシーディングにも Prisma を使用することができます。

## データベースのマイグレーションとシーディング

プロジェクトのルートディレクトリで、以下のコマンドを使用して Prisma マイグレーションを実行します。

```sh
npx prisma migrate dev
```
マイグレーションが完了したら、データベースを更新するために次のコマンドを実行します。
```sh
npx prisma db push
```
シーディングを使用してデータをデータベースに追加することもできます。シーディングスクリプトを作成し、データベースに初期データを挿入します。
```sh
npx prisma db seed
```

## コードガイドと解説

CodeTour は、Visual Studio Code の拡張機能です。開発チームがコードを理解し、協力し、学習するのを支援します。コード片と解説、注釈、リンクを関連付けて対話型のコードガイドを作成することができます。以下は CodeTour のインストールとブラウジングの手順です。

### 1. CodeTour のインストール

1. Visual Studio Code（VSCode）を開きます。

2. 左側のサイドバーで「Extensions」をクリックします（またはショートカットキー `Ctrl+Shift+X` または `Cmd+Shift+X` を使用します）。

3. 検索バーに "CodeTour" と入力し、Enter キーを押して検索します。

4. 検索結果で "CodeTour" プラグインを見つけて "Install" をクリックしてインストールします。

5. インストールが完了すると、拡張機能バーに "CodeTour" アイコンが表示されます。

### 2. CodeTour のブラウジング

[Navigating Tours](https://github.com/microsoft/codetour#navigating-tours) を参照してください。

1. ブラウジングしたいコードプロジェクトまたはファイルを開きます。

2. VSCode 内で、左側の CodeTour アイコンをクリックして CodeTour パネルを開きます。

3. CodeTour パネル内で、作成された CodeTour（あれば）が表示されます。

4. 興味のある CodeTour を選択し、"Start Tour" をクリックしてブラウジングを開始します。

5. CodeTour が進行中の間、コードスニペット、コメント、解説、リンクなどが表示されます。

6. 各ステップでコード、コメント、関連するリソースを確認することができます。一部のステップでは、特定の操作を実行するよう指示されることがあります（リンクをクリックしたり、コードを確認したり）。

7. ステップ内では、ナビゲーションボタン（次のステップ、前のステップ、終了など）を使用して異なるステップに移動できます。

8. CodeTour を完了した後、CodeTour パネルから他の CodeTour を選択してブラウジングできます。

## 単体テストとテストカバレッジレポート

この文書では、プロジェクトでの単体テストとテストカバレッジレポートの実行方法について説明します。

### 1. 単体テストの実行

プロジェクトの単体テストを実行するには、次のコマンドを使用します：

```shell
npm run test:ut
```

このコマンドは Jest およびフロントエンドの単体テストをトリガーします。単体テストは、コードの各単体（関数、コンポーネントなど）が期待どおりに機能しているかを確認するツールです。テストの実行が完了すると、テストカバレッジレポートが生成されます。

### 2. 生成された結果

単体テストの実行後、2つのテストカバレッジレポートファイルが生成されます：

. `reports/from-cypress.json`：これは Cypress テストからのテストカバレッジレポートファイルです。
. `reports/from-jest.json`：これは Jest テストからのテストカバレッジレポートファイルです。

これらの 2 つのレポートには、テスト中のコードのカバレッジ情報が含まれており、テストの品質とカバレッジ範囲を評価するのに役立ちます。
また、カバレッジ情報は `coverage` フォルダー内にも保存され、必要に応じて参照できます。
 