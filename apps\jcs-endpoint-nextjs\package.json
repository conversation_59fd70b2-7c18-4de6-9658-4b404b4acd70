{"private": true, "author": {"name": "WSST"}, "version": "2.0.0", "license": "Copyright (C) 2024, 2025, Hitachi Solutions, Ltd.", "scripts": {"dev": "prisma generate && next dev -H 0.0.0.0", "dev:test": "prisma generate && set NODE_ENV=test&& next dev -H 0.0.0.0", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "cypress": "cypress open", "test": "jest"}, "dependencies": {"@azure/identity": "^4.10.2", "@azure/service-bus": "^7.9.3", "@azure/storage-blob": "^12.17.0", "@prisma/client": "^4.14.0", "@types/react": "^18.0.25", "clsx": "^2.0.0", "dayjs": "^1.11.10", "flowbite": "^2.1.1", "iron-session": "^8.0.3", "jsonwebtoken": "^9.0.2", "next": "^13.5.6", "react": "^18.2.0", "react-icons": "^4.10.1", "swr": "^2.2.1", "uuid": "^11.1.0", "winston": "^3.11.0"}, "devDependencies": {"@cypress/code-coverage": "^3.11.0", "@faker-js/faker": "^8.3.1", "@testing-library/jest-dom": "5.16.4", "@testing-library/react": "14.0.0", "@testing-library/user-event": "14.4.3", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^18.16.12", "@types/react": "18.0.28", "@types/testing-library__jest-dom": "^5.14.5", "autoprefixer": "^10.4.4", "check-code-coverage": "^1.10.5", "cypress": "^12.17.4", "cypress-plugin-tab": "^1.0.5", "cypress-real-events": "^1.10.1", "encoding": "^0.1.13", "eslint": "8.11.0", "eslint-config-next": "^13.0.5", "jest": "29.5.0", "jest-environment-jsdom": "29.5.0", "jest-mock-extended": "^3.0.5", "node-mocks-http": "^1.13.0", "postcss": "^8.4.12", "prettier": "^3.1.0", "prettier-plugin-organize-attributes": "^1.0.0", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-tailwindcss": "^0.5.7", "prisma": "^4.14.0", "start-server-and-test": "^2.0.0", "tailwindcss": "^3.0.23", "ts-node": "^10.9.1", "typescript": "^4.9.5"}, "nyc": {"all": true, "excludeAfterRemap": true, "exclude": ["cypress/**/*.*", "coverage/**/*.*", "prisma/**/*.*", "types/**/*.*", "__tests__/**/*.*", "*.*"], "report-dir": "cypress-coverage"}, "prettier": {"printWidth": 80}}