import React from "react";
import NotificationList from "@/app/ui/notification-list";

describe("お知らせコンポーネントのテスト", () => {
  it("お知らせデータが存在しない場合、メッセージが表示される", () => {
    // お知らせコンポーネントをマウント
    cy.mount(<NotificationList api="/api/notifications" />);
    // お知らせデータをモックして空のデータを返す
    cy.intercept("GET", "/api/notifications", {
      statusCode: 200,
      body: [],
    });
    // 「お知らせはありません。」というメッセージが表示されることを確認
    cy.contains("お知らせはありません。");
  });

  it("複数のお知らせが存在する場合、公開日の降順で表示される", () => {
    // お知らせデータをモックして複数のデータを返す
    cy.intercept("GET", "/api/notifications", {
      statusCode: 200,
      body: [
        {
          id: "id2222222222",
          content: "明日はイベントがありますので、お忘れなく参加してください。",
          publishedAt: "2023/08/02",
        },
        {
          id: "id1111111111",
          content: "これは重要なお知らせの内容です。",
          publishedAt: "2023/08/01",
        },
      ],
    });
    cy.wait(3000);
    // お知らせコンポーネントをマウント
    cy.mount(<NotificationList api="/api/notifications" />);
    // データが表示されるのを待つ（適切なデータの順序で表示されていることを確認）
    cy.get(":nth-child(1) .whitespace-pre-line").contains(
      "2023/08/02 明日はイベントがありますので、お忘れなく参加してください。",
    );
    cy.get(":nth-child(2) .whitespace-pre-line").contains(
      "2023/08/01 これは重要なお知らせの内容です。",
    );
  });

  it("お知らせの内容に改行文字 '\n' が含まれている場合、表示される内容は改行されて表示される", () => {
    // お知らせデータをモックして改行文字を含むデータを返す
    cy.intercept("GET", "/api/notifications", {
      statusCode: 200,
      body: [
        {
          id: "id2222222222",
          content:
            "明日はイベントがありますので、\nお忘れなく参加してください。",
          publishedAt: "2023/08/02",
        },
      ],
    });
    // データが表示されるのを待つ（改行が適切に表示されていることを確認）
    cy.wait(3000);
    // お知らせコンポーネントをマウント
    cy.mount(<NotificationList api="/api/notifications" />);
    // 高さが40以上であることを確認（改行が反映されていることを確認）
    cy.get(":nth-child(1) .whitespace-pre-line")
      .invoke("height")
      .should("be.gt", 40);
  });
});
