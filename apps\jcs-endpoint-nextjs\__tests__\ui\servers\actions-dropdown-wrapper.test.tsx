/**
 * @jest-environment jsdom
 */
// Node/バックエンド関連の依存を完全にモックし、ESM構文エラーを防止する
jest.mock("@azure/storage-blob", () => ({}));
jest.mock("@azure/service-bus", () => ({}));
jest.mock("@azure/msal-node", () => ({}));
jest.mock("@azure/identity", () => ({}));
jest.mock("next/server", () => ({}));
jest.mock("next/dist/server/web/exports/next-response", () => ({}));

// Next.js App Routerのフックをモックし、テスト環境でのエラーを防ぐ
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    replace: jest.fn(),
    push: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
  }),
  usePathname: () => "/mock-path",
  useSearchParams: () => ({
    get: jest.fn(),
    set: jest.fn(),
    entries: () => [],
    keys: () => [],
    values: () => [],
    toString: () => "",
  }),
}));

// Responseのpolyfill（ReferenceError防止）
(global as any).Response = function() {};

// ServerActionsDropdownコンポーネントをモック
jest.mock("@/app/ui/servers/actions-dropdown", () => {
  return function MockServerActionsDropdown(props: any) {
    const { serverId } = props;

    return (
      <div data-testid={`dropdown-${serverId}`}>
        <button>タスクを選択</button>
      </div>
    );
  };
});

// Spinnerコンポーネントをモック
jest.mock("@/app/ui/spinner", () => {
  return function MockSpinner() {
    return <div data-testid="spinner">Loading...</div>;
  };
});

import { render, screen, waitFor, act } from "@testing-library/react";
import ServerActionsDropdownWrapper from "@/app/ui/servers/actions-dropdown-wrapper";

/**
 * @fileoverview サーバ操作ドロップダウンラッパーコンポーネントのテスト
 * @description サーバ操作ドロップダウンラッパーコンポーネントの初期読み込み状態管理、Spinner表示、透明プレースホルダー表示機能を検証する。
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

describe("ServerActionsDropdownWrapper", () => {
  const mockProps = {
    serverId: "test-server-1",
    serverName: "テストサーバー",
    serverTypeCode: "SERVER_TYPE.GENERAL_MANAGER",
    canExportOplog: true,
    maxExportDaysSpan: 30,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  /**
   * 試験観点：初期読み込み状態でのSpinner表示
   * 試験対象：ServerActionsDropdownWrapper コンポーネントの初期読み込み状態
   * 試験手順：
   * 1. コンポーネントをレンダリング
   * 2. 初期状態でSpinnerが表示されることを確認
   * 確認項目：
   * - 初期状態でSpinnerが表示されること
   * - ServerActionsDropdownが表示されないこと
   */
  it("正常系: 初期読み込み状態でSpinnerを表示する", () => {
    render(<ServerActionsDropdownWrapper {...mockProps} />);

    // Spinnerが表示されることを確認
    expect(screen.getByTestId("spinner")).toBeInTheDocument();
    
    // ServerActionsDropdownは表示されないことを確認
    expect(screen.queryByTestId(`dropdown-${mockProps.serverId}`)).not.toBeInTheDocument();
  });

  /**
   * 試験観点：読み込み完了後のServerActionsDropdown表示
   * 試験対象：ServerActionsDropdownWrapper コンポーネントの読み込み完了状態
   * 試験手順：
   * 1. コンポーネントをレンダリング
   * 2. 100ms経過後にServerActionsDropdownが表示されることを確認
   * 確認項目：
   * - 読み込み完了後にServerActionsDropdownが表示されること
   * - Spinnerが表示されないこと
   */
  it("正常系: 読み込み完了後にServerActionsDropdownを表示する", async () => {
    render(<ServerActionsDropdownWrapper {...mockProps} />);

    // 100ms経過させる
    act(() => {
      jest.advanceTimersByTime(100);
    });

    await waitFor(() => {
      // ServerActionsDropdownが表示されることを確認
      expect(screen.getByTestId(`dropdown-${mockProps.serverId}`)).toBeInTheDocument();
      expect(screen.getByText("タスクを選択")).toBeInTheDocument();
    });

    // Spinnerが表示されないことを確認
    expect(screen.queryByTestId("spinner")).not.toBeInTheDocument();
  });

  /**
   * 試験観点：ドロップダウンが表示されない場合の透明プレースホルダー表示
   * 試験対象：ServerActionsDropdownWrapper コンポーネントの透明プレースホルダー表示機能
   * 試験手順：
   * 1. ドロップダウンが表示されない条件でコンポーネントをレンダリング
   * 2. 100ms経過後に透明プレースホルダーが表示されることを確認
   * 確認項目：
   * - ドロップダウンが表示されない場合に透明プレースホルダーが表示されること
   * - 固定サイズが適用されていること
   */
  it("正常系: ドロップダウンが表示されない場合に透明プレースホルダーを表示する", async () => {
    const propsWithNoActions = {
      ...mockProps,
      serverTypeCode: "SERVER_TYPE.HIBUN_CONSOLE", // 操作不可のサーバタイプ
      canExportOplog: false,
    };

    const { container } = render(<ServerActionsDropdownWrapper {...propsWithNoActions} />);

    // 100ms経過させる
    act(() => {
      jest.advanceTimersByTime(100);
    });

    await waitFor(() => {
      // 透明プレースホルダーが表示されることを確認（invisibleクラスを持つdiv要素）
      const placeholder = container.querySelector('div.invisible');
      expect(placeholder).toBeInTheDocument();

      // 固定サイズクラスが適用されていることを確認
      expect(placeholder).toHaveClass('w-56', 'min-w-[180px]', 'max-w-[240px]', 'px-3', 'py-2', 'rounded', 'h-8');
    });

    // ServerActionsDropdownは表示されないことを確認
    expect(screen.queryByTestId(`dropdown-${propsWithNoActions.serverId}`)).not.toBeInTheDocument();
  });

  /**
   * 試験観点：Spinnerプレースホルダーの固定サイズ確認
   * 試験対象：ServerActionsDropdownWrapper コンポーネントのSpinnerプレースホルダー
   * 試験手順：
   * 1. コンポーネントをレンダリング
   * 2. Spinnerプレースホルダーに固定サイズクラスが適用されていることを確認
   * 確認項目：
   * - Spinnerプレースホルダーに正しい固定サイズクラスが適用されていること
   */
  it("正常系: Spinnerプレースホルダーに固定サイズが適用されている", () => {
    const { container } = render(<ServerActionsDropdownWrapper {...mockProps} />);

    // Spinnerプレースホルダーのコンテナを取得
    const spinnerContainer = container.querySelector('div');

    // 固定サイズクラスが適用されていることを確認
    expect(spinnerContainer).toHaveClass('w-56', 'min-w-[180px]', 'max-w-[240px]', 'px-3', 'py-2', 'rounded', 'h-8');
    expect(spinnerContainer).toHaveClass('flex', 'items-center', 'justify-center');
  });
});
