# JCS 端点资产与任务管理系统 - 架构设计文档

## 1. 引言 (Introduction)

### 1.1. 项目背景与目标 (Project Background and Goals)

随着企业IT环境日益复杂，对端点资产的有效管理和相关任务的自动化执行变得至关重要。本项目“JCS 端点资产与任务管理系统”（以下简称“本系统”）旨在为用户（通常为顾客系统管理者）提供一个集中化、易于使用的门户，以提升其对端点虚拟机 (VM) 资产的管理效率，并自动化执行后台任务（如数据导入、多文件日志导出与压缩等）。

**开发背景**:
为应对日益增长的IT资产管理需求，并提升用户在多产品环境下的操作效率，本项目旨在提供一个统一的、通过浏览器访问的门户系统，作为使用资产分发管理服务的核心窗口。该门户需要整合来自不同管理工具（如JP1/ITDM2、秘文管理控制台）的资产信息概览，并允许用户深入到各产品原生界面进行详细操作。同时，系统管理员（资产分发管理服务提供方）负责部署和维护门户系统及其依赖的后端服务。

**核心目标**:
*   **集中管理与可视性**: 提供一个集中化平台，用于管理端点VM上的资产，并展示来自多个源系统（JP1/ITDM2，秘文等）的整合信息。
*   **任务自动化**: 自动化执行常见的后台管理任务，如数据导出、导入、日志收集等，减少人工操作，提高效率和准确性。
*   **用户友好的交互**: 通过Web浏览器提供直观的用户界面，方便用户访问和使用服务。
*   **可靠性与健壮性**: 确保任务执行的可靠性、可管理性，以及脚本的健壮性与版本控制。
*   **Azure PaaS原生**: 充分利用Azure PaaS服务（如Azure Functions, Azure Service Bus, Azure Automation, Azure SQL Database, Azure Storage）构建云原生解决方案，以获得高可用性、可伸缩性和可维护性。

### 1.2. 设计哲学与核心原则 (Design Philosophy and Core Principles)

本系统的设计遵循以下哲学和核心原则，以确保其满足业务需求并具备良好的技术特性：

1.  **信赖Azure Automation调度与执行**: 核心后台任务的执行依赖Azure Automation服务及其Hybrid Runbook Worker (HRW) 架构。系统充分利用Azure Automation成熟的作业调度、队列管理、安全执行和生命周期控制能力。Runbook本身专注于业务逻辑执行，其最终结果由外部监控服务轮询获取。
2.  **流程清晰与解耦**: 业务流程（如任务提交、状态更新、超时处理）主要通过Azure Service Bus进行异步消息传递，实现各组件间的松耦合，增强系统的弹性和可扩展性。
3.  **Runbook作业的独立生命周期与串行核心逻辑**: 在HRW上执行的Runbook作业不受云端沙箱时限，其核心业务步骤（如调用容器内工具、处理输出、打包）按顺序串行执行，确保操作的原子性和可预测性。
4.  **明确的并发控制与超时管理**:
    *   **容器级并发**: 通过数据库表 (`ContainerConcurrencyStatus`) 对特定VM上特定Docker容器的访问进行串行化控制，防止资源冲突。门户后端 ([`createTaskAction` Server Action设计文档](../components/actions/create-task-action.md)) 在检查并发状态时，若记录不存在则创建初始记录；实际的并发锁的获取与释放在任务生命周期的关键节点由后端服务 ([`TaskExecuteFunc`组件设计文档](../components/backend-services-functions/function-task-execute.md), [`RunbookProcessorFunc`组件设计文档](../components/backend-services-functions/function-runbook-processor.md) 等) 负责。
    *   **主动超时监控与结果轮询 ([`RunbookMonitorFunc`](../components/backend-services-functions/function-runbook-monitor.md))**: 通过专用的、定时触发的Azure Function ([`RunbookMonitorFunc`组件设计文档](../components/backend-services-functions/function-runbook-monitor.md)) 对长时间运行的Runbook作业进行监控，并将这些作业的原始结束事件作为消息发送到消息队列供后续处理。
    *   **统一的最终结果处理与资源回收 ([`RunbookProcessorFunc`](../components/backend-services-functions/function-runbook-processor.md))**: 通过专用的Azure Function ([`RunbookProcessorFunc`组件设计文档](../components/backend-services-functions/function-runbook-processor.md)) 消费来自[`RunbookMonitorFunc`](../components/backend-services-functions/function-runbook-monitor.md)的结果消息，负责处理所有Runbook作业的最终状态更新、文件归档、并发锁释放、工作区清理，以及在必要时调用Azure Automation API停止作业。
    *   **组件级超时感知**: 各Azure Function设计为短时执行，并明确其自身的超时限制和对长时间运行Runbook的异步解耦。
5.  **用户体验优先**: 提供简洁直观的Web门户，简化用户操作，清晰展示任务状态和结果。
6.  **安全第一**: 遵循最小权限原则，优先使用托管身份进行服务间认证，通过Keycloak进行用户身份认证，并考虑网络和数据安全。
7.  **可观测性**: 通过Azure Monitor等工具实现全面的日志记录、性能监控和告警，确保系统运行状态透明化。
8.  **文档作为单一事实来源 (SSoT)**: 本文档库 (`docs/` 目录) 是系统设计、规格和技术细节的权威来源。本架构设计文档提供高层视图，各核心组件的详细技术规格在其位于 `docs/components/` 目录下的独立设计文档中定义。

### 1.3. 文档范围与目标读者 (Scope and Target Audience)

本文档是“JCS 端点资产与任务管理系统”的**系统架构设计文档 (SAD)**。它描述了系统的整体架构、核心组件（高层职责）、组件间的关键交互、主要技术选型、以及核心设计原则和非功能性需求考量。

**范围**:
*   系统的高层架构和逻辑组件划分。
*   核心组件的职责概述、主要接口概念和关键行为。
*   主要的数据流和控制流。
*   技术选型及其理由。
*   关键的非功能性需求的设计考量。

**目标读者**:
*   系统架构师和设计师、开发团队、测试团队、运维团队、项目经理和产品负责人、AI编程助手。

**本文档不包含**:
*   各组件的详细技术规格（如UI精确布局、API具体参数、Server Action内部步骤、Azure Function详细算法、DB精确字段定义等）。这些详细信息请参考各自独立的组件设计文档，主要位于 `docs/components/` 目录下（例如，UI组件如[`服务器列表功能设计`](../components/03-servers/server-list.md)，Server Actions如[`createTaskAction Server Action设计文档`](../components/actions/create-task-action.md)，Azure Functions如[`TaskExecuteFunc`组件设计文档](../components/backend-services-functions/function-task-execute.md)，数据模型文档如[`Task数据模型`](../data-models/task.md)等）。数据库结构的权威来源是 `prisma/schema.prisma` 文件。

### 1.4. 核心约束与假设 (Key Constraints and Assumptions)

本系统的设计和实现受到以下核心约束和假设的影响：

*   **VM端约束**: 不部署自定义编译型Agent；所有VM端操作基于PowerShell脚本；VM基础环境由VM所有者负责。
*   **用户身份认证**: 严格依赖外部Keycloak。
*   **API授权 (初始阶段)**: 仅验证Token合法性，暂无细粒度角色权限。
*   **身份管理**: Azure服务间优先使用托管身份。
*   **技术栈限制**: Azure PaaS, Next.js, PowerShell。
*   **浏览器兼容性**: Microsoft Edge, Google Chrome (现代版本)，日语环境。
*   **开发与部署环境**: Monorepo (参见[`Monorepo项目实施指南`](../guides/monorepo-structure-and-deployment-workflow.md))；Next.js本地Git部署；Functions Zip包部署；Runbooks本地脚本部署；基础设施手动配置。

## 2. 系统概述 (System Overview)

### 2.1. 整体功能描述 (Overall Functional Description)

本系统为资产分发管理服务的用户（顾客系统管理者）和系统管理员（服务提供方）提供一个基于Web的门户。

**对于顾客系统管理者**:
*   **统一访问入口**: 集中使用各项服务。
*   **信息整合与查阅**: 整合JP1/ITDM2和秘文信息；查阅通知、许可证、媒体、手册、文件和支持信息。
*   **任务自动化与管理**: 执行服务器后台任务（如操作日志导出、管理项目定义导入/导出）；查看任务状态与结果；中止特定状态任务。
*   **安全操作**: Keycloak认证登录 (MFA)；修改密码。
*   **操作日志审计**: 查阅用户在门户的关键操作记录。

**(各主要功能模块的详细功能规格、用户流程、技术实现等，请参见 `docs/components/` 目录下对应的组件详细设计文档，例如 [`服务器列表功能设计`](../components/03-servers/server-list.md)，[`任务列表功能设计`](../components/13-task-list.md) - 假设路径等。)**

**对于系统管理员 (服务提供方)**:
*   部署、配置和维护云资源。
*   通过Keycloak管理用户。
*   发布和更新内容。

### 2.2. 系统上下文与边界 (System Context and Boundaries)

本系统与以下外部实体和系统进行交互：
*   **用户 (顾客系统管理者)**: 通过Web浏览器与Next.js门户交互。
*   **Keycloak**: 外部IdP，负责用户认证。
*   **目标端点VM**: 运行被管理应用和HRW Agent。
*   **Azure PaaS服务**: App Service, Functions, Service Bus, Automation, SQL Database, Blob Storage, Files, Monitor。
*   **(间接) JP1/ITDM2, 秘文管理控制台等**: Runbook可能与其交互。

### 2.3. 主要技术栈概览 (High-Level Technology Stack)

*   **前端**: Next.js (v13.5.6+)
*   **后端API (Next.js)**: Node.js (v20 LTS+)
*   **后端服务 (Serverless)**: Azure Functions (Node.js/TypeScript)
*   **自动化脚本**: PowerShell
*   **消息队列**: Azure Service Bus
*   **数据库**: Azure SQL Database (Prisma v4.16.2+)
*   **对象存储**: Azure Blob Storage
*   **文件共享**: Azure Files
*   **身份认证**: Keycloak (v24.0.1+)
*   **版本控制**: Git (Monorepo)

## 3. 整体架构视图 (Overall Architecture View)

### 3.1. C4模型 - 系统上下文图 (C4 Model - System Context Diagram)

```mermaid
graph TD
    classDef system fill:#1168bd,stroke:#0b4884,color:#fff;
    classDef person fill:#08427b,stroke:#002050,color:#fff;
    classDef ext_system fill:#999,stroke:#666,color:#fff;
    User["👤 顾客系统管理者"]:::person
    SysAdmin["👤 服务提供方系统管理员"]:::person
    JCS_Portal["JCS 端点资产与任务管理系统"]:::system
    KeycloakIdP["🔐 Keycloak (IdP)"]:::ext_system
    TargetVMs["🖥️ 目标端点VM (HRW & Docker)"]:::ext_system
    AzurePaaS["☁️ Azure PaaS 服务"]:::ext_system
    ManagedApps["被管理应用 (e.g., JP1, HIBUN)"]:::ext_system
    User -- "访问门户, 管理/执行任务" --> JCS_Portal
    SysAdmin -- "部署/维护, 管理用户" --> JCS_Portal
    SysAdmin -- "管理Keycloak" --> KeycloakIdP
    JCS_Portal -- "用户认证" --> KeycloakIdP
    JCS_Portal -- "执行VM任务" --> TargetVMs
    JCS_Portal -- "构建于/依赖" --> AzurePaaS
    TargetVMs -- "运行" --> ManagedApps
```
**图 3.1: 系统上下文图 (System Context Diagram)**

### 3.2. 核心架构图 (Core Architecture Diagram)

```mermaid
graph TD
    classDef user_nextjs_app fill:#e3f2fd,stroke:#1976d2,stroke-width:2px;
    classDef keycloak_idp fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px;
    classDef servicebus fill:#fce4ec,stroke:#c2185b,stroke-width:2px;
    classDef az_function fill:#fff3e0,stroke:#f57c00,stroke-width:2px;
    classDef az_function_timeout fill:#ffebee,stroke:#c62828,stroke-width:2px;
    classDef database fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px;
    classDef storage fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px;
    classDef automation fill:#e0f7fa,stroke:#0097a7,stroke-width:2px;
    classDef vm_hrw_components fill:#f1f8e9,stroke:#689f38,stroke-width:2px;
    classDef monitor_ops fill:#fafafa,stroke:#616161,stroke-width:2px;

    subgraph "应用与用户交互层"
        direction LR
        User["👤 用户"] -- "访问/操作" --> NextJsApp["🌐 Next.js 应用"]:::user_nextjs_app
        NextJsApp -- "认证 (OIDC)" --> Keycloak["🔐 Keycloak"]:::keycloak_idp
    end
    subgraph "消息与事件驱动层"
        direction TB
        NextJsApp -- "任务消息" --> SB_TaskInputQ["SB: TaskInputQueue"]:::servicebus
        SB_TaskInputQ -.-> SB_TaskInputQ_DLQ["DLQ"]:::servicebus -- "触发" --> TaskExecuteTimeoutFunc["⚡ Func: TaskExecuteTimeoutFunc<br/>([设计文档](../components/backend-services-functions/function-task-execute-timeout.md))"]:::az_function_timeout
        NextJsApp -- "取消消息" --> SB_TaskControlQ["SB: TaskControlQueue"]:::servicebus
        SB_TaskControlQ -.-> SB_TaskControlQ_DLQ["DLQ"]:::servicebus -- "触发" --> TaskCancelTimeoutFunc["⚡ Func: TaskCancelTimeoutFunc<br/>([设计文档](../components/backend-services-functions/function-task-cancellation-timeout.md))"]:::az_function_timeout
        RunbookMonitorFunc -- "Runbook结果" --> SB_RunbookStatusQ["SB: RunbookStatusQueue"]:::servicebus
        SB_RunbookStatusQ -.-> SB_RunbookStatusQ_DLQ["DLQ"]:::servicebus -- "触发" --> RunbookProcessorTimeoutFunc["⚡ Func: RunbookProcessorTimeoutFunc<br/>([设计文档](../components/backend-services-functions/function-runbook-processor-timeout.md))"]:::az_function_timeout
    end
    subgraph "后端核心服务"
        direction TB
        SB_TaskInputQ --> TaskExecuteFunc["⚡ Func: TaskExecuteFunc<br/>([设计文档](../components/backend-services-functions/function-task-execute.md))"]:::az_function
        TaskExecuteFunc --> SQLDB["💾 Azure SQL DB"]:::database
        TaskExecuteFunc --> AzureFiles_Work["📁 Azure Files (工作区)"]:::storage
        TaskExecuteFunc --> Blob_TempUpload["Azure Blob (临时上传)"]:::storage
        TaskExecuteFunc --> AzAutomationAPI["🤖 Azure Automation API"]:::automation
        SB_TaskControlQ --> TaskCancellationFunc["⚡ Func: TaskCancelFunc<br/>([设计文档](../components/backend-services-functions/function-task-cancellation.md))"]:::az_function
        TaskCancellationFunc --> SQLDB
        SB_RunbookStatusQ --> RunbookProcessorFunc["⚡ Func: RunbookProcessorFunc<br/>([设计文档](../components/backend-services-functions/function-runbook-processor.md))"]:::az_function
        RunbookProcessorFunc --> SQLDB; RunbookProcessorFunc --> AzureFiles_Work
        RunbookProcessorFunc --> Blob_FinalOutput["Azure Blob (最终产物)"]:::storage
        RunbookProcessorFunc --> AzAutomationAPI
        TimerTrigger["⏰ 定时器"] --> RunbookMonitorFunc["⚡ Func: RunbookMonitorFunc<br/>([设计文档](../components/backend-services-functions/function-runbook-monitor.md))"]:::az_function
        RunbookMonitorFunc --> SQLDB; RunbookMonitorFunc --> AzAutomationAPI
        TaskExecuteTimeoutFunc --> SQLDB; TaskExecuteTimeoutFunc --> AzAutomationAPI; TaskExecuteTimeoutFunc --> AzureFiles_Work
        TaskCancelTimeoutFunc --> SQLDB
        RunbookProcessorTimeoutFunc --> SQLDB; RunbookProcessorTimeoutFunc --> AzureFiles_Work
    end
    subgraph "自动化执行层"
        direction TB
        AzAutomationAPI -- "调度作业" --> HRW_Group["HRW组"]:::automation
        HRW_Group --> VM_HRW["VM: HRW Agent"]:::vm_hrw_components
        VM_HRW -- "执行脚本" --> VM_Runbook["VM: PowerShell Runbook"]:::vm_hrw_components
    end
    subgraph "监控与运维"
        AzureMonitor["📊 Azure Monitor"]:::monitor_ops
        NextJsApp -.-> AzureMonitor; TaskExecuteFunc -.-> AzureMonitor; TaskCancellationFunc -.-> AzureMonitor; RunbookProcessorFunc -.-> AzureMonitor; RunbookMonitorFunc -.-> AzureMonitor
        TaskExecuteTimeoutFunc -.-> AzureMonitor; TaskCancelTimeoutFunc -.-> AzureMonitor; RunbookProcessorTimeoutFunc -.-> AzureMonitor
        AzAutomationAPI -.-> AzureMonitor; VM_Runbook -.-> AzureMonitor; SQLDB -.-> AzureMonitor; SB_TaskInputQ -.-> AzureMonitor
    end
    SQLDB <-. "数据存取" .-> NextJsApp; Blob_FinalOutput <-. "文件链接" .-> NextJsApp; Blob_TempUpload <-. "文件上传" .-> NextJsApp;
```
**图 3.2: 核心架构图 (Core Architecture Diagram)**
*说明: 此图展示了系统的主要组件、它们之间的交互以及关键数据流。包含了DLQ及其触发的超时处理Functions（如[`TaskExecuteTimeoutFunc`](../components/backend-services-functions/function-task-execute-timeout.md)，[`TaskCancellationTimeoutFunc`](../components/backend-services-functions/function-task-cancellation-timeout.md)，[`RunbookProcessorTimeoutFunc`](../components/backend-services-functions/function-runbook-processor-timeout.md)），并明确了各组件与核心数据存储和Azure Automation API的交互。Runbook的执行结果由[`RunbookMonitorFunc`](../components/backend-services-functions/function-runbook-monitor.md)通过轮询Azure Automation API获取，并将原始结果信息发送到`RunbookStatusQueue`供[`RunbookProcessorFunc`](../components/backend-services-functions/function-runbook-processor.md)处理。并发锁的创建由门户后端 ([`createTaskAction`](../components/actions/create-task-action.md)) 负责，实际的获取与释放由[`TaskExecuteFunc`](../components/backend-services-functions/function-task-execute.md)和[`RunbookProcessorFunc`](../components/backend-services-functions/function-runbook-processor.md)等负责。任务执行上下文由[`createTaskAction`](../components/actions/create-task-action.md)准备并存入`Task`记录。*

### 3.3. 主要组件及其职责概览 (Overview of Major Components and Responsibilities)

| 组件/服务 (Component/Service)             | 主要职责 (Primary Responsibilities)                                                                                                                              | 组件设计文档 / 内部章节参考 |
|-------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------|
| **Next.js 应用** ([`应用设计概述`](../components/apps-nextjs.md) - 假设路径) | 提供用户界面，处理用户输入，通过Server Action ([`createTaskAction`](../components/actions/create-task-action.md))发起后台任务，用户认证，临时文件上传。 | (见4.1节)                   |
| **Keycloak** (IdP)                        | 外部身份提供商，负责用户认证和Token颁发。                                                                                                                   | (见4.2节)                   |
| **Azure Service Bus**                     | 提供可靠的异步消息队列，解耦组件。                                                              | (见第5节)                    |
| **`TaskExecuteFunc`**                     | 监听新任务，获取并发锁，准备环境，提交Runbook作业，更新任务状态。执行记录保留策略。 | [`设计文档`](../components/backend-services-functions/function-task-execute.md) |
| **`RunbookProcessorFunc`**                | 处理Runbook结果，更新任务终态，归档文件，释放锁，清理工作区。 | [`设计文档`](../components/backend-services-functions/function-runbook-processor.md) |
| **`TaskCancellationFunc`**                | 处理任务取消请求，更新任务状态或备注。                                                                                      | [`设计文档`](../components/backend-services-functions/function-task-cancellation.md) |
| **`RunbookMonitorFunc`**                  | 定时监控运行中作业，检测超时/结束，发送结果到队列，更新任务状态。 | [`设计文档`](../components/backend-services-functions/function-runbook-monitor.md) |
| **`TaskExecuteTimeoutFunc`**              | 处理`TaskExecuteFunc`超时 (DLQ)，尝试停止作业、更新状态、释放资源。                                                  | [`设计文档`](../components/backend-services-functions/function-task-execute-timeout.md) |
| **`TaskCancellationTimeoutFunc`**         | 处理`TaskCancellationFunc`超时 (DLQ)，记录错误，更新任务状态。                                                              | [`设计文档`](../components/backend-services-functions/function-task-cancellation-timeout.md) |
| **`RunbookProcessorTimeoutFunc`**         | 处理`RunbookProcessorFunc`超时 (DLQ)，尝试释放资源，更新任务状态。                                             | [`设计文档`](../components/backend-services-functions/function-runbook-processor-timeout.md) |
| **Azure Automation Account & Runbooks**   | 存储、管理和执行PowerShell Runbook。                                      | (见7.1, 7.2节)              |
| **Hybrid Runbook Worker (HRW) & Group**   | VM代理，执行Runbook。 | (见7.3节)                   |
| **Azure SQL Database**                    | 持久化存储核心业务数据。                                       | (见8.1节)                   |
| **Azure Blob Storage**                    | 存储导出文件、产品媒体、临时上传文件。                                          | (见8.2.1节)                 |
| **Azure Files**                           | Runbook执行的临时文件共享工作区。                                                                          | (见8.2.2节)                 |
| **Azure Monitor**                         | 收集日志、指标，提供告警和诊断。                                                                                                               | (见第10节)                  |

## 4. 应用与用户交互层 (Application and User Interaction Layer)

本层负责所有用户直接交互，提供用户界面，并作为用户请求进入系统的入口。其详细设计见 [`Next.js应用设计概述`](../components/apps-nextjs.md) (假设路径) 及各具体UI功能模块的组件设计文档 (例如 [`服务器列表功能设计`](../components/03-servers/server-list.md))。

### 4.1. Next.js 应用 (门户 Frontend & API Backend)

Next.js应用的核心职责是提供用户界面和处理用户请求。它通过Server Actions (特别是[`createTaskAction`](../components/actions/create-task-action.md)) 与后端任务创建流程集成，并通过API Routes (如果需要) 提供数据查询等服务。用户认证依赖Keycloak。

### 4.2. Keycloak (外部身份提供商 - IdP)

Keycloak是本系统的身份认证核心，负责验证用户身份（包括MFA）、颁发和管理访问令牌。系统管理员通过Keycloak管理用户账户和认证策略。详细的认证流程（如OIDC流程）和安全配置（如密码策略、MFA设置）由Keycloak自身能力支持，本系统作为客户端与其集成。

## 5. 消息与事件驱动层 (Messaging and Event-Driven Layer)

Azure Service Bus是本系统实现异步处理和组件解耦的关键。它通过队列机制确保任务请求、状态更新和控制命令的可靠传递。

*   **`TaskInputQueue`**: 接收新任务请求，由[`TaskExecuteFunc`](../components/backend-services-functions/function-task-execute.md)消费。
*   **`RunbookStatusQueue`**: 接收原始Runbook作业结果，由[`RunbookProcessorFunc`](../components/backend-services-functions/function-runbook-processor.md)消费。
*   **`TaskControlQueue`**: 接收任务取消请求，由[`TaskCancellationFunc`](../components/backend-services-functions/function-task-cancellation.md)消费。

每个主队列都配置了相应的**死信队列 (DLQ)**，用于处理无法被正常消费的消息。这些DLQ分别由对应的超时处理Functions ([`TaskExecuteTimeoutFunc`](../components/backend-services-functions/function-task-execute-timeout.md), [`RunbookProcessorTimeoutFunc`](../components/backend-services-functions/function-runbook-processor-timeout.md), [`TaskCancellationTimeoutFunc`](../components/backend-services-functions/function-task-cancellation-timeout.md)) 监听和处理。

消息通常采用JSON格式，包含必要的标识符（如`taskId`）和上下文数据。

## 6. 后端核心服务 (Backend Core Services - Azure Functions)

本系统的核心后端业务逻辑、任务编排、状态管理、作业监控以及与其它Azure服务的集成，主要通过一系列无服务器Azure Functions实现。这些Functions通常使用Node.js/TypeScript运行时，并配置了适当的触发器（Service Bus队列、定时器）和绑定。

**所有后端Azure Function的详细技术规格、处理流程、接口定义、错误处理和配置项，均在其各自独立的组件设计文档中详细定义。这些文档位于 [`docs/components/backend-services-functions/`](../components/backend-services-functions/) 目录下。**

以下仅对各Function的核心职责进行高层概述：

*   **[`TaskExecuteFunc`](../components/backend-services-functions/function-task-execute.md)**: 处理新任务，获取锁，准备环境，提交Runbook。
*   **[`RunbookMonitorFunc`](../components/backend-services-functions/function-runbook-monitor.md)**: 定时监控Runbook作业，获取结果或检测超时。
*   **[`RunbookProcessorFunc`](../components/backend-services-functions/function-runbook-processor.md)**: 处理Runbook作业的最终结果，归档文件，更新DB，释放资源。
*   **[`TaskCancellationFunc`](../components/backend-services-functions/function-task-cancellation.md)**: 处理任务取消请求。
*   **[`TaskExecuteTimeoutFunc`](../components/backend-services-functions/function-task-execute-timeout.md)**: 处理`TaskExecuteFunc`执行超时后的补偿。
*   **[`TaskCancellationTimeoutFunc`](../components/backend-services-functions/function-task-cancellation-timeout.md)**: 处理`TaskCancellationFunc`执行超时后的补偿。
*   **[`RunbookProcessorTimeoutFunc`](../components/backend-services-functions/function-runbook-processor-timeout.md)**: 处理`RunbookProcessorFunc`执行超时后的补偿。

## 7. 自动化执行层 (Automation Execution Layer)

本层负责在目标端点VM上实际执行后台任务脚本。

### 7.1. Azure Automation Account

Azure Automation账户是管理所有自动化资源（Runbook、模块、凭证、计划等）的中心容器。它提供Runbook的存储、版本控制、执行引擎以及作业调度能力。本系统通过API与之交互以提交作业并获取其状态。

### 7.2. PowerShell Runbooks (在Hybrid Runbook Worker上执行)

PowerShell脚本是在目标VM上执行具体业务逻辑的单元。它们负责接收参数、与目标VM上的Docker容器（如JP1/ITDM2）交互、在Azure Files工作区进行文件操作、处理错误，并通过标准流输出结果和日志。Runbook的设计应保持独立，不直接与其他Azure服务（除Azure Files和目标容器外）交互。
(具体的Runbook脚本设计，例如操作日志导出、管理项目定义导入/导出的脚本，应有其独立的组件设计文档，例如在 `docs/components/automation-runbooks/` 目录下。)

### 7.3. Hybrid Runbook Worker (HRW) & Group

Hybrid Runbook Worker (HRW) Agent部署在目标VM上，使其能够从Azure Automation接收并执行Runbook作业，访问本地资源。每个目标服务器在`Server`数据表中记录其对应的HRW组名 (`hrwGroupName`)，任务提交时会指定此组名以确保作业在正确的VM上执行。

## 8. 数据持久化与存储层 (Data Persistence and Storage Layer)

### 8.1. Azure SQL Database (核心业务数据库)

Azure SQL Database用于存储本系统的核心业务数据、状态信息、配置参数和元数据。使用Prisma ORM进行数据访问。
核心数据表包括：
*   **`License`**: 存储客户许可证信息。([`License数据模型`](../data-models/license.md))
*   **`Server`**: 存储可管理的服务器条目。([`Server数据模型`](../data-models/server.md))
*   **`Task`**: 管理后台任务的执行状态和结果。([`Task数据模型`](../data-models/task.md))
*   **`ContainerConcurrencyStatus`**: 管理容器并发访问。([`ContainerConcurrencyStatus数据模型`](../data-models/container-concurrency-status.md))
*   **`OperationLog`**: 存储操作日志导出文件的元数据。([`OperationLog数据模型`](../data-models/operation-log.md))
*   **`LOV`**: 存储系统值列表和配置参数。([`LOV值列表定义`](../definitions/lov-definitions.md))
*   其他业务支持表 (如 `Notification`, `ProductMedia` 等)。

数据库Schema的权威来源是 `prisma/schema.prisma` 文件，迁移和种子数据通过Prisma Migrate/Seed管理。

### 8.2. Azure Storage Services

#### 8.2.1. Azure Blob Storage (最终成果物与临时上传)

用于存储用户可下载的最终成果物（如操作日志压缩包、导出的管理项目定义CSV）以及管理项目定义导入任务的临时上传文件。文件下载通过SAS Token机制提供。目录结构按`licenseId`, `taskId`等进行组织。各容器名称通过环境变量配置 (参考[`环境变量指南`](../guides/environment-variables.md))。

#### 8.2.2. Azure Files (任务临时工作区)

作为Azure Automation Runbook执行时的主要临时文件交换和暂存区。每个任务创建独立的工作区 (`TaskWorkspaces/{taskId}/`)，任务结束后由[`RunbookProcessorFunc`](../components/backend-services-functions/function-runbook-processor.md)或相关超时处理函数负责清理。

## 9. 安全设计 (Security Design)

### 9.1. 身份与访问管理 (Identity and Access Management)
*   **用户认证**: 依赖外部Keycloak，采用OIDC/OAuth 2.0，支持MFA。
*   **服务间认证**: Azure服务间优先使用Azure托管身份。
*   **API授权**: Next.js API层验证Keycloak颁发的Access Token。
*   **密钥管理**: 敏感配置通过Azure App Service/Functions的应用程序设置或Azure Key Vault引用进行安全管理。

### 9.2. 网络安全 (Network Security)
*   尽可能为Azure PaaS服务配置私有终结点，通过VNet集成。
*   使用NSGs/Azure Firewall控制流量。强制HTTPS。
*   HRW Agent与Azure Automation服务之间通过出站HTTPS通信。

### 9.3. 数据安全 (Data Security)
*   传输中加密 (TLS/SSL)。静态加密 (Azure SQL TDE, Azure Storage SSE)。
*   数据库访问遵循最小权限原则。避免在日志中记录敏感数据。

### 9.4. 安全日志与审计 (Security Logging and Auditing)
*   Keycloak记录登录审计日志，门户在`AuditLogin`表记录成功登录。
*   Azure Automation记录作业执行历史。各Azure PaaS服务日志收集到Azure Monitor。
*   应用程序记录关键安全事件。配置Azure Monitor告警。

## 10. 监控与运维 (Monitoring and Operations)

### 10.1. 监控平台 (Monitoring Platform)
主要使用Azure Monitor，包括Application Insights, Log Analytics Workspace, Dashboards & Workbooks。

### 10.2. 日志收集策略
*   Next.js应用、Azure Functions集成到Application Insights。
*   Azure Automation作业日志、PaaS服务诊断日志、HRW VM日志（通过AMA）收集到Log Analytics。
*   日志应包含时间戳、级别、组件名、关联ID (`taskId`等)、事件描述。推荐结构化日志。日志级别通过环境变量 `LOG_LEVEL` 控制。

### 10.3. 关键指标与告警 (Key Metrics and Alerting)
监控应用/Functions错误率、响应时间、资源利用率；Service Bus队列深度/DLQ计数；Automation作业失败率、`RunbookMonitorFunc`检测到的超时数；SQL Database DTU/vCore利用率、死锁；Storage可用性、延迟；容器繁忙失败频率、任务长时间卡滞等。HRW Agent健康状况。

### 10.4. 故障排除与诊断 (Troubleshooting and Diagnostics)
利用Application Insights进行分布式跟踪，使用KQL查询Log Analytics，定期检查Azure资源健康。运维手册和错误代码列表 ([`错误消息定义`](../definitions/error-messages.md)) 作为重要参考。

## 11. 性能、可靠性与成本 (Performance, Reliability, and Cost)

### 11.1. 性能考量 (Performance Considerations)
关注并发控制、队列管理、Runbook执行效率、`RunbookMonitorFunc`轮询间隔与API调用频率的平衡、Azure Function性能、数据库查询与索引优化、网络延迟。

### 11.2. 可靠性与高可用设计 (Reliability and High Availability)
通过异步解耦、DLQ与重试机制、Azure Function的幂等性设计、健壮错误处理、明确超时控制、Runbook健壮性、`RunbookMonitorFunc`的轮询与超时检测、以及利用PaaS服务的高可用选项来确保系统可靠性。任务取消机制提供了一定的用户控制能力。

### 11.3. 成本优化策略 (Cost Optimization Strategies)
优先采用Serverless架构（Azure Functions），根据需求选择合适的PaaS服务层级。优化Runbook执行效率，合理配置Blob存储层与生命周期管理。及时清理临时文件和旧任务记录。通过 `LOG_LEVEL` 优化日志存储成本。尽量在同区域部署资源以减少数据传输费用。使用资源标记与Azure预算进行成本跟踪和管理。

## 12. 配置管理 (Configuration Management)

### 12.1. 概述 (Overview of Configuration Approach)
*   **环境变量优先**: 应用程序从运行时环境读取配置。
*   **敏感配置管理**: 通过Azure平台应用程序设置或Key Vault安全管理。
*   **业务规则与枚举值**: 通过Azure SQL Database的 `LOV` 表管理 ([`LOV值列表定义`](../definitions/lov-definitions.md))。
*   **基础设施配置**: 通过配置指南手动实施。

### 12.2. 核心环境变量对架构的影响与概览 (Key Environment Variables Impacting Architecture - Overview)
本系统依赖一系列环境变量来配置其行为和与外部服务的连接。这些变量的正确设置至关重要。
**所有环境变量的详尽列表、具体用途、默认值（若有）、设置方法和管理规范，均定义在项目核心配置指南 [`环境变量指南`](../guides/environment-variables.md)。**
核心类别包括：服务连接字符串/URL、身份认证配置、Azure服务集成参数、行为控制与超时阈值、存储容器名称、日志配置、消息队列名称等。

### 12.3. 配置更新与部署 (Configuration Update and Deployment Strategy)
应用设置/环境变量更新通常通过Azure门户、CLI或部署脚本进行，可能导致应用重启。`LOV`表中的配置可通过数据库工具或专用管理界面更新，更改通常较快生效（可能受应用缓存影响）。基础设施配置变更依据配置指南手动实施。

## 13. 部署视图 (Deployment View)

### 13.1. 基础设施部署策略 (Infrastructure Deployment Strategy)
当前阶段，所有Azure基础设施资源均通过Azure门户或Azure CLI/PowerShell脚本，按照配置指南手动创建和配置。未来可考虑使用IaC工具（如ARM模板、Bicep或Terraform）进行自动化部署。

### 13.2. 各组件部署流程概述
*   **Next.js 应用**: 部署到Azure App Service (Linux Plan)，通常通过设置本地Git远程到App Service的部署中心进行推送部署。
*   **Azure Functions 应用**: 打包为Zip文件，部署到Azure Functions (Consumption/Premium Plan)。
*   **Azure Automation Runbooks**: 通过PowerShell脚本或手动从Azure门户发布/更新到目标Automation账户。HRW Agent在目标VM上手动或通过脚本安装和配置。
*   **Azure SQL Database**: 手动创建实例和数据库。Schema和种子数据通过Prisma Migrate/Seed进行管理和部署。
*   **其他Azure PaaS服务** (Service Bus, Storage等): 手动创建和配置。
(详细的部署步骤和配置指南应在独立的 [`部署流程指南`](../guides/deployment-procedures.md) - 假设路径 中维护。)

### 13.3. 环境分离策略 (Environment Segregation)
推荐为开发(Dev)、测试/质量保证(QA)、生产(Prod)等环境创建独立的Azure资源组和PaaS服务实例，以实现环境间的完全隔离。每个环境通过其自身的配置文件（如应用设置中的环境变量值、不同的数据库连接字符串等）进行区分。Keycloak也应有对应环境的Realm或Client配置。数据在各环境间应完全隔离。

## 14. 附录 (Appendices)

### 14.1. 术语表 (Glossary of Terms)
请参阅：[`项目术语表`](../definitions/glossary.md)。

### 14.2. 参考资料 (References)
*   **Azure官方文档**: (列出相关服务的官方文档链接，如Azure Functions, Service Bus, Automation等)
*   **Keycloak官方文档**
*   **Next.js官方文档**
*   **Prisma官方文档**
*   **项目内部文档**:
    *   [`Monorepo项目实施指南`](../guides/monorepo-structure-and-deployment-workflow.md)
    *   [`AI协作与文档规范指南`](../guides/ai-collaboration-and-documentation-standards.md)
    *   [`环境变量指南`](../guides/environment-variables.md)
    *   [`LOV值列表定义`](../definitions/lov-definitions.md)
    *   [`错误消息定义`](../definitions/error-messages.md)
    *   [`API规范 (OpenAPI)`](../apis/openapi.v1.yaml) (如果适用)
    *   **组件设计文档**:
        *   `docs/components/01-login.md` (假设路径) - 登录功能
        *   `docs/components/03-servers/server-list.md` - 服务器列表功能
        *   `docs/components/13-task-list.md` (假设路径) - 任务列表功能
        *   `docs/components/actions/create-task-action.md` - `createTaskAction` Server Action
        *   `docs/components/backend-services-functions/` - 目录下包含所有核心Azure Function的详细设计文档 (例如, `function-task-execute.md`, `function-runbook-monitor.md` 等7个)
        *   `docs/components/automation-runbooks/` (假设路径) - 目录下包含各PowerShell Runbook的详细设计文档
    *   **数据模型文档**:
        *   `docs/data-models/` - 目录下包含对Prisma Schema中核心数据模型的业务逻辑补充说明 (例如, `task.md`, `server.md` 等)
