/**
 * @file error.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2024, Hitachi Solutions, Ltd.
 */

"use client";

import { useEffect } from "react";

// メイン画面の共通のエラーコンポーネント
export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Optionally log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <div
      id="message-modal"
      tabIndex={-1}
      className="flex justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full"
    >
      <div className="relative w-full max-w-md max-h-full">
        <div className="relative rounded shadow bg-gray-600">
          <div className="flex items-center justify-between p-4 border-b rounded-t bg-gradient-header">
            <h3 className="text-lg font-semibold text-white">エラー</h3>
          </div>
          <div className="h-32 px-8 py-4 space-y-4 bg-white text-base font-medium">
            <div>
              <img
                src="/dialogerror_32.png"
                className="w-8 h-8 me-2 inline-block"
                alt="info"
              />
              {error.message}
            </div>
          </div>
          <div className="flex flex-row-reverse items-center p-4 border-t rounded-b bg-gradient-header">
            <button
              onClick={reset}
              type="button"
              className="w-28 rounded bg-gradient-dark px-3 py-2 text-center text-xs font-medium text-white 
              drop-shadow-dark shadow-dark hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
            >
              OK
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
