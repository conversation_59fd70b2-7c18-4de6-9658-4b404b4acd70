#!/usr/bin/env node

const ExcelJS = require('exceljs');
const path = require('path');

/**
 * 基于客户示例创建UT動作確認チェックリスト模板
 */
async function createCustomerTemplate() {
    try {
        console.log('📋 基于客户格式创建UT動作確認チェックリスト模板...\n');
        
        const workbook = new ExcelJS.Workbook();
        
        // 1. 创建表紙（封面）工作表
        await createCoverSheet(workbook);
        
        // 2. 创建目次（目录）工作表
        await createIndexSheet(workbook);
        
        // 3. 创建试验观点・测试结果集计工作表
        await createSummarySheet(workbook);
        
        // 4. 创建API对象工作表
        await createApiSheet(workbook);
        
        // 5. 创建示例测试工作表（3.1格式）
        await createSampleTestSheet(workbook);
        
        // 保存模板文件
        const templatePath = path.join(__dirname, '..', 'templates', 'UT動作確認チェックリスト_template.xlsx');
        
        // 确保templates目录存在
        const fs = require('fs');
        const templatesDir = path.dirname(templatePath);
        if (!fs.existsSync(templatesDir)) {
            fs.mkdirSync(templatesDir, { recursive: true });
        }
        
        await workbook.xlsx.writeFile(templatePath);
        
        console.log('✅ 客户格式模板创建完成!');
        console.log(`📁 模板文件: ${templatePath}`);
        
        return templatePath;
        
    } catch (error) {
        console.error('❌ 创建模板时出错:', error.message);
        throw error;
    }
}

/**
 * 创建表紙（封面）工作表
 */
async function createCoverSheet(workbook) {
    const worksheet = workbook.addWorksheet('表紙');
    
    // 设置列宽
    worksheet.columns = [
        { width: 8 }, { width: 12 }, { width: 12 }, { width: 12 }, { width: 12 },
        { width: 12 }, { width: 12 }, { width: 12 }, { width: 12 }, { width: 12 },
        { width: 12 }, { width: 12 }, { width: 12 }, { width: 12 }, { width: 12 }, { width: 12 }
    ];
    
    // 设置标题
    worksheet.getCell('B8').value = '資産配布管理システム';
    worksheet.getCell('B8').font = { size: 16, bold: true };
    
    // 设置UT動作確認書标题（合并单元格）
    worksheet.mergeCells('J12:P14');
    worksheet.getCell('J12').value = 'UT動作確認書';
    worksheet.getCell('J12').font = { size: 20, bold: true };
    worksheet.getCell('J12').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('J12').border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
    };
    
    // 添加项目信息区域
    const infoStartRow = 20;
    const infoData = [
        ['プロジェクト名:', '[プロジェクト名を入力]'],
        ['作成日:', new Date().toLocaleDateString('ja-JP')],
        ['作成者:', '[作成者名を入力]'],
        ['バージョン:', '1.0']
    ];
    
    infoData.forEach((row, index) => {
        worksheet.getCell(`B${infoStartRow + index}`).value = row[0];
        worksheet.getCell(`B${infoStartRow + index}`).font = { bold: true };
        worksheet.getCell(`D${infoStartRow + index}`).value = row[1];
    });
}

/**
 * 创建目次（目录）工作表
 */
async function createIndexSheet(workbook) {
    const worksheet = workbook.addWorksheet('目次');
    
    // 设置列宽
    worksheet.columns = [
        { width: 40 }, { width: 15 }, { width: 15 }, { width: 15 }, { width: 15 }
    ];
    
    // 标题
    worksheet.getCell('A1').value = '目次';
    worksheet.getCell('A1').font = { size: 16, bold: true };
    
    // 目录内容
    const indexData = [
        '1章.試験観点・テスト結果集計シート',
        '2章.フロントエンド',
        '3章.バックエンド',
        '  3.1 [テスト項目名]',
        '  3.2 [テスト項目名]',
        '  3.3 [テスト項目名]',
        '  ...'
    ];
    
    indexData.forEach((item, index) => {
        worksheet.getCell(`A${index + 3}`).value = item;
        if (item.startsWith('  ')) {
            worksheet.getCell(`A${index + 3}`).font = { size: 10 };
        } else {
            worksheet.getCell(`A${index + 3}`).font = { size: 12, bold: true };
        }
    });
}

/**
 * 创建试验观点・测试结果集计工作表
 */
async function createSummarySheet(workbook) {
    const worksheet = workbook.addWorksheet('1.試験観点・テスト結果集計シート');
    
    // 设置列宽
    worksheet.columns = [
        { width: 8 }, { width: 40 }, { width: 12 }, { width: 12 }, { width: 12 },
        { width: 12 }, { width: 12 }, { width: 12 }, { width: 12 }, { width: 12 },
        { width: 12 }, { width: 12 }, { width: 12 }, { width: 12 }, { width: 20 }, { width: 10 }
    ];
    
    // 标题
    worksheet.getCell('A1').value = '1章.試験観点・テスト結果集計シート';
    worksheet.getCell('A1').font = { size: 14, bold: true };
    
    // 说明文字
    worksheet.getCell('B3').value = '動作確認試験の観点およびその実施結果の集計を次に示します。';
    
    // 表头
    const headers = [
        '項番', '試験観点', '', '', '', '', '', '', '', '', '', '', '', '試験手順参照先', '試験数'
    ];
    
    headers.forEach((header, index) => {
        const cell = worksheet.getCell(5, index + 3); // 从C列开始
        cell.value = header;
        cell.font = { bold: true };
        cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
        };
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE6E6FA' }
        };
    });
    
    // 合并试验观点标题
    worksheet.mergeCells('D5:M5');
    worksheet.getCell('D5').value = '試験観点';
    worksheet.getCell('D5').alignment = { horizontal: 'center' };
    
    // 添加示例数据行
    const sampleData = [
        ['1', '正常系テスト', '', '', '', '', '', '', '', '', '', '', '', '3.1-3.5', '15'],
        ['2', '異常系テスト', '', '', '', '', '', '', '', '', '', '', '', '3.6-3.10', '12'],
        ['3', 'エラーハンドリング', '', '', '', '', '', '', '', '', '', '', '', '3.11-3.15', '8']
    ];
    
    sampleData.forEach((row, rowIndex) => {
        row.forEach((value, colIndex) => {
            const cell = worksheet.getCell(6 + rowIndex, colIndex + 3);
            cell.value = value;
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' }
            };
        });
    });
}

/**
 * 创建API对象工作表
 */
async function createApiSheet(workbook) {
    const worksheet = workbook.addWorksheet('API');
    
    // 设置列宽
    worksheet.columns = [
        { width: 8 }, { width: 40 }, { width: 10 }, { width: 10 }, { width: 10 }, { width: 15 }, { width: 30 }
    ];
    
    // 表头
    const headers = ['', 'API対象', '削除', '新規', '変更', '変更しない', '備考'];
    headers.forEach((header, index) => {
        const cell = worksheet.getCell(1, index + 1);
        cell.value = header;
        cell.font = { bold: true };
        cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
        };
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE6E6FA' }
        };
    });
    
    // 示例数据
    const apiData = [
        ['/api/audit-login-logs', '-', '-', '●', '-', 'セッション取得方式変更'],
        ['/api/notifications/system', '-', '-', '-', '●', 'お知らせ取得処理'],
        ['/api/notifications', '-', '-', '●', '-', '利用者お知らせ取得'],
        ['/api/licenses/current', '-', '-', '-', '●', 'ライセンス情報取得']
    ];
    
    apiData.forEach((row, rowIndex) => {
        row.forEach((value, colIndex) => {
            const cell = worksheet.getCell(2 + rowIndex, colIndex + 2);
            cell.value = value;
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' }
            };
            if (value === '●') {
                cell.font = { color: { argb: 'FFFF0000' }, bold: true };
            }
        });
    });
}

/**
 * 创建示例测试工作表（基于3.1格式）
 */
async function createSampleTestSheet(workbook) {
    const worksheet = workbook.addWorksheet('3.1_サンプル');
    
    // 设置列宽
    worksheet.columns = [
        { width: 8 }, { width: 8 }, { width: 25 }, { width: 35 }, { width: 40 }, 
        { width: 35 }, { width: 12 }, { width: 10 }, { width: 10 }, { width: 10 }, { width: 15 }
    ];
    
    // 章节标题
    worksheet.getCell('B1').value = '3章.バックエンド';
    worksheet.getCell('B1').font = { size: 14, bold: true };
    
    // 节标题（合并单元格）
    worksheet.mergeCells('B2:E2');
    worksheet.getCell('B2').value = '3.1';
    worksheet.getCell('B2').font = { size: 12, bold: true };
    worksheet.getCell('B2').alignment = { horizontal: 'center' };
    worksheet.getCell('B2').border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
    };
    worksheet.getCell('B2').fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFCCCCCC' }
    };
    
    // 表头
    const headers = ['項番', '試験観点', '試験対象', '試験手順', '確認項目', '実施日', '実施者', '再鑑者', '試験結果', '備考'];
    headers.forEach((header, index) => {
        const cell = worksheet.getCell(3, index + 2); // 从B列开始
        cell.value = header;
        cell.font = { bold: true };
        cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
        };
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE6E6FA' }
        };
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    });
    
    // 示例测试用例
    const testCases = [
        {
            no: 1,
            viewpoint: '正常系テスト例',
            target: 'GET /api/example',
            procedure: '1. モックリクエストを作成する\n2. 正常なレスポンスを確認する\n3. ステータスコードを確認する',
            confirmation: '1. レスポンスが正常に返されること\n2. ステータスコードが200であること',
            date: new Date().toLocaleDateString('ja-JP'),
            implementer: '[実施者名]',
            reviewer: '[再鑑者名]',
            result: 'OK',
            remarks: ''
        },
        {
            no: 2,
            viewpoint: '異常系テスト例',
            target: 'GET /api/example',
            procedure: '1. 不正なリクエストを作成する\n2. エラーレスポンスを確認する\n3. エラーメッセージを確認する',
            confirmation: '1. 適切なエラーが返されること\n2. エラーメッセージが正しいこと',
            date: new Date().toLocaleDateString('ja-JP'),
            implementer: '[実施者名]',
            reviewer: '[再鑑者名]',
            result: 'OK',
            remarks: ''
        }
    ];
    
    testCases.forEach((testCase, rowIndex) => {
        const row = 4 + rowIndex;
        const values = [
            testCase.no,
            testCase.viewpoint,
            testCase.target,
            testCase.procedure,
            testCase.confirmation,
            testCase.date,
            testCase.implementer,
            testCase.reviewer,
            testCase.result,
            testCase.remarks
        ];
        
        values.forEach((value, colIndex) => {
            const cell = worksheet.getCell(row, colIndex + 2);
            cell.value = value;
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' }
            };
            cell.alignment = { vertical: 'top', wrapText: true };
            
            // 结果列特殊格式
            if (colIndex === 8 && value === 'OK') {
                cell.font = { color: { argb: 'FF008000' }, bold: true };
            }
        });
        
        // 设置行高以适应多行文本
        worksheet.getRow(row).height = 60;
    });
}

// 如果直接运行此脚本
if (require.main === module) {
    createCustomerTemplate()
        .then((templatePath) => {
            console.log('\n✅ 客户格式模板创建完成!');
            console.log(`📁 模板文件: ${templatePath}`);
            console.log('\n💡 使用方法:');
            console.log('1. 复制模板文件到目标位置');
            console.log('2. 根据实际测试内容修改工作表');
            console.log('3. 使用generate-test-excel.js生成实际测试报告');
        })
        .catch(error => {
            console.error('❌ 创建失败:', error.message);
            process.exit(1);
        });
}

module.exports = { createCustomerTemplate };