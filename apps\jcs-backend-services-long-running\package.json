{"name": "jcs-backend-services-long-running", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"build": "tsc", "watch": "tsc -w", "clean": "<PERSON><PERSON><PERSON> dist", "prestart": "npm run clean && npm run build", "start": "cp -f local.settings.dev.json local.settings.json && npm run build && func start", "start:test": "cp -f local.settings.test.json local.settings.json && npm run build && func start", "postinstall": "prisma generate", "test": "jest", "test:watch": "jest --watchAll", "test:coverage": "jest --coverage"}, "dependencies": {"@azure/functions": "^4.0.0", "@azure/identity": "^4.10.1", "@azure/service-bus": "^7.9.5", "@azure/storage-blob": "^12.27.0", "@azure/storage-file-share": "^12.27.0", "@prisma/client": "^6.10.1"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "18.x", "azure-functions-core-tools": "^4.x", "jest": "^29.7.0", "jest-mock-extended": "^3.0.2", "prisma": "^6.10.1", "rimraf": "^5.0.7", "ts-jest": "^29.1.4", "typescript": "^5.8.3"}}