# 缺陷調査手法と標準作業手順書

## 目的

本文書は、Git履歴から缺陷修正を網羅的に調査し、標準化された缺陷報告書を作成するための**再現可能な作業手順**を定義します。

## 適用範囲

- jcs-backend-services-standard
- jcs-backend-services-long-running
- jcs-endpoint-nextjs

## 前提条件

- Git環境が利用可能
- PowerShell環境が利用可能
- 対象リポジトリへのアクセス権限

---

## 標準作業手順

### STEP 1: 事前準備

#### 1.1 調査対象の確認
- [ ] 調査開始日（YYYY-MM-DD形式）
- [ ] 調査終了日（YYYY-MM-DD形式）
- [ ] 対象リポジトリパス
  - 根目录: `e:\Git\ito\jcs-endpoint-monorepo`
  - Next.js: `e:\Git\ito\jcs-endpoint-monorepo\apps\jcs-endpoint-nextjs`

#### 1.2 作業ディレクトリの設定
```bash
# 根目录での作業
cd e:\Git\ito\jcs-endpoint-monorepo

# Next.jsでの作業
cd e:\Git\ito\jcs-endpoint-monorepo\apps\jcs-endpoint-nextjs
```

### STEP 2: 全コミット調査（必須）

#### 2.1 根目录リポジトリの調査
```bash
# 作業ディレクトリ: e:\Git\ito\jcs-endpoint-monorepo
powershell -Command "git log --since='YYYY-MM-DD' --until='YYYY-MM-DD' --oneline"

# 出力がtruncatedされた場合
powershell -Command "git --no-pager log --since='YYYY-MM-DD' --until='YYYY-MM-DD' --oneline"
```

#### 2.2 Next.jsリポジトリの調査
```bash
# 作業ディレクトリ: e:\Git\ito\jcs-endpoint-monorepo\apps\jcs-endpoint-nextjs
powershell -Command "git log --since='YYYY-MM-DD' --until='YYYY-MM-DD' --oneline"
```

#### 2.3 コミット一覧の記録
- 全てのコミットハッシュとメッセージを記録
- **キーワード検索は使用しない**（重要な修正を見落とすため）

### STEP 3: 業務コード変更の特定

#### 3.1 各コミットの変更ファイル確認
```bash
# 各コミットに対して実行
powershell -Command "git show <commit-hash> --name-only"
```

#### 3.2 業務コードファイルの判定基準

**対象ファイル（業務コード）:**
- `apps/jcs-backend-services-standard/**/*.ts` (テストファイル以外)
- `apps/jcs-backend-services-long-running/**/*.ts` (テストファイル以外)
- `apps/jcs-endpoint-nextjs/app/**/*.ts`
- `apps/jcs-endpoint-nextjs/app/**/*.tsx`

**除外ファイル:**
- `**/__tests__/**`
- `**/*.test.ts`
- `**/*.spec.ts`
- `**/*.md`
- `**/package.json`
- `**/tsconfig.json`
- `docs/**`
- `review-records/**`

#### 3.3 業務影響度の判定
各変更ファイルについて：
- [ ] **高影響**: 実行時ロジックの変更
- [ ] **中影響**: 型定義、インターフェース変更
- [ ] **低影響**: コメント、フォーマットのみ

### STEP 4: 詳細コード分析

#### 4.1 高影響・中影響コミットの詳細確認
```bash
# コード変更の詳細確認
powershell -Command "git show <commit-hash>"

# 特定ファイルのみ確認
powershell -Command "git show <commit-hash> -- <file-path>"

# 変更統計の確認
powershell -Command "git show <commit-hash> --stat"
```

#### 4.2 缺陷パターンの識別

**確認すべき缺陷パターン:**
- [ ] **楽観ロック制御の不備**: `updateMany`での`updatedAt`チェック追加
- [ ] **型安全性の改善**: `any` → `unknown`、型定義の追加
- [ ] **エラーハンドリング**: `throw new Error` → 適切なエラー処理
- [ ] **セキュリティ**: `Math.random()` → 暗号学的安全な生成
- [ ] **設定の統一**: ファイル名、URL、環境変数の不整合修正
- [ ] **メッセージの統一**: ハードコーディング → 関数使用

#### 4.3 缺陷の判定基準

**缺陷として記録する条件:**
1. **実行時動作の変更**がある
2. **セキュリティ**に関わる修正
3. **データ整合性**に関わる修正
4. **型安全性**の改善
5. **エラー処理**の改善

**除外する変更:**
- コメントのみの変更
- フォーマットのみの変更
- 文書のみの更新

### STEP 5: 缺陷報告書の作成

#### 5.1 缺陷報告書のテンプレート

**ファイル名**: `缺陷報告_YYYY年MM月DD日以降.md`

**必須セクション:**
1. **概要**
2. **修正対象期間**
3. **缺陷一覧**（表形式）
4. **修正サマリー**
5. **影響範囲**

#### 5.2 缺陷一覧表の標準形式

```markdown
| 題名 | 現象 | 発生条件 | 原因 | 対策－修正前(対策前) | 対策－修正後(対策後) |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **DEF-XXX: [缺陷名称]** | [現象の説明] | [発生条件] | [原因分析] | [修正前のコード] | [修正後のコード] |
```

#### 5.3 缺陷編号の規則
- **DEF-001, DEF-002, ...** の連番
- **時系列順**に配置（古いコミットから順番）
- 新しい缺陷追加時は**全体の編号を見直し**

### STEP 6: 品質チェック

#### 6.1 必須チェック項目
- [ ] **全コミット確認済み**（キーワード検索のみに依存していない）
- [ ] **缺陷編号の連続性**（DEF-001, DEF-002, ...）
- [ ] **時系列順の配置**（古いコミットから順番）
- [ ] **重複缺陷の統合**
- [ ] **期間別統計の正確性**

#### 6.2 最終確認
```bash
# 缺陷数の確認
grep -c "DEF-[0-9]" 缺陷報告_*.md

# 編号の連続性確認
grep "DEF-[0-9]" 缺陷報告_*.md | grep -o "DEF-[0-9]*"
```

---

## 重要な注意事項

### ❌ 禁止事項
- キーワード検索のみに依存（`git log --grep="fix|bug"`等）
- コミットメッセージのみで判断
- 文書更新コミットの無視

### ✅ 必須事項
- 全コミットの確認
- 変更ファイルの確認
- 実際のコード変更の確認

---

## トラブルシューティング

### Git出力がtruncatedされる場合
```bash
powershell -Command "git --no-pager log --since='YYYY-MM-DD' --until='YYYY-MM-DD' --oneline"
```

### 大量のコミットがある場合
```bash
# 週単位で分割調査
git log --since="YYYY-MM-DD" --until="YYYY-MM-DD" --oneline
```

---

## 主要な経験教訓

### キーワード依存調査の危険性
**問題**: キーワード検索のみでは重要な缺陷修正を見落とす

**具体例**:
- "新增生成安全唯一标识符的功能" → セキュリティ脆弱性修正
- "优化任务处理逻辑" → 楽観ロック制御修正
- "统一返回类型" → 型安全性改善

**対策**: 全コミットの確認が必須

### 見落としやすい缺陷パターン
- 楽観ロック制御の不備（`updateMany`での`updatedAt`チェック）
- 型安全性の改善（`any` → `unknown`）
- セキュリティ改善（`Math.random()` → 暗号学的安全な生成）
- エラーハンドリング（`throw new Error` → 適切な処理）



---

---

**作成日**: 2025年7月16日
**文書バージョン**: 3.0 (簡潔版)
