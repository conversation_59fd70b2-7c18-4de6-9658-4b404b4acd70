/**
 * @file route.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2024, 2025, Hitachi Solutions, Ltd.
 */

import {
  atLeastTwoKindsRegex, ENV, PORTAL_ERROR_MESSAGES
} from "@/app/lib/definitions";
import Logger from "@/app/lib/logger";
import { handleApiError } from "@/app/lib/portal-error";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

// PUTメソッドの実装
export async function PUT(req: Request) {
  // リクエストボディからパスワードと新しいパスワードを取得
  const { password, newPassword, oneTimePassword } = await req.json();

  // サーバーセッションからユーザーIDを取得
  const session = await getIronSession<SessionData>(cookies(), sessionOptions);
  const userId = session?.user.userId;

  // 新しいパスワードが8文字未満または128文字を超える場合はエラーを返す
  if (newPassword.length < 8 || newPassword.length > 128) {
    return NextResponse.json(
      { error: PORTAL_ERROR_MESSAGES.EMEC0009 },
      { status: 400 },
    );
  }
  // もし新しいパスワードが指定された正規表現にマッチしない場合
  else if (!atLeastTwoKindsRegex.test(newPassword)) {
    // 400 Bad RequestステータスとともにエラーメッセージをJSON形式で返す
    return NextResponse.json(
      { error: PORTAL_ERROR_MESSAGES.EMEC0010 },
      { status: 400 },
    );
  }
  // もしユーザーIDが新しいパスワードと一致する場合
  else if (userId === newPassword) {
    // 400 Bad RequestステータスとともにエラーメッセージをJSON形式で返す
    return NextResponse.json(
      { error: PORTAL_ERROR_MESSAGES.EMEC0011 },
      { status: 400 },
    );
  }
  // もし現在のパスワードが新しいパスワードと一致する場合
  else if (password === newPassword) {
    // 400 Bad RequestステータスとともにエラーメッセージをJSON形式で返す
    return NextResponse.json(
      { error: PORTAL_ERROR_MESSAGES.EMEC0012 },
      { status: 400 },
    );
  }

  try {
    // keycloakのユーザー名とパスワードの検証
    // KEYCLOAKのREALM
    const realm = ENV.KEYCLOAK_REALM;
    // KEYCLOAKのCLIENT
    const clientId = ENV.KEYCLOAK_CLIENT;
    // KEYCLAOKログイン成功後のコールバックアドレス
    const redirectUri = ENV.KEYCLOAK_REDIRECT_URL;
    // KEYCLOAKサービスドメイン
    const domainName = ENV.KEYCLOAK_INTERNAL_DOMAIN_NAME;
    // KEYCLOAKサーバのクライアント認証器
    const clientSecret = ENV.KEYCLOAK_CLIENT_SECRET;
    // プロファイル内容取得検証
    if (!realm || !clientId || !redirectUri || !domainName || !clientSecret) {
      Logger.error({ message: PORTAL_ERROR_MESSAGES.EMEC0007 });
      return NextResponse.json(
        { error: PORTAL_ERROR_MESSAGES.EMEC0007 },
        { status: 404 },
      );
    }
    // アクセストークン要求URLの構築
    const checkUrl = `${domainName}/realms/${realm}/protocol/openid-connect/token`;
    // API呼び出しパラメータ作成
    const params = new URLSearchParams();
    // 権限タイプ
    params.append('grant_type', 'password');
    // KEYCLOAKのCLIENT
    params.append('client_id', clientId);
    // Keycloakサーバのクライアント認証器
    params.append('client_secret', clientSecret);
    // KEYCLAOKのユーザー名
    params.append('username', session.user.userId);
    // KEYCLAOKのパスワード
    params.append('password', password);
    // ワンタイムコード
    params.append('otp', oneTimePassword);

    // BufferオブジェクトをBase 64符号化文字列に変換する
    const basicAuth = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');
    // オブジェクトheadersを作成する
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Bearer ${basicAuth}`
    };
    // APIアクセス
    const response = await fetch(checkUrl, {
      method: 'POST',
      headers: headers,
      body: params.toString(),
    });

    // 取得結果json形式変換
    const data = await response.json();
    // APIの実行に失敗しました
    if (!response.ok) {
      Logger.error({ source: domainName, message: data });
      // パスワードまたはワンタイムコードが一致しない場合もエラーを返す
      if (data.error === 'invalid_grant') {
        Logger.error({
          message: `ユーザー[${userId}]は認証に失敗しました。`,
        });

        return NextResponse.json(
          { error: PORTAL_ERROR_MESSAGES.EMEC0015 },
          { status: 403 },
        );
      } else {
        // その以外の場合、共通の異常処理を行う
        throw new Error(data.error);
      }
    } else {

      // KEYCLOAKパスワードを更新し
      const updateUrl = `${domainName}/admin/realms/${realm}/users/${session.user.id}/reset-password`;
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${data.access_token}`
      };
      // パスワードが変更
      const updatePwd = await fetch(updateUrl, {
        method: 'PUT',
        headers: headers,
        body: JSON.stringify({
          type: 'password',
          value: newPassword,
          temporary: false
        }),
      });
      // APIの実行に失敗しました
      if (!updatePwd.ok) {
        Logger.error({ message: PORTAL_ERROR_MESSAGES.EMEC0013 });
        return NextResponse.json(
          { error: PORTAL_ERROR_MESSAGES.EMEC0013 },
          { status: 400 },
        );
      } else {
        // KEYCLAOKパスワード変更メッセージが返される
        if (updatePwd.status !== 204) {
          Logger.error({ 
            message: `ユーザー[${userId}]のパスワードが変更に失敗しました。`
          });
          return NextResponse.json(
            { error: PORTAL_ERROR_MESSAGES.EMEC0013 },
            { status: 400 },
          );
        } else {
          Logger.info({
            message: `ユーザー[${userId}]のパスワードが変更されました。`,
          });
          return NextResponse.json({ message: PORTAL_ERROR_MESSAGES.EMEC0008 });
        }
      }
    }
  } catch (error) {
    // エラーが発生した場合はエラーハンドリング関数に委譲
    return handleApiError(error);
  }
}
